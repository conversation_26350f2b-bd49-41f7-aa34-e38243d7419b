	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc30012a --dep-file=Ifx_Fifo.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c'

	
$TC16X
	.sdecl	'.rodata.Bsp..1.cnt',data,rom
	.sect	'.rodata.Bsp..1.cnt'
	.align	4
.1.cnt:	.type	object
	.size	.1.cnt,8
	.word	-1,2147483647
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_create',code,cluster('Ifx_Fifo_create')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_create'
	.align	2
	
	.global	Ifx_Fifo_create
; Function Ifx_Fifo_create
.L173:
Ifx_Fifo_create:	.type	func
	mov	d15,d5
.L494:
	add	d4,#3
.L493:
	insert	d0,d4,#0,#0,#2
	extr	d8,d0,#0,#16
.L495:
	add	d0,d8,#28
.L771:
	add	d4,d0,#8
	call	malloc
.L492:
	jz.a	a2,.L2
.L772:
	mov.aa	a4,a2
.L496:
	mov	e4,d15,d8
	call	Ifx_Fifo_init
.L2:
	j	.L3
.L3:
	ret
.L366:
	
__Ifx_Fifo_create_function_end:
	.size	Ifx_Fifo_create,__Ifx_Fifo_create_function_end-Ifx_Fifo_create
.L219:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_destroy',code,cluster('Ifx_Fifo_destroy')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_destroy'
	.align	2
	
	.global	Ifx_Fifo_destroy
; Function Ifx_Fifo_destroy
.L175:
Ifx_Fifo_destroy:	.type	func
	call	free
.L497:
	ret
.L370:
	
__Ifx_Fifo_destroy_function_end:
	.size	Ifx_Fifo_destroy,__Ifx_Fifo_destroy_function_end-Ifx_Fifo_destroy
.L224:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_init',code,cluster('Ifx_Fifo_init')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_init'
	.align	2
	
	.global	Ifx_Fifo_init
; Function Ifx_Fifo_init
.L177:
Ifx_Fifo_init:	.type	func
	add	d4,#3
.L498:
	insert	d15,d4,#0,#0,#2
	extr	d0,d15,#0,#16
.L499:
	mov	d15,#0
.L781:
	st.b	[a4]24,d15
.L782:
	mov	d15,#1
.L783:
	st.b	[a4]25,d15
.L784:
	mov.d	d15,a4
.L500:
	add	d15,d15,#28
.L501:
	add	d15,#7
	insert	d15,d15,#0,#0,#3
.L785:
	st.w	[a4],d15
.L786:
	mov	d15,#0
.L787:
	st.h	[a4]4,d15
.L788:
	mov	d15,#0
.L789:
	st.h	[a4]14,d15
.L790:
	mov	d15,#0
	st.w	[a4]10,d15
.L791:
	st.w	[a4]6,d15
.L792:
	mov	d15,#0
	st.h	[a4]18,d15
.L793:
	st.h	[a4]16,d15
.L794:
	st.h	[a4]20,d0
.L795:
	st.h	[a4]22,d5
.L796:
	mov.aa	a2,a4
.L502:
	j	.L4
.L4:
	ret
.L372:
	
__Ifx_Fifo_init_function_end:
	.size	Ifx_Fifo_init,__Ifx_Fifo_init_function_end-Ifx_Fifo_init
.L229:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_beginRead',code,cluster('Ifx_Fifo_beginRead')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_beginRead'
	.align	2
	
; Function Ifx_Fifo_beginRead
.L179:
Ifx_Fifo_beginRead:	.type	func
	mfcr	d15,#65068
.L504:
	extr.u	d15,d15,#15,#1
.L505:
	ne	d0,d15,#0
.L506:
	j	.L5
.L5:
	disable
.L897:
	nop
.L898:
	j	.L6
.L6:
	ld.h	d15,[a4]4
.L899:
	j	.L7
.L7:
	min	d15,d4,d15
	extr	d1,d15,#0,#16
.L507:
	ld.h	d15,[a4]22
.L900:
	div	e2,d1,d15
.L901:
	sub	d1,d3
.L508:
	extr	d2,d1,#0,#16
.L509:
	mov	d15,#0
.L902:
	st.b	[a4]24,d15
.L903:
	sub	d4,d2
.L503:
	ld.h	d15,[a4]20
.L904:
	min	d15,d4,d15
.L905:
	st.w	[a4]6,d15
.L464:
	jeq	d0,#0,.L8
.L906:
	enable
.L8:
	j	.L9
.L9:
	ret
.L456:
	
__Ifx_Fifo_beginRead_function_end:
	.size	Ifx_Fifo_beginRead,__Ifx_Fifo_beginRead_function_end-Ifx_Fifo_beginRead
.L244:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_canReadCount',code,cluster('Ifx_Fifo_canReadCount')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_canReadCount'
	.align	2
	
	.global	Ifx_Fifo_canReadCount
; Function Ifx_Fifo_canReadCount
.L181:
Ifx_Fifo_canReadCount:	.type	func
	mov	d0,d4
.L511:
	ld.h	d15,[a4]22
.L661:
	jlt	d0,d15,.L10
.L662:
	ld.h	d15,[a4]20
.L663:
	jge	d15,d0,.L11
.L10:
	mov	d2,#0
.L513:
	j	.L12
.L11:
	mfcr	d15,#65068
.L514:
	extr.u	d15,d15,#15,#1
.L515:
	ne	d4,d15,#0
.L510:
	j	.L13
.L13:
	disable
.L664:
	nop
.L665:
	j	.L14
.L14:
	ld.h	d15,[a4]4
.L666:
	j	.L15
.L15:
	sub	d0,d15
.L512:
	jge	d0,#1,.L16
.L667:
	mov	d0,#0
.L516:
	st.w	[a4]6,d0
.L668:
	mov	d0,#1
.L669:
	st.b	[a4]24,d0
.L284:
	jeq	d4,#0,.L17
.L670:
	enable
.L17:
	mov	d2,#1
.L517:
	j	.L18
.L16:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e2,[a15]0
.L671:
	ne	d15,d6,d2
	or.ne	d15,d7,d3
.L672:
	jne	d15,#0,.L19
.L673:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e2,[a15]0
.L518:
	j	.L20
.L19:
	mfcr	d1,#65068
.L519:
	extr.u	d1,d1,#15,#1
.L520:
	ne	d15,d1,#0
.L521:
	j	.L21
.L21:
	disable
.L674:
	nop
.L675:
	j	.L22
.L22:
	j	.L23
.L23:
	ld.w	d1,0xf0000010
	mov	d3,#0
	mov	d2,d1
.L522:
	ld.w	d9,0xf000002c
.L676:
	mov	d8,#0
.L677:
	or	d2,d8
	or	d3,d9
.L678:
	j	.L24
.L24:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e8,[a15]0
.L679:
	and	d2,d8
.L523:
	and	d3,d9
.L309:
	jeq	d15,#0,.L25
.L680:
	enable
.L25:
	j	.L26
.L26:
	addx	d2,d2,d6
.L524:
	addc	d3,d3,d7
.L20:
	j	.L27
.L27:
	mov	d15,#0
.L681:
	st.b	[a4]24,d15
.L682:
	st.w	[a4]6,d0
.L313:
	jeq	d4,#0,.L28
.L683:
	enable
.L28:
	j	.L29
.L30:
.L29:
	ld.bu	d15,[a4]24
.L684:
	jne	d15,#0,.L31
.L315:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e0,[a15]0
.L685:
	ne	d15,d2,d0
	or.ne	d15,d3,d1
.L686:
	jne	d15,#0,.L32
.L687:
	mov	d15,#0
.L525:
	j	.L33
.L32:
	mfcr	d15,#65068
.L526:
	extr.u	d15,d15,#15,#1
.L527:
	ne	d15,d15,#0
.L528:
	j	.L34
.L34:
	disable
.L688:
	nop
.L689:
	j	.L35
.L35:
	j	.L36
.L36:
	ld.w	d0,0xf0000010
	mov	d5,#0
	mov	d4,d0
.L530:
	ld.w	d1,0xf000002c
.L690:
	mov	d0,#0
.L691:
	or	d0,d4
.L531:
	or	d1,d5
.L692:
	j	.L37
.L37:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e4,[a15]0
.L693:
	and	d0,d4
.L532:
	and	d1,d5
.L325:
	jeq	d15,#0,.L38
.L694:
	enable
.L38:
	j	.L39
.L39:
	ge.u	d15,d0,d2
.L529:
	and.eq	d15,d1,d3
	or.lt	d15,d3,d1
.L33:
	j	.L40
.L40:
	jeq	d15,#0,.L30
.L31:
	mov	d15,#0
.L695:
	st.w	[a4]6,d15
.L696:
	ld.bu	d15,[a4]24
.L697:
	eq	d2,d15,#1
.L18:
.L12:
	j	.L41
.L41:
	ret
.L261:
	
__Ifx_Fifo_canReadCount_function_end:
	.size	Ifx_Fifo_canReadCount,__Ifx_Fifo_canReadCount_function_end-Ifx_Fifo_canReadCount
.L204:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_readEnd',code,cluster('Ifx_Fifo_readEnd')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_readEnd'
	.align	2
	
; Function Ifx_Fifo_readEnd
.L183:
Ifx_Fifo_readEnd:	.type	func
	mfcr	d15,#65068
.L534:
	extr.u	d15,d15,#15,#1
.L535:
	ne	d0,d15,#0
.L536:
	j	.L42
.L42:
	disable
.L911:
	nop
.L912:
	j	.L43
.L43:
	ld.h	d15,[a4]4
.L913:
	sub	d15,d5
	st.h	[a4]4,d15
.L914:
	ld.w	d15,[a4]10
.L915:
	jeq	d15,#0,.L44
.L916:
	ld.w	d15,[a4]10
.L917:
	sub	d15,d5
	st.w	[a4]10,d15
.L918:
	ld.w	d15,[a4]10
.L919:
	jge	d15,#1,.L45
.L920:
	mov	d15,#0
.L921:
	st.w	[a4]10,d15
.L922:
	mov	d15,#1
.L923:
	st.b	[a4]25,d15
.L45:
.L44:
	jeq	d0,#0,.L46
.L924:
	enable
.L46:
	sub	d4,d5
.L533:
	extr	d2,d4,#0,#16
.L925:
	j	.L47
.L47:
	ret
.L466:
	
__Ifx_Fifo_readEnd_function_end:
	.size	Ifx_Fifo_readEnd,__Ifx_Fifo_readEnd_function_end-Ifx_Fifo_readEnd
.L249:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_read',code,cluster('Ifx_Fifo_read')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_read'
	.align	2
	
	.global	Ifx_Fifo_read
; Function Ifx_Fifo_read
.L185:
Ifx_Fifo_read:	.type	func
	sub.a	a10,#8
.L537:
	mov.aa	a15,a4
.L538:
	mov.aa	a12,a5
.L539:
	mov	d8,d4
.L541:
	mov	d9,#0
.L543:
	jeq	d8,#0,.L48
.L801:
	ld.a	a2,[a15]
.L802:
	st.a	[a10],a2
.L803:
	ld.h	d15,[a15]20
.L804:
	st.h	[a10]6,d15
.L805:
	ld.h	d15,[a15]16
.L806:
	st.h	[a10]4,d15
.L388:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L807:
	ne	d15,d6,d0
	or.ne	d15,d7,d1
.L808:
	jne	d15,#0,.L49
.L809:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e10,[a2]0
.L544:
	j	.L50
.L49:
	mfcr	d15,#65068
.L545:
	extr.u	d15,d15,#15,#1
.L546:
	ne	d15,d15,#0
.L547:
	j	.L51
.L51:
	disable
.L810:
	nop
.L811:
	j	.L52
.L52:
	j	.L53
.L53:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L548:
	ld.w	d1,0xf000002c
.L812:
	mov	d0,#0
.L813:
	or	d0,d2
.L549:
	or	d1,d3
.L814:
	j	.L54
.L54:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L815:
	and	d0,d2
.L550:
	and	d1,d3
.L397:
	jeq	d15,#0,.L55
.L816:
	enable
.L55:
	j	.L56
.L56:
	addx	d10,d0,d6
	addc	d11,d1,d7
.L50:
	j	.L57
.L57:
.L58:
	mov.aa	a4,a15
.L551:
	mov	d4,d8
.L553:
	call	Ifx_Fifo_beginRead
.L552:
	mov	d15,d2
.L555:
	jeq	d15,#0,.L59
.L817:
	lea	a4,[a10]0
.L818:
	mov.aa	a5,a12
.L557:
	mov	d4,d15
.L554:
	call	Ifx_CircularBuffer_read8
.L540:
	mov.aa	a12,a2
.L558:
	mov.aa	a4,a15
.L559:
	mov	e4,d15,d8
	call	Ifx_Fifo_readEnd
.L542:
	mov	d8,d2
.L59:
	jne	d9,#0,.L60
.L400:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L819:
	ne	d15,d10,d0
.L556:
	or.ne	d15,d11,d1
.L820:
	jne	d15,#0,.L61
.L821:
	mov	d15,#0
.L560:
	j	.L62
.L61:
	mfcr	d15,#65068
.L561:
	extr.u	d15,d15,#15,#1
.L562:
	ne	d15,d15,#0
.L563:
	j	.L63
.L63:
	disable
.L822:
	nop
.L823:
	j	.L64
.L64:
	j	.L65
.L65:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L565:
	ld.w	d1,0xf000002c
.L824:
	mov	d0,#0
.L825:
	or	d0,d2
.L566:
	or	d1,d3
.L826:
	j	.L66
.L66:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L827:
	and	d0,d2
.L567:
	and	d1,d3
.L408:
	jeq	d15,#0,.L67
.L828:
	enable
.L67:
	j	.L68
.L68:
	ge.u	d15,d0,d10
.L564:
	and.eq	d15,d1,d11
	or.lt	d15,d11,d1
.L62:
	j	.L69
.L69:
	jeq	d15,#0,.L70
.L60:
	mov	d15,#0
.L829:
	st.w	[a15]6,d15
.L830:
	j	.L71
.L70:
	jeq	d8,#0,.L72
.L831:
	j	.L73
.L74:
.L73:
	ld.bu	d15,[a15]24
.L832:
	jne	d15,#0,.L75
.L409:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L833:
	ne	d15,d10,d0
	or.ne	d15,d11,d1
.L834:
	jne	d15,#0,.L76
.L835:
	mov	d15,#0
.L568:
	j	.L77
.L76:
	mfcr	d15,#65068
.L569:
	extr.u	d15,d15,#15,#1
.L570:
	ne	d15,d15,#0
.L571:
	j	.L78
.L78:
	disable
.L836:
	nop
.L837:
	j	.L79
.L79:
	j	.L80
.L80:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L573:
	ld.w	d1,0xf000002c
.L838:
	mov	d0,#0
.L839:
	or	d0,d2
.L574:
	or	d1,d3
.L840:
	j	.L81
.L81:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L841:
	and	d0,d2
.L575:
	and	d1,d3
.L416:
	jeq	d15,#0,.L82
.L842:
	enable
.L82:
	j	.L83
.L83:
	ge.u	d15,d0,d10
.L572:
	and.eq	d15,d1,d11
	or.lt	d15,d11,d1
.L77:
	j	.L84
.L84:
	jeq	d15,#0,.L74
.L75:
	ld.bu	d15,[a15]24
.L843:
	eq	d9,d15,#0
.L72:
	jne	d8,#0,.L58
.L71:
	ld.hu	d15,[a10]4
.L844:
	st.h	[a15]16,d15
.L48:
	mov	d2,d8
.L576:
	j	.L85
.L85:
	ret
.L378:
	
__Ifx_Fifo_read_function_end:
	.size	Ifx_Fifo_read,__Ifx_Fifo_read_function_end-Ifx_Fifo_read
.L234:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_clear',code,cluster('Ifx_Fifo_clear')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_clear'
	.align	2
	
	.global	Ifx_Fifo_clear
; Function Ifx_Fifo_clear
.L187:
Ifx_Fifo_clear:	.type	func
	mfcr	d15,#65068
.L577:
	extr.u	d15,d15,#15,#1
.L578:
	ne	d0,d15,#0
.L579:
	j	.L86
.L86:
	disable
.L750:
	nop
.L751:
	j	.L87
.L87:
	ld.w	d15,[a4]10
.L752:
	jeq	d15,#0,.L88
.L753:
	mov	d15,#0
.L754:
	st.w	[a4]10,d15
.L755:
	mov	d15,#1
.L756:
	st.b	[a4]25,d15
.L88:
	mov	d15,#0
.L757:
	st.b	[a4]24,d15
.L758:
	mov	d15,#0
.L759:
	st.w	[a4]6,d15
.L760:
	mov	d15,#0
.L761:
	st.h	[a4]4,d15
.L762:
	mov	d15,#0
.L763:
	st.h	[a4]14,d15
.L764:
	ld.h	d15,[a4]18
.L765:
	st.h	[a4]16,d15
.L364:
	jeq	d0,#0,.L89
.L766:
	enable
.L89:
	ret
.L359:
	
__Ifx_Fifo_clear_function_end:
	.size	Ifx_Fifo_clear,__Ifx_Fifo_clear_function_end-Ifx_Fifo_clear
.L214:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_beginWrite',code,cluster('Ifx_Fifo_beginWrite')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_beginWrite'
	.align	2
	
; Function Ifx_Fifo_beginWrite
.L189:
Ifx_Fifo_beginWrite:	.type	func
	mfcr	d15,#65068
.L581:
	extr.u	d15,d15,#15,#1
.L582:
	ne	d0,d15,#0
.L583:
	j	.L90
.L90:
	disable
.L930:
	nop
.L931:
	j	.L91
.L91:
	ld.h	d1,[a4]4
.L932:
	j	.L92
.L92:
	ld.h	d15,[a4]20
.L933:
	sub	d15,d1
.L934:
	min	d15,d4,d15
	extr	d1,d15,#0,#16
.L584:
	ld.h	d15,[a4]22
.L935:
	div	e2,d1,d15
.L936:
	sub	d1,d3
.L585:
	extr	d2,d1,#0,#16
.L586:
	mov	d15,#0
.L937:
	st.b	[a4]25,d15
.L938:
	sub	d4,d2
.L580:
	ld.h	d15,[a4]20
.L939:
	min	d15,d4,d15
.L940:
	st.w	[a4]10,d15
.L482:
	jeq	d0,#0,.L93
.L941:
	enable
.L93:
	j	.L94
.L94:
	ret
.L474:
	
__Ifx_Fifo_beginWrite_function_end:
	.size	Ifx_Fifo_beginWrite,__Ifx_Fifo_beginWrite_function_end-Ifx_Fifo_beginWrite
.L254:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_canWriteCount',code,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_canWriteCount'
	.align	2
	
	.global	Ifx_Fifo_canWriteCount
; Function Ifx_Fifo_canWriteCount
.L191:
Ifx_Fifo_canWriteCount:	.type	func
	mov	d2,d4
.L588:
	ld.h	d15,[a4]22
.L702:
	jlt	d2,d15,.L95
.L703:
	ld.h	d15,[a4]20
.L704:
	jge	d15,d2,.L96
.L95:
	mov	d2,#0
.L589:
	j	.L97
.L96:
	mfcr	d15,#65068
.L591:
	extr.u	d15,d15,#15,#1
.L592:
	ne	d3,d15,#0
.L593:
	j	.L98
.L98:
	disable
.L705:
	nop
.L706:
	j	.L99
.L99:
	ld.h	d0,[a4]4
.L707:
	j	.L100
.L100:
	ld.h	d15,[a4]20
.L708:
	sub	d0,d15,d0
.L709:
	jlt	d0,d2,.L101
.L710:
	mov	d0,#0
.L711:
	st.w	[a4]10,d0
.L712:
	mov	d0,#1
.L713:
	st.b	[a4]25,d0
.L335:
	jeq	d3,#0,.L102
.L714:
	enable
.L102:
	mov	d2,#1
.L590:
	j	.L103
.L101:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e0,[a15]0
.L715:
	ne	d15,d6,d0
	or.ne	d15,d7,d1
.L716:
	jne	d15,#0,.L104
.L717:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e4,[a15]0
.L587:
	j	.L105
.L104:
	mfcr	d0,#65068
.L596:
	extr.u	d0,d0,#15,#1
.L597:
	ne	d15,d0,#0
.L598:
	j	.L106
.L106:
	disable
.L718:
	nop
.L719:
	j	.L107
.L107:
	j	.L108
.L108:
	ld.w	d0,0xf0000010
	mov	d5,#0
	mov	d4,d0
.L595:
	ld.w	d1,0xf000002c
.L720:
	mov	d0,#0
.L721:
	or	d0,d4
.L599:
	or	d1,d5
.L722:
	j	.L109
.L109:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e4,[a15]0
.L723:
	and	d0,d4
.L600:
	and	d1,d5
.L346:
	jeq	d15,#0,.L110
.L724:
	enable
.L110:
	j	.L111
.L111:
	addx	d4,d0,d6
	addc	d5,d1,d7
.L105:
	j	.L112
.L112:
	mov	d15,#0
.L725:
	st.b	[a4]25,d15
.L348:
	ld.h	d0,[a4]4
.L726:
	j	.L113
.L113:
	ld.h	d15,[a4]20
.L727:
	sub	d15,d0
.L728:
	sub	d2,d15
.L594:
	mov	d15,#0
.L729:
	max	d15,d15,d2
.L730:
	st.w	[a4]10,d15
.L349:
	jeq	d3,#0,.L114
.L731:
	enable
.L114:
	j	.L115
.L116:
.L115:
	ld.bu	d15,[a4]25
.L732:
	jne	d15,#0,.L117
.L350:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e0,[a15]0
.L733:
	ne	d15,d4,d0
	or.ne	d15,d5,d1
.L734:
	jne	d15,#0,.L118
.L735:
	mov	d15,#0
.L601:
	j	.L119
.L118:
	mfcr	d15,#65068
.L602:
	extr.u	d15,d15,#15,#1
.L603:
	ne	d15,d15,#0
.L604:
	j	.L120
.L120:
	disable
.L736:
	nop
.L737:
	j	.L121
.L121:
	j	.L122
.L122:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L606:
	ld.w	d1,0xf000002c
.L738:
	mov	d0,#0
.L739:
	or	d0,d2
.L607:
	or	d1,d3
.L740:
	j	.L123
.L123:
	movh.a	a15,#@his(.1.cnt)
	lea	a15,[a15]@los(.1.cnt)
	ld.d	e2,[a15]0
.L741:
	and	d0,d2
.L608:
	and	d1,d3
.L358:
	jeq	d15,#0,.L124
.L742:
	enable
.L124:
	j	.L125
.L125:
	ge.u	d15,d0,d4
.L605:
	and.eq	d15,d1,d5
	or.lt	d15,d5,d1
.L119:
	j	.L126
.L126:
	jeq	d15,#0,.L116
.L117:
	mov	d15,#0
.L743:
	st.w	[a4]10,d15
.L744:
	ld.bu	d15,[a4]25
.L745:
	eq	d2,d15,#1
.L103:
.L97:
	j	.L127
.L127:
	ret
.L326:
	
__Ifx_Fifo_canWriteCount_function_end:
	.size	Ifx_Fifo_canWriteCount,__Ifx_Fifo_canWriteCount_function_end-Ifx_Fifo_canWriteCount
.L209:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_endWrite',code,cluster('Ifx_Fifo_endWrite')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_endWrite'
	.align	2
	
; Function Ifx_Fifo_endWrite
.L193:
Ifx_Fifo_endWrite:	.type	func
	mfcr	d15,#65068
.L610:
	extr.u	d15,d15,#15,#1
.L611:
	ne	d1,d15,#0
.L612:
	j	.L128
.L128:
	disable
.L946:
	nop
.L947:
	j	.L129
.L129:
	ld.h	d15,[a4]4
.L948:
	add	d0,d15,d5
	st.h	[a4]4,d0
.L949:
	ld.h	d0,[a4]14
.L950:
	ld.h	d15,[a4]4
.L951:
	max	d15,d0,d15
.L952:
	st.h	[a4]14,d15
.L953:
	ld.w	d15,[a4]6
.L954:
	jeq	d15,#0,.L130
.L955:
	ld.w	d15,[a4]6
.L956:
	sub	d15,d5
	st.w	[a4]6,d15
.L957:
	ld.w	d15,[a4]6
.L958:
	jge	d15,#1,.L131
.L959:
	mov	d15,#0
.L960:
	st.w	[a4]6,d15
.L961:
	mov	d15,#1
.L962:
	st.b	[a4]24,d15
.L131:
.L130:
	jeq	d1,#0,.L132
.L963:
	enable
.L132:
	sub	d4,d5
.L609:
	extr	d2,d4,#0,#16
.L964:
	j	.L133
.L133:
	ret
.L484:
	
__Ifx_Fifo_endWrite_function_end:
	.size	Ifx_Fifo_endWrite,__Ifx_Fifo_endWrite_function_end-Ifx_Fifo_endWrite
.L259:
	; End of function
	
	.sdecl	'.text.Ifx_Fifo.Ifx_Fifo_write',code,cluster('Ifx_Fifo_write')
	.sect	'.text.Ifx_Fifo.Ifx_Fifo_write'
	.align	2
	
	.global	Ifx_Fifo_write
; Function Ifx_Fifo_write
.L195:
Ifx_Fifo_write:	.type	func
	sub.a	a10,#8
.L613:
	mov.aa	a15,a4
.L614:
	mov.aa	a12,a5
.L615:
	mov	d8,d4
.L617:
	mov	d9,#0
.L619:
	jeq	d8,#0,.L134
.L849:
	ld.a	a2,[a15]
.L850:
	st.a	[a10],a2
.L851:
	ld.h	d15,[a15]20
.L852:
	st.h	[a10]6,d15
.L853:
	ld.h	d15,[a15]18
.L854:
	st.h	[a10]4,d15
.L427:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L855:
	ne	d15,d6,d0
	or.ne	d15,d7,d1
.L856:
	jne	d15,#0,.L135
.L857:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e10,[a2]0
.L620:
	j	.L136
.L135:
	mfcr	d15,#65068
.L621:
	extr.u	d15,d15,#15,#1
.L622:
	ne	d15,d15,#0
.L623:
	j	.L137
.L137:
	disable
.L858:
	nop
.L859:
	j	.L138
.L138:
	j	.L139
.L139:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L624:
	ld.w	d1,0xf000002c
.L860:
	mov	d0,#0
.L861:
	or	d0,d2
.L625:
	or	d1,d3
.L862:
	j	.L140
.L140:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L863:
	and	d0,d2
.L626:
	and	d1,d3
.L436:
	jeq	d15,#0,.L141
.L864:
	enable
.L141:
	j	.L142
.L142:
	addx	d10,d0,d6
	addc	d11,d1,d7
.L136:
	j	.L143
.L143:
.L144:
	mov.aa	a4,a15
.L627:
	mov	d4,d8
.L629:
	call	Ifx_Fifo_beginWrite
.L628:
	mov	d15,d2
.L631:
	jeq	d15,#0,.L145
.L865:
	lea	a4,[a10]0
.L866:
	mov.aa	a5,a12
.L633:
	mov	d4,d15
.L630:
	call	Ifx_CircularBuffer_write8
.L616:
	mov.aa	a12,a2
.L634:
	mov.aa	a4,a15
.L635:
	mov	e4,d15,d8
	call	Ifx_Fifo_endWrite
.L618:
	mov	d8,d2
.L145:
	jne	d9,#0,.L146
.L439:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L867:
	ne	d15,d10,d0
.L632:
	or.ne	d15,d11,d1
.L868:
	jne	d15,#0,.L147
.L869:
	mov	d15,#0
.L636:
	j	.L148
.L147:
	mfcr	d15,#65068
.L637:
	extr.u	d15,d15,#15,#1
.L638:
	ne	d15,d15,#0
.L639:
	j	.L149
.L149:
	disable
.L870:
	nop
.L871:
	j	.L150
.L150:
	j	.L151
.L151:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L641:
	ld.w	d1,0xf000002c
.L872:
	mov	d0,#0
.L873:
	or	d0,d2
.L642:
	or	d1,d3
.L874:
	j	.L152
.L152:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L875:
	and	d0,d2
.L643:
	and	d1,d3
.L447:
	jeq	d15,#0,.L153
.L876:
	enable
.L153:
	j	.L154
.L154:
	ge.u	d15,d0,d10
.L640:
	and.eq	d15,d1,d11
	or.lt	d15,d11,d1
.L148:
	j	.L155
.L155:
	jeq	d15,#0,.L156
.L146:
	mov	d15,#0
.L877:
	st.w	[a15]10,d15
.L878:
	j	.L157
.L156:
	jeq	d8,#0,.L158
.L879:
	j	.L159
.L160:
.L159:
	ld.bu	d15,[a15]25
.L880:
	jne	d15,#0,.L161
.L448:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e0,[a2]0
.L881:
	ne	d15,d10,d0
	or.ne	d15,d11,d1
.L882:
	jne	d15,#0,.L162
.L883:
	mov	d15,#0
.L644:
	j	.L163
.L162:
	mfcr	d15,#65068
.L645:
	extr.u	d15,d15,#15,#1
.L646:
	ne	d15,d15,#0
.L647:
	j	.L164
.L164:
	disable
.L884:
	nop
.L885:
	j	.L165
.L165:
	j	.L166
.L166:
	ld.w	d0,0xf0000010
	mov	d3,#0
	mov	d2,d0
.L649:
	ld.w	d1,0xf000002c
.L886:
	mov	d0,#0
.L887:
	or	d0,d2
.L650:
	or	d1,d3
.L888:
	j	.L167
.L167:
	movh.a	a2,#@his(.1.cnt)
	lea	a2,[a2]@los(.1.cnt)
	ld.d	e2,[a2]0
.L889:
	and	d0,d2
.L651:
	and	d1,d3
.L455:
	jeq	d15,#0,.L168
.L890:
	enable
.L168:
	j	.L169
.L169:
	ge.u	d15,d0,d10
.L648:
	and.eq	d15,d1,d11
	or.lt	d15,d11,d1
.L163:
	j	.L170
.L170:
	jeq	d15,#0,.L160
.L161:
	ld.bu	d15,[a15]25
.L891:
	eq	d9,d15,#0
.L158:
	jne	d8,#0,.L144
.L157:
	ld.hu	d15,[a10]4
.L892:
	st.h	[a15]18,d15
.L134:
	mov	d2,d8
.L652:
	j	.L171
.L171:
	ret
.L417:
	
__Ifx_Fifo_write_function_end:
	.size	Ifx_Fifo_write,__Ifx_Fifo_write_function_end-Ifx_Fifo_write
.L239:
	; End of function
	
	.calls	'Ifx_Fifo_create','malloc'
	.calls	'Ifx_Fifo_create','Ifx_Fifo_init'
	.calls	'Ifx_Fifo_destroy','free'
	.calls	'Ifx_Fifo_read','Ifx_Fifo_beginRead'
	.calls	'Ifx_Fifo_read','Ifx_CircularBuffer_read8'
	.calls	'Ifx_Fifo_read','Ifx_Fifo_readEnd'
	.calls	'Ifx_Fifo_write','Ifx_Fifo_beginWrite'
	.calls	'Ifx_Fifo_write','Ifx_CircularBuffer_write8'
	.calls	'Ifx_Fifo_write','Ifx_Fifo_endWrite'
	.calls	'Ifx_Fifo_create','',0
	.calls	'Ifx_Fifo_destroy','',0
	.calls	'Ifx_Fifo_init','',0
	.calls	'Ifx_Fifo_beginRead','',0
	.calls	'Ifx_Fifo_canReadCount','',0
	.calls	'Ifx_Fifo_readEnd','',0
	.calls	'Ifx_Fifo_read','',8
	.calls	'Ifx_Fifo_clear','',0
	.calls	'Ifx_Fifo_beginWrite','',0
	.calls	'Ifx_Fifo_canWriteCount','',0
	.calls	'Ifx_Fifo_endWrite','',0
	.extern	malloc
	.extern	free
	.extern	Ifx_CircularBuffer_read8
	.extern	Ifx_CircularBuffer_write8
	.calls	'Ifx_Fifo_write','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L197:
	.word	82333
	.half	3
	.word	.L198
	.byte	4
.L196:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L199
	.byte	2,1,1,3
	.word	242
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	245
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	290
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	302
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0
.L306:
	.byte	7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0
.L373:
	.byte	3
	.word	414
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	388
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	420
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	420
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	388
	.byte	6,0
.L264:
	.byte	7
	.byte	'short int',0,2,5
.L270:
	.byte	7
	.byte	'long int',0,4,5,10,3,60,9,12,11
	.byte	'count',0
	.word	506
	.byte	2,2,35,0,11
	.byte	'readerWaitx',0
	.word	519
	.byte	4,2,35,2,11
	.byte	'writerWaitx',0
	.word	519
	.byte	4,2,35,6,11
	.byte	'maxcount',0
	.word	506
	.byte	2,2,35,10,0
.L260:
	.byte	7
	.byte	'unsigned char',0,1,8,12
	.word	612
	.byte	12
	.word	612
	.byte	13
	.byte	'_Fifo',0,3,73,16,28,11
	.byte	'buffer',0
	.word	420
	.byte	4,2,35,0,11
	.byte	'shared',0
	.word	531
	.byte	12,2,35,4,11
	.byte	'startIndex',0
	.word	506
	.byte	2,2,35,16,11
	.byte	'endIndex',0
	.word	506
	.byte	2,2,35,18,11
	.byte	'size',0
	.word	506
	.byte	2,2,35,20,11
	.byte	'elementSize',0
	.word	506
	.byte	2,2,35,22,11
	.byte	'eventReader',0
	.word	629
	.byte	1,2,35,24,11
	.byte	'eventWriter',0
	.word	634
	.byte	1,2,35,25,0
.L262:
	.byte	3
	.word	639
.L279:
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,3,206,1,22
	.word	506
	.byte	1,1
.L280:
	.byte	5
	.byte	'fifo',0,3,206,1,51
	.word	798
.L282:
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,13
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,241,8,16,4,14
	.byte	'ENDINIT',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'LCK',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'PW',0,4
	.word	873
	.byte	14,16,2,35,0,14
	.byte	'REL',0,4
	.word	873
	.byte	16,0,2,35,0,0,15,5,247,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	889
	.byte	4,2,35,0,0,7
	.byte	'unsigned short int',0,2,7,13
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,250,8,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'IR0',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'DR',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'IR1',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'UR',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PAR',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'TCR',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'TCTR',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,5,255,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	1047
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,137,9,16,4,14
	.byte	'AE',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'OE',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'IS0',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'DS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'TO',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'IS1',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'US',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PAS',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'TCS',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'TCT',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'TIM',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,5,135,15,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	1291
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_SCU_WDTCPU',0,5,175,15,25,12,11
	.byte	'CON0',0
	.word	985
	.byte	4,2,35,0,11
	.byte	'CON1',0
	.word	1251
	.byte	4,2,35,4,11
	.byte	'SR',0
	.word	1482
	.byte	4,2,35,8,0,12
	.word	1522
	.byte	3
	.word	1585
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,4,181,3,17,1,1,5
	.byte	'watchdog',0,4,181,3,65
	.word	1590
	.byte	5
	.byte	'password',0,4,181,3,82
	.word	1025
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,4,140,4,17,1,1,5
	.byte	'watchdog',0,4,140,4,63
	.word	1590
	.byte	5
	.byte	'password',0,4,140,4,80
	.word	1025
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,4,227,3,19
	.word	1025
	.byte	1,1,5
	.byte	'watchdog',0,4,227,3,74
	.word	1590
	.byte	6,0,13
	.byte	'_Ifx_P_OUT_Bits',0,7,143,3,16,4,14
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'P2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'P3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'P4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'P5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'P6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'P7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'P8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'P9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'P10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'P11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'P12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'P13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'P14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'P15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,181,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	1820
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMR_Bits',0,7,169,2,16,4,14
	.byte	'PS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PS2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PS3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PS4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PS5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PS6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PS7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PS8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PS9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'PS10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'PS11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'PS12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'PS13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PS14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'PS15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'PCL0',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'PCL1',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'PCL2',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'PCL3',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'PCL4',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'PCL5',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'PCL6',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'PCL7',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'PCL8',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'PCL9',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'PCL10',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'PCL11',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'PCL12',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'PCL13',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'PCL14',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'PCL15',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,133,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	2136
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_ID_Bits',0,7,110,16,4,14
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'MODNUMBER',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,148,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	2707
	.byte	4,2,35,0,0,16,4
	.word	612
	.byte	17,3,0,13
	.byte	'_Ifx_P_IOCR0_Bits',0,7,140,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PC0',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PC1',0,1
	.word	612
	.byte	5,0,2,35,1,14
	.byte	'reserved_16',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PC2',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PC3',0,1
	.word	612
	.byte	5,0,2,35,3,0,15,7,164,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	2835
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_IOCR4_Bits',0,7,166,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PC4',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PC5',0,1
	.word	612
	.byte	5,0,2,35,1,14
	.byte	'reserved_16',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PC6',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PC7',0,1
	.word	612
	.byte	5,0,2,35,3,0,15,7,180,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	3050
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_IOCR8_Bits',0,7,179,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PC8',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PC9',0,1
	.word	612
	.byte	5,0,2,35,1,14
	.byte	'reserved_16',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PC10',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PC11',0,1
	.word	612
	.byte	5,0,2,35,3,0,15,7,188,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	3265
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_IOCR12_Bits',0,7,153,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PC12',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PC13',0,1
	.word	612
	.byte	5,0,2,35,1,14
	.byte	'reserved_16',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PC14',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PC15',0,1
	.word	612
	.byte	5,0,2,35,3,0,15,7,172,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	3482
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_IN_Bits',0,7,118,16,4,14
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'P2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'P3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'P4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'P5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'P6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'P7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'P8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'P9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'P10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'P11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'P12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'P13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'P14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'P15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,156,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	3702
	.byte	4,2,35,0,0,16,24
	.word	612
	.byte	17,23,0,13
	.byte	'_Ifx_P_PDR0_Bits',0,7,205,3,16,4,14
	.byte	'PD0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PL0',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PD1',0,1
	.word	612
	.byte	3,1,2,35,0,14
	.byte	'PL1',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PD2',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PL2',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'PD3',0,1
	.word	612
	.byte	3,1,2,35,1,14
	.byte	'PL3',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'PD4',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PL4',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'PD5',0,1
	.word	612
	.byte	3,1,2,35,2,14
	.byte	'PL5',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'PD6',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PL6',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'PD7',0,1
	.word	612
	.byte	3,1,2,35,3,14
	.byte	'PL7',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,205,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	4025
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_PDR1_Bits',0,7,226,3,16,4,14
	.byte	'PD8',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PL8',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PD9',0,1
	.word	612
	.byte	3,1,2,35,0,14
	.byte	'PL9',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PD10',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'PL10',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'PD11',0,1
	.word	612
	.byte	3,1,2,35,1,14
	.byte	'PL11',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'PD12',0,1
	.word	612
	.byte	3,5,2,35,2,14
	.byte	'PL12',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'PD13',0,1
	.word	612
	.byte	3,1,2,35,2,14
	.byte	'PL13',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'PD14',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'PL14',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'PD15',0,1
	.word	612
	.byte	3,1,2,35,3,14
	.byte	'PL15',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,213,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	4329
	.byte	4,2,35,0,0,16,8
	.word	612
	.byte	17,7,0,13
	.byte	'_Ifx_P_ESR_Bits',0,7,88,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,140,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	4654
	.byte	4,2,35,0,0,16,12
	.word	612
	.byte	17,11,0,13
	.byte	'_Ifx_P_PDISC_Bits',0,7,183,3,16,4,14
	.byte	'PDIS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PDIS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PDIS2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PDIS3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PDIS4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PDIS5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PDIS6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PDIS7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PDIS8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PDIS9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'PDIS10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'PDIS11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'PDIS12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'PDIS13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PDIS14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'PDIS15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,197,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	4994
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_PCSR_Bits',0,7,165,3,16,4,14
	.byte	'SEL0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SEL1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'SEL2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SEL3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'SEL4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'SEL5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'SEL6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'SEL7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	2,6,2,35,1,14
	.byte	'SEL10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'SEL11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'reserved_12',0,4
	.word	850
	.byte	19,1,2,35,0,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,189,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	5360
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMSR0_Bits',0,7,206,2,16,4,14
	.byte	'PS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PS2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PS3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,15,7,149,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	5646
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMSR4_Bits',0,7,227,2,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'PS4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PS5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PS6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PS7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,15,7,165,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	5793
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMSR8_Bits',0,7,238,2,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'PS8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PS9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'PS10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'PS11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'reserved_12',0,4
	.word	850
	.byte	20,0,2,35,0,0,15,7,173,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	5962
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMSR12_Bits',0,7,216,2,16,4,14
	.byte	'reserved_0',0,2
	.word	1025
	.byte	12,4,2,35,0,14
	.byte	'PS12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'PS13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PS14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'PS15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,157,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6134
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMCR0_Bits',0,7,232,1,16,4,14
	.byte	'reserved_0',0,2
	.word	1025
	.byte	16,0,2,35,0,14
	.byte	'PCL0',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'PCL1',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'PCL2',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'PCL3',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'reserved_20',0,2
	.word	1025
	.byte	12,0,2,35,2,0,15,7,229,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6309
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMCR4_Bits',0,7,253,1,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	20,12,2,35,0,14
	.byte	'PCL4',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'PCL5',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'PCL6',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'PCL7',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,15,7,245,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6483
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMCR8_Bits',0,7,136,2,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	24,8,2,35,0,14
	.byte	'PCL8',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'PCL9',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'PCL10',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'PCL11',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,15,7,253,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6657
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMCR12_Bits',0,7,243,1,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	28,4,2,35,0,14
	.byte	'PCL12',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'PCL13',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'PCL14',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'PCL15',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,237,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6833
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMSR_Bits',0,7,249,2,16,4,14
	.byte	'PS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PS2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PS3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PS4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PS5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PS6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PS7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PS8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PS9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'PS10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'PS11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'PS12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'PS13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PS14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'PS15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,141,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	6989
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_OMCR_Bits',0,7,147,2,16,4,14
	.byte	'reserved_0',0,2
	.word	1025
	.byte	16,0,2,35,0,14
	.byte	'PCL0',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'PCL1',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'PCL2',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'PCL3',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'PCL4',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'PCL5',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'PCL6',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'PCL7',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'PCL8',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'PCL9',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'PCL10',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'PCL11',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'PCL12',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'PCL13',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'PCL14',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'PCL15',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,221,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	7322
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_LPCR0_Bits',0,7,192,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,15,7,196,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	7670
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_LPCR1_Bits',0,7,200,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,13
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,7,208,1,16,4,14
	.byte	'RDIS_CTRL',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'RX_DIS',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'TERM',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'LRXTERM',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,15,7,204,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	7794
	.byte	4,2,35,0,11
	.byte	'B_P21',0
	.word	7878
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_LPCR2_Bits',0,7,218,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'LVDSR',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'LVDSRL',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'reserved_10',0,1
	.word	612
	.byte	2,4,2,35,1,14
	.byte	'TDIS_CTRL',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'TX_DIS',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'TX_PD',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'TX_PWDPD',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,7,213,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	8058
	.byte	4,2,35,0,0,16,76
	.word	612
	.byte	17,75,0,13
	.byte	'_Ifx_P_ACCEN1_Bits',0,7,82,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,7,132,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	8311
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P_ACCEN0_Bits',0,7,45,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,7,252,3,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	8398
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_P',0,7,229,5,25,128,2,11
	.byte	'OUT',0
	.word	2096
	.byte	4,2,35,0,11
	.byte	'OMR',0
	.word	2667
	.byte	4,2,35,4,11
	.byte	'ID',0
	.word	2786
	.byte	4,2,35,8,11
	.byte	'reserved_C',0
	.word	2826
	.byte	4,2,35,12,11
	.byte	'IOCR0',0
	.word	3010
	.byte	4,2,35,16,11
	.byte	'IOCR4',0
	.word	3225
	.byte	4,2,35,20,11
	.byte	'IOCR8',0
	.word	3442
	.byte	4,2,35,24,11
	.byte	'IOCR12',0
	.word	3662
	.byte	4,2,35,28,11
	.byte	'reserved_20',0
	.word	2826
	.byte	4,2,35,32,11
	.byte	'IN',0
	.word	3976
	.byte	4,2,35,36,11
	.byte	'reserved_28',0
	.word	4016
	.byte	24,2,35,40,11
	.byte	'PDR0',0
	.word	4289
	.byte	4,2,35,64,11
	.byte	'PDR1',0
	.word	4605
	.byte	4,2,35,68,11
	.byte	'reserved_48',0
	.word	4645
	.byte	8,2,35,72,11
	.byte	'ESR',0
	.word	4945
	.byte	4,2,35,80,11
	.byte	'reserved_54',0
	.word	4985
	.byte	12,2,35,84,11
	.byte	'PDISC',0
	.word	5320
	.byte	4,2,35,96,11
	.byte	'PCSR',0
	.word	5606
	.byte	4,2,35,100,11
	.byte	'reserved_68',0
	.word	4645
	.byte	8,2,35,104,11
	.byte	'OMSR0',0
	.word	5753
	.byte	4,2,35,112,11
	.byte	'OMSR4',0
	.word	5922
	.byte	4,2,35,116,11
	.byte	'OMSR8',0
	.word	6094
	.byte	4,2,35,120,11
	.byte	'OMSR12',0
	.word	6269
	.byte	4,2,35,124,11
	.byte	'OMCR0',0
	.word	6443
	.byte	4,3,35,128,1,11
	.byte	'OMCR4',0
	.word	6617
	.byte	4,3,35,132,1,11
	.byte	'OMCR8',0
	.word	6793
	.byte	4,3,35,136,1,11
	.byte	'OMCR12',0
	.word	6949
	.byte	4,3,35,140,1,11
	.byte	'OMSR',0
	.word	7282
	.byte	4,3,35,144,1,11
	.byte	'OMCR',0
	.word	7630
	.byte	4,3,35,148,1,11
	.byte	'reserved_98',0
	.word	4645
	.byte	8,3,35,152,1,11
	.byte	'LPCR0',0
	.word	7754
	.byte	4,3,35,160,1,11
	.byte	'LPCR1',0
	.word	8003
	.byte	4,3,35,164,1,11
	.byte	'LPCR2',0
	.word	8262
	.byte	4,3,35,168,1,11
	.byte	'reserved_A4',0
	.word	8302
	.byte	76,3,35,172,1,11
	.byte	'ACCEN1',0
	.word	8358
	.byte	4,3,35,248,1,11
	.byte	'ACCEN0',0
	.word	8925
	.byte	4,3,35,252,1,0,12
	.word	8965
	.byte	3
	.word	9568
	.byte	18,6,172,1,9,4,19
	.byte	'IfxPort_State_notChanged',0,0,19
	.byte	'IfxPort_State_high',0,1,19
	.byte	'IfxPort_State_low',0,128,128,4,19
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,6,208,4,17,1,1,5
	.byte	'port',0,6,208,4,44
	.word	9573
	.byte	5
	.byte	'pinIndex',0,6,208,4,56
	.word	612
	.byte	5
	.byte	'action',0,6,208,4,80
	.word	9578
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,8,226,8,20
	.word	302
	.byte	1,1,6,0,18,10,156,1,9,1,19
	.byte	'IfxCpu_ResourceCpu_0',0,0,19
	.byte	'IfxCpu_ResourceCpu_1',0,1,19
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,9,141,6,31
	.word	9799
	.byte	1,1,6,0
.L275:
	.byte	8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,9,139,5,20
	.word	612
	.byte	1,1
.L276:
	.byte	6,0
.L272:
	.byte	8
	.byte	'IfxCpu_disableInterrupts',0,3,9,147,5,20
	.word	612
	.byte	1,1
.L273:
	.byte	20,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,9,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,9,225,5,17,1,1,6,0
.L283:
	.byte	4
	.byte	'IfxCpu_restoreInterrupts',0,3,9,168,7,17,1,1
.L285:
	.byte	5
	.byte	'enabled',0,9,168,7,50
	.word	612
.L287:
	.byte	6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,9,161,6,19
	.word	10121
	.byte	1,1,5
	.byte	'address',0,9,161,6,55
	.word	1025
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,9,190,6,20
	.word	612
	.byte	1,1,5
	.byte	'address',0,9,190,6,70
	.word	1025
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,9,172,8,17,1,1,5
	.byte	'address',0,9,172,8,56
	.word	10121
	.byte	5
	.byte	'count',0,9,172,8,72
	.word	10121
	.byte	20,6,0,0,13
	.byte	'_Ifx_SRC_SRCR_Bits',0,12,45,16,4,14
	.byte	'SRPN',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	2,6,2,35,1,14
	.byte	'SRE',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'TOS',0,1
	.word	612
	.byte	2,3,2,35,1,14
	.byte	'reserved_13',0,1
	.word	612
	.byte	3,0,2,35,1,14
	.byte	'ECC',0,1
	.word	612
	.byte	6,2,2,35,2,14
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'SRR',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'CLRR',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'SETR',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'IOV',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'IOVCLR',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'SWS',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'SWSCLR',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,12,70,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	10352
	.byte	4,2,35,0,0,12
	.word	10642
	.byte	3
	.word	10681
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,11,250,1,17,1,1,5
	.byte	'src',0,11,250,1,60
	.word	10686
	.byte	6,0,13
	.byte	'_Ifx_STM_CLC_Bits',0,14,100,16,4,14
	.byte	'DISR',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'DISS',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EDIS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,15,14,149,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	10734
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_ID_Bits',0,14,142,1,16,4,14
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'MODNUMBER',0,2
	.word	1025
	.byte	16,0,2,35,2,0,15,14,181,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	10890
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM0_Bits',0,14,192,1,16,4,14
	.byte	'STM31_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,229,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11012
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM1_Bits',0,14,204,1,16,4,14
	.byte	'STM35_4',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,245,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11097
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM2_Bits',0,14,210,1,16,4,14
	.byte	'STM39_8',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,253,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11182
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM3_Bits',0,14,216,1,16,4,14
	.byte	'STM43_12',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,133,3,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11267
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM4_Bits',0,14,222,1,16,4,14
	.byte	'STM47_16',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,141,3,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11353
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM5_Bits',0,14,228,1,16,4,14
	.byte	'STM51_20',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,149,3,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11439
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM6_Bits',0,14,234,1,16,4,14
	.byte	'STM63_32',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,157,3,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11525
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_CAP_Bits',0,14,88,16,4,14
	.byte	'STMCAP63_32',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,133,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11611
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_CMP_Bits',0,14,123,16,4,14
	.byte	'CMPVAL',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,165,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11698
	.byte	4,2,35,0,0,16,8
	.word	11740
	.byte	17,1,0,13
	.byte	'_Ifx_STM_CMCON_Bits',0,14,110,16,4,14
	.byte	'MSIZE0',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,14
	.byte	'MSTART0',0,1
	.word	612
	.byte	5,3,2,35,1,14
	.byte	'reserved_13',0,1
	.word	612
	.byte	3,0,2,35,1,14
	.byte	'MSIZE1',0,1
	.word	612
	.byte	5,3,2,35,2,14
	.byte	'reserved_21',0,1
	.word	612
	.byte	3,0,2,35,2,14
	.byte	'MSTART1',0,1
	.word	612
	.byte	5,3,2,35,3,14
	.byte	'reserved_29',0,1
	.word	612
	.byte	3,0,2,35,3,0,15,14,157,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	11789
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_ICR_Bits',0,14,129,1,16,4,14
	.byte	'CMP0EN',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'CMP0IR',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'CMP0OS',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'CMP1EN',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'CMP1IR',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'CMP1OS',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'reserved_7',0,4
	.word	850
	.byte	25,0,2,35,0,0,15,14,173,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12020
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_ISCR_Bits',0,14,150,1,16,4,14
	.byte	'CMP0IRR',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'CMP0IRS',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'CMP1IRR',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'CMP1IRS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,15,14,189,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12237
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_TIM0SV_Bits',0,14,198,1,16,4,14
	.byte	'STM31_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,237,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12401
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_CAPSV_Bits',0,14,94,16,4,14
	.byte	'STMCAP63_32',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,141,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12488
	.byte	4,2,35,0,0,16,144,1
	.word	612
	.byte	17,143,1,0,13
	.byte	'_Ifx_STM_OCS_Bits',0,14,182,1,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	24,8,2,35,0,14
	.byte	'SUS',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'SUS_P',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'SUSSTA',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	2,0,2,35,3,0,15,14,221,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12588
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,14,175,1,16,4,14
	.byte	'CLR',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,4
	.word	850
	.byte	31,0,2,35,0,0,15,14,213,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12748
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_KRST1_Bits',0,14,168,1,16,4,14
	.byte	'RST',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,4
	.word	850
	.byte	31,0,2,35,0,0,15,14,205,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12854
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_KRST0_Bits',0,14,160,1,16,4,14
	.byte	'RST',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'RSTSTAT',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,15,14,197,2,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	12958
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_ACCEN1_Bits',0,14,82,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,15,14,253,1,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	13081
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM_ACCEN0_Bits',0,14,45,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,15,14,245,1,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	13170
	.byte	4,2,35,0,0,13
	.byte	'_Ifx_STM',0,14,173,3,25,128,2,11
	.byte	'CLC',0
	.word	10850
	.byte	4,2,35,0,11
	.byte	'reserved_4',0
	.word	2826
	.byte	4,2,35,4,11
	.byte	'ID',0
	.word	10972
	.byte	4,2,35,8,11
	.byte	'reserved_C',0
	.word	2826
	.byte	4,2,35,12,11
	.byte	'TIM0',0
	.word	11057
	.byte	4,2,35,16,11
	.byte	'TIM1',0
	.word	11142
	.byte	4,2,35,20,11
	.byte	'TIM2',0
	.word	11227
	.byte	4,2,35,24,11
	.byte	'TIM3',0
	.word	11313
	.byte	4,2,35,28,11
	.byte	'TIM4',0
	.word	11399
	.byte	4,2,35,32,11
	.byte	'TIM5',0
	.word	11485
	.byte	4,2,35,36,11
	.byte	'TIM6',0
	.word	11571
	.byte	4,2,35,40,11
	.byte	'CAP',0
	.word	11658
	.byte	4,2,35,44,11
	.byte	'CMP',0
	.word	11780
	.byte	8,2,35,48,11
	.byte	'CMCON',0
	.word	11980
	.byte	4,2,35,56,11
	.byte	'ICR',0
	.word	12197
	.byte	4,2,35,60,11
	.byte	'ISCR',0
	.word	12361
	.byte	4,2,35,64,11
	.byte	'reserved_44',0
	.word	4985
	.byte	12,2,35,68,11
	.byte	'TIM0SV',0
	.word	12448
	.byte	4,2,35,80,11
	.byte	'CAPSV',0
	.word	12537
	.byte	4,2,35,84,11
	.byte	'reserved_58',0
	.word	12577
	.byte	144,1,2,35,88,11
	.byte	'OCS',0
	.word	12708
	.byte	4,3,35,232,1,11
	.byte	'KRSTCLR',0
	.word	12814
	.byte	4,3,35,236,1,11
	.byte	'KRST1',0
	.word	12918
	.byte	4,3,35,240,1,11
	.byte	'KRST0',0
	.word	13041
	.byte	4,3,35,244,1,11
	.byte	'ACCEN1',0
	.word	13130
	.byte	4,3,35,248,1,11
	.byte	'ACCEN0',0
	.word	13699
	.byte	4,3,35,252,1,0,12
	.word	13739
	.byte	3
	.word	14159
.L302:
	.byte	8
	.byte	'IfxStm_get',0,3,13,162,4,19
	.word	388
	.byte	1,1
.L303:
	.byte	5
	.byte	'stm',0,13,162,4,39
	.word	14164
.L305:
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,13,179,4,20
	.word	302
	.byte	1,1,5
	.byte	'stm',0,13,179,4,49
	.word	14164
	.byte	20,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,13,190,4,19
	.word	10121
	.byte	1,1,5
	.byte	'stm',0,13,190,4,44
	.word	14164
	.byte	6,0
.L298:
	.byte	8
	.byte	'disableInterrupts',0,3,15,108,20
	.word	612
	.byte	1,1
.L299:
	.byte	20,6,0,0
.L308:
	.byte	4
	.byte	'restoreInterrupts',0,3,15,142,1,17,1,1
.L310:
	.byte	5
	.byte	'enabled',0,15,142,1,43
	.word	612
.L312:
	.byte	20,6,0,0
.L266:
	.byte	7
	.byte	'long long int',0,8,5
.L289:
	.byte	8
	.byte	'getDeadLine',0,3,15,164,2,25
	.word	14380
	.byte	1,1
.L290:
	.byte	5
	.byte	'timeout',0,15,164,2,50
	.word	14380
.L292:
	.byte	20,6,0,0
.L314:
	.byte	8
	.byte	'isDeadLine',0,3,15,211,2,20
	.word	612
	.byte	1,1
.L316:
	.byte	5
	.byte	'deadLine',0,15,211,2,44
	.word	14380
.L318:
	.byte	20,6,0,0
.L294:
	.byte	8
	.byte	'now',0,3,15,221,1,25
	.word	14380
	.byte	1,1
.L295:
	.byte	20,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,15,240,1,25
	.word	14380
	.byte	1,1,20,6,0,0,21
	.byte	'__max',0
	.word	866
	.byte	1,1,1,1,22
	.word	866
	.byte	22
	.word	866
	.byte	0,21
	.byte	'__min',0
	.word	866
	.byte	1,1,1,1,22
	.word	866
	.byte	22
	.word	866
	.byte	0,12
	.word	866
	.byte	21
	.byte	'__mfcr',0
	.word	14603
	.byte	1,1,1,1,22
	.word	866
	.byte	0,23
	.byte	'__nop',0,1,1,1,1,23
	.byte	'__disable',0,1,1,1,1,23
	.byte	'__enable',0,1,1,1,1,24
	.word	250
	.byte	25
	.word	276
	.byte	6,0,24
	.word	311
	.byte	25
	.word	343
	.byte	6,0,24
	.word	356
	.byte	6,0,24
	.word	425
	.byte	25
	.word	444
	.byte	6,0,24
	.word	460
	.byte	25
	.word	475
	.byte	25
	.word	489
	.byte	6,0,13
	.byte	'_Ifx_CPU_ICR_Bits',0,16,246,2,16,4,14
	.byte	'CCPN',0,4
	.word	873
	.byte	10,22,2,35,0,14
	.byte	'reserved_10',0,4
	.word	873
	.byte	5,17,2,35,0,14
	.byte	'IE',0,4
	.word	873
	.byte	1,16,2,35,0,14
	.byte	'PIPN',0,4
	.word	873
	.byte	10,6,2,35,0,14
	.byte	'reserved_26',0,4
	.word	873
	.byte	6,0,2,35,0,0
.L277:
	.byte	15,16,223,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	14730
	.byte	4,2,35,0,0
.L385:
	.byte	10,17,96,9,8,11
	.byte	'base',0
	.word	420
	.byte	4,2,35,0,11
	.byte	'index',0
	.word	1025
	.byte	2,2,35,4,11
	.byte	'length',0
	.word	1025
	.byte	2,2,35,6,0,26
	.word	414
.L419:
	.byte	3
	.word	14938
	.byte	24
	.word	803
	.byte	25
	.word	834
	.byte	6,0,27
	.byte	'malloc',0,18,78,33
	.word	420
	.byte	1,1,1,1,28,18,78,44
	.word	850
	.byte	0,29
	.byte	'free',0,18,80,33,1,1,1,1,28,18,80,49
	.word	420
	.byte	0,3
	.word	14887
	.byte	27
	.byte	'Ifx_CircularBuffer_read8',0,19,87,7
	.word	420
	.byte	1,1,1,1,5
	.byte	'buffer',0,19,87,52
	.word	15010
	.byte	5
	.byte	'data',0,19,87,66
	.word	420
	.byte	5
	.byte	'count',0,19,87,82
	.word	506
	.byte	0,27
	.byte	'Ifx_CircularBuffer_write8',0,19,107,13
	.word	14943
	.byte	1,1,1,1,5
	.byte	'buffer',0,19,107,59
	.word	15010
	.byte	5
	.byte	'data',0,19,107,79
	.word	14943
	.byte	5
	.byte	'count',0,19,107,95
	.word	506
	.byte	0,24
	.word	1595
	.byte	25
	.word	1635
	.byte	25
	.word	1653
	.byte	6,0,24
	.word	1673
	.byte	25
	.word	1711
	.byte	25
	.word	1729
	.byte	6,0,24
	.word	1749
	.byte	25
	.word	1800
	.byte	6,0,24
	.word	9681
	.byte	25
	.word	9709
	.byte	25
	.word	9723
	.byte	25
	.word	9741
	.byte	6,0,24
	.word	9759
	.byte	6,0,24
	.word	9878
	.byte	6,0,24
	.word	9912
	.byte	6,0,24
	.word	9954
	.byte	20,30
	.word	9912
	.byte	31
	.word	9952
	.byte	0,6,0,0,24
	.word	9995
	.byte	6,0,24
	.word	10029
	.byte	6,0,24
	.word	10069
	.byte	25
	.word	10102
	.byte	6,0,24
	.word	10142
	.byte	25
	.word	10183
	.byte	6,0,24
	.word	10202
	.byte	25
	.word	10257
	.byte	6,0,24
	.word	10276
	.byte	25
	.word	10316
	.byte	25
	.word	10333
	.byte	20,6,0,0,24
	.word	10691
	.byte	25
	.word	10719
	.byte	6,0,24
	.word	14169
	.byte	25
	.word	14192
	.byte	6,0,24
	.word	14207
	.byte	25
	.word	14239
	.byte	20,20,30
	.word	9759
	.byte	31
	.word	9797
	.byte	0,0,6,0,0,24
	.word	14257
	.byte	25
	.word	14285
	.byte	6,0,24
	.word	14300
	.byte	20,30
	.word	9954
	.byte	32
	.word	9991
	.byte	30
	.word	9912
	.byte	31
	.word	9952
	.byte	0,31
	.word	9992
	.byte	0,0,6,0,0,24
	.word	14333
	.byte	25
	.word	14359
	.byte	20,30
	.word	10069
	.byte	25
	.word	10102
	.byte	31
	.word	10119
	.byte	0,6,0,0,24
	.word	14397
	.byte	25
	.word	14421
	.byte	20,30
	.word	14487
	.byte	32
	.word	14503
	.byte	30
	.word	14300
	.byte	32
	.word	14329
	.byte	30
	.word	9954
	.byte	32
	.word	9991
	.byte	30
	.word	9912
	.byte	31
	.word	9952
	.byte	0,31
	.word	9992
	.byte	0,0,31
	.word	14330
	.byte	0,0,31
	.word	14504
	.byte	30
	.word	14333
	.byte	25
	.word	14359
	.byte	32
	.word	14376
	.byte	30
	.word	10069
	.byte	25
	.word	10102
	.byte	31
	.word	10119
	.byte	0,31
	.word	14377
	.byte	0,0,31
	.word	14505
	.byte	30
	.word	14169
	.byte	25
	.word	14192
	.byte	31
	.word	14205
	.byte	0,31
	.word	14506
	.byte	0,0,6,0,0,24
	.word	14442
	.byte	25
	.word	14465
	.byte	20,30
	.word	14487
	.byte	32
	.word	14503
	.byte	30
	.word	14300
	.byte	32
	.word	14329
	.byte	30
	.word	9954
	.byte	32
	.word	9991
	.byte	30
	.word	9912
	.byte	31
	.word	9952
	.byte	0,31
	.word	9992
	.byte	0,0,31
	.word	14330
	.byte	0,0,31
	.word	14504
	.byte	30
	.word	14333
	.byte	25
	.word	14359
	.byte	32
	.word	14376
	.byte	30
	.word	10069
	.byte	25
	.word	10102
	.byte	31
	.word	10119
	.byte	0,31
	.word	14377
	.byte	0,0,31
	.word	14505
	.byte	30
	.word	14169
	.byte	25
	.word	14192
	.byte	31
	.word	14205
	.byte	0,31
	.word	14506
	.byte	0,0,6,0,0,24
	.word	14487
	.byte	20,30
	.word	14300
	.byte	32
	.word	14329
	.byte	30
	.word	9954
	.byte	32
	.word	9991
	.byte	30
	.word	9912
	.byte	31
	.word	9952
	.byte	0,31
	.word	9992
	.byte	0,0,31
	.word	14330
	.byte	0,0,6,30
	.word	14333
	.byte	25
	.word	14359
	.byte	32
	.word	14376
	.byte	30
	.word	10069
	.byte	25
	.word	10102
	.byte	31
	.word	10119
	.byte	0,31
	.word	14377
	.byte	0,0,6,30
	.word	14169
	.byte	25
	.word	14192
	.byte	31
	.word	14205
	.byte	0,6,0,0,24
	.word	14509
	.byte	20,30
	.word	14169
	.byte	25
	.word	14192
	.byte	31
	.word	14205
	.byte	0,6,0,0,33
	.byte	'__wchar_t',0,20,1,1
	.word	506
	.byte	33
	.byte	'__size_t',0,20,1,1
	.word	850
	.byte	33
	.byte	'__ptrdiff_t',0,20,1,1
	.word	866
	.byte	34,1,3
	.word	15954
	.byte	33
	.byte	'__codeptr',0,20,1,1
	.word	15956
	.byte	33
	.byte	'size_t',0,21,24,25
	.word	850
	.byte	33
	.byte	'boolean',0,22,101,29
	.word	612
	.byte	33
	.byte	'uint8',0,22,105,29
	.word	612
	.byte	33
	.byte	'uint16',0,22,109,29
	.word	1025
	.byte	33
	.byte	'uint32',0,22,113,29
	.word	10121
	.byte	33
	.byte	'uint64',0,22,118,29
	.word	388
	.byte	33
	.byte	'sint16',0,22,126,29
	.word	506
	.byte	33
	.byte	'sint32',0,22,131,1,29
	.word	519
	.byte	33
	.byte	'sint64',0,22,138,1,29
	.word	14380
	.byte	33
	.byte	'float32',0,22,167,1,29
	.word	302
	.byte	33
	.byte	'pvoid',0,17,57,28
	.word	420
	.byte	33
	.byte	'Ifx_TickTime',0,17,79,28
	.word	14380
	.byte	33
	.byte	'Ifx_SizeT',0,17,92,16
	.word	506
	.byte	33
	.byte	'Ifx_CircularBuffer',0,17,101,3
	.word	14887
	.byte	33
	.byte	'Ifx_Priority',0,17,103,16
	.word	1025
	.byte	18,17,130,1,9,1,19
	.byte	'Ifx_RxSel_a',0,0,19
	.byte	'Ifx_RxSel_b',0,1,19
	.byte	'Ifx_RxSel_c',0,2,19
	.byte	'Ifx_RxSel_d',0,3,19
	.byte	'Ifx_RxSel_e',0,4,19
	.byte	'Ifx_RxSel_f',0,5,19
	.byte	'Ifx_RxSel_g',0,6,19
	.byte	'Ifx_RxSel_h',0,7,0,33
	.byte	'Ifx_RxSel',0,17,140,1,3
	.word	16234
	.byte	33
	.byte	'Ifx_Fifo_Shared',0,3,66,3
	.word	531
	.byte	33
	.byte	'Ifx_Fifo',0,3,83,3
	.word	639
	.byte	13
	.byte	'_Ifx_CPU_A_Bits',0,16,45,16,4,14
	.byte	'ADDR',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_A_Bits',0,16,48,3
	.word	16413
	.byte	13
	.byte	'_Ifx_CPU_BIV_Bits',0,16,51,16,4,14
	.byte	'VSS',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'BIV',0,4
	.word	873
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BIV_Bits',0,16,55,3
	.word	16474
	.byte	13
	.byte	'_Ifx_CPU_BTV_Bits',0,16,58,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'BTV',0,4
	.word	873
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_BTV_Bits',0,16,62,3
	.word	16553
	.byte	13
	.byte	'_Ifx_CPU_CCNT_Bits',0,16,65,16,4,14
	.byte	'CountValue',0,4
	.word	873
	.byte	31,1,2,35,0,14
	.byte	'SOvf',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT_Bits',0,16,69,3
	.word	16639
	.byte	13
	.byte	'_Ifx_CPU_CCTRL_Bits',0,16,72,16,4,14
	.byte	'CM',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'CE',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'M1',0,4
	.word	873
	.byte	3,27,2,35,0,14
	.byte	'M2',0,4
	.word	873
	.byte	3,24,2,35,0,14
	.byte	'M3',0,4
	.word	873
	.byte	3,21,2,35,0,14
	.byte	'reserved_11',0,4
	.word	873
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL_Bits',0,16,80,3
	.word	16728
	.byte	13
	.byte	'_Ifx_CPU_COMPAT_Bits',0,16,83,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'RM',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'SP',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'reserved_5',0,4
	.word	873
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT_Bits',0,16,89,3
	.word	16874
	.byte	13
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,16,92,16,4,14
	.byte	'CORE_ID',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CORE_ID_Bits',0,16,96,3
	.word	17001
	.byte	13
	.byte	'_Ifx_CPU_CPR_L_Bits',0,16,99,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'LOWBND',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L_Bits',0,16,103,3
	.word	17099
	.byte	13
	.byte	'_Ifx_CPU_CPR_U_Bits',0,16,106,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'UPPBND',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U_Bits',0,16,110,3
	.word	17192
	.byte	13
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,16,113,16,4,14
	.byte	'MODREV',0,4
	.word	873
	.byte	8,24,2,35,0,14
	.byte	'MOD_32B',0,4
	.word	873
	.byte	8,16,2,35,0,14
	.byte	'MOD',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID_Bits',0,16,118,3
	.word	17285
	.byte	13
	.byte	'_Ifx_CPU_CPXE_Bits',0,16,121,16,4,14
	.byte	'XE',0,4
	.word	873
	.byte	8,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE_Bits',0,16,125,3
	.word	17392
	.byte	13
	.byte	'_Ifx_CPU_CREVT_Bits',0,16,128,1,16,4,14
	.byte	'EVTA',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'BBM',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'BOD',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'SUSP',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'CNT',0,4
	.word	873
	.byte	2,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT_Bits',0,16,136,1,3
	.word	17479
	.byte	13
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,16,139,1,16,4,14
	.byte	'CID',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID_Bits',0,16,143,1,3
	.word	17633
	.byte	13
	.byte	'_Ifx_CPU_D_Bits',0,16,146,1,16,4,14
	.byte	'DATA',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_D_Bits',0,16,149,1,3
	.word	17727
	.byte	13
	.byte	'_Ifx_CPU_DATR_Bits',0,16,152,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'SBE',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'reserved_4',0,4
	.word	873
	.byte	5,23,2,35,0,14
	.byte	'CWE',0,4
	.word	873
	.byte	1,22,2,35,0,14
	.byte	'CFE',0,4
	.word	873
	.byte	1,21,2,35,0,14
	.byte	'reserved_11',0,4
	.word	873
	.byte	3,18,2,35,0,14
	.byte	'SOE',0,4
	.word	873
	.byte	1,17,2,35,0,14
	.byte	'SME',0,4
	.word	873
	.byte	1,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DATR_Bits',0,16,163,1,3
	.word	17790
	.byte	13
	.byte	'_Ifx_CPU_DBGSR_Bits',0,16,166,1,16,4,14
	.byte	'DE',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'HALT',0,4
	.word	873
	.byte	2,29,2,35,0,14
	.byte	'SIH',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'SUSP',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'reserved_5',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'PREVSUSP',0,4
	.word	873
	.byte	1,25,2,35,0,14
	.byte	'PEVT',0,4
	.word	873
	.byte	1,24,2,35,0,14
	.byte	'EVTSRC',0,4
	.word	873
	.byte	5,19,2,35,0,14
	.byte	'reserved_13',0,4
	.word	873
	.byte	19,0,2,35,0,0,33
	.byte	'Ifx_CPU_DBGSR_Bits',0,16,177,1,3
	.word	18008
	.byte	13
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,16,180,1,16,4,14
	.byte	'DTA',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'reserved_1',0,4
	.word	873
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR_Bits',0,16,184,1,3
	.word	18223
	.byte	13
	.byte	'_Ifx_CPU_DCON0_Bits',0,16,187,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'DCBYP',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'reserved_2',0,4
	.word	873
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0_Bits',0,16,192,1,3
	.word	18317
	.byte	13
	.byte	'_Ifx_CPU_DCON2_Bits',0,16,195,1,16,4,14
	.byte	'DCACHE_SZE',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'DSCRATCH_SZE',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2_Bits',0,16,199,1,3
	.word	18433
	.byte	13
	.byte	'_Ifx_CPU_DCX_Bits',0,16,202,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	6,26,2,35,0,14
	.byte	'DCXValue',0,4
	.word	873
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_CPU_DCX_Bits',0,16,206,1,3
	.word	18534
	.byte	13
	.byte	'_Ifx_CPU_DEADD_Bits',0,16,209,1,16,4,14
	.byte	'ERROR_ADDRESS',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD_Bits',0,16,212,1,3
	.word	18627
	.byte	13
	.byte	'_Ifx_CPU_DIEAR_Bits',0,16,215,1,16,4,14
	.byte	'TA',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR_Bits',0,16,218,1,3
	.word	18707
	.byte	13
	.byte	'_Ifx_CPU_DIETR_Bits',0,16,221,1,16,4,14
	.byte	'IED',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'IE_T',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'IE_C',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'IE_S',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'IE_BI',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'E_INFO',0,4
	.word	873
	.byte	6,21,2,35,0,14
	.byte	'IE_DUAL',0,4
	.word	873
	.byte	1,20,2,35,0,14
	.byte	'IE_SP',0,4
	.word	873
	.byte	1,19,2,35,0,14
	.byte	'IE_BS',0,4
	.word	873
	.byte	1,18,2,35,0,14
	.byte	'reserved_14',0,4
	.word	873
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR_Bits',0,16,233,1,3
	.word	18776
	.byte	13
	.byte	'_Ifx_CPU_DMS_Bits',0,16,236,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'DMSValue',0,4
	.word	873
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_DMS_Bits',0,16,240,1,3
	.word	19005
	.byte	13
	.byte	'_Ifx_CPU_DPR_L_Bits',0,16,243,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'LOWBND',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L_Bits',0,16,247,1,3
	.word	19098
	.byte	13
	.byte	'_Ifx_CPU_DPR_U_Bits',0,16,250,1,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'UPPBND',0,4
	.word	873
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U_Bits',0,16,254,1,3
	.word	19193
	.byte	13
	.byte	'_Ifx_CPU_DPRE_Bits',0,16,129,2,16,4,14
	.byte	'RE',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE_Bits',0,16,133,2,3
	.word	19288
	.byte	13
	.byte	'_Ifx_CPU_DPWE_Bits',0,16,136,2,16,4,14
	.byte	'WE',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE_Bits',0,16,140,2,3
	.word	19378
	.byte	13
	.byte	'_Ifx_CPU_DSTR_Bits',0,16,143,2,16,4,14
	.byte	'SRE',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'GAE',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'LBE',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	3,26,2,35,0,14
	.byte	'CRE',0,4
	.word	873
	.byte	1,25,2,35,0,14
	.byte	'reserved_7',0,4
	.word	873
	.byte	7,18,2,35,0,14
	.byte	'DTME',0,4
	.word	873
	.byte	1,17,2,35,0,14
	.byte	'LOE',0,4
	.word	873
	.byte	1,16,2,35,0,14
	.byte	'SDE',0,4
	.word	873
	.byte	1,15,2,35,0,14
	.byte	'SCE',0,4
	.word	873
	.byte	1,14,2,35,0,14
	.byte	'CAC',0,4
	.word	873
	.byte	1,13,2,35,0,14
	.byte	'MPE',0,4
	.word	873
	.byte	1,12,2,35,0,14
	.byte	'CLE',0,4
	.word	873
	.byte	1,11,2,35,0,14
	.byte	'reserved_21',0,4
	.word	873
	.byte	3,8,2,35,0,14
	.byte	'ALN',0,4
	.word	873
	.byte	1,7,2,35,0,14
	.byte	'reserved_25',0,4
	.word	873
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR_Bits',0,16,161,2,3
	.word	19468
	.byte	13
	.byte	'_Ifx_CPU_EXEVT_Bits',0,16,164,2,16,4,14
	.byte	'EVTA',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'BBM',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'BOD',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'SUSP',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'CNT',0,4
	.word	873
	.byte	2,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT_Bits',0,16,172,2,3
	.word	19792
	.byte	13
	.byte	'_Ifx_CPU_FCX_Bits',0,16,175,2,16,4,14
	.byte	'FCXO',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'FCXS',0,4
	.word	873
	.byte	4,12,2,35,0,14
	.byte	'reserved_20',0,4
	.word	873
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FCX_Bits',0,16,180,2,3
	.word	19946
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,16,183,2,16,4,14
	.byte	'TST',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'TCL',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'reserved_2',0,4
	.word	873
	.byte	6,24,2,35,0,14
	.byte	'RM',0,4
	.word	873
	.byte	2,22,2,35,0,14
	.byte	'reserved_10',0,4
	.word	873
	.byte	8,14,2,35,0,14
	.byte	'FXE',0,4
	.word	873
	.byte	1,13,2,35,0,14
	.byte	'FUE',0,4
	.word	873
	.byte	1,12,2,35,0,14
	.byte	'FZE',0,4
	.word	873
	.byte	1,11,2,35,0,14
	.byte	'FVE',0,4
	.word	873
	.byte	1,10,2,35,0,14
	.byte	'FIE',0,4
	.word	873
	.byte	1,9,2,35,0,14
	.byte	'reserved_23',0,4
	.word	873
	.byte	3,6,2,35,0,14
	.byte	'FX',0,4
	.word	873
	.byte	1,5,2,35,0,14
	.byte	'FU',0,4
	.word	873
	.byte	1,4,2,35,0,14
	.byte	'FZ',0,4
	.word	873
	.byte	1,3,2,35,0,14
	.byte	'FV',0,4
	.word	873
	.byte	1,2,2,35,0,14
	.byte	'FI',0,4
	.word	873
	.byte	1,1,2,35,0,14
	.byte	'reserved_31',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,16,202,2,3
	.word	20052
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,205,2,16,4,14
	.byte	'OPC',0,4
	.word	873
	.byte	8,24,2,35,0,14
	.byte	'FMT',0,4
	.word	873
	.byte	1,23,2,35,0,14
	.byte	'reserved_9',0,4
	.word	873
	.byte	7,16,2,35,0,14
	.byte	'DREG',0,4
	.word	873
	.byte	4,12,2,35,0,14
	.byte	'reserved_20',0,4
	.word	873
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,212,2,3
	.word	20401
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,16,215,2,16,4,14
	.byte	'PC',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,16,218,2,3
	.word	20561
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,221,2,16,4,14
	.byte	'SRC1',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,224,2,3
	.word	20642
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,227,2,16,4,14
	.byte	'SRC2',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,230,2,3
	.word	20729
	.byte	13
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,233,2,16,4,14
	.byte	'SRC3',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,236,2,3
	.word	20816
	.byte	13
	.byte	'_Ifx_CPU_ICNT_Bits',0,16,239,2,16,4,14
	.byte	'CountValue',0,4
	.word	873
	.byte	31,1,2,35,0,14
	.byte	'SOvf',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT_Bits',0,16,243,2,3
	.word	20903
	.byte	33
	.byte	'Ifx_CPU_ICR_Bits',0,16,253,2,3
	.word	14730
	.byte	13
	.byte	'_Ifx_CPU_ISP_Bits',0,16,128,3,16,4,14
	.byte	'ISP',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_ISP_Bits',0,16,131,3,3
	.word	21020
	.byte	13
	.byte	'_Ifx_CPU_LCX_Bits',0,16,134,3,16,4,14
	.byte	'LCXO',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'LCXS',0,4
	.word	873
	.byte	4,12,2,35,0,14
	.byte	'reserved_20',0,4
	.word	873
	.byte	12,0,2,35,0,0,33
	.byte	'Ifx_CPU_LCX_Bits',0,16,139,3,3
	.word	21086
	.byte	13
	.byte	'_Ifx_CPU_M1CNT_Bits',0,16,142,3,16,4,14
	.byte	'CountValue',0,4
	.word	873
	.byte	31,1,2,35,0,14
	.byte	'SOvf',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT_Bits',0,16,146,3,3
	.word	21192
	.byte	13
	.byte	'_Ifx_CPU_M2CNT_Bits',0,16,149,3,16,4,14
	.byte	'CountValue',0,4
	.word	873
	.byte	31,1,2,35,0,14
	.byte	'SOvf',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT_Bits',0,16,153,3,3
	.word	21285
	.byte	13
	.byte	'_Ifx_CPU_M3CNT_Bits',0,16,156,3,16,4,14
	.byte	'CountValue',0,4
	.word	873
	.byte	31,1,2,35,0,14
	.byte	'SOvf',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT_Bits',0,16,160,3,3
	.word	21378
	.byte	13
	.byte	'_Ifx_CPU_PC_Bits',0,16,163,3,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'PC',0,4
	.word	873
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_CPU_PC_Bits',0,16,167,3,3
	.word	21471
	.byte	13
	.byte	'_Ifx_CPU_PCON0_Bits',0,16,170,3,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'PCBYP',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'reserved_2',0,4
	.word	873
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0_Bits',0,16,175,3,3
	.word	21556
	.byte	13
	.byte	'_Ifx_CPU_PCON1_Bits',0,16,178,3,16,4,14
	.byte	'PCINV',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'PBINV',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'reserved_2',0,4
	.word	873
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1_Bits',0,16,183,3,3
	.word	21672
	.byte	13
	.byte	'_Ifx_CPU_PCON2_Bits',0,16,186,3,16,4,14
	.byte	'PCACHE_SZE',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'PSCRATCH_SZE',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2_Bits',0,16,190,3,3
	.word	21783
	.byte	13
	.byte	'_Ifx_CPU_PCXI_Bits',0,16,193,3,16,4,14
	.byte	'PCXO',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'PCXS',0,4
	.word	873
	.byte	4,12,2,35,0,14
	.byte	'UL',0,4
	.word	873
	.byte	1,11,2,35,0,14
	.byte	'PIE',0,4
	.word	873
	.byte	1,10,2,35,0,14
	.byte	'PCPN',0,4
	.word	873
	.byte	10,0,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI_Bits',0,16,200,3,3
	.word	21884
	.byte	13
	.byte	'_Ifx_CPU_PIEAR_Bits',0,16,203,3,16,4,14
	.byte	'TA',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR_Bits',0,16,206,3,3
	.word	22014
	.byte	13
	.byte	'_Ifx_CPU_PIETR_Bits',0,16,209,3,16,4,14
	.byte	'IED',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'IE_T',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'IE_C',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'IE_S',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'IE_BI',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'E_INFO',0,4
	.word	873
	.byte	6,21,2,35,0,14
	.byte	'IE_DUAL',0,4
	.word	873
	.byte	1,20,2,35,0,14
	.byte	'IE_SP',0,4
	.word	873
	.byte	1,19,2,35,0,14
	.byte	'IE_BS',0,4
	.word	873
	.byte	1,18,2,35,0,14
	.byte	'reserved_14',0,4
	.word	873
	.byte	18,0,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR_Bits',0,16,221,3,3
	.word	22083
	.byte	13
	.byte	'_Ifx_CPU_PMA0_Bits',0,16,224,3,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	13,19,2,35,0,14
	.byte	'DAC',0,4
	.word	873
	.byte	3,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0_Bits',0,16,229,3,3
	.word	22312
	.byte	13
	.byte	'_Ifx_CPU_PMA1_Bits',0,16,232,3,16,4,14
	.byte	'reserved_0',0,4
	.word	873
	.byte	14,18,2,35,0,14
	.byte	'CAC',0,4
	.word	873
	.byte	2,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1_Bits',0,16,237,3,3
	.word	22425
	.byte	13
	.byte	'_Ifx_CPU_PMA2_Bits',0,16,240,3,16,4,14
	.byte	'PSI',0,4
	.word	873
	.byte	16,16,2,35,0,14
	.byte	'reserved_16',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2_Bits',0,16,244,3,3
	.word	22538
	.byte	13
	.byte	'_Ifx_CPU_PSTR_Bits',0,16,247,3,16,4,14
	.byte	'FRE',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'reserved_1',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'FBE',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	9,20,2,35,0,14
	.byte	'FPE',0,4
	.word	873
	.byte	1,19,2,35,0,14
	.byte	'reserved_13',0,4
	.word	873
	.byte	1,18,2,35,0,14
	.byte	'FME',0,4
	.word	873
	.byte	1,17,2,35,0,14
	.byte	'reserved_15',0,4
	.word	873
	.byte	17,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR_Bits',0,16,129,4,3
	.word	22629
	.byte	13
	.byte	'_Ifx_CPU_PSW_Bits',0,16,132,4,16,4,14
	.byte	'CDC',0,4
	.word	873
	.byte	7,25,2,35,0,14
	.byte	'CDE',0,4
	.word	873
	.byte	1,24,2,35,0,14
	.byte	'GW',0,4
	.word	873
	.byte	1,23,2,35,0,14
	.byte	'IS',0,4
	.word	873
	.byte	1,22,2,35,0,14
	.byte	'IO',0,4
	.word	873
	.byte	2,20,2,35,0,14
	.byte	'PRS',0,4
	.word	873
	.byte	2,18,2,35,0,14
	.byte	'S',0,4
	.word	873
	.byte	1,17,2,35,0,14
	.byte	'reserved_15',0,4
	.word	873
	.byte	12,5,2,35,0,14
	.byte	'SAV',0,4
	.word	873
	.byte	1,4,2,35,0,14
	.byte	'AV',0,4
	.word	873
	.byte	1,3,2,35,0,14
	.byte	'SV',0,4
	.word	873
	.byte	1,2,2,35,0,14
	.byte	'V',0,4
	.word	873
	.byte	1,1,2,35,0,14
	.byte	'C',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_PSW_Bits',0,16,147,4,3
	.word	22832
	.byte	13
	.byte	'_Ifx_CPU_SEGEN_Bits',0,16,150,4,16,4,14
	.byte	'ADFLIP',0,4
	.word	873
	.byte	8,24,2,35,0,14
	.byte	'ADTYPE',0,4
	.word	873
	.byte	2,22,2,35,0,14
	.byte	'reserved_10',0,4
	.word	873
	.byte	21,1,2,35,0,14
	.byte	'AE',0,4
	.word	873
	.byte	1,0,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN_Bits',0,16,156,4,3
	.word	23075
	.byte	13
	.byte	'_Ifx_CPU_SMACON_Bits',0,16,159,4,16,4,14
	.byte	'PC',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'reserved_1',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'PT',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	5,24,2,35,0,14
	.byte	'DC',0,4
	.word	873
	.byte	1,23,2,35,0,14
	.byte	'reserved_9',0,4
	.word	873
	.byte	1,22,2,35,0,14
	.byte	'DT',0,4
	.word	873
	.byte	1,21,2,35,0,14
	.byte	'reserved_11',0,4
	.word	873
	.byte	13,8,2,35,0,14
	.byte	'IODT',0,4
	.word	873
	.byte	1,7,2,35,0,14
	.byte	'reserved_25',0,4
	.word	873
	.byte	7,0,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON_Bits',0,16,171,4,3
	.word	23203
	.byte	13
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,16,174,4,16,4,14
	.byte	'EN',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,16,177,4,3
	.word	23444
	.byte	13
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,16,180,4,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,16,183,4,3
	.word	23527
	.byte	13
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,186,4,16,4,14
	.byte	'EN',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,189,4,3
	.word	23618
	.byte	13
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,192,4,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,195,4,3
	.word	23709
	.byte	13
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,16,198,4,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'ADDR',0,4
	.word	850
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,16,202,4,3
	.word	23808
	.byte	13
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,16,205,4,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'ADDR',0,4
	.word	850
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,16,209,4,3
	.word	23915
	.byte	13
	.byte	'_Ifx_CPU_SWEVT_Bits',0,16,212,4,16,4,14
	.byte	'EVTA',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'BBM',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'BOD',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'SUSP',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'CNT',0,4
	.word	873
	.byte	2,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT_Bits',0,16,220,4,3
	.word	24022
	.byte	13
	.byte	'_Ifx_CPU_SYSCON_Bits',0,16,223,4,16,4,14
	.byte	'FCDSF',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'PROTEN',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'TPROTEN',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'IS',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'IT',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'reserved_5',0,4
	.word	873
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON_Bits',0,16,231,4,3
	.word	24176
	.byte	13
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,16,234,4,16,4,14
	.byte	'ASI',0,4
	.word	873
	.byte	5,27,2,35,0,14
	.byte	'reserved_5',0,4
	.word	873
	.byte	27,0,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,16,238,4,3
	.word	24337
	.byte	13
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,16,241,4,16,4,14
	.byte	'TEXP0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'TEXP1',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'TEXP2',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'reserved_3',0,4
	.word	873
	.byte	13,16,2,35,0,14
	.byte	'TTRAP',0,4
	.word	873
	.byte	1,15,2,35,0,14
	.byte	'reserved_17',0,4
	.word	873
	.byte	15,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON_Bits',0,16,249,4,3
	.word	24435
	.byte	13
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,16,252,4,16,4,14
	.byte	'Timer',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,16,255,4,3
	.word	24607
	.byte	13
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,16,130,5,16,4,14
	.byte	'ADDR',0,4
	.word	873
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR_Bits',0,16,133,5,3
	.word	24687
	.byte	13
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,16,136,5,16,4,14
	.byte	'EVTA',0,4
	.word	873
	.byte	3,29,2,35,0,14
	.byte	'BBM',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'BOD',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'SUSP',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'CNT',0,4
	.word	873
	.byte	2,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	4,20,2,35,0,14
	.byte	'TYP',0,4
	.word	873
	.byte	1,19,2,35,0,14
	.byte	'RNG',0,4
	.word	873
	.byte	1,18,2,35,0,14
	.byte	'reserved_14',0,4
	.word	873
	.byte	1,17,2,35,0,14
	.byte	'ASI_EN',0,4
	.word	873
	.byte	1,16,2,35,0,14
	.byte	'ASI',0,4
	.word	873
	.byte	5,11,2,35,0,14
	.byte	'reserved_21',0,4
	.word	873
	.byte	6,5,2,35,0,14
	.byte	'AST',0,4
	.word	873
	.byte	1,4,2,35,0,14
	.byte	'ALD',0,4
	.word	873
	.byte	1,3,2,35,0,14
	.byte	'reserved_29',0,4
	.word	873
	.byte	3,0,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT_Bits',0,16,153,5,3
	.word	24760
	.byte	13
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,16,156,5,16,4,14
	.byte	'T0',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'T1',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'T2',0,4
	.word	873
	.byte	1,29,2,35,0,14
	.byte	'T3',0,4
	.word	873
	.byte	1,28,2,35,0,14
	.byte	'T4',0,4
	.word	873
	.byte	1,27,2,35,0,14
	.byte	'T5',0,4
	.word	873
	.byte	1,26,2,35,0,14
	.byte	'T6',0,4
	.word	873
	.byte	1,25,2,35,0,14
	.byte	'T7',0,4
	.word	873
	.byte	1,24,2,35,0,14
	.byte	'reserved_8',0,4
	.word	873
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,16,167,5,3
	.word	25078
	.byte	15,16,175,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16413
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_A',0,16,180,5,3
	.word	25273
	.byte	15,16,183,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16474
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BIV',0,16,188,5,3
	.word	25332
	.byte	15,16,191,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16553
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_BTV',0,16,196,5,3
	.word	25393
	.byte	15,16,199,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16639
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCNT',0,16,204,5,3
	.word	25454
	.byte	15,16,207,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16728
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CCTRL',0,16,212,5,3
	.word	25516
	.byte	15,16,215,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	16874
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_COMPAT',0,16,220,5,3
	.word	25579
	.byte	15,16,223,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17001
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CORE_ID',0,16,228,5,3
	.word	25643
	.byte	15,16,231,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17099
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_L',0,16,236,5,3
	.word	25708
	.byte	15,16,239,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17192
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPR_U',0,16,244,5,3
	.word	25771
	.byte	15,16,247,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17285
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPU_ID',0,16,252,5,3
	.word	25834
	.byte	15,16,255,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17392
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CPXE',0,16,132,6,3
	.word	25898
	.byte	15,16,135,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17479
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CREVT',0,16,140,6,3
	.word	25960
	.byte	15,16,143,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17633
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_CUS_ID',0,16,148,6,3
	.word	26023
	.byte	15,16,151,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17727
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_D',0,16,156,6,3
	.word	26087
	.byte	15,16,159,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	17790
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DATR',0,16,164,6,3
	.word	26146
	.byte	15,16,167,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18008
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DBGSR',0,16,172,6,3
	.word	26208
	.byte	15,16,175,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18223
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DBGTCR',0,16,180,6,3
	.word	26271
	.byte	15,16,183,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18317
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON0',0,16,188,6,3
	.word	26335
	.byte	15,16,191,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18433
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCON2',0,16,196,6,3
	.word	26398
	.byte	15,16,199,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18534
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DCX',0,16,204,6,3
	.word	26461
	.byte	15,16,207,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18627
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DEADD',0,16,212,6,3
	.word	26522
	.byte	15,16,215,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18707
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIEAR',0,16,220,6,3
	.word	26585
	.byte	15,16,223,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	18776
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DIETR',0,16,228,6,3
	.word	26648
	.byte	15,16,231,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19005
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DMS',0,16,236,6,3
	.word	26711
	.byte	15,16,239,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19098
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_L',0,16,244,6,3
	.word	26772
	.byte	15,16,247,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19193
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPR_U',0,16,252,6,3
	.word	26835
	.byte	15,16,255,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19288
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPRE',0,16,132,7,3
	.word	26898
	.byte	15,16,135,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19378
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DPWE',0,16,140,7,3
	.word	26960
	.byte	15,16,143,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19468
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_DSTR',0,16,148,7,3
	.word	27022
	.byte	15,16,151,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19792
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_EXEVT',0,16,156,7,3
	.word	27084
	.byte	15,16,159,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	19946
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FCX',0,16,164,7,3
	.word	27147
	.byte	15,16,167,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20052
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,16,172,7,3
	.word	27208
	.byte	15,16,175,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20401
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,16,180,7,3
	.word	27278
	.byte	15,16,183,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20561
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,16,188,7,3
	.word	27348
	.byte	15,16,191,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20642
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,16,196,7,3
	.word	27417
	.byte	15,16,199,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20729
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,16,204,7,3
	.word	27488
	.byte	15,16,207,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20816
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,16,212,7,3
	.word	27559
	.byte	15,16,215,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	20903
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ICNT',0,16,220,7,3
	.word	27630
	.byte	33
	.byte	'Ifx_CPU_ICR',0,16,228,7,3
	.word	14847
	.byte	15,16,231,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21020
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_ISP',0,16,236,7,3
	.word	27713
	.byte	15,16,239,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21086
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_LCX',0,16,244,7,3
	.word	27774
	.byte	15,16,247,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21192
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M1CNT',0,16,252,7,3
	.word	27835
	.byte	15,16,255,7,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21285
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M2CNT',0,16,132,8,3
	.word	27898
	.byte	15,16,135,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21378
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_M3CNT',0,16,140,8,3
	.word	27961
	.byte	15,16,143,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21471
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PC',0,16,148,8,3
	.word	28024
	.byte	15,16,151,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21556
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON0',0,16,156,8,3
	.word	28084
	.byte	15,16,159,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21672
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON1',0,16,164,8,3
	.word	28147
	.byte	15,16,167,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21783
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCON2',0,16,172,8,3
	.word	28210
	.byte	15,16,175,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	21884
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PCXI',0,16,180,8,3
	.word	28273
	.byte	15,16,183,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22014
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIEAR',0,16,188,8,3
	.word	28335
	.byte	15,16,191,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22083
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PIETR',0,16,196,8,3
	.word	28398
	.byte	15,16,199,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22312
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA0',0,16,204,8,3
	.word	28461
	.byte	15,16,207,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22425
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA1',0,16,212,8,3
	.word	28523
	.byte	15,16,215,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22538
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PMA2',0,16,220,8,3
	.word	28585
	.byte	15,16,223,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22629
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSTR',0,16,228,8,3
	.word	28647
	.byte	15,16,231,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	22832
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_PSW',0,16,236,8,3
	.word	28709
	.byte	15,16,239,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23075
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SEGEN',0,16,244,8,3
	.word	28770
	.byte	15,16,247,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23203
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SMACON',0,16,252,8,3
	.word	28833
	.byte	15,16,255,8,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23444
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENA',0,16,132,9,3
	.word	28897
	.byte	15,16,135,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23527
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_ACCENB',0,16,140,9,3
	.word	28967
	.byte	15,16,143,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23618
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,16,148,9,3
	.word	29037
	.byte	15,16,151,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23709
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,16,156,9,3
	.word	29111
	.byte	15,16,159,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23808
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,16,164,9,3
	.word	29185
	.byte	15,16,167,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	23915
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,16,172,9,3
	.word	29255
	.byte	15,16,175,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24022
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SWEVT',0,16,180,9,3
	.word	29325
	.byte	15,16,183,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24176
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_SYSCON',0,16,188,9,3
	.word	29388
	.byte	15,16,191,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24337
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TASK_ASI',0,16,196,9,3
	.word	29452
	.byte	15,16,199,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24435
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_CON',0,16,204,9,3
	.word	29518
	.byte	15,16,207,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24607
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TPS_TIMER',0,16,212,9,3
	.word	29583
	.byte	15,16,215,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24687
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_ADR',0,16,220,9,3
	.word	29650
	.byte	15,16,223,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	24760
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TR_EVT',0,16,228,9,3
	.word	29714
	.byte	15,16,231,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	25078
	.byte	4,2,35,0,0,33
	.byte	'Ifx_CPU_TRIG_ACC',0,16,236,9,3
	.word	29778
	.byte	13
	.byte	'_Ifx_CPU_CPR',0,16,247,9,25,8,11
	.byte	'L',0
	.word	25708
	.byte	4,2,35,0,11
	.byte	'U',0
	.word	25771
	.byte	4,2,35,4,0,12
	.word	29844
	.byte	33
	.byte	'Ifx_CPU_CPR',0,16,251,9,3
	.word	29886
	.byte	13
	.byte	'_Ifx_CPU_DPR',0,16,254,9,25,8,11
	.byte	'L',0
	.word	26772
	.byte	4,2,35,0,11
	.byte	'U',0
	.word	26835
	.byte	4,2,35,4,0,12
	.word	29912
	.byte	33
	.byte	'Ifx_CPU_DPR',0,16,130,10,3
	.word	29954
	.byte	13
	.byte	'_Ifx_CPU_SPROT_RGN',0,16,133,10,25,16,11
	.byte	'LA',0
	.word	29185
	.byte	4,2,35,0,11
	.byte	'UA',0
	.word	29255
	.byte	4,2,35,4,11
	.byte	'ACCENA',0
	.word	29037
	.byte	4,2,35,8,11
	.byte	'ACCENB',0
	.word	29111
	.byte	4,2,35,12,0,12
	.word	29980
	.byte	33
	.byte	'Ifx_CPU_SPROT_RGN',0,16,139,10,3
	.word	30062
	.byte	16,12
	.word	29583
	.byte	17,2,0,13
	.byte	'_Ifx_CPU_TPS',0,16,142,10,25,16,11
	.byte	'CON',0
	.word	29518
	.byte	4,2,35,0,11
	.byte	'TIMER',0
	.word	30094
	.byte	12,2,35,4,0,12
	.word	30103
	.byte	33
	.byte	'Ifx_CPU_TPS',0,16,146,10,3
	.word	30151
	.byte	13
	.byte	'_Ifx_CPU_TR',0,16,149,10,25,8,11
	.byte	'EVT',0
	.word	29714
	.byte	4,2,35,0,11
	.byte	'ADR',0
	.word	29650
	.byte	4,2,35,4,0,12
	.word	30177
	.byte	33
	.byte	'Ifx_CPU_TR',0,16,153,10,3
	.word	30222
	.byte	16,176,32
	.word	612
	.byte	17,175,32,0,16,208,223,1
	.word	612
	.byte	17,207,223,1,0,16,248,1
	.word	612
	.byte	17,247,1,0,16,244,29
	.word	612
	.byte	17,243,29,0,16,188,3
	.word	612
	.byte	17,187,3,0,16,232,3
	.word	612
	.byte	17,231,3,0,16,252,23
	.word	612
	.byte	17,251,23,0,16,228,63
	.word	612
	.byte	17,227,63,0,16,128,1
	.word	29912
	.byte	17,15,0,12
	.word	30337
	.byte	16,128,31
	.word	612
	.byte	17,255,30,0,16,64
	.word	29844
	.byte	17,7,0,12
	.word	30363
	.byte	16,192,31
	.word	612
	.byte	17,191,31,0,16,16
	.word	25898
	.byte	17,3,0,16,16
	.word	26898
	.byte	17,3,0,16,16
	.word	26960
	.byte	17,3,0,16,208,7
	.word	612
	.byte	17,207,7,0,12
	.word	30103
	.byte	16,240,23
	.word	612
	.byte	17,239,23,0,16,64
	.word	30177
	.byte	17,7,0,12
	.word	30442
	.byte	16,192,23
	.word	612
	.byte	17,191,23,0,16,232,1
	.word	612
	.byte	17,231,1,0,16,28
	.word	612
	.byte	17,27,0,16,180,1
	.word	612
	.byte	17,179,1,0,16,16
	.word	612
	.byte	17,15,0,16,172,1
	.word	612
	.byte	17,171,1,0,16,64
	.word	26087
	.byte	17,15,0,16,64
	.word	612
	.byte	17,63,0,16,64
	.word	25273
	.byte	17,15,0,13
	.byte	'_Ifx_CPU',0,16,166,10,25,128,128,4,11
	.byte	'reserved_0',0
	.word	30247
	.byte	176,32,2,35,0,11
	.byte	'SEGEN',0
	.word	28770
	.byte	4,3,35,176,32,11
	.byte	'reserved_1034',0
	.word	30258
	.byte	208,223,1,3,35,180,32,11
	.byte	'TASK_ASI',0
	.word	29452
	.byte	4,4,35,132,128,2,11
	.byte	'reserved_8008',0
	.word	30271
	.byte	248,1,4,35,136,128,2,11
	.byte	'PMA0',0
	.word	28461
	.byte	4,4,35,128,130,2,11
	.byte	'PMA1',0
	.word	28523
	.byte	4,4,35,132,130,2,11
	.byte	'PMA2',0
	.word	28585
	.byte	4,4,35,136,130,2,11
	.byte	'reserved_810C',0
	.word	30282
	.byte	244,29,4,35,140,130,2,11
	.byte	'DCON2',0
	.word	26398
	.byte	4,4,35,128,160,2,11
	.byte	'reserved_9004',0
	.word	4645
	.byte	8,4,35,132,160,2,11
	.byte	'SMACON',0
	.word	28833
	.byte	4,4,35,140,160,2,11
	.byte	'DSTR',0
	.word	27022
	.byte	4,4,35,144,160,2,11
	.byte	'reserved_9014',0
	.word	2826
	.byte	4,4,35,148,160,2,11
	.byte	'DATR',0
	.word	26146
	.byte	4,4,35,152,160,2,11
	.byte	'DEADD',0
	.word	26522
	.byte	4,4,35,156,160,2,11
	.byte	'DIEAR',0
	.word	26585
	.byte	4,4,35,160,160,2,11
	.byte	'DIETR',0
	.word	26648
	.byte	4,4,35,164,160,2,11
	.byte	'reserved_9028',0
	.word	4016
	.byte	24,4,35,168,160,2,11
	.byte	'DCON0',0
	.word	26335
	.byte	4,4,35,192,160,2,11
	.byte	'reserved_9044',0
	.word	30293
	.byte	188,3,4,35,196,160,2,11
	.byte	'PSTR',0
	.word	28647
	.byte	4,4,35,128,164,2,11
	.byte	'PCON1',0
	.word	28147
	.byte	4,4,35,132,164,2,11
	.byte	'PCON2',0
	.word	28210
	.byte	4,4,35,136,164,2,11
	.byte	'PCON0',0
	.word	28084
	.byte	4,4,35,140,164,2,11
	.byte	'PIEAR',0
	.word	28335
	.byte	4,4,35,144,164,2,11
	.byte	'PIETR',0
	.word	28398
	.byte	4,4,35,148,164,2,11
	.byte	'reserved_9218',0
	.word	30304
	.byte	232,3,4,35,152,164,2,11
	.byte	'COMPAT',0
	.word	25579
	.byte	4,4,35,128,168,2,11
	.byte	'reserved_9404',0
	.word	30315
	.byte	252,23,4,35,132,168,2,11
	.byte	'FPU_TRAP_CON',0
	.word	27208
	.byte	4,4,35,128,192,2,11
	.byte	'FPU_TRAP_PC',0
	.word	27348
	.byte	4,4,35,132,192,2,11
	.byte	'FPU_TRAP_OPC',0
	.word	27278
	.byte	4,4,35,136,192,2,11
	.byte	'reserved_A00C',0
	.word	2826
	.byte	4,4,35,140,192,2,11
	.byte	'FPU_TRAP_SRC1',0
	.word	27417
	.byte	4,4,35,144,192,2,11
	.byte	'FPU_TRAP_SRC2',0
	.word	27488
	.byte	4,4,35,148,192,2,11
	.byte	'FPU_TRAP_SRC3',0
	.word	27559
	.byte	4,4,35,152,192,2,11
	.byte	'reserved_A01C',0
	.word	30326
	.byte	228,63,4,35,156,192,2,11
	.byte	'DPR',0
	.word	30347
	.byte	128,1,4,35,128,128,3,11
	.byte	'reserved_C080',0
	.word	30352
	.byte	128,31,4,35,128,129,3,11
	.byte	'CPR',0
	.word	30372
	.byte	64,4,35,128,160,3,11
	.byte	'reserved_D040',0
	.word	30377
	.byte	192,31,4,35,192,160,3,11
	.byte	'CPXE',0
	.word	30388
	.byte	16,4,35,128,192,3,11
	.byte	'DPRE',0
	.word	30397
	.byte	16,4,35,144,192,3,11
	.byte	'DPWE',0
	.word	30406
	.byte	16,4,35,160,192,3,11
	.byte	'reserved_E030',0
	.word	30415
	.byte	208,7,4,35,176,192,3,11
	.byte	'TPS',0
	.word	30426
	.byte	16,4,35,128,200,3,11
	.byte	'reserved_E410',0
	.word	30431
	.byte	240,23,4,35,144,200,3,11
	.byte	'TR',0
	.word	30451
	.byte	64,4,35,128,224,3,11
	.byte	'reserved_F040',0
	.word	30456
	.byte	192,23,4,35,192,224,3,11
	.byte	'CCTRL',0
	.word	25516
	.byte	4,4,35,128,248,3,11
	.byte	'CCNT',0
	.word	25454
	.byte	4,4,35,132,248,3,11
	.byte	'ICNT',0
	.word	27630
	.byte	4,4,35,136,248,3,11
	.byte	'M1CNT',0
	.word	27835
	.byte	4,4,35,140,248,3,11
	.byte	'M2CNT',0
	.word	27898
	.byte	4,4,35,144,248,3,11
	.byte	'M3CNT',0
	.word	27961
	.byte	4,4,35,148,248,3,11
	.byte	'reserved_FC18',0
	.word	30467
	.byte	232,1,4,35,152,248,3,11
	.byte	'DBGSR',0
	.word	26208
	.byte	4,4,35,128,250,3,11
	.byte	'reserved_FD04',0
	.word	2826
	.byte	4,4,35,132,250,3,11
	.byte	'EXEVT',0
	.word	27084
	.byte	4,4,35,136,250,3,11
	.byte	'CREVT',0
	.word	25960
	.byte	4,4,35,140,250,3,11
	.byte	'SWEVT',0
	.word	29325
	.byte	4,4,35,144,250,3,11
	.byte	'reserved_FD14',0
	.word	30478
	.byte	28,4,35,148,250,3,11
	.byte	'TRIG_ACC',0
	.word	29778
	.byte	4,4,35,176,250,3,11
	.byte	'reserved_FD34',0
	.word	4985
	.byte	12,4,35,180,250,3,11
	.byte	'DMS',0
	.word	26711
	.byte	4,4,35,192,250,3,11
	.byte	'DCX',0
	.word	26461
	.byte	4,4,35,196,250,3,11
	.byte	'DBGTCR',0
	.word	26271
	.byte	4,4,35,200,250,3,11
	.byte	'reserved_FD4C',0
	.word	30487
	.byte	180,1,4,35,204,250,3,11
	.byte	'PCXI',0
	.word	28273
	.byte	4,4,35,128,252,3,11
	.byte	'PSW',0
	.word	28709
	.byte	4,4,35,132,252,3,11
	.byte	'PC',0
	.word	28024
	.byte	4,4,35,136,252,3,11
	.byte	'reserved_FE0C',0
	.word	4645
	.byte	8,4,35,140,252,3,11
	.byte	'SYSCON',0
	.word	29388
	.byte	4,4,35,148,252,3,11
	.byte	'CPU_ID',0
	.word	25834
	.byte	4,4,35,152,252,3,11
	.byte	'CORE_ID',0
	.word	25643
	.byte	4,4,35,156,252,3,11
	.byte	'BIV',0
	.word	25332
	.byte	4,4,35,160,252,3,11
	.byte	'BTV',0
	.word	25393
	.byte	4,4,35,164,252,3,11
	.byte	'ISP',0
	.word	27713
	.byte	4,4,35,168,252,3,11
	.byte	'ICR',0
	.word	14847
	.byte	4,4,35,172,252,3,11
	.byte	'reserved_FE30',0
	.word	4645
	.byte	8,4,35,176,252,3,11
	.byte	'FCX',0
	.word	27147
	.byte	4,4,35,184,252,3,11
	.byte	'LCX',0
	.word	27774
	.byte	4,4,35,188,252,3,11
	.byte	'reserved_FE40',0
	.word	30498
	.byte	16,4,35,192,252,3,11
	.byte	'CUS_ID',0
	.word	26023
	.byte	4,4,35,208,252,3,11
	.byte	'reserved_FE54',0
	.word	30507
	.byte	172,1,4,35,212,252,3,11
	.byte	'D',0
	.word	30518
	.byte	64,4,35,128,254,3,11
	.byte	'reserved_FF40',0
	.word	30527
	.byte	64,4,35,192,254,3,11
	.byte	'A',0
	.word	30536
	.byte	64,4,35,128,255,3,11
	.byte	'reserved_FFC0',0
	.word	30527
	.byte	64,4,35,192,255,3,0,12
	.word	30545
	.byte	33
	.byte	'Ifx_CPU',0,16,130,11,3
	.word	32336
	.byte	18,10,127,9,1,19
	.byte	'IfxCpu_Id_0',0,0,19
	.byte	'IfxCpu_Id_1',0,1,19
	.byte	'IfxCpu_Id_none',0,2,0,33
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	32358
	.byte	33
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	9799
	.byte	33
	.byte	'Ifx_SRC_SRCR_Bits',0,12,62,3
	.word	10352
	.byte	33
	.byte	'Ifx_SRC_SRCR',0,12,75,3
	.word	10642
	.byte	13
	.byte	'_Ifx_SRC_AGBT',0,12,86,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	32503
	.byte	33
	.byte	'Ifx_SRC_AGBT',0,12,89,3
	.word	32535
	.byte	13
	.byte	'_Ifx_SRC_ASCLIN',0,12,92,25,12,11
	.byte	'TX',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'RX',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,8,0,12
	.word	32561
	.byte	33
	.byte	'Ifx_SRC_ASCLIN',0,12,97,3
	.word	32620
	.byte	13
	.byte	'_Ifx_SRC_BCUSPB',0,12,100,25,4,11
	.byte	'SBSRC',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	32648
	.byte	33
	.byte	'Ifx_SRC_BCUSPB',0,12,103,3
	.word	32685
	.byte	16,64
	.word	10642
	.byte	17,15,0,13
	.byte	'_Ifx_SRC_CAN',0,12,106,25,64,11
	.byte	'INT',0
	.word	32713
	.byte	64,2,35,0,0,12
	.word	32722
	.byte	33
	.byte	'Ifx_SRC_CAN',0,12,109,3
	.word	32754
	.byte	13
	.byte	'_Ifx_SRC_CCU6',0,12,112,25,16,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SR2',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'SR3',0
	.word	10642
	.byte	4,2,35,12,0,12
	.word	32779
	.byte	33
	.byte	'Ifx_SRC_CCU6',0,12,118,3
	.word	32851
	.byte	16,8
	.word	10642
	.byte	17,1,0,13
	.byte	'_Ifx_SRC_CERBERUS',0,12,121,25,8,11
	.byte	'SR',0
	.word	32877
	.byte	8,2,35,0,0,12
	.word	32886
	.byte	33
	.byte	'Ifx_SRC_CERBERUS',0,12,124,3
	.word	32922
	.byte	13
	.byte	'_Ifx_SRC_CIF',0,12,127,25,16,11
	.byte	'MI',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'MIEP',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'ISP',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'MJPEG',0
	.word	10642
	.byte	4,2,35,12,0,12
	.word	32952
	.byte	33
	.byte	'Ifx_SRC_CIF',0,12,133,1,3
	.word	33025
	.byte	13
	.byte	'_Ifx_SRC_CPU',0,12,136,1,25,4,11
	.byte	'SBSRC',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	33051
	.byte	33
	.byte	'Ifx_SRC_CPU',0,12,139,1,3
	.word	33086
	.byte	16,192,1
	.word	10642
	.byte	17,47,0,13
	.byte	'_Ifx_SRC_DMA',0,12,142,1,25,208,1,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'reserved_4',0
	.word	4985
	.byte	12,2,35,4,11
	.byte	'CH',0
	.word	33112
	.byte	192,1,2,35,16,0,12
	.word	33122
	.byte	33
	.byte	'Ifx_SRC_DMA',0,12,147,1,3
	.word	33189
	.byte	13
	.byte	'_Ifx_SRC_DSADC',0,12,150,1,25,8,11
	.byte	'SRM',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SRA',0
	.word	10642
	.byte	4,2,35,4,0,12
	.word	33215
	.byte	33
	.byte	'Ifx_SRC_DSADC',0,12,154,1,3
	.word	33263
	.byte	13
	.byte	'_Ifx_SRC_EMEM',0,12,157,1,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	33291
	.byte	33
	.byte	'Ifx_SRC_EMEM',0,12,160,1,3
	.word	33324
	.byte	16,40
	.word	612
	.byte	17,39,0,13
	.byte	'_Ifx_SRC_ERAY',0,12,163,1,25,80,11
	.byte	'INT',0
	.word	32877
	.byte	8,2,35,0,11
	.byte	'TINT',0
	.word	32877
	.byte	8,2,35,8,11
	.byte	'NDAT',0
	.word	32877
	.byte	8,2,35,16,11
	.byte	'MBSC',0
	.word	32877
	.byte	8,2,35,24,11
	.byte	'OBUSY',0
	.word	10642
	.byte	4,2,35,32,11
	.byte	'IBUSY',0
	.word	10642
	.byte	4,2,35,36,11
	.byte	'reserved_28',0
	.word	33351
	.byte	40,2,35,40,0,12
	.word	33360
	.byte	33
	.byte	'Ifx_SRC_ERAY',0,12,172,1,3
	.word	33487
	.byte	13
	.byte	'_Ifx_SRC_ETH',0,12,175,1,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	33514
	.byte	33
	.byte	'Ifx_SRC_ETH',0,12,178,1,3
	.word	33546
	.byte	13
	.byte	'_Ifx_SRC_FCE',0,12,181,1,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	33572
	.byte	33
	.byte	'Ifx_SRC_FCE',0,12,184,1,3
	.word	33604
	.byte	13
	.byte	'_Ifx_SRC_FFT',0,12,187,1,25,12,11
	.byte	'DONE',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'RFS',0
	.word	10642
	.byte	4,2,35,8,0,12
	.word	33630
	.byte	33
	.byte	'Ifx_SRC_FFT',0,12,192,1,3
	.word	33690
	.byte	13
	.byte	'_Ifx_SRC_GPSR',0,12,195,1,25,32,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SR2',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'SR3',0
	.word	10642
	.byte	4,2,35,12,11
	.byte	'reserved_10',0
	.word	30498
	.byte	16,2,35,16,0,12
	.word	33716
	.byte	33
	.byte	'Ifx_SRC_GPSR',0,12,202,1,3
	.word	33810
	.byte	13
	.byte	'_Ifx_SRC_GPT12',0,12,205,1,25,48,11
	.byte	'CIRQ',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'T2',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'T3',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'T4',0
	.word	10642
	.byte	4,2,35,12,11
	.byte	'T5',0
	.word	10642
	.byte	4,2,35,16,11
	.byte	'T6',0
	.word	10642
	.byte	4,2,35,20,11
	.byte	'reserved_18',0
	.word	4016
	.byte	24,2,35,24,0,12
	.word	33837
	.byte	33
	.byte	'Ifx_SRC_GPT12',0,12,214,1,3
	.word	33954
	.byte	16,12
	.word	10642
	.byte	17,2,0,16,32
	.word	10642
	.byte	17,7,0,16,32
	.word	33991
	.byte	17,0,0,16,88
	.word	612
	.byte	17,87,0,16,108
	.word	10642
	.byte	17,26,0,16,96
	.word	612
	.byte	17,95,0,16,96
	.word	33991
	.byte	17,2,0,16,160,3
	.word	612
	.byte	17,159,3,0,16,64
	.word	33991
	.byte	17,1,0,16,192,3
	.word	612
	.byte	17,191,3,0,16,16
	.word	10642
	.byte	17,3,0,16,64
	.word	34076
	.byte	17,3,0,16,192,2
	.word	612
	.byte	17,191,2,0,16,52
	.word	612
	.byte	17,51,0,13
	.byte	'_Ifx_SRC_GTM',0,12,217,1,25,204,18,11
	.byte	'AEIIRQ',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'ARUIRQ',0
	.word	33982
	.byte	12,2,35,4,11
	.byte	'reserved_10',0
	.word	2826
	.byte	4,2,35,16,11
	.byte	'BRCIRQ',0
	.word	10642
	.byte	4,2,35,20,11
	.byte	'CMPIRQ',0
	.word	10642
	.byte	4,2,35,24,11
	.byte	'SPEIRQ',0
	.word	32877
	.byte	8,2,35,28,11
	.byte	'reserved_24',0
	.word	4645
	.byte	8,2,35,36,11
	.byte	'PSM',0
	.word	34000
	.byte	32,2,35,44,11
	.byte	'reserved_4C',0
	.word	34009
	.byte	88,2,35,76,11
	.byte	'DPLL',0
	.word	34018
	.byte	108,3,35,164,1,11
	.byte	'reserved_110',0
	.word	34027
	.byte	96,3,35,144,2,11
	.byte	'ERR',0
	.word	10642
	.byte	4,3,35,240,2,11
	.byte	'reserved_174',0
	.word	4985
	.byte	12,3,35,244,2,11
	.byte	'TIM',0
	.word	34036
	.byte	96,3,35,128,3,11
	.byte	'reserved_1E0',0
	.word	34045
	.byte	160,3,3,35,224,3,11
	.byte	'MCS',0
	.word	34036
	.byte	96,3,35,128,7,11
	.byte	'reserved_3E0',0
	.word	34045
	.byte	160,3,3,35,224,7,11
	.byte	'TOM',0
	.word	34056
	.byte	64,3,35,128,11,11
	.byte	'reserved_5C0',0
	.word	34065
	.byte	192,3,3,35,192,11,11
	.byte	'ATOM',0
	.word	34085
	.byte	64,3,35,128,15,11
	.byte	'reserved_7C0',0
	.word	34094
	.byte	192,2,3,35,192,15,11
	.byte	'MCSW0',0
	.word	33982
	.byte	12,3,35,128,18,11
	.byte	'reserved_90C',0
	.word	34105
	.byte	52,3,35,140,18,11
	.byte	'MCSW1',0
	.word	33982
	.byte	12,3,35,192,18,0,12
	.word	34114
	.byte	33
	.byte	'Ifx_SRC_GTM',0,12,243,1,3
	.word	34574
	.byte	13
	.byte	'_Ifx_SRC_HSCT',0,12,246,1,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	34600
	.byte	33
	.byte	'Ifx_SRC_HSCT',0,12,249,1,3
	.word	34633
	.byte	13
	.byte	'_Ifx_SRC_HSSL',0,12,252,1,25,16,11
	.byte	'COK',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'RDI',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'TRG',0
	.word	10642
	.byte	4,2,35,12,0,12
	.word	34660
	.byte	33
	.byte	'Ifx_SRC_HSSL',0,12,130,2,3
	.word	34733
	.byte	16,56
	.word	612
	.byte	17,55,0,13
	.byte	'_Ifx_SRC_I2C',0,12,133,2,25,80,11
	.byte	'BREQ',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'LBREQ',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SREQ',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'LSREQ',0
	.word	10642
	.byte	4,2,35,12,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,16,11
	.byte	'P',0
	.word	10642
	.byte	4,2,35,20,11
	.byte	'reserved_18',0
	.word	34760
	.byte	56,2,35,24,0,12
	.word	34769
	.byte	33
	.byte	'Ifx_SRC_I2C',0,12,142,2,3
	.word	34892
	.byte	13
	.byte	'_Ifx_SRC_LMU',0,12,145,2,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	34918
	.byte	33
	.byte	'Ifx_SRC_LMU',0,12,148,2,3
	.word	34950
	.byte	13
	.byte	'_Ifx_SRC_MSC',0,12,151,2,25,20,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SR2',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'SR3',0
	.word	10642
	.byte	4,2,35,12,11
	.byte	'SR4',0
	.word	10642
	.byte	4,2,35,16,0,12
	.word	34976
	.byte	33
	.byte	'Ifx_SRC_MSC',0,12,158,2,3
	.word	35061
	.byte	13
	.byte	'_Ifx_SRC_PMU',0,12,161,2,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	35087
	.byte	33
	.byte	'Ifx_SRC_PMU',0,12,164,2,3
	.word	35119
	.byte	13
	.byte	'_Ifx_SRC_PSI5',0,12,167,2,25,32,11
	.byte	'SR',0
	.word	33991
	.byte	32,2,35,0,0,12
	.word	35145
	.byte	33
	.byte	'Ifx_SRC_PSI5',0,12,170,2,3
	.word	35178
	.byte	13
	.byte	'_Ifx_SRC_PSI5S',0,12,173,2,25,32,11
	.byte	'SR',0
	.word	33991
	.byte	32,2,35,0,0,12
	.word	35205
	.byte	33
	.byte	'Ifx_SRC_PSI5S',0,12,176,2,3
	.word	35239
	.byte	13
	.byte	'_Ifx_SRC_QSPI',0,12,179,2,25,24,11
	.byte	'TX',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'RX',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'ERR',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'PT',0
	.word	10642
	.byte	4,2,35,12,11
	.byte	'HC',0
	.word	10642
	.byte	4,2,35,16,11
	.byte	'U',0
	.word	10642
	.byte	4,2,35,20,0,12
	.word	35267
	.byte	33
	.byte	'Ifx_SRC_QSPI',0,12,187,2,3
	.word	35360
	.byte	13
	.byte	'_Ifx_SRC_SCR',0,12,190,2,25,4,11
	.byte	'SR',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	35387
	.byte	33
	.byte	'Ifx_SRC_SCR',0,12,193,2,3
	.word	35419
	.byte	13
	.byte	'_Ifx_SRC_SCU',0,12,196,2,25,20,11
	.byte	'DTS',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'ERU',0
	.word	34076
	.byte	16,2,35,4,0,12
	.word	35445
	.byte	33
	.byte	'Ifx_SRC_SCU',0,12,200,2,3
	.word	35491
	.byte	16,24
	.word	10642
	.byte	17,5,0,13
	.byte	'_Ifx_SRC_SENT',0,12,203,2,25,24,11
	.byte	'SR',0
	.word	35517
	.byte	24,2,35,0,0,12
	.word	35526
	.byte	33
	.byte	'Ifx_SRC_SENT',0,12,206,2,3
	.word	35559
	.byte	13
	.byte	'_Ifx_SRC_SMU',0,12,209,2,25,12,11
	.byte	'SR',0
	.word	33982
	.byte	12,2,35,0,0,12
	.word	35586
	.byte	33
	.byte	'Ifx_SRC_SMU',0,12,212,2,3
	.word	35618
	.byte	13
	.byte	'_Ifx_SRC_STM',0,12,215,2,25,8,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,0,12
	.word	35644
	.byte	33
	.byte	'Ifx_SRC_STM',0,12,219,2,3
	.word	35690
	.byte	13
	.byte	'_Ifx_SRC_VADCCG',0,12,222,2,25,16,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SR2',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'SR3',0
	.word	10642
	.byte	4,2,35,12,0,12
	.word	35716
	.byte	33
	.byte	'Ifx_SRC_VADCCG',0,12,228,2,3
	.word	35791
	.byte	13
	.byte	'_Ifx_SRC_VADCG',0,12,231,2,25,16,11
	.byte	'SR0',0
	.word	10642
	.byte	4,2,35,0,11
	.byte	'SR1',0
	.word	10642
	.byte	4,2,35,4,11
	.byte	'SR2',0
	.word	10642
	.byte	4,2,35,8,11
	.byte	'SR3',0
	.word	10642
	.byte	4,2,35,12,0,12
	.word	35820
	.byte	33
	.byte	'Ifx_SRC_VADCG',0,12,237,2,3
	.word	35894
	.byte	13
	.byte	'_Ifx_SRC_XBAR',0,12,240,2,25,4,11
	.byte	'SRC',0
	.word	10642
	.byte	4,2,35,0,0,12
	.word	35922
	.byte	33
	.byte	'Ifx_SRC_XBAR',0,12,243,2,3
	.word	35956
	.byte	16,4
	.word	32503
	.byte	17,0,0,12
	.word	35983
	.byte	13
	.byte	'_Ifx_SRC_GAGBT',0,12,128,3,25,4,11
	.byte	'AGBT',0
	.word	35992
	.byte	4,2,35,0,0,12
	.word	35997
	.byte	33
	.byte	'Ifx_SRC_GAGBT',0,12,131,3,3
	.word	36033
	.byte	16,48
	.word	32561
	.byte	17,3,0,12
	.word	36061
	.byte	13
	.byte	'_Ifx_SRC_GASCLIN',0,12,134,3,25,48,11
	.byte	'ASCLIN',0
	.word	36070
	.byte	48,2,35,0,0,12
	.word	36075
	.byte	33
	.byte	'Ifx_SRC_GASCLIN',0,12,137,3,3
	.word	36115
	.byte	12
	.word	32648
	.byte	13
	.byte	'_Ifx_SRC_GBCU',0,12,140,3,25,4,11
	.byte	'SPB',0
	.word	36145
	.byte	4,2,35,0,0,12
	.word	36150
	.byte	33
	.byte	'Ifx_SRC_GBCU',0,12,143,3,3
	.word	36184
	.byte	16,64
	.word	32722
	.byte	17,0,0,12
	.word	36211
	.byte	13
	.byte	'_Ifx_SRC_GCAN',0,12,146,3,25,64,11
	.byte	'CAN',0
	.word	36220
	.byte	64,2,35,0,0,12
	.word	36225
	.byte	33
	.byte	'Ifx_SRC_GCAN',0,12,149,3,3
	.word	36259
	.byte	16,32
	.word	32779
	.byte	17,1,0,12
	.word	36286
	.byte	13
	.byte	'_Ifx_SRC_GCCU6',0,12,152,3,25,32,11
	.byte	'CCU6',0
	.word	36295
	.byte	32,2,35,0,0,12
	.word	36300
	.byte	33
	.byte	'Ifx_SRC_GCCU6',0,12,155,3,3
	.word	36336
	.byte	12
	.word	32886
	.byte	13
	.byte	'_Ifx_SRC_GCERBERUS',0,12,158,3,25,8,11
	.byte	'CERBERUS',0
	.word	36364
	.byte	8,2,35,0,0,12
	.word	36369
	.byte	33
	.byte	'Ifx_SRC_GCERBERUS',0,12,161,3,3
	.word	36413
	.byte	16,16
	.word	32952
	.byte	17,0,0,12
	.word	36445
	.byte	13
	.byte	'_Ifx_SRC_GCIF',0,12,164,3,25,16,11
	.byte	'CIF',0
	.word	36454
	.byte	16,2,35,0,0,12
	.word	36459
	.byte	33
	.byte	'Ifx_SRC_GCIF',0,12,167,3,3
	.word	36493
	.byte	16,8
	.word	33051
	.byte	17,1,0,12
	.word	36520
	.byte	13
	.byte	'_Ifx_SRC_GCPU',0,12,170,3,25,8,11
	.byte	'CPU',0
	.word	36529
	.byte	8,2,35,0,0,12
	.word	36534
	.byte	33
	.byte	'Ifx_SRC_GCPU',0,12,173,3,3
	.word	36568
	.byte	16,208,1
	.word	33122
	.byte	17,0,0,12
	.word	36595
	.byte	13
	.byte	'_Ifx_SRC_GDMA',0,12,176,3,25,208,1,11
	.byte	'DMA',0
	.word	36605
	.byte	208,1,2,35,0,0,12
	.word	36610
	.byte	33
	.byte	'Ifx_SRC_GDMA',0,12,179,3,3
	.word	36646
	.byte	12
	.word	33215
	.byte	12
	.word	33215
	.byte	12
	.word	33215
	.byte	13
	.byte	'_Ifx_SRC_GDSADC',0,12,182,3,25,32,11
	.byte	'DSADC0',0
	.word	36673
	.byte	8,2,35,0,11
	.byte	'reserved_8',0
	.word	4645
	.byte	8,2,35,8,11
	.byte	'DSADC2',0
	.word	36678
	.byte	8,2,35,16,11
	.byte	'DSADC3',0
	.word	36683
	.byte	8,2,35,24,0,12
	.word	36688
	.byte	33
	.byte	'Ifx_SRC_GDSADC',0,12,188,3,3
	.word	36779
	.byte	16,4
	.word	33291
	.byte	17,0,0,12
	.word	36808
	.byte	13
	.byte	'_Ifx_SRC_GEMEM',0,12,191,3,25,4,11
	.byte	'EMEM',0
	.word	36817
	.byte	4,2,35,0,0,12
	.word	36822
	.byte	33
	.byte	'Ifx_SRC_GEMEM',0,12,194,3,3
	.word	36858
	.byte	16,80
	.word	33360
	.byte	17,0,0,12
	.word	36886
	.byte	13
	.byte	'_Ifx_SRC_GERAY',0,12,197,3,25,80,11
	.byte	'ERAY',0
	.word	36895
	.byte	80,2,35,0,0,12
	.word	36900
	.byte	33
	.byte	'Ifx_SRC_GERAY',0,12,200,3,3
	.word	36936
	.byte	16,4
	.word	33514
	.byte	17,0,0,12
	.word	36964
	.byte	13
	.byte	'_Ifx_SRC_GETH',0,12,203,3,25,4,11
	.byte	'ETH',0
	.word	36973
	.byte	4,2,35,0,0,12
	.word	36978
	.byte	33
	.byte	'Ifx_SRC_GETH',0,12,206,3,3
	.word	37012
	.byte	16,4
	.word	33572
	.byte	17,0,0,12
	.word	37039
	.byte	13
	.byte	'_Ifx_SRC_GFCE',0,12,209,3,25,4,11
	.byte	'FCE',0
	.word	37048
	.byte	4,2,35,0,0,12
	.word	37053
	.byte	33
	.byte	'Ifx_SRC_GFCE',0,12,212,3,3
	.word	37087
	.byte	16,12
	.word	33630
	.byte	17,0,0,12
	.word	37114
	.byte	13
	.byte	'_Ifx_SRC_GFFT',0,12,215,3,25,12,11
	.byte	'FFT',0
	.word	37123
	.byte	12,2,35,0,0,12
	.word	37128
	.byte	33
	.byte	'Ifx_SRC_GFFT',0,12,218,3,3
	.word	37162
	.byte	16,64
	.word	33716
	.byte	17,1,0,12
	.word	37189
	.byte	13
	.byte	'_Ifx_SRC_GGPSR',0,12,221,3,25,64,11
	.byte	'GPSR',0
	.word	37198
	.byte	64,2,35,0,0,12
	.word	37203
	.byte	33
	.byte	'Ifx_SRC_GGPSR',0,12,224,3,3
	.word	37239
	.byte	16,48
	.word	33837
	.byte	17,0,0,12
	.word	37267
	.byte	13
	.byte	'_Ifx_SRC_GGPT12',0,12,227,3,25,48,11
	.byte	'GPT12',0
	.word	37276
	.byte	48,2,35,0,0,12
	.word	37281
	.byte	33
	.byte	'Ifx_SRC_GGPT12',0,12,230,3,3
	.word	37319
	.byte	16,204,18
	.word	34114
	.byte	17,0,0,12
	.word	37348
	.byte	13
	.byte	'_Ifx_SRC_GGTM',0,12,233,3,25,204,18,11
	.byte	'GTM',0
	.word	37358
	.byte	204,18,2,35,0,0,12
	.word	37363
	.byte	33
	.byte	'Ifx_SRC_GGTM',0,12,236,3,3
	.word	37399
	.byte	16,4
	.word	34600
	.byte	17,0,0,12
	.word	37426
	.byte	13
	.byte	'_Ifx_SRC_GHSCT',0,12,239,3,25,4,11
	.byte	'HSCT',0
	.word	37435
	.byte	4,2,35,0,0,12
	.word	37440
	.byte	33
	.byte	'Ifx_SRC_GHSCT',0,12,242,3,3
	.word	37476
	.byte	16,64
	.word	34660
	.byte	17,3,0,12
	.word	37504
	.byte	13
	.byte	'_Ifx_SRC_GHSSL',0,12,245,3,25,68,11
	.byte	'HSSL',0
	.word	37513
	.byte	64,2,35,0,11
	.byte	'EXI',0
	.word	10642
	.byte	4,2,35,64,0,12
	.word	37518
	.byte	33
	.byte	'Ifx_SRC_GHSSL',0,12,249,3,3
	.word	37567
	.byte	16,80
	.word	34769
	.byte	17,0,0,12
	.word	37595
	.byte	13
	.byte	'_Ifx_SRC_GI2C',0,12,252,3,25,80,11
	.byte	'I2C',0
	.word	37604
	.byte	80,2,35,0,0,12
	.word	37609
	.byte	33
	.byte	'Ifx_SRC_GI2C',0,12,255,3,3
	.word	37643
	.byte	16,4
	.word	34918
	.byte	17,0,0,12
	.word	37670
	.byte	13
	.byte	'_Ifx_SRC_GLMU',0,12,130,4,25,4,11
	.byte	'LMU',0
	.word	37679
	.byte	4,2,35,0,0,12
	.word	37684
	.byte	33
	.byte	'Ifx_SRC_GLMU',0,12,133,4,3
	.word	37718
	.byte	16,40
	.word	34976
	.byte	17,1,0,12
	.word	37745
	.byte	13
	.byte	'_Ifx_SRC_GMSC',0,12,136,4,25,40,11
	.byte	'MSC',0
	.word	37754
	.byte	40,2,35,0,0,12
	.word	37759
	.byte	33
	.byte	'Ifx_SRC_GMSC',0,12,139,4,3
	.word	37793
	.byte	16,8
	.word	35087
	.byte	17,1,0,12
	.word	37820
	.byte	13
	.byte	'_Ifx_SRC_GPMU',0,12,142,4,25,8,11
	.byte	'PMU',0
	.word	37829
	.byte	8,2,35,0,0,12
	.word	37834
	.byte	33
	.byte	'Ifx_SRC_GPMU',0,12,145,4,3
	.word	37868
	.byte	16,32
	.word	35145
	.byte	17,0,0,12
	.word	37895
	.byte	13
	.byte	'_Ifx_SRC_GPSI5',0,12,148,4,25,32,11
	.byte	'PSI5',0
	.word	37904
	.byte	32,2,35,0,0,12
	.word	37909
	.byte	33
	.byte	'Ifx_SRC_GPSI5',0,12,151,4,3
	.word	37945
	.byte	16,32
	.word	35205
	.byte	17,0,0,12
	.word	37973
	.byte	13
	.byte	'_Ifx_SRC_GPSI5S',0,12,154,4,25,32,11
	.byte	'PSI5S',0
	.word	37982
	.byte	32,2,35,0,0,12
	.word	37987
	.byte	33
	.byte	'Ifx_SRC_GPSI5S',0,12,157,4,3
	.word	38025
	.byte	16,96
	.word	35267
	.byte	17,3,0,12
	.word	38054
	.byte	13
	.byte	'_Ifx_SRC_GQSPI',0,12,160,4,25,96,11
	.byte	'QSPI',0
	.word	38063
	.byte	96,2,35,0,0,12
	.word	38068
	.byte	33
	.byte	'Ifx_SRC_GQSPI',0,12,163,4,3
	.word	38104
	.byte	16,4
	.word	35387
	.byte	17,0,0,12
	.word	38132
	.byte	13
	.byte	'_Ifx_SRC_GSCR',0,12,166,4,25,4,11
	.byte	'SCR',0
	.word	38141
	.byte	4,2,35,0,0,12
	.word	38146
	.byte	33
	.byte	'Ifx_SRC_GSCR',0,12,169,4,3
	.word	38180
	.byte	12
	.word	35445
	.byte	13
	.byte	'_Ifx_SRC_GSCU',0,12,172,4,25,20,11
	.byte	'SCU',0
	.word	38207
	.byte	20,2,35,0,0,12
	.word	38212
	.byte	33
	.byte	'Ifx_SRC_GSCU',0,12,175,4,3
	.word	38246
	.byte	16,24
	.word	35526
	.byte	17,0,0,12
	.word	38273
	.byte	13
	.byte	'_Ifx_SRC_GSENT',0,12,178,4,25,24,11
	.byte	'SENT',0
	.word	38282
	.byte	24,2,35,0,0,12
	.word	38287
	.byte	33
	.byte	'Ifx_SRC_GSENT',0,12,181,4,3
	.word	38323
	.byte	16,12
	.word	35586
	.byte	17,0,0,12
	.word	38351
	.byte	13
	.byte	'_Ifx_SRC_GSMU',0,12,184,4,25,12,11
	.byte	'SMU',0
	.word	38360
	.byte	12,2,35,0,0,12
	.word	38365
	.byte	33
	.byte	'Ifx_SRC_GSMU',0,12,187,4,3
	.word	38399
	.byte	16,16
	.word	35644
	.byte	17,1,0,12
	.word	38426
	.byte	13
	.byte	'_Ifx_SRC_GSTM',0,12,190,4,25,16,11
	.byte	'STM',0
	.word	38435
	.byte	16,2,35,0,0,12
	.word	38440
	.byte	33
	.byte	'Ifx_SRC_GSTM',0,12,193,4,3
	.word	38474
	.byte	16,64
	.word	35820
	.byte	17,3,0,12
	.word	38501
	.byte	16,224,1
	.word	612
	.byte	17,223,1,0,16,32
	.word	35716
	.byte	17,1,0,12
	.word	38526
	.byte	13
	.byte	'_Ifx_SRC_GVADC',0,12,196,4,25,192,2,11
	.byte	'G',0
	.word	38510
	.byte	64,2,35,0,11
	.byte	'reserved_40',0
	.word	38515
	.byte	224,1,2,35,64,11
	.byte	'CG',0
	.word	38535
	.byte	32,3,35,160,2,0,12
	.word	38540
	.byte	33
	.byte	'Ifx_SRC_GVADC',0,12,201,4,3
	.word	38609
	.byte	12
	.word	35922
	.byte	13
	.byte	'_Ifx_SRC_GXBAR',0,12,204,4,25,4,11
	.byte	'XBAR',0
	.word	38637
	.byte	4,2,35,0,0,12
	.word	38642
	.byte	33
	.byte	'Ifx_SRC_GXBAR',0,12,207,4,3
	.word	38678
	.byte	13
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	38706
	.byte	13
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	39263
	.byte	13
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,14
	.byte	'STM0DIS',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'STM1DIS',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'STM2DIS',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,4
	.word	850
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	39340
	.byte	13
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,14
	.byte	'BAUD1DIV',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'BAUD2DIV',0,1
	.word	612
	.byte	4,0,2,35,0,14
	.byte	'SRIDIV',0,1
	.word	612
	.byte	4,4,2,35,1,14
	.byte	'LPDIV',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'SPBDIV',0,1
	.word	612
	.byte	4,4,2,35,2,14
	.byte	'FSI2DIV',0,1
	.word	612
	.byte	2,2,2,35,2,14
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'FSIDIV',0,1
	.word	612
	.byte	2,6,2,35,3,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,14
	.byte	'CLKSEL',0,1
	.word	612
	.byte	2,2,2,35,3,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	39476
	.byte	13
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,14
	.byte	'CANDIV',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'ERAYDIV',0,1
	.word	612
	.byte	4,0,2,35,0,14
	.byte	'STMDIV',0,1
	.word	612
	.byte	4,4,2,35,1,14
	.byte	'GTMDIV',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'ETHDIV',0,1
	.word	612
	.byte	4,4,2,35,2,14
	.byte	'ASCLINFDIV',0,1
	.word	612
	.byte	4,0,2,35,2,14
	.byte	'ASCLINSDIV',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'INSEL',0,1
	.word	612
	.byte	2,2,2,35,3,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	39756
	.byte	13
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,14
	.byte	'BBBDIV',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	26,2,2,35,0,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	39994
	.byte	13
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,14
	.byte	'PLLDIV',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'PLLSEL',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'PLLERAYDIV',0,1
	.word	612
	.byte	6,2,2,35,1,14
	.byte	'PLLERAYSEL',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'SRIDIV',0,1
	.word	612
	.byte	6,2,2,35,2,14
	.byte	'SRISEL',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	5,3,2,35,3,14
	.byte	'SLCK',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,150,1,3
	.word	40122
	.byte	13
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,153,1,16,4,14
	.byte	'SPBDIV',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'SPBSEL',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'GTMDIV',0,1
	.word	612
	.byte	6,2,2,35,1,14
	.byte	'GTMSEL',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'STMDIV',0,1
	.word	612
	.byte	6,2,2,35,2,14
	.byte	'STMSEL',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	5,3,2,35,3,14
	.byte	'SLCK',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,165,1,3
	.word	40365
	.byte	13
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,168,1,16,4,14
	.byte	'MAXDIV',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	26,2,2,35,0,14
	.byte	'UP',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,174,1,3
	.word	40600
	.byte	13
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,177,1,16,4,14
	.byte	'CPU0DIV',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,181,1,3
	.word	40728
	.byte	13
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,5,184,1,16,4,14
	.byte	'CPU1DIV',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7_Bits',0,5,188,1,3
	.word	40828
	.byte	13
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,191,1,16,4,14
	.byte	'CHREV',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'CHTEC',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'CHID',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'EEA',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'UCODE',0,1
	.word	612
	.byte	7,0,2,35,2,14
	.byte	'FSIZE',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'SP',0,1
	.word	612
	.byte	2,2,2,35,3,14
	.byte	'SEC',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,202,1,3
	.word	40928
	.byte	13
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,205,1,16,4,14
	.byte	'PWD',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'START',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'CAL',0,4
	.word	850
	.byte	20,8,2,35,0,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'SLCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,213,1,3
	.word	41136
	.byte	13
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,216,1,16,4,14
	.byte	'LOWER',0,2
	.word	1025
	.byte	10,6,2,35,0,14
	.byte	'reserved_10',0,1
	.word	612
	.byte	5,1,2,35,1,14
	.byte	'LLU',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'UPPER',0,2
	.word	1025
	.byte	10,6,2,35,2,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	4,2,2,35,3,14
	.byte	'SLCK',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'UOF',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,225,1,3
	.word	41301
	.byte	13
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,228,1,16,4,14
	.byte	'RESULT',0,2
	.word	1025
	.byte	10,6,2,35,0,14
	.byte	'reserved_10',0,1
	.word	612
	.byte	4,2,2,35,1,14
	.byte	'RDY',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'BUSY',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,235,1,3
	.word	41484
	.byte	13
	.byte	'_Ifx_SCU_EICR_Bits',0,5,238,1,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'EXIS0',0,1
	.word	612
	.byte	3,1,2,35,0,14
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'FEN0',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'REN0',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'LDEN0',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EIEN0',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'INP0',0,1
	.word	612
	.byte	3,1,2,35,1,14
	.byte	'reserved_15',0,4
	.word	850
	.byte	5,12,2,35,0,14
	.byte	'EXIS1',0,1
	.word	612
	.byte	3,1,2,35,2,14
	.byte	'reserved_23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'FEN1',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'REN1',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'LDEN1',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'EIEN1',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'INP1',0,1
	.word	612
	.byte	3,1,2,35,3,14
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EICR_Bits',0,5,129,2,3
	.word	41638
	.byte	13
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,132,2,16,4,14
	.byte	'INTF0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'INTF1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'INTF2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'INTF3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'INTF4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'INTF5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'INTF6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'INTF7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR_Bits',0,5,143,2,3
	.word	42002
	.byte	13
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,146,2,16,4,14
	.byte	'POL',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'MODE',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'ENON',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PSEL',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,2
	.word	1025
	.byte	12,0,2,35,0,14
	.byte	'EMSF',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'SEMSF',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'reserved_18',0,1
	.word	612
	.byte	6,0,2,35,2,14
	.byte	'EMSFM',0,1
	.word	612
	.byte	2,6,2,35,3,14
	.byte	'SEMSFM',0,1
	.word	612
	.byte	2,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_EMSR_Bits',0,5,159,2,3
	.word	42213
	.byte	13
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,162,2,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	7,1,2,35,0,14
	.byte	'EDCON',0,2
	.word	1025
	.byte	2,7,2,35,0,14
	.byte	'reserved_9',0,4
	.word	850
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,167,2,3
	.word	42465
	.byte	13
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,170,2,16,4,14
	.byte	'ARI',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ARC',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,175,2,3
	.word	42583
	.byte	13
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,178,2,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	28,4,2,35,0,14
	.byte	'EVR13OFF',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'BPEVR13OFF',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,185,2,3
	.word	42694
	.byte	13
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,5,188,2,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	28,4,2,35,0,14
	.byte	'EVR33OFF',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'BPEVR33OFF',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVR33CON_Bits',0,5,195,2,3
	.word	42857
	.byte	13
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,198,2,16,4,14
	.byte	'ADC13V',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'ADC33V',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'ADCSWDV',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'VAL',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,205,2,3
	.word	43020
	.byte	13
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,5,208,2,16,4,14
	.byte	'DVS13TRIM',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'DVS33TRIM',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'VAL',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,5,215,2,3
	.word	43178
	.byte	13
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,218,2,16,4,14
	.byte	'EVR13OVMOD',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'EVR13UVMOD',0,1
	.word	612
	.byte	2,2,2,35,0,14
	.byte	'reserved_6',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'EVR33OVMOD',0,1
	.word	612
	.byte	2,6,2,35,1,14
	.byte	'reserved_10',0,1
	.word	612
	.byte	2,4,2,35,1,14
	.byte	'EVR33UVMOD',0,1
	.word	612
	.byte	2,2,2,35,1,14
	.byte	'reserved_14',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'SWDOVMOD',0,1
	.word	612
	.byte	2,6,2,35,2,14
	.byte	'reserved_18',0,1
	.word	612
	.byte	2,4,2,35,2,14
	.byte	'SWDUVMOD',0,1
	.word	612
	.byte	2,2,2,35,2,14
	.byte	'reserved_22',0,2
	.word	1025
	.byte	10,0,2,35,2,0,33
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,232,2,3
	.word	43343
	.byte	13
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,5,235,2,16,4,14
	.byte	'OSCTRIM',0,2
	.word	1025
	.byte	10,6,2,35,0,14
	.byte	'OSCPTAT',0,1
	.word	612
	.byte	6,0,2,35,1,14
	.byte	'OSCANASEL',0,1
	.word	612
	.byte	4,4,2,35,2,14
	.byte	'HPBGTRIM',0,2
	.word	1025
	.byte	7,5,2,35,2,14
	.byte	'HPBGCLKEN',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'OSC3V3',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'reserved_29',0,1
	.word	612
	.byte	2,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,5,245,2,3
	.word	43672
	.byte	13
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,248,2,16,4,14
	.byte	'EVR13OVVAL',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'EVR33OVVAL',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SWDOVVAL',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,255,2,3
	.word	43893
	.byte	13
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,130,3,16,4,14
	.byte	'RST13TRIM',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	16,8,2,35,0,14
	.byte	'RST13OFF',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'BPRST13OFF',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'RST33OFF',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'BPRST33OFF',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'RSTSWDOFF',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'BPRSTSWDOFF',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,142,3,3
	.word	44056
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,5,145,3,16,4,14
	.byte	'SD5P',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SD5I',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SD5D',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,5,152,3,3
	.word	44328
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,155,3,16,4,14
	.byte	'SD33P',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SD33I',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SD33D',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,162,3,3
	.word	44481
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,5,165,3,16,4,14
	.byte	'CT5REG0',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'CT5REG1',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'CT5REG2',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,5,172,3,3
	.word	44637
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,5,175,3,16,4,14
	.byte	'CT5REG3',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'CT5REG4',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	15,1,2,35,2,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,5,181,3,3
	.word	44799
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,5,184,3,16,4,14
	.byte	'CT33REG0',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'CT33REG1',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'CT33REG2',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,5,191,3,3
	.word	44942
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,5,194,3,16,4,14
	.byte	'CT33REG3',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'CT33REG4',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	15,1,2,35,2,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,5,200,3,3
	.word	45107
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,203,3,16,4,14
	.byte	'SDFREQSPRD',0,2
	.word	1025
	.byte	16,0,2,35,0,14
	.byte	'SDFREQ',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'SDSTEP',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	2,2,2,35,3,14
	.byte	'SDSAMPLE',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,211,3,3
	.word	45252
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,214,3,16,4,14
	.byte	'DRVP',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SDMINMAXDC',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'DRVN',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'SDLUT',0,1
	.word	612
	.byte	6,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,222,3,3
	.word	45433
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,225,3,16,4,14
	.byte	'SDPWMPRE',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SDPID',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SDVOKLVL',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,232,3,3
	.word	45607
	.byte	13
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,5,235,3,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SYNCDIV',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'reserved_11',0,4
	.word	850
	.byte	20,1,2,35,0,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,5,241,3,3
	.word	45767
	.byte	13
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,244,3,16,4,14
	.byte	'EVR13',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'OV13',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EVR33',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'OV33',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'OVSWD',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'UV13',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'UV33',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'UVSWD',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EXTPASS13',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EXTPASS33',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'BGPROK',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'reserved_11',0,4
	.word	850
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,130,4,3
	.word	45911
	.byte	13
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,5,133,4,16,4,14
	.byte	'EVR13TRIM',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'SDVOUTSEL',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	15,1,2,35,2,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,5,139,4,3
	.word	46185
	.byte	13
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,142,4,16,4,14
	.byte	'EVR13UVVAL',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'EVR33UVVAL',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SWDUVVAL',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,149,4,3
	.word	46324
	.byte	13
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,152,4,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'SEL0',0,1
	.word	612
	.byte	4,2,2,35,0,14
	.byte	'reserved_6',0,2
	.word	1025
	.byte	10,0,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'NSEL',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'SEL1',0,1
	.word	612
	.byte	4,2,2,35,2,14
	.byte	'reserved_22',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'DIV1',0,1
	.word	612
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,163,4,3
	.word	46487
	.byte	13
	.byte	'_Ifx_SCU_FDR_Bits',0,5,166,4,16,4,14
	.byte	'STEP',0,2
	.word	1025
	.byte	10,6,2,35,0,14
	.byte	'reserved_10',0,1
	.word	612
	.byte	4,2,2,35,1,14
	.byte	'DM',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'RESULT',0,2
	.word	1025
	.byte	10,6,2,35,2,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	5,1,2,35,3,14
	.byte	'DISCLK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_FDR_Bits',0,5,174,4,3
	.word	46705
	.byte	13
	.byte	'_Ifx_SCU_FMR_Bits',0,5,177,4,16,4,14
	.byte	'FS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'FS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'FS2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'FS3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'FS4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'FS5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'FS6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'FS7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'FC0',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'FC1',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'FC2',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'FC3',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'FC4',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'FC5',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'FC6',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'FC7',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_SCU_FMR_Bits',0,5,197,4,3
	.word	46868
	.byte	13
	.byte	'_Ifx_SCU_ID_Bits',0,5,200,4,16,4,14
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'MODNUMBER',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_ID_Bits',0,5,205,4,3
	.word	47204
	.byte	13
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,208,4,16,4,14
	.byte	'IPEN00',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'IPEN01',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'IPEN02',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'IPEN03',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'IPEN04',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'IPEN05',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'IPEN06',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'IPEN07',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	5,3,2,35,1,14
	.byte	'GEEN0',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'IGP0',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'IPEN10',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'IPEN11',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'IPEN12',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'IPEN13',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'IPEN14',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'IPEN15',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'IPEN16',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'IPEN17',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	5,3,2,35,3,14
	.byte	'GEEN1',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'IGP1',0,1
	.word	612
	.byte	2,0,2,35,3,0,33
	.byte	'Ifx_SCU_IGCR_Bits',0,5,232,4,3
	.word	47311
	.byte	13
	.byte	'_Ifx_SCU_IN_Bits',0,5,235,4,16,4,14
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_IN_Bits',0,5,240,4,3
	.word	47763
	.byte	13
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,243,4,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'PC0',0,1
	.word	612
	.byte	4,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	4,4,2,35,1,14
	.byte	'PC1',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_IOCR_Bits',0,5,250,4,3
	.word	47862
	.byte	13
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,253,4,16,4,14
	.byte	'LBISTREQ',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'LBISTREQP',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PATTERNS',0,2
	.word	1025
	.byte	14,0,2,35,0,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,131,5,3
	.word	48012
	.byte	13
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,134,5,16,4,14
	.byte	'SEED',0,4
	.word	850
	.byte	23,9,2,35,0,14
	.byte	'reserved_23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'SPLITSH',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'BODY',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'LBISTFREQU',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,141,5,3
	.word	48161
	.byte	13
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,144,5,16,4,14
	.byte	'SIGNATURE',0,4
	.word	850
	.byte	24,8,2,35,0,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	7,1,2,35,3,14
	.byte	'LBISTDONE',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,149,5,3
	.word	48322
	.byte	13
	.byte	'_Ifx_SCU_LCLCON_Bits',0,5,152,5,16,4,14
	.byte	'reserved_0',0,2
	.word	1025
	.byte	16,0,2,35,0,14
	.byte	'LS',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'reserved_17',0,2
	.word	1025
	.byte	14,1,2,35,2,14
	.byte	'LSEN',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_LCLCON_Bits',0,5,158,5,3
	.word	48452
	.byte	13
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,161,5,16,4,14
	.byte	'LCLT0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'LCLT1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,166,5,3
	.word	48584
	.byte	13
	.byte	'_Ifx_SCU_MANID_Bits',0,5,169,5,16,4,14
	.byte	'DEPT',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'MANUF',0,2
	.word	1025
	.byte	11,0,2,35,0,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_MANID_Bits',0,5,174,5,3
	.word	48699
	.byte	13
	.byte	'_Ifx_SCU_OMR_Bits',0,5,177,5,16,4,14
	.byte	'PS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,2
	.word	1025
	.byte	14,0,2,35,0,14
	.byte	'PCL0',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'PCL1',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'reserved_18',0,2
	.word	1025
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_OMR_Bits',0,5,185,5,3
	.word	48810
	.byte	13
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,188,5,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PLLLV',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'OSCRES',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'GAINSEL',0,1
	.word	612
	.byte	2,3,2,35,0,14
	.byte	'MODE',0,1
	.word	612
	.byte	2,1,2,35,0,14
	.byte	'SHBY',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PLLHV',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'reserved_9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'X1D',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'X1DEN',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'OSCVAL',0,1
	.word	612
	.byte	5,3,2,35,2,14
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,14
	.byte	'APREN',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'CAP0EN',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'CAP1EN',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'CAP2EN',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'CAP3EN',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,209,5,3
	.word	48968
	.byte	13
	.byte	'_Ifx_SCU_OUT_Bits',0,5,212,5,16,4,14
	.byte	'P0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'P1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_OUT_Bits',0,5,217,5,3
	.word	49380
	.byte	13
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,220,5,16,4,14
	.byte	'CSEL0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'CSEL1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'CSEL2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,2
	.word	1025
	.byte	13,0,2,35,0,14
	.byte	'OVSTRT',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'OVSTP',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'DCINVAL',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'OVCONF',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'POVCONF',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	6,0,2,35,3,0,33
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,233,5,3
	.word	49481
	.byte	13
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,236,5,16,4,14
	.byte	'OVEN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'OVEN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'OVEN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,4
	.word	850
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,242,5,3
	.word	49748
	.byte	13
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,245,5,16,4,14
	.byte	'PDIS0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PDIS1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC_Bits',0,5,250,5,3
	.word	49884
	.byte	13
	.byte	'_Ifx_SCU_PDR_Bits',0,5,253,5,16,4,14
	.byte	'PD0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'PL0',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PD1',0,1
	.word	612
	.byte	3,1,2,35,0,14
	.byte	'PL1',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDR_Bits',0,5,132,6,3
	.word	49995
	.byte	13
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,135,6,16,4,14
	.byte	'PDR0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PDR1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PDR2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PDR3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PDR4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PDR5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PDR6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PDR7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR_Bits',0,5,146,6,3
	.word	50128
	.byte	13
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,149,6,16,4,14
	.byte	'VCOBYP',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'VCOPWD',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'MODEN',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'SETFINDIS',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'CLRFINDIS',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'OSCDISCDIS',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'reserved_7',0,2
	.word	1025
	.byte	2,7,2,35,0,14
	.byte	'NDIV',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'PLLPWD',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'RESLD',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'PDIV',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,166,6,3
	.word	50331
	.byte	13
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,169,6,16,4,14
	.byte	'K2DIV',0,1
	.word	612
	.byte	7,1,2,35,0,14
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'K3DIV',0,1
	.word	612
	.byte	7,1,2,35,1,14
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'K1DIV',0,1
	.word	612
	.byte	7,1,2,35,2,14
	.byte	'reserved_23',0,2
	.word	1025
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,177,6,3
	.word	50687
	.byte	13
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,180,6,16,4,14
	.byte	'MODCFG',0,2
	.word	1025
	.byte	16,0,2,35,0,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,184,6,3
	.word	50865
	.byte	13
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,187,6,16,4,14
	.byte	'VCOBYP',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'VCOPWD',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'SETFINDIS',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'CLRFINDIS',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'OSCDISCDIS',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'reserved_7',0,2
	.word	1025
	.byte	2,7,2,35,0,14
	.byte	'NDIV',0,1
	.word	612
	.byte	5,2,2,35,1,14
	.byte	'reserved_14',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'PLLPWD',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'RESLD',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'reserved_19',0,1
	.word	612
	.byte	5,0,2,35,2,14
	.byte	'PDIV',0,1
	.word	612
	.byte	4,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,204,6,3
	.word	50965
	.byte	13
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,207,6,16,4,14
	.byte	'K2DIV',0,1
	.word	612
	.byte	7,1,2,35,0,14
	.byte	'reserved_7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'K3DIV',0,1
	.word	612
	.byte	4,4,2,35,1,14
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'K1DIV',0,1
	.word	612
	.byte	7,1,2,35,2,14
	.byte	'reserved_23',0,2
	.word	1025
	.byte	9,0,2,35,2,0,33
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,215,6,3
	.word	51335
	.byte	13
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,218,6,16,4,14
	.byte	'VCOBYST',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PWDSTAT',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'VCOLOCK',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'FINDIS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'K1RDY',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'K2RDY',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,227,6,3
	.word	51521
	.byte	13
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,230,6,16,4,14
	.byte	'VCOBYST',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'VCOLOCK',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'FINDIS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'K1RDY',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'K2RDY',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'reserved_6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'MODRUN',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,241,6,3
	.word	51719
	.byte	13
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,244,6,16,4,14
	.byte	'REQSLP',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'SMUSLP',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'reserved_3',0,1
	.word	612
	.byte	5,0,2,35,0,14
	.byte	'PMST',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'reserved_11',0,4
	.word	850
	.byte	21,0,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,251,6,3
	.word	51952
	.byte	13
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,254,6,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1WKEN',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PINAWKEN',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PINBWKEN',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'ESR0DFEN',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'ESR0EDCON',0,1
	.word	612
	.byte	2,1,2,35,0,14
	.byte	'ESR1DFEN',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'ESR1EDCON',0,1
	.word	612
	.byte	2,6,2,35,1,14
	.byte	'PINADFEN',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'PINAEDCON',0,1
	.word	612
	.byte	2,3,2,35,1,14
	.byte	'PINBDFEN',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PINBEDCON',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'SCREN',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'STBYRAMSEL',0,1
	.word	612
	.byte	2,5,2,35,2,14
	.byte	'SCRCLKSEL',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'SCRWKEN',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'TRISTEN',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'TRISTREQ',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'PORSTDF',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'DCDCSYNC',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	3,3,2,35,3,14
	.byte	'ESR0TRIST',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'reserved_30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,153,7,3
	.word	52104
	.byte	13
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,156,7,16,4,14
	.byte	'SCRSTEN',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SCRSTREQ',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	6,0,2,35,0,14
	.byte	'CPUIDLSEL',0,1
	.word	612
	.byte	3,5,2,35,1,14
	.byte	'reserved_11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'IRADIS',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'reserved_13',0,1
	.word	612
	.byte	3,0,2,35,1,14
	.byte	'SCRCFG',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'CPUSEL',0,1
	.word	612
	.byte	3,5,2,35,3,14
	.byte	'STBYEVEN',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'STBYEV',0,1
	.word	612
	.byte	3,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,170,7,3
	.word	52671
	.byte	13
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,5,173,7,16,4,14
	.byte	'SCRINT',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'BUSY',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'SCRECC',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'SCRWDT',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'SCRRST',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'reserved_12',0,1
	.word	612
	.byte	4,0,2,35,1,14
	.byte	'TCINT',0,1
	.word	612
	.byte	8,0,2,35,2,14
	.byte	'TCINTREQ',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'SMURST',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'RST',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'reserved_27',0,1
	.word	612
	.byte	4,1,2,35,3,14
	.byte	'LCK',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,5,187,7,3
	.word	52965
	.byte	13
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,190,7,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'ESR1WKP',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'ESR1OVRUN',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PINAWKP',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PINAOVRUN',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PINBWKP',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PINBOVRUN',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PORSTDF',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'HWCFGEVR',0,1
	.word	612
	.byte	3,3,2,35,1,14
	.byte	'STBYRAM',0,1
	.word	612
	.byte	2,1,2,35,1,14
	.byte	'TRIST',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'SCRST',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'SCRWKP',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'SCR',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'SCRWKEN',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'ESR1WKEN',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'PINAWKEN',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'PINBWKEN',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'reserved_23',0,2
	.word	1025
	.byte	4,5,2,35,2,14
	.byte	'ESR0TRIST',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'reserved_28',0,1
	.word	612
	.byte	4,0,2,35,3,0,33
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,214,7,3
	.word	53243
	.byte	13
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,217,7,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'ESR1WKPCLR',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'ESR1OVRUNCLR',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PINAWKPCLR',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'PINAOVRUNCLR',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PINBWKPCLR',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PINBOVRUNCLR',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'SCRSTCLR',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'SCRWKPCLR',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'reserved_18',0,2
	.word	1025
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,230,7,3
	.word	53739
	.byte	13
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,233,7,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'CLRC',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,2
	.word	1025
	.byte	10,4,2,35,0,14
	.byte	'CSS0',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'CSS1',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'CSS2',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'reserved_15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'USRINFO',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,243,7,3
	.word	54052
	.byte	13
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,246,7,16,4,14
	.byte	'ESR0',0,1
	.word	612
	.byte	2,6,2,35,0,14
	.byte	'ESR1',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'reserved_4',0,1
	.word	612
	.byte	2,2,2,35,0,14
	.byte	'SMU',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'SW',0,1
	.word	612
	.byte	2,6,2,35,1,14
	.byte	'STM0',0,1
	.word	612
	.byte	2,4,2,35,1,14
	.byte	'STM1',0,1
	.word	612
	.byte	2,2,2,35,1,14
	.byte	'STM2',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,129,8,3
	.word	54261
	.byte	13
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,132,8,16,4,14
	.byte	'ESR0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SMU',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'SW',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'STM0',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'STM1',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'STM2',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'reserved_8',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'PORST',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'reserved_17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'CB0',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'CB1',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'CB3',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'reserved_21',0,1
	.word	612
	.byte	2,1,2,35,2,14
	.byte	'EVR13',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'EVR33',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'SWD',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'reserved_26',0,1
	.word	612
	.byte	2,4,2,35,3,14
	.byte	'STBYR',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'reserved_29',0,1
	.word	612
	.byte	3,0,2,35,3,0,33
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,155,8,3
	.word	54472
	.byte	13
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,158,8,16,4,14
	.byte	'HBT',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,4
	.word	850
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,162,8,3
	.word	54904
	.byte	13
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,165,8,16,4,14
	.byte	'HWCFG',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'FTM',0,1
	.word	612
	.byte	7,1,2,35,1,14
	.byte	'MODE',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'FCBAE',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'LUDIS',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'reserved_18',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'TRSTL',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'SPDEN',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'reserved_21',0,1
	.word	612
	.byte	3,0,2,35,2,14
	.byte	'RAMINT',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'reserved_25',0,1
	.word	612
	.byte	7,0,2,35,3,0,33
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,178,8,3
	.word	55000
	.byte	13
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,181,8,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SWRSTREQ',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,4
	.word	850
	.byte	30,0,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,186,8,3
	.word	55260
	.byte	13
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,189,8,16,4,14
	.byte	'CCTRIG0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'RAMINTM',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'SETLUDIS',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,14
	.byte	'DATM',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'reserved_9',0,4
	.word	850
	.byte	23,0,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,198,8,3
	.word	55385
	.byte	13
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,201,8,16,4,14
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,208,8,3
	.word	55582
	.byte	13
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,211,8,16,4,14
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,218,8,3
	.word	55735
	.byte	13
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,221,8,16,4,14
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,228,8,3
	.word	55888
	.byte	13
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,231,8,16,4,14
	.byte	'ESR0T',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'ESR1T',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SMUT',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,238,8,3
	.word	56041
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,247,8,3
	.word	889
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,134,9,3
	.word	1047
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,150,9,3
	.word	1291
	.byte	13
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,153,9,16,4,14
	.byte	'ENDINIT',0,4
	.word	873
	.byte	1,31,2,35,0,14
	.byte	'LCK',0,4
	.word	873
	.byte	1,30,2,35,0,14
	.byte	'PW',0,4
	.word	873
	.byte	14,16,2,35,0,14
	.byte	'REL',0,4
	.word	873
	.byte	16,0,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,159,9,3
	.word	56296
	.byte	13
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,162,9,16,4,14
	.byte	'CLRIRF',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'IR0',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'DR',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'IR1',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'UR',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PAR',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'TCR',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'TCTR',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,175,9,3
	.word	56422
	.byte	13
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,178,9,16,4,14
	.byte	'AE',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'OE',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'IS0',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'DS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'TO',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'IS1',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'US',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PAS',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'TCS',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'TCT',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'TIM',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,191,9,3
	.word	56674
	.byte	15,5,199,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	38706
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN0',0,5,204,9,3
	.word	56893
	.byte	15,5,207,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	39263
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ACCEN1',0,5,212,9,3
	.word	56957
	.byte	15,5,215,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	39340
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ARSTDIS',0,5,220,9,3
	.word	57021
	.byte	15,5,223,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	39476
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON0',0,5,228,9,3
	.word	57086
	.byte	15,5,231,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	39756
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON1',0,5,236,9,3
	.word	57151
	.byte	15,5,239,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	39994
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON2',0,5,244,9,3
	.word	57216
	.byte	15,5,247,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40122
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON3',0,5,252,9,3
	.word	57281
	.byte	15,5,255,9,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40365
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON4',0,5,132,10,3
	.word	57346
	.byte	15,5,135,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40600
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON5',0,5,140,10,3
	.word	57411
	.byte	15,5,143,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40728
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON6',0,5,148,10,3
	.word	57476
	.byte	15,5,151,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40828
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CCUCON7',0,5,156,10,3
	.word	57541
	.byte	15,5,159,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	40928
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_CHIPID',0,5,164,10,3
	.word	57606
	.byte	15,5,167,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	41136
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSCON',0,5,172,10,3
	.word	57670
	.byte	15,5,175,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	41301
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSLIM',0,5,180,10,3
	.word	57734
	.byte	15,5,183,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	41484
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_DTSSTAT',0,5,188,10,3
	.word	57798
	.byte	15,5,191,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	41638
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EICR',0,5,196,10,3
	.word	57863
	.byte	15,5,199,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42002
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EIFR',0,5,204,10,3
	.word	57925
	.byte	15,5,207,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42213
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EMSR',0,5,212,10,3
	.word	57987
	.byte	15,5,215,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42465
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESRCFG',0,5,220,10,3
	.word	58049
	.byte	15,5,223,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42583
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ESROCFG',0,5,228,10,3
	.word	58113
	.byte	15,5,231,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42694
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR13CON',0,5,236,10,3
	.word	58178
	.byte	15,5,239,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	42857
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVR33CON',0,5,244,10,3
	.word	58244
	.byte	15,5,247,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	43020
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,252,10,3
	.word	58310
	.byte	15,5,255,10,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	43178
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRDVSTAT',0,5,132,11,3
	.word	58378
	.byte	15,5,135,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	43343
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,140,11,3
	.word	58445
	.byte	15,5,143,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	43672
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROSCCTRL',0,5,148,11,3
	.word	58513
	.byte	15,5,151,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	43893
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVROVMON',0,5,156,11,3
	.word	58581
	.byte	15,5,159,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44056
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRRSTCON',0,5,164,11,3
	.word	58647
	.byte	15,5,167,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44328
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,5,172,11,3
	.word	58714
	.byte	15,5,175,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44481
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,180,11,3
	.word	58783
	.byte	15,5,183,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44637
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,5,188,11,3
	.word	58852
	.byte	15,5,191,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44799
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,5,196,11,3
	.word	58921
	.byte	15,5,199,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	44942
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,5,204,11,3
	.word	58990
	.byte	15,5,207,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45107
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,5,212,11,3
	.word	59059
	.byte	15,5,215,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45252
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,220,11,3
	.word	59128
	.byte	15,5,223,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45433
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,228,11,3
	.word	59196
	.byte	15,5,231,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45607
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,236,11,3
	.word	59264
	.byte	15,5,239,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45767
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSDCTRL4',0,5,244,11,3
	.word	59332
	.byte	15,5,247,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	45911
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRSTAT',0,5,252,11,3
	.word	59400
	.byte	15,5,255,11,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	46185
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRTRIM',0,5,132,12,3
	.word	59465
	.byte	15,5,135,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	46324
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EVRUVMON',0,5,140,12,3
	.word	59530
	.byte	15,5,143,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	46487
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_EXTCON',0,5,148,12,3
	.word	59596
	.byte	15,5,151,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	46705
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FDR',0,5,156,12,3
	.word	59660
	.byte	15,5,159,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	46868
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_FMR',0,5,164,12,3
	.word	59721
	.byte	15,5,167,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	47204
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_ID',0,5,172,12,3
	.word	59782
	.byte	15,5,175,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	47311
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IGCR',0,5,180,12,3
	.word	59842
	.byte	15,5,183,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	47763
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IN',0,5,188,12,3
	.word	59904
	.byte	15,5,191,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	47862
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_IOCR',0,5,196,12,3
	.word	59964
	.byte	15,5,199,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48012
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,204,12,3
	.word	60026
	.byte	15,5,207,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48161
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,212,12,3
	.word	60094
	.byte	15,5,215,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48322
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,220,12,3
	.word	60162
	.byte	15,5,223,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48452
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLCON',0,5,228,12,3
	.word	60230
	.byte	15,5,231,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48584
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_LCLTEST',0,5,236,12,3
	.word	60294
	.byte	15,5,239,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48699
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_MANID',0,5,244,12,3
	.word	60359
	.byte	15,5,247,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48810
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OMR',0,5,252,12,3
	.word	60422
	.byte	15,5,255,12,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	48968
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OSCCON',0,5,132,13,3
	.word	60483
	.byte	15,5,135,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	49380
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OUT',0,5,140,13,3
	.word	60547
	.byte	15,5,143,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	49481
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCCON',0,5,148,13,3
	.word	60608
	.byte	15,5,151,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	49748
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_OVCENABLE',0,5,156,13,3
	.word	60672
	.byte	15,5,159,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	49884
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDISC',0,5,164,13,3
	.word	60739
	.byte	15,5,167,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	49995
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDR',0,5,172,13,3
	.word	60802
	.byte	15,5,175,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	50128
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PDRR',0,5,180,13,3
	.word	60863
	.byte	15,5,183,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	50331
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON0',0,5,188,13,3
	.word	60925
	.byte	15,5,191,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	50687
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON1',0,5,196,13,3
	.word	60990
	.byte	15,5,199,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	50865
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLCON2',0,5,204,13,3
	.word	61055
	.byte	15,5,207,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	50965
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,212,13,3
	.word	61120
	.byte	15,5,215,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	51335
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,220,13,3
	.word	61189
	.byte	15,5,223,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	51521
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,228,13,3
	.word	61258
	.byte	15,5,231,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	51719
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PLLSTAT',0,5,236,13,3
	.word	61327
	.byte	15,5,239,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	51952
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMCSR',0,5,244,13,3
	.word	61392
	.byte	15,5,247,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	52104
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR0',0,5,252,13,3
	.word	61455
	.byte	15,5,255,13,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	52671
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR1',0,5,132,14,3
	.word	61520
	.byte	15,5,135,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	52965
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWCR2',0,5,140,14,3
	.word	61585
	.byte	15,5,143,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	53243
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTAT',0,5,148,14,3
	.word	61650
	.byte	15,5,151,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	53739
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,156,14,3
	.word	61716
	.byte	15,5,159,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	54261
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON',0,5,164,14,3
	.word	61785
	.byte	15,5,167,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	54052
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTCON2',0,5,172,14,3
	.word	61849
	.byte	15,5,175,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	54472
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_RSTSTAT',0,5,180,14,3
	.word	61914
	.byte	15,5,183,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	54904
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SAFECON',0,5,188,14,3
	.word	61979
	.byte	15,5,191,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55000
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_STSTAT',0,5,196,14,3
	.word	62044
	.byte	15,5,199,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55260
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SWRSTCON',0,5,204,14,3
	.word	62108
	.byte	15,5,207,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55385
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_SYSCON',0,5,212,14,3
	.word	62174
	.byte	15,5,215,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55582
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPCLR',0,5,220,14,3
	.word	62238
	.byte	15,5,223,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55735
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPDIS',0,5,228,14,3
	.word	62303
	.byte	15,5,231,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	55888
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSET',0,5,236,14,3
	.word	62368
	.byte	15,5,239,14,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	56041
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_TRAPSTAT',0,5,244,14,3
	.word	62433
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,252,14,3
	.word	985
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,132,15,3
	.word	1251
	.byte	33
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,140,15,3
	.word	1482
	.byte	15,5,143,15,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	56296
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON0',0,5,148,15,3
	.word	62584
	.byte	15,5,151,15,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	56422
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_CON1',0,5,156,15,3
	.word	62651
	.byte	15,5,159,15,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	56674
	.byte	4,2,35,0,0,33
	.byte	'Ifx_SCU_WDTS_SR',0,5,164,15,3
	.word	62718
	.byte	12
	.word	1522
	.byte	33
	.byte	'Ifx_SCU_WDTCPU',0,5,180,15,3
	.word	62783
	.byte	13
	.byte	'_Ifx_SCU_WDTS',0,5,183,15,25,12,11
	.byte	'CON0',0
	.word	62584
	.byte	4,2,35,0,11
	.byte	'CON1',0
	.word	62651
	.byte	4,2,35,4,11
	.byte	'SR',0
	.word	62718
	.byte	4,2,35,8,0,12
	.word	62812
	.byte	33
	.byte	'Ifx_SCU_WDTS',0,5,188,15,3
	.word	62873
	.byte	16,8
	.word	58049
	.byte	17,1,0,16,20
	.word	612
	.byte	17,19,0,16,8
	.word	61392
	.byte	17,1,0,12
	.word	62812
	.byte	16,24
	.word	1522
	.byte	17,1,0,12
	.word	62932
	.byte	16,16
	.word	57863
	.byte	17,3,0,16,16
	.word	59842
	.byte	17,3,0,16,180,3
	.word	612
	.byte	17,179,3,0,13
	.byte	'_Ifx_SCU',0,5,201,15,25,128,8,11
	.byte	'reserved_0',0
	.word	4645
	.byte	8,2,35,0,11
	.byte	'ID',0
	.word	59782
	.byte	4,2,35,8,11
	.byte	'reserved_C',0
	.word	2826
	.byte	4,2,35,12,11
	.byte	'OSCCON',0
	.word	60483
	.byte	4,2,35,16,11
	.byte	'PLLSTAT',0
	.word	61327
	.byte	4,2,35,20,11
	.byte	'PLLCON0',0
	.word	60925
	.byte	4,2,35,24,11
	.byte	'PLLCON1',0
	.word	60990
	.byte	4,2,35,28,11
	.byte	'PLLCON2',0
	.word	61055
	.byte	4,2,35,32,11
	.byte	'PLLERAYSTAT',0
	.word	61258
	.byte	4,2,35,36,11
	.byte	'PLLERAYCON0',0
	.word	61120
	.byte	4,2,35,40,11
	.byte	'PLLERAYCON1',0
	.word	61189
	.byte	4,2,35,44,11
	.byte	'CCUCON0',0
	.word	57086
	.byte	4,2,35,48,11
	.byte	'CCUCON1',0
	.word	57151
	.byte	4,2,35,52,11
	.byte	'FDR',0
	.word	59660
	.byte	4,2,35,56,11
	.byte	'EXTCON',0
	.word	59596
	.byte	4,2,35,60,11
	.byte	'CCUCON2',0
	.word	57216
	.byte	4,2,35,64,11
	.byte	'CCUCON3',0
	.word	57281
	.byte	4,2,35,68,11
	.byte	'CCUCON4',0
	.word	57346
	.byte	4,2,35,72,11
	.byte	'CCUCON5',0
	.word	57411
	.byte	4,2,35,76,11
	.byte	'RSTSTAT',0
	.word	61914
	.byte	4,2,35,80,11
	.byte	'reserved_54',0
	.word	2826
	.byte	4,2,35,84,11
	.byte	'RSTCON',0
	.word	61785
	.byte	4,2,35,88,11
	.byte	'ARSTDIS',0
	.word	57021
	.byte	4,2,35,92,11
	.byte	'SWRSTCON',0
	.word	62108
	.byte	4,2,35,96,11
	.byte	'RSTCON2',0
	.word	61849
	.byte	4,2,35,100,11
	.byte	'reserved_68',0
	.word	2826
	.byte	4,2,35,104,11
	.byte	'EVRRSTCON',0
	.word	58647
	.byte	4,2,35,108,11
	.byte	'ESRCFG',0
	.word	62900
	.byte	8,2,35,112,11
	.byte	'ESROCFG',0
	.word	58113
	.byte	4,2,35,120,11
	.byte	'SYSCON',0
	.word	62174
	.byte	4,2,35,124,11
	.byte	'CCUCON6',0
	.word	57476
	.byte	4,3,35,128,1,11
	.byte	'CCUCON7',0
	.word	57541
	.byte	4,3,35,132,1,11
	.byte	'reserved_88',0
	.word	62909
	.byte	20,3,35,136,1,11
	.byte	'PDR',0
	.word	60802
	.byte	4,3,35,156,1,11
	.byte	'IOCR',0
	.word	59964
	.byte	4,3,35,160,1,11
	.byte	'OUT',0
	.word	60547
	.byte	4,3,35,164,1,11
	.byte	'OMR',0
	.word	60422
	.byte	4,3,35,168,1,11
	.byte	'IN',0
	.word	59904
	.byte	4,3,35,172,1,11
	.byte	'EVRSTAT',0
	.word	59400
	.byte	4,3,35,176,1,11
	.byte	'EVRDVSTAT',0
	.word	58378
	.byte	4,3,35,180,1,11
	.byte	'EVR13CON',0
	.word	58178
	.byte	4,3,35,184,1,11
	.byte	'EVR33CON',0
	.word	58244
	.byte	4,3,35,188,1,11
	.byte	'STSTAT',0
	.word	62044
	.byte	4,3,35,192,1,11
	.byte	'reserved_C4',0
	.word	2826
	.byte	4,3,35,196,1,11
	.byte	'PMSWCR0',0
	.word	61455
	.byte	4,3,35,200,1,11
	.byte	'PMSWSTAT',0
	.word	61650
	.byte	4,3,35,204,1,11
	.byte	'PMSWSTATCLR',0
	.word	61716
	.byte	4,3,35,208,1,11
	.byte	'PMCSR',0
	.word	62918
	.byte	8,3,35,212,1,11
	.byte	'reserved_DC',0
	.word	2826
	.byte	4,3,35,220,1,11
	.byte	'DTSSTAT',0
	.word	57798
	.byte	4,3,35,224,1,11
	.byte	'DTSCON',0
	.word	57670
	.byte	4,3,35,228,1,11
	.byte	'PMSWCR1',0
	.word	61520
	.byte	4,3,35,232,1,11
	.byte	'PMSWCR2',0
	.word	61585
	.byte	4,3,35,236,1,11
	.byte	'WDTS',0
	.word	62927
	.byte	12,3,35,240,1,11
	.byte	'EMSR',0
	.word	57987
	.byte	4,3,35,252,1,11
	.byte	'WDTCPU',0
	.word	62941
	.byte	24,3,35,128,2,11
	.byte	'reserved_118',0
	.word	4985
	.byte	12,3,35,152,2,11
	.byte	'TRAPSTAT',0
	.word	62433
	.byte	4,3,35,164,2,11
	.byte	'TRAPSET',0
	.word	62368
	.byte	4,3,35,168,2,11
	.byte	'TRAPCLR',0
	.word	62238
	.byte	4,3,35,172,2,11
	.byte	'TRAPDIS',0
	.word	62303
	.byte	4,3,35,176,2,11
	.byte	'reserved_134',0
	.word	2826
	.byte	4,3,35,180,2,11
	.byte	'LCLCON1',0
	.word	60230
	.byte	4,3,35,184,2,11
	.byte	'LCLTEST',0
	.word	60294
	.byte	4,3,35,188,2,11
	.byte	'CHIPID',0
	.word	57606
	.byte	4,3,35,192,2,11
	.byte	'MANID',0
	.word	60359
	.byte	4,3,35,196,2,11
	.byte	'reserved_148',0
	.word	4645
	.byte	8,3,35,200,2,11
	.byte	'SAFECON',0
	.word	61979
	.byte	4,3,35,208,2,11
	.byte	'reserved_154',0
	.word	30498
	.byte	16,3,35,212,2,11
	.byte	'LBISTCTRL0',0
	.word	60026
	.byte	4,3,35,228,2,11
	.byte	'LBISTCTRL1',0
	.word	60094
	.byte	4,3,35,232,2,11
	.byte	'LBISTCTRL2',0
	.word	60162
	.byte	4,3,35,236,2,11
	.byte	'reserved_170',0
	.word	30478
	.byte	28,3,35,240,2,11
	.byte	'PDISC',0
	.word	60739
	.byte	4,3,35,140,3,11
	.byte	'reserved_190',0
	.word	4645
	.byte	8,3,35,144,3,11
	.byte	'EVRTRIM',0
	.word	59465
	.byte	4,3,35,152,3,11
	.byte	'EVRADCSTAT',0
	.word	58310
	.byte	4,3,35,156,3,11
	.byte	'EVRUVMON',0
	.word	59530
	.byte	4,3,35,160,3,11
	.byte	'EVROVMON',0
	.word	58581
	.byte	4,3,35,164,3,11
	.byte	'EVRMONCTRL',0
	.word	58445
	.byte	4,3,35,168,3,11
	.byte	'reserved_1AC',0
	.word	2826
	.byte	4,3,35,172,3,11
	.byte	'EVRSDCTRL1',0
	.word	59128
	.byte	4,3,35,176,3,11
	.byte	'EVRSDCTRL2',0
	.word	59196
	.byte	4,3,35,180,3,11
	.byte	'EVRSDCTRL3',0
	.word	59264
	.byte	4,3,35,184,3,11
	.byte	'EVRSDCTRL4',0
	.word	59332
	.byte	4,3,35,188,3,11
	.byte	'EVRSDCOEFF1',0
	.word	58714
	.byte	4,3,35,192,3,11
	.byte	'EVRSDCOEFF2',0
	.word	58783
	.byte	4,3,35,196,3,11
	.byte	'EVRSDCOEFF3',0
	.word	58852
	.byte	4,3,35,200,3,11
	.byte	'EVRSDCOEFF4',0
	.word	58921
	.byte	4,3,35,204,3,11
	.byte	'EVRSDCOEFF5',0
	.word	58990
	.byte	4,3,35,208,3,11
	.byte	'EVRSDCOEFF6',0
	.word	59059
	.byte	4,3,35,212,3,11
	.byte	'EVROSCCTRL',0
	.word	58513
	.byte	4,3,35,216,3,11
	.byte	'reserved_1DC',0
	.word	2826
	.byte	4,3,35,220,3,11
	.byte	'OVCENABLE',0
	.word	60672
	.byte	4,3,35,224,3,11
	.byte	'OVCCON',0
	.word	60608
	.byte	4,3,35,228,3,11
	.byte	'reserved_1E8',0
	.word	33351
	.byte	40,3,35,232,3,11
	.byte	'EICR',0
	.word	62946
	.byte	16,3,35,144,4,11
	.byte	'EIFR',0
	.word	57925
	.byte	4,3,35,160,4,11
	.byte	'FMR',0
	.word	59721
	.byte	4,3,35,164,4,11
	.byte	'PDRR',0
	.word	60863
	.byte	4,3,35,168,4,11
	.byte	'IGCR',0
	.word	62955
	.byte	16,3,35,172,4,11
	.byte	'reserved_23C',0
	.word	2826
	.byte	4,3,35,188,4,11
	.byte	'DTSLIM',0
	.word	57734
	.byte	4,3,35,192,4,11
	.byte	'reserved_244',0
	.word	62964
	.byte	180,3,3,35,196,4,11
	.byte	'ACCEN1',0
	.word	56957
	.byte	4,3,35,248,7,11
	.byte	'ACCEN0',0
	.word	56893
	.byte	4,3,35,252,7,0,12
	.word	62975
	.byte	33
	.byte	'Ifx_SCU',0,5,181,16,3
	.word	64965
	.byte	33
	.byte	'Ifx_STM_ACCEN0_Bits',0,14,79,3
	.word	13170
	.byte	33
	.byte	'Ifx_STM_ACCEN1_Bits',0,14,85,3
	.word	13081
	.byte	33
	.byte	'Ifx_STM_CAP_Bits',0,14,91,3
	.word	11611
	.byte	33
	.byte	'Ifx_STM_CAPSV_Bits',0,14,97,3
	.word	12488
	.byte	33
	.byte	'Ifx_STM_CLC_Bits',0,14,107,3
	.word	10734
	.byte	33
	.byte	'Ifx_STM_CMCON_Bits',0,14,120,3
	.word	11789
	.byte	33
	.byte	'Ifx_STM_CMP_Bits',0,14,126,3
	.word	11698
	.byte	33
	.byte	'Ifx_STM_ICR_Bits',0,14,139,1,3
	.word	12020
	.byte	33
	.byte	'Ifx_STM_ID_Bits',0,14,147,1,3
	.word	10890
	.byte	33
	.byte	'Ifx_STM_ISCR_Bits',0,14,157,1,3
	.word	12237
	.byte	33
	.byte	'Ifx_STM_KRST0_Bits',0,14,165,1,3
	.word	12958
	.byte	33
	.byte	'Ifx_STM_KRST1_Bits',0,14,172,1,3
	.word	12854
	.byte	33
	.byte	'Ifx_STM_KRSTCLR_Bits',0,14,179,1,3
	.word	12748
	.byte	33
	.byte	'Ifx_STM_OCS_Bits',0,14,189,1,3
	.word	12588
	.byte	33
	.byte	'Ifx_STM_TIM0_Bits',0,14,195,1,3
	.word	11012
	.byte	33
	.byte	'Ifx_STM_TIM0SV_Bits',0,14,201,1,3
	.word	12401
	.byte	33
	.byte	'Ifx_STM_TIM1_Bits',0,14,207,1,3
	.word	11097
	.byte	33
	.byte	'Ifx_STM_TIM2_Bits',0,14,213,1,3
	.word	11182
	.byte	33
	.byte	'Ifx_STM_TIM3_Bits',0,14,219,1,3
	.word	11267
	.byte	33
	.byte	'Ifx_STM_TIM4_Bits',0,14,225,1,3
	.word	11353
	.byte	33
	.byte	'Ifx_STM_TIM5_Bits',0,14,231,1,3
	.word	11439
	.byte	33
	.byte	'Ifx_STM_TIM6_Bits',0,14,237,1,3
	.word	11525
	.byte	33
	.byte	'Ifx_STM_ACCEN0',0,14,250,1,3
	.word	13699
	.byte	33
	.byte	'Ifx_STM_ACCEN1',0,14,130,2,3
	.word	13130
	.byte	33
	.byte	'Ifx_STM_CAP',0,14,138,2,3
	.word	11658
	.byte	33
	.byte	'Ifx_STM_CAPSV',0,14,146,2,3
	.word	12537
	.byte	33
	.byte	'Ifx_STM_CLC',0,14,154,2,3
	.word	10850
	.byte	33
	.byte	'Ifx_STM_CMCON',0,14,162,2,3
	.word	11980
	.byte	33
	.byte	'Ifx_STM_CMP',0,14,170,2,3
	.word	11740
	.byte	33
	.byte	'Ifx_STM_ICR',0,14,178,2,3
	.word	12197
	.byte	33
	.byte	'Ifx_STM_ID',0,14,186,2,3
	.word	10972
	.byte	33
	.byte	'Ifx_STM_ISCR',0,14,194,2,3
	.word	12361
	.byte	33
	.byte	'Ifx_STM_KRST0',0,14,202,2,3
	.word	13041
	.byte	33
	.byte	'Ifx_STM_KRST1',0,14,210,2,3
	.word	12918
	.byte	33
	.byte	'Ifx_STM_KRSTCLR',0,14,218,2,3
	.word	12814
	.byte	33
	.byte	'Ifx_STM_OCS',0,14,226,2,3
	.word	12708
	.byte	33
	.byte	'Ifx_STM_TIM0',0,14,234,2,3
	.word	11057
	.byte	33
	.byte	'Ifx_STM_TIM0SV',0,14,242,2,3
	.word	12448
	.byte	33
	.byte	'Ifx_STM_TIM1',0,14,250,2,3
	.word	11142
	.byte	33
	.byte	'Ifx_STM_TIM2',0,14,130,3,3
	.word	11227
	.byte	33
	.byte	'Ifx_STM_TIM3',0,14,138,3,3
	.word	11313
	.byte	33
	.byte	'Ifx_STM_TIM4',0,14,146,3,3
	.word	11399
	.byte	33
	.byte	'Ifx_STM_TIM5',0,14,154,3,3
	.word	11485
	.byte	33
	.byte	'Ifx_STM_TIM6',0,14,162,3,3
	.word	11571
	.byte	12
	.word	13739
	.byte	33
	.byte	'Ifx_STM',0,14,201,3,3
	.word	66070
	.byte	18,23,236,10,9,1,19
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,19
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,33
	.byte	'IfxScu_CCUCON0_CLKSEL',0,23,240,10,3
	.word	66092
	.byte	18,23,250,10,9,1,19
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,19
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,19
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,33
	.byte	'IfxScu_WDTCON1_IR',0,23,255,10,3
	.word	66189
	.byte	13
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,24,45,16,4,14
	.byte	'EN0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'EN1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EN2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'EN3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'EN4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'EN5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'EN6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'EN7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'EN8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'EN9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'EN10',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'EN11',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'EN12',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'EN13',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'EN14',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'EN15',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'EN16',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'EN17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'EN18',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'EN19',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'EN20',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'EN21',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'EN22',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'EN23',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'EN24',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'EN25',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'EN26',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'EN27',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'EN28',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'EN29',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'EN30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'EN31',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,24,79,3
	.word	66311
	.byte	13
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,24,82,16,4,14
	.byte	'reserved_0',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,24,85,3
	.word	66872
	.byte	13
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,24,88,16,4,14
	.byte	'SEL',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'CLR',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'DIS',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'reserved_10',0,4
	.word	850
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,24,95,3
	.word	66953
	.byte	13
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,24,98,16,4,14
	.byte	'VLD0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'VLD1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'VLD2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'VLD3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'VLD4',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'VLD5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'VLD6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'VLD7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'VLD8',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'VLD9',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'reserved_10',0,4
	.word	850
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,24,111,3
	.word	67106
	.byte	13
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,24,114,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'ADDR',0,4
	.word	850
	.byte	19,8,2,35,0,14
	.byte	'ERR',0,1
	.word	612
	.byte	6,2,2,35,3,14
	.byte	'VLD',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'CLR',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,24,121,3
	.word	67354
	.byte	13
	.byte	'_Ifx_FLASH_COMM0_Bits',0,24,124,16,4,14
	.byte	'STATUS',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'reserved_8',0,4
	.word	850
	.byte	24,0,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0_Bits',0,24,128,1,3
	.word	67500
	.byte	13
	.byte	'_Ifx_FLASH_COMM1_Bits',0,24,131,1,16,4,14
	.byte	'STATUS',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'DATA',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM1_Bits',0,24,136,1,3
	.word	67598
	.byte	13
	.byte	'_Ifx_FLASH_COMM2_Bits',0,24,139,1,16,4,14
	.byte	'STATUS',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'DATA',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_COMM2_Bits',0,24,144,1,3
	.word	67714
	.byte	13
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,24,147,1,16,4,14
	.byte	'RCODE',0,4
	.word	850
	.byte	22,10,2,35,0,14
	.byte	'reserved_22',0,2
	.word	1025
	.byte	8,2,2,35,2,14
	.byte	'EDCERRINJ',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'ECCORDIS',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRD_Bits',0,24,153,1,3
	.word	67830
	.byte	13
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,24,156,1,16,4,14
	.byte	'RCODE',0,4
	.word	850
	.byte	22,10,2,35,0,14
	.byte	'reserved_22',0,2
	.word	1025
	.byte	8,2,2,35,2,14
	.byte	'EDCERRINJ',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'ECCORDIS',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCRP_Bits',0,24,162,1,3
	.word	67970
	.byte	13
	.byte	'_Ifx_FLASH_ECCW_Bits',0,24,165,1,16,4,14
	.byte	'WCODE',0,4
	.word	850
	.byte	22,10,2,35,0,14
	.byte	'reserved_22',0,2
	.word	1025
	.byte	8,2,2,35,2,14
	.byte	'DECENCDIS',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'PECENCDIS',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_ECCW_Bits',0,24,171,1,3
	.word	68110
	.byte	13
	.byte	'_Ifx_FLASH_FCON_Bits',0,24,174,1,16,4,14
	.byte	'WSPFLASH',0,1
	.word	612
	.byte	4,4,2,35,0,14
	.byte	'WSECPF',0,1
	.word	612
	.byte	2,2,2,35,0,14
	.byte	'WSDFLASH',0,2
	.word	1025
	.byte	6,4,2,35,0,14
	.byte	'WSECDF',0,1
	.word	612
	.byte	3,1,2,35,1,14
	.byte	'IDLE',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'ESLDIS',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'SLEEP',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'NSAFECC',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'STALL',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'RES21',0,1
	.word	612
	.byte	2,2,2,35,2,14
	.byte	'RES23',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'VOPERM',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'SQERM',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'PROERM',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'reserved_27',0,1
	.word	612
	.byte	3,2,2,35,3,14
	.byte	'PR5V',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'EOBM',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FCON_Bits',0,24,193,1,3
	.word	68249
	.byte	13
	.byte	'_Ifx_FLASH_FPRO_Bits',0,24,196,1,16,4,14
	.byte	'PROINP',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'PRODISP',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'PROIND',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'PRODISD',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'PROINHSMCOTP',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'RES5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'PROINOTP',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'RES7',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'PROINDBG',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PRODISDBG',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'PROINHSM',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'reserved_11',0,1
	.word	612
	.byte	5,0,2,35,1,14
	.byte	'DCFP',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'DDFP',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'DDFPX',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'reserved_19',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'DDFD',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'reserved_21',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'ENPE',0,1
	.word	612
	.byte	2,0,2,35,2,14
	.byte	'reserved_24',0,1
	.word	612
	.byte	8,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FPRO_Bits',0,24,218,1,3
	.word	68611
	.byte	13
	.byte	'_Ifx_FLASH_FSR_Bits',0,24,221,1,16,4,14
	.byte	'FABUSY',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'D0BUSY',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'RES1',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'P0BUSY',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'P1BUSY',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'RES5',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'RES6',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'PROG',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'ERASE',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'PFPAGE',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'DFPAGE',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'OPER',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'SQER',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'PROER',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'PFSBER',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'PFDBER',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'PFMBER',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'RES17',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'DFSBER',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'DFDBER',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'DFTBER',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'DFMBER',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'SRIADDERR',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'reserved_23',0,2
	.word	1025
	.byte	2,7,2,35,2,14
	.byte	'PVER',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'EVER',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'SPND',0,1
	.word	612
	.byte	1,4,2,35,3,14
	.byte	'SLM',0,1
	.word	612
	.byte	1,3,2,35,3,14
	.byte	'reserved_29',0,1
	.word	612
	.byte	1,2,2,35,3,14
	.byte	'ORIER',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'reserved_31',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_FSR_Bits',0,24,254,1,3
	.word	69052
	.byte	13
	.byte	'_Ifx_FLASH_ID_Bits',0,24,129,2,16,4,14
	.byte	'MODREV',0,1
	.word	612
	.byte	8,0,2,35,0,14
	.byte	'MODTYPE',0,1
	.word	612
	.byte	8,0,2,35,1,14
	.byte	'MODNUMBER',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_ID_Bits',0,24,134,2,3
	.word	69658
	.byte	13
	.byte	'_Ifx_FLASH_MARD_Bits',0,24,137,2,16,4,14
	.byte	'HMARGIN',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SELD0',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'reserved_2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'SPND',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'SPNDERR',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'reserved_5',0,2
	.word	1025
	.byte	10,1,2,35,0,14
	.byte	'TRAPDIS',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARD_Bits',0,24,147,2,3
	.word	69769
	.byte	13
	.byte	'_Ifx_FLASH_MARP_Bits',0,24,150,2,16,4,14
	.byte	'SELP0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SELP1',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'RES2',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'RES3',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'reserved_4',0,2
	.word	1025
	.byte	11,1,2,35,0,14
	.byte	'TRAPDIS',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_MARP_Bits',0,24,159,2,3
	.word	69983
	.byte	13
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,24,162,2,16,4,14
	.byte	'L',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'NSAFECC',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'RAMIN',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'RAMINSEL',0,1
	.word	612
	.byte	4,0,2,35,0,14
	.byte	'OSCCFG',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'MODE',0,1
	.word	612
	.byte	2,5,2,35,1,14
	.byte	'APREN',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'CAP0EN',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'CAP1EN',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'CAP2EN',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'CAP3EN',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'ESR0CNT',0,2
	.word	1025
	.byte	12,4,2,35,2,14
	.byte	'RES29',0,1
	.word	612
	.byte	2,2,2,35,3,14
	.byte	'RES30',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'RPRO',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCOND_Bits',0,24,179,2,3
	.word	70170
	.byte	13
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,24,182,2,16,4,14
	.byte	'OCDSDIS',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'DBGIFLCK',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'EDM',0,1
	.word	612
	.byte	2,4,2,35,0,14
	.byte	'reserved_4',0,4
	.word	850
	.byte	28,0,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,24,188,2,3
	.word	70494
	.byte	13
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,24,191,2,16,4,14
	.byte	'HSMDBGDIS',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'DBGIFLCK',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'TSTIFLCK',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'HSMTSTDIS',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'RES15',0,2
	.word	1025
	.byte	12,0,2,35,0,14
	.byte	'reserved_16',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,24,199,2,3
	.word	70637
	.byte	13
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,24,202,2,16,4,14
	.byte	'HSMBOOTEN',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'SSWWAIT',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'HSMDX',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'HSM6X',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'HSM16X',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'HSM17X',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'S6ROM',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'HSMENPINS',0,2
	.word	1025
	.byte	2,7,2,35,0,14
	.byte	'HSMENRES',0,1
	.word	612
	.byte	2,5,2,35,1,14
	.byte	'DESTDBG',0,1
	.word	612
	.byte	2,3,2,35,1,14
	.byte	'BLKFLAN',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'reserved_14',0,1
	.word	612
	.byte	2,0,2,35,1,14
	.byte	'S16ROM',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'S17ROM',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'reserved_18',0,2
	.word	1025
	.byte	14,0,2,35,2,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,24,219,2,3
	.word	70826
	.byte	13
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,24,222,2,16,4,14
	.byte	'S0ROM',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'S1ROM',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'S2ROM',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'S3ROM',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'S4ROM',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'S5ROM',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'S6ROM',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'S7ROM',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'S8ROM',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'S9ROM',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'S10ROM',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'S11ROM',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'S12ROM',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'S13ROM',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'S14ROM',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'S15ROM',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'S16ROM',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'S17ROM',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'S18ROM',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'S19ROM',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'S20ROM',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'S21ROM',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'S22ROM',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'S23ROM',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'S24ROM',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'S25ROM',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'S26ROM',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'reserved_27',0,1
	.word	612
	.byte	2,3,2,35,3,14
	.byte	'BML',0,1
	.word	612
	.byte	2,1,2,35,3,14
	.byte	'TP',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,24,254,2,3
	.word	71189
	.byte	13
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,24,129,3,16,4,14
	.byte	'S0L',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'S1L',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'S2L',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'S3L',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'S4L',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'S5L',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'S6L',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'S7L',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'S8L',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'S9L',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'S10L',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'S11L',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'S12L',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'S13L',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'S14L',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'S15L',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'S16L',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'S17L',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'S18L',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'S19L',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'S20L',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'S21L',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'S22L',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'S23L',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'S24L',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'S25L',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'S26L',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'reserved_27',0,1
	.word	612
	.byte	4,1,2,35,3,14
	.byte	'RPRO',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONP_Bits',0,24,160,3,3
	.word	71784
	.byte	13
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,24,163,3,16,4,14
	.byte	'S0WOP',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'S1WOP',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'S2WOP',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'S3WOP',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'S4WOP',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'S5WOP',0,1
	.word	612
	.byte	1,2,2,35,0,14
	.byte	'S6WOP',0,1
	.word	612
	.byte	1,1,2,35,0,14
	.byte	'S7WOP',0,1
	.word	612
	.byte	1,0,2,35,0,14
	.byte	'S8WOP',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'S9WOP',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'S10WOP',0,1
	.word	612
	.byte	1,5,2,35,1,14
	.byte	'S11WOP',0,1
	.word	612
	.byte	1,4,2,35,1,14
	.byte	'S12WOP',0,1
	.word	612
	.byte	1,3,2,35,1,14
	.byte	'S13WOP',0,1
	.word	612
	.byte	1,2,2,35,1,14
	.byte	'S14WOP',0,1
	.word	612
	.byte	1,1,2,35,1,14
	.byte	'S15WOP',0,1
	.word	612
	.byte	1,0,2,35,1,14
	.byte	'S16WOP',0,1
	.word	612
	.byte	1,7,2,35,2,14
	.byte	'S17WOP',0,1
	.word	612
	.byte	1,6,2,35,2,14
	.byte	'S18WOP',0,1
	.word	612
	.byte	1,5,2,35,2,14
	.byte	'S19WOP',0,1
	.word	612
	.byte	1,4,2,35,2,14
	.byte	'S20WOP',0,1
	.word	612
	.byte	1,3,2,35,2,14
	.byte	'S21WOP',0,1
	.word	612
	.byte	1,2,2,35,2,14
	.byte	'S22WOP',0,1
	.word	612
	.byte	1,1,2,35,2,14
	.byte	'S23WOP',0,1
	.word	612
	.byte	1,0,2,35,2,14
	.byte	'S24WOP',0,1
	.word	612
	.byte	1,7,2,35,3,14
	.byte	'S25WOP',0,1
	.word	612
	.byte	1,6,2,35,3,14
	.byte	'S26WOP',0,1
	.word	612
	.byte	1,5,2,35,3,14
	.byte	'reserved_27',0,1
	.word	612
	.byte	4,1,2,35,3,14
	.byte	'DATM',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,24,194,3,3
	.word	72308
	.byte	13
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,24,197,3,16,4,14
	.byte	'TAG',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,24,201,3,3
	.word	72890
	.byte	13
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,24,204,3,16,4,14
	.byte	'TAG',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,24,208,3,3
	.word	72992
	.byte	13
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,24,211,3,16,4,14
	.byte	'TAG',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,4
	.word	850
	.byte	26,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,24,215,3,3
	.word	73094
	.byte	13
	.byte	'_Ifx_FLASH_RRAD_Bits',0,24,218,3,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	3,5,2,35,0,14
	.byte	'ADD',0,4
	.word	850
	.byte	29,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD_Bits',0,24,222,3,3
	.word	73196
	.byte	13
	.byte	'_Ifx_FLASH_RRCT_Bits',0,24,225,3,16,4,14
	.byte	'STRT',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'STP',0,1
	.word	612
	.byte	1,6,2,35,0,14
	.byte	'BUSY',0,1
	.word	612
	.byte	1,5,2,35,0,14
	.byte	'DONE',0,1
	.word	612
	.byte	1,4,2,35,0,14
	.byte	'ERR',0,1
	.word	612
	.byte	1,3,2,35,0,14
	.byte	'reserved_5',0,1
	.word	612
	.byte	3,0,2,35,0,14
	.byte	'EOBM',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'reserved_9',0,1
	.word	612
	.byte	7,0,2,35,1,14
	.byte	'CNT',0,2
	.word	1025
	.byte	16,0,2,35,2,0,33
	.byte	'Ifx_FLASH_RRCT_Bits',0,24,236,3,3
	.word	73290
	.byte	13
	.byte	'_Ifx_FLASH_RRD0_Bits',0,24,239,3,16,4,14
	.byte	'DATA',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0_Bits',0,24,242,3,3
	.word	73500
	.byte	13
	.byte	'_Ifx_FLASH_RRD1_Bits',0,24,245,3,16,4,14
	.byte	'DATA',0,4
	.word	850
	.byte	32,0,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1_Bits',0,24,248,3,3
	.word	73573
	.byte	13
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,24,251,3,16,4,14
	.byte	'SEL',0,1
	.word	612
	.byte	6,2,2,35,0,14
	.byte	'reserved_6',0,1
	.word	612
	.byte	2,0,2,35,0,14
	.byte	'CLR',0,1
	.word	612
	.byte	1,7,2,35,1,14
	.byte	'DIS',0,1
	.word	612
	.byte	1,6,2,35,1,14
	.byte	'reserved_10',0,4
	.word	850
	.byte	22,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,24,130,4,3
	.word	73646
	.byte	13
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,24,133,4,16,4,14
	.byte	'VLD0',0,1
	.word	612
	.byte	1,7,2,35,0,14
	.byte	'reserved_1',0,4
	.word	850
	.byte	31,0,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,24,137,4,3
	.word	73801
	.byte	13
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,24,140,4,16,4,14
	.byte	'reserved_0',0,1
	.word	612
	.byte	5,3,2,35,0,14
	.byte	'ADDR',0,4
	.word	850
	.byte	19,8,2,35,0,14
	.byte	'ERR',0,1
	.word	612
	.byte	6,2,2,35,3,14
	.byte	'VLD',0,1
	.word	612
	.byte	1,1,2,35,3,14
	.byte	'CLR',0,1
	.word	612
	.byte	1,0,2,35,3,0,33
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,24,147,4,3
	.word	73906
	.byte	15,24,155,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	66311
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN0',0,24,160,4,3
	.word	74054
	.byte	15,24,163,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	66872
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ACCEN1',0,24,168,4,3
	.word	74120
	.byte	15,24,171,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	66953
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_CFG',0,24,176,4,3
	.word	74186
	.byte	15,24,179,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67106
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_STAT',0,24,184,4,3
	.word	74254
	.byte	15,24,187,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67354
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_CBAB_TOP',0,24,192,4,3
	.word	74323
	.byte	15,24,195,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67500
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM0',0,24,200,4,3
	.word	74391
	.byte	15,24,203,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67598
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM1',0,24,208,4,3
	.word	74456
	.byte	15,24,211,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67714
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_COMM2',0,24,216,4,3
	.word	74521
	.byte	15,24,219,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67830
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRD',0,24,224,4,3
	.word	74586
	.byte	15,24,227,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	67970
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCRP',0,24,232,4,3
	.word	74651
	.byte	15,24,235,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	68110
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ECCW',0,24,240,4,3
	.word	74716
	.byte	15,24,243,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	68249
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FCON',0,24,248,4,3
	.word	74780
	.byte	15,24,251,4,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	68611
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FPRO',0,24,128,5,3
	.word	74844
	.byte	15,24,131,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	69052
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_FSR',0,24,136,5,3
	.word	74908
	.byte	15,24,139,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	69658
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_ID',0,24,144,5,3
	.word	74971
	.byte	15,24,147,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	69769
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARD',0,24,152,5,3
	.word	75033
	.byte	15,24,155,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	69983
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_MARP',0,24,160,5,3
	.word	75097
	.byte	15,24,163,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	70170
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCOND',0,24,168,5,3
	.word	75161
	.byte	15,24,171,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	70494
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONDBG',0,24,176,5,3
	.word	75228
	.byte	15,24,179,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	70637
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSM',0,24,184,5,3
	.word	75297
	.byte	15,24,187,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	70826
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,24,192,5,3
	.word	75366
	.byte	15,24,195,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	71189
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONOTP',0,24,200,5,3
	.word	75439
	.byte	15,24,203,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	71784
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONP',0,24,208,5,3
	.word	75508
	.byte	15,24,211,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	72308
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_PROCONWOP',0,24,216,5,3
	.word	75575
	.byte	15,24,219,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	72890
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG0',0,24,224,5,3
	.word	75644
	.byte	15,24,227,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	72992
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG1',0,24,232,5,3
	.word	75712
	.byte	15,24,235,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73094
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RDB_CFG2',0,24,240,5,3
	.word	75780
	.byte	15,24,243,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73196
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRAD',0,24,248,5,3
	.word	75848
	.byte	15,24,251,5,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73290
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRCT',0,24,128,6,3
	.word	75912
	.byte	15,24,131,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73500
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD0',0,24,136,6,3
	.word	75976
	.byte	15,24,139,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73573
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_RRD1',0,24,144,6,3
	.word	76040
	.byte	15,24,147,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73646
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_CFG',0,24,152,6,3
	.word	76104
	.byte	15,24,155,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73801
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_STAT',0,24,160,6,3
	.word	76172
	.byte	15,24,163,6,9,4,11
	.byte	'U',0
	.word	850
	.byte	4,2,35,0,11
	.byte	'I',0
	.word	866
	.byte	4,2,35,0,11
	.byte	'B',0
	.word	73906
	.byte	4,2,35,0,0,33
	.byte	'Ifx_FLASH_UBAB_TOP',0,24,168,6,3
	.word	76241
	.byte	13
	.byte	'_Ifx_FLASH_CBAB',0,24,179,6,25,12,11
	.byte	'CFG',0
	.word	74186
	.byte	4,2,35,0,11
	.byte	'STAT',0
	.word	74254
	.byte	4,2,35,4,11
	.byte	'TOP',0
	.word	74323
	.byte	4,2,35,8,0,12
	.word	76309
	.byte	33
	.byte	'Ifx_FLASH_CBAB',0,24,184,6,3
	.word	76372
	.byte	13
	.byte	'_Ifx_FLASH_RDB',0,24,187,6,25,12,11
	.byte	'CFG0',0
	.word	75644
	.byte	4,2,35,0,11
	.byte	'CFG1',0
	.word	75712
	.byte	4,2,35,4,11
	.byte	'CFG2',0
	.word	75780
	.byte	4,2,35,8,0,12
	.word	76401
	.byte	33
	.byte	'Ifx_FLASH_RDB',0,24,192,6,3
	.word	76465
	.byte	13
	.byte	'_Ifx_FLASH_UBAB',0,24,195,6,25,12,11
	.byte	'CFG',0
	.word	76104
	.byte	4,2,35,0,11
	.byte	'STAT',0
	.word	76172
	.byte	4,2,35,4,11
	.byte	'TOP',0
	.word	76241
	.byte	4,2,35,8,0,12
	.word	76493
	.byte	33
	.byte	'Ifx_FLASH_UBAB',0,24,200,6,3
	.word	76556
	.byte	33
	.byte	'Ifx_P_ACCEN0_Bits',0,7,79,3
	.word	8398
	.byte	33
	.byte	'Ifx_P_ACCEN1_Bits',0,7,85,3
	.word	8311
	.byte	33
	.byte	'Ifx_P_ESR_Bits',0,7,107,3
	.word	4654
	.byte	33
	.byte	'Ifx_P_ID_Bits',0,7,115,3
	.word	2707
	.byte	33
	.byte	'Ifx_P_IN_Bits',0,7,137,1,3
	.word	3702
	.byte	33
	.byte	'Ifx_P_IOCR0_Bits',0,7,150,1,3
	.word	2835
	.byte	33
	.byte	'Ifx_P_IOCR12_Bits',0,7,163,1,3
	.word	3482
	.byte	33
	.byte	'Ifx_P_IOCR4_Bits',0,7,176,1,3
	.word	3050
	.byte	33
	.byte	'Ifx_P_IOCR8_Bits',0,7,189,1,3
	.word	3265
	.byte	33
	.byte	'Ifx_P_LPCR0_Bits',0,7,197,1,3
	.word	7670
	.byte	33
	.byte	'Ifx_P_LPCR1_Bits',0,7,205,1,3
	.word	7794
	.byte	33
	.byte	'Ifx_P_LPCR1_P21_Bits',0,7,215,1,3
	.word	7878
	.byte	33
	.byte	'Ifx_P_LPCR2_Bits',0,7,229,1,3
	.word	8058
	.byte	33
	.byte	'Ifx_P_OMCR0_Bits',0,7,240,1,3
	.word	6309
	.byte	33
	.byte	'Ifx_P_OMCR12_Bits',0,7,250,1,3
	.word	6833
	.byte	33
	.byte	'Ifx_P_OMCR4_Bits',0,7,133,2,3
	.word	6483
	.byte	33
	.byte	'Ifx_P_OMCR8_Bits',0,7,144,2,3
	.word	6657
	.byte	33
	.byte	'Ifx_P_OMCR_Bits',0,7,166,2,3
	.word	7322
	.byte	33
	.byte	'Ifx_P_OMR_Bits',0,7,203,2,3
	.word	2136
	.byte	33
	.byte	'Ifx_P_OMSR0_Bits',0,7,213,2,3
	.word	5646
	.byte	33
	.byte	'Ifx_P_OMSR12_Bits',0,7,224,2,3
	.word	6134
	.byte	33
	.byte	'Ifx_P_OMSR4_Bits',0,7,235,2,3
	.word	5793
	.byte	33
	.byte	'Ifx_P_OMSR8_Bits',0,7,246,2,3
	.word	5962
	.byte	33
	.byte	'Ifx_P_OMSR_Bits',0,7,140,3,3
	.word	6989
	.byte	33
	.byte	'Ifx_P_OUT_Bits',0,7,162,3,3
	.word	1820
	.byte	33
	.byte	'Ifx_P_PCSR_Bits',0,7,180,3,3
	.word	5360
	.byte	33
	.byte	'Ifx_P_PDISC_Bits',0,7,202,3,3
	.word	4994
	.byte	33
	.byte	'Ifx_P_PDR0_Bits',0,7,223,3,3
	.word	4025
	.byte	33
	.byte	'Ifx_P_PDR1_Bits',0,7,244,3,3
	.word	4329
	.byte	33
	.byte	'Ifx_P_ACCEN0',0,7,129,4,3
	.word	8925
	.byte	33
	.byte	'Ifx_P_ACCEN1',0,7,137,4,3
	.word	8358
	.byte	33
	.byte	'Ifx_P_ESR',0,7,145,4,3
	.word	4945
	.byte	33
	.byte	'Ifx_P_ID',0,7,153,4,3
	.word	2786
	.byte	33
	.byte	'Ifx_P_IN',0,7,161,4,3
	.word	3976
	.byte	33
	.byte	'Ifx_P_IOCR0',0,7,169,4,3
	.word	3010
	.byte	33
	.byte	'Ifx_P_IOCR12',0,7,177,4,3
	.word	3662
	.byte	33
	.byte	'Ifx_P_IOCR4',0,7,185,4,3
	.word	3225
	.byte	33
	.byte	'Ifx_P_IOCR8',0,7,193,4,3
	.word	3442
	.byte	33
	.byte	'Ifx_P_LPCR0',0,7,201,4,3
	.word	7754
	.byte	33
	.byte	'Ifx_P_LPCR1',0,7,210,4,3
	.word	8003
	.byte	33
	.byte	'Ifx_P_LPCR2',0,7,218,4,3
	.word	8262
	.byte	33
	.byte	'Ifx_P_OMCR',0,7,226,4,3
	.word	7630
	.byte	33
	.byte	'Ifx_P_OMCR0',0,7,234,4,3
	.word	6443
	.byte	33
	.byte	'Ifx_P_OMCR12',0,7,242,4,3
	.word	6949
	.byte	33
	.byte	'Ifx_P_OMCR4',0,7,250,4,3
	.word	6617
	.byte	33
	.byte	'Ifx_P_OMCR8',0,7,130,5,3
	.word	6793
	.byte	33
	.byte	'Ifx_P_OMR',0,7,138,5,3
	.word	2667
	.byte	33
	.byte	'Ifx_P_OMSR',0,7,146,5,3
	.word	7282
	.byte	33
	.byte	'Ifx_P_OMSR0',0,7,154,5,3
	.word	5753
	.byte	33
	.byte	'Ifx_P_OMSR12',0,7,162,5,3
	.word	6269
	.byte	33
	.byte	'Ifx_P_OMSR4',0,7,170,5,3
	.word	5922
	.byte	33
	.byte	'Ifx_P_OMSR8',0,7,178,5,3
	.word	6094
	.byte	33
	.byte	'Ifx_P_OUT',0,7,186,5,3
	.word	2096
	.byte	33
	.byte	'Ifx_P_PCSR',0,7,194,5,3
	.word	5606
	.byte	33
	.byte	'Ifx_P_PDISC',0,7,202,5,3
	.word	5320
	.byte	33
	.byte	'Ifx_P_PDR0',0,7,210,5,3
	.word	4289
	.byte	33
	.byte	'Ifx_P_PDR1',0,7,218,5,3
	.word	4605
	.byte	12
	.word	8965
	.byte	33
	.byte	'Ifx_P',0,7,139,6,3
	.word	77903
	.byte	18,6,83,9,1,19
	.byte	'IfxPort_InputMode_undefined',0,127,19
	.byte	'IfxPort_InputMode_noPullDevice',0,0,19
	.byte	'IfxPort_InputMode_pullDown',0,8,19
	.byte	'IfxPort_InputMode_pullUp',0,16,0,33
	.byte	'IfxPort_InputMode',0,6,89,3
	.word	77923
	.byte	18,6,120,9,1,19
	.byte	'IfxPort_OutputIdx_general',0,128,1,19
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,19
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,19
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,19
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,19
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,19
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,19
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,33
	.byte	'IfxPort_OutputIdx',0,6,130,1,3
	.word	78074
	.byte	18,6,134,1,9,1,19
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,19
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,33
	.byte	'IfxPort_OutputMode',0,6,138,1,3
	.word	78318
	.byte	18,6,144,1,9,1,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,19
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,19
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,19
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,19
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,19
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,19
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,19
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,19
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,33
	.byte	'IfxPort_PadDriver',0,6,158,1,3
	.word	78416
	.byte	33
	.byte	'IfxPort_State',0,6,178,1,3
	.word	9578
	.byte	10,6,190,1,9,8,11
	.byte	'port',0
	.word	9573
	.byte	4,2,35,0,11
	.byte	'pinIndex',0
	.word	612
	.byte	1,2,35,4,0,33
	.byte	'IfxPort_Pin',0,6,194,1,3
	.word	78881
	.byte	33
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,8,148,1,16
	.word	245
	.byte	10,8,212,5,9,8,11
	.byte	'value',0
	.word	10121
	.byte	4,2,35,0,11
	.byte	'mask',0
	.word	10121
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_CcuconRegConfig',0,8,216,5,3
	.word	78981
	.byte	10,8,221,5,9,8,11
	.byte	'pDivider',0
	.word	612
	.byte	1,2,35,0,11
	.byte	'nDivider',0
	.word	612
	.byte	1,2,35,1,11
	.byte	'k2Initial',0
	.word	612
	.byte	1,2,35,2,11
	.byte	'waitTime',0
	.word	302
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_InitialStepConfig',0,8,227,5,3
	.word	79052
	.byte	10,8,231,5,9,12,11
	.byte	'k2Step',0
	.word	612
	.byte	1,2,35,0,11
	.byte	'waitTime',0
	.word	302
	.byte	4,2,35,2,11
	.byte	'hookFunction',0
	.word	78941
	.byte	4,2,35,8,0,33
	.byte	'IfxScuCcu_PllStepsConfig',0,8,236,5,3
	.word	79169
	.byte	3
	.word	242
	.byte	10,8,244,5,9,48,11
	.byte	'ccucon0',0
	.word	78981
	.byte	8,2,35,0,11
	.byte	'ccucon1',0
	.word	78981
	.byte	8,2,35,8,11
	.byte	'ccucon2',0
	.word	78981
	.byte	8,2,35,16,11
	.byte	'ccucon5',0
	.word	78981
	.byte	8,2,35,24,11
	.byte	'ccucon6',0
	.word	78981
	.byte	8,2,35,32,11
	.byte	'ccucon7',0
	.word	78981
	.byte	8,2,35,40,0,33
	.byte	'IfxScuCcu_ClockDistributionConfig',0,8,252,5,3
	.word	79271
	.byte	10,8,128,6,9,8,11
	.byte	'value',0
	.word	10121
	.byte	4,2,35,0,11
	.byte	'mask',0
	.word	10121
	.byte	4,2,35,4,0,33
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,8,132,6,3
	.word	79423
	.byte	3
	.word	79169
	.byte	10,8,137,6,9,16,11
	.byte	'numOfPllDividerSteps',0
	.word	612
	.byte	1,2,35,0,11
	.byte	'pllDividerStep',0
	.word	79499
	.byte	4,2,35,4,11
	.byte	'pllInitialStep',0
	.word	79052
	.byte	8,2,35,8,0,33
	.byte	'IfxScuCcu_SysPllConfig',0,8,142,6,3
	.word	79504
	.byte	18,9,144,1,9,1,19
	.byte	'IfxCpu_CounterMode_normal',0,0,19
	.byte	'IfxCpu_CounterMode_task',0,1,0,33
	.byte	'IfxCpu_CounterMode',0,9,148,1,3
	.word	79621
	.byte	10,9,160,1,9,6,11
	.byte	'counter',0
	.word	10121
	.byte	4,2,35,0,11
	.byte	'overlfow',0
	.word	612
	.byte	1,2,35,4,0,33
	.byte	'IfxCpu_Counter',0,9,164,1,3
	.word	79710
	.byte	10,9,172,1,9,32,11
	.byte	'instruction',0
	.word	79710
	.byte	6,2,35,0,11
	.byte	'clock',0
	.word	79710
	.byte	6,2,35,6,11
	.byte	'counter1',0
	.word	79710
	.byte	6,2,35,12,11
	.byte	'counter2',0
	.word	79710
	.byte	6,2,35,18,11
	.byte	'counter3',0
	.word	79710
	.byte	6,2,35,24,0,33
	.byte	'IfxCpu_Perf',0,9,179,1,3
	.word	79776
	.byte	18,25,69,9,1,19
	.byte	'IfxSrc_Tos_cpu0',0,0,19
	.byte	'IfxSrc_Tos_cpu1',0,1,19
	.byte	'IfxSrc_Tos_dma',0,3,0,33
	.byte	'IfxSrc_Tos',0,25,74,3
	.word	79894
	.byte	18,13,151,1,9,1,19
	.byte	'IfxStm_Comparator_0',0,0,19
	.byte	'IfxStm_Comparator_1',0,1,0,33
	.byte	'IfxStm_Comparator',0,13,155,1,3
	.word	79972
	.byte	18,13,159,1,9,1,19
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,19
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,33
	.byte	'IfxStm_ComparatorInterrupt',0,13,163,1,3
	.word	80050
	.byte	18,13,167,1,9,1,19
	.byte	'IfxStm_ComparatorOffset_0',0,0,19
	.byte	'IfxStm_ComparatorOffset_1',0,1,19
	.byte	'IfxStm_ComparatorOffset_2',0,2,19
	.byte	'IfxStm_ComparatorOffset_3',0,3,19
	.byte	'IfxStm_ComparatorOffset_4',0,4,19
	.byte	'IfxStm_ComparatorOffset_5',0,5,19
	.byte	'IfxStm_ComparatorOffset_6',0,6,19
	.byte	'IfxStm_ComparatorOffset_7',0,7,19
	.byte	'IfxStm_ComparatorOffset_8',0,8,19
	.byte	'IfxStm_ComparatorOffset_9',0,9,19
	.byte	'IfxStm_ComparatorOffset_10',0,10,19
	.byte	'IfxStm_ComparatorOffset_11',0,11,19
	.byte	'IfxStm_ComparatorOffset_12',0,12,19
	.byte	'IfxStm_ComparatorOffset_13',0,13,19
	.byte	'IfxStm_ComparatorOffset_14',0,14,19
	.byte	'IfxStm_ComparatorOffset_15',0,15,19
	.byte	'IfxStm_ComparatorOffset_16',0,16,19
	.byte	'IfxStm_ComparatorOffset_17',0,17,19
	.byte	'IfxStm_ComparatorOffset_18',0,18,19
	.byte	'IfxStm_ComparatorOffset_19',0,19,19
	.byte	'IfxStm_ComparatorOffset_20',0,20,19
	.byte	'IfxStm_ComparatorOffset_21',0,21,19
	.byte	'IfxStm_ComparatorOffset_22',0,22,19
	.byte	'IfxStm_ComparatorOffset_23',0,23,19
	.byte	'IfxStm_ComparatorOffset_24',0,24,19
	.byte	'IfxStm_ComparatorOffset_25',0,25,19
	.byte	'IfxStm_ComparatorOffset_26',0,26,19
	.byte	'IfxStm_ComparatorOffset_27',0,27,19
	.byte	'IfxStm_ComparatorOffset_28',0,28,19
	.byte	'IfxStm_ComparatorOffset_29',0,29,19
	.byte	'IfxStm_ComparatorOffset_30',0,30,19
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,33
	.byte	'IfxStm_ComparatorOffset',0,13,201,1,3
	.word	80159
	.byte	18,13,205,1,9,1,19
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,19
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,19
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,19
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,19
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,19
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,19
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,19
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,19
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,19
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,19
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,19
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,19
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,19
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,19
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,19
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,19
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,19
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,19
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,19
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,19
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,19
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,19
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,19
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,19
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,19
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,19
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,19
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,19
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,19
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,19
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,19
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,33
	.byte	'IfxStm_ComparatorSize',0,13,239,1,3
	.word	81117
	.byte	18,13,244,1,9,1,19
	.byte	'IfxStm_SleepMode_enable',0,0,19
	.byte	'IfxStm_SleepMode_disable',0,1,0,33
	.byte	'IfxStm_SleepMode',0,13,248,1,3
	.word	82137
	.byte	18,13,252,1,9,1,19
	.byte	'IfxStm_SuspendMode_none',0,0,19
	.byte	'IfxStm_SuspendMode_hard',0,1,19
	.byte	'IfxStm_SuspendMode_soft',0,2,0,33
	.byte	'IfxStm_SuspendMode',0,13,129,2,3
	.word	82223
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L198:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,58,15,59,15,57
	.byte	15,11,15,0,0,11,13,0,3,8,73,19,11,15,56,9,0,0,12,53,0,73,19,0,0,13,19,1,3,8,58,15,59,15,57,15,11,15,0
	.byte	0,14,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,15,23,1,58,15,59,15,57,15,11,15,0,0,16,1,1,11,15,73,19
	.byte	0,0,17,33,0,47,15,0,0,18,4,1,58,15,59,15,57,15,11,15,0,0,19,40,0,3,8,28,13,0,0,20,11,1,0,0,21,46,1,3,8
	.byte	73,19,54,15,39,12,63,12,60,12,0,0,22,5,0,73,19,0,0,23,46,0,3,8,54,15,39,12,63,12,60,12,0,0,24,46,1,49
	.byte	19,0,0,25,5,0,49,19,0,0,26,38,0,73,19,0,0,27,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12
	.byte	0,0,28,5,0,58,15,59,15,57,15,73,19,0,0,29,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,30,29
	.byte	1,49,19,0,0,31,11,0,49,19,0,0,32,11,1,49,19,0,0,33,22,0,3,8,58,15,59,15,57,15,73,19,0,0,34,21,0,54,15
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L199:
	.word	.L654-.L653
.L653:
	.half	3
	.word	.L656-.L655
.L655:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'stdlib.h',0,2,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_CircularBuffer.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'stddef.h',0,2,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0,0
.L656:
.L654:
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_info'
.L200:
	.word	1450
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_canReadCount',0,1,139,1,9
	.word	.L260
	.byte	1,1,1
	.word	.L181,.L261,.L180
	.byte	4
	.byte	'fifo',0,1,139,1,41
	.word	.L262,.L263
	.byte	4
	.byte	'count',0,1,139,1,57
	.word	.L264,.L265
	.byte	4
	.byte	'timeout',0,1,139,1,77
	.word	.L266,.L267
	.byte	5
	.word	.L181,.L261
	.byte	6
	.byte	'result',0,1,141,1,13
	.word	.L260,.L268
	.byte	5
	.word	.L11,.L12
	.byte	6
	.byte	'interruptState',0,1,151,1,17
	.word	.L260,.L269
	.byte	6
	.byte	'waitCount',0,1,152,1,17
	.word	.L270,.L271
	.byte	7
	.word	.L272,.L11,.L14
	.byte	8
	.word	.L273,.L11,.L14
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L274
	.byte	7
	.word	.L275,.L11,.L13
	.byte	8
	.word	.L276,.L11,.L13
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L278
	.byte	0,0,0,0,7
	.word	.L279,.L14,.L15
	.byte	9
	.word	.L280,.L281
	.byte	10
	.word	.L282,.L14,.L15
	.byte	0,7
	.word	.L283,.L284,.L17
	.byte	9
	.word	.L285,.L286
	.byte	10
	.word	.L287,.L284,.L17
	.byte	0,5
	.word	.L16,.L12
	.byte	6
	.byte	'DeadLine',0,1,165,1,26
	.word	.L266,.L288
	.byte	7
	.word	.L289,.L16,.L27
	.byte	9
	.word	.L290,.L291
	.byte	8
	.word	.L292,.L16,.L27
	.byte	6
	.byte	'deadLine',0,3,166,2,18
	.word	.L266,.L293
	.byte	7
	.word	.L294,.L19,.L26
	.byte	8
	.word	.L295,.L19,.L26
	.byte	6
	.byte	'stmNow',0,3,223,1,18
	.word	.L266,.L296
	.byte	6
	.byte	'interruptState',0,3,224,1,18
	.word	.L260,.L297
	.byte	7
	.word	.L298,.L19,.L23
	.byte	8
	.word	.L299,.L19,.L23
	.byte	7
	.word	.L272,.L19,.L22
	.byte	8
	.word	.L273,.L19,.L22
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L300
	.byte	7
	.word	.L275,.L19,.L21
	.byte	8
	.word	.L276,.L19,.L21
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L301
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L23,.L24
	.byte	9
	.word	.L303,.L304
	.byte	8
	.word	.L305,.L23,.L24
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L307
	.byte	0,0,7
	.word	.L308,.L309,.L25
	.byte	9
	.word	.L310,.L311
	.byte	8
	.word	.L312,.L309,.L25
	.byte	7
	.word	.L283,.L309,.L25
	.byte	9
	.word	.L285,.L286
	.byte	10
	.word	.L287,.L309,.L25
	.byte	0,0,0,0,0,0,0,7
	.word	.L283,.L313,.L28
	.byte	9
	.word	.L285,.L286
	.byte	10
	.word	.L287,.L313,.L28
	.byte	0,7
	.word	.L314,.L315,.L31
	.byte	9
	.word	.L316,.L317
	.byte	8
	.word	.L318,.L315,.L31
	.byte	6
	.byte	'result',0,3,213,2,13
	.word	.L260,.L319
	.byte	7
	.word	.L294,.L32,.L39
	.byte	8
	.word	.L295,.L32,.L39
	.byte	6
	.byte	'stmNow',0,3,223,1,18
	.word	.L266,.L320
	.byte	6
	.byte	'interruptState',0,3,224,1,18
	.word	.L260,.L321
	.byte	7
	.word	.L298,.L32,.L36
	.byte	8
	.word	.L299,.L32,.L36
	.byte	7
	.word	.L272,.L32,.L35
	.byte	8
	.word	.L273,.L32,.L35
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L322
	.byte	7
	.word	.L275,.L32,.L34
	.byte	8
	.word	.L276,.L32,.L34
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L323
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L36,.L37
	.byte	9
	.word	.L303,.L304
	.byte	8
	.word	.L305,.L36,.L37
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L324
	.byte	0,0,7
	.word	.L308,.L325,.L38
	.byte	9
	.word	.L310,.L311
	.byte	8
	.word	.L312,.L325,.L38
	.byte	7
	.word	.L283,.L325,.L38
	.byte	9
	.word	.L285,.L286
	.byte	10
	.word	.L287,.L325,.L38
	.byte	0,0,0,0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49
	.byte	16,2,6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_line'
.L202:
	.word	.L658-.L657
.L657:
	.half	3
	.word	.L660-.L659
.L659:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0,0,0,0,0
.L660:
	.byte	5,9,7,0,5,2
	.word	.L181
	.byte	3,138,1,1,5,22,9
	.half	.L511-.L181
	.byte	3,6,1,5,9,9
	.half	.L661-.L511
	.byte	1,5,53,7,9
	.half	.L662-.L661
	.byte	1,5,47,9
	.half	.L663-.L662
	.byte	1,5,16,7,9
	.half	.L10-.L663
	.byte	3,2,1,5,23,9
	.half	.L513-.L10
	.byte	1,4,2,5,19,9
	.half	.L11-.L513
	.byte	3,251,3,1,5,17,9
	.half	.L514-.L11
	.byte	3,1,1,5,21,9
	.half	.L515-.L514
	.byte	1,5,5,9
	.half	.L510-.L515
	.byte	1,5,14,9
	.half	.L13-.L510
	.byte	3,8,1,5,10,9
	.half	.L664-.L13
	.byte	3,1,1,5,5,9
	.half	.L665-.L664
	.byte	3,1,1,4,5,5,24,9
	.half	.L14-.L665
	.byte	3,183,124,1,5,5,9
	.half	.L666-.L14
	.byte	1,4,1,5,32,9
	.half	.L15-.L666
	.byte	3,74,1,5,9,9
	.half	.L512-.L15
	.byte	3,2,1,5,40,7,9
	.half	.L667-.L512
	.byte	3,2,1,5,38,9
	.half	.L516-.L667
	.byte	1,5,40,9
	.half	.L668-.L516
	.byte	3,1,1,5,38,9
	.half	.L669-.L668
	.byte	1,4,2,5,5,9
	.half	.L284-.L669
	.byte	3,139,6,1,5,17,7,9
	.half	.L670-.L284
	.byte	3,2,1,4,1,5,38,9
	.half	.L17-.L670
	.byte	3,245,121,1,5,41,9
	.half	.L517-.L17
	.byte	3,125,1,4,3,5,20,9
	.half	.L16-.L517
	.byte	3,138,1,1,5,17,9
	.half	.L671-.L16
	.byte	1,5,5,9
	.half	.L672-.L671
	.byte	1,5,20,7,9
	.half	.L673-.L672
	.byte	3,2,1,5,33,9
	.half	.L518-.L673
	.byte	1,4,2,5,19,9
	.half	.L19-.L518
	.byte	3,228,2,1,5,17,9
	.half	.L519-.L19
	.byte	3,1,1,5,21,9
	.half	.L520-.L519
	.byte	1,5,5,9
	.half	.L521-.L520
	.byte	1,5,14,9
	.half	.L21-.L521
	.byte	3,8,1,5,10,9
	.half	.L674-.L21
	.byte	3,1,1,5,5,9
	.half	.L675-.L674
	.byte	3,1,1,4,3,9
	.half	.L22-.L675
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L23-.L22
	.byte	3,184,3,1,5,32,9
	.half	.L522-.L23
	.byte	3,1,1,5,36,9
	.half	.L676-.L522
	.byte	1,5,12,9
	.half	.L677-.L676
	.byte	1,5,5,9
	.half	.L678-.L677
	.byte	3,2,1,4,3,5,68,9
	.half	.L24-.L678
	.byte	3,186,125,1,5,66,9
	.half	.L679-.L24
	.byte	1,4,2,5,5,9
	.half	.L309-.L679
	.byte	3,199,5,1,5,17,7,9
	.half	.L680-.L309
	.byte	3,2,1,4,3,5,5,9
	.half	.L25-.L680
	.byte	3,186,122,1,5,26,9
	.half	.L26-.L25
	.byte	3,200,0,1,5,5,9
	.half	.L20-.L26
	.byte	3,3,1,4,1,5,40,9
	.half	.L27-.L20
	.byte	3,245,126,1,5,38,9
	.half	.L681-.L27
	.byte	1,9
	.half	.L682-.L681
	.byte	3,1,1,4,2,5,5,9
	.half	.L313-.L682
	.byte	3,131,6,1,5,17,7,9
	.half	.L683-.L313
	.byte	3,2,1,4,1,5,83,9
	.half	.L28-.L683
	.byte	3,254,121,1,5,25,9
	.half	.L29-.L28
	.byte	1,5,20,9
	.half	.L684-.L29
	.byte	1,4,3,5,21,7,9
	.half	.L315-.L684
	.byte	3,173,1,1,5,18,9
	.half	.L685-.L315
	.byte	1,5,5,9
	.half	.L686-.L685
	.byte	1,5,16,7,9
	.half	.L687-.L686
	.byte	3,2,1,5,23,9
	.half	.L525-.L687
	.byte	1,4,2,5,19,9
	.half	.L32-.L525
	.byte	3,181,2,1,5,17,9
	.half	.L526-.L32
	.byte	3,1,1,5,21,9
	.half	.L527-.L526
	.byte	1,5,5,9
	.half	.L528-.L527
	.byte	1,5,14,9
	.half	.L34-.L528
	.byte	3,8,1,5,10,9
	.half	.L688-.L34
	.byte	3,1,1,5,5,9
	.half	.L689-.L688
	.byte	3,1,1,4,3,9
	.half	.L35-.L689
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L36-.L35
	.byte	3,184,3,1,5,32,9
	.half	.L530-.L36
	.byte	3,1,1,5,36,9
	.half	.L690-.L530
	.byte	1,5,12,9
	.half	.L691-.L690
	.byte	1,5,5,9
	.half	.L692-.L691
	.byte	3,2,1,4,3,5,68,9
	.half	.L37-.L692
	.byte	3,186,125,1,5,66,9
	.half	.L693-.L37
	.byte	1,4,2,5,5,9
	.half	.L325-.L693
	.byte	3,199,5,1,5,17,7,9
	.half	.L694-.L325
	.byte	3,2,1,4,3,5,5,9
	.half	.L38-.L694
	.byte	3,186,122,1,5,24,9
	.half	.L39-.L38
	.byte	3,247,0,1,5,5,9
	.half	.L33-.L39
	.byte	3,3,1,4,1,5,74,9
	.half	.L40-.L33
	.byte	3,202,126,1,5,40,7,9
	.half	.L31-.L40
	.byte	3,3,1,5,38,9
	.half	.L695-.L31
	.byte	1,5,26,9
	.half	.L696-.L695
	.byte	3,1,1,5,40,9
	.half	.L697-.L696
	.byte	1,5,5,9
	.half	.L12-.L697
	.byte	3,4,1,5,1,9
	.half	.L41-.L12
	.byte	3,1,1,7,9
	.half	.L204-.L41
	.byte	0,1,1
.L658:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L181,0,.L204-.L181,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_info'
.L205:
	.word	1464
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_canWriteCount',0,1,171,2,9
	.word	.L260
	.byte	1,1,1
	.word	.L191,.L326,.L190
	.byte	4
	.byte	'fifo',0,1,171,2,42
	.word	.L262,.L327
	.byte	4
	.byte	'count',0,1,171,2,58
	.word	.L264,.L328
	.byte	4
	.byte	'timeout',0,1,171,2,78
	.word	.L266,.L329
	.byte	5
	.word	.L191,.L326
	.byte	6
	.byte	'result',0,1,173,2,13
	.word	.L260,.L330
	.byte	5
	.word	.L96,.L97
	.byte	6
	.byte	'interruptState',0,1,185,2,17
	.word	.L260,.L331
	.byte	7
	.word	.L272,.L96,.L99
	.byte	8
	.word	.L273,.L96,.L99
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L332
	.byte	7
	.word	.L275,.L96,.L98
	.byte	8
	.word	.L276,.L96,.L98
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L333
	.byte	0,0,0,0,7
	.word	.L279,.L99,.L100
	.byte	9
	.word	.L280,.L334
	.byte	10
	.word	.L282,.L99,.L100
	.byte	0,7
	.word	.L283,.L335,.L102
	.byte	9
	.word	.L285,.L336
	.byte	10
	.word	.L287,.L335,.L102
	.byte	0,5
	.word	.L101,.L97
	.byte	6
	.byte	'DeadLine',0,1,197,2,26
	.word	.L266,.L337
	.byte	7
	.word	.L289,.L101,.L112
	.byte	9
	.word	.L290,.L338
	.byte	8
	.word	.L292,.L101,.L112
	.byte	6
	.byte	'deadLine',0,3,166,2,18
	.word	.L266,.L339
	.byte	7
	.word	.L294,.L104,.L111
	.byte	8
	.word	.L295,.L104,.L111
	.byte	6
	.byte	'stmNow',0,3,223,1,18
	.word	.L266,.L340
	.byte	6
	.byte	'interruptState',0,3,224,1,18
	.word	.L260,.L341
	.byte	7
	.word	.L298,.L104,.L108
	.byte	8
	.word	.L299,.L104,.L108
	.byte	7
	.word	.L272,.L104,.L107
	.byte	8
	.word	.L273,.L104,.L107
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L342
	.byte	7
	.word	.L275,.L104,.L106
	.byte	8
	.word	.L276,.L104,.L106
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L343
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L108,.L109
	.byte	9
	.word	.L303,.L344
	.byte	8
	.word	.L305,.L108,.L109
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L345
	.byte	0,0,7
	.word	.L308,.L346,.L110
	.byte	9
	.word	.L310,.L347
	.byte	8
	.word	.L312,.L346,.L110
	.byte	7
	.word	.L283,.L346,.L110
	.byte	9
	.word	.L285,.L336
	.byte	10
	.word	.L287,.L346,.L110
	.byte	0,0,0,0,0,0,0,7
	.word	.L279,.L348,.L113
	.byte	9
	.word	.L280,.L334
	.byte	10
	.word	.L282,.L348,.L113
	.byte	0,7
	.word	.L283,.L349,.L114
	.byte	9
	.word	.L285,.L336
	.byte	10
	.word	.L287,.L349,.L114
	.byte	0,7
	.word	.L314,.L350,.L117
	.byte	9
	.word	.L316,.L351
	.byte	8
	.word	.L318,.L350,.L117
	.byte	6
	.byte	'result',0,3,213,2,13
	.word	.L260,.L352
	.byte	7
	.word	.L294,.L118,.L125
	.byte	8
	.word	.L295,.L118,.L125
	.byte	6
	.byte	'stmNow',0,3,223,1,18
	.word	.L266,.L353
	.byte	6
	.byte	'interruptState',0,3,224,1,18
	.word	.L260,.L354
	.byte	7
	.word	.L298,.L118,.L122
	.byte	8
	.word	.L299,.L118,.L122
	.byte	7
	.word	.L272,.L118,.L121
	.byte	8
	.word	.L273,.L118,.L121
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L355
	.byte	7
	.word	.L275,.L118,.L120
	.byte	8
	.word	.L276,.L118,.L120
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L356
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L122,.L123
	.byte	9
	.word	.L303,.L344
	.byte	8
	.word	.L305,.L122,.L123
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L357
	.byte	0,0,7
	.word	.L308,.L358,.L124
	.byte	9
	.word	.L310,.L347
	.byte	8
	.word	.L312,.L358,.L124
	.byte	7
	.word	.L283,.L358,.L124
	.byte	9
	.word	.L285,.L336
	.byte	10
	.word	.L287,.L358,.L124
	.byte	0,0,0,0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49
	.byte	16,2,6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_line'
.L207:
	.word	.L699-.L698
.L698:
	.half	3
	.word	.L701-.L700
.L700:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0,0,0,0,0
.L701:
	.byte	5,9,7,0,5,2
	.word	.L191
	.byte	3,170,2,1,5,22,9
	.half	.L588-.L191
	.byte	3,7,1,5,9,9
	.half	.L702-.L588
	.byte	1,5,53,7,9
	.half	.L703-.L702
	.byte	1,5,47,9
	.half	.L704-.L703
	.byte	1,5,16,7,9
	.half	.L95-.L704
	.byte	3,2,1,5,23,9
	.half	.L589-.L95
	.byte	1,4,2,5,19,9
	.half	.L96-.L589
	.byte	3,218,2,1,5,17,9
	.half	.L591-.L96
	.byte	3,1,1,5,21,9
	.half	.L592-.L591
	.byte	1,5,5,9
	.half	.L593-.L592
	.byte	1,5,14,9
	.half	.L98-.L593
	.byte	3,8,1,5,10,9
	.half	.L705-.L98
	.byte	3,1,1,5,5,9
	.half	.L706-.L705
	.byte	3,1,1,4,5,5,24,9
	.half	.L99-.L706
	.byte	3,183,124,1,5,5,9
	.half	.L707-.L99
	.byte	1,4,1,5,18,9
	.half	.L100-.L707
	.byte	3,236,0,1,5,25,9
	.half	.L708-.L100
	.byte	1,5,9,9
	.half	.L709-.L708
	.byte	1,5,40,7,9
	.half	.L710-.L709
	.byte	3,2,1,5,38,9
	.half	.L711-.L710
	.byte	1,5,40,9
	.half	.L712-.L711
	.byte	3,1,1,5,38,9
	.half	.L713-.L712
	.byte	1,4,2,5,5,9
	.half	.L335-.L713
	.byte	3,235,4,1,5,17,7,9
	.half	.L714-.L335
	.byte	3,2,1,4,1,5,38,9
	.half	.L102-.L714
	.byte	3,149,123,1,5,41,9
	.half	.L590-.L102
	.byte	3,125,1,4,3,5,20,9
	.half	.L101-.L590
	.byte	3,106,1,5,17,9
	.half	.L715-.L101
	.byte	1,5,5,9
	.half	.L716-.L715
	.byte	1,5,20,7,9
	.half	.L717-.L716
	.byte	3,2,1,5,33,9
	.half	.L587-.L717
	.byte	1,4,2,5,19,9
	.half	.L104-.L587
	.byte	3,228,2,1,5,17,9
	.half	.L596-.L104
	.byte	3,1,1,5,21,9
	.half	.L597-.L596
	.byte	1,5,5,9
	.half	.L598-.L597
	.byte	1,5,14,9
	.half	.L106-.L598
	.byte	3,8,1,5,10,9
	.half	.L718-.L106
	.byte	3,1,1,5,5,9
	.half	.L719-.L718
	.byte	3,1,1,4,3,9
	.half	.L107-.L719
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L108-.L107
	.byte	3,184,3,1,5,32,9
	.half	.L595-.L108
	.byte	3,1,1,5,36,9
	.half	.L720-.L595
	.byte	1,5,12,9
	.half	.L721-.L720
	.byte	1,5,5,9
	.half	.L722-.L721
	.byte	3,2,1,4,3,5,68,9
	.half	.L109-.L722
	.byte	3,186,125,1,5,66,9
	.half	.L723-.L109
	.byte	1,4,2,5,5,9
	.half	.L346-.L723
	.byte	3,199,5,1,5,17,7,9
	.half	.L724-.L346
	.byte	3,2,1,4,3,5,5,9
	.half	.L110-.L724
	.byte	3,186,122,1,5,26,9
	.half	.L111-.L110
	.byte	3,200,0,1,5,5,9
	.half	.L105-.L111
	.byte	3,3,1,4,1,5,40,9
	.half	.L112-.L105
	.byte	3,21,1,5,38,9
	.half	.L725-.L112
	.byte	1,4,5,5,24,9
	.half	.L348-.L725
	.byte	3,138,127,1,5,5,9
	.half	.L726-.L348
	.byte	1,4,1,5,62,9
	.half	.L113-.L726
	.byte	3,247,0,1,5,69,9
	.half	.L727-.L113
	.byte	1,5,55,9
	.half	.L728-.L727
	.byte	1,5,46,9
	.half	.L594-.L728
	.byte	1,5,45,9
	.half	.L729-.L594
	.byte	1,5,38,9
	.half	.L730-.L729
	.byte	1,4,2,5,5,9
	.half	.L349-.L730
	.byte	3,227,4,1,5,17,7,9
	.half	.L731-.L349
	.byte	3,2,1,4,1,5,83,9
	.half	.L114-.L731
	.byte	3,158,123,1,5,25,9
	.half	.L115-.L114
	.byte	1,5,20,9
	.half	.L732-.L115
	.byte	1,4,3,5,21,7,9
	.half	.L350-.L732
	.byte	3,13,1,5,18,9
	.half	.L733-.L350
	.byte	1,5,5,9
	.half	.L734-.L733
	.byte	1,5,16,7,9
	.half	.L735-.L734
	.byte	3,2,1,5,23,9
	.half	.L601-.L735
	.byte	1,4,2,5,19,9
	.half	.L118-.L601
	.byte	3,181,2,1,5,17,9
	.half	.L602-.L118
	.byte	3,1,1,5,21,9
	.half	.L603-.L602
	.byte	1,5,5,9
	.half	.L604-.L603
	.byte	1,5,14,9
	.half	.L120-.L604
	.byte	3,8,1,5,10,9
	.half	.L736-.L120
	.byte	3,1,1,5,5,9
	.half	.L737-.L736
	.byte	3,1,1,4,3,9
	.half	.L121-.L737
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L122-.L121
	.byte	3,184,3,1,5,32,9
	.half	.L606-.L122
	.byte	3,1,1,5,36,9
	.half	.L738-.L606
	.byte	1,5,12,9
	.half	.L739-.L738
	.byte	1,5,5,9
	.half	.L740-.L739
	.byte	3,2,1,4,3,5,68,9
	.half	.L123-.L740
	.byte	3,186,125,1,5,66,9
	.half	.L741-.L123
	.byte	1,4,2,5,5,9
	.half	.L358-.L741
	.byte	3,199,5,1,5,17,7,9
	.half	.L742-.L358
	.byte	3,2,1,4,3,5,5,9
	.half	.L124-.L742
	.byte	3,186,122,1,5,24,9
	.half	.L125-.L124
	.byte	3,247,0,1,5,5,9
	.half	.L119-.L125
	.byte	3,3,1,4,1,5,74,9
	.half	.L126-.L119
	.byte	3,106,1,5,40,7,9
	.half	.L117-.L126
	.byte	3,3,1,5,38,9
	.half	.L743-.L117
	.byte	1,5,26,9
	.half	.L744-.L743
	.byte	3,1,1,5,40,9
	.half	.L745-.L744
	.byte	1,5,5,9
	.half	.L97-.L745
	.byte	3,4,1,5,1,9
	.half	.L127-.L97
	.byte	3,1,1,7,9
	.half	.L209-.L127
	.byte	0,1,1
.L699:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L191,0,.L209-.L191,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_info'
.L210:
	.word	470
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_clear',0,1,134,2,6,1,1,1
	.word	.L187,.L359,.L186
	.byte	4
	.byte	'fifo',0,1,134,2,31
	.word	.L262,.L360
	.byte	5
	.word	.L187,.L359
	.byte	6
	.byte	'interruptState',0,1,136,2,13
	.word	.L260,.L361
	.byte	7
	.word	.L272,.L187,.L87
	.byte	8
	.word	.L273,.L187,.L87
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L362
	.byte	7
	.word	.L275,.L187,.L86
	.byte	8
	.word	.L276,.L187,.L86
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L363
	.byte	0,0,0,0,7
	.word	.L283,.L364,.L89
	.byte	9
	.word	.L285,.L365
	.byte	10
	.word	.L287,.L364,.L89
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_line'
.L212:
	.word	.L747-.L746
.L746:
	.half	3
	.word	.L749-.L748
.L748:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L749:
	.byte	4,2,5,19,7,0,5,2
	.word	.L187
	.byte	3,141,5,1,5,17,9
	.half	.L577-.L187
	.byte	3,1,1,5,21,9
	.half	.L578-.L577
	.byte	1,5,5,9
	.half	.L579-.L578
	.byte	1,5,14,9
	.half	.L86-.L579
	.byte	3,8,1,5,10,9
	.half	.L750-.L86
	.byte	3,1,1,5,5,9
	.half	.L751-.L750
	.byte	3,1,1,4,1,5,21,9
	.half	.L87-.L751
	.byte	3,243,124,1,5,5,9
	.half	.L752-.L87
	.byte	1,5,36,7,9
	.half	.L753-.L752
	.byte	3,2,1,5,34,9
	.half	.L754-.L753
	.byte	1,5,36,9
	.half	.L755-.L754
	.byte	3,1,1,5,34,9
	.half	.L756-.L755
	.byte	1,5,32,9
	.half	.L88-.L756
	.byte	3,3,1,5,30,9
	.half	.L757-.L88
	.byte	1,5,32,9
	.half	.L758-.L757
	.byte	3,1,1,5,30,9
	.half	.L759-.L758
	.byte	1,5,32,9
	.half	.L760-.L759
	.byte	3,1,1,5,30,9
	.half	.L761-.L760
	.byte	1,5,32,9
	.half	.L762-.L761
	.byte	3,1,1,5,30,9
	.half	.L763-.L762
	.byte	1,5,36,9
	.half	.L764-.L763
	.byte	3,1,1,5,30,9
	.half	.L765-.L764
	.byte	1,4,2,5,5,9
	.half	.L364-.L765
	.byte	3,148,5,1,5,17,7,9
	.half	.L766-.L364
	.byte	3,2,1,4,1,5,1,9
	.half	.L89-.L766
	.byte	3,236,122,1,7,9
	.half	.L214-.L89
	.byte	0,1,1
.L747:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L187,0,.L214-.L187,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_info'
.L215:
	.word	356
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_create',0,1,71,11
	.word	.L262
	.byte	1,1,1
	.word	.L173,.L366,.L172
	.byte	4
	.byte	'size',0,1,71,37
	.word	.L264,.L367
	.byte	4
	.byte	'elementSize',0,1,71,53
	.word	.L264,.L368
	.byte	5
	.word	.L173,.L366
	.byte	6
	.byte	'fifo',0,1,73,15
	.word	.L262,.L369
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_line'
.L217:
	.word	.L768-.L767
.L767:
	.half	3
	.word	.L770-.L769
.L769:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0,0
.L770:
	.byte	5,11,7,0,5,2
	.word	.L173
	.byte	3,198,0,1,5,12,9
	.half	.L494-.L173
	.byte	3,4,1,5,24,9
	.half	.L495-.L494
	.byte	3,2,1,5,43,9
	.half	.L771-.L495
	.byte	1,5,5,9
	.half	.L492-.L771
	.byte	3,2,1,5,42,7,9
	.half	.L772-.L492
	.byte	3,2,1,5,5,9
	.half	.L2-.L772
	.byte	3,3,1,5,1,9
	.half	.L3-.L2
	.byte	3,1,1,7,9
	.half	.L219-.L3
	.byte	0,1,1
.L768:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L173,0,.L219-.L173,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_info'
.L220:
	.word	311
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_destroy',0,1,88,6,1,1,1
	.word	.L175,.L370,.L174
	.byte	4
	.byte	'fifo',0,1,88,33
	.word	.L262,.L371
	.byte	5
	.word	.L175,.L370
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_line'
.L222:
	.word	.L774-.L773
.L773:
	.half	3
	.word	.L776-.L775
.L775:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0,0
.L776:
	.byte	5,10,7,0,5,2
	.word	.L175
	.byte	3,217,0,1,5,1,9
	.half	.L497-.L175
	.byte	3,1,1,7,9
	.half	.L224-.L497
	.byte	0,1,1
.L774:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L175,0,.L224-.L175,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_info'
.L225:
	.word	373
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L228,.L227
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_init',0,1,94,11
	.word	.L262
	.byte	1,1,1
	.word	.L177,.L372,.L176
	.byte	4
	.byte	'buffer',0,1,94,31
	.word	.L373,.L374
	.byte	4
	.byte	'size',0,1,94,49
	.word	.L264,.L375
	.byte	4
	.byte	'elementSize',0,1,94,65
	.word	.L264,.L376
	.byte	5
	.word	.L177,.L372
	.byte	6
	.byte	'fifo',0,1,96,15
	.word	.L262,.L377
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_line'
.L227:
	.word	.L778-.L777
.L777:
	.half	3
	.word	.L780-.L779
.L779:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0,0
.L780:
	.byte	5,12,7,0,5,2
	.word	.L177
	.byte	3,225,0,1,5,36,9
	.half	.L499-.L177
	.byte	3,7,1,5,34,9
	.half	.L781-.L499
	.byte	1,5,36,9
	.half	.L782-.L781
	.byte	3,1,1,5,34,9
	.half	.L783-.L782
	.byte	1,5,45,9
	.half	.L784-.L783
	.byte	3,1,1,5,34,9
	.half	.L785-.L784
	.byte	1,5,36,9
	.half	.L786-.L785
	.byte	3,1,1,5,34,9
	.half	.L787-.L786
	.byte	1,5,36,9
	.half	.L788-.L787
	.byte	3,1,1,5,34,9
	.half	.L789-.L788
	.byte	1,5,61,9
	.half	.L790-.L789
	.byte	3,1,1,5,34,9
	.half	.L791-.L790
	.byte	1,5,51,9
	.half	.L792-.L791
	.byte	3,1,1,5,34,9
	.half	.L793-.L792
	.byte	1,9
	.half	.L794-.L793
	.byte	3,1,1,9
	.half	.L795-.L794
	.byte	3,1,1,5,5,9
	.half	.L796-.L795
	.byte	3,3,1,5,1,9
	.half	.L4-.L796
	.byte	3,1,1,7,9
	.half	.L229-.L4
	.byte	0,1,1
.L778:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L177,0,.L229-.L177,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_info'
.L230:
	.word	1613
	.half	3
	.word	.L231
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L233,.L232
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_read',0,1,211,1,11
	.word	.L264
	.byte	1,1,1
	.word	.L185,.L378,.L184
	.byte	4
	.byte	'fifo',0,1,211,1,35
	.word	.L262,.L379
	.byte	4
	.byte	'data',0,1,211,1,47
	.word	.L373,.L380
	.byte	4
	.byte	'count',0,1,211,1,63
	.word	.L264,.L381
	.byte	4
	.byte	'timeout',0,1,211,1,83
	.word	.L266,.L382
	.byte	5
	.word	.L185,.L378
	.byte	6
	.byte	'DeadLine',0,1,213,1,24
	.word	.L266,.L383
	.byte	6
	.byte	'blockSize',0,1,214,1,24
	.word	.L264,.L384
	.byte	6
	.byte	'buffer',0,1,215,1,24
	.word	.L385,.L386
	.byte	6
	.byte	'Stop',0,1,216,1,24
	.word	.L260,.L387
	.byte	7
	.word	.L289,.L388,.L58
	.byte	8
	.word	.L290,.L389
	.byte	9
	.word	.L292,.L388,.L58
	.byte	6
	.byte	'deadLine',0,2,166,2,18
	.word	.L266,.L390
	.byte	7
	.word	.L294,.L49,.L56
	.byte	9
	.word	.L295,.L49,.L56
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L391
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L392
	.byte	7
	.word	.L298,.L49,.L53
	.byte	9
	.word	.L299,.L49,.L53
	.byte	7
	.word	.L272,.L49,.L52
	.byte	9
	.word	.L273,.L49,.L52
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L393
	.byte	7
	.word	.L275,.L49,.L51
	.byte	9
	.word	.L276,.L49,.L51
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L394
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L53,.L54
	.byte	8
	.word	.L303,.L395
	.byte	9
	.word	.L305,.L53,.L54
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L396
	.byte	0,0,7
	.word	.L308,.L397,.L55
	.byte	8
	.word	.L310,.L398
	.byte	9
	.word	.L312,.L397,.L55
	.byte	7
	.word	.L283,.L397,.L55
	.byte	8
	.word	.L285,.L399
	.byte	10
	.word	.L287,.L397,.L55
	.byte	0,0,0,0,0,0,0,7
	.word	.L314,.L400,.L60
	.byte	8
	.word	.L316,.L401
	.byte	9
	.word	.L318,.L400,.L60
	.byte	6
	.byte	'result',0,2,213,2,13
	.word	.L260,.L402
	.byte	7
	.word	.L294,.L61,.L68
	.byte	9
	.word	.L295,.L61,.L68
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L403
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L404
	.byte	7
	.word	.L298,.L61,.L65
	.byte	9
	.word	.L299,.L61,.L65
	.byte	7
	.word	.L272,.L61,.L64
	.byte	9
	.word	.L273,.L61,.L64
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L405
	.byte	7
	.word	.L275,.L61,.L63
	.byte	9
	.word	.L276,.L61,.L63
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L406
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L65,.L66
	.byte	8
	.word	.L303,.L395
	.byte	9
	.word	.L305,.L65,.L66
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L407
	.byte	0,0,7
	.word	.L308,.L408,.L67
	.byte	8
	.word	.L310,.L398
	.byte	9
	.word	.L312,.L408,.L67
	.byte	7
	.word	.L283,.L408,.L67
	.byte	8
	.word	.L285,.L399
	.byte	10
	.word	.L287,.L408,.L67
	.byte	0,0,0,0,0,0,0,7
	.word	.L314,.L409,.L75
	.byte	8
	.word	.L316,.L401
	.byte	9
	.word	.L318,.L409,.L75
	.byte	6
	.byte	'result',0,2,213,2,13
	.word	.L260,.L410
	.byte	7
	.word	.L294,.L76,.L83
	.byte	9
	.word	.L295,.L76,.L83
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L411
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L412
	.byte	7
	.word	.L298,.L76,.L80
	.byte	9
	.word	.L299,.L76,.L80
	.byte	7
	.word	.L272,.L76,.L79
	.byte	9
	.word	.L273,.L76,.L79
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L413
	.byte	7
	.word	.L275,.L76,.L78
	.byte	9
	.word	.L276,.L76,.L78
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L414
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L80,.L81
	.byte	8
	.word	.L303,.L395
	.byte	9
	.word	.L305,.L80,.L81
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L415
	.byte	0,0,7
	.word	.L308,.L416,.L82
	.byte	8
	.word	.L310,.L398
	.byte	9
	.word	.L312,.L416,.L82
	.byte	7
	.word	.L283,.L416,.L82
	.byte	8
	.word	.L285,.L399
	.byte	10
	.word	.L287,.L416,.L82
	.byte	0,0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_abbrev'
.L231:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_line'
.L232:
	.word	.L798-.L797
.L797:
	.half	3
	.word	.L800-.L799
.L799:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0,0
.L800:
	.byte	5,11,7,0,5,2
	.word	.L185
	.byte	3,210,1,1,5,29,9
	.half	.L541-.L185
	.byte	3,5,1,5,5,9
	.half	.L543-.L541
	.byte	3,5,1,5,29,7,9
	.half	.L801-.L543
	.byte	3,2,1,5,23,9
	.half	.L802-.L801
	.byte	1,5,37,9
	.half	.L803-.L802
	.byte	3,1,1,5,23,9
	.half	.L804-.L803
	.byte	1,5,37,9
	.half	.L805-.L804
	.byte	3,1,1,5,23,9
	.half	.L806-.L805
	.byte	1,4,2,5,20,9
	.half	.L388-.L806
	.byte	3,199,0,1,5,17,9
	.half	.L807-.L388
	.byte	1,5,5,9
	.half	.L808-.L807
	.byte	1,5,20,7,9
	.half	.L809-.L808
	.byte	3,2,1,5,33,9
	.half	.L544-.L809
	.byte	1,4,3,5,19,9
	.half	.L49-.L544
	.byte	3,228,2,1,5,17,9
	.half	.L545-.L49
	.byte	3,1,1,5,21,9
	.half	.L546-.L545
	.byte	1,5,5,9
	.half	.L547-.L546
	.byte	1,5,14,9
	.half	.L51-.L547
	.byte	3,8,1,5,10,9
	.half	.L810-.L51
	.byte	3,1,1,5,5,9
	.half	.L811-.L810
	.byte	3,1,1,4,2,9
	.half	.L52-.L811
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L53-.L52
	.byte	3,184,3,1,5,32,9
	.half	.L548-.L53
	.byte	3,1,1,5,36,9
	.half	.L812-.L548
	.byte	1,5,12,9
	.half	.L813-.L812
	.byte	1,5,5,9
	.half	.L814-.L813
	.byte	3,2,1,4,2,5,68,9
	.half	.L54-.L814
	.byte	3,186,125,1,5,66,9
	.half	.L815-.L54
	.byte	1,4,3,5,5,9
	.half	.L397-.L815
	.byte	3,199,5,1,5,17,7,9
	.half	.L816-.L397
	.byte	3,2,1,4,2,5,5,9
	.half	.L55-.L816
	.byte	3,186,122,1,5,26,9
	.half	.L56-.L55
	.byte	3,200,0,1,5,5,9
	.half	.L50-.L56
	.byte	3,3,1,4,1,5,50,9
	.half	.L58-.L50
	.byte	3,181,127,1,5,13,9
	.half	.L555-.L58
	.byte	3,2,1,5,51,7,9
	.half	.L817-.L555
	.byte	3,3,1,5,65,9
	.half	.L818-.L817
	.byte	1,5,23,9
	.half	.L540-.L818
	.byte	1,5,55,9
	.half	.L558-.L540
	.byte	3,1,1,5,17,9
	.half	.L59-.L558
	.byte	3,3,1,4,2,5,21,7,9
	.half	.L400-.L59
	.byte	3,232,0,1,5,18,9
	.half	.L819-.L400
	.byte	1,5,5,9
	.half	.L820-.L819
	.byte	1,5,16,7,9
	.half	.L821-.L820
	.byte	3,2,1,5,23,9
	.half	.L560-.L821
	.byte	1,4,3,5,19,9
	.half	.L61-.L560
	.byte	3,181,2,1,5,17,9
	.half	.L561-.L61
	.byte	3,1,1,5,21,9
	.half	.L562-.L561
	.byte	1,5,5,9
	.half	.L563-.L562
	.byte	1,5,14,9
	.half	.L63-.L563
	.byte	3,8,1,5,10,9
	.half	.L822-.L63
	.byte	3,1,1,5,5,9
	.half	.L823-.L822
	.byte	3,1,1,4,2,9
	.half	.L64-.L823
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L65-.L64
	.byte	3,184,3,1,5,32,9
	.half	.L565-.L65
	.byte	3,1,1,5,36,9
	.half	.L824-.L565
	.byte	1,5,12,9
	.half	.L825-.L824
	.byte	1,5,5,9
	.half	.L826-.L825
	.byte	3,2,1,4,2,5,68,9
	.half	.L66-.L826
	.byte	3,186,125,1,5,66,9
	.half	.L827-.L66
	.byte	1,4,3,5,5,9
	.half	.L408-.L827
	.byte	3,199,5,1,5,17,7,9
	.half	.L828-.L408
	.byte	3,2,1,4,2,5,5,9
	.half	.L67-.L828
	.byte	3,186,122,1,5,24,9
	.half	.L68-.L67
	.byte	3,247,0,1,5,5,9
	.half	.L62-.L68
	.byte	3,3,1,4,1,5,58,9
	.half	.L69-.L62
	.byte	3,143,127,1,5,44,7,9
	.half	.L60-.L69
	.byte	3,3,1,5,42,9
	.half	.L829-.L60
	.byte	1,5,17,9
	.half	.L830-.L829
	.byte	3,1,1,5,13,9
	.half	.L70-.L830
	.byte	3,3,1,5,87,7,9
	.half	.L831-.L70
	.byte	3,2,1,5,29,9
	.half	.L73-.L831
	.byte	1,5,24,9
	.half	.L832-.L73
	.byte	1,4,2,5,21,7,9
	.half	.L409-.L832
	.byte	3,223,0,1,5,18,9
	.half	.L833-.L409
	.byte	1,5,5,9
	.half	.L834-.L833
	.byte	1,5,16,7,9
	.half	.L835-.L834
	.byte	3,2,1,5,23,9
	.half	.L568-.L835
	.byte	1,4,3,5,19,9
	.half	.L76-.L568
	.byte	3,181,2,1,5,17,9
	.half	.L569-.L76
	.byte	3,1,1,5,21,9
	.half	.L570-.L569
	.byte	1,5,5,9
	.half	.L571-.L570
	.byte	1,5,14,9
	.half	.L78-.L571
	.byte	3,8,1,5,10,9
	.half	.L836-.L78
	.byte	3,1,1,5,5,9
	.half	.L837-.L836
	.byte	3,1,1,4,2,9
	.half	.L79-.L837
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L80-.L79
	.byte	3,184,3,1,5,32,9
	.half	.L573-.L80
	.byte	3,1,1,5,36,9
	.half	.L838-.L573
	.byte	1,5,12,9
	.half	.L839-.L838
	.byte	1,5,5,9
	.half	.L840-.L839
	.byte	3,2,1,4,2,5,68,9
	.half	.L81-.L840
	.byte	3,186,125,1,5,66,9
	.half	.L841-.L81
	.byte	1,4,3,5,5,9
	.half	.L416-.L841
	.byte	3,199,5,1,5,17,7,9
	.half	.L842-.L416
	.byte	3,2,1,4,2,5,5,9
	.half	.L82-.L842
	.byte	3,186,122,1,5,24,9
	.half	.L83-.L82
	.byte	3,247,0,1,5,5,9
	.half	.L77-.L83
	.byte	3,3,1,4,1,5,78,9
	.half	.L84-.L77
	.byte	3,152,127,1,5,29,7,9
	.half	.L75-.L84
	.byte	3,3,1,5,43,9
	.half	.L843-.L75
	.byte	1,5,29,9
	.half	.L72-.L843
	.byte	3,2,1,5,34,7,9
	.half	.L71-.L72
	.byte	3,2,1,5,26,9
	.half	.L844-.L71
	.byte	1,5,5,9
	.half	.L48-.L844
	.byte	3,3,1,5,1,9
	.half	.L85-.L48
	.byte	3,1,1,7,9
	.half	.L234-.L85
	.byte	0,1,1
.L798:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_ranges'
.L233:
	.word	-1,.L185,0,.L234-.L185,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_info'
.L235:
	.word	1614
	.half	3
	.word	.L236
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L238,.L237
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_write',0,1,241,2,11
	.word	.L264
	.byte	1,1,1
	.word	.L195,.L417,.L194
	.byte	4
	.byte	'fifo',0,1,241,2,36
	.word	.L262,.L418
	.byte	4
	.byte	'data',0,1,241,2,54
	.word	.L419,.L420
	.byte	4
	.byte	'count',0,1,241,2,70
	.word	.L264,.L421
	.byte	4
	.byte	'timeout',0,1,241,2,90
	.word	.L266,.L422
	.byte	5
	.word	.L195,.L417
	.byte	6
	.byte	'DeadLine',0,1,243,2,24
	.word	.L266,.L423
	.byte	6
	.byte	'blockSize',0,1,244,2,24
	.word	.L264,.L424
	.byte	6
	.byte	'buffer',0,1,245,2,24
	.word	.L385,.L425
	.byte	6
	.byte	'Stop',0,1,246,2,24
	.word	.L260,.L426
	.byte	7
	.word	.L289,.L427,.L144
	.byte	8
	.word	.L290,.L428
	.byte	9
	.word	.L292,.L427,.L144
	.byte	6
	.byte	'deadLine',0,2,166,2,18
	.word	.L266,.L429
	.byte	7
	.word	.L294,.L135,.L142
	.byte	9
	.word	.L295,.L135,.L142
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L430
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L431
	.byte	7
	.word	.L298,.L135,.L139
	.byte	9
	.word	.L299,.L135,.L139
	.byte	7
	.word	.L272,.L135,.L138
	.byte	9
	.word	.L273,.L135,.L138
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L432
	.byte	7
	.word	.L275,.L135,.L137
	.byte	9
	.word	.L276,.L135,.L137
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L433
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L139,.L140
	.byte	8
	.word	.L303,.L434
	.byte	9
	.word	.L305,.L139,.L140
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L435
	.byte	0,0,7
	.word	.L308,.L436,.L141
	.byte	8
	.word	.L310,.L437
	.byte	9
	.word	.L312,.L436,.L141
	.byte	7
	.word	.L283,.L436,.L141
	.byte	8
	.word	.L285,.L438
	.byte	10
	.word	.L287,.L436,.L141
	.byte	0,0,0,0,0,0,0,7
	.word	.L314,.L439,.L146
	.byte	8
	.word	.L316,.L440
	.byte	9
	.word	.L318,.L439,.L146
	.byte	6
	.byte	'result',0,2,213,2,13
	.word	.L260,.L441
	.byte	7
	.word	.L294,.L147,.L154
	.byte	9
	.word	.L295,.L147,.L154
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L442
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L443
	.byte	7
	.word	.L298,.L147,.L151
	.byte	9
	.word	.L299,.L147,.L151
	.byte	7
	.word	.L272,.L147,.L150
	.byte	9
	.word	.L273,.L147,.L150
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L444
	.byte	7
	.word	.L275,.L147,.L149
	.byte	9
	.word	.L276,.L147,.L149
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L445
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L151,.L152
	.byte	8
	.word	.L303,.L434
	.byte	9
	.word	.L305,.L151,.L152
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L446
	.byte	0,0,7
	.word	.L308,.L447,.L153
	.byte	8
	.word	.L310,.L437
	.byte	9
	.word	.L312,.L447,.L153
	.byte	7
	.word	.L283,.L447,.L153
	.byte	8
	.word	.L285,.L438
	.byte	10
	.word	.L287,.L447,.L153
	.byte	0,0,0,0,0,0,0,7
	.word	.L314,.L448,.L161
	.byte	8
	.word	.L316,.L440
	.byte	9
	.word	.L318,.L448,.L161
	.byte	6
	.byte	'result',0,2,213,2,13
	.word	.L260,.L449
	.byte	7
	.word	.L294,.L162,.L169
	.byte	9
	.word	.L295,.L162,.L169
	.byte	6
	.byte	'stmNow',0,2,223,1,18
	.word	.L266,.L450
	.byte	6
	.byte	'interruptState',0,2,224,1,18
	.word	.L260,.L451
	.byte	7
	.word	.L298,.L162,.L166
	.byte	9
	.word	.L299,.L162,.L166
	.byte	7
	.word	.L272,.L162,.L165
	.byte	9
	.word	.L273,.L162,.L165
	.byte	6
	.byte	'enabled',0,3,149,5,13
	.word	.L260,.L452
	.byte	7
	.word	.L275,.L162,.L164
	.byte	9
	.word	.L276,.L162,.L164
	.byte	6
	.byte	'reg',0,3,141,5,17
	.word	.L277,.L453
	.byte	0,0,0,0,0,0,7
	.word	.L302,.L166,.L167
	.byte	8
	.word	.L303,.L434
	.byte	9
	.word	.L305,.L166,.L167
	.byte	6
	.byte	'result',0,4,164,4,12
	.word	.L306,.L454
	.byte	0,0,7
	.word	.L308,.L455,.L168
	.byte	8
	.word	.L310,.L437
	.byte	9
	.word	.L312,.L455,.L168
	.byte	7
	.word	.L283,.L455,.L168
	.byte	8
	.word	.L285,.L438
	.byte	10
	.word	.L287,.L455,.L168
	.byte	0,0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_abbrev'
.L236:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_line'
.L237:
	.word	.L846-.L845
.L845:
	.half	3
	.word	.L848-.L847
.L847:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0,0
.L848:
	.byte	5,11,7,0,5,2
	.word	.L195
	.byte	3,240,2,1,5,29,9
	.half	.L617-.L195
	.byte	3,5,1,5,5,9
	.half	.L619-.L617
	.byte	3,5,1,5,29,7,9
	.half	.L849-.L619
	.byte	3,2,1,5,23,9
	.half	.L850-.L849
	.byte	1,5,37,9
	.half	.L851-.L850
	.byte	3,1,1,5,23,9
	.half	.L852-.L851
	.byte	1,5,37,9
	.half	.L853-.L852
	.byte	3,1,1,5,23,9
	.half	.L854-.L853
	.byte	1,4,2,5,20,9
	.half	.L427-.L854
	.byte	3,169,127,1,5,17,9
	.half	.L855-.L427
	.byte	1,5,5,9
	.half	.L856-.L855
	.byte	1,5,20,7,9
	.half	.L857-.L856
	.byte	3,2,1,5,33,9
	.half	.L620-.L857
	.byte	1,4,3,5,19,9
	.half	.L135-.L620
	.byte	3,228,2,1,5,17,9
	.half	.L621-.L135
	.byte	3,1,1,5,21,9
	.half	.L622-.L621
	.byte	1,5,5,9
	.half	.L623-.L622
	.byte	1,5,14,9
	.half	.L137-.L623
	.byte	3,8,1,5,10,9
	.half	.L858-.L137
	.byte	3,1,1,5,5,9
	.half	.L859-.L858
	.byte	3,1,1,4,2,9
	.half	.L138-.L859
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L139-.L138
	.byte	3,184,3,1,5,32,9
	.half	.L624-.L139
	.byte	3,1,1,5,36,9
	.half	.L860-.L624
	.byte	1,5,12,9
	.half	.L861-.L860
	.byte	1,5,5,9
	.half	.L862-.L861
	.byte	3,2,1,4,2,5,68,9
	.half	.L140-.L862
	.byte	3,186,125,1,5,66,9
	.half	.L863-.L140
	.byte	1,4,3,5,5,9
	.half	.L436-.L863
	.byte	3,199,5,1,5,17,7,9
	.half	.L864-.L436
	.byte	3,2,1,4,2,5,5,9
	.half	.L141-.L864
	.byte	3,186,122,1,5,26,9
	.half	.L142-.L141
	.byte	3,200,0,1,5,5,9
	.half	.L136-.L142
	.byte	3,3,1,4,1,5,51,9
	.half	.L144-.L136
	.byte	3,211,0,1,5,13,9
	.half	.L631-.L144
	.byte	3,2,1,5,52,7,9
	.half	.L865-.L631
	.byte	3,3,1,5,66,9
	.half	.L866-.L865
	.byte	1,5,23,9
	.half	.L616-.L866
	.byte	1,5,56,9
	.half	.L634-.L616
	.byte	3,1,1,5,17,9
	.half	.L145-.L634
	.byte	3,3,1,4,2,5,21,7,9
	.half	.L439-.L145
	.byte	3,74,1,5,18,9
	.half	.L867-.L439
	.byte	1,5,5,9
	.half	.L868-.L867
	.byte	1,5,16,7,9
	.half	.L869-.L868
	.byte	3,2,1,5,23,9
	.half	.L636-.L869
	.byte	1,4,3,5,19,9
	.half	.L147-.L636
	.byte	3,181,2,1,5,17,9
	.half	.L637-.L147
	.byte	3,1,1,5,21,9
	.half	.L638-.L637
	.byte	1,5,5,9
	.half	.L639-.L638
	.byte	1,5,14,9
	.half	.L149-.L639
	.byte	3,8,1,5,10,9
	.half	.L870-.L149
	.byte	3,1,1,5,5,9
	.half	.L871-.L870
	.byte	3,1,1,4,2,9
	.half	.L150-.L871
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L151-.L150
	.byte	3,184,3,1,5,32,9
	.half	.L641-.L151
	.byte	3,1,1,5,36,9
	.half	.L872-.L641
	.byte	1,5,12,9
	.half	.L873-.L872
	.byte	1,5,5,9
	.half	.L874-.L873
	.byte	3,2,1,4,2,5,68,9
	.half	.L152-.L874
	.byte	3,186,125,1,5,66,9
	.half	.L875-.L152
	.byte	1,4,3,5,5,9
	.half	.L447-.L875
	.byte	3,199,5,1,5,17,7,9
	.half	.L876-.L447
	.byte	3,2,1,4,2,5,5,9
	.half	.L153-.L876
	.byte	3,186,122,1,5,24,9
	.half	.L154-.L153
	.byte	3,247,0,1,5,5,9
	.half	.L148-.L154
	.byte	3,3,1,4,1,5,58,9
	.half	.L155-.L148
	.byte	3,45,1,5,44,7,9
	.half	.L146-.L155
	.byte	3,3,1,5,42,9
	.half	.L877-.L146
	.byte	1,5,17,9
	.half	.L878-.L877
	.byte	3,1,1,5,13,9
	.half	.L156-.L878
	.byte	3,3,1,5,87,7,9
	.half	.L879-.L156
	.byte	3,2,1,5,29,9
	.half	.L159-.L879
	.byte	1,5,24,9
	.half	.L880-.L159
	.byte	1,4,2,5,21,7,9
	.half	.L448-.L880
	.byte	3,65,1,5,18,9
	.half	.L881-.L448
	.byte	1,5,5,9
	.half	.L882-.L881
	.byte	1,5,16,7,9
	.half	.L883-.L882
	.byte	3,2,1,5,23,9
	.half	.L644-.L883
	.byte	1,4,3,5,19,9
	.half	.L162-.L644
	.byte	3,181,2,1,5,17,9
	.half	.L645-.L162
	.byte	3,1,1,5,21,9
	.half	.L646-.L645
	.byte	1,5,5,9
	.half	.L647-.L646
	.byte	1,5,14,9
	.half	.L164-.L647
	.byte	3,8,1,5,10,9
	.half	.L884-.L164
	.byte	3,1,1,5,5,9
	.half	.L885-.L884
	.byte	3,1,1,4,2,9
	.half	.L165-.L885
	.byte	3,213,123,1,4,4,5,24,9
	.half	.L166-.L165
	.byte	3,184,3,1,5,32,9
	.half	.L649-.L166
	.byte	3,1,1,5,36,9
	.half	.L886-.L649
	.byte	1,5,12,9
	.half	.L887-.L886
	.byte	1,5,5,9
	.half	.L888-.L887
	.byte	3,2,1,4,2,5,68,9
	.half	.L167-.L888
	.byte	3,186,125,1,5,66,9
	.half	.L889-.L167
	.byte	1,4,3,5,5,9
	.half	.L455-.L889
	.byte	3,199,5,1,5,17,7,9
	.half	.L890-.L455
	.byte	3,2,1,4,2,5,5,9
	.half	.L168-.L890
	.byte	3,186,122,1,5,24,9
	.half	.L169-.L168
	.byte	3,247,0,1,5,5,9
	.half	.L163-.L169
	.byte	3,3,1,4,1,5,78,9
	.half	.L170-.L163
	.byte	3,54,1,5,28,7,9
	.half	.L161-.L170
	.byte	3,3,1,5,42,9
	.half	.L891-.L161
	.byte	1,5,29,9
	.half	.L158-.L891
	.byte	3,2,1,5,32,7,9
	.half	.L157-.L158
	.byte	3,2,1,5,24,9
	.half	.L892-.L157
	.byte	1,5,5,9
	.half	.L134-.L892
	.byte	3,3,1,5,1,9
	.half	.L171-.L134
	.byte	3,1,1,7,9
	.half	.L239-.L171
	.byte	0,1,1
.L846:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_ranges'
.L238:
	.word	-1,.L195,0,.L239-.L195,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_info'
.L240:
	.word	550
	.half	3
	.word	.L241
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L243,.L242
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_beginRead',0,1,123,18
	.word	.L264
	.byte	1,1
	.word	.L179,.L456,.L178
	.byte	4
	.byte	'fifo',0,1,123,47
	.word	.L262,.L457
	.byte	4
	.byte	'count',0,1,123,63
	.word	.L264,.L458
	.byte	5
	.word	.L179,.L456
	.byte	6
	.byte	'interruptState',0,1,125,15
	.word	.L260,.L459
	.byte	6
	.byte	'blockSize',0,1,126,15
	.word	.L264,.L460
	.byte	7
	.word	.L272,.L179,.L6
	.byte	8
	.word	.L273,.L179,.L6
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L461
	.byte	7
	.word	.L275,.L179,.L5
	.byte	8
	.word	.L276,.L179,.L5
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L462
	.byte	0,0,0,0,7
	.word	.L279,.L6,.L7
	.byte	9
	.word	.L280,.L463
	.byte	10
	.word	.L282,.L6,.L7
	.byte	0,7
	.word	.L283,.L464,.L8
	.byte	9
	.word	.L285,.L465
	.byte	10
	.word	.L287,.L464,.L8
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_abbrev'
.L241:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_line'
.L242:
	.word	.L894-.L893
.L893:
	.half	3
	.word	.L896-.L895
.L895:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0,0,0,0,0
.L896:
	.byte	4,2,5,19,7,0,5,2
	.word	.L179
	.byte	3,141,5,1,5,17,9
	.half	.L504-.L179
	.byte	3,1,1,5,21,9
	.half	.L505-.L504
	.byte	1,5,5,9
	.half	.L506-.L505
	.byte	1,5,14,9
	.half	.L5-.L506
	.byte	3,8,1,5,10,9
	.half	.L897-.L5
	.byte	3,1,1,5,5,9
	.half	.L898-.L897
	.byte	3,1,1,4,3,5,24,9
	.half	.L6-.L898
	.byte	3,183,124,1,5,5,9
	.half	.L899-.L6
	.byte	1,4,1,5,37,9
	.half	.L7-.L899
	.byte	3,177,127,1,5,48,9
	.half	.L507-.L7
	.byte	3,1,1,5,42,9
	.half	.L900-.L507
	.byte	1,5,29,9
	.half	.L901-.L900
	.byte	1,5,32,9
	.half	.L509-.L901
	.byte	3,1,1,5,30,9
	.half	.L902-.L509
	.byte	1,5,44,9
	.half	.L903-.L902
	.byte	3,1,1,5,61,9
	.half	.L503-.L903
	.byte	1,5,37,9
	.half	.L904-.L503
	.byte	1,5,30,9
	.half	.L905-.L904
	.byte	1,4,2,5,5,9
	.half	.L464-.L905
	.byte	3,166,6,1,5,17,7,9
	.half	.L906-.L464
	.byte	3,2,1,4,1,5,5,9
	.half	.L8-.L906
	.byte	3,219,121,1,5,1,9
	.half	.L9-.L8
	.byte	3,1,1,7,9
	.half	.L244-.L9
	.byte	0,1,1
.L894:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_ranges'
.L243:
	.word	-1,.L179,0,.L244-.L179,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_info'
.L245:
	.word	517
	.half	3
	.word	.L246
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L248,.L247
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_readEnd',0,1,185,1,18
	.word	.L264
	.byte	1,1
	.word	.L183,.L466,.L182
	.byte	4
	.byte	'fifo',0,1,185,1,45
	.word	.L262,.L467
	.byte	4
	.byte	'count',0,1,185,1,61
	.word	.L264,.L468
	.byte	4
	.byte	'blockSize',0,1,185,1,78
	.word	.L264,.L469
	.byte	5
	.word	.L183,.L466
	.byte	6
	.byte	'interruptState',0,1,187,1,13
	.word	.L260,.L470
	.byte	7
	.word	.L272,.L183,.L43
	.byte	8
	.word	.L273,.L183,.L43
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L471
	.byte	7
	.word	.L275,.L183,.L42
	.byte	8
	.word	.L276,.L183,.L42
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L472
	.byte	0,0,0,0,7
	.word	.L283,.L44,.L46
	.byte	9
	.word	.L285,.L473
	.byte	10
	.word	.L287,.L44,.L46
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_abbrev'
.L246:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_line'
.L247:
	.word	.L908-.L907
.L907:
	.half	3
	.word	.L910-.L909
.L909:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L910:
	.byte	4,2,5,19,7,0,5,2
	.word	.L183
	.byte	3,141,5,1,5,17,9
	.half	.L534-.L183
	.byte	3,1,1,5,21,9
	.half	.L535-.L534
	.byte	1,5,5,9
	.half	.L536-.L535
	.byte	1,5,14,9
	.half	.L42-.L536
	.byte	3,8,1,5,10,9
	.half	.L911-.L42
	.byte	3,1,1,5,5,9
	.half	.L912-.L911
	.byte	3,1,1,4,1,5,17,9
	.half	.L43-.L912
	.byte	3,167,124,1,5,24,9
	.half	.L913-.L43
	.byte	1,5,21,9
	.half	.L914-.L913
	.byte	3,2,1,5,5,9
	.half	.L915-.L914
	.byte	1,5,21,7,9
	.half	.L916-.L915
	.byte	3,2,1,5,34,9
	.half	.L917-.L916
	.byte	1,5,25,9
	.half	.L918-.L917
	.byte	3,2,1,5,9,9
	.half	.L919-.L918
	.byte	1,5,40,7,9
	.half	.L920-.L919
	.byte	3,2,1,5,38,9
	.half	.L921-.L920
	.byte	1,5,40,9
	.half	.L922-.L921
	.byte	3,1,1,5,38,9
	.half	.L923-.L922
	.byte	1,4,2,5,5,9
	.half	.L44-.L923
	.byte	3,225,5,1,5,17,7,9
	.half	.L924-.L44
	.byte	3,2,1,4,1,5,18,9
	.half	.L46-.L924
	.byte	3,163,122,1,5,5,9
	.half	.L925-.L46
	.byte	1,5,1,9
	.half	.L47-.L925
	.byte	3,1,1,7,9
	.half	.L249-.L47
	.byte	0,1,1
.L908:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_ranges'
.L248:
	.word	-1,.L183,0,.L249-.L183,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_info'
.L250:
	.word	556
	.half	3
	.word	.L251
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L253,.L252
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_beginWrite',0,1,155,2,18
	.word	.L264
	.byte	1,1
	.word	.L189,.L474,.L188
	.byte	4
	.byte	'fifo',0,1,155,2,48
	.word	.L262,.L475
	.byte	4
	.byte	'count',0,1,155,2,64
	.word	.L264,.L476
	.byte	5
	.word	.L189,.L474
	.byte	6
	.byte	'blockSize',0,1,157,2,15
	.word	.L264,.L477
	.byte	6
	.byte	'interruptState',0,1,158,2,15
	.word	.L260,.L478
	.byte	7
	.word	.L272,.L189,.L91
	.byte	8
	.word	.L273,.L189,.L91
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L479
	.byte	7
	.word	.L275,.L189,.L90
	.byte	8
	.word	.L276,.L189,.L90
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L480
	.byte	0,0,0,0,7
	.word	.L279,.L91,.L92
	.byte	9
	.word	.L280,.L481
	.byte	10
	.word	.L282,.L91,.L92
	.byte	0,7
	.word	.L283,.L482,.L93
	.byte	9
	.word	.L285,.L483
	.byte	10
	.word	.L287,.L482,.L93
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_abbrev'
.L251:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_line'
.L252:
	.word	.L927-.L926
.L926:
	.half	3
	.word	.L929-.L928
.L928:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0,0,0,0,0
.L929:
	.byte	4,2,5,19,7,0,5,2
	.word	.L189
	.byte	3,141,5,1,5,17,9
	.half	.L581-.L189
	.byte	3,1,1,5,21,9
	.half	.L582-.L581
	.byte	1,5,5,9
	.half	.L583-.L582
	.byte	1,5,14,9
	.half	.L90-.L583
	.byte	3,8,1,5,10,9
	.half	.L930-.L90
	.byte	3,1,1,5,5,9
	.half	.L931-.L930
	.byte	3,1,1,4,3,5,24,9
	.half	.L91-.L931
	.byte	3,183,124,1,5,5,9
	.half	.L932-.L91
	.byte	1,4,1,5,49,9
	.half	.L92-.L932
	.byte	3,209,0,1,5,56,9
	.half	.L933-.L92
	.byte	1,5,37,9
	.half	.L934-.L933
	.byte	1,5,48,9
	.half	.L584-.L934
	.byte	3,1,1,5,42,9
	.half	.L935-.L584
	.byte	1,5,29,9
	.half	.L936-.L935
	.byte	1,5,32,9
	.half	.L586-.L936
	.byte	3,1,1,5,30,9
	.half	.L937-.L586
	.byte	1,5,44,9
	.half	.L938-.L937
	.byte	3,1,1,5,61,9
	.half	.L580-.L938
	.byte	1,5,37,9
	.half	.L939-.L580
	.byte	1,5,30,9
	.half	.L940-.L939
	.byte	1,4,2,5,5,9
	.half	.L482-.L940
	.byte	3,134,5,1,5,17,7,9
	.half	.L941-.L482
	.byte	3,2,1,4,1,5,5,9
	.half	.L93-.L941
	.byte	3,251,122,1,5,1,9
	.half	.L94-.L93
	.byte	3,1,1,7,9
	.half	.L254-.L94
	.byte	0,1,1
.L927:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_ranges'
.L253:
	.word	-1,.L189,0,.L254-.L189,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_info'
.L255:
	.word	518
	.half	3
	.word	.L256
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L258,.L257
	.byte	2
	.word	.L196
	.byte	3
	.byte	'Ifx_Fifo_endWrite',0,1,213,2,18
	.word	.L264
	.byte	1,1
	.word	.L193,.L484,.L192
	.byte	4
	.byte	'fifo',0,1,213,2,46
	.word	.L262,.L485
	.byte	4
	.byte	'count',0,1,213,2,62
	.word	.L264,.L486
	.byte	4
	.byte	'blockSize',0,1,213,2,79
	.word	.L264,.L487
	.byte	5
	.word	.L193,.L484
	.byte	6
	.byte	'interruptState',0,1,215,2,13
	.word	.L260,.L488
	.byte	7
	.word	.L272,.L193,.L129
	.byte	8
	.word	.L273,.L193,.L129
	.byte	6
	.byte	'enabled',0,2,149,5,13
	.word	.L260,.L489
	.byte	7
	.word	.L275,.L193,.L128
	.byte	8
	.word	.L276,.L193,.L128
	.byte	6
	.byte	'reg',0,2,141,5,17
	.word	.L277,.L490
	.byte	0,0,0,0,7
	.word	.L283,.L130,.L132
	.byte	9
	.word	.L285,.L491
	.byte	10
	.word	.L287,.L130,.L132
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_abbrev'
.L256:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_line'
.L257:
	.word	.L943-.L942
.L942:
	.half	3
	.word	.L945-.L944
.L944:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling/Ifx_Fifo.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L945:
	.byte	4,2,5,19,7,0,5,2
	.word	.L193
	.byte	3,141,5,1,5,17,9
	.half	.L610-.L193
	.byte	3,1,1,5,21,9
	.half	.L611-.L610
	.byte	1,5,5,9
	.half	.L612-.L611
	.byte	1,5,14,9
	.half	.L128-.L612
	.byte	3,8,1,5,10,9
	.half	.L946-.L128
	.byte	3,1,1,5,5,9
	.half	.L947-.L946
	.byte	3,1,1,4,1,5,17,9
	.half	.L129-.L947
	.byte	3,195,125,1,5,26,9
	.half	.L948-.L129
	.byte	1,5,47,9
	.half	.L949-.L948
	.byte	3,1,1,5,70,9
	.half	.L950-.L949
	.byte	1,5,34,9
	.half	.L951-.L950
	.byte	1,5,27,9
	.half	.L952-.L951
	.byte	1,5,21,9
	.half	.L953-.L952
	.byte	3,2,1,5,5,9
	.half	.L954-.L953
	.byte	1,5,21,7,9
	.half	.L955-.L954
	.byte	3,2,1,5,34,9
	.half	.L956-.L955
	.byte	1,5,25,9
	.half	.L957-.L956
	.byte	3,2,1,5,9,9
	.half	.L958-.L957
	.byte	1,5,40,7,9
	.half	.L959-.L958
	.byte	3,2,1,5,38,9
	.half	.L960-.L959
	.byte	1,5,40,9
	.half	.L961-.L960
	.byte	3,1,1,5,38,9
	.half	.L962-.L961
	.byte	1,4,2,5,5,9
	.half	.L130-.L962
	.byte	3,196,4,1,5,17,7,9
	.half	.L963-.L130
	.byte	3,2,1,4,1,5,18,9
	.half	.L132-.L963
	.byte	3,192,123,1,5,5,9
	.half	.L964-.L132
	.byte	1,5,1,9
	.half	.L133-.L964
	.byte	3,1,1,7,9
	.half	.L259-.L133
	.byte	0,1,1
.L943:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_ranges'
.L258:
	.word	-1,.L193,0,.L259-.L193,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_loc'
.L178:
	.word	-1,.L179,0,.L456-.L179
	.half	2
	.byte	138,0
	.word	0,0
.L460:
	.word	-1,.L179,.L507-.L179,.L508-.L179
	.half	1
	.byte	81
	.word	.L509-.L179,.L456-.L179
	.half	1
	.byte	82
	.word	0,0
.L458:
	.word	-1,.L179,0,.L503-.L179
	.half	1
	.byte	84
	.word	0,0
.L461:
	.word	0,0
.L465:
	.word	0,0
.L457:
	.word	-1,.L179,0,.L456-.L179
	.half	1
	.byte	100
	.word	0,0
.L463:
	.word	0,0
.L459:
	.word	-1,.L179,.L506-.L179,.L456-.L179
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L462:
	.word	-1,.L179,.L504-.L179,.L505-.L179
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L189,0,.L474-.L189
	.half	2
	.byte	138,0
	.word	0,0
.L477:
	.word	-1,.L189,.L584-.L189,.L585-.L189
	.half	1
	.byte	81
	.word	.L586-.L189,.L474-.L189
	.half	1
	.byte	82
	.word	0,0
.L476:
	.word	-1,.L189,0,.L580-.L189
	.half	1
	.byte	84
	.word	0,0
.L479:
	.word	0,0
.L483:
	.word	0,0
.L481:
	.word	0,0
.L475:
	.word	-1,.L189,0,.L474-.L189
	.half	1
	.byte	100
	.word	0,0
.L478:
	.word	-1,.L189,.L583-.L189,.L474-.L189
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L480:
	.word	-1,.L189,.L581-.L189,.L582-.L189
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_loc'
.L288:
	.word	-1,.L181,.L518-.L181,.L19-.L181
	.half	2
	.byte	144,33
	.word	.L20-.L181,.L12-.L181
	.half	2
	.byte	144,33
	.word	0,0
.L180:
	.word	-1,.L181,0,.L261-.L181
	.half	2
	.byte	138,0
	.word	0,0
.L265:
	.word	-1,.L181,0,.L510-.L181
	.half	1
	.byte	84
	.word	.L511-.L181,.L512-.L181
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L293:
	.word	0,0
.L317:
	.word	0,0
.L311:
	.word	0,0
.L274:
	.word	0,0
.L322:
	.word	0,0
.L300:
	.word	0,0
.L286:
	.word	0,0
.L263:
	.word	-1,.L181,0,.L261-.L181
	.half	1
	.byte	100
	.word	0,0
.L281:
	.word	0,0
.L269:
	.word	-1,.L181,.L510-.L181,.L29-.L181
	.half	1
	.byte	84
	.word	0,0
.L297:
	.word	-1,.L181,.L521-.L181,.L20-.L181
	.half	1
	.byte	95
	.word	0,0
.L321:
	.word	-1,.L181,.L528-.L181,.L529-.L181
	.half	1
	.byte	95
	.word	0,0
.L301:
	.word	-1,.L181,.L519-.L181,.L520-.L181
	.half	1
	.byte	81
	.word	0,0
.L323:
	.word	-1,.L181,.L526-.L181,.L527-.L181
	.half	1
	.byte	95
	.word	0,0
.L278:
	.word	-1,.L181,.L514-.L181,.L515-.L181
	.half	1
	.byte	95
	.word	0,0
.L268:
	.word	-1,.L181,.L513-.L181,.L11-.L181
	.half	1
	.byte	82
	.word	.L517-.L181,.L16-.L181
	.half	1
	.byte	82
	.word	.L12-.L181,.L261-.L181
	.half	1
	.byte	82
	.word	0,0
.L319:
	.word	-1,.L181,.L525-.L181,.L32-.L181
	.half	1
	.byte	95
	.word	.L529-.L181,.L31-.L181
	.half	1
	.byte	95
	.word	0,0
.L307:
	.word	-1,.L181,.L522-.L181,.L523-.L181
	.half	2
	.byte	144,33
	.word	0,0
.L324:
	.word	-1,.L181,.L530-.L181,.L531-.L181
	.half	2
	.byte	144,34
	.word	.L531-.L181,.L532-.L181
	.half	2
	.byte	144,32
	.word	0,0
.L304:
	.word	0,0
.L320:
	.word	-1,.L181,.L532-.L181,.L33-.L181
	.half	2
	.byte	144,32
	.word	0,0
.L296:
	.word	-1,.L181,.L523-.L181,.L524-.L181
	.half	2
	.byte	144,33
	.word	0,0
.L267:
	.word	-1,.L181,0,.L261-.L181
	.half	2
	.byte	144,35
	.word	0,0
.L291:
	.word	0,0
.L271:
	.word	-1,.L181,.L15-.L181,.L516-.L181
	.half	5
	.byte	144,32,157,32,0
	.word	.L16-.L181,.L29-.L181
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_loc'
.L337:
	.word	-1,.L191,.L587-.L191,.L104-.L191
	.half	2
	.byte	144,34
	.word	.L105-.L191,.L97-.L191
	.half	2
	.byte	144,34
	.word	0,0
.L190:
	.word	-1,.L191,0,.L326-.L191
	.half	2
	.byte	138,0
	.word	0,0
.L328:
	.word	-1,.L191,0,.L587-.L191
	.half	1
	.byte	84
	.word	.L588-.L191,.L589-.L191
	.half	1
	.byte	82
	.word	.L96-.L191,.L590-.L191
	.half	1
	.byte	82
	.word	.L101-.L191,.L594-.L191
	.half	1
	.byte	82
	.word	.L104-.L191,.L595-.L191
	.half	1
	.byte	84
	.word	0,0
.L339:
	.word	0,0
.L351:
	.word	0,0
.L347:
	.word	0,0
.L355:
	.word	0,0
.L342:
	.word	0,0
.L332:
	.word	0,0
.L336:
	.word	0,0
.L334:
	.word	0,0
.L327:
	.word	-1,.L191,0,.L326-.L191
	.half	1
	.byte	100
	.word	0,0
.L341:
	.word	-1,.L191,.L598-.L191,.L105-.L191
	.half	1
	.byte	95
	.word	0,0
.L354:
	.word	-1,.L191,.L604-.L191,.L605-.L191
	.half	1
	.byte	95
	.word	0,0
.L331:
	.word	-1,.L191,.L593-.L191,.L115-.L191
	.half	1
	.byte	83
	.word	0,0
.L333:
	.word	-1,.L191,.L591-.L191,.L592-.L191
	.half	1
	.byte	95
	.word	0,0
.L356:
	.word	-1,.L191,.L602-.L191,.L603-.L191
	.half	1
	.byte	95
	.word	0,0
.L343:
	.word	-1,.L191,.L596-.L191,.L597-.L191
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L330:
	.word	-1,.L191,.L589-.L191,.L96-.L191
	.half	1
	.byte	82
	.word	.L590-.L191,.L101-.L191
	.half	1
	.byte	82
	.word	.L97-.L191,.L326-.L191
	.half	1
	.byte	82
	.word	0,0
.L352:
	.word	-1,.L191,.L601-.L191,.L118-.L191
	.half	1
	.byte	95
	.word	.L605-.L191,.L117-.L191
	.half	1
	.byte	95
	.word	0,0
.L357:
	.word	-1,.L191,.L606-.L191,.L607-.L191
	.half	2
	.byte	144,33
	.word	.L607-.L191,.L608-.L191
	.half	2
	.byte	144,32
	.word	0,0
.L345:
	.word	-1,.L191,.L595-.L191,.L599-.L191
	.half	2
	.byte	144,34
	.word	.L599-.L191,.L600-.L191
	.half	2
	.byte	144,32
	.word	0,0
.L344:
	.word	0,0
.L340:
	.word	-1,.L191,.L600-.L191,.L105-.L191
	.half	2
	.byte	144,32
	.word	0,0
.L353:
	.word	-1,.L191,.L608-.L191,.L119-.L191
	.half	2
	.byte	144,32
	.word	0,0
.L338:
	.word	0,0
.L329:
	.word	-1,.L191,0,.L326-.L191
	.half	2
	.byte	144,35
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_loc'
.L186:
	.word	-1,.L187,0,.L359-.L187
	.half	2
	.byte	138,0
	.word	0,0
.L362:
	.word	0,0
.L365:
	.word	0,0
.L360:
	.word	-1,.L187,0,.L359-.L187
	.half	1
	.byte	100
	.word	0,0
.L361:
	.word	-1,.L187,.L579-.L187,.L359-.L187
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L363:
	.word	-1,.L187,.L577-.L187,.L578-.L187
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_loc'
.L172:
	.word	-1,.L173,0,.L366-.L173
	.half	2
	.byte	138,0
	.word	0,0
.L368:
	.word	-1,.L173,0,.L492-.L173
	.half	1
	.byte	85
	.word	.L494-.L173,.L366-.L173
	.half	1
	.byte	95
	.word	0,0
.L369:
	.word	-1,.L173,.L492-.L173,.L366-.L173
	.half	1
	.byte	98
	.word	.L496-.L173,.L2-.L173
	.half	1
	.byte	100
	.word	0,0
.L367:
	.word	-1,.L173,0,.L493-.L173
	.half	1
	.byte	84
	.word	.L495-.L173,.L366-.L173
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L175,0,.L370-.L175
	.half	2
	.byte	138,0
	.word	0,0
.L371:
	.word	-1,.L175,0,.L497-.L175
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_loc'
.L192:
	.word	-1,.L193,0,.L484-.L193
	.half	2
	.byte	138,0
	.word	0,0
.L487:
	.word	-1,.L193,0,.L484-.L193
	.half	1
	.byte	85
	.word	0,0
.L486:
	.word	-1,.L193,0,.L609-.L193
	.half	1
	.byte	84
	.word	0,0
.L489:
	.word	0,0
.L491:
	.word	0,0
.L485:
	.word	-1,.L193,0,.L484-.L193
	.half	1
	.byte	100
	.word	0,0
.L488:
	.word	-1,.L193,.L612-.L193,.L484-.L193
	.half	1
	.byte	81
	.word	0,0
.L490:
	.word	-1,.L193,.L610-.L193,.L611-.L193
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_loc'
.L176:
	.word	-1,.L177,0,.L372-.L177
	.half	2
	.byte	138,0
	.word	0,0
.L374:
	.word	-1,.L177,0,.L372-.L177
	.half	1
	.byte	100
	.word	.L500-.L177,.L501-.L177
	.half	1
	.byte	95
	.word	.L502-.L177,.L372-.L177
	.half	1
	.byte	98
	.word	0,0
.L376:
	.word	-1,.L177,0,.L372-.L177
	.half	1
	.byte	85
	.word	0,0
.L377:
	.word	0,0
.L375:
	.word	-1,.L177,0,.L498-.L177
	.half	1
	.byte	84
	.word	.L499-.L177,.L372-.L177
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_loc'
.L383:
	.word	-1,.L185,.L544-.L185,.L49-.L185
	.half	2
	.byte	144,37
	.word	.L50-.L185,.L48-.L185
	.half	2
	.byte	144,37
	.word	0,0
.L184:
	.word	-1,.L185,0,.L537-.L185
	.half	2
	.byte	138,0
	.word	.L537-.L185,.L378-.L185
	.half	2
	.byte	138,8
	.word	.L378-.L185,.L378-.L185
	.half	2
	.byte	138,0
	.word	0,0
.L387:
	.word	-1,.L185,.L543-.L185,.L378-.L185
	.half	1
	.byte	89
	.word	0,0
.L384:
	.word	-1,.L185,.L552-.L185,.L554-.L185
	.half	1
	.byte	82
	.word	.L555-.L185,.L556-.L185
	.half	1
	.byte	95
	.word	.L554-.L185,.L540-.L185
	.half	1
	.byte	84
	.word	0,0
.L386:
	.word	-1,.L185,0,.L378-.L185
	.half	2
	.byte	145,120
	.word	0,0
.L381:
	.word	-1,.L185,0,.L58-.L185
	.half	1
	.byte	84
	.word	.L541-.L185,.L542-.L185
	.half	1
	.byte	88
	.word	.L553-.L185,.L552-.L185
	.half	1
	.byte	84
	.word	.L542-.L185,.L59-.L185
	.half	1
	.byte	82
	.word	.L59-.L185,.L378-.L185
	.half	1
	.byte	88
	.word	.L576-.L185,.L378-.L185
	.half	1
	.byte	82
	.word	0,0
.L380:
	.word	-1,.L185,0,.L58-.L185
	.half	1
	.byte	101
	.word	.L539-.L185,.L540-.L185
	.half	1
	.byte	108
	.word	.L557-.L185,.L540-.L185
	.half	1
	.byte	101
	.word	.L540-.L185,.L542-.L185
	.half	1
	.byte	98
	.word	.L558-.L185,.L378-.L185
	.half	1
	.byte	108
	.word	0,0
.L390:
	.word	0,0
.L401:
	.word	0,0
.L398:
	.word	0,0
.L413:
	.word	0,0
.L405:
	.word	0,0
.L393:
	.word	0,0
.L399:
	.word	0,0
.L379:
	.word	-1,.L185,0,.L58-.L185
	.half	1
	.byte	100
	.word	.L538-.L185,.L378-.L185
	.half	1
	.byte	111
	.word	.L551-.L185,.L552-.L185
	.half	1
	.byte	100
	.word	.L559-.L185,.L542-.L185
	.half	1
	.byte	100
	.word	0,0
.L412:
	.word	-1,.L185,.L571-.L185,.L572-.L185
	.half	1
	.byte	95
	.word	0,0
.L392:
	.word	-1,.L185,.L547-.L185,.L50-.L185
	.half	1
	.byte	95
	.word	0,0
.L404:
	.word	-1,.L185,.L563-.L185,.L564-.L185
	.half	1
	.byte	95
	.word	0,0
.L394:
	.word	-1,.L185,.L545-.L185,.L546-.L185
	.half	1
	.byte	95
	.word	0,0
.L414:
	.word	-1,.L185,.L569-.L185,.L570-.L185
	.half	1
	.byte	95
	.word	0,0
.L406:
	.word	-1,.L185,.L561-.L185,.L562-.L185
	.half	1
	.byte	95
	.word	0,0
.L410:
	.word	-1,.L185,.L568-.L185,.L76-.L185
	.half	1
	.byte	95
	.word	.L572-.L185,.L75-.L185
	.half	1
	.byte	95
	.word	0,0
.L402:
	.word	-1,.L185,.L560-.L185,.L61-.L185
	.half	1
	.byte	95
	.word	.L564-.L185,.L60-.L185
	.half	1
	.byte	95
	.word	.L70-.L185,.L73-.L185
	.half	1
	.byte	95
	.word	0,0
.L407:
	.word	-1,.L185,.L565-.L185,.L566-.L185
	.half	2
	.byte	144,33
	.word	.L566-.L185,.L567-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L396:
	.word	-1,.L185,.L548-.L185,.L549-.L185
	.half	2
	.byte	144,33
	.word	.L549-.L185,.L550-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L415:
	.word	-1,.L185,.L573-.L185,.L574-.L185
	.half	2
	.byte	144,33
	.word	.L574-.L185,.L575-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L395:
	.word	0,0
.L391:
	.word	-1,.L185,.L550-.L185,.L50-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L411:
	.word	-1,.L185,.L575-.L185,.L77-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L403:
	.word	-1,.L185,.L567-.L185,.L62-.L185
	.half	2
	.byte	144,32
	.word	0,0
.L382:
	.word	-1,.L185,0,.L58-.L185
	.half	2
	.byte	144,35
	.word	0,0
.L389:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_loc'
.L182:
	.word	-1,.L183,0,.L466-.L183
	.half	2
	.byte	138,0
	.word	0,0
.L469:
	.word	-1,.L183,0,.L466-.L183
	.half	1
	.byte	85
	.word	0,0
.L468:
	.word	-1,.L183,0,.L533-.L183
	.half	1
	.byte	84
	.word	0,0
.L471:
	.word	0,0
.L473:
	.word	0,0
.L467:
	.word	-1,.L183,0,.L466-.L183
	.half	1
	.byte	100
	.word	0,0
.L470:
	.word	-1,.L183,.L536-.L183,.L466-.L183
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L472:
	.word	-1,.L183,.L534-.L183,.L535-.L183
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_loc'
.L423:
	.word	-1,.L195,.L620-.L195,.L135-.L195
	.half	2
	.byte	144,37
	.word	.L136-.L195,.L134-.L195
	.half	2
	.byte	144,37
	.word	0,0
.L194:
	.word	-1,.L195,0,.L613-.L195
	.half	2
	.byte	138,0
	.word	.L613-.L195,.L417-.L195
	.half	2
	.byte	138,8
	.word	.L417-.L195,.L417-.L195
	.half	2
	.byte	138,0
	.word	0,0
.L426:
	.word	-1,.L195,.L619-.L195,.L417-.L195
	.half	1
	.byte	89
	.word	0,0
.L424:
	.word	-1,.L195,.L628-.L195,.L630-.L195
	.half	1
	.byte	82
	.word	.L631-.L195,.L632-.L195
	.half	1
	.byte	95
	.word	.L630-.L195,.L616-.L195
	.half	1
	.byte	84
	.word	0,0
.L425:
	.word	-1,.L195,0,.L417-.L195
	.half	2
	.byte	145,120
	.word	0,0
.L421:
	.word	-1,.L195,0,.L144-.L195
	.half	1
	.byte	84
	.word	.L617-.L195,.L618-.L195
	.half	1
	.byte	88
	.word	.L629-.L195,.L628-.L195
	.half	1
	.byte	84
	.word	.L618-.L195,.L145-.L195
	.half	1
	.byte	82
	.word	.L145-.L195,.L417-.L195
	.half	1
	.byte	88
	.word	.L652-.L195,.L417-.L195
	.half	1
	.byte	82
	.word	0,0
.L420:
	.word	-1,.L195,0,.L144-.L195
	.half	1
	.byte	101
	.word	.L615-.L195,.L616-.L195
	.half	1
	.byte	108
	.word	.L633-.L195,.L616-.L195
	.half	1
	.byte	101
	.word	.L616-.L195,.L618-.L195
	.half	1
	.byte	98
	.word	.L634-.L195,.L417-.L195
	.half	1
	.byte	108
	.word	0,0
.L429:
	.word	0,0
.L440:
	.word	0,0
.L437:
	.word	0,0
.L432:
	.word	0,0
.L444:
	.word	0,0
.L452:
	.word	0,0
.L438:
	.word	0,0
.L418:
	.word	-1,.L195,0,.L144-.L195
	.half	1
	.byte	100
	.word	.L614-.L195,.L417-.L195
	.half	1
	.byte	111
	.word	.L627-.L195,.L628-.L195
	.half	1
	.byte	100
	.word	.L635-.L195,.L618-.L195
	.half	1
	.byte	100
	.word	0,0
.L443:
	.word	-1,.L195,.L639-.L195,.L640-.L195
	.half	1
	.byte	95
	.word	0,0
.L451:
	.word	-1,.L195,.L647-.L195,.L648-.L195
	.half	1
	.byte	95
	.word	0,0
.L431:
	.word	-1,.L195,.L623-.L195,.L136-.L195
	.half	1
	.byte	95
	.word	0,0
.L433:
	.word	-1,.L195,.L621-.L195,.L622-.L195
	.half	1
	.byte	95
	.word	0,0
.L445:
	.word	-1,.L195,.L637-.L195,.L638-.L195
	.half	1
	.byte	95
	.word	0,0
.L453:
	.word	-1,.L195,.L645-.L195,.L646-.L195
	.half	1
	.byte	95
	.word	0,0
.L449:
	.word	-1,.L195,.L644-.L195,.L162-.L195
	.half	1
	.byte	95
	.word	.L648-.L195,.L161-.L195
	.half	1
	.byte	95
	.word	0,0
.L441:
	.word	-1,.L195,.L636-.L195,.L147-.L195
	.half	1
	.byte	95
	.word	.L640-.L195,.L146-.L195
	.half	1
	.byte	95
	.word	.L156-.L195,.L159-.L195
	.half	1
	.byte	95
	.word	0,0
.L435:
	.word	-1,.L195,.L624-.L195,.L625-.L195
	.half	2
	.byte	144,33
	.word	.L625-.L195,.L626-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L454:
	.word	-1,.L195,.L649-.L195,.L650-.L195
	.half	2
	.byte	144,33
	.word	.L650-.L195,.L651-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L446:
	.word	-1,.L195,.L641-.L195,.L642-.L195
	.half	2
	.byte	144,33
	.word	.L642-.L195,.L643-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L434:
	.word	0,0
.L442:
	.word	-1,.L195,.L643-.L195,.L148-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L450:
	.word	-1,.L195,.L651-.L195,.L163-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L430:
	.word	-1,.L195,.L626-.L195,.L136-.L195
	.half	2
	.byte	144,32
	.word	0,0
.L428:
	.word	0,0
.L422:
	.word	-1,.L195,0,.L144-.L195
	.half	2
	.byte	144,35
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L965:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_create')
	.sect	'.debug_frame'
	.word	12
	.word	.L965,.L173,.L366-.L173
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_destroy')
	.sect	'.debug_frame'
	.word	12
	.word	.L965,.L175,.L370-.L175
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_init')
	.sect	'.debug_frame'
	.word	20
	.word	.L965,.L177,.L372-.L177
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_beginRead')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L179,.L456-.L179
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_canReadCount')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L181,.L261-.L181
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_readEnd')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L183,.L466-.L183
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_read')
	.sect	'.debug_frame'
	.word	36
	.word	.L965,.L185,.L378-.L185
	.byte	4
	.word	(.L537-.L185)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L378-.L537)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_clear')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L187,.L359-.L187
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_beginWrite')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L189,.L474-.L189
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_canWriteCount')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L191,.L326-.L191
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_endWrite')
	.sect	'.debug_frame'
	.word	24
	.word	.L965,.L193,.L484-.L193
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Ifx_Fifo_write')
	.sect	'.debug_frame'
	.word	36
	.word	.L965,.L195,.L417-.L195
	.byte	4
	.word	(.L613-.L195)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L417-.L613)/2
	.byte	19,0,8,26,0,0
	; Module end
