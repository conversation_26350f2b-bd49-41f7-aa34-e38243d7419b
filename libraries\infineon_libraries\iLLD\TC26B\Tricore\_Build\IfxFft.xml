<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxFft" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Fft/Std/IfxFft.c</iLLD:file>
  <iLLD:file class="mchal">Fft/Fft/IfxFft_Fft.c</iLLD:file>
  <iLLD:file class="mchal">Dma/Dma/IfxDma_Dma.c</iLLD:file>
</iLLD:filelist>
