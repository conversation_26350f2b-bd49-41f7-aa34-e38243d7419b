/**
 * \file IfxMsc_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Msc_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Msc
 * 
 */
#ifndef IFXMSC_BF_H
#define IFXMSC_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Msc_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_MSC_ABC_Bits.ABB */
#define IFX_MSC_ABC_ABB_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.ABB */
#define IFX_MSC_ABC_ABB_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.ABB */
#define IFX_MSC_ABC_ABB_OFF (31u)

/** \brief  Length for Ifx_MSC_ABC_Bits.CLKSEL */
#define IFX_MSC_ABC_CLKSEL_LEN (3u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.CLKSEL */
#define IFX_MSC_ABC_CLKSEL_MSK (0x7u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.CLKSEL */
#define IFX_MSC_ABC_CLKSEL_OFF (27u)

/** \brief  Length for Ifx_MSC_ABC_Bits.HIGH */
#define IFX_MSC_ABC_HIGH_LEN (4u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.HIGH */
#define IFX_MSC_ABC_HIGH_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_ABC_Bits.HIGH */
#define IFX_MSC_ABC_HIGH_OFF (4u)

/** \brief  Length for Ifx_MSC_ABC_Bits.LOW */
#define IFX_MSC_ABC_LOW_LEN (4u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.LOW */
#define IFX_MSC_ABC_LOW_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_ABC_Bits.LOW */
#define IFX_MSC_ABC_LOW_OFF (0u)

/** \brief  Length for Ifx_MSC_ABC_Bits.NDA */
#define IFX_MSC_ABC_NDA_LEN (3u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.NDA */
#define IFX_MSC_ABC_NDA_MSK (0x7u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.NDA */
#define IFX_MSC_ABC_NDA_OFF (16u)

/** \brief  Length for Ifx_MSC_ABC_Bits.OASR */
#define IFX_MSC_ABC_OASR_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.OASR */
#define IFX_MSC_ABC_OASR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.OASR */
#define IFX_MSC_ABC_OASR_OFF (10u)

/** \brief  Length for Ifx_MSC_ABC_Bits.OFM */
#define IFX_MSC_ABC_OFM_LEN (2u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.OFM */
#define IFX_MSC_ABC_OFM_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.OFM */
#define IFX_MSC_ABC_OFM_OFF (13u)

/** \brief  Length for Ifx_MSC_ABC_Bits.OIE */
#define IFX_MSC_ABC_OIE_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.OIE */
#define IFX_MSC_ABC_OIE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.OIE */
#define IFX_MSC_ABC_OIE_OFF (15u)

/** \brief  Length for Ifx_MSC_ABC_Bits.OIP */
#define IFX_MSC_ABC_OIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.OIP */
#define IFX_MSC_ABC_OIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.OIP */
#define IFX_MSC_ABC_OIP_OFF (8u)

/** \brief  Length for Ifx_MSC_ABC_Bits.OVF */
#define IFX_MSC_ABC_OVF_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.OVF */
#define IFX_MSC_ABC_OVF_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.OVF */
#define IFX_MSC_ABC_OVF_OFF (12u)

/** \brief  Length for Ifx_MSC_ABC_Bits.UASR */
#define IFX_MSC_ABC_UASR_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.UASR */
#define IFX_MSC_ABC_UASR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.UASR */
#define IFX_MSC_ABC_UASR_OFF (21u)

/** \brief  Length for Ifx_MSC_ABC_Bits.UFM */
#define IFX_MSC_ABC_UFM_LEN (2u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.UFM */
#define IFX_MSC_ABC_UFM_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.UFM */
#define IFX_MSC_ABC_UFM_OFF (24u)

/** \brief  Length for Ifx_MSC_ABC_Bits.UIE */
#define IFX_MSC_ABC_UIE_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.UIE */
#define IFX_MSC_ABC_UIE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.UIE */
#define IFX_MSC_ABC_UIE_OFF (26u)

/** \brief  Length for Ifx_MSC_ABC_Bits.UIP */
#define IFX_MSC_ABC_UIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.UIP */
#define IFX_MSC_ABC_UIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.UIP */
#define IFX_MSC_ABC_UIP_OFF (19u)

/** \brief  Length for Ifx_MSC_ABC_Bits.UNF */
#define IFX_MSC_ABC_UNF_LEN (1u)

/** \brief  Mask for Ifx_MSC_ABC_Bits.UNF */
#define IFX_MSC_ABC_UNF_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ABC_Bits.UNF */
#define IFX_MSC_ABC_UNF_OFF (23u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN0 */
#define IFX_MSC_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN0 */
#define IFX_MSC_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN0 */
#define IFX_MSC_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN10 */
#define IFX_MSC_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN10 */
#define IFX_MSC_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN10 */
#define IFX_MSC_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN11 */
#define IFX_MSC_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN11 */
#define IFX_MSC_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN11 */
#define IFX_MSC_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN12 */
#define IFX_MSC_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN12 */
#define IFX_MSC_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN12 */
#define IFX_MSC_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN13 */
#define IFX_MSC_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN13 */
#define IFX_MSC_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN13 */
#define IFX_MSC_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN14 */
#define IFX_MSC_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN14 */
#define IFX_MSC_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN14 */
#define IFX_MSC_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN15 */
#define IFX_MSC_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN15 */
#define IFX_MSC_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN15 */
#define IFX_MSC_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN16 */
#define IFX_MSC_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN16 */
#define IFX_MSC_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN16 */
#define IFX_MSC_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN17 */
#define IFX_MSC_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN17 */
#define IFX_MSC_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN17 */
#define IFX_MSC_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN18 */
#define IFX_MSC_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN18 */
#define IFX_MSC_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN18 */
#define IFX_MSC_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN19 */
#define IFX_MSC_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN19 */
#define IFX_MSC_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN19 */
#define IFX_MSC_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN1 */
#define IFX_MSC_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN1 */
#define IFX_MSC_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN1 */
#define IFX_MSC_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN20 */
#define IFX_MSC_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN20 */
#define IFX_MSC_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN20 */
#define IFX_MSC_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN21 */
#define IFX_MSC_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN21 */
#define IFX_MSC_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN21 */
#define IFX_MSC_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN22 */
#define IFX_MSC_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN22 */
#define IFX_MSC_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN22 */
#define IFX_MSC_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN23 */
#define IFX_MSC_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN23 */
#define IFX_MSC_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN23 */
#define IFX_MSC_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN24 */
#define IFX_MSC_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN24 */
#define IFX_MSC_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN24 */
#define IFX_MSC_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN25 */
#define IFX_MSC_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN25 */
#define IFX_MSC_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN25 */
#define IFX_MSC_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN26 */
#define IFX_MSC_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN26 */
#define IFX_MSC_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN26 */
#define IFX_MSC_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN27 */
#define IFX_MSC_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN27 */
#define IFX_MSC_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN27 */
#define IFX_MSC_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN28 */
#define IFX_MSC_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN28 */
#define IFX_MSC_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN28 */
#define IFX_MSC_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN29 */
#define IFX_MSC_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN29 */
#define IFX_MSC_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN29 */
#define IFX_MSC_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN2 */
#define IFX_MSC_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN2 */
#define IFX_MSC_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN2 */
#define IFX_MSC_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN30 */
#define IFX_MSC_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN30 */
#define IFX_MSC_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN30 */
#define IFX_MSC_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN31 */
#define IFX_MSC_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN31 */
#define IFX_MSC_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN31 */
#define IFX_MSC_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN3 */
#define IFX_MSC_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN3 */
#define IFX_MSC_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN3 */
#define IFX_MSC_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN4 */
#define IFX_MSC_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN4 */
#define IFX_MSC_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN4 */
#define IFX_MSC_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN5 */
#define IFX_MSC_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN5 */
#define IFX_MSC_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN5 */
#define IFX_MSC_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN6 */
#define IFX_MSC_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN6 */
#define IFX_MSC_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN6 */
#define IFX_MSC_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN7 */
#define IFX_MSC_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN7 */
#define IFX_MSC_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN7 */
#define IFX_MSC_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN8 */
#define IFX_MSC_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN8 */
#define IFX_MSC_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN8 */
#define IFX_MSC_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_MSC_ACCEN0_Bits.EN9 */
#define IFX_MSC_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_MSC_ACCEN0_Bits.EN9 */
#define IFX_MSC_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ACCEN0_Bits.EN9 */
#define IFX_MSC_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_MSC_CLC_Bits.DISR */
#define IFX_MSC_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_MSC_CLC_Bits.DISR */
#define IFX_MSC_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_CLC_Bits.DISR */
#define IFX_MSC_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_MSC_CLC_Bits.DISS */
#define IFX_MSC_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_MSC_CLC_Bits.DISS */
#define IFX_MSC_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_CLC_Bits.DISS */
#define IFX_MSC_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_MSC_CLC_Bits.EDIS */
#define IFX_MSC_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_MSC_CLC_Bits.EDIS */
#define IFX_MSC_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_CLC_Bits.EDIS */
#define IFX_MSC_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_MSC_DC_Bits.DCH */
#define IFX_MSC_DC_DCH_LEN (16u)

/** \brief  Mask for Ifx_MSC_DC_Bits.DCH */
#define IFX_MSC_DC_DCH_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DC_Bits.DCH */
#define IFX_MSC_DC_DCH_OFF (16u)

/** \brief  Length for Ifx_MSC_DC_Bits.DCL */
#define IFX_MSC_DC_DCL_LEN (16u)

/** \brief  Mask for Ifx_MSC_DC_Bits.DCL */
#define IFX_MSC_DC_DCL_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DC_Bits.DCL */
#define IFX_MSC_DC_DCL_OFF (0u)

/** \brief  Length for Ifx_MSC_DD_Bits.DDH */
#define IFX_MSC_DD_DDH_LEN (16u)

/** \brief  Mask for Ifx_MSC_DD_Bits.DDH */
#define IFX_MSC_DD_DDH_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DD_Bits.DDH */
#define IFX_MSC_DD_DDH_OFF (16u)

/** \brief  Length for Ifx_MSC_DD_Bits.DDL */
#define IFX_MSC_DD_DDL_LEN (16u)

/** \brief  Mask for Ifx_MSC_DD_Bits.DDL */
#define IFX_MSC_DD_DDL_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DD_Bits.DDL */
#define IFX_MSC_DD_DDL_OFF (0u)

/** \brief  Length for Ifx_MSC_DDE_Bits.DDHE */
#define IFX_MSC_DDE_DDHE_LEN (16u)

/** \brief  Mask for Ifx_MSC_DDE_Bits.DDHE */
#define IFX_MSC_DDE_DDHE_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DDE_Bits.DDHE */
#define IFX_MSC_DDE_DDHE_OFF (16u)

/** \brief  Length for Ifx_MSC_DDE_Bits.DDLE */
#define IFX_MSC_DDE_DDLE_LEN (16u)

/** \brief  Mask for Ifx_MSC_DDE_Bits.DDLE */
#define IFX_MSC_DDE_DDLE_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DDE_Bits.DDLE */
#define IFX_MSC_DDE_DDLE_OFF (0u)

/** \brief  Length for Ifx_MSC_DDM_Bits.DDHM */
#define IFX_MSC_DDM_DDHM_LEN (16u)

/** \brief  Mask for Ifx_MSC_DDM_Bits.DDHM */
#define IFX_MSC_DDM_DDHM_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DDM_Bits.DDHM */
#define IFX_MSC_DDM_DDHM_OFF (16u)

/** \brief  Length for Ifx_MSC_DDM_Bits.DDLM */
#define IFX_MSC_DDM_DDLM_LEN (16u)

/** \brief  Mask for Ifx_MSC_DDM_Bits.DDLM */
#define IFX_MSC_DDM_DDLM_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_DDM_Bits.DDLM */
#define IFX_MSC_DDM_DDLM_OFF (0u)

/** \brief  Length for Ifx_MSC_DSC_Bits.CP */
#define IFX_MSC_DSC_CP_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.CP */
#define IFX_MSC_DSC_CP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.CP */
#define IFX_MSC_DSC_CP_OFF (1u)

/** \brief  Length for Ifx_MSC_DSC_Bits.DP */
#define IFX_MSC_DSC_DP_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.DP */
#define IFX_MSC_DSC_DP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.DP */
#define IFX_MSC_DSC_DP_OFF (2u)

/** \brief  Length for Ifx_MSC_DSC_Bits.DSDIS */
#define IFX_MSC_DSC_DSDIS_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.DSDIS */
#define IFX_MSC_DSC_DSDIS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.DSDIS */
#define IFX_MSC_DSC_DSDIS_OFF (15u)

/** \brief  Length for Ifx_MSC_DSC_Bits.ENSELH */
#define IFX_MSC_DSC_ENSELH_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.ENSELH */
#define IFX_MSC_DSC_ENSELH_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.ENSELH */
#define IFX_MSC_DSC_ENSELH_OFF (14u)

/** \brief  Length for Ifx_MSC_DSC_Bits.ENSELL */
#define IFX_MSC_DSC_ENSELL_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.ENSELL */
#define IFX_MSC_DSC_ENSELL_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.ENSELL */
#define IFX_MSC_DSC_ENSELL_OFF (13u)

/** \brief  Length for Ifx_MSC_DSC_Bits.NBC */
#define IFX_MSC_DSC_NBC_LEN (6u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.NBC */
#define IFX_MSC_DSC_NBC_MSK (0x3fu)

/** \brief  Offset for Ifx_MSC_DSC_Bits.NBC */
#define IFX_MSC_DSC_NBC_OFF (16u)

/** \brief  Length for Ifx_MSC_DSC_Bits.NDBH */
#define IFX_MSC_DSC_NDBH_LEN (5u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.NDBH */
#define IFX_MSC_DSC_NDBH_MSK (0x1fu)

/** \brief  Offset for Ifx_MSC_DSC_Bits.NDBH */
#define IFX_MSC_DSC_NDBH_OFF (8u)

/** \brief  Length for Ifx_MSC_DSC_Bits.NDBL */
#define IFX_MSC_DSC_NDBL_LEN (5u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.NDBL */
#define IFX_MSC_DSC_NDBL_MSK (0x1fu)

/** \brief  Offset for Ifx_MSC_DSC_Bits.NDBL */
#define IFX_MSC_DSC_NDBL_OFF (3u)

/** \brief  Length for Ifx_MSC_DSC_Bits.PPD */
#define IFX_MSC_DSC_PPD_LEN (5u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.PPD */
#define IFX_MSC_DSC_PPD_MSK (0x1fu)

/** \brief  Offset for Ifx_MSC_DSC_Bits.PPD */
#define IFX_MSC_DSC_PPD_OFF (24u)

/** \brief  Length for Ifx_MSC_DSC_Bits.TM */
#define IFX_MSC_DSC_TM_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSC_Bits.TM */
#define IFX_MSC_DSC_TM_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSC_Bits.TM */
#define IFX_MSC_DSC_TM_OFF (0u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.CCF */
#define IFX_MSC_DSCE_CCF_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.CCF */
#define IFX_MSC_DSCE_CCF_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.CCF */
#define IFX_MSC_DSCE_CCF_OFF (15u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.CDCM */
#define IFX_MSC_DSCE_CDCM_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.CDCM */
#define IFX_MSC_DSCE_CDCM_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.CDCM */
#define IFX_MSC_DSCE_CDCM_OFF (31u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.EXEN */
#define IFX_MSC_DSCE_EXEN_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.EXEN */
#define IFX_MSC_DSCE_EXEN_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.EXEN */
#define IFX_MSC_DSCE_EXEN_OFF (14u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.INJENP0 */
#define IFX_MSC_DSCE_INJENP0_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.INJENP0 */
#define IFX_MSC_DSCE_INJENP0_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.INJENP0 */
#define IFX_MSC_DSCE_INJENP0_OFF (16u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.INJENP1 */
#define IFX_MSC_DSCE_INJENP1_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.INJENP1 */
#define IFX_MSC_DSCE_INJENP1_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.INJENP1 */
#define IFX_MSC_DSCE_INJENP1_OFF (24u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.INJPOSP0 */
#define IFX_MSC_DSCE_INJPOSP0_LEN (6u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.INJPOSP0 */
#define IFX_MSC_DSCE_INJPOSP0_MSK (0x3fu)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.INJPOSP0 */
#define IFX_MSC_DSCE_INJPOSP0_OFF (17u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.INJPOSP1 */
#define IFX_MSC_DSCE_INJPOSP1_LEN (6u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.INJPOSP1 */
#define IFX_MSC_DSCE_INJPOSP1_MSK (0x3fu)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.INJPOSP1 */
#define IFX_MSC_DSCE_INJPOSP1_OFF (25u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.NDBHE */
#define IFX_MSC_DSCE_NDBHE_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.NDBHE */
#define IFX_MSC_DSCE_NDBHE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.NDBHE */
#define IFX_MSC_DSCE_NDBHE_OFF (0u)

/** \brief  Length for Ifx_MSC_DSCE_Bits.NDBLE */
#define IFX_MSC_DSCE_NDBLE_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSCE_Bits.NDBLE */
#define IFX_MSC_DSCE_NDBLE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSCE_Bits.NDBLE */
#define IFX_MSC_DSCE_NDBLE_OFF (1u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH0 */
#define IFX_MSC_DSDSH_SH0_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH0 */
#define IFX_MSC_DSDSH_SH0_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH0 */
#define IFX_MSC_DSDSH_SH0_OFF (0u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH10 */
#define IFX_MSC_DSDSH_SH10_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH10 */
#define IFX_MSC_DSDSH_SH10_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH10 */
#define IFX_MSC_DSDSH_SH10_OFF (20u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH11 */
#define IFX_MSC_DSDSH_SH11_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH11 */
#define IFX_MSC_DSDSH_SH11_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH11 */
#define IFX_MSC_DSDSH_SH11_OFF (22u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH12 */
#define IFX_MSC_DSDSH_SH12_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH12 */
#define IFX_MSC_DSDSH_SH12_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH12 */
#define IFX_MSC_DSDSH_SH12_OFF (24u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH13 */
#define IFX_MSC_DSDSH_SH13_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH13 */
#define IFX_MSC_DSDSH_SH13_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH13 */
#define IFX_MSC_DSDSH_SH13_OFF (26u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH14 */
#define IFX_MSC_DSDSH_SH14_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH14 */
#define IFX_MSC_DSDSH_SH14_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH14 */
#define IFX_MSC_DSDSH_SH14_OFF (28u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH15 */
#define IFX_MSC_DSDSH_SH15_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH15 */
#define IFX_MSC_DSDSH_SH15_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH15 */
#define IFX_MSC_DSDSH_SH15_OFF (30u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH1 */
#define IFX_MSC_DSDSH_SH1_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH1 */
#define IFX_MSC_DSDSH_SH1_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH1 */
#define IFX_MSC_DSDSH_SH1_OFF (2u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH2 */
#define IFX_MSC_DSDSH_SH2_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH2 */
#define IFX_MSC_DSDSH_SH2_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH2 */
#define IFX_MSC_DSDSH_SH2_OFF (4u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH3 */
#define IFX_MSC_DSDSH_SH3_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH3 */
#define IFX_MSC_DSDSH_SH3_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH3 */
#define IFX_MSC_DSDSH_SH3_OFF (6u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH4 */
#define IFX_MSC_DSDSH_SH4_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH4 */
#define IFX_MSC_DSDSH_SH4_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH4 */
#define IFX_MSC_DSDSH_SH4_OFF (8u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH5 */
#define IFX_MSC_DSDSH_SH5_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH5 */
#define IFX_MSC_DSDSH_SH5_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH5 */
#define IFX_MSC_DSDSH_SH5_OFF (10u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH6 */
#define IFX_MSC_DSDSH_SH6_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH6 */
#define IFX_MSC_DSDSH_SH6_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH6 */
#define IFX_MSC_DSDSH_SH6_OFF (12u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH7 */
#define IFX_MSC_DSDSH_SH7_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH7 */
#define IFX_MSC_DSDSH_SH7_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH7 */
#define IFX_MSC_DSDSH_SH7_OFF (14u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH8 */
#define IFX_MSC_DSDSH_SH8_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH8 */
#define IFX_MSC_DSDSH_SH8_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH8 */
#define IFX_MSC_DSDSH_SH8_OFF (16u)

/** \brief  Length for Ifx_MSC_DSDSH_Bits.SH9 */
#define IFX_MSC_DSDSH_SH9_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSH_Bits.SH9 */
#define IFX_MSC_DSDSH_SH9_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSH_Bits.SH9 */
#define IFX_MSC_DSDSH_SH9_OFF (18u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH16 */
#define IFX_MSC_DSDSHE_SH16_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH16 */
#define IFX_MSC_DSDSHE_SH16_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH16 */
#define IFX_MSC_DSDSHE_SH16_OFF (0u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH17 */
#define IFX_MSC_DSDSHE_SH17_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH17 */
#define IFX_MSC_DSDSHE_SH17_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH17 */
#define IFX_MSC_DSDSHE_SH17_OFF (2u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH18 */
#define IFX_MSC_DSDSHE_SH18_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH18 */
#define IFX_MSC_DSDSHE_SH18_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH18 */
#define IFX_MSC_DSDSHE_SH18_OFF (4u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH19 */
#define IFX_MSC_DSDSHE_SH19_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH19 */
#define IFX_MSC_DSDSHE_SH19_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH19 */
#define IFX_MSC_DSDSHE_SH19_OFF (6u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH20 */
#define IFX_MSC_DSDSHE_SH20_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH20 */
#define IFX_MSC_DSDSHE_SH20_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH20 */
#define IFX_MSC_DSDSHE_SH20_OFF (8u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH21 */
#define IFX_MSC_DSDSHE_SH21_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH21 */
#define IFX_MSC_DSDSHE_SH21_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH21 */
#define IFX_MSC_DSDSHE_SH21_OFF (10u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH22 */
#define IFX_MSC_DSDSHE_SH22_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH22 */
#define IFX_MSC_DSDSHE_SH22_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH22 */
#define IFX_MSC_DSDSHE_SH22_OFF (12u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH23 */
#define IFX_MSC_DSDSHE_SH23_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH23 */
#define IFX_MSC_DSDSHE_SH23_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH23 */
#define IFX_MSC_DSDSHE_SH23_OFF (14u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH24 */
#define IFX_MSC_DSDSHE_SH24_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH24 */
#define IFX_MSC_DSDSHE_SH24_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH24 */
#define IFX_MSC_DSDSHE_SH24_OFF (16u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH25 */
#define IFX_MSC_DSDSHE_SH25_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH25 */
#define IFX_MSC_DSDSHE_SH25_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH25 */
#define IFX_MSC_DSDSHE_SH25_OFF (18u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH26 */
#define IFX_MSC_DSDSHE_SH26_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH26 */
#define IFX_MSC_DSDSHE_SH26_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH26 */
#define IFX_MSC_DSDSHE_SH26_OFF (20u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH27 */
#define IFX_MSC_DSDSHE_SH27_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH27 */
#define IFX_MSC_DSDSHE_SH27_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH27 */
#define IFX_MSC_DSDSHE_SH27_OFF (22u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH28 */
#define IFX_MSC_DSDSHE_SH28_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH28 */
#define IFX_MSC_DSDSHE_SH28_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH28 */
#define IFX_MSC_DSDSHE_SH28_OFF (24u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH29 */
#define IFX_MSC_DSDSHE_SH29_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH29 */
#define IFX_MSC_DSDSHE_SH29_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH29 */
#define IFX_MSC_DSDSHE_SH29_OFF (26u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH30 */
#define IFX_MSC_DSDSHE_SH30_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH30 */
#define IFX_MSC_DSDSHE_SH30_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH30 */
#define IFX_MSC_DSDSHE_SH30_OFF (28u)

/** \brief  Length for Ifx_MSC_DSDSHE_Bits.SH31 */
#define IFX_MSC_DSDSHE_SH31_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSHE_Bits.SH31 */
#define IFX_MSC_DSDSHE_SH31_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSHE_Bits.SH31 */
#define IFX_MSC_DSDSHE_SH31_OFF (30u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL0 */
#define IFX_MSC_DSDSL_SL0_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL0 */
#define IFX_MSC_DSDSL_SL0_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL0 */
#define IFX_MSC_DSDSL_SL0_OFF (0u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL10 */
#define IFX_MSC_DSDSL_SL10_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL10 */
#define IFX_MSC_DSDSL_SL10_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL10 */
#define IFX_MSC_DSDSL_SL10_OFF (20u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL11 */
#define IFX_MSC_DSDSL_SL11_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL11 */
#define IFX_MSC_DSDSL_SL11_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL11 */
#define IFX_MSC_DSDSL_SL11_OFF (22u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL12 */
#define IFX_MSC_DSDSL_SL12_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL12 */
#define IFX_MSC_DSDSL_SL12_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL12 */
#define IFX_MSC_DSDSL_SL12_OFF (24u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL13 */
#define IFX_MSC_DSDSL_SL13_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL13 */
#define IFX_MSC_DSDSL_SL13_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL13 */
#define IFX_MSC_DSDSL_SL13_OFF (26u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL14 */
#define IFX_MSC_DSDSL_SL14_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL14 */
#define IFX_MSC_DSDSL_SL14_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL14 */
#define IFX_MSC_DSDSL_SL14_OFF (28u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL15 */
#define IFX_MSC_DSDSL_SL15_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL15 */
#define IFX_MSC_DSDSL_SL15_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL15 */
#define IFX_MSC_DSDSL_SL15_OFF (30u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL1 */
#define IFX_MSC_DSDSL_SL1_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL1 */
#define IFX_MSC_DSDSL_SL1_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL1 */
#define IFX_MSC_DSDSL_SL1_OFF (2u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL2 */
#define IFX_MSC_DSDSL_SL2_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL2 */
#define IFX_MSC_DSDSL_SL2_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL2 */
#define IFX_MSC_DSDSL_SL2_OFF (4u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL3 */
#define IFX_MSC_DSDSL_SL3_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL3 */
#define IFX_MSC_DSDSL_SL3_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL3 */
#define IFX_MSC_DSDSL_SL3_OFF (6u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL4 */
#define IFX_MSC_DSDSL_SL4_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL4 */
#define IFX_MSC_DSDSL_SL4_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL4 */
#define IFX_MSC_DSDSL_SL4_OFF (8u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL5 */
#define IFX_MSC_DSDSL_SL5_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL5 */
#define IFX_MSC_DSDSL_SL5_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL5 */
#define IFX_MSC_DSDSL_SL5_OFF (10u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL6 */
#define IFX_MSC_DSDSL_SL6_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL6 */
#define IFX_MSC_DSDSL_SL6_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL6 */
#define IFX_MSC_DSDSL_SL6_OFF (12u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL7 */
#define IFX_MSC_DSDSL_SL7_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL7 */
#define IFX_MSC_DSDSL_SL7_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL7 */
#define IFX_MSC_DSDSL_SL7_OFF (14u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL8 */
#define IFX_MSC_DSDSL_SL8_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL8 */
#define IFX_MSC_DSDSL_SL8_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL8 */
#define IFX_MSC_DSDSL_SL8_OFF (16u)

/** \brief  Length for Ifx_MSC_DSDSL_Bits.SL9 */
#define IFX_MSC_DSDSL_SL9_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSL_Bits.SL9 */
#define IFX_MSC_DSDSL_SL9_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSL_Bits.SL9 */
#define IFX_MSC_DSDSL_SL9_OFF (18u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL16 */
#define IFX_MSC_DSDSLE_SL16_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL16 */
#define IFX_MSC_DSDSLE_SL16_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL16 */
#define IFX_MSC_DSDSLE_SL16_OFF (0u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL17 */
#define IFX_MSC_DSDSLE_SL17_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL17 */
#define IFX_MSC_DSDSLE_SL17_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL17 */
#define IFX_MSC_DSDSLE_SL17_OFF (2u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL18 */
#define IFX_MSC_DSDSLE_SL18_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL18 */
#define IFX_MSC_DSDSLE_SL18_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL18 */
#define IFX_MSC_DSDSLE_SL18_OFF (4u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL19 */
#define IFX_MSC_DSDSLE_SL19_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL19 */
#define IFX_MSC_DSDSLE_SL19_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL19 */
#define IFX_MSC_DSDSLE_SL19_OFF (6u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL20 */
#define IFX_MSC_DSDSLE_SL20_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL20 */
#define IFX_MSC_DSDSLE_SL20_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL20 */
#define IFX_MSC_DSDSLE_SL20_OFF (8u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL21 */
#define IFX_MSC_DSDSLE_SL21_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL21 */
#define IFX_MSC_DSDSLE_SL21_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL21 */
#define IFX_MSC_DSDSLE_SL21_OFF (10u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL22 */
#define IFX_MSC_DSDSLE_SL22_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL22 */
#define IFX_MSC_DSDSLE_SL22_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL22 */
#define IFX_MSC_DSDSLE_SL22_OFF (12u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL23 */
#define IFX_MSC_DSDSLE_SL23_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL23 */
#define IFX_MSC_DSDSLE_SL23_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL23 */
#define IFX_MSC_DSDSLE_SL23_OFF (14u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL24 */
#define IFX_MSC_DSDSLE_SL24_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL24 */
#define IFX_MSC_DSDSLE_SL24_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL24 */
#define IFX_MSC_DSDSLE_SL24_OFF (16u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL25 */
#define IFX_MSC_DSDSLE_SL25_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL25 */
#define IFX_MSC_DSDSLE_SL25_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL25 */
#define IFX_MSC_DSDSLE_SL25_OFF (18u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL26 */
#define IFX_MSC_DSDSLE_SL26_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL26 */
#define IFX_MSC_DSDSLE_SL26_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL26 */
#define IFX_MSC_DSDSLE_SL26_OFF (20u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL27 */
#define IFX_MSC_DSDSLE_SL27_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL27 */
#define IFX_MSC_DSDSLE_SL27_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL27 */
#define IFX_MSC_DSDSLE_SL27_OFF (22u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL28 */
#define IFX_MSC_DSDSLE_SL28_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL28 */
#define IFX_MSC_DSDSLE_SL28_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL28 */
#define IFX_MSC_DSDSLE_SL28_OFF (24u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL29 */
#define IFX_MSC_DSDSLE_SL29_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL29 */
#define IFX_MSC_DSDSLE_SL29_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL29 */
#define IFX_MSC_DSDSLE_SL29_OFF (26u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL30 */
#define IFX_MSC_DSDSLE_SL30_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL30 */
#define IFX_MSC_DSDSLE_SL30_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL30 */
#define IFX_MSC_DSDSLE_SL30_OFF (28u)

/** \brief  Length for Ifx_MSC_DSDSLE_Bits.SL31 */
#define IFX_MSC_DSDSLE_SL31_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSDSLE_Bits.SL31 */
#define IFX_MSC_DSDSLE_SL31_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSDSLE_Bits.SL31 */
#define IFX_MSC_DSDSLE_SL31_OFF (30u)

/** \brief  Length for Ifx_MSC_DSS_Bits.CFA */
#define IFX_MSC_DSS_CFA_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSS_Bits.CFA */
#define IFX_MSC_DSS_CFA_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSS_Bits.CFA */
#define IFX_MSC_DSS_CFA_OFF (25u)

/** \brief  Length for Ifx_MSC_DSS_Bits.DC */
#define IFX_MSC_DSS_DC_LEN (8u)

/** \brief  Mask for Ifx_MSC_DSS_Bits.DC */
#define IFX_MSC_DSS_DC_MSK (0xffu)

/** \brief  Offset for Ifx_MSC_DSS_Bits.DC */
#define IFX_MSC_DSS_DC_OFF (16u)

/** \brief  Length for Ifx_MSC_DSS_Bits.DFA */
#define IFX_MSC_DSS_DFA_LEN (1u)

/** \brief  Mask for Ifx_MSC_DSS_Bits.DFA */
#define IFX_MSC_DSS_DFA_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_DSS_Bits.DFA */
#define IFX_MSC_DSS_DFA_OFF (24u)

/** \brief  Length for Ifx_MSC_DSS_Bits.NPTF */
#define IFX_MSC_DSS_NPTF_LEN (4u)

/** \brief  Mask for Ifx_MSC_DSS_Bits.NPTF */
#define IFX_MSC_DSS_NPTF_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_DSS_Bits.NPTF */
#define IFX_MSC_DSS_NPTF_OFF (8u)

/** \brief  Length for Ifx_MSC_DSS_Bits.PFC */
#define IFX_MSC_DSS_PFC_LEN (4u)

/** \brief  Mask for Ifx_MSC_DSS_Bits.PFC */
#define IFX_MSC_DSS_PFC_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_DSS_Bits.PFC */
#define IFX_MSC_DSS_PFC_OFF (0u)

/** \brief  Length for Ifx_MSC_DSTE_Bits.NDD */
#define IFX_MSC_DSTE_NDD_LEN (4u)

/** \brief  Mask for Ifx_MSC_DSTE_Bits.NDD */
#define IFX_MSC_DSTE_NDD_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_DSTE_Bits.NDD */
#define IFX_MSC_DSTE_NDD_OFF (8u)

/** \brief  Length for Ifx_MSC_DSTE_Bits.PPCE */
#define IFX_MSC_DSTE_PPCE_LEN (6u)

/** \brief  Mask for Ifx_MSC_DSTE_Bits.PPCE */
#define IFX_MSC_DSTE_PPCE_MSK (0x3fu)

/** \brief  Offset for Ifx_MSC_DSTE_Bits.PPCE */
#define IFX_MSC_DSTE_PPCE_OFF (2u)

/** \brief  Length for Ifx_MSC_DSTE_Bits.PPDE */
#define IFX_MSC_DSTE_PPDE_LEN (2u)

/** \brief  Mask for Ifx_MSC_DSTE_Bits.PPDE */
#define IFX_MSC_DSTE_PPDE_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_DSTE_Bits.PPDE */
#define IFX_MSC_DSTE_PPDE_OFF (0u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH0 */
#define IFX_MSC_ESR_ENH0_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH0 */
#define IFX_MSC_ESR_ENH0_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH0 */
#define IFX_MSC_ESR_ENH0_OFF (16u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH10 */
#define IFX_MSC_ESR_ENH10_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH10 */
#define IFX_MSC_ESR_ENH10_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH10 */
#define IFX_MSC_ESR_ENH10_OFF (26u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH11 */
#define IFX_MSC_ESR_ENH11_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH11 */
#define IFX_MSC_ESR_ENH11_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH11 */
#define IFX_MSC_ESR_ENH11_OFF (27u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH12 */
#define IFX_MSC_ESR_ENH12_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH12 */
#define IFX_MSC_ESR_ENH12_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH12 */
#define IFX_MSC_ESR_ENH12_OFF (28u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH13 */
#define IFX_MSC_ESR_ENH13_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH13 */
#define IFX_MSC_ESR_ENH13_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH13 */
#define IFX_MSC_ESR_ENH13_OFF (29u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH14 */
#define IFX_MSC_ESR_ENH14_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH14 */
#define IFX_MSC_ESR_ENH14_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH14 */
#define IFX_MSC_ESR_ENH14_OFF (30u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH15 */
#define IFX_MSC_ESR_ENH15_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH15 */
#define IFX_MSC_ESR_ENH15_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH15 */
#define IFX_MSC_ESR_ENH15_OFF (31u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH1 */
#define IFX_MSC_ESR_ENH1_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH1 */
#define IFX_MSC_ESR_ENH1_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH1 */
#define IFX_MSC_ESR_ENH1_OFF (17u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH2 */
#define IFX_MSC_ESR_ENH2_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH2 */
#define IFX_MSC_ESR_ENH2_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH2 */
#define IFX_MSC_ESR_ENH2_OFF (18u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH3 */
#define IFX_MSC_ESR_ENH3_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH3 */
#define IFX_MSC_ESR_ENH3_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH3 */
#define IFX_MSC_ESR_ENH3_OFF (19u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH4 */
#define IFX_MSC_ESR_ENH4_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH4 */
#define IFX_MSC_ESR_ENH4_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH4 */
#define IFX_MSC_ESR_ENH4_OFF (20u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH5 */
#define IFX_MSC_ESR_ENH5_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH5 */
#define IFX_MSC_ESR_ENH5_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH5 */
#define IFX_MSC_ESR_ENH5_OFF (21u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH6 */
#define IFX_MSC_ESR_ENH6_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH6 */
#define IFX_MSC_ESR_ENH6_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH6 */
#define IFX_MSC_ESR_ENH6_OFF (22u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH7 */
#define IFX_MSC_ESR_ENH7_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH7 */
#define IFX_MSC_ESR_ENH7_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH7 */
#define IFX_MSC_ESR_ENH7_OFF (23u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH8 */
#define IFX_MSC_ESR_ENH8_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH8 */
#define IFX_MSC_ESR_ENH8_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH8 */
#define IFX_MSC_ESR_ENH8_OFF (24u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENH9 */
#define IFX_MSC_ESR_ENH9_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENH9 */
#define IFX_MSC_ESR_ENH9_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENH9 */
#define IFX_MSC_ESR_ENH9_OFF (25u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL0 */
#define IFX_MSC_ESR_ENL0_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL0 */
#define IFX_MSC_ESR_ENL0_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL0 */
#define IFX_MSC_ESR_ENL0_OFF (0u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL10 */
#define IFX_MSC_ESR_ENL10_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL10 */
#define IFX_MSC_ESR_ENL10_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL10 */
#define IFX_MSC_ESR_ENL10_OFF (10u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL11 */
#define IFX_MSC_ESR_ENL11_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL11 */
#define IFX_MSC_ESR_ENL11_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL11 */
#define IFX_MSC_ESR_ENL11_OFF (11u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL12 */
#define IFX_MSC_ESR_ENL12_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL12 */
#define IFX_MSC_ESR_ENL12_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL12 */
#define IFX_MSC_ESR_ENL12_OFF (12u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL13 */
#define IFX_MSC_ESR_ENL13_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL13 */
#define IFX_MSC_ESR_ENL13_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL13 */
#define IFX_MSC_ESR_ENL13_OFF (13u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL14 */
#define IFX_MSC_ESR_ENL14_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL14 */
#define IFX_MSC_ESR_ENL14_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL14 */
#define IFX_MSC_ESR_ENL14_OFF (14u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL15 */
#define IFX_MSC_ESR_ENL15_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL15 */
#define IFX_MSC_ESR_ENL15_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL15 */
#define IFX_MSC_ESR_ENL15_OFF (15u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL1 */
#define IFX_MSC_ESR_ENL1_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL1 */
#define IFX_MSC_ESR_ENL1_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL1 */
#define IFX_MSC_ESR_ENL1_OFF (1u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL2 */
#define IFX_MSC_ESR_ENL2_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL2 */
#define IFX_MSC_ESR_ENL2_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL2 */
#define IFX_MSC_ESR_ENL2_OFF (2u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL3 */
#define IFX_MSC_ESR_ENL3_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL3 */
#define IFX_MSC_ESR_ENL3_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL3 */
#define IFX_MSC_ESR_ENL3_OFF (3u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL4 */
#define IFX_MSC_ESR_ENL4_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL4 */
#define IFX_MSC_ESR_ENL4_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL4 */
#define IFX_MSC_ESR_ENL4_OFF (4u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL5 */
#define IFX_MSC_ESR_ENL5_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL5 */
#define IFX_MSC_ESR_ENL5_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL5 */
#define IFX_MSC_ESR_ENL5_OFF (5u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL6 */
#define IFX_MSC_ESR_ENL6_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL6 */
#define IFX_MSC_ESR_ENL6_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL6 */
#define IFX_MSC_ESR_ENL6_OFF (6u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL7 */
#define IFX_MSC_ESR_ENL7_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL7 */
#define IFX_MSC_ESR_ENL7_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL7 */
#define IFX_MSC_ESR_ENL7_OFF (7u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL8 */
#define IFX_MSC_ESR_ENL8_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL8 */
#define IFX_MSC_ESR_ENL8_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL8 */
#define IFX_MSC_ESR_ENL8_OFF (8u)

/** \brief  Length for Ifx_MSC_ESR_Bits.ENL9 */
#define IFX_MSC_ESR_ENL9_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESR_Bits.ENL9 */
#define IFX_MSC_ESR_ENL9_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESR_Bits.ENL9 */
#define IFX_MSC_ESR_ENL9_OFF (9u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH16 */
#define IFX_MSC_ESRE_ENH16_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH16 */
#define IFX_MSC_ESRE_ENH16_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH16 */
#define IFX_MSC_ESRE_ENH16_OFF (16u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH17 */
#define IFX_MSC_ESRE_ENH17_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH17 */
#define IFX_MSC_ESRE_ENH17_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH17 */
#define IFX_MSC_ESRE_ENH17_OFF (17u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH18 */
#define IFX_MSC_ESRE_ENH18_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH18 */
#define IFX_MSC_ESRE_ENH18_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH18 */
#define IFX_MSC_ESRE_ENH18_OFF (18u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH19 */
#define IFX_MSC_ESRE_ENH19_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH19 */
#define IFX_MSC_ESRE_ENH19_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH19 */
#define IFX_MSC_ESRE_ENH19_OFF (19u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH20 */
#define IFX_MSC_ESRE_ENH20_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH20 */
#define IFX_MSC_ESRE_ENH20_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH20 */
#define IFX_MSC_ESRE_ENH20_OFF (20u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH21 */
#define IFX_MSC_ESRE_ENH21_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH21 */
#define IFX_MSC_ESRE_ENH21_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH21 */
#define IFX_MSC_ESRE_ENH21_OFF (21u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH22 */
#define IFX_MSC_ESRE_ENH22_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH22 */
#define IFX_MSC_ESRE_ENH22_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH22 */
#define IFX_MSC_ESRE_ENH22_OFF (22u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH23 */
#define IFX_MSC_ESRE_ENH23_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH23 */
#define IFX_MSC_ESRE_ENH23_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH23 */
#define IFX_MSC_ESRE_ENH23_OFF (23u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH24 */
#define IFX_MSC_ESRE_ENH24_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH24 */
#define IFX_MSC_ESRE_ENH24_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH24 */
#define IFX_MSC_ESRE_ENH24_OFF (24u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH25 */
#define IFX_MSC_ESRE_ENH25_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH25 */
#define IFX_MSC_ESRE_ENH25_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH25 */
#define IFX_MSC_ESRE_ENH25_OFF (25u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH26 */
#define IFX_MSC_ESRE_ENH26_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH26 */
#define IFX_MSC_ESRE_ENH26_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH26 */
#define IFX_MSC_ESRE_ENH26_OFF (26u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH27 */
#define IFX_MSC_ESRE_ENH27_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH27 */
#define IFX_MSC_ESRE_ENH27_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH27 */
#define IFX_MSC_ESRE_ENH27_OFF (27u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH28 */
#define IFX_MSC_ESRE_ENH28_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH28 */
#define IFX_MSC_ESRE_ENH28_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH28 */
#define IFX_MSC_ESRE_ENH28_OFF (28u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH29 */
#define IFX_MSC_ESRE_ENH29_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH29 */
#define IFX_MSC_ESRE_ENH29_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH29 */
#define IFX_MSC_ESRE_ENH29_OFF (29u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH30 */
#define IFX_MSC_ESRE_ENH30_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH30 */
#define IFX_MSC_ESRE_ENH30_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH30 */
#define IFX_MSC_ESRE_ENH30_OFF (30u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENH31 */
#define IFX_MSC_ESRE_ENH31_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENH31 */
#define IFX_MSC_ESRE_ENH31_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENH31 */
#define IFX_MSC_ESRE_ENH31_OFF (31u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL16 */
#define IFX_MSC_ESRE_ENL16_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL16 */
#define IFX_MSC_ESRE_ENL16_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL16 */
#define IFX_MSC_ESRE_ENL16_OFF (0u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL17 */
#define IFX_MSC_ESRE_ENL17_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL17 */
#define IFX_MSC_ESRE_ENL17_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL17 */
#define IFX_MSC_ESRE_ENL17_OFF (1u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL18 */
#define IFX_MSC_ESRE_ENL18_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL18 */
#define IFX_MSC_ESRE_ENL18_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL18 */
#define IFX_MSC_ESRE_ENL18_OFF (2u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL19 */
#define IFX_MSC_ESRE_ENL19_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL19 */
#define IFX_MSC_ESRE_ENL19_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL19 */
#define IFX_MSC_ESRE_ENL19_OFF (3u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL20 */
#define IFX_MSC_ESRE_ENL20_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL20 */
#define IFX_MSC_ESRE_ENL20_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL20 */
#define IFX_MSC_ESRE_ENL20_OFF (4u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL21 */
#define IFX_MSC_ESRE_ENL21_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL21 */
#define IFX_MSC_ESRE_ENL21_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL21 */
#define IFX_MSC_ESRE_ENL21_OFF (5u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL22 */
#define IFX_MSC_ESRE_ENL22_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL22 */
#define IFX_MSC_ESRE_ENL22_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL22 */
#define IFX_MSC_ESRE_ENL22_OFF (6u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL23 */
#define IFX_MSC_ESRE_ENL23_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL23 */
#define IFX_MSC_ESRE_ENL23_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL23 */
#define IFX_MSC_ESRE_ENL23_OFF (7u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL24 */
#define IFX_MSC_ESRE_ENL24_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL24 */
#define IFX_MSC_ESRE_ENL24_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL24 */
#define IFX_MSC_ESRE_ENL24_OFF (8u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL25 */
#define IFX_MSC_ESRE_ENL25_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL25 */
#define IFX_MSC_ESRE_ENL25_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL25 */
#define IFX_MSC_ESRE_ENL25_OFF (9u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL26 */
#define IFX_MSC_ESRE_ENL26_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL26 */
#define IFX_MSC_ESRE_ENL26_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL26 */
#define IFX_MSC_ESRE_ENL26_OFF (10u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL27 */
#define IFX_MSC_ESRE_ENL27_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL27 */
#define IFX_MSC_ESRE_ENL27_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL27 */
#define IFX_MSC_ESRE_ENL27_OFF (11u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL28 */
#define IFX_MSC_ESRE_ENL28_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL28 */
#define IFX_MSC_ESRE_ENL28_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL28 */
#define IFX_MSC_ESRE_ENL28_OFF (12u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL29 */
#define IFX_MSC_ESRE_ENL29_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL29 */
#define IFX_MSC_ESRE_ENL29_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL29 */
#define IFX_MSC_ESRE_ENL29_OFF (13u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL30 */
#define IFX_MSC_ESRE_ENL30_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL30 */
#define IFX_MSC_ESRE_ENL30_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL30 */
#define IFX_MSC_ESRE_ENL30_OFF (14u)

/** \brief  Length for Ifx_MSC_ESRE_Bits.ENL31 */
#define IFX_MSC_ESRE_ENL31_LEN (1u)

/** \brief  Mask for Ifx_MSC_ESRE_Bits.ENL31 */
#define IFX_MSC_ESRE_ENL31_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ESRE_Bits.ENL31 */
#define IFX_MSC_ESRE_ENL31_OFF (15u)

/** \brief  Length for Ifx_MSC_FDR_Bits.DISCLK */
#define IFX_MSC_FDR_DISCLK_LEN (1u)

/** \brief  Mask for Ifx_MSC_FDR_Bits.DISCLK */
#define IFX_MSC_FDR_DISCLK_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_FDR_Bits.DISCLK */
#define IFX_MSC_FDR_DISCLK_OFF (31u)

/** \brief  Length for Ifx_MSC_FDR_Bits.DM */
#define IFX_MSC_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_MSC_FDR_Bits.DM */
#define IFX_MSC_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_FDR_Bits.DM */
#define IFX_MSC_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_MSC_FDR_Bits.ENHW */
#define IFX_MSC_FDR_ENHW_LEN (1u)

/** \brief  Mask for Ifx_MSC_FDR_Bits.ENHW */
#define IFX_MSC_FDR_ENHW_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_FDR_Bits.ENHW */
#define IFX_MSC_FDR_ENHW_OFF (30u)

/** \brief  Length for Ifx_MSC_FDR_Bits.RESULT */
#define IFX_MSC_FDR_RESULT_LEN (10u)

/** \brief  Mask for Ifx_MSC_FDR_Bits.RESULT */
#define IFX_MSC_FDR_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_MSC_FDR_Bits.RESULT */
#define IFX_MSC_FDR_RESULT_OFF (16u)

/** \brief  Length for Ifx_MSC_FDR_Bits.STEP */
#define IFX_MSC_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_MSC_FDR_Bits.STEP */
#define IFX_MSC_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_MSC_FDR_Bits.STEP */
#define IFX_MSC_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_MSC_ICR_Bits.ECIE */
#define IFX_MSC_ICR_ECIE_LEN (1u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.ECIE */
#define IFX_MSC_ICR_ECIE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.ECIE */
#define IFX_MSC_ICR_ECIE_OFF (7u)

/** \brief  Length for Ifx_MSC_ICR_Bits.ECIP */
#define IFX_MSC_ICR_ECIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.ECIP */
#define IFX_MSC_ICR_ECIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.ECIP */
#define IFX_MSC_ICR_ECIP_OFF (4u)

/** \brief  Length for Ifx_MSC_ICR_Bits.EDIE */
#define IFX_MSC_ICR_EDIE_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.EDIE */
#define IFX_MSC_ICR_EDIE_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.EDIE */
#define IFX_MSC_ICR_EDIE_OFF (2u)

/** \brief  Length for Ifx_MSC_ICR_Bits.EDIP */
#define IFX_MSC_ICR_EDIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.EDIP */
#define IFX_MSC_ICR_EDIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.EDIP */
#define IFX_MSC_ICR_EDIP_OFF (0u)

/** \brief  Length for Ifx_MSC_ICR_Bits.RDIE */
#define IFX_MSC_ICR_RDIE_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.RDIE */
#define IFX_MSC_ICR_RDIE_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.RDIE */
#define IFX_MSC_ICR_RDIE_OFF (14u)

/** \brief  Length for Ifx_MSC_ICR_Bits.RDIP */
#define IFX_MSC_ICR_RDIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.RDIP */
#define IFX_MSC_ICR_RDIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.RDIP */
#define IFX_MSC_ICR_RDIP_OFF (12u)

/** \brief  Length for Ifx_MSC_ICR_Bits.TFIE */
#define IFX_MSC_ICR_TFIE_LEN (1u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.TFIE */
#define IFX_MSC_ICR_TFIE_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.TFIE */
#define IFX_MSC_ICR_TFIE_OFF (11u)

/** \brief  Length for Ifx_MSC_ICR_Bits.TFIP */
#define IFX_MSC_ICR_TFIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_ICR_Bits.TFIP */
#define IFX_MSC_ICR_TFIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_ICR_Bits.TFIP */
#define IFX_MSC_ICR_TFIP_OFF (8u)

/** \brief  Length for Ifx_MSC_ID_Bits.MODNUMBER */
#define IFX_MSC_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_MSC_ID_Bits.MODNUMBER */
#define IFX_MSC_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_MSC_ID_Bits.MODNUMBER */
#define IFX_MSC_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_MSC_ID_Bits.MODREV */
#define IFX_MSC_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_MSC_ID_Bits.MODREV */
#define IFX_MSC_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_MSC_ID_Bits.MODREV */
#define IFX_MSC_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_MSC_ID_Bits.MODTYPE */
#define IFX_MSC_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_MSC_ID_Bits.MODTYPE */
#define IFX_MSC_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_MSC_ID_Bits.MODTYPE */
#define IFX_MSC_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CCP */
#define IFX_MSC_ISC_CCP_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CCP */
#define IFX_MSC_ISC_CCP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CCP */
#define IFX_MSC_ISC_CCP_OFF (5u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CDDIS */
#define IFX_MSC_ISC_CDDIS_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CDDIS */
#define IFX_MSC_ISC_CDDIS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CDDIS */
#define IFX_MSC_ISC_CDDIS_OFF (6u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CDECI */
#define IFX_MSC_ISC_CDECI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CDECI */
#define IFX_MSC_ISC_CDECI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CDECI */
#define IFX_MSC_ISC_CDECI_OFF (1u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CDEDI */
#define IFX_MSC_ISC_CDEDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CDEDI */
#define IFX_MSC_ISC_CDEDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CDEDI */
#define IFX_MSC_ISC_CDEDI_OFF (0u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CDP */
#define IFX_MSC_ISC_CDP_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CDP */
#define IFX_MSC_ISC_CDP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CDP */
#define IFX_MSC_ISC_CDP_OFF (4u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CDTFI */
#define IFX_MSC_ISC_CDTFI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CDTFI */
#define IFX_MSC_ISC_CDTFI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CDTFI */
#define IFX_MSC_ISC_CDTFI_OFF (2u)

/** \brief  Length for Ifx_MSC_ISC_Bits.CURDI */
#define IFX_MSC_ISC_CURDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.CURDI */
#define IFX_MSC_ISC_CURDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.CURDI */
#define IFX_MSC_ISC_CURDI_OFF (3u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SCP */
#define IFX_MSC_ISC_SCP_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SCP */
#define IFX_MSC_ISC_SCP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SCP */
#define IFX_MSC_ISC_SCP_OFF (21u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SDDIS */
#define IFX_MSC_ISC_SDDIS_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SDDIS */
#define IFX_MSC_ISC_SDDIS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SDDIS */
#define IFX_MSC_ISC_SDDIS_OFF (22u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SDECI */
#define IFX_MSC_ISC_SDECI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SDECI */
#define IFX_MSC_ISC_SDECI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SDECI */
#define IFX_MSC_ISC_SDECI_OFF (17u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SDEDI */
#define IFX_MSC_ISC_SDEDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SDEDI */
#define IFX_MSC_ISC_SDEDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SDEDI */
#define IFX_MSC_ISC_SDEDI_OFF (16u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SDP */
#define IFX_MSC_ISC_SDP_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SDP */
#define IFX_MSC_ISC_SDP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SDP */
#define IFX_MSC_ISC_SDP_OFF (20u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SDTFI */
#define IFX_MSC_ISC_SDTFI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SDTFI */
#define IFX_MSC_ISC_SDTFI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SDTFI */
#define IFX_MSC_ISC_SDTFI_OFF (18u)

/** \brief  Length for Ifx_MSC_ISC_Bits.SURDI */
#define IFX_MSC_ISC_SURDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISC_Bits.SURDI */
#define IFX_MSC_ISC_SURDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISC_Bits.SURDI */
#define IFX_MSC_ISC_SURDI_OFF (19u)

/** \brief  Length for Ifx_MSC_ISR_Bits.DECI */
#define IFX_MSC_ISR_DECI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISR_Bits.DECI */
#define IFX_MSC_ISR_DECI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISR_Bits.DECI */
#define IFX_MSC_ISR_DECI_OFF (1u)

/** \brief  Length for Ifx_MSC_ISR_Bits.DEDI */
#define IFX_MSC_ISR_DEDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISR_Bits.DEDI */
#define IFX_MSC_ISR_DEDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISR_Bits.DEDI */
#define IFX_MSC_ISR_DEDI_OFF (0u)

/** \brief  Length for Ifx_MSC_ISR_Bits.DTFI */
#define IFX_MSC_ISR_DTFI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISR_Bits.DTFI */
#define IFX_MSC_ISR_DTFI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISR_Bits.DTFI */
#define IFX_MSC_ISR_DTFI_OFF (2u)

/** \brief  Length for Ifx_MSC_ISR_Bits.URDI */
#define IFX_MSC_ISR_URDI_LEN (1u)

/** \brief  Mask for Ifx_MSC_ISR_Bits.URDI */
#define IFX_MSC_ISR_URDI_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_ISR_Bits.URDI */
#define IFX_MSC_ISR_URDI_OFF (3u)

/** \brief  Length for Ifx_MSC_KRST0_Bits.RST */
#define IFX_MSC_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_MSC_KRST0_Bits.RST */
#define IFX_MSC_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_KRST0_Bits.RST */
#define IFX_MSC_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_MSC_KRST0_Bits.RSTSTAT */
#define IFX_MSC_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_MSC_KRST0_Bits.RSTSTAT */
#define IFX_MSC_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_KRST0_Bits.RSTSTAT */
#define IFX_MSC_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_MSC_KRST1_Bits.RST */
#define IFX_MSC_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_MSC_KRST1_Bits.RST */
#define IFX_MSC_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_KRST1_Bits.RST */
#define IFX_MSC_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_MSC_KRSTCLR_Bits.CLR */
#define IFX_MSC_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_MSC_KRSTCLR_Bits.CLR */
#define IFX_MSC_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_KRSTCLR_Bits.CLR */
#define IFX_MSC_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CLKCTRL */
#define IFX_MSC_OCR_CLKCTRL_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CLKCTRL */
#define IFX_MSC_OCR_CLKCTRL_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CLKCTRL */
#define IFX_MSC_OCR_CLKCTRL_OFF (8u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CLP */
#define IFX_MSC_OCR_CLP_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CLP */
#define IFX_MSC_OCR_CLP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CLP */
#define IFX_MSC_OCR_CLP_OFF (0u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CSC */
#define IFX_MSC_OCR_CSC_LEN (2u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CSC */
#define IFX_MSC_OCR_CSC_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CSC */
#define IFX_MSC_OCR_CSC_OFF (13u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CSH */
#define IFX_MSC_OCR_CSH_LEN (2u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CSH */
#define IFX_MSC_OCR_CSH_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CSH */
#define IFX_MSC_OCR_CSH_OFF (11u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CSL */
#define IFX_MSC_OCR_CSL_LEN (2u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CSL */
#define IFX_MSC_OCR_CSL_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CSL */
#define IFX_MSC_OCR_CSL_OFF (9u)

/** \brief  Length for Ifx_MSC_OCR_Bits.CSLP */
#define IFX_MSC_OCR_CSLP_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.CSLP */
#define IFX_MSC_OCR_CSLP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.CSLP */
#define IFX_MSC_OCR_CSLP_OFF (2u)

/** \brief  Length for Ifx_MSC_OCR_Bits.ILP */
#define IFX_MSC_OCR_ILP_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.ILP */
#define IFX_MSC_OCR_ILP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.ILP */
#define IFX_MSC_OCR_ILP_OFF (3u)

/** \brief  Length for Ifx_MSC_OCR_Bits.SDISEL */
#define IFX_MSC_OCR_SDISEL_LEN (3u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.SDISEL */
#define IFX_MSC_OCR_SDISEL_MSK (0x7u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.SDISEL */
#define IFX_MSC_OCR_SDISEL_OFF (16u)

/** \brief  Length for Ifx_MSC_OCR_Bits.SLP */
#define IFX_MSC_OCR_SLP_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCR_Bits.SLP */
#define IFX_MSC_OCR_SLP_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCR_Bits.SLP */
#define IFX_MSC_OCR_SLP_OFF (1u)

/** \brief  Length for Ifx_MSC_OCS_Bits.SUS */
#define IFX_MSC_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_MSC_OCS_Bits.SUS */
#define IFX_MSC_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_OCS_Bits.SUS */
#define IFX_MSC_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_MSC_OCS_Bits.SUS_P */
#define IFX_MSC_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCS_Bits.SUS_P */
#define IFX_MSC_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCS_Bits.SUS_P */
#define IFX_MSC_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_MSC_OCS_Bits.SUSSTA */
#define IFX_MSC_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_MSC_OCS_Bits.SUSSTA */
#define IFX_MSC_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_OCS_Bits.SUSSTA */
#define IFX_MSC_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_MSC_UD_Bits.C */
#define IFX_MSC_UD_C_LEN (1u)

/** \brief  Mask for Ifx_MSC_UD_Bits.C */
#define IFX_MSC_UD_C_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_UD_Bits.C */
#define IFX_MSC_UD_C_OFF (18u)

/** \brief  Length for Ifx_MSC_UD_Bits.DATA */
#define IFX_MSC_UD_DATA_LEN (8u)

/** \brief  Mask for Ifx_MSC_UD_Bits.DATA */
#define IFX_MSC_UD_DATA_MSK (0xffu)

/** \brief  Offset for Ifx_MSC_UD_Bits.DATA */
#define IFX_MSC_UD_DATA_OFF (0u)

/** \brief  Length for Ifx_MSC_UD_Bits.IPF */
#define IFX_MSC_UD_IPF_LEN (1u)

/** \brief  Mask for Ifx_MSC_UD_Bits.IPF */
#define IFX_MSC_UD_IPF_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_UD_Bits.IPF */
#define IFX_MSC_UD_IPF_OFF (21u)

/** \brief  Length for Ifx_MSC_UD_Bits.LABF */
#define IFX_MSC_UD_LABF_LEN (2u)

/** \brief  Mask for Ifx_MSC_UD_Bits.LABF */
#define IFX_MSC_UD_LABF_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_UD_Bits.LABF */
#define IFX_MSC_UD_LABF_OFF (19u)

/** \brief  Length for Ifx_MSC_UD_Bits.P */
#define IFX_MSC_UD_P_LEN (1u)

/** \brief  Mask for Ifx_MSC_UD_Bits.P */
#define IFX_MSC_UD_P_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_UD_Bits.P */
#define IFX_MSC_UD_P_OFF (17u)

/** \brief  Length for Ifx_MSC_UD_Bits.PERR */
#define IFX_MSC_UD_PERR_LEN (1u)

/** \brief  Mask for Ifx_MSC_UD_Bits.PERR */
#define IFX_MSC_UD_PERR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_UD_Bits.PERR */
#define IFX_MSC_UD_PERR_OFF (22u)

/** \brief  Length for Ifx_MSC_UD_Bits.V */
#define IFX_MSC_UD_V_LEN (1u)

/** \brief  Mask for Ifx_MSC_UD_Bits.V */
#define IFX_MSC_UD_V_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_UD_Bits.V */
#define IFX_MSC_UD_V_OFF (16u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTC */
#define IFX_MSC_USCE_USTC_LEN (1u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTC */
#define IFX_MSC_USCE_USTC_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTC */
#define IFX_MSC_USCE_USTC_OFF (10u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTF */
#define IFX_MSC_USCE_USTF_LEN (1u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTF */
#define IFX_MSC_USCE_USTF_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTF */
#define IFX_MSC_USCE_USTF_OFF (9u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTOEN */
#define IFX_MSC_USCE_USTOEN_LEN (1u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTOEN */
#define IFX_MSC_USCE_USTOEN_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTOEN */
#define IFX_MSC_USCE_USTOEN_OFF (8u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTOIP */
#define IFX_MSC_USCE_USTOIP_LEN (2u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTOIP */
#define IFX_MSC_USCE_USTOIP_MSK (0x3u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTOIP */
#define IFX_MSC_USCE_USTOIP_OFF (14u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTOPRE */
#define IFX_MSC_USCE_USTOPRE_LEN (4u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTOPRE */
#define IFX_MSC_USCE_USTOPRE_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTOPRE */
#define IFX_MSC_USCE_USTOPRE_OFF (0u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTOVAL */
#define IFX_MSC_USCE_USTOVAL_LEN (4u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTOVAL */
#define IFX_MSC_USCE_USTOVAL_MSK (0xfu)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTOVAL */
#define IFX_MSC_USCE_USTOVAL_OFF (4u)

/** \brief  Length for Ifx_MSC_USCE_Bits.USTS */
#define IFX_MSC_USCE_USTS_LEN (1u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.USTS */
#define IFX_MSC_USCE_USTS_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.USTS */
#define IFX_MSC_USCE_USTS_OFF (11u)

/** \brief  Length for Ifx_MSC_USCE_Bits.UTASR */
#define IFX_MSC_USCE_UTASR_LEN (1u)

/** \brief  Mask for Ifx_MSC_USCE_Bits.UTASR */
#define IFX_MSC_USCE_UTASR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USCE_Bits.UTASR */
#define IFX_MSC_USCE_UTASR_OFF (13u)

/** \brief  Length for Ifx_MSC_USR_Bits.PCTR */
#define IFX_MSC_USR_PCTR_LEN (1u)

/** \brief  Mask for Ifx_MSC_USR_Bits.PCTR */
#define IFX_MSC_USR_PCTR_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USR_Bits.PCTR */
#define IFX_MSC_USR_PCTR_OFF (4u)

/** \brief  Length for Ifx_MSC_USR_Bits.SRDC */
#define IFX_MSC_USR_SRDC_LEN (1u)

/** \brief  Mask for Ifx_MSC_USR_Bits.SRDC */
#define IFX_MSC_USR_SRDC_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USR_Bits.SRDC */
#define IFX_MSC_USR_SRDC_OFF (5u)

/** \brief  Length for Ifx_MSC_USR_Bits.UC */
#define IFX_MSC_USR_UC_LEN (5u)

/** \brief  Mask for Ifx_MSC_USR_Bits.UC */
#define IFX_MSC_USR_UC_MSK (0x1fu)

/** \brief  Offset for Ifx_MSC_USR_Bits.UC */
#define IFX_MSC_USR_UC_OFF (16u)

/** \brief  Length for Ifx_MSC_USR_Bits.UFT */
#define IFX_MSC_USR_UFT_LEN (1u)

/** \brief  Mask for Ifx_MSC_USR_Bits.UFT */
#define IFX_MSC_USR_UFT_MSK (0x1u)

/** \brief  Offset for Ifx_MSC_USR_Bits.UFT */
#define IFX_MSC_USR_UFT_OFF (0u)

/** \brief  Length for Ifx_MSC_USR_Bits.URR */
#define IFX_MSC_USR_URR_LEN (3u)

/** \brief  Mask for Ifx_MSC_USR_Bits.URR */
#define IFX_MSC_USR_URR_MSK (0x7u)

/** \brief  Offset for Ifx_MSC_USR_Bits.URR */
#define IFX_MSC_USR_URR_OFF (1u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXMSC_BF_H */
