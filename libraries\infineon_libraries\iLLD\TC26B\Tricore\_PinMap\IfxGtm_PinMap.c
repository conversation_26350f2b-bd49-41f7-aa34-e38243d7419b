/**
 * \file IfxGtm_PinMap.c
 * \brief GTM I/O map
 * \ingroup IfxLld_Gtm
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */

#include "IfxGtm_PinMap.h"

IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT0_P02_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 0, {&MODULE_P02, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT109_P10_7_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 109, {&MODULE_P10, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT48_P22_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 48, {&MODULE_P22, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT53_P21_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 53, {&MODULE_P21, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT85_P14_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 85, {&MODULE_P14, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT8_P02_8_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 8, {&MODULE_P02, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_0_TOUT9_P00_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 9, {&MODULE_P00, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT103_P10_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 103, {&MODULE_P10, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT10_P00_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 10, {&MODULE_P00, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT11_P00_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 11, {&MODULE_P00, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT1_P02_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 1, {&MODULE_P02, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT31_P33_9_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 31, {&MODULE_P33, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT47_P22_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 47, {&MODULE_P22, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT54_P21_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 54, {&MODULE_P21, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_1_TOUT84_P14_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 84, {&MODULE_P14, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT104_P10_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 104, {&MODULE_P10, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT107_P10_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 107, {&MODULE_P10, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT12_P00_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 12, {&MODULE_P00, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT2_P02_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 2, {&MODULE_P02, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT33_P33_11_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 33, {&MODULE_P33,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT46_P23_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 46, {&MODULE_P23, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT55_P21_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 55, {&MODULE_P21, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_2_TOUT83_P14_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 83, {&MODULE_P14, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT105_P10_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 105, {&MODULE_P10, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT108_P10_6_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 108, {&MODULE_P10, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT13_P00_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 13, {&MODULE_P00, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT38_P32_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 38, {&MODULE_P32, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT3_P02_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 3, {&MODULE_P02, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT49_P22_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 49, {&MODULE_P22, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT56_P21_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 56, {&MODULE_P21, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT60_P20_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 60, {&MODULE_P20, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_3_TOUT82_P14_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 82, {&MODULE_P14, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT102_P10_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 102, {&MODULE_P10, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT14_P00_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 14, {&MODULE_P00, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT39_P32_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 39, {&MODULE_P32, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT4_P02_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 4, {&MODULE_P02, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT50_P22_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 50, {&MODULE_P22, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT57_P21_6_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 57, {&MODULE_P21, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT61_P20_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 61, {&MODULE_P20, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_4_TOUT81_P14_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 81, {&MODULE_P14, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT110_P10_8_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 110, {&MODULE_P10, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT15_P00_6_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 15, {&MODULE_P00, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT23_P33_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 23, {&MODULE_P33, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT40_P32_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 40, {&MODULE_P32, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT41_P23_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 41, {&MODULE_P23, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT58_P21_7_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 58, {&MODULE_P21, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_5_TOUT5_P02_5_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 5, {&MODULE_P02, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT106_P10_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 106, {&MODULE_P10, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT16_P00_7_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 16, {&MODULE_P00, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT24_P33_2_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 24, {&MODULE_P33, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT42_P23_1_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 42, {&MODULE_P23, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT59_P20_0_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 59, {&MODULE_P20, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_6_TOUT6_P02_6_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 6, {&MODULE_P02, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_7_TOUT17_P00_8_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 17, {&MODULE_P00, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_7_TOUT25_P33_3_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 25, {&MODULE_P33, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_7_TOUT45_P23_4_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 45, {&MODULE_P23, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_7_TOUT64_P20_8_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 64, {&MODULE_P20, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM0_7_TOUT7_P02_7_OUT = {IfxGtm_Atom_0, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 7, {&MODULE_P02, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT0_P02_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 0, {&MODULE_P02, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT109_P10_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 109, {&MODULE_P10, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT48_P22_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 48, {&MODULE_P22, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT53_P21_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 53, {&MODULE_P21, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT68_P20_12_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 68, {&MODULE_P20,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT76_P15_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 76, {&MODULE_P15, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT77_P15_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 77, {&MODULE_P15, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT87_P14_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 87, {&MODULE_P14, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT8_P02_8_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 8, {&MODULE_P02, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_0_TOUT9_P00_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 9, {&MODULE_P00, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT103_P10_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 103, {&MODULE_P10, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT10_P00_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 10, {&MODULE_P00, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT11_P00_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 11, {&MODULE_P00, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT1_P02_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 1, {&MODULE_P02, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT31_P33_9_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 31, {&MODULE_P33, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT47_P22_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 47, {&MODULE_P22, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT54_P21_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 54, {&MODULE_P21, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT69_P20_13_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 69, {&MODULE_P20,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT78_P15_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 78, {&MODULE_P15, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT79_P15_8_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 79, {&MODULE_P15, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_1_TOUT86_P14_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 86, {&MODULE_P14, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT104_P10_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 104, {&MODULE_P10, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT107_P10_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 107, {&MODULE_P10, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT12_P00_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 12, {&MODULE_P00, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT2_P02_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 2, {&MODULE_P02, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT33_P33_11_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 33, {&MODULE_P33,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT46_P23_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 46, {&MODULE_P23, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT55_P21_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 55, {&MODULE_P21, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT70_P20_14_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 70, {&MODULE_P20,14}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_2_TOUT80_P14_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 80, {&MODULE_P14, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT105_P10_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 105, {&MODULE_P10, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT108_P10_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 108, {&MODULE_P10, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT13_P00_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 13, {&MODULE_P00, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT38_P32_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 38, {&MODULE_P32, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT3_P02_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 3, {&MODULE_P02, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT49_P22_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 49, {&MODULE_P22, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT56_P21_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 56, {&MODULE_P21, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT60_P20_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 60, {&MODULE_P20, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_3_TOUT71_P15_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 71, {&MODULE_P15, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT102_P10_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 102, {&MODULE_P10, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT14_P00_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 14, {&MODULE_P00, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT39_P32_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 39, {&MODULE_P32, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT4_P02_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 4, {&MODULE_P02, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT50_P22_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 50, {&MODULE_P22, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT57_P21_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 57, {&MODULE_P21, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT61_P20_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 61, {&MODULE_P20, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_4_TOUT72_P15_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 72, {&MODULE_P15, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT110_P10_8_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 110, {&MODULE_P10, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT15_P00_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 15, {&MODULE_P00, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT23_P33_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 23, {&MODULE_P33, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT40_P32_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 40, {&MODULE_P32, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT41_P23_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 41, {&MODULE_P23, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT58_P21_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 58, {&MODULE_P21, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT5_P02_5_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 5, {&MODULE_P02, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT65_P20_9_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 65, {&MODULE_P20, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_5_TOUT73_P15_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 73, {&MODULE_P15, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT106_P10_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 106, {&MODULE_P10, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT16_P00_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 16, {&MODULE_P00, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT24_P33_2_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 24, {&MODULE_P33, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT42_P23_1_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 42, {&MODULE_P23, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT59_P20_0_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 59, {&MODULE_P20, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT66_P20_10_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 66, {&MODULE_P20,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT6_P02_6_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 6, {&MODULE_P02, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_6_TOUT74_P15_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 74, {&MODULE_P15, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT17_P00_8_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 17, {&MODULE_P00, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT25_P33_3_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 25, {&MODULE_P33, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT45_P23_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 45, {&MODULE_P23, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT67_P20_11_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 67, {&MODULE_P20,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT75_P15_4_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 75, {&MODULE_P15, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM1_7_TOUT7_P02_7_OUT = {IfxGtm_Atom_1, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 7, {&MODULE_P02, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_0_TOUT18_P00_9_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 18, {&MODULE_P00, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_0_TOUT26_P33_4_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 26, {&MODULE_P33, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_0_TOUT32_P33_10_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 32, {&MODULE_P33,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_0_TOUT68_P20_12_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 68, {&MODULE_P20,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_0_TOUT94_P13_3_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_c, 94, {&MODULE_P13, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_1_TOUT19_P00_10_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 19, {&MODULE_P00,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_1_TOUT27_P33_5_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 27, {&MODULE_P33, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_1_TOUT43_P23_2_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 43, {&MODULE_P23, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_1_TOUT69_P20_13_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 69, {&MODULE_P20,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_1_TOUT95_P11_2_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_c, 95, {&MODULE_P11, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT20_P00_11_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 20, {&MODULE_P00,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT28_P33_6_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 28, {&MODULE_P33, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT44_P23_3_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 44, {&MODULE_P23, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT70_P20_14_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 70, {&MODULE_P20,14}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT88_P14_8_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 88, {&MODULE_P14, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_2_TOUT96_P11_3_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_c, 96, {&MODULE_P11, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_3_TOUT21_P00_12_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 21, {&MODULE_P00,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_3_TOUT29_P33_7_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 29, {&MODULE_P33, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_3_TOUT71_P15_0_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 71, {&MODULE_P15, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_3_TOUT89_P14_9_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 89, {&MODULE_P14, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_3_TOUT97_P11_6_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_c, 97, {&MODULE_P11, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT22_P33_0_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 22, {&MODULE_P33, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT30_P33_8_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 30, {&MODULE_P33, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT34_P33_12_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 34, {&MODULE_P33,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT51_P21_0_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 51, {&MODULE_P21, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT72_P15_1_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 72, {&MODULE_P15, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT90_P14_10_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 90, {&MODULE_P14,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_4_TOUT98_P11_9_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_c, 98, {&MODULE_P11, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT35_P33_13_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 35, {&MODULE_P33,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT52_P21_1_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 52, {&MODULE_P21, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT65_P20_9_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 65, {&MODULE_P20, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT73_P15_2_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 73, {&MODULE_P15, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT91_P13_0_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 91, {&MODULE_P13, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_5_TOUT99_P11_10_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_c, 99, {&MODULE_P11,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT100_P11_11_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 100, {&MODULE_P11,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT36_P32_0_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 36, {&MODULE_P32, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT62_P20_6_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 62, {&MODULE_P20, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT66_P20_10_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 66, {&MODULE_P20,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT74_P15_3_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 74, {&MODULE_P15, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_6_TOUT92_P13_1_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_c, 92, {&MODULE_P13, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT101_P11_12_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 101, {&MODULE_P11,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT63_P20_7_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 63, {&MODULE_P20, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT64_P20_8_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 64, {&MODULE_P20, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT67_P20_11_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 67, {&MODULE_P20,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT75_P15_4_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 75, {&MODULE_P15, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM2_7_TOUT93_P13_2_OUT = {IfxGtm_Atom_2, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_c, 93, {&MODULE_P13, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT18_P00_9_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 18, {&MODULE_P00, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT26_P33_4_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 26, {&MODULE_P33, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT32_P33_10_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 32, {&MODULE_P33,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT76_P15_5_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 76, {&MODULE_P15, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT77_P15_6_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 77, {&MODULE_P15, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT85_P14_5_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 85, {&MODULE_P14, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT87_P14_7_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 87, {&MODULE_P14, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_0_TOUT94_P13_3_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_0, IfxGtm_ToutSel_d, 94, {&MODULE_P13, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT19_P00_10_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 19, {&MODULE_P00,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT27_P33_5_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 27, {&MODULE_P33, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT43_P23_2_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 43, {&MODULE_P23, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT78_P15_7_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 78, {&MODULE_P15, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT79_P15_8_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 79, {&MODULE_P15, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT84_P14_4_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 84, {&MODULE_P14, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT86_P14_6_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 86, {&MODULE_P14, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_1_TOUT95_P11_2_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_1, IfxGtm_ToutSel_d, 95, {&MODULE_P11, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT20_P00_11_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 20, {&MODULE_P00,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT28_P33_6_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 28, {&MODULE_P33, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT44_P23_3_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 44, {&MODULE_P23, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT80_P14_0_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 80, {&MODULE_P14, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT83_P14_3_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 83, {&MODULE_P14, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT88_P14_8_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 88, {&MODULE_P14, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_2_TOUT96_P11_3_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_2, IfxGtm_ToutSel_d, 96, {&MODULE_P11, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_3_TOUT21_P00_12_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 21, {&MODULE_P00,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_3_TOUT29_P33_7_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 29, {&MODULE_P33, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_3_TOUT82_P14_2_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 82, {&MODULE_P14, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_3_TOUT89_P14_9_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 89, {&MODULE_P14, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_3_TOUT97_P11_6_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_3, IfxGtm_ToutSel_d, 97, {&MODULE_P11, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT22_P33_0_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 22, {&MODULE_P33, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT30_P33_8_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 30, {&MODULE_P33, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT34_P33_12_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 34, {&MODULE_P33,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT51_P21_0_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 51, {&MODULE_P21, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT81_P14_1_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 81, {&MODULE_P14, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT90_P14_10_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 90, {&MODULE_P14,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_4_TOUT98_P11_9_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_4, IfxGtm_ToutSel_d, 98, {&MODULE_P11, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_5_TOUT35_P33_13_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 35, {&MODULE_P33,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_5_TOUT52_P21_1_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 52, {&MODULE_P21, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_5_TOUT91_P13_0_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 91, {&MODULE_P13, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_5_TOUT99_P11_10_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_5, IfxGtm_ToutSel_d, 99, {&MODULE_P11,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_6_TOUT100_P11_11_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 100, {&MODULE_P11,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_6_TOUT36_P32_0_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 36, {&MODULE_P32, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_6_TOUT62_P20_6_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 62, {&MODULE_P20, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_6_TOUT92_P13_1_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_6, IfxGtm_ToutSel_d, 92, {&MODULE_P13, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_7_TOUT101_P11_12_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 101, {&MODULE_P11,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_7_TOUT63_P20_7_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 63, {&MODULE_P20, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Atom_ToutMap IfxGtm_ATOM3_7_TOUT93_P13_2_OUT = {IfxGtm_Atom_3, IfxGtm_Atom_Ch_7, IfxGtm_ToutSel_d, 93, {&MODULE_P13, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Clk_Out IfxGtm_CLK0_P23_1_OUT = {&MODULE_GTM, {&MODULE_P23, 1}, IfxPort_OutputIdx_alt4};
IfxGtm_Clk_Out IfxGtm_CLK1_P32_4_OUT = {&MODULE_GTM, {&MODULE_P32, 4}, IfxPort_OutputIdx_alt4};
IfxGtm_Clk_Out IfxGtm_CLK2_P11_12_OUT = {&MODULE_GTM, {&MODULE_P11,12}, IfxPort_OutputIdx_alt3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN0_P02_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P02, 0}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN109_P10_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P10, 7}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN18_P00_9_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P00, 9}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN26_P33_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P33, 4}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN32_P33_10_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P33,10}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN34_P33_12_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P33,12}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN48_P22_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P22, 1}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN53_P21_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P21, 2}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN77_P15_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P15, 6}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN85_P14_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P14, 5}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN87_P14_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P14, 7}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN8_P02_8_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P02, 8}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM0_0_TIN9_P00_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_0, {&MODULE_P00, 0}, (IfxGtm_ChXSel)13};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN103_P10_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P10, 1}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN10_P00_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P00, 1}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN11_P00_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P00, 2}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN19_P00_10_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P00,10}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN1_P02_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P02, 1}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN27_P33_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P33, 5}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN31_P33_9_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P33, 9}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN35_P33_13_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P33,13}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN47_P22_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P22, 0}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN54_P21_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P21, 3}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN78_P15_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P15, 7}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_1_TIN86_P14_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_1, {&MODULE_P14, 6}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN104_P10_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P10, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN107_P10_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P10, 5}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN20_P00_11_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P00,11}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN28_P33_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P33, 6}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN2_P02_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P02, 2}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN33_P33_11_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P33,11}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN36_P32_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P32, 0}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN46_P23_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P23, 5}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN55_P21_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P21, 4}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_2_TIN79_P15_8_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_2, {&MODULE_P15, 8}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN105_P10_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P10, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN108_P10_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P10, 6}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN21_P00_12_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P00,12}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN29_P33_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P33, 7}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN38_P32_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P32, 2}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN3_P02_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P02, 3}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN49_P22_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P22, 2}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN51_P21_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P21, 0}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN56_P21_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P21, 5}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN60_P20_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P20, 1}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM0_3_TIN80_P14_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_3, {&MODULE_P14, 0}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN102_P10_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P10, 0}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN22_P33_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P33, 0}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN30_P33_8_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P33, 8}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN39_P32_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P32, 3}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN4_P02_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P02, 4}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN50_P22_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P22, 3}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN52_P21_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P21, 1}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN57_P21_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P21, 6}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN61_P20_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P20, 3}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM0_4_TIN81_P14_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_4, {&MODULE_P14, 1}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN110_P10_8_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P10, 8}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN23_P33_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P33, 1}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN40_P32_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P32, 4}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN41_P23_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P23, 0}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN58_P21_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P21, 7}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN5_P02_5_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P02, 5}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_5_TIN82_P14_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_5, {&MODULE_P14, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN106_P10_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P10, 4}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN24_P33_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P33, 2}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN42_P23_1_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P23, 1}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN43_P23_2_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P23, 2}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN59_P20_0_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P20, 0}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN62_P20_6_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P20, 6}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM0_6_TIN83_P14_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_6, {&MODULE_P14, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN25_P33_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P33, 3}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN44_P23_3_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P23, 3}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN45_P23_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P23, 4}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN63_P20_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P20, 7}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN64_P20_8_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P20, 8}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN7_P02_7_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P02, 7}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM0_7_TIN84_P14_4_IN = {IfxGtm_Tim_0, IfxGtm_Tim_Ch_7, {&MODULE_P14, 4}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN0_P02_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P02, 0}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN109_P10_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P10, 7}, (IfxGtm_ChXSel)14};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN18_P00_9_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P00, 9}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN26_P33_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P33, 4}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN32_P33_10_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P33,10}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN48_P22_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P22, 1}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN53_P21_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P21, 2}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN68_P20_12_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P20,12}, (IfxGtm_ChXSel)15};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN76_P15_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P15, 5}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN77_P15_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P15, 6}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN85_P14_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P14, 5}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN87_P14_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P14, 7}, (IfxGtm_ChXSel)13};
IfxGtm_Tim_TinMap IfxGtm_TIM1_0_TIN94_P13_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_0, {&MODULE_P13, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN103_P10_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P10, 1}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN19_P00_10_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P00,10}, (IfxGtm_ChXSel)14};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN1_P02_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P02, 1}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN27_P33_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P33, 5}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN31_P33_9_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P33, 9}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN47_P22_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P22, 0}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN54_P21_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P21, 3}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN69_P20_13_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P20,13}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN78_P15_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P15, 7}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN86_P14_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P14, 6}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_1_TIN95_P11_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_1, {&MODULE_P11, 2}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN104_P10_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P10, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN107_P10_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P10, 5}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN12_P00_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P00, 3}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN20_P00_11_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P00,11}, (IfxGtm_ChXSel)14};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN28_P33_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P33, 6}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN2_P02_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P02, 2}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN33_P33_11_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P33,11}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN46_P23_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P23, 5}, (IfxGtm_ChXSel)15};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN55_P21_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P21, 4}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN70_P20_14_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P20,14}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN79_P15_8_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P15, 8}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_2_TIN96_P11_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_2, {&MODULE_P11, 3}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN105_P10_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P10, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN108_P10_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P10, 6}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN13_P00_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P00, 4}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN21_P00_12_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P00,12}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN29_P33_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P33, 7}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN38_P32_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P32, 2}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN3_P02_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P02, 3}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN49_P22_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P22, 2}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN56_P21_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P21, 5}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN71_P15_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P15, 0}, (IfxGtm_ChXSel)10};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN80_P14_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P14, 0}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_3_TIN97_P11_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_3, {&MODULE_P11, 6}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN102_P10_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P10, 0}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN14_P00_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P00, 5}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN22_P33_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P33, 0}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN30_P33_8_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P33, 8}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN39_P32_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P32, 3}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN4_P02_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P02, 4}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN50_P22_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P22, 3}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN57_P21_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P21, 6}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN72_P15_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P15, 1}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN81_P14_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P14, 1}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_4_TIN98_P11_9_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_4, {&MODULE_P11, 9}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN110_P10_8_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P10, 8}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN15_P00_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P00, 6}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN40_P32_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P32, 4}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN41_P23_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P23, 0}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN58_P21_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P21, 7}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN5_P02_5_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P02, 5}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN65_P20_9_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P20, 9}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN73_P15_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P15, 2}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN82_P14_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P14, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN91_P13_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P13, 0}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_5_TIN99_P11_10_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_5, {&MODULE_P11,10}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN100_P11_11_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P11,11}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN106_P10_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P10, 4}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN16_P00_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P00, 7}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN24_P33_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P33, 2}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN42_P23_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P23, 1}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN43_P23_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P23, 2}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN59_P20_0_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P20, 0}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN66_P20_10_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P20,10}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN6_P02_6_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P02, 6}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN74_P15_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P15, 3}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN83_P14_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P14, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_6_TIN92_P13_1_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_6, {&MODULE_P13, 1}, (IfxGtm_ChXSel)13};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN101_P11_12_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P11,12}, (IfxGtm_ChXSel)12};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN17_P00_8_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P00, 8}, (IfxGtm_ChXSel)11};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN25_P33_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P33, 3}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN44_P23_3_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P23, 3}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN45_P23_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P23, 4}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN64_P20_8_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P20, 8}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN67_P20_11_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P20,11}, (IfxGtm_ChXSel)8};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN75_P15_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P15, 4}, (IfxGtm_ChXSel)7};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN7_P02_7_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P02, 7}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN84_P14_4_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P14, 4}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM1_7_TIN93_P13_2_IN = {IfxGtm_Tim_1, IfxGtm_Tim_Ch_7, {&MODULE_P13, 2}, (IfxGtm_ChXSel)9};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN34_P33_12_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P33,12}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN68_P20_12_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P20,12}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN76_P15_5_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P15, 5}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN8_P02_8_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P02, 8}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN94_P13_3_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P13, 3}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_0_TIN9_P00_0_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_0, {&MODULE_P00, 0}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_1_TIN10_P00_1_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_1, {&MODULE_P00, 1}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_1_TIN11_P00_2_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_1, {&MODULE_P00, 2}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_1_TIN35_P33_13_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_1, {&MODULE_P33,13}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_1_TIN69_P20_13_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_1, {&MODULE_P20,13}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_1_TIN95_P11_2_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_1, {&MODULE_P11, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_2_TIN12_P00_3_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_2, {&MODULE_P00, 3}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_2_TIN36_P32_0_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_2, {&MODULE_P32, 0}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_2_TIN70_P20_14_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_2, {&MODULE_P20,14}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_2_TIN88_P14_8_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_2, {&MODULE_P14, 8}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_2_TIN96_P11_3_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_2, {&MODULE_P11, 3}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_3_TIN13_P00_4_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_3, {&MODULE_P00, 4}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_3_TIN60_P20_1_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_3, {&MODULE_P20, 1}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_3_TIN71_P15_0_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_3, {&MODULE_P15, 0}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_3_TIN89_P14_9_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_3, {&MODULE_P14, 9}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_3_TIN97_P11_6_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_3, {&MODULE_P11, 6}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN14_P00_5_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P00, 5}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN51_P21_0_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P21, 0}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN61_P20_3_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P20, 3}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN72_P15_1_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P15, 1}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN90_P14_10_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P14,10}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_4_TIN98_P11_9_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_4, {&MODULE_P11, 9}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN15_P00_6_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P00, 6}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN52_P21_1_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P21, 1}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN65_P20_9_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P20, 9}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN73_P15_2_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P15, 2}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN91_P13_0_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P13, 0}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_5_TIN99_P11_10_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_5, {&MODULE_P11,10}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN100_P11_11_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P11,11}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN16_P00_7_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P00, 7}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN62_P20_6_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P20, 6}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN66_P20_10_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P20,10}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN74_P15_3_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P15, 3}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_6_TIN92_P13_1_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_6, {&MODULE_P13, 1}, (IfxGtm_ChXSel)3};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN101_P11_12_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P11,12}, (IfxGtm_ChXSel)2};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN17_P00_8_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P00, 8}, (IfxGtm_ChXSel)1};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN63_P20_7_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P20, 7}, (IfxGtm_ChXSel)5};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN67_P20_11_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P20,11}, (IfxGtm_ChXSel)6};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN75_P15_4_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P15, 4}, (IfxGtm_ChXSel)4};
IfxGtm_Tim_TinMap IfxGtm_TIM2_7_TIN93_P13_2_IN = {IfxGtm_Tim_2, IfxGtm_Tim_Ch_7, {&MODULE_P13, 2}, (IfxGtm_ChXSel)3};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT109_P10_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 109, {&MODULE_P10, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT18_P00_9_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 18, {&MODULE_P00, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT26_P33_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 26, {&MODULE_P33, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT32_P33_10_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 32, {&MODULE_P33,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT53_P21_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 53, {&MODULE_P21, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT76_P15_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 76, {&MODULE_P15, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT77_P15_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 77, {&MODULE_P15, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT85_P14_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 85, {&MODULE_P14, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_0_TOUT87_P14_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 87, {&MODULE_P14, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT12_P00_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 12, {&MODULE_P00, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT2_P02_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 2, {&MODULE_P02, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT41_P23_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 41, {&MODULE_P23, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT46_P23_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 46, {&MODULE_P23, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT62_P20_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_b, 62, {&MODULE_P20, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT70_P20_14_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_b, 70, {&MODULE_P20,14}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_10_TOUT96_P11_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 96, {&MODULE_P11, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT13_P00_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 13, {&MODULE_P00, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT3_P02_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 3, {&MODULE_P02, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT43_P23_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 43, {&MODULE_P23, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT49_P22_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 49, {&MODULE_P22, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT63_P20_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_b, 63, {&MODULE_P20, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT71_P15_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_b, 71, {&MODULE_P15, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_11_TOUT97_P11_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 97, {&MODULE_P11, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT14_P00_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 14, {&MODULE_P00, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT34_P33_12_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_b, 34, {&MODULE_P33,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT44_P23_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 44, {&MODULE_P23, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT4_P02_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 4, {&MODULE_P02, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT50_P22_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 50, {&MODULE_P22, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT72_P15_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_b, 72, {&MODULE_P15, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_12_TOUT98_P11_9_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 98, {&MODULE_P11, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT15_P00_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_a, 15, {&MODULE_P00, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT35_P33_13_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_b, 35, {&MODULE_P33,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT5_P02_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_a, 5, {&MODULE_P02, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT65_P20_9_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_b, 65, {&MODULE_P20, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT73_P15_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_b, 73, {&MODULE_P15, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_13_TOUT99_P11_10_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_a, 99, {&MODULE_P11,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT100_P11_11_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_a, 100, {&MODULE_P11,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT16_P00_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_a, 16, {&MODULE_P00, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT36_P32_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_b, 36, {&MODULE_P32, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT66_P20_10_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_b, 66, {&MODULE_P20,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT6_P02_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_a, 6, {&MODULE_P02, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_14_TOUT74_P15_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_b, 74, {&MODULE_P15, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT101_P11_12_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_a, 101, {&MODULE_P11,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT17_P00_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_a, 17, {&MODULE_P00, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT42_P23_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_b, 42, {&MODULE_P23, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT67_P20_11_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_b, 67, {&MODULE_P20,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT75_P15_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_b, 75, {&MODULE_P15, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_15_TOUT7_P02_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_a, 7, {&MODULE_P02, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT103_P10_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 103, {&MODULE_P10, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT19_P00_10_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 19, {&MODULE_P00,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT27_P33_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 27, {&MODULE_P33, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT31_P33_9_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 31, {&MODULE_P33, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT54_P21_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 54, {&MODULE_P21, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT78_P15_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 78, {&MODULE_P15, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_1_TOUT86_P14_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 86, {&MODULE_P14, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT104_P10_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 104, {&MODULE_P10, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT107_P10_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 107, {&MODULE_P10, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT20_P00_11_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 20, {&MODULE_P00,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT28_P33_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 28, {&MODULE_P33, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT33_P33_11_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 33, {&MODULE_P33,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT55_P21_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 55, {&MODULE_P21, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT79_P15_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 79, {&MODULE_P15, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_2_TOUT88_P14_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 88, {&MODULE_P14, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT105_P10_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 105, {&MODULE_P10, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT108_P10_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 108, {&MODULE_P10, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT21_P00_12_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 21, {&MODULE_P00,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT29_P33_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 29, {&MODULE_P33, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT38_P32_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 38, {&MODULE_P32, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT56_P21_5_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 56, {&MODULE_P21, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT60_P20_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 60, {&MODULE_P20, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT80_P14_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 80, {&MODULE_P14, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_3_TOUT89_P14_9_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 89, {&MODULE_P14, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT102_P10_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 102, {&MODULE_P10, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT22_P33_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 22, {&MODULE_P33, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT30_P33_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 30, {&MODULE_P33, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT39_P32_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 39, {&MODULE_P32, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT57_P21_6_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 57, {&MODULE_P21, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT61_P20_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 61, {&MODULE_P20, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT81_P14_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 81, {&MODULE_P14, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_4_TOUT90_P14_10_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 90, {&MODULE_P14,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT110_P10_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 110, {&MODULE_P10, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT23_P33_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 23, {&MODULE_P33, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT40_P32_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 40, {&MODULE_P32, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT58_P21_7_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 58, {&MODULE_P21, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT82_P14_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 82, {&MODULE_P14, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_5_TOUT91_P13_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 91, {&MODULE_P13, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT106_P10_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 106, {&MODULE_P10, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT24_P33_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 24, {&MODULE_P33, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT42_P23_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 42, {&MODULE_P23, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT59_P20_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 59, {&MODULE_P20, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT83_P14_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 83, {&MODULE_P14, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_6_TOUT92_P13_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 92, {&MODULE_P13, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_7_TOUT25_P33_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 25, {&MODULE_P33, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_7_TOUT45_P23_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 45, {&MODULE_P23, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_7_TOUT64_P20_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 64, {&MODULE_P20, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_7_TOUT84_P14_4_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 84, {&MODULE_P14, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_7_TOUT93_P13_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 93, {&MODULE_P13, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT0_P02_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 0, {&MODULE_P02, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT48_P22_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 48, {&MODULE_P22, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT51_P21_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 51, {&MODULE_P21, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT68_P20_12_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_b, 68, {&MODULE_P20,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT8_P02_8_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 8, {&MODULE_P02, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT94_P13_3_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 94, {&MODULE_P13, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT95_P11_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 95, {&MODULE_P11, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_8_TOUT9_P00_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_a, 9, {&MODULE_P00, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT10_P00_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_a, 10, {&MODULE_P00, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT11_P00_2_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_a, 11, {&MODULE_P00, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT1_P02_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_a, 1, {&MODULE_P02, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT47_P22_0_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_a, 47, {&MODULE_P22, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT52_P21_1_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_a, 52, {&MODULE_P21, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM0_9_TOUT69_P20_13_OUT = {IfxGtm_Tom_0, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_b, 69, {&MODULE_P20,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT18_P00_9_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 18, {&MODULE_P00, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT26_P33_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 26, {&MODULE_P33, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT32_P33_10_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 32, {&MODULE_P33,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT48_P22_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 48, {&MODULE_P22, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT53_P21_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 53, {&MODULE_P21, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT68_P20_12_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_a, 68, {&MODULE_P20,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT76_P15_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 76, {&MODULE_P15, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT77_P15_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 77, {&MODULE_P15, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT85_P14_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 85, {&MODULE_P14, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT87_P14_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 87, {&MODULE_P14, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT8_P02_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 8, {&MODULE_P02, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT94_P13_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 94, {&MODULE_P13, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_0_TOUT9_P00_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_0, IfxGtm_ToutSel_b, 9, {&MODULE_P00, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_10_TOUT104_P10_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_b, 104, {&MODULE_P10, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_10_TOUT107_P10_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_b, 107, {&MODULE_P10, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_10_TOUT2_P02_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_b, 2, {&MODULE_P02, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_10_TOUT62_P20_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_10, IfxGtm_ToutSel_a, 62, {&MODULE_P20, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_11_TOUT105_P10_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_b, 105, {&MODULE_P10, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_11_TOUT108_P10_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_b, 108, {&MODULE_P10, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_11_TOUT3_P02_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_b, 3, {&MODULE_P02, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_11_TOUT60_P20_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 60, {&MODULE_P20, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_11_TOUT63_P20_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_11, IfxGtm_ToutSel_a, 63, {&MODULE_P20, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_12_TOUT102_P10_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_b, 102, {&MODULE_P10, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_12_TOUT34_P33_12_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 34, {&MODULE_P33,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_12_TOUT4_P02_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_b, 4, {&MODULE_P02, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_12_TOUT61_P20_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_12, IfxGtm_ToutSel_a, 61, {&MODULE_P20, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_13_TOUT110_P10_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_b, 110, {&MODULE_P10, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_13_TOUT35_P33_13_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_a, 35, {&MODULE_P33,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_13_TOUT5_P02_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_b, 5, {&MODULE_P02, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_13_TOUT65_P20_9_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_13, IfxGtm_ToutSel_a, 65, {&MODULE_P20, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_14_TOUT36_P32_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_a, 36, {&MODULE_P32, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_14_TOUT66_P20_10_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_a, 66, {&MODULE_P20,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_14_TOUT6_P02_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_14, IfxGtm_ToutSel_b, 6, {&MODULE_P02, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_15_TOUT67_P20_11_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_a, 67, {&MODULE_P20,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_15_TOUT7_P02_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_15, IfxGtm_ToutSel_b, 7, {&MODULE_P02, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT10_P00_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 10, {&MODULE_P00, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT11_P00_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 11, {&MODULE_P00, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT19_P00_10_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 19, {&MODULE_P00,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT27_P33_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 27, {&MODULE_P33, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT31_P33_9_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 31, {&MODULE_P33, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT47_P22_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 47, {&MODULE_P22, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT54_P21_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 54, {&MODULE_P21, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT69_P20_13_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_a, 69, {&MODULE_P20,13}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT78_P15_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 78, {&MODULE_P15, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT86_P14_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 86, {&MODULE_P14, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_1_TOUT95_P11_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_1, IfxGtm_ToutSel_b, 95, {&MODULE_P11, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT12_P00_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 12, {&MODULE_P00, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT20_P00_11_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 20, {&MODULE_P00,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT28_P33_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 28, {&MODULE_P33, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT33_P33_11_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 33, {&MODULE_P33,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT46_P23_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 46, {&MODULE_P23, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT55_P21_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 55, {&MODULE_P21, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT70_P20_14_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_a, 70, {&MODULE_P20,14}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT79_P15_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 79, {&MODULE_P15, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT88_P14_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 88, {&MODULE_P14, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_2_TOUT96_P11_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_2, IfxGtm_ToutSel_b, 96, {&MODULE_P11, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT13_P00_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 13, {&MODULE_P00, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT21_P00_12_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 21, {&MODULE_P00,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT29_P33_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 29, {&MODULE_P33, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT38_P32_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 38, {&MODULE_P32, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT49_P22_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 49, {&MODULE_P22, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT56_P21_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 56, {&MODULE_P21, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT71_P15_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_a, 71, {&MODULE_P15, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT80_P14_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 80, {&MODULE_P14, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT89_P14_9_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 89, {&MODULE_P14, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_3_TOUT97_P11_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_3, IfxGtm_ToutSel_b, 97, {&MODULE_P11, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT14_P00_5_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 14, {&MODULE_P00, 5}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT22_P33_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 22, {&MODULE_P33, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT30_P33_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 30, {&MODULE_P33, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT39_P32_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 39, {&MODULE_P32, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT50_P22_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 50, {&MODULE_P22, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT57_P21_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 57, {&MODULE_P21, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT72_P15_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_a, 72, {&MODULE_P15, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT81_P14_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 81, {&MODULE_P14, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT90_P14_10_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 90, {&MODULE_P14,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_4_TOUT98_P11_9_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_4, IfxGtm_ToutSel_b, 98, {&MODULE_P11, 9}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT15_P00_6_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 15, {&MODULE_P00, 6}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT23_P33_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 23, {&MODULE_P33, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT40_P32_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 40, {&MODULE_P32, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT41_P23_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 41, {&MODULE_P23, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT58_P21_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 58, {&MODULE_P21, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT73_P15_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_a, 73, {&MODULE_P15, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT82_P14_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 82, {&MODULE_P14, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT91_P13_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 91, {&MODULE_P13, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_5_TOUT99_P11_10_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_5, IfxGtm_ToutSel_b, 99, {&MODULE_P11,10}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT100_P11_11_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 100, {&MODULE_P11,11}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT106_P10_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 106, {&MODULE_P10, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT16_P00_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 16, {&MODULE_P00, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT24_P33_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 24, {&MODULE_P33, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT43_P23_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 43, {&MODULE_P23, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT59_P20_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 59, {&MODULE_P20, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT74_P15_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_a, 74, {&MODULE_P15, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT83_P14_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 83, {&MODULE_P14, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_6_TOUT92_P13_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_6, IfxGtm_ToutSel_b, 92, {&MODULE_P13, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT101_P11_12_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 101, {&MODULE_P11,12}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT17_P00_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 17, {&MODULE_P00, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT25_P33_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 25, {&MODULE_P33, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT44_P23_3_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 44, {&MODULE_P23, 3}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT45_P23_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 45, {&MODULE_P23, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT64_P20_8_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 64, {&MODULE_P20, 8}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT75_P15_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_a, 75, {&MODULE_P15, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT84_P14_4_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 84, {&MODULE_P14, 4}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_7_TOUT93_P13_2_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_7, IfxGtm_ToutSel_b, 93, {&MODULE_P13, 2}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_8_TOUT0_P02_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_b, 0, {&MODULE_P02, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_8_TOUT109_P10_7_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_b, 109, {&MODULE_P10, 7}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_8_TOUT51_P21_0_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_8, IfxGtm_ToutSel_b, 51, {&MODULE_P21, 0}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_9_TOUT103_P10_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_b, 103, {&MODULE_P10, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_9_TOUT1_P02_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_b, 1, {&MODULE_P02, 1}, IfxPort_OutputIdx_alt1};
IfxGtm_Tom_ToutMap IfxGtm_TOM1_9_TOUT52_P21_1_OUT = {IfxGtm_Tom_1, IfxGtm_Tom_Ch_9, IfxGtm_ToutSel_b, 52, {&MODULE_P21, 1}, IfxPort_OutputIdx_alt1};

#include "IfxGtm_bf.h"

void IfxGtm_PinMap_setTimTin(IfxGtm_Tim_TinMap *config, IfxPort_InputMode inputMode)
{
    uint32 shift = config->channel * 4;

    __ldmst_c(&(MODULE_GTM.INOUTSEL.TIM[config->tim].INSEL.U), (0xFU << shift), ((uint32)config->select) << shift);

    if (inputMode != IfxPort_InputMode_undefined)
    {
        IfxPort_setPinModeInput(config->pin.port, config->pin.pinIndex, inputMode);
    }
}

void IfxGtm_PinMap_setAtomTout(IfxGtm_Atom_ToutMap *config, IfxPort_OutputMode outputMode, IfxPort_PadDriver padDriver)
{
    uint32 outselReg = (config->toutn >> 4);
    uint32 shift = (config->toutn & 0xFU) * 2;
    uint32 outsel = (uint32)config->toutSel << shift;
    uint32 mask = 0x3U << shift;

    __ldmst_c(&(MODULE_GTM.INOUTSEL.T.OUTSEL[outselReg].U), mask, outsel);
    IfxPort_setPinModeOutput(config->pin.port, config->pin.pinIndex, outputMode, config->select);
    IfxPort_setPinPadDriver(config->pin.port, config->pin.pinIndex, padDriver);
}

void IfxGtm_PinMap_setTomTout(IfxGtm_Tom_ToutMap *config, IfxPort_OutputMode outputMode, IfxPort_PadDriver padDriver)
{
    uint32 outselReg = (config->toutn >> 4);
    uint32 shift = (config->toutn & 0xFU) * 2;
    uint32 outsel = (uint32)config->toutSel << shift;
    uint32 mask = 0x3U << shift;

    __ldmst_c(&(MODULE_GTM.INOUTSEL.T.OUTSEL[outselReg].U), mask, outsel);
    IfxPort_setPinModeOutput(config->pin.port, config->pin.pinIndex, outputMode, config->select);
    IfxPort_setPinPadDriver(config->pin.port, config->pin.pinIndex, padDriver);
}
