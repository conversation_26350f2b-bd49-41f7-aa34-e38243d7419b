	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20012a --dep-file=zf_driver_encoder.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_encoder.src ../libraries/zf_driver/zf_driver_encoder.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_encoder.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_encoder.encoder_mapping_set',code,cluster('encoder_mapping_set')
	.sect	'.text.zf_driver_encoder.encoder_mapping_set'
	.align	2
	
; Function encoder_mapping_set
.L97:
encoder_mapping_set:	.type	func
	mov	d15,d6
.L489:
	mov	d0,#0
	jeq	d0,d4,.L2
.L643:
	mov	d0,#1
	jeq	d0,d4,.L3
.L644:
	mov	d0,#2
	jeq	d0,d4,.L4
.L645:
	mov	d0,#3
	jeq	d0,d4,.L5
.L646:
	mov	d0,#4
	jeq	d0,d4,.L6
	j	.L7
.L2:
	jne	d5,#0,.L8
.L647:
	movh.a	a15,#@his(IfxGpt120_T2INA_P00_7_IN)
.L490:
	lea	a15,[a15]@los(IfxGpt120_T2INA_P00_7_IN)
.L648:
	j	.L9
.L8:
	jne	d5,#1,.L10
.L649:
	movh.a	a15,#@his(IfxGpt120_T2INB_P33_7_IN)
.L491:
	lea	a15,[a15]@los(IfxGpt120_T2INB_P33_7_IN)
.L650:
	j	.L11
.L10:
	mov	d4,#0
.L488:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#62
.L487:
	call	debug_assert_handler
.L11:
.L9:
	jne	d15,#0,.L12
.L651:
	movh.a	a12,#@his(IfxGpt120_T2EUDA_P00_8_IN)
.L492:
	lea	a12,[a12]@los(IfxGpt120_T2EUDA_P00_8_IN)
.L652:
	j	.L13
.L12:
	jne	d15,#1,.L14
.L653:
	movh.a	a12,#@his(IfxGpt120_T2EUDB_P33_6_IN)
.L493:
	lea	a12,[a12]@los(IfxGpt120_T2EUDB_P33_6_IN)
.L654:
	j	.L15
.L14:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#66
	call	debug_assert_handler
.L15:
.L13:
	j	.L16
.L3:
	jne	d5,#2,.L17
.L655:
	movh.a	a15,#@his(IfxGpt120_T3INA_P02_6_IN)
.L496:
	lea	a15,[a15]@los(IfxGpt120_T3INA_P02_6_IN)
.L656:
	j	.L18
.L17:
	mov	d4,#0
.L495:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#72
.L494:
	call	debug_assert_handler
.L18:
	jne	d15,#2,.L19
.L657:
	movh.a	a12,#@his(IfxGpt120_T3EUDA_P02_7_IN)
.L497:
	lea	a12,[a12]@los(IfxGpt120_T3EUDA_P02_7_IN)
.L658:
	j	.L20
.L19:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#75
	call	debug_assert_handler
.L20:
	j	.L21
.L4:
	jne	d5,#3,.L22
.L659:
	movh.a	a15,#@his(IfxGpt120_T4INA_P02_8_IN)
.L500:
	lea	a15,[a15]@los(IfxGpt120_T4INA_P02_8_IN)
.L660:
	j	.L23
.L22:
	mov	d4,#0
.L499:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#81
.L498:
	call	debug_assert_handler
.L23:
	jne	d15,#3,.L24
.L661:
	movh.a	a12,#@his(IfxGpt120_T4EUDA_P00_9_IN)
.L501:
	lea	a12,[a12]@los(IfxGpt120_T4EUDA_P00_9_IN)
.L662:
	j	.L25
.L24:
	jne	d15,#4,.L26
.L663:
	movh.a	a12,#@his(IfxGpt120_T4EUDB_P33_5_IN)
.L502:
	lea	a12,[a12]@los(IfxGpt120_T4EUDB_P33_5_IN)
.L664:
	j	.L27
.L26:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#85
	call	debug_assert_handler
.L27:
.L25:
	j	.L28
.L5:
	jne	d5,#4,.L29
.L665:
	movh.a	a15,#@his(IfxGpt120_T5INA_P21_7_IN)
.L505:
	lea	a15,[a15]@los(IfxGpt120_T5INA_P21_7_IN)
.L666:
	j	.L30
.L29:
	jne	d5,#5,.L31
.L667:
	movh.a	a15,#@his(IfxGpt120_T5INB_P10_3_IN)
.L506:
	lea	a15,[a15]@los(IfxGpt120_T5INB_P10_3_IN)
.L668:
	j	.L32
.L31:
	mov	d4,#0
.L504:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#92
.L503:
	call	debug_assert_handler
.L32:
.L30:
	jne	d15,#5,.L33
.L669:
	movh.a	a12,#@his(IfxGpt120_T5EUDA_P21_6_IN)
.L507:
	lea	a12,[a12]@los(IfxGpt120_T5EUDA_P21_6_IN)
.L670:
	j	.L34
.L33:
	jne	d15,#6,.L35
.L671:
	movh.a	a12,#@his(IfxGpt120_T5EUDB_P10_1_IN)
.L508:
	lea	a12,[a12]@los(IfxGpt120_T5EUDB_P10_1_IN)
.L672:
	j	.L36
.L35:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#96
	call	debug_assert_handler
.L36:
.L34:
	j	.L37
.L6:
	jne	d5,#6,.L38
.L673:
	movh.a	a15,#@his(IfxGpt120_T6INA_P20_3_IN)
.L511:
	lea	a15,[a15]@los(IfxGpt120_T6INA_P20_3_IN)
.L674:
	j	.L39
.L38:
	jne	d5,#7,.L40
.L675:
	movh.a	a15,#@his(IfxGpt120_T6INB_P10_2_IN)
.L512:
	lea	a15,[a15]@los(IfxGpt120_T6INB_P10_2_IN)
.L676:
	j	.L41
.L40:
	mov	d4,#0
.L510:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#103
.L509:
	call	debug_assert_handler
.L41:
.L39:
	jne	d15,#7,.L42
.L677:
	movh.a	a12,#@his(IfxGpt120_T6EUDA_P20_0_IN)
.L513:
	lea	a12,[a12]@los(IfxGpt120_T6EUDA_P20_0_IN)
.L678:
	j	.L43
.L42:
	mov	d4,#0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#106
	call	debug_assert_handler
.L43:
	j	.L44
.L7:
.L44:
.L37:
.L28:
.L21:
.L16:
	mov	d4,#16
.L679:
	mov	d5,#0
.L514:
	mov.aa	a4,a15
.L515:
	call	IfxGpt12_initTxInPinWithPadLevel
.L516:
	mov	d4,#16
.L680:
	mov	d5,#0
.L517:
	mov.aa	a4,a12
.L518:
	call	IfxGpt12_initTxEudInPinWithPadLevel
.L519:
	ret
.L478:
	
__encoder_mapping_set_function_end:
	.size	encoder_mapping_set,__encoder_mapping_set_function_end-encoder_mapping_set
.L134:
	; End of function
	
	.sdecl	'.text.zf_driver_encoder.encoder_get_count',code,cluster('encoder_get_count')
	.sect	'.text.zf_driver_encoder.encoder_get_count'
	.align	2
	
	.global	encoder_get_count
; Function encoder_get_count
.L99:
encoder_get_count:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L45
.L545:
	mov	d15,#1
	jeq	d15,d4,.L46
.L546:
	mov	d15,#2
	jeq	d15,d4,.L47
.L547:
	mov	d15,#3
	jeq	d15,d4,.L48
.L548:
	mov	d15,#4
	jeq	d15,d4,.L49
	j	.L50
.L45:
	ld.w	d15,0xf0002e34
.L549:
	extr.u	d15,d15,#0,#16
.L550:
	j	.L51
.L51:
	extr	d2,d15,#0,#16
.L520:
	j	.L52
.L46:
	ld.w	d15,0xf0002e38
.L551:
	extr.u	d15,d15,#0,#16
.L552:
	j	.L53
.L53:
	extr	d2,d15,#0,#16
.L521:
	j	.L54
.L47:
	ld.w	d15,0xf0002e3c
.L553:
	extr.u	d15,d15,#0,#16
.L554:
	j	.L55
.L55:
	extr	d2,d15,#0,#16
.L522:
	j	.L56
.L48:
	ld.w	d15,0xf0002e40
.L555:
	extr.u	d15,d15,#0,#16
.L556:
	j	.L57
.L57:
	extr	d2,d15,#0,#16
.L523:
	j	.L58
.L49:
	ld.w	d15,0xf0002e44
.L557:
	extr.u	d15,d15,#0,#16
.L558:
	j	.L59
.L59:
	extr	d2,d15,#0,#16
.L524:
	j	.L60
.L50:
	mov	d2,#0
.L60:
.L58:
.L56:
.L54:
.L52:
	movh.a	a15,#@his(encoder_mode)
	lea	a15,[a15]@los(encoder_mode)
.L559:
	addsc.a	a15,a15,d4,#0
	ld.bu	d15,[a15]
.L560:
	jne	d15,#0,.L61
.L561:
	mov	d15,#4
.L562:
	div	e2,d2,d15
.L61:
	j	.L62
.L62:
	ret
.L138:
	
__encoder_get_count_function_end:
	.size	encoder_get_count,__encoder_get_count_function_end-encoder_get_count
.L114:
	; End of function
	
	.sdecl	'.text.zf_driver_encoder.encoder_clear_count',code,cluster('encoder_clear_count')
	.sect	'.text.zf_driver_encoder.encoder_clear_count'
	.align	2
	
	.global	encoder_clear_count
; Function encoder_clear_count
.L101:
encoder_clear_count:	.type	func
	mov	d15,#0
	jeq	d15,d4,.L63
.L567:
	mov	d15,#1
	jeq	d15,d4,.L64
.L568:
	mov	d15,#2
	jeq	d15,d4,.L65
.L569:
	mov	d15,#3
	jeq	d15,d4,.L66
.L570:
	mov	d15,#4
	jeq	d15,d4,.L67
	j	.L68
.L63:
	mov	d15,#0
.L165:
	st.w	0xf0002e34,d15
.L166:
	j	.L69
.L64:
	mov	d15,#0
.L173:
	st.w	0xf0002e38,d15
.L174:
	j	.L70
.L65:
	mov	d15,#0
.L181:
	st.w	0xf0002e3c,d15
.L182:
	j	.L71
.L66:
	mov	d15,#0
.L189:
	st.w	0xf0002e40,d15
.L190:
	j	.L72
.L67:
	mov	d15,#0
.L197:
	st.w	0xf0002e44,d15
.L198:
	j	.L73
.L68:
.L73:
.L72:
.L71:
.L70:
.L69:
	ret
.L162:
	
__encoder_clear_count_function_end:
	.size	encoder_clear_count,__encoder_clear_count_function_end-encoder_clear_count
.L119:
	; End of function
	
	.sdecl	'.text.zf_driver_encoder.encoder_quad_init',code,cluster('encoder_quad_init')
	.sect	'.text.zf_driver_encoder.encoder_quad_init'
	.align	2
	
	.global	encoder_quad_init
; Function encoder_quad_init
.L103:
encoder_quad_init:	.type	func
	mov	d15,d4
.L526:
	mov	e8,d6,d5
.L575:
	lea	a4,0xf0002e00
	call	IfxGpt12_enableModule
.L525:
	mov	d0,#1
.L211:
	ld.bu	d1,0xf0002e15
.L576:
	insert	d0,d1,d0,#3,#2
	st.b	0xf0002e15,d0
.L212:
	mov	d0,#0
.L219:
	ld.bu	d1,0xf0002e21
.L577:
	insert	d0,d1,d0,#3,#2
	st.b	0xf0002e21,d0
.L220:
	mov	e4,d8,d15
.L528:
	mov	d6,d9
.L529:
	call	encoder_mapping_set
.L530:
	mov	d0,#0
	jeq	d15,d0,.L74
.L578:
	mov	d0,#1
	jeq	d15,d0,.L75
.L579:
	mov	d0,#2
	jeq	d15,d0,.L76
.L580:
	mov	d0,#3
	jeq	d15,d0,.L77
.L581:
	mov	d0,#4
	jeq	d15,d0,.L78
	j	.L79
.L74:
	mov	d0,#3
.L227:
	ld.bu	d1,0xf0002e10
.L582:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e10,d0
.L228:
	mov	d0,#1
.L235:
	ld.bu	d1,0xf0002e11
.L583:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e11,d0
.L236:
	mov	d0,#7
.L243:
	ld.bu	d1,0xf0002e10
.L584:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e10,d0
.L244:
	mov	d0,#1
.L251:
	ld.bu	d1,0xf0002e10
.L585:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e10,d0
.L252:
	j	.L80
.L75:
	mov	d0,#3
.L259:
	ld.bu	d1,0xf0002e14
.L586:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e14,d0
.L260:
	mov	d0,#1
.L267:
	ld.bu	d1,0xf0002e15
.L587:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e15,d0
.L268:
	mov	d0,#7
.L275:
	ld.bu	d1,0xf0002e14
.L588:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e14,d0
.L276:
	mov	d0,#1
.L283:
	ld.bu	d1,0xf0002e14
.L589:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e14,d0
.L284:
	j	.L81
.L76:
	mov	d0,#3
.L291:
	ld.bu	d1,0xf0002e18
.L590:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e18,d0
.L292:
	mov	d0,#1
.L299:
	ld.bu	d1,0xf0002e19
.L591:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e19,d0
.L300:
	mov	d0,#7
.L307:
	ld.bu	d1,0xf0002e18
.L592:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e18,d0
.L308:
	mov	d0,#1
.L315:
	ld.bu	d1,0xf0002e18
.L593:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e18,d0
.L316:
	j	.L82
.L77:
	mov	d0,#3
.L323:
	ld.bu	d1,0xf0002e1c
.L594:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e1c,d0
.L324:
	mov	d0,#1
.L331:
	ld.bu	d1,0xf0002e1d
.L595:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e1d,d0
.L332:
	mov	d0,#7
.L339:
	ld.bu	d1,0xf0002e1c
.L596:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e1c,d0
.L340:
	mov	d0,#1
.L347:
	ld.bu	d1,0xf0002e1c
.L597:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e1c,d0
.L348:
	j	.L83
.L78:
	mov	d0,#3
.L355:
	ld.bu	d1,0xf0002e20
.L598:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e20,d0
.L356:
	mov	d0,#1
.L363:
	ld.bu	d1,0xf0002e21
.L599:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e21,d0
.L364:
	mov	d0,#7
.L371:
	ld.bu	d1,0xf0002e20
.L600:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e20,d0
.L372:
	mov	d0,#1
.L379:
	ld.bu	d1,0xf0002e20
.L601:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e20,d0
.L380:
	j	.L84
.L79:
.L84:
.L83:
.L82:
.L81:
.L80:
	movh.a	a15,#@his(encoder_mode)
	lea	a15,[a15]@los(encoder_mode)
.L602:
	addsc.a	a15,a15,d15,#0
.L603:
	mov	d15,#0
.L527:
	st.b	[a15],d15
.L604:
	ret
.L204:
	
__encoder_quad_init_function_end:
	.size	encoder_quad_init,__encoder_quad_init_function_end-encoder_quad_init
.L124:
	; End of function
	
	.sdecl	'.text.zf_driver_encoder.encoder_dir_init',code,cluster('encoder_dir_init')
	.sect	'.text.zf_driver_encoder.encoder_dir_init'
	.align	2
	
	.global	encoder_dir_init
; Function encoder_dir_init
.L105:
encoder_dir_init:	.type	func
	mov	d15,d4
.L532:
	mov	e8,d6,d5
.L609:
	lea	a4,0xf0002e00
	call	IfxGpt12_enableModule
.L531:
	mov	d0,#1
.L390:
	ld.bu	d1,0xf0002e15
.L610:
	insert	d0,d1,d0,#3,#2
	st.b	0xf0002e15,d0
.L391:
	mov	d0,#0
.L394:
	ld.bu	d1,0xf0002e21
.L611:
	insert	d0,d1,d0,#3,#2
	st.b	0xf0002e21,d0
.L395:
	mov	e4,d8,d15
.L534:
	mov	d6,d9
.L535:
	call	encoder_mapping_set
.L536:
	mov	d0,#0
	jeq	d15,d0,.L85
.L612:
	mov	d0,#1
	jeq	d15,d0,.L86
.L613:
	mov	d0,#2
	jeq	d15,d0,.L87
.L614:
	mov	d0,#3
	jeq	d15,d0,.L88
.L615:
	mov	d0,#4
	jeq	d15,d0,.L89
	j	.L90
.L85:
	mov	d0,#1
.L398:
	ld.bu	d1,0xf0002e10
.L616:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e10,d0
.L399:
	mov	d0,#1
.L402:
	ld.bu	d1,0xf0002e11
.L617:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e11,d0
.L403:
	mov	d0,#1
.L406:
	ld.bu	d1,0xf0002e10
.L618:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e10,d0
.L407:
	mov	d0,#1
.L410:
	ld.bu	d1,0xf0002e10
.L619:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e10,d0
.L411:
	j	.L91
.L86:
	mov	d0,#1
.L414:
	ld.bu	d1,0xf0002e14
.L620:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e14,d0
.L415:
	mov	d0,#1
.L418:
	ld.bu	d1,0xf0002e15
.L621:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e15,d0
.L419:
	mov	d0,#1
.L422:
	ld.bu	d1,0xf0002e14
.L622:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e14,d0
.L423:
	mov	d0,#1
.L426:
	ld.bu	d1,0xf0002e14
.L623:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e14,d0
.L427:
	j	.L92
.L87:
	mov	d0,#1
.L430:
	ld.bu	d1,0xf0002e18
.L624:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e18,d0
.L431:
	mov	d0,#1
.L434:
	ld.bu	d1,0xf0002e19
.L625:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e19,d0
.L435:
	mov	d0,#1
.L438:
	ld.bu	d1,0xf0002e18
.L626:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e18,d0
.L439:
	mov	d0,#1
.L442:
	ld.bu	d1,0xf0002e18
.L627:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e18,d0
.L443:
	j	.L93
.L88:
	mov	d0,#1
.L446:
	ld.bu	d1,0xf0002e1c
.L628:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e1c,d0
.L447:
	mov	d0,#1
.L450:
	ld.bu	d1,0xf0002e1d
.L629:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e1d,d0
.L451:
	mov	d0,#1
.L454:
	ld.bu	d1,0xf0002e1c
.L630:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e1c,d0
.L455:
	mov	d0,#1
.L458:
	ld.bu	d1,0xf0002e1c
.L631:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e1c,d0
.L459:
	j	.L94
.L89:
	mov	d0,#1
.L462:
	ld.bu	d1,0xf0002e20
.L632:
	insert	d0,d1,d0,#0,#3
	st.b	0xf0002e20,d0
.L463:
	mov	d0,#1
.L466:
	ld.bu	d1,0xf0002e21
.L633:
	insert	d0,d1,d0,#0,#1
	st.b	0xf0002e21,d0
.L467:
	mov	d0,#1
.L470:
	ld.bu	d1,0xf0002e20
.L634:
	insert	d0,d1,d0,#3,#3
	st.b	0xf0002e20,d0
.L471:
	mov	d0,#1
.L474:
	ld.bu	d1,0xf0002e20
.L635:
	insert	d0,d1,d0,#6,#1
	st.b	0xf0002e20,d0
.L475:
	j	.L95
.L90:
.L95:
.L94:
.L93:
.L92:
.L91:
	movh.a	a15,#@his(encoder_mode)
	lea	a15,[a15]@los(encoder_mode)
.L636:
	addsc.a	a15,a15,d15,#0
.L637:
	mov	d15,#1
.L533:
	st.b	[a15],d15
.L638:
	ret
.L386:
	
__encoder_dir_init_function_end:
	.size	encoder_dir_init,__encoder_dir_init_function_end-encoder_dir_init
.L129:
	; End of function
	
	.sdecl	'.data.zf_driver_encoder.encoder_mode',data,cluster('encoder_mode')
	.sect	'.data.zf_driver_encoder.encoder_mode'
encoder_mode:	.type	object
	.size	encoder_mode,5
	.space	5
	.sdecl	'.rodata.zf_driver_encoder..1.str',data,rom
	.sect	'.rodata.zf_driver_encoder..1.str'
.1.str:	.type	object
	.size	.1.str,43
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,101,110,99,111,100,101,114
	.byte	46,99
	.space	1
	.calls	'encoder_mapping_set','debug_assert_handler'
	.calls	'encoder_mapping_set','IfxGpt12_initTxInPinWithPadLevel'
	.calls	'encoder_mapping_set','IfxGpt12_initTxEudInPinWithPadLevel'
	.calls	'encoder_quad_init','IfxGpt12_enableModule'
	.calls	'encoder_quad_init','encoder_mapping_set'
	.calls	'encoder_dir_init','IfxGpt12_enableModule'
	.calls	'encoder_dir_init','encoder_mapping_set'
	.calls	'encoder_mapping_set','',0
	.calls	'encoder_get_count','',0
	.calls	'encoder_clear_count','',0
	.calls	'encoder_quad_init','',0
	.extern	IfxGpt120_T2EUDA_P00_8_IN
	.extern	IfxGpt120_T2EUDB_P33_6_IN
	.extern	IfxGpt120_T3EUDA_P02_7_IN
	.extern	IfxGpt120_T4EUDA_P00_9_IN
	.extern	IfxGpt120_T4EUDB_P33_5_IN
	.extern	IfxGpt120_T5EUDA_P21_6_IN
	.extern	IfxGpt120_T5EUDB_P10_1_IN
	.extern	IfxGpt120_T6EUDA_P20_0_IN
	.extern	IfxGpt120_T2INA_P00_7_IN
	.extern	IfxGpt120_T2INB_P33_7_IN
	.extern	IfxGpt120_T3INA_P02_6_IN
	.extern	IfxGpt120_T4INA_P02_8_IN
	.extern	IfxGpt120_T5INA_P21_7_IN
	.extern	IfxGpt120_T5INB_P10_3_IN
	.extern	IfxGpt120_T6INA_P20_3_IN
	.extern	IfxGpt120_T6INB_P10_2_IN
	.extern	IfxGpt12_enableModule
	.extern	IfxGpt12_initTxEudInPinWithPadLevel
	.extern	IfxGpt12_initTxInPinWithPadLevel
	.extern	debug_assert_handler
	.calls	'encoder_dir_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L107:
	.word	95280
	.half	3
	.word	.L108
	.byte	4
.L106:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L109
	.byte	2
	.byte	'unsigned long int',0,4,7,2
	.byte	'unsigned char',0,1,8,3,1,106,5,1,4
	.byte	'notSynchronised',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'signalLoss',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'signalDegradation',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'trackingLoss',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'commError',0,1
	.word	226
	.byte	1,3,2,35,0,0,5,1,103,9,4,6
	.byte	'status',0
	.word	205
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	243
	.byte	1,2,35,0,0,7
	.byte	'void',0,8
	.word	405
	.byte	9
	.byte	'IfxStdIf_InterfaceDriver',0,2,118,15
	.word	411
	.byte	10,1,1,11
	.word	411
	.byte	0,8
	.word	449
	.byte	9
	.byte	'IfxStdIf_Pos_OnZeroIrq',0,1,135,1,16
	.word	458
	.byte	2
	.byte	'float',0,4,4,12
	.word	495
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	504
	.byte	9
	.byte	'IfxStdIf_Pos_GetAbsolutePosition',0,1,129,1,19
	.word	517
	.byte	2
	.byte	'long int',0,4,5,12
	.word	564
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	576
	.byte	9
	.byte	'IfxStdIf_Pos_GetOffset',0,1,142,1,18
	.word	589
	.byte	9
	.byte	'IfxStdIf_Pos_GetPosition',0,1,152,1,19
	.word	517
	.byte	13,1,95,9,1,14
	.byte	'IfxStdIf_Pos_Dir_forward',0,0,14
	.byte	'IfxStdIf_Pos_Dir_backward',0,1,14
	.byte	'IfxStdIf_Pos_Dir_unknown',0,2,0,12
	.word	660
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	748
	.byte	9
	.byte	'IfxStdIf_Pos_GetDirection',0,1,161,1,28
	.word	761
	.byte	12
	.word	372
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	801
	.byte	9
	.byte	'IfxStdIf_Pos_GetFault',0,1,168,1,31
	.word	814
	.byte	9
	.byte	'IfxStdIf_Pos_GetRawPosition',0,1,184,1,18
	.word	589
	.byte	2
	.byte	'unsigned short int',0,2,7,12
	.word	887
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	909
	.byte	9
	.byte	'IfxStdIf_Pos_GetPeriodPerRotation',0,1,175,1,18
	.word	922
	.byte	9
	.byte	'IfxStdIf_Pos_GetRefreshPeriod',0,1,190,1,19
	.word	517
	.byte	9
	.byte	'IfxStdIf_Pos_GetResolution',0,1,196,1,18
	.word	589
	.byte	13,1,84,9,1,14
	.byte	'IfxStdIf_Pos_SensorType_encoder',0,0,14
	.byte	'IfxStdIf_Pos_SensorType_hall',0,1,14
	.byte	'IfxStdIf_Pos_SensorType_resolver',0,2,14
	.byte	'IfxStdIf_Pos_SensorType_angletrk',0,3,14
	.byte	'IfxStdIf_Pos_SensorType_igmr',0,4,14
	.byte	'IfxStdIf_Pos_SensorType_virtual',0,5,0,12
	.word	1045
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	1251
	.byte	9
	.byte	'IfxStdIf_Pos_GetSensorType',0,1,202,1,35
	.word	1264
	.byte	9
	.byte	'IfxStdIf_Pos_GetTurn',0,1,214,1,18
	.word	589
	.byte	9
	.byte	'IfxStdIf_Pos_OnEventA',0,1,221,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_Pos_Reset',0,1,239,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_Pos_ResetFaults',0,1,248,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_Pos_GetSpeed',0,1,208,1,19
	.word	517
	.byte	9
	.byte	'IfxStdIf_Pos_Update',0,1,230,1,16
	.word	458
	.byte	10,1,1,11
	.word	411
	.byte	11
	.word	564
	.byte	0,8
	.word	1488
	.byte	9
	.byte	'IfxStdIf_Pos_SetOffset',0,1,255,1,16
	.word	1502
	.byte	10,1,1,11
	.word	411
	.byte	11
	.word	495
	.byte	0,8
	.word	1539
	.byte	9
	.byte	'IfxStdIf_Pos_SetPosition',0,1,134,2,16
	.word	1553
	.byte	9
	.byte	'IfxStdIf_Pos_SetRawPosition',0,1,140,2,16
	.word	1502
	.byte	9
	.byte	'IfxStdIf_Pos_SetSpeed',0,1,147,2,16
	.word	1553
	.byte	9
	.byte	'IfxStdIf_Pos_SetRefreshPeriod',0,1,154,2,16
	.word	1553
	.byte	15
	.byte	'IfxStdIf_Pos_',0,1,158,2,8,92,6
	.byte	'driver',0
	.word	416
	.byte	4,2,35,0,6
	.byte	'onZeroIrq',0
	.word	463
	.byte	4,2,35,4,6
	.byte	'getAbsolutePosition',0
	.word	522
	.byte	4,2,35,8,6
	.byte	'getOffset',0
	.word	594
	.byte	4,2,35,12,6
	.byte	'getPosition',0
	.word	626
	.byte	4,2,35,16,6
	.byte	'getDirection',0
	.word	766
	.byte	4,2,35,20,6
	.byte	'getFault',0
	.word	819
	.byte	4,2,35,24,6
	.byte	'getRawPosition',0
	.word	850
	.byte	4,2,35,28,6
	.byte	'getPeriodPerRotation',0
	.word	927
	.byte	4,2,35,32,6
	.byte	'getRefreshPeriod',0
	.word	970
	.byte	4,2,35,36,6
	.byte	'getResolution',0
	.word	1009
	.byte	4,2,35,40,6
	.byte	'getSensorType',0
	.word	1269
	.byte	4,2,35,44,6
	.byte	'getTurn',0
	.word	1305
	.byte	4,2,35,48,6
	.byte	'onEventA',0
	.word	1335
	.byte	4,2,35,52,6
	.byte	'reset',0
	.word	1366
	.byte	4,2,35,56,6
	.byte	'resetFaults',0
	.word	1394
	.byte	4,2,35,60,6
	.byte	'getSpeed',0
	.word	1428
	.byte	4,2,35,64,6
	.byte	'update',0
	.word	1459
	.byte	4,2,35,68,6
	.byte	'setOffset',0
	.word	1507
	.byte	4,2,35,72,6
	.byte	'setPosition',0
	.word	1558
	.byte	4,2,35,76,6
	.byte	'setRawPosition',0
	.word	1592
	.byte	4,2,35,80,6
	.byte	'setSpeed',0
	.word	1629
	.byte	4,2,35,84,6
	.byte	'setRefreshPeriod',0
	.word	1660
	.byte	4,2,35,88,0,8
	.word	1699
	.byte	16
	.byte	'IfxStdIf_Pos_getFault',0,3,1,225,2,32
	.word	372
	.byte	1,1,17
	.byte	'stdIf',0,1,225,2,68
	.word	2203
	.byte	18,0,8
	.word	405
	.byte	8
	.word	449
	.byte	8
	.word	504
	.byte	8
	.word	576
	.byte	8
	.word	504
	.byte	8
	.word	748
	.byte	8
	.word	801
	.byte	8
	.word	576
	.byte	8
	.word	909
	.byte	8
	.word	504
	.byte	8
	.word	576
	.byte	8
	.word	1251
	.byte	8
	.word	576
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	504
	.byte	8
	.word	449
	.byte	8
	.word	1488
	.byte	8
	.word	1539
	.byte	8
	.word	1488
	.byte	8
	.word	1539
	.byte	8
	.word	1539
	.byte	19,1,1,8
	.word	2374
	.byte	20
	.byte	'Ifx__jump_and_link',0,3,3,61,17,1,1,17
	.byte	'fun',0,3,61,43
	.word	2377
	.byte	18,0,2
	.byte	'__fract',0,4,128,1,16
	.byte	'Ifx__float_to_fract',0,3,3,152,2,18
	.word	2422
	.byte	1,1,17
	.byte	'a',0,3,152,2,44
	.word	495
	.byte	18,0,20
	.byte	'Ifx__stopPerfCounters',0,3,3,172,2,17,1,1,18,0,2
	.byte	'unsigned long long int',0,8,7,16
	.byte	'__ld64',0,3,4,135,1,19
	.word	2511
	.byte	1,1,17
	.byte	'addr',0,4,135,1,32
	.word	411
	.byte	18,0,20
	.byte	'__st64',0,3,4,143,1,17,1,1,17
	.byte	'addr',0,4,143,1,30
	.word	411
	.byte	17
	.byte	'value',0,4,143,1,43
	.word	2511
	.byte	18,0,2
	.byte	'unsigned int',0,4,7,2
	.byte	'int',0,4,5,15
	.byte	'_Ifx_SRC_SRCR_Bits',0,6,45,16,4,4
	.byte	'SRPN',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'SRE',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'TOS',0,1
	.word	226
	.byte	2,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	226
	.byte	3,0,2,35,1,4
	.byte	'ECC',0,1
	.word	226
	.byte	6,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'SRR',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'CLRR',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'SETR',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'IOV',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'IOVCLR',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'SWS',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'SWSCLR',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,6,70,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	2641
	.byte	4,2,35,0,0,21
	.word	2931
	.byte	8
	.word	2970
	.byte	20
	.byte	'IfxSrc_clearRequest',0,3,5,250,1,17,1,1,17
	.byte	'src',0,5,250,1,60
	.word	2975
	.byte	18,0,2
	.byte	'unsigned int',0,4,7,15
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,8,241,8,16,4,4
	.byte	'ENDINIT',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'LCK',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'PW',0,4
	.word	3023
	.byte	14,16,2,35,0,4
	.byte	'REL',0,4
	.word	3023
	.byte	16,0,2,35,0,0,5,8,247,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3039
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,8,250,8,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'IR0',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'DR',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'IR1',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'UR',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PAR',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'TCR',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'TCTR',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,8,255,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3175
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,8,137,9,16,4,4
	.byte	'AE',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'OE',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'IS0',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'DS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'TO',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'IS1',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'US',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PAS',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'TCS',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'TCT',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'TIM',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,8,135,15,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	3419
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_SCU_WDTCPU',0,8,175,15,25,12,6
	.byte	'CON0',0
	.word	3135
	.byte	4,2,35,0,6
	.byte	'CON1',0
	.word	3379
	.byte	4,2,35,4,6
	.byte	'SR',0
	.word	3610
	.byte	4,2,35,8,0,21
	.word	3650
	.byte	8
	.word	3713
	.byte	20
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,7,181,3,17,1,1,17
	.byte	'watchdog',0,7,181,3,65
	.word	3718
	.byte	17
	.byte	'password',0,7,181,3,82
	.word	887
	.byte	18,0,20
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,7,140,4,17,1,1,17
	.byte	'watchdog',0,7,140,4,63
	.word	3718
	.byte	17
	.byte	'password',0,7,140,4,80
	.word	887
	.byte	18,0,16
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,7,227,3,19
	.word	887
	.byte	1,1,17
	.byte	'watchdog',0,7,227,3,74
	.word	3718
	.byte	18,0,13,10,156,1,9,1,14
	.byte	'IfxCpu_ResourceCpu_0',0,0,14
	.byte	'IfxCpu_ResourceCpu_1',0,1,14
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,16
	.byte	'IfxCpu_getCoreIndex',0,3,9,141,6,31
	.word	3948
	.byte	1,1,18,0,16
	.byte	'IfxCpu_areInterruptsEnabled',0,3,9,139,5,20
	.word	226
	.byte	1,1,18,0,16
	.byte	'IfxCpu_getPerformanceCounter',0,3,9,161,6,19
	.word	205
	.byte	1,1,17
	.byte	'address',0,9,161,6,55
	.word	887
	.byte	18,0,16
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,9,190,6,20
	.word	226
	.byte	1,1,17
	.byte	'address',0,9,190,6,70
	.word	887
	.byte	18,0,20
	.byte	'IfxCpu_updatePerformanceCounter',0,3,9,172,8,17,1,1,17
	.byte	'address',0,9,172,8,56
	.word	205
	.byte	17
	.byte	'count',0,9,172,8,72
	.word	205
	.byte	22,18,0,0,15
	.byte	'_Ifx_P_OUT_Bits',0,12,143,3,16,4,4
	.byte	'P0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'P2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'P3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'P4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'P5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'P6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'P7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'P8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'P9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'P10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'P11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'P12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'P13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'P14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'P15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,181,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	4313
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMR_Bits',0,12,169,2,16,4,4
	.byte	'PS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PS4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PS8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'PS12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'PCL0',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'PCL4',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'PCL8',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'PCL12',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,133,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	4629
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_ID_Bits',0,12,110,16,4,4
	.byte	'MODREV',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,148,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5200
	.byte	4,2,35,0,0,23,4
	.word	226
	.byte	24,3,0,15
	.byte	'_Ifx_P_IOCR0_Bits',0,12,140,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PC0',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PC1',0,1
	.word	226
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PC2',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PC3',0,1
	.word	226
	.byte	5,0,2,35,3,0,5,12,164,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5328
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR4_Bits',0,12,166,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PC4',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PC5',0,1
	.word	226
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PC6',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PC7',0,1
	.word	226
	.byte	5,0,2,35,3,0,5,12,180,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5543
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR8_Bits',0,12,179,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PC8',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PC9',0,1
	.word	226
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PC10',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PC11',0,1
	.word	226
	.byte	5,0,2,35,3,0,5,12,188,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5758
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IOCR12_Bits',0,12,153,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PC12',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PC13',0,1
	.word	226
	.byte	5,0,2,35,1,4
	.byte	'reserved_16',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PC14',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PC15',0,1
	.word	226
	.byte	5,0,2,35,3,0,5,12,172,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	5975
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_IN_Bits',0,12,118,16,4,4
	.byte	'P0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'P2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'P3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'P4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'P5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'P6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'P7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'P8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'P9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'P10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'P11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'P12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'P13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'P14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'P15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,156,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6195
	.byte	4,2,35,0,0,23,24
	.word	226
	.byte	24,23,0,15
	.byte	'_Ifx_P_PDR0_Bits',0,12,205,3,16,4,4
	.byte	'PD0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PL0',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PD1',0,1
	.word	226
	.byte	3,1,2,35,0,4
	.byte	'PL1',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PD2',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PL2',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'PD3',0,1
	.word	226
	.byte	3,1,2,35,1,4
	.byte	'PL3',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'PD4',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PL4',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'PD5',0,1
	.word	226
	.byte	3,1,2,35,2,4
	.byte	'PL5',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'PD6',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PL6',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'PD7',0,1
	.word	226
	.byte	3,1,2,35,3,4
	.byte	'PL7',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,205,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6518
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_PDR1_Bits',0,12,226,3,16,4,4
	.byte	'PD8',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PL8',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PD9',0,1
	.word	226
	.byte	3,1,2,35,0,4
	.byte	'PL9',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PD10',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'PL10',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'PD11',0,1
	.word	226
	.byte	3,1,2,35,1,4
	.byte	'PL11',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'PD12',0,1
	.word	226
	.byte	3,5,2,35,2,4
	.byte	'PL12',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'PD13',0,1
	.word	226
	.byte	3,1,2,35,2,4
	.byte	'PL13',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'PD14',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'PL14',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'PD15',0,1
	.word	226
	.byte	3,1,2,35,3,4
	.byte	'PL15',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,213,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	6822
	.byte	4,2,35,0,0,23,8
	.word	226
	.byte	24,7,0,15
	.byte	'_Ifx_P_ESR_Bits',0,12,88,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,140,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7147
	.byte	4,2,35,0,0,23,12
	.word	226
	.byte	24,11,0,15
	.byte	'_Ifx_P_PDISC_Bits',0,12,183,3,16,4,4
	.byte	'PDIS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PDIS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PDIS2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PDIS3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PDIS4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PDIS5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PDIS6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PDIS7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PDIS8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PDIS9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'PDIS10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'PDIS11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'PDIS12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'PDIS13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PDIS14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'PDIS15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,197,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7487
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_PCSR_Bits',0,12,165,3,16,4,4
	.byte	'SEL0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SEL1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'SEL2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SEL3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'SEL4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'SEL5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'SEL6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'SEL7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'SEL10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'SEL11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,4
	.word	2618
	.byte	19,1,2,35,0,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,189,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	7853
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR0_Bits',0,12,206,2,16,4,4
	.byte	'PS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,5,12,149,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8139
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR4_Bits',0,12,227,2,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'PS4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,5,12,165,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8286
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR8_Bits',0,12,238,2,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'PS8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,4
	.word	2618
	.byte	20,0,2,35,0,0,5,12,173,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8455
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR12_Bits',0,12,216,2,16,4,4
	.byte	'reserved_0',0,2
	.word	887
	.byte	12,4,2,35,0,4
	.byte	'PS12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,157,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8627
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR0_Bits',0,12,232,1,16,4,4
	.byte	'reserved_0',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'reserved_20',0,2
	.word	887
	.byte	12,0,2,35,2,0,5,12,229,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8802
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR4_Bits',0,12,253,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	20,12,2,35,0,4
	.byte	'PCL4',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	8,0,2,35,3,0,5,12,245,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	8976
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR8_Bits',0,12,136,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	24,8,2,35,0,4
	.byte	'PCL8',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,5,12,253,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9150
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR12_Bits',0,12,243,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	28,4,2,35,0,4
	.byte	'PCL12',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,237,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9326
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMSR_Bits',0,12,249,2,16,4,4
	.byte	'PS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PS2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PS3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PS4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PS5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PS6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PS7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PS8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PS9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'PS10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'PS11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'PS12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'PS13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PS14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'PS15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,141,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9482
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_OMCR_Bits',0,12,147,2,16,4,4
	.byte	'reserved_0',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'PCL2',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'PCL3',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'PCL4',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'PCL5',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'PCL6',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'PCL7',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'PCL8',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'PCL9',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'PCL10',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'PCL11',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'PCL12',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'PCL13',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'PCL14',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'PCL15',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,221,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	9815
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR0_Bits',0,12,192,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,5,12,196,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10163
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR1_Bits',0,12,200,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,15
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,12,208,1,16,4,4
	.byte	'RDIS_CTRL',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'RX_DIS',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'TERM',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'LRXTERM',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,5,12,204,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10287
	.byte	4,2,35,0,6
	.byte	'B_P21',0
	.word	10371
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_LPCR2_Bits',0,12,218,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'LVDSR',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'LVDSRL',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	2,4,2,35,1,4
	.byte	'TDIS_CTRL',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'TX_DIS',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'TX_PD',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'TX_PWDPD',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,12,213,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10551
	.byte	4,2,35,0,0,23,76
	.word	226
	.byte	24,75,0,15
	.byte	'_Ifx_P_ACCEN1_Bits',0,12,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,5,12,132,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10804
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P_ACCEN0_Bits',0,12,45,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,12,252,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	10891
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_P',0,12,229,5,25,128,2,6
	.byte	'OUT',0
	.word	4589
	.byte	4,2,35,0,6
	.byte	'OMR',0
	.word	5160
	.byte	4,2,35,4,6
	.byte	'ID',0
	.word	5279
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5319
	.byte	4,2,35,12,6
	.byte	'IOCR0',0
	.word	5503
	.byte	4,2,35,16,6
	.byte	'IOCR4',0
	.word	5718
	.byte	4,2,35,20,6
	.byte	'IOCR8',0
	.word	5935
	.byte	4,2,35,24,6
	.byte	'IOCR12',0
	.word	6155
	.byte	4,2,35,28,6
	.byte	'reserved_20',0
	.word	5319
	.byte	4,2,35,32,6
	.byte	'IN',0
	.word	6469
	.byte	4,2,35,36,6
	.byte	'reserved_28',0
	.word	6509
	.byte	24,2,35,40,6
	.byte	'PDR0',0
	.word	6782
	.byte	4,2,35,64,6
	.byte	'PDR1',0
	.word	7098
	.byte	4,2,35,68,6
	.byte	'reserved_48',0
	.word	7138
	.byte	8,2,35,72,6
	.byte	'ESR',0
	.word	7438
	.byte	4,2,35,80,6
	.byte	'reserved_54',0
	.word	7478
	.byte	12,2,35,84,6
	.byte	'PDISC',0
	.word	7813
	.byte	4,2,35,96,6
	.byte	'PCSR',0
	.word	8099
	.byte	4,2,35,100,6
	.byte	'reserved_68',0
	.word	7138
	.byte	8,2,35,104,6
	.byte	'OMSR0',0
	.word	8246
	.byte	4,2,35,112,6
	.byte	'OMSR4',0
	.word	8415
	.byte	4,2,35,116,6
	.byte	'OMSR8',0
	.word	8587
	.byte	4,2,35,120,6
	.byte	'OMSR12',0
	.word	8762
	.byte	4,2,35,124,6
	.byte	'OMCR0',0
	.word	8936
	.byte	4,3,35,128,1,6
	.byte	'OMCR4',0
	.word	9110
	.byte	4,3,35,132,1,6
	.byte	'OMCR8',0
	.word	9286
	.byte	4,3,35,136,1,6
	.byte	'OMCR12',0
	.word	9442
	.byte	4,3,35,140,1,6
	.byte	'OMSR',0
	.word	9775
	.byte	4,3,35,144,1,6
	.byte	'OMCR',0
	.word	10123
	.byte	4,3,35,148,1,6
	.byte	'reserved_98',0
	.word	7138
	.byte	8,3,35,152,1,6
	.byte	'LPCR0',0
	.word	10247
	.byte	4,3,35,160,1,6
	.byte	'LPCR1',0
	.word	10496
	.byte	4,3,35,164,1,6
	.byte	'LPCR2',0
	.word	10755
	.byte	4,3,35,168,1,6
	.byte	'reserved_A4',0
	.word	10795
	.byte	76,3,35,172,1,6
	.byte	'ACCEN1',0
	.word	10851
	.byte	4,3,35,248,1,6
	.byte	'ACCEN0',0
	.word	11418
	.byte	4,3,35,252,1,0,21
	.word	11458
	.byte	8
	.word	12061
	.byte	13,11,83,9,1,14
	.byte	'IfxPort_InputMode_undefined',0,127,14
	.byte	'IfxPort_InputMode_noPullDevice',0,0,14
	.byte	'IfxPort_InputMode_pullDown',0,8,14
	.byte	'IfxPort_InputMode_pullUp',0,16,0,20
	.byte	'IfxPort_setPinModeInput',0,3,11,196,4,17,1,1,17
	.byte	'port',0,11,196,4,48
	.word	12066
	.byte	17
	.byte	'pinIndex',0,11,196,4,60
	.word	226
	.byte	17
	.byte	'mode',0,11,196,4,88
	.word	12071
	.byte	18,0,13,11,134,1,9,1,14
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,14
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,13,11,120,9,1,14
	.byte	'IfxPort_OutputIdx_general',0,128,1,14
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,14
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,14
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,14
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,14
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,14
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,14
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,20
	.byte	'IfxPort_setPinModeOutput',0,3,11,202,4,17,1,1,17
	.byte	'port',0,11,202,4,49
	.word	12066
	.byte	17
	.byte	'pinIndex',0,11,202,4,61
	.word	226
	.byte	17
	.byte	'mode',0,11,202,4,90
	.word	12276
	.byte	17
	.byte	'index',0,11,202,4,114
	.word	12346
	.byte	18,0,13,11,172,1,9,4,14
	.byte	'IfxPort_State_notChanged',0,0,14
	.byte	'IfxPort_State_high',0,1,14
	.byte	'IfxPort_State_low',0,128,128,4,14
	.byte	'IfxPort_State_toggled',0,129,128,4,0,20
	.byte	'IfxPort_setPinState',0,3,11,208,4,17,1,1,17
	.byte	'port',0,11,208,4,44
	.word	12066
	.byte	17
	.byte	'pinIndex',0,11,208,4,56
	.word	226
	.byte	17
	.byte	'action',0,11,208,4,80
	.word	12659
	.byte	18,0,15
	.byte	'_Ifx_GPT12_CLC_Bits',0,14,95,16,4,4
	.byte	'DISR',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'DISS',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EDIS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,5,14,184,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	12840
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_PISEL_Bits',0,14,145,1,16,4,4
	.byte	'IST2IN',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'IST2EUD',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'IST3IN',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'IST3EUD',0,1
	.word	226
	.byte	2,2,2,35,0,4
	.byte	'IST4IN',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'IST4EUD',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'IST5IN',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'IST5EUD',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'IST6IN',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'IST6EUD',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'ISCAPIN',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,232,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	12998
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ID_Bits',0,14,105,16,4,4
	.byte	'MODREV',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,192,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13294
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T2CON_Bits',0,14,169,1,16,4,4
	.byte	'T2I',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'T2M',0,1
	.word	226
	.byte	3,2,2,35,0,4
	.byte	'T2R',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'T2UD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'T2UDE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'T2RC',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	2,4,2,35,1,4
	.byte	'T2IRDIS',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'T2EDGE',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'T2CHDIR',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'T2RDIR',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,248,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13417
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T3CON_Bits',0,14,193,1,16,4,4
	.byte	'T3I',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'T3M',0,1
	.word	226
	.byte	3,2,2,35,0,4
	.byte	'T3R',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'T3UD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'T3UDE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'T3OE',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'T3OTL',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'BPS1',0,1
	.word	226
	.byte	2,3,2,35,1,4
	.byte	'T3EDGE',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'T3CHDIR',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'T3RDIR',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,136,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13700
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T4CON_Bits',0,14,217,1,16,4,4
	.byte	'T4I',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'T4M',0,1
	.word	226
	.byte	3,2,2,35,0,4
	.byte	'T4R',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'T4UD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'T4UDE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'T4RC',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'CLRT2EN',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'CLRT3EN',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'T4IRDIS',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'T4EDGE',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'T4CHDIR',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'T4RDIR',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,152,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	13974
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T5CON_Bits',0,14,242,1,16,4,4
	.byte	'T5I',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'T5M',0,1
	.word	226
	.byte	3,2,2,35,0,4
	.byte	'T5R',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'T5UD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'T5UDE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'T5RC',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'CT3',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'CI',0,1
	.word	226
	.byte	2,2,2,35,1,4
	.byte	'T5CLR',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'T5SC',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,168,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14272
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T6CON_Bits',0,14,138,2,16,4,4
	.byte	'T6I',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'T6M',0,1
	.word	226
	.byte	3,2,2,35,0,4
	.byte	'T6R',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'T6UD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'T6UDE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'T6OE',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'T6OTL',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'BPS2',0,1
	.word	226
	.byte	2,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'T6CLR',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'T6SR',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,184,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14543
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_CAPREL_Bits',0,14,88,16,4,4
	.byte	'CAPREL',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,176,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14818
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T2_Bits',0,14,162,1,16,4,4
	.byte	'T2',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,240,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	14928
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T3_Bits',0,14,186,1,16,4,4
	.byte	'T3',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,128,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15031
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T4_Bits',0,14,210,1,16,4,4
	.byte	'T4',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,144,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15134
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T5_Bits',0,14,235,1,16,4,4
	.byte	'T5',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,160,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15237
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_T6_Bits',0,14,131,2,16,4,4
	.byte	'T6',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,5,14,176,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15340
	.byte	4,2,35,0,0,23,160,1
	.word	226
	.byte	24,159,1,0,15
	.byte	'_Ifx_GPT12_OCS_Bits',0,14,135,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	24,8,2,35,0,4
	.byte	'SUS',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'SUS_P',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'SUSSTA',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	2,0,2,35,3,0,5,14,224,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15454
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRSTCLR_Bits',0,14,128,1,16,4,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,5,14,216,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15616
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRST1_Bits',0,14,121,16,4,4
	.byte	'RST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,5,14,208,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15724
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_KRST0_Bits',0,14,113,16,4,4
	.byte	'RST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'RSTSTAT',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,5,14,200,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15829
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ACCEN1_Bits',0,14,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,5,14,168,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	15953
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12_ACCEN0_Bits',0,14,45,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	226
	.byte	1,0,2,35,3,0,5,14,160,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	16044
	.byte	4,2,35,0,0,15
	.byte	'_Ifx_GPT12',0,14,200,3,25,128,2,6
	.byte	'CLC',0
	.word	12958
	.byte	4,2,35,0,6
	.byte	'PISEL',0
	.word	13254
	.byte	4,2,35,4,6
	.byte	'ID',0
	.word	13377
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5319
	.byte	4,2,35,12,6
	.byte	'T2CON',0
	.word	13660
	.byte	4,2,35,16,6
	.byte	'T3CON',0
	.word	13934
	.byte	4,2,35,20,6
	.byte	'T4CON',0
	.word	14232
	.byte	4,2,35,24,6
	.byte	'T5CON',0
	.word	14503
	.byte	4,2,35,28,6
	.byte	'T6CON',0
	.word	14778
	.byte	4,2,35,32,6
	.byte	'reserved_24',0
	.word	7478
	.byte	12,2,35,36,6
	.byte	'CAPREL',0
	.word	14888
	.byte	4,2,35,48,6
	.byte	'T2',0
	.word	14991
	.byte	4,2,35,52,6
	.byte	'T3',0
	.word	15094
	.byte	4,2,35,56,6
	.byte	'T4',0
	.word	15197
	.byte	4,2,35,60,6
	.byte	'T5',0
	.word	15300
	.byte	4,2,35,64,6
	.byte	'T6',0
	.word	15403
	.byte	4,2,35,68,6
	.byte	'reserved_48',0
	.word	15443
	.byte	160,1,2,35,72,6
	.byte	'OCS',0
	.word	15576
	.byte	4,3,35,232,1,6
	.byte	'KRSTCLR',0
	.word	15684
	.byte	4,3,35,236,1,6
	.byte	'KRST1',0
	.word	15789
	.byte	4,3,35,240,1,6
	.byte	'KRST0',0
	.word	15913
	.byte	4,3,35,244,1,6
	.byte	'ACCEN1',0
	.word	16004
	.byte	4,3,35,248,1,6
	.byte	'ACCEN0',0
	.word	16575
	.byte	4,3,35,252,1,0,21
	.word	16615
	.byte	8
	.word	16986
	.byte	13,13,120,9,1,14
	.byte	'IfxGpt12_CounterInputMode_counterDisabled',0,0,14
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxIN',0,3,14
	.byte	'IfxGpt12_CounterInputMode_risingEdgeTxOTL',0,5,14
	.byte	'IfxGpt12_CounterInputMode_fallingEdgeTxOTL',0,6,14
	.byte	'IfxGpt12_CounterInputMode_bothEdgesTxOTL',0,7,0
.L226:
	.byte	20
	.byte	'IfxGpt12_T2_setCounterInputMode',0,3,13,206,8,17,1,1
.L229:
	.byte	17
	.byte	'gpt12',0,13,206,8,60
	.word	16991
.L231:
	.byte	17
	.byte	'inputMode',0,13,206,8,93
	.word	16996
.L233:
	.byte	18,0,13,13,245,1,9,1,14
	.byte	'IfxGpt12_TimerDirectionSource_internal',0,0,14
	.byte	'IfxGpt12_TimerDirectionSource_external',0,1,0
.L234:
	.byte	20
	.byte	'IfxGpt12_T2_setDirectionSource',0,3,13,212,8,17,1,1
.L237:
	.byte	17
	.byte	'gpt12',0,13,212,8,59
	.word	16991
.L239:
	.byte	17
	.byte	'source',0,13,212,8,96
	.word	17383
.L241:
	.byte	18,0,13,13,189,1,9,1,14
	.byte	'IfxGpt12_Mode_timer',0,0,14
	.byte	'IfxGpt12_Mode_counter',0,1,14
	.byte	'IfxGpt12_Mode_lowGatedTimer',0,2,14
	.byte	'IfxGpt12_Mode_highGatedTimer',0,3,14
	.byte	'IfxGpt12_Mode_reload',0,4,14
	.byte	'IfxGpt12_Mode_capture',0,5,14
	.byte	'IfxGpt12_Mode_incrementalInterfaceRotationDetection',0,6,14
	.byte	'IfxGpt12_Mode_incrementalInterfaceEdgeDetection',0,7,0
.L242:
	.byte	20
	.byte	'IfxGpt12_T2_setMode',0,3,13,244,8,17,1,1
.L245:
	.byte	17
	.byte	'gpt12',0,13,244,8,48
	.word	16991
.L247:
	.byte	17
	.byte	'mode',0,13,244,8,69
	.word	17544
.L249:
	.byte	18,0
.L258:
	.byte	20
	.byte	'IfxGpt12_T3_setCounterInputMode',0,3,13,183,9,17,1,1
.L261:
	.byte	17
	.byte	'gpt12',0,13,183,9,60
	.word	16991
.L263:
	.byte	17
	.byte	'inputMode',0,13,183,9,93
	.word	16996
.L265:
	.byte	18,0
.L266:
	.byte	20
	.byte	'IfxGpt12_T3_setDirectionSource',0,3,13,190,9,17,1,1
.L269:
	.byte	17
	.byte	'gpt12',0,13,190,9,59
	.word	16991
.L271:
	.byte	17
	.byte	'source',0,13,190,9,96
	.word	17383
.L273:
	.byte	18,0
.L274:
	.byte	20
	.byte	'IfxGpt12_T3_setMode',0,3,13,214,9,17,1,1
.L277:
	.byte	17
	.byte	'gpt12',0,13,214,9,48
	.word	16991
.L279:
	.byte	17
	.byte	'mode',0,13,214,9,69
	.word	17544
.L281:
	.byte	18,0
.L290:
	.byte	20
	.byte	'IfxGpt12_T4_setCounterInputMode',0,3,13,154,10,17,1,1
.L293:
	.byte	17
	.byte	'gpt12',0,13,154,10,60
	.word	16991
.L295:
	.byte	17
	.byte	'inputMode',0,13,154,10,93
	.word	16996
.L297:
	.byte	18,0
.L298:
	.byte	20
	.byte	'IfxGpt12_T4_setDirectionSource',0,3,13,160,10,17,1,1
.L301:
	.byte	17
	.byte	'gpt12',0,13,160,10,59
	.word	16991
.L303:
	.byte	17
	.byte	'source',0,13,160,10,96
	.word	17383
.L305:
	.byte	18,0
.L306:
	.byte	20
	.byte	'IfxGpt12_T4_setMode',0,3,13,190,10,17,1,1
.L309:
	.byte	17
	.byte	'gpt12',0,13,190,10,48
	.word	16991
.L311:
	.byte	17
	.byte	'mode',0,13,190,10,69
	.word	17544
.L313:
	.byte	18,0
.L322:
	.byte	20
	.byte	'IfxGpt12_T5_setCounterInputMode',0,3,13,147,11,17,1,1
.L325:
	.byte	17
	.byte	'gpt12',0,13,147,11,60
	.word	16991
.L327:
	.byte	17
	.byte	'inputMode',0,13,147,11,93
	.word	16996
.L329:
	.byte	18,0
.L330:
	.byte	20
	.byte	'IfxGpt12_T5_setDirectionSource',0,3,13,153,11,17,1,1
.L333:
	.byte	17
	.byte	'gpt12',0,13,153,11,59
	.word	16991
.L335:
	.byte	17
	.byte	'source',0,13,153,11,96
	.word	17383
.L337:
	.byte	18,0
.L338:
	.byte	20
	.byte	'IfxGpt12_T5_setMode',0,3,13,173,11,17,1,1
.L341:
	.byte	17
	.byte	'gpt12',0,13,173,11,48
	.word	16991
.L343:
	.byte	17
	.byte	'mode',0,13,173,11,69
	.word	17544
.L345:
	.byte	18,0
.L354:
	.byte	20
	.byte	'IfxGpt12_T6_setCounterInputMode',0,3,13,235,11,17,1,1
.L357:
	.byte	17
	.byte	'gpt12',0,13,235,11,60
	.word	16991
.L359:
	.byte	17
	.byte	'inputMode',0,13,235,11,93
	.word	16996
.L361:
	.byte	18,0
.L362:
	.byte	20
	.byte	'IfxGpt12_T6_setDirectionSource',0,3,13,242,11,17,1,1
.L365:
	.byte	17
	.byte	'gpt12',0,13,242,11,59
	.word	16991
.L367:
	.byte	17
	.byte	'source',0,13,242,11,96
	.word	17383
.L369:
	.byte	18,0
.L370:
	.byte	20
	.byte	'IfxGpt12_T6_setMode',0,3,13,134,12,17,1,1
.L373:
	.byte	17
	.byte	'gpt12',0,13,134,12,48
	.word	16991
.L375:
	.byte	17
	.byte	'mode',0,13,134,12,69
	.word	17544
.L377:
	.byte	18,0,13,13,79,9,1,14
	.byte	'IfxGpt12_CaptureInput_A',0,0,14
	.byte	'IfxGpt12_CaptureInput_B',0,1,14
	.byte	'IfxGpt12_CaptureInput_C',0,2,14
	.byte	'IfxGpt12_CaptureInput_D',0,3,0,20
	.byte	'IfxGpt12_setCaptureInput',0,3,13,213,12,17,1,1,17
	.byte	'gpt12',0,13,213,12,53
	.word	16991
	.byte	17
	.byte	'input',0,13,213,12,82
	.word	18696
	.byte	18,0,13,13,145,1,9,1,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_8',0,0,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_4',0,1,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_32',0,2,14
	.byte	'IfxGpt12_Gpt1BlockPrescaler_16',0,3,0
.L210:
	.byte	20
	.byte	'IfxGpt12_setGpt1BlockPrescaler',0,3,13,225,12,17,1,1
.L213:
	.byte	17
	.byte	'gpt12',0,13,225,12,59
	.word	16991
.L215:
	.byte	17
	.byte	'bps1',0,13,225,12,94
	.word	18871
.L217:
	.byte	18,0,13,13,156,1,9,1,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_4',0,0,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_2',0,1,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_16',0,2,14
	.byte	'IfxGpt12_Gpt2BlockPrescaler_8',0,3,0
.L218:
	.byte	20
	.byte	'IfxGpt12_setGpt2BlockPrescaler',0,3,13,231,12,17,1,1
.L221:
	.byte	17
	.byte	'gpt12',0,13,231,12,59
	.word	16991
.L223:
	.byte	17
	.byte	'bps2',0,13,231,12,94
	.word	19078
.L225:
	.byte	18,0
.L142:
	.byte	16
	.byte	'IfxGpt12_T2_getTimerValue',0,3,13,188,8,19
	.word	887
	.byte	1,1
.L143:
	.byte	17
	.byte	'gpt12',0,13,188,8,56
	.word	16991
.L145:
	.byte	18,0
.L146:
	.byte	16
	.byte	'IfxGpt12_T3_getTimerValue',0,3,13,171,9,19
	.word	887
	.byte	1,1
.L147:
	.byte	17
	.byte	'gpt12',0,13,171,9,56
	.word	16991
.L149:
	.byte	18,0
.L150:
	.byte	16
	.byte	'IfxGpt12_T4_getTimerValue',0,3,13,136,10,19
	.word	887
	.byte	1,1
.L151:
	.byte	17
	.byte	'gpt12',0,13,136,10,56
	.word	16991
.L153:
	.byte	18,0
.L154:
	.byte	16
	.byte	'IfxGpt12_T5_getTimerValue',0,3,13,245,10,19
	.word	887
	.byte	1,1
.L155:
	.byte	17
	.byte	'gpt12',0,13,245,10,56
	.word	16991
.L157:
	.byte	18,0
.L158:
	.byte	16
	.byte	'IfxGpt12_T6_getTimerValue',0,3,13,223,11,19
	.word	887
	.byte	1,1
.L159:
	.byte	17
	.byte	'gpt12',0,13,223,11,56
	.word	16991
.L161:
	.byte	18,0,13,13,159,2,9,1,14
	.byte	'IfxGpt12_TimerRun_stop',0,0,14
	.byte	'IfxGpt12_TimerRun_start',0,1,0
.L250:
	.byte	20
	.byte	'IfxGpt12_T2_run',0,3,13,194,8,17,1,1
.L253:
	.byte	17
	.byte	'gpt12',0,13,194,8,44
	.word	16991
.L255:
	.byte	17
	.byte	'runTimer',0,13,194,8,69
	.word	19559
.L257:
	.byte	18,0
.L164:
	.byte	20
	.byte	'IfxGpt12_T2_setTimerValue',0,3,13,146,9,17,1,1
.L167:
	.byte	17
	.byte	'gpt12',0,13,146,9,54
	.word	16991
.L169:
	.byte	17
	.byte	'value',0,13,146,9,68
	.word	887
.L171:
	.byte	18,0
.L282:
	.byte	20
	.byte	'IfxGpt12_T3_run',0,3,13,177,9,17,1,1
.L285:
	.byte	17
	.byte	'gpt12',0,13,177,9,44
	.word	16991
.L287:
	.byte	17
	.byte	'runTimer',0,13,177,9,69
	.word	19559
.L289:
	.byte	18,0
.L172:
	.byte	20
	.byte	'IfxGpt12_T3_setTimerValue',0,3,13,233,9,17,1,1
.L175:
	.byte	17
	.byte	'gpt12',0,13,233,9,54
	.word	16991
.L177:
	.byte	17
	.byte	'value',0,13,233,9,68
	.word	887
.L179:
	.byte	18,0
.L314:
	.byte	20
	.byte	'IfxGpt12_T4_run',0,3,13,142,10,17,1,1
.L317:
	.byte	17
	.byte	'gpt12',0,13,142,10,44
	.word	16991
.L319:
	.byte	17
	.byte	'runTimer',0,13,142,10,69
	.word	19559
.L321:
	.byte	18,0
.L180:
	.byte	20
	.byte	'IfxGpt12_T4_setTimerValue',0,3,13,220,10,17,1,1
.L183:
	.byte	17
	.byte	'gpt12',0,13,220,10,54
	.word	16991
.L185:
	.byte	17
	.byte	'value',0,13,220,10,68
	.word	887
.L187:
	.byte	18,0
.L346:
	.byte	20
	.byte	'IfxGpt12_T5_run',0,3,13,251,10,17,1,1
.L349:
	.byte	17
	.byte	'gpt12',0,13,251,10,44
	.word	16991
.L351:
	.byte	17
	.byte	'runTimer',0,13,251,10,69
	.word	19559
.L353:
	.byte	18,0
.L188:
	.byte	20
	.byte	'IfxGpt12_T5_setTimerValue',0,3,13,198,11,17,1,1
.L191:
	.byte	17
	.byte	'gpt12',0,13,198,11,54
	.word	16991
.L193:
	.byte	17
	.byte	'value',0,13,198,11,68
	.word	887
.L195:
	.byte	18,0
.L378:
	.byte	20
	.byte	'IfxGpt12_T6_run',0,3,13,229,11,17,1,1
.L381:
	.byte	17
	.byte	'gpt12',0,13,229,11,44
	.word	16991
.L383:
	.byte	17
	.byte	'runTimer',0,13,229,11,69
	.word	19559
.L385:
	.byte	18,0
.L196:
	.byte	20
	.byte	'IfxGpt12_T6_setTimerValue',0,3,13,159,12,17,1,1
.L199:
	.byte	17
	.byte	'gpt12',0,13,159,12,54
	.word	16991
.L201:
	.byte	17
	.byte	'value',0,13,159,12,68
	.word	887
.L203:
	.byte	18,0,25
	.word	2208
	.byte	26
	.word	2242
	.byte	18,0,25
	.word	2382
	.byte	26
	.word	2408
	.byte	18,0,25
	.word	2434
	.byte	26
	.word	2466
	.byte	18,0,25
	.word	2479
	.byte	18,0,25
	.word	2537
	.byte	26
	.word	2556
	.byte	18,0,25
	.word	2572
	.byte	26
	.word	2587
	.byte	26
	.word	2601
	.byte	18,0,25
	.word	2980
	.byte	26
	.word	3008
	.byte	18,0,25
	.word	3723
	.byte	26
	.word	3763
	.byte	26
	.word	3781
	.byte	18,0,25
	.word	3801
	.byte	26
	.word	3839
	.byte	26
	.word	3857
	.byte	18,0,25
	.word	3877
	.byte	26
	.word	3928
	.byte	18,0,25
	.word	4027
	.byte	18,0,25
	.word	4061
	.byte	18,0,25
	.word	4103
	.byte	26
	.word	4144
	.byte	18,0,25
	.word	4163
	.byte	26
	.word	4218
	.byte	18,0,25
	.word	4237
	.byte	26
	.word	4277
	.byte	26
	.word	4294
	.byte	22,18,0,0,25
	.word	12196
	.byte	26
	.word	12228
	.byte	26
	.word	12242
	.byte	26
	.word	12260
	.byte	18,0,25
	.word	12563
	.byte	26
	.word	12596
	.byte	26
	.word	12610
	.byte	26
	.word	12628
	.byte	26
	.word	12642
	.byte	18,0,25
	.word	12762
	.byte	26
	.word	12790
	.byte	26
	.word	12804
	.byte	26
	.word	12822
	.byte	18,0,25
	.word	17307
	.byte	26
	.word	17347
	.byte	26
	.word	17362
	.byte	18,0,25
	.word	17472
	.byte	26
	.word	17511
	.byte	26
	.word	17526
	.byte	18,0,25
	.word	17809
	.byte	26
	.word	17837
	.byte	26
	.word	17852
	.byte	18,0,25
	.word	17868
	.byte	26
	.word	17908
	.byte	26
	.word	17923
	.byte	18,0,25
	.word	17944
	.byte	26
	.word	17983
	.byte	26
	.word	17998
	.byte	18,0,25
	.word	18016
	.byte	26
	.word	18044
	.byte	26
	.word	18059
	.byte	18,0,25
	.word	18075
	.byte	26
	.word	18115
	.byte	26
	.word	18130
	.byte	18,0,25
	.word	18151
	.byte	26
	.word	18190
	.byte	26
	.word	18205
	.byte	18,0,25
	.word	18223
	.byte	26
	.word	18251
	.byte	26
	.word	18266
	.byte	18,0,25
	.word	18282
	.byte	26
	.word	18322
	.byte	26
	.word	18337
	.byte	18,0,25
	.word	18358
	.byte	26
	.word	18397
	.byte	26
	.word	18412
	.byte	18,0,25
	.word	18430
	.byte	26
	.word	18458
	.byte	26
	.word	18473
	.byte	18,0,25
	.word	18489
	.byte	26
	.word	18529
	.byte	26
	.word	18544
	.byte	18,0,25
	.word	18565
	.byte	26
	.word	18604
	.byte	26
	.word	18619
	.byte	18,0,25
	.word	18637
	.byte	26
	.word	18665
	.byte	26
	.word	18680
	.byte	18,0,25
	.word	18806
	.byte	26
	.word	18839
	.byte	26
	.word	18854
	.byte	18,0,25
	.word	19008
	.byte	26
	.word	19047
	.byte	26
	.word	19062
	.byte	18,0,25
	.word	19214
	.byte	26
	.word	19253
	.byte	26
	.word	19268
	.byte	18,0,27
	.byte	'IfxGpt12_enableModule',0,13,149,6,17,1,1,1,1,17
	.byte	'gpt12',0,13,149,6,50
	.word	16991
	.byte	0,25
	.word	19284
	.byte	26
	.word	19322
	.byte	18,0,25
	.word	19339
	.byte	26
	.word	19377
	.byte	18,0,25
	.word	19394
	.byte	26
	.word	19432
	.byte	18,0,25
	.word	19449
	.byte	26
	.word	19487
	.byte	18,0,25
	.word	19504
	.byte	26
	.word	19542
	.byte	18,0,25
	.word	19617
	.byte	26
	.word	19641
	.byte	26
	.word	19656
	.byte	18,0,25
	.word	19676
	.byte	26
	.word	19710
	.byte	26
	.word	19725
	.byte	18,0,25
	.word	19742
	.byte	26
	.word	19766
	.byte	26
	.word	19781
	.byte	18,0,25
	.word	19801
	.byte	26
	.word	19835
	.byte	26
	.word	19850
	.byte	18,0,25
	.word	19867
	.byte	26
	.word	19891
	.byte	26
	.word	19906
	.byte	18,0,25
	.word	19926
	.byte	26
	.word	19960
	.byte	26
	.word	19975
	.byte	18,0,25
	.word	19992
	.byte	26
	.word	20016
	.byte	26
	.word	20031
	.byte	18,0,25
	.word	20051
	.byte	26
	.word	20085
	.byte	26
	.word	20100
	.byte	18,0,25
	.word	20117
	.byte	26
	.word	20141
	.byte	26
	.word	20156
	.byte	18,0,25
	.word	20176
	.byte	26
	.word	20210
	.byte	26
	.word	20225
	.byte	18,0,3,11,190,1,9,8,6
	.byte	'port',0
	.word	12066
	.byte	4,2,35,0,6
	.byte	'pinIndex',0
	.word	226
	.byte	1,2,35,4,0,13,16,130,1,9,1,14
	.byte	'Ifx_RxSel_a',0,0,14
	.byte	'Ifx_RxSel_b',0,1,14
	.byte	'Ifx_RxSel_c',0,2,14
	.byte	'Ifx_RxSel_d',0,3,14
	.byte	'Ifx_RxSel_e',0,4,14
	.byte	'Ifx_RxSel_f',0,5,14
	.byte	'Ifx_RxSel_g',0,6,14
	.byte	'Ifx_RxSel_h',0,7,0,3,15,67,15,20,6
	.byte	'module',0
	.word	16991
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	226
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	21083
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	21122
	.byte	1,2,35,16,0,28
	.word	21241
.L484:
	.byte	8
	.word	21307
	.byte	13,11,144,1,9,1,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,14
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,14
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,14
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,14
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,14
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,14
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,14
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,14
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,14
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxGpt12_initTxEudInPinWithPadLevel',0,13,161,8,17,1,1,1,1,17
	.byte	'txEudIn',0,13,161,8,78
	.word	21312
	.byte	17
	.byte	'inputMode',0,13,161,8,105
	.word	12071
	.byte	17
	.byte	'padDriver',0,13,161,8,134,1
	.word	21317
	.byte	0,3,15,76,15,20,6
	.byte	'module',0
	.word	16991
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	226
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	21083
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	21122
	.byte	1,2,35,16,0,28
	.word	21834
.L482:
	.byte	8
	.word	21900
	.byte	27
	.byte	'IfxGpt12_initTxInPinWithPadLevel',0,13,169,8,17,1,1,1,1,17
	.byte	'txIn',0,13,169,8,74
	.word	21905
	.byte	17
	.byte	'inputMode',0,13,169,8,98
	.word	12071
	.byte	17
	.byte	'padDriver',0,13,169,8,127
	.word	21317
	.byte	0,2
	.byte	'char',0,1,6,8
	.word	22005
	.byte	27
	.byte	'debug_assert_handler',0,17,112,9,1,1,1,1,17
	.byte	'pass',0,17,112,47
	.word	226
	.byte	17
	.byte	'file',0,17,112,59
	.word	22013
	.byte	17
	.byte	'line',0,17,112,69
	.word	2634
	.byte	0
.L137:
	.byte	2
	.byte	'short int',0,2,5
.L139:
	.byte	13,18,75,9,1,14
	.byte	'TIM2_ENCODER',0,0,14
	.byte	'TIM3_ENCODER',0,1,14
	.byte	'TIM4_ENCODER',0,2,14
	.byte	'TIM5_ENCODER',0,3,14
	.byte	'TIM6_ENCODER',0,4,0
.L206:
	.byte	13,18,42,9,1,14
	.byte	'TIM2_ENCODER_CH1_P00_7',0,0,14
	.byte	'TIM2_ENCODER_CH1_P33_7',0,1,14
	.byte	'TIM3_ENCODER_CH1_P02_6',0,2,14
	.byte	'TIM4_ENCODER_CH1_P02_8',0,3,14
	.byte	'TIM5_ENCODER_CH1_P21_7',0,4,14
	.byte	'TIM5_ENCODER_CH1_P10_3',0,5,14
	.byte	'TIM6_ENCODER_CH1_P20_3',0,6,14
	.byte	'TIM6_ENCODER_CH1_P10_2',0,7,0
.L208:
	.byte	13,18,59,9,1,14
	.byte	'TIM2_ENCODER_CH2_P00_8',0,0,14
	.byte	'TIM2_ENCODER_CH2_P33_6',0,1,14
	.byte	'TIM3_ENCODER_CH2_P02_7',0,2,14
	.byte	'TIM4_ENCODER_CH2_P00_9',0,3,14
	.byte	'TIM4_ENCODER_CH2_P33_5',0,4,14
	.byte	'TIM5_ENCODER_CH2_P21_6',0,5,14
	.byte	'TIM5_ENCODER_CH2_P10_1',0,6,14
	.byte	'TIM6_ENCODER_CH2_P20_0',0,7,0,9
	.byte	'__wchar_t',0,19,1,1
	.word	22087
	.byte	9
	.byte	'__size_t',0,19,1,1
	.word	2618
	.byte	9
	.byte	'__ptrdiff_t',0,19,1,1
	.word	2634
	.byte	29,1,8
	.word	22648
	.byte	9
	.byte	'__codeptr',0,19,1,1
	.word	22650
	.byte	9
	.byte	'__intptr_t',0,19,1,1
	.word	2634
	.byte	9
	.byte	'__uintptr_t',0,19,1,1
	.word	2618
	.byte	9
	.byte	'boolean',0,20,101,29
	.word	226
	.byte	9
	.byte	'uint8',0,20,105,29
	.word	226
	.byte	9
	.byte	'uint16',0,20,109,29
	.word	887
	.byte	9
	.byte	'uint32',0,20,113,29
	.word	205
	.byte	9
	.byte	'uint64',0,20,118,29
	.word	2511
	.byte	9
	.byte	'sint16',0,20,126,29
	.word	22087
	.byte	9
	.byte	'sint32',0,20,131,1,29
	.word	564
	.byte	2
	.byte	'long long int',0,8,5,9
	.byte	'sint64',0,20,138,1,29
	.word	22818
	.byte	9
	.byte	'float32',0,20,167,1,29
	.word	495
	.byte	9
	.byte	'pvoid',0,16,57,28
	.word	411
	.byte	9
	.byte	'Ifx_TickTime',0,16,79,28
	.word	22818
	.byte	9
	.byte	'Ifx_SizeT',0,16,92,16
	.word	22087
	.byte	9
	.byte	'Ifx_Priority',0,16,103,16
	.word	887
	.byte	9
	.byte	'Ifx_RxSel',0,16,140,1,3
	.word	21122
	.byte	8
	.word	22087
	.byte	12
	.word	226
	.byte	1,1,11
	.word	411
	.byte	11
	.word	411
	.byte	11
	.word	22961
	.byte	11
	.word	22818
	.byte	0,8
	.word	22966
	.byte	9
	.byte	'IfxStdIf_DPipe_Write',0,21,92,19
	.word	22994
	.byte	9
	.byte	'IfxStdIf_DPipe_Read',0,21,107,19
	.word	22994
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadCount',0,21,115,18
	.word	589
	.byte	21
	.word	226
	.byte	8
	.word	23092
	.byte	12
	.word	23097
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	23102
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,21,123,36
	.word	23115
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,21,147,1,18
	.word	589
	.byte	8
	.word	23102
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,21,155,1,37
	.word	23194
	.byte	12
	.word	226
	.byte	1,1,11
	.word	411
	.byte	11
	.word	22087
	.byte	11
	.word	22818
	.byte	0,8
	.word	23237
	.byte	9
	.byte	'IfxStdIf_DPipe_CanReadCount',0,21,166,1,19
	.word	23260
	.byte	9
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,21,177,1,19
	.word	23260
	.byte	12
	.word	226
	.byte	1,1,11
	.word	411
	.byte	11
	.word	22818
	.byte	0,8
	.word	23340
	.byte	9
	.byte	'IfxStdIf_DPipe_FlushTx',0,21,186,1,19
	.word	23358
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearTx',0,21,200,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearRx',0,21,193,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_DPipe_OnReceive',0,21,208,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_DPipe_OnTransmit',0,21,215,1,16
	.word	458
	.byte	9
	.byte	'IfxStdIf_DPipe_OnError',0,21,222,1,16
	.word	458
	.byte	12
	.word	205
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	23560
	.byte	9
	.byte	'IfxStdIf_DPipe_GetSendCount',0,21,131,1,18
	.word	23573
	.byte	12
	.word	22818
	.byte	1,1,11
	.word	411
	.byte	0,8
	.word	23615
	.byte	9
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,21,139,1,24
	.word	23628
	.byte	9
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,21,229,1,16
	.word	458
	.byte	15
	.byte	'IfxStdIf_DPipe_',0,21,233,1,8,76,6
	.byte	'driver',0
	.word	416
	.byte	4,2,35,0,6
	.byte	'txDisabled',0
	.word	226
	.byte	1,2,35,4,6
	.byte	'write',0
	.word	22999
	.byte	4,2,35,8,6
	.byte	'read',0
	.word	23028
	.byte	4,2,35,12,6
	.byte	'getReadCount',0
	.word	23056
	.byte	4,2,35,16,6
	.byte	'getReadEvent',0
	.word	23120
	.byte	4,2,35,20,6
	.byte	'getWriteCount',0
	.word	23156
	.byte	4,2,35,24,6
	.byte	'getWriteEvent',0
	.word	23199
	.byte	4,2,35,28,6
	.byte	'canReadCount',0
	.word	23265
	.byte	4,2,35,32,6
	.byte	'canWriteCount',0
	.word	23302
	.byte	4,2,35,36,6
	.byte	'flushTx',0
	.word	23363
	.byte	4,2,35,40,6
	.byte	'clearTx',0
	.word	23395
	.byte	4,2,35,44,6
	.byte	'clearRx',0
	.word	23427
	.byte	4,2,35,48,6
	.byte	'onReceive',0
	.word	23459
	.byte	4,2,35,52,6
	.byte	'onTransmit',0
	.word	23493
	.byte	4,2,35,56,6
	.byte	'onError',0
	.word	23528
	.byte	4,2,35,60,6
	.byte	'getSendCount',0
	.word	23578
	.byte	4,2,35,64,6
	.byte	'getTxTimeStamp',0
	.word	23633
	.byte	4,2,35,68,6
	.byte	'resetSendCount',0
	.word	23672
	.byte	4,2,35,72,0,9
	.byte	'IfxStdIf_DPipe',0,21,71,32
	.word	23711
	.byte	8
	.word	22966
	.byte	8
	.word	22966
	.byte	8
	.word	576
	.byte	8
	.word	23102
	.byte	8
	.word	576
	.byte	8
	.word	23102
	.byte	8
	.word	23237
	.byte	8
	.word	23237
	.byte	8
	.word	23340
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	449
	.byte	8
	.word	23560
	.byte	8
	.word	23615
	.byte	8
	.word	449
	.byte	21
	.word	226
	.byte	8
	.word	24219
	.byte	9
	.byte	'IfxStdIf_DPipe_WriteEvent',0,21,73,32
	.word	24224
	.byte	9
	.byte	'IfxStdIf_DPipe_ReadEvent',0,21,74,32
	.word	24224
	.byte	13,1,76,9,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_oneFold',0,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_twoFold',0,2,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_fourFold',0,4,0,9
	.byte	'IfxStdIf_Pos_ResolutionFactor',0,1,81,3
	.word	24296
	.byte	9
	.byte	'IfxStdIf_Pos_SensorType',0,1,92,3
	.word	1045
	.byte	9
	.byte	'IfxStdIf_Pos_Dir',0,1,100,3
	.word	660
	.byte	9
	.byte	'IfxStdIf_Pos_Status',0,1,114,3
	.word	372
	.byte	9
	.byte	'IfxStdIf_Pos',0,1,119,30
	.word	1699
	.byte	3,1,186,2,9,36,6
	.byte	'offset',0
	.word	564
	.byte	4,2,35,0,6
	.byte	'reversed',0
	.word	226
	.byte	1,2,35,4,6
	.byte	'resolution',0
	.word	564
	.byte	4,2,35,6,6
	.byte	'periodPerRotation',0
	.word	887
	.byte	2,2,35,10,6
	.byte	'resolutionFactor',0
	.word	24296
	.byte	1,2,35,12,6
	.byte	'updatePeriod',0
	.word	495
	.byte	4,2,35,14,6
	.byte	'speedModeThreshold',0
	.word	495
	.byte	4,2,35,18,6
	.byte	'minSpeed',0
	.word	495
	.byte	4,2,35,22,6
	.byte	'maxSpeed',0
	.word	495
	.byte	4,2,35,26,6
	.byte	'speedFilterEnabled',0
	.word	226
	.byte	1,2,35,30,6
	.byte	'speedFilerCutOffFrequency',0
	.word	495
	.byte	4,2,35,32,0,9
	.byte	'IfxStdIf_Pos_Config',0,1,199,2,3
	.word	24567
	.byte	3,22,64,9,12,6
	.byte	'a',0
	.word	495
	.byte	4,2,35,0,6
	.byte	'b',0
	.word	495
	.byte	4,2,35,4,6
	.byte	'out',0
	.word	495
	.byte	4,2,35,8,0,9
	.byte	'Ifx_LowPassPt1F32',0,22,69,3
	.word	24859
	.byte	13,23,69,9,1,14
	.byte	'IfxSrc_Tos_cpu0',0,0,14
	.byte	'IfxSrc_Tos_cpu1',0,1,14
	.byte	'IfxSrc_Tos_dma',0,3,0,9
	.byte	'IfxSrc_Tos',0,23,74,3
	.word	24926
	.byte	9
	.byte	'Ifx_SRC_SRCR_Bits',0,6,62,3
	.word	2641
	.byte	9
	.byte	'Ifx_SRC_SRCR',0,6,75,3
	.word	2931
	.byte	15
	.byte	'_Ifx_SRC_AGBT',0,6,86,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	25051
	.byte	9
	.byte	'Ifx_SRC_AGBT',0,6,89,3
	.word	25083
	.byte	15
	.byte	'_Ifx_SRC_ASCLIN',0,6,92,25,12,6
	.byte	'TX',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'RX',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,8,0,21
	.word	25109
	.byte	9
	.byte	'Ifx_SRC_ASCLIN',0,6,97,3
	.word	25168
	.byte	15
	.byte	'_Ifx_SRC_BCUSPB',0,6,100,25,4,6
	.byte	'SBSRC',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	25196
	.byte	9
	.byte	'Ifx_SRC_BCUSPB',0,6,103,3
	.word	25233
	.byte	23,64
	.word	2931
	.byte	24,15,0,15
	.byte	'_Ifx_SRC_CAN',0,6,106,25,64,6
	.byte	'INT',0
	.word	25261
	.byte	64,2,35,0,0,21
	.word	25270
	.byte	9
	.byte	'Ifx_SRC_CAN',0,6,109,3
	.word	25302
	.byte	15
	.byte	'_Ifx_SRC_CCU6',0,6,112,25,16,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2931
	.byte	4,2,35,12,0,21
	.word	25327
	.byte	9
	.byte	'Ifx_SRC_CCU6',0,6,118,3
	.word	25399
	.byte	23,8
	.word	2931
	.byte	24,1,0,15
	.byte	'_Ifx_SRC_CERBERUS',0,6,121,25,8,6
	.byte	'SR',0
	.word	25425
	.byte	8,2,35,0,0,21
	.word	25434
	.byte	9
	.byte	'Ifx_SRC_CERBERUS',0,6,124,3
	.word	25470
	.byte	15
	.byte	'_Ifx_SRC_CIF',0,6,127,25,16,6
	.byte	'MI',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'MIEP',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'ISP',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'MJPEG',0
	.word	2931
	.byte	4,2,35,12,0,21
	.word	25500
	.byte	9
	.byte	'Ifx_SRC_CIF',0,6,133,1,3
	.word	25573
	.byte	15
	.byte	'_Ifx_SRC_CPU',0,6,136,1,25,4,6
	.byte	'SBSRC',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	25599
	.byte	9
	.byte	'Ifx_SRC_CPU',0,6,139,1,3
	.word	25634
	.byte	23,192,1
	.word	2931
	.byte	24,47,0,15
	.byte	'_Ifx_SRC_DMA',0,6,142,1,25,208,1,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'reserved_4',0
	.word	7478
	.byte	12,2,35,4,6
	.byte	'CH',0
	.word	25660
	.byte	192,1,2,35,16,0,21
	.word	25670
	.byte	9
	.byte	'Ifx_SRC_DMA',0,6,147,1,3
	.word	25737
	.byte	15
	.byte	'_Ifx_SRC_DSADC',0,6,150,1,25,8,6
	.byte	'SRM',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SRA',0
	.word	2931
	.byte	4,2,35,4,0,21
	.word	25763
	.byte	9
	.byte	'Ifx_SRC_DSADC',0,6,154,1,3
	.word	25811
	.byte	15
	.byte	'_Ifx_SRC_EMEM',0,6,157,1,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	25839
	.byte	9
	.byte	'Ifx_SRC_EMEM',0,6,160,1,3
	.word	25872
	.byte	23,40
	.word	226
	.byte	24,39,0,15
	.byte	'_Ifx_SRC_ERAY',0,6,163,1,25,80,6
	.byte	'INT',0
	.word	25425
	.byte	8,2,35,0,6
	.byte	'TINT',0
	.word	25425
	.byte	8,2,35,8,6
	.byte	'NDAT',0
	.word	25425
	.byte	8,2,35,16,6
	.byte	'MBSC',0
	.word	25425
	.byte	8,2,35,24,6
	.byte	'OBUSY',0
	.word	2931
	.byte	4,2,35,32,6
	.byte	'IBUSY',0
	.word	2931
	.byte	4,2,35,36,6
	.byte	'reserved_28',0
	.word	25899
	.byte	40,2,35,40,0,21
	.word	25908
	.byte	9
	.byte	'Ifx_SRC_ERAY',0,6,172,1,3
	.word	26035
	.byte	15
	.byte	'_Ifx_SRC_ETH',0,6,175,1,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	26062
	.byte	9
	.byte	'Ifx_SRC_ETH',0,6,178,1,3
	.word	26094
	.byte	15
	.byte	'_Ifx_SRC_FCE',0,6,181,1,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	26120
	.byte	9
	.byte	'Ifx_SRC_FCE',0,6,184,1,3
	.word	26152
	.byte	15
	.byte	'_Ifx_SRC_FFT',0,6,187,1,25,12,6
	.byte	'DONE',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'RFS',0
	.word	2931
	.byte	4,2,35,8,0,21
	.word	26178
	.byte	9
	.byte	'Ifx_SRC_FFT',0,6,192,1,3
	.word	26238
	.byte	23,16
	.word	226
	.byte	24,15,0,15
	.byte	'_Ifx_SRC_GPSR',0,6,195,1,25,32,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2931
	.byte	4,2,35,12,6
	.byte	'reserved_10',0
	.word	26264
	.byte	16,2,35,16,0,21
	.word	26273
	.byte	9
	.byte	'Ifx_SRC_GPSR',0,6,202,1,3
	.word	26367
	.byte	15
	.byte	'_Ifx_SRC_GPT12',0,6,205,1,25,48,6
	.byte	'CIRQ',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'T2',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'T3',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'T4',0
	.word	2931
	.byte	4,2,35,12,6
	.byte	'T5',0
	.word	2931
	.byte	4,2,35,16,6
	.byte	'T6',0
	.word	2931
	.byte	4,2,35,20,6
	.byte	'reserved_18',0
	.word	6509
	.byte	24,2,35,24,0,21
	.word	26394
	.byte	9
	.byte	'Ifx_SRC_GPT12',0,6,214,1,3
	.word	26511
	.byte	23,12
	.word	2931
	.byte	24,2,0,23,32
	.word	2931
	.byte	24,7,0,23,32
	.word	26548
	.byte	24,0,0,23,88
	.word	226
	.byte	24,87,0,23,108
	.word	2931
	.byte	24,26,0,23,96
	.word	226
	.byte	24,95,0,23,96
	.word	26548
	.byte	24,2,0,23,160,3
	.word	226
	.byte	24,159,3,0,23,64
	.word	26548
	.byte	24,1,0,23,192,3
	.word	226
	.byte	24,191,3,0,23,16
	.word	2931
	.byte	24,3,0,23,64
	.word	26633
	.byte	24,3,0,23,192,2
	.word	226
	.byte	24,191,2,0,23,52
	.word	226
	.byte	24,51,0,15
	.byte	'_Ifx_SRC_GTM',0,6,217,1,25,204,18,6
	.byte	'AEIIRQ',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'ARUIRQ',0
	.word	26539
	.byte	12,2,35,4,6
	.byte	'reserved_10',0
	.word	5319
	.byte	4,2,35,16,6
	.byte	'BRCIRQ',0
	.word	2931
	.byte	4,2,35,20,6
	.byte	'CMPIRQ',0
	.word	2931
	.byte	4,2,35,24,6
	.byte	'SPEIRQ',0
	.word	25425
	.byte	8,2,35,28,6
	.byte	'reserved_24',0
	.word	7138
	.byte	8,2,35,36,6
	.byte	'PSM',0
	.word	26557
	.byte	32,2,35,44,6
	.byte	'reserved_4C',0
	.word	26566
	.byte	88,2,35,76,6
	.byte	'DPLL',0
	.word	26575
	.byte	108,3,35,164,1,6
	.byte	'reserved_110',0
	.word	26584
	.byte	96,3,35,144,2,6
	.byte	'ERR',0
	.word	2931
	.byte	4,3,35,240,2,6
	.byte	'reserved_174',0
	.word	7478
	.byte	12,3,35,244,2,6
	.byte	'TIM',0
	.word	26593
	.byte	96,3,35,128,3,6
	.byte	'reserved_1E0',0
	.word	26602
	.byte	160,3,3,35,224,3,6
	.byte	'MCS',0
	.word	26593
	.byte	96,3,35,128,7,6
	.byte	'reserved_3E0',0
	.word	26602
	.byte	160,3,3,35,224,7,6
	.byte	'TOM',0
	.word	26613
	.byte	64,3,35,128,11,6
	.byte	'reserved_5C0',0
	.word	26622
	.byte	192,3,3,35,192,11,6
	.byte	'ATOM',0
	.word	26642
	.byte	64,3,35,128,15,6
	.byte	'reserved_7C0',0
	.word	26651
	.byte	192,2,3,35,192,15,6
	.byte	'MCSW0',0
	.word	26539
	.byte	12,3,35,128,18,6
	.byte	'reserved_90C',0
	.word	26662
	.byte	52,3,35,140,18,6
	.byte	'MCSW1',0
	.word	26539
	.byte	12,3,35,192,18,0,21
	.word	26671
	.byte	9
	.byte	'Ifx_SRC_GTM',0,6,243,1,3
	.word	27131
	.byte	15
	.byte	'_Ifx_SRC_HSCT',0,6,246,1,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	27157
	.byte	9
	.byte	'Ifx_SRC_HSCT',0,6,249,1,3
	.word	27190
	.byte	15
	.byte	'_Ifx_SRC_HSSL',0,6,252,1,25,16,6
	.byte	'COK',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'RDI',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'TRG',0
	.word	2931
	.byte	4,2,35,12,0,21
	.word	27217
	.byte	9
	.byte	'Ifx_SRC_HSSL',0,6,130,2,3
	.word	27290
	.byte	23,56
	.word	226
	.byte	24,55,0,15
	.byte	'_Ifx_SRC_I2C',0,6,133,2,25,80,6
	.byte	'BREQ',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'LBREQ',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SREQ',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'LSREQ',0
	.word	2931
	.byte	4,2,35,12,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,16,6
	.byte	'P',0
	.word	2931
	.byte	4,2,35,20,6
	.byte	'reserved_18',0
	.word	27317
	.byte	56,2,35,24,0,21
	.word	27326
	.byte	9
	.byte	'Ifx_SRC_I2C',0,6,142,2,3
	.word	27449
	.byte	15
	.byte	'_Ifx_SRC_LMU',0,6,145,2,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	27475
	.byte	9
	.byte	'Ifx_SRC_LMU',0,6,148,2,3
	.word	27507
	.byte	15
	.byte	'_Ifx_SRC_MSC',0,6,151,2,25,20,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2931
	.byte	4,2,35,12,6
	.byte	'SR4',0
	.word	2931
	.byte	4,2,35,16,0,21
	.word	27533
	.byte	9
	.byte	'Ifx_SRC_MSC',0,6,158,2,3
	.word	27618
	.byte	15
	.byte	'_Ifx_SRC_PMU',0,6,161,2,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	27644
	.byte	9
	.byte	'Ifx_SRC_PMU',0,6,164,2,3
	.word	27676
	.byte	15
	.byte	'_Ifx_SRC_PSI5',0,6,167,2,25,32,6
	.byte	'SR',0
	.word	26548
	.byte	32,2,35,0,0,21
	.word	27702
	.byte	9
	.byte	'Ifx_SRC_PSI5',0,6,170,2,3
	.word	27735
	.byte	15
	.byte	'_Ifx_SRC_PSI5S',0,6,173,2,25,32,6
	.byte	'SR',0
	.word	26548
	.byte	32,2,35,0,0,21
	.word	27762
	.byte	9
	.byte	'Ifx_SRC_PSI5S',0,6,176,2,3
	.word	27796
	.byte	15
	.byte	'_Ifx_SRC_QSPI',0,6,179,2,25,24,6
	.byte	'TX',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'RX',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'ERR',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'PT',0
	.word	2931
	.byte	4,2,35,12,6
	.byte	'HC',0
	.word	2931
	.byte	4,2,35,16,6
	.byte	'U',0
	.word	2931
	.byte	4,2,35,20,0,21
	.word	27824
	.byte	9
	.byte	'Ifx_SRC_QSPI',0,6,187,2,3
	.word	27917
	.byte	15
	.byte	'_Ifx_SRC_SCR',0,6,190,2,25,4,6
	.byte	'SR',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	27944
	.byte	9
	.byte	'Ifx_SRC_SCR',0,6,193,2,3
	.word	27976
	.byte	15
	.byte	'_Ifx_SRC_SCU',0,6,196,2,25,20,6
	.byte	'DTS',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'ERU',0
	.word	26633
	.byte	16,2,35,4,0,21
	.word	28002
	.byte	9
	.byte	'Ifx_SRC_SCU',0,6,200,2,3
	.word	28048
	.byte	23,24
	.word	2931
	.byte	24,5,0,15
	.byte	'_Ifx_SRC_SENT',0,6,203,2,25,24,6
	.byte	'SR',0
	.word	28074
	.byte	24,2,35,0,0,21
	.word	28083
	.byte	9
	.byte	'Ifx_SRC_SENT',0,6,206,2,3
	.word	28116
	.byte	15
	.byte	'_Ifx_SRC_SMU',0,6,209,2,25,12,6
	.byte	'SR',0
	.word	26539
	.byte	12,2,35,0,0,21
	.word	28143
	.byte	9
	.byte	'Ifx_SRC_SMU',0,6,212,2,3
	.word	28175
	.byte	15
	.byte	'_Ifx_SRC_STM',0,6,215,2,25,8,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,0,21
	.word	28201
	.byte	9
	.byte	'Ifx_SRC_STM',0,6,219,2,3
	.word	28247
	.byte	15
	.byte	'_Ifx_SRC_VADCCG',0,6,222,2,25,16,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2931
	.byte	4,2,35,12,0,21
	.word	28273
	.byte	9
	.byte	'Ifx_SRC_VADCCG',0,6,228,2,3
	.word	28348
	.byte	15
	.byte	'_Ifx_SRC_VADCG',0,6,231,2,25,16,6
	.byte	'SR0',0
	.word	2931
	.byte	4,2,35,0,6
	.byte	'SR1',0
	.word	2931
	.byte	4,2,35,4,6
	.byte	'SR2',0
	.word	2931
	.byte	4,2,35,8,6
	.byte	'SR3',0
	.word	2931
	.byte	4,2,35,12,0,21
	.word	28377
	.byte	9
	.byte	'Ifx_SRC_VADCG',0,6,237,2,3
	.word	28451
	.byte	15
	.byte	'_Ifx_SRC_XBAR',0,6,240,2,25,4,6
	.byte	'SRC',0
	.word	2931
	.byte	4,2,35,0,0,21
	.word	28479
	.byte	9
	.byte	'Ifx_SRC_XBAR',0,6,243,2,3
	.word	28513
	.byte	23,4
	.word	25051
	.byte	24,0,0,21
	.word	28540
	.byte	15
	.byte	'_Ifx_SRC_GAGBT',0,6,128,3,25,4,6
	.byte	'AGBT',0
	.word	28549
	.byte	4,2,35,0,0,21
	.word	28554
	.byte	9
	.byte	'Ifx_SRC_GAGBT',0,6,131,3,3
	.word	28590
	.byte	23,48
	.word	25109
	.byte	24,3,0,21
	.word	28618
	.byte	15
	.byte	'_Ifx_SRC_GASCLIN',0,6,134,3,25,48,6
	.byte	'ASCLIN',0
	.word	28627
	.byte	48,2,35,0,0,21
	.word	28632
	.byte	9
	.byte	'Ifx_SRC_GASCLIN',0,6,137,3,3
	.word	28672
	.byte	21
	.word	25196
	.byte	15
	.byte	'_Ifx_SRC_GBCU',0,6,140,3,25,4,6
	.byte	'SPB',0
	.word	28702
	.byte	4,2,35,0,0,21
	.word	28707
	.byte	9
	.byte	'Ifx_SRC_GBCU',0,6,143,3,3
	.word	28741
	.byte	23,64
	.word	25270
	.byte	24,0,0,21
	.word	28768
	.byte	15
	.byte	'_Ifx_SRC_GCAN',0,6,146,3,25,64,6
	.byte	'CAN',0
	.word	28777
	.byte	64,2,35,0,0,21
	.word	28782
	.byte	9
	.byte	'Ifx_SRC_GCAN',0,6,149,3,3
	.word	28816
	.byte	23,32
	.word	25327
	.byte	24,1,0,21
	.word	28843
	.byte	15
	.byte	'_Ifx_SRC_GCCU6',0,6,152,3,25,32,6
	.byte	'CCU6',0
	.word	28852
	.byte	32,2,35,0,0,21
	.word	28857
	.byte	9
	.byte	'Ifx_SRC_GCCU6',0,6,155,3,3
	.word	28893
	.byte	21
	.word	25434
	.byte	15
	.byte	'_Ifx_SRC_GCERBERUS',0,6,158,3,25,8,6
	.byte	'CERBERUS',0
	.word	28921
	.byte	8,2,35,0,0,21
	.word	28926
	.byte	9
	.byte	'Ifx_SRC_GCERBERUS',0,6,161,3,3
	.word	28970
	.byte	23,16
	.word	25500
	.byte	24,0,0,21
	.word	29002
	.byte	15
	.byte	'_Ifx_SRC_GCIF',0,6,164,3,25,16,6
	.byte	'CIF',0
	.word	29011
	.byte	16,2,35,0,0,21
	.word	29016
	.byte	9
	.byte	'Ifx_SRC_GCIF',0,6,167,3,3
	.word	29050
	.byte	23,8
	.word	25599
	.byte	24,1,0,21
	.word	29077
	.byte	15
	.byte	'_Ifx_SRC_GCPU',0,6,170,3,25,8,6
	.byte	'CPU',0
	.word	29086
	.byte	8,2,35,0,0,21
	.word	29091
	.byte	9
	.byte	'Ifx_SRC_GCPU',0,6,173,3,3
	.word	29125
	.byte	23,208,1
	.word	25670
	.byte	24,0,0,21
	.word	29152
	.byte	15
	.byte	'_Ifx_SRC_GDMA',0,6,176,3,25,208,1,6
	.byte	'DMA',0
	.word	29162
	.byte	208,1,2,35,0,0,21
	.word	29167
	.byte	9
	.byte	'Ifx_SRC_GDMA',0,6,179,3,3
	.word	29203
	.byte	21
	.word	25763
	.byte	21
	.word	25763
	.byte	21
	.word	25763
	.byte	15
	.byte	'_Ifx_SRC_GDSADC',0,6,182,3,25,32,6
	.byte	'DSADC0',0
	.word	29230
	.byte	8,2,35,0,6
	.byte	'reserved_8',0
	.word	7138
	.byte	8,2,35,8,6
	.byte	'DSADC2',0
	.word	29235
	.byte	8,2,35,16,6
	.byte	'DSADC3',0
	.word	29240
	.byte	8,2,35,24,0,21
	.word	29245
	.byte	9
	.byte	'Ifx_SRC_GDSADC',0,6,188,3,3
	.word	29336
	.byte	23,4
	.word	25839
	.byte	24,0,0,21
	.word	29365
	.byte	15
	.byte	'_Ifx_SRC_GEMEM',0,6,191,3,25,4,6
	.byte	'EMEM',0
	.word	29374
	.byte	4,2,35,0,0,21
	.word	29379
	.byte	9
	.byte	'Ifx_SRC_GEMEM',0,6,194,3,3
	.word	29415
	.byte	23,80
	.word	25908
	.byte	24,0,0,21
	.word	29443
	.byte	15
	.byte	'_Ifx_SRC_GERAY',0,6,197,3,25,80,6
	.byte	'ERAY',0
	.word	29452
	.byte	80,2,35,0,0,21
	.word	29457
	.byte	9
	.byte	'Ifx_SRC_GERAY',0,6,200,3,3
	.word	29493
	.byte	23,4
	.word	26062
	.byte	24,0,0,21
	.word	29521
	.byte	15
	.byte	'_Ifx_SRC_GETH',0,6,203,3,25,4,6
	.byte	'ETH',0
	.word	29530
	.byte	4,2,35,0,0,21
	.word	29535
	.byte	9
	.byte	'Ifx_SRC_GETH',0,6,206,3,3
	.word	29569
	.byte	23,4
	.word	26120
	.byte	24,0,0,21
	.word	29596
	.byte	15
	.byte	'_Ifx_SRC_GFCE',0,6,209,3,25,4,6
	.byte	'FCE',0
	.word	29605
	.byte	4,2,35,0,0,21
	.word	29610
	.byte	9
	.byte	'Ifx_SRC_GFCE',0,6,212,3,3
	.word	29644
	.byte	23,12
	.word	26178
	.byte	24,0,0,21
	.word	29671
	.byte	15
	.byte	'_Ifx_SRC_GFFT',0,6,215,3,25,12,6
	.byte	'FFT',0
	.word	29680
	.byte	12,2,35,0,0,21
	.word	29685
	.byte	9
	.byte	'Ifx_SRC_GFFT',0,6,218,3,3
	.word	29719
	.byte	23,64
	.word	26273
	.byte	24,1,0,21
	.word	29746
	.byte	15
	.byte	'_Ifx_SRC_GGPSR',0,6,221,3,25,64,6
	.byte	'GPSR',0
	.word	29755
	.byte	64,2,35,0,0,21
	.word	29760
	.byte	9
	.byte	'Ifx_SRC_GGPSR',0,6,224,3,3
	.word	29796
	.byte	23,48
	.word	26394
	.byte	24,0,0,21
	.word	29824
	.byte	15
	.byte	'_Ifx_SRC_GGPT12',0,6,227,3,25,48,6
	.byte	'GPT12',0
	.word	29833
	.byte	48,2,35,0,0,21
	.word	29838
	.byte	9
	.byte	'Ifx_SRC_GGPT12',0,6,230,3,3
	.word	29876
	.byte	23,204,18
	.word	26671
	.byte	24,0,0,21
	.word	29905
	.byte	15
	.byte	'_Ifx_SRC_GGTM',0,6,233,3,25,204,18,6
	.byte	'GTM',0
	.word	29915
	.byte	204,18,2,35,0,0,21
	.word	29920
	.byte	9
	.byte	'Ifx_SRC_GGTM',0,6,236,3,3
	.word	29956
	.byte	23,4
	.word	27157
	.byte	24,0,0,21
	.word	29983
	.byte	15
	.byte	'_Ifx_SRC_GHSCT',0,6,239,3,25,4,6
	.byte	'HSCT',0
	.word	29992
	.byte	4,2,35,0,0,21
	.word	29997
	.byte	9
	.byte	'Ifx_SRC_GHSCT',0,6,242,3,3
	.word	30033
	.byte	23,64
	.word	27217
	.byte	24,3,0,21
	.word	30061
	.byte	15
	.byte	'_Ifx_SRC_GHSSL',0,6,245,3,25,68,6
	.byte	'HSSL',0
	.word	30070
	.byte	64,2,35,0,6
	.byte	'EXI',0
	.word	2931
	.byte	4,2,35,64,0,21
	.word	30075
	.byte	9
	.byte	'Ifx_SRC_GHSSL',0,6,249,3,3
	.word	30124
	.byte	23,80
	.word	27326
	.byte	24,0,0,21
	.word	30152
	.byte	15
	.byte	'_Ifx_SRC_GI2C',0,6,252,3,25,80,6
	.byte	'I2C',0
	.word	30161
	.byte	80,2,35,0,0,21
	.word	30166
	.byte	9
	.byte	'Ifx_SRC_GI2C',0,6,255,3,3
	.word	30200
	.byte	23,4
	.word	27475
	.byte	24,0,0,21
	.word	30227
	.byte	15
	.byte	'_Ifx_SRC_GLMU',0,6,130,4,25,4,6
	.byte	'LMU',0
	.word	30236
	.byte	4,2,35,0,0,21
	.word	30241
	.byte	9
	.byte	'Ifx_SRC_GLMU',0,6,133,4,3
	.word	30275
	.byte	23,40
	.word	27533
	.byte	24,1,0,21
	.word	30302
	.byte	15
	.byte	'_Ifx_SRC_GMSC',0,6,136,4,25,40,6
	.byte	'MSC',0
	.word	30311
	.byte	40,2,35,0,0,21
	.word	30316
	.byte	9
	.byte	'Ifx_SRC_GMSC',0,6,139,4,3
	.word	30350
	.byte	23,8
	.word	27644
	.byte	24,1,0,21
	.word	30377
	.byte	15
	.byte	'_Ifx_SRC_GPMU',0,6,142,4,25,8,6
	.byte	'PMU',0
	.word	30386
	.byte	8,2,35,0,0,21
	.word	30391
	.byte	9
	.byte	'Ifx_SRC_GPMU',0,6,145,4,3
	.word	30425
	.byte	23,32
	.word	27702
	.byte	24,0,0,21
	.word	30452
	.byte	15
	.byte	'_Ifx_SRC_GPSI5',0,6,148,4,25,32,6
	.byte	'PSI5',0
	.word	30461
	.byte	32,2,35,0,0,21
	.word	30466
	.byte	9
	.byte	'Ifx_SRC_GPSI5',0,6,151,4,3
	.word	30502
	.byte	23,32
	.word	27762
	.byte	24,0,0,21
	.word	30530
	.byte	15
	.byte	'_Ifx_SRC_GPSI5S',0,6,154,4,25,32,6
	.byte	'PSI5S',0
	.word	30539
	.byte	32,2,35,0,0,21
	.word	30544
	.byte	9
	.byte	'Ifx_SRC_GPSI5S',0,6,157,4,3
	.word	30582
	.byte	23,96
	.word	27824
	.byte	24,3,0,21
	.word	30611
	.byte	15
	.byte	'_Ifx_SRC_GQSPI',0,6,160,4,25,96,6
	.byte	'QSPI',0
	.word	30620
	.byte	96,2,35,0,0,21
	.word	30625
	.byte	9
	.byte	'Ifx_SRC_GQSPI',0,6,163,4,3
	.word	30661
	.byte	23,4
	.word	27944
	.byte	24,0,0,21
	.word	30689
	.byte	15
	.byte	'_Ifx_SRC_GSCR',0,6,166,4,25,4,6
	.byte	'SCR',0
	.word	30698
	.byte	4,2,35,0,0,21
	.word	30703
	.byte	9
	.byte	'Ifx_SRC_GSCR',0,6,169,4,3
	.word	30737
	.byte	21
	.word	28002
	.byte	15
	.byte	'_Ifx_SRC_GSCU',0,6,172,4,25,20,6
	.byte	'SCU',0
	.word	30764
	.byte	20,2,35,0,0,21
	.word	30769
	.byte	9
	.byte	'Ifx_SRC_GSCU',0,6,175,4,3
	.word	30803
	.byte	23,24
	.word	28083
	.byte	24,0,0,21
	.word	30830
	.byte	15
	.byte	'_Ifx_SRC_GSENT',0,6,178,4,25,24,6
	.byte	'SENT',0
	.word	30839
	.byte	24,2,35,0,0,21
	.word	30844
	.byte	9
	.byte	'Ifx_SRC_GSENT',0,6,181,4,3
	.word	30880
	.byte	23,12
	.word	28143
	.byte	24,0,0,21
	.word	30908
	.byte	15
	.byte	'_Ifx_SRC_GSMU',0,6,184,4,25,12,6
	.byte	'SMU',0
	.word	30917
	.byte	12,2,35,0,0,21
	.word	30922
	.byte	9
	.byte	'Ifx_SRC_GSMU',0,6,187,4,3
	.word	30956
	.byte	23,16
	.word	28201
	.byte	24,1,0,21
	.word	30983
	.byte	15
	.byte	'_Ifx_SRC_GSTM',0,6,190,4,25,16,6
	.byte	'STM',0
	.word	30992
	.byte	16,2,35,0,0,21
	.word	30997
	.byte	9
	.byte	'Ifx_SRC_GSTM',0,6,193,4,3
	.word	31031
	.byte	23,64
	.word	28377
	.byte	24,3,0,21
	.word	31058
	.byte	23,224,1
	.word	226
	.byte	24,223,1,0,23,32
	.word	28273
	.byte	24,1,0,21
	.word	31083
	.byte	15
	.byte	'_Ifx_SRC_GVADC',0,6,196,4,25,192,2,6
	.byte	'G',0
	.word	31067
	.byte	64,2,35,0,6
	.byte	'reserved_40',0
	.word	31072
	.byte	224,1,2,35,64,6
	.byte	'CG',0
	.word	31092
	.byte	32,3,35,160,2,0,21
	.word	31097
	.byte	9
	.byte	'Ifx_SRC_GVADC',0,6,201,4,3
	.word	31166
	.byte	21
	.word	28479
	.byte	15
	.byte	'_Ifx_SRC_GXBAR',0,6,204,4,25,4,6
	.byte	'XBAR',0
	.word	31194
	.byte	4,2,35,0,0,21
	.word	31199
	.byte	9
	.byte	'Ifx_SRC_GXBAR',0,6,207,4,3
	.word	31235
	.byte	13,24,236,10,9,1,14
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,14
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,9
	.byte	'IfxScu_CCUCON0_CLKSEL',0,24,240,10,3
	.word	31263
	.byte	13,24,250,10,9,1,14
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,14
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,14
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,9
	.byte	'IfxScu_WDTCON1_IR',0,24,255,10,3
	.word	31360
	.byte	15
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,8,45,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_ACCEN0_Bits',0,8,79,3
	.word	31482
	.byte	15
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,8,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN1_Bits',0,8,85,3
	.word	32039
	.byte	15
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,8,88,16,4,4
	.byte	'STM0DIS',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'STM1DIS',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'STM2DIS',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,4
	.word	2618
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,8,94,3
	.word	32116
	.byte	15
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,8,97,16,4,4
	.byte	'BAUD1DIV',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'BAUD2DIV',0,1
	.word	226
	.byte	4,0,2,35,0,4
	.byte	'SRIDIV',0,1
	.word	226
	.byte	4,4,2,35,1,4
	.byte	'LPDIV',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'SPBDIV',0,1
	.word	226
	.byte	4,4,2,35,2,4
	.byte	'FSI2DIV',0,1
	.word	226
	.byte	2,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'FSIDIV',0,1
	.word	226
	.byte	2,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	2,4,2,35,3,4
	.byte	'CLKSEL',0,1
	.word	226
	.byte	2,2,2,35,3,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON0_Bits',0,8,111,3
	.word	32252
	.byte	15
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,8,114,16,4,4
	.byte	'CANDIV',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'ERAYDIV',0,1
	.word	226
	.byte	4,0,2,35,0,4
	.byte	'STMDIV',0,1
	.word	226
	.byte	4,4,2,35,1,4
	.byte	'GTMDIV',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'ETHDIV',0,1
	.word	226
	.byte	4,4,2,35,2,4
	.byte	'ASCLINFDIV',0,1
	.word	226
	.byte	4,0,2,35,2,4
	.byte	'ASCLINSDIV',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'INSEL',0,1
	.word	226
	.byte	2,2,2,35,3,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON1_Bits',0,8,126,3
	.word	32532
	.byte	15
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,8,129,1,16,4,4
	.byte	'BBBDIV',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	26,2,2,35,0,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON2_Bits',0,8,135,1,3
	.word	32770
	.byte	15
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,8,138,1,16,4,4
	.byte	'PLLDIV',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'PLLSEL',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'PLLERAYDIV',0,1
	.word	226
	.byte	6,2,2,35,1,4
	.byte	'PLLERAYSEL',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'SRIDIV',0,1
	.word	226
	.byte	6,2,2,35,2,4
	.byte	'SRISEL',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	5,3,2,35,3,4
	.byte	'SLCK',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON3_Bits',0,8,150,1,3
	.word	32898
	.byte	15
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,8,153,1,16,4,4
	.byte	'SPBDIV',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'SPBSEL',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'GTMDIV',0,1
	.word	226
	.byte	6,2,2,35,1,4
	.byte	'GTMSEL',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'STMDIV',0,1
	.word	226
	.byte	6,2,2,35,2,4
	.byte	'STMSEL',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	5,3,2,35,3,4
	.byte	'SLCK',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON4_Bits',0,8,165,1,3
	.word	33141
	.byte	15
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,8,168,1,16,4,4
	.byte	'MAXDIV',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	26,2,2,35,0,4
	.byte	'UP',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CCUCON5_Bits',0,8,174,1,3
	.word	33376
	.byte	15
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,8,177,1,16,4,4
	.byte	'CPU0DIV',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON6_Bits',0,8,181,1,3
	.word	33504
	.byte	15
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,8,184,1,16,4,4
	.byte	'CPU1DIV',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON7_Bits',0,8,188,1,3
	.word	33604
	.byte	15
	.byte	'_Ifx_SCU_CHIPID_Bits',0,8,191,1,16,4,4
	.byte	'CHREV',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'CHTEC',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'CHID',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'EEA',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'UCODE',0,1
	.word	226
	.byte	7,0,2,35,2,4
	.byte	'FSIZE',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'SP',0,1
	.word	226
	.byte	2,2,2,35,3,4
	.byte	'SEC',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_CHIPID_Bits',0,8,202,1,3
	.word	33704
	.byte	15
	.byte	'_Ifx_SCU_DTSCON_Bits',0,8,205,1,16,4,4
	.byte	'PWD',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'START',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'CAL',0,4
	.word	2618
	.byte	20,8,2,35,0,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'SLCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_DTSCON_Bits',0,8,213,1,3
	.word	33912
	.byte	15
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,8,216,1,16,4,4
	.byte	'LOWER',0,2
	.word	887
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	5,1,2,35,1,4
	.byte	'LLU',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'UPPER',0,2
	.word	887
	.byte	10,6,2,35,2,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	4,2,2,35,3,4
	.byte	'SLCK',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'UOF',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_DTSLIM_Bits',0,8,225,1,3
	.word	34077
	.byte	15
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,8,228,1,16,4,4
	.byte	'RESULT',0,2
	.word	887
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	4,2,2,35,1,4
	.byte	'RDY',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'BUSY',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,8,235,1,3
	.word	34260
	.byte	15
	.byte	'_Ifx_SCU_EICR_Bits',0,8,238,1,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'EXIS0',0,1
	.word	226
	.byte	3,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'FEN0',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'REN0',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'LDEN0',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EIEN0',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'INP0',0,1
	.word	226
	.byte	3,1,2,35,1,4
	.byte	'reserved_15',0,4
	.word	2618
	.byte	5,12,2,35,0,4
	.byte	'EXIS1',0,1
	.word	226
	.byte	3,1,2,35,2,4
	.byte	'reserved_23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'FEN1',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'REN1',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'LDEN1',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EIEN1',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'INP1',0,1
	.word	226
	.byte	3,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EICR_Bits',0,8,129,2,3
	.word	34414
	.byte	15
	.byte	'_Ifx_SCU_EIFR_Bits',0,8,132,2,16,4,4
	.byte	'INTF0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'INTF1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'INTF2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'INTF3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'INTF4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'INTF5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'INTF6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'INTF7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_EIFR_Bits',0,8,143,2,3
	.word	34778
	.byte	15
	.byte	'_Ifx_SCU_EMSR_Bits',0,8,146,2,16,4,4
	.byte	'POL',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'MODE',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'ENON',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PSEL',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,2
	.word	887
	.byte	12,0,2,35,0,4
	.byte	'EMSF',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'SEMSF',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	226
	.byte	6,0,2,35,2,4
	.byte	'EMSFM',0,1
	.word	226
	.byte	2,6,2,35,3,4
	.byte	'SEMSFM',0,1
	.word	226
	.byte	2,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_EMSR_Bits',0,8,159,2,3
	.word	34989
	.byte	15
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,8,162,2,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	7,1,2,35,0,4
	.byte	'EDCON',0,2
	.word	887
	.byte	2,7,2,35,0,4
	.byte	'reserved_9',0,4
	.word	2618
	.byte	23,0,2,35,0,0,9
	.byte	'Ifx_SCU_ESRCFG_Bits',0,8,167,2,3
	.word	35241
	.byte	15
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,8,170,2,16,4,4
	.byte	'ARI',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ARC',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_ESROCFG_Bits',0,8,175,2,3
	.word	35359
	.byte	15
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,8,178,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	28,4,2,35,0,4
	.byte	'EVR13OFF',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'BPEVR13OFF',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVR13CON_Bits',0,8,185,2,3
	.word	35470
	.byte	15
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,8,188,2,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	28,4,2,35,0,4
	.byte	'EVR33OFF',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'BPEVR33OFF',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVR33CON_Bits',0,8,195,2,3
	.word	35633
	.byte	15
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,8,198,2,16,4,4
	.byte	'ADC13V',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'ADC33V',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'ADCSWDV',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'VAL',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,8,205,2,3
	.word	35796
	.byte	15
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,8,208,2,16,4,4
	.byte	'DVS13TRIM',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'DVS33TRIM',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'VAL',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,8,215,2,3
	.word	35954
	.byte	15
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,8,218,2,16,4,4
	.byte	'EVR13OVMOD',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'EVR13UVMOD',0,1
	.word	226
	.byte	2,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'EVR33OVMOD',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	2,4,2,35,1,4
	.byte	'EVR33UVMOD',0,1
	.word	226
	.byte	2,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'SWDOVMOD',0,1
	.word	226
	.byte	2,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	226
	.byte	2,4,2,35,2,4
	.byte	'SWDUVMOD',0,1
	.word	226
	.byte	2,2,2,35,2,4
	.byte	'reserved_22',0,2
	.word	887
	.byte	10,0,2,35,2,0,9
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,8,232,2,3
	.word	36119
	.byte	15
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,8,235,2,16,4,4
	.byte	'OSCTRIM',0,2
	.word	887
	.byte	10,6,2,35,0,4
	.byte	'OSCPTAT',0,1
	.word	226
	.byte	6,0,2,35,1,4
	.byte	'OSCANASEL',0,1
	.word	226
	.byte	4,4,2,35,2,4
	.byte	'HPBGTRIM',0,2
	.word	887
	.byte	7,5,2,35,2,4
	.byte	'HPBGCLKEN',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'OSC3V3',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	226
	.byte	2,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,8,245,2,3
	.word	36448
	.byte	15
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,8,248,2,16,4,4
	.byte	'EVR13OVVAL',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'EVR33OVVAL',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SWDOVVAL',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVROVMON_Bits',0,8,255,2,3
	.word	36669
	.byte	15
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,8,130,3,16,4,4
	.byte	'RST13TRIM',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	16,8,2,35,0,4
	.byte	'RST13OFF',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'BPRST13OFF',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'RST33OFF',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'BPRST33OFF',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'RSTSWDOFF',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'BPRSTSWDOFF',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,8,142,3,3
	.word	36832
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,8,145,3,16,4,4
	.byte	'SD5P',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SD5I',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SD5D',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,8,152,3,3
	.word	37104
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,8,155,3,16,4,4
	.byte	'SD33P',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SD33I',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SD33D',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,8,162,3,3
	.word	37257
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,8,165,3,16,4,4
	.byte	'CT5REG0',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'CT5REG1',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'CT5REG2',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,8,172,3,3
	.word	37413
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,8,175,3,16,4,4
	.byte	'CT5REG3',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'CT5REG4',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,8,181,3,3
	.word	37575
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,8,184,3,16,4,4
	.byte	'CT33REG0',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'CT33REG1',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'CT33REG2',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,8,191,3,3
	.word	37718
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,8,194,3,16,4,4
	.byte	'CT33REG3',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'CT33REG4',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,8,200,3,3
	.word	37883
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,8,203,3,16,4,4
	.byte	'SDFREQSPRD',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'SDFREQ',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'SDSTEP',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	2,2,2,35,3,4
	.byte	'SDSAMPLE',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,8,211,3,3
	.word	38028
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,8,214,3,16,4,4
	.byte	'DRVP',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SDMINMAXDC',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'DRVN',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'SDLUT',0,1
	.word	226
	.byte	6,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,8,222,3,3
	.word	38209
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,8,225,3,16,4,4
	.byte	'SDPWMPRE',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SDPID',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SDVOKLVL',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,8,232,3,3
	.word	38383
	.byte	15
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,8,235,3,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SYNCDIV',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2618
	.byte	20,1,2,35,0,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,8,241,3,3
	.word	38543
	.byte	15
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,8,244,3,16,4,4
	.byte	'EVR13',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'OV13',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EVR33',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'OV33',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'OVSWD',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'UV13',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'UV33',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'UVSWD',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EXTPASS13',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EXTPASS33',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'BGPROK',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2618
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,8,130,4,3
	.word	38687
	.byte	15
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,8,133,4,16,4,4
	.byte	'EVR13TRIM',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'SDVOUTSEL',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	15,1,2,35,2,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,8,139,4,3
	.word	38961
	.byte	15
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,8,142,4,16,4,4
	.byte	'EVR13UVVAL',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'EVR33UVVAL',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SWDUVVAL',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,8,149,4,3
	.word	39100
	.byte	15
	.byte	'_Ifx_SCU_EXTCON_Bits',0,8,152,4,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'SEL0',0,1
	.word	226
	.byte	4,2,2,35,0,4
	.byte	'reserved_6',0,2
	.word	887
	.byte	10,0,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'NSEL',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'SEL1',0,1
	.word	226
	.byte	4,2,2,35,2,4
	.byte	'reserved_22',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'DIV1',0,1
	.word	226
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_SCU_EXTCON_Bits',0,8,163,4,3
	.word	39263
	.byte	15
	.byte	'_Ifx_SCU_FDR_Bits',0,8,166,4,16,4,4
	.byte	'STEP',0,2
	.word	887
	.byte	10,6,2,35,0,4
	.byte	'reserved_10',0,1
	.word	226
	.byte	4,2,2,35,1,4
	.byte	'DM',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'RESULT',0,2
	.word	887
	.byte	10,6,2,35,2,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	5,1,2,35,3,4
	.byte	'DISCLK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_FDR_Bits',0,8,174,4,3
	.word	39481
	.byte	15
	.byte	'_Ifx_SCU_FMR_Bits',0,8,177,4,16,4,4
	.byte	'FS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'FS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'FS2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'FS3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'FS4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'FS5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'FS6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'FS7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'FC0',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'FC1',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'FC2',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'FC3',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'FC4',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'FC5',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'FC6',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'FC7',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_SCU_FMR_Bits',0,8,197,4,3
	.word	39644
	.byte	15
	.byte	'_Ifx_SCU_ID_Bits',0,8,200,4,16,4,4
	.byte	'MODREV',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_ID_Bits',0,8,205,4,3
	.word	39980
	.byte	15
	.byte	'_Ifx_SCU_IGCR_Bits',0,8,208,4,16,4,4
	.byte	'IPEN00',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'IPEN01',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'IPEN02',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'IPEN03',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'IPEN04',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'IPEN05',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'IPEN06',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'IPEN07',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	5,3,2,35,1,4
	.byte	'GEEN0',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'IGP0',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'IPEN10',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'IPEN11',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'IPEN12',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'IPEN13',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'IPEN14',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'IPEN15',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'IPEN16',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'IPEN17',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	5,3,2,35,3,4
	.byte	'GEEN1',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'IGP1',0,1
	.word	226
	.byte	2,0,2,35,3,0,9
	.byte	'Ifx_SCU_IGCR_Bits',0,8,232,4,3
	.word	40087
	.byte	15
	.byte	'_Ifx_SCU_IN_Bits',0,8,235,4,16,4,4
	.byte	'P0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_IN_Bits',0,8,240,4,3
	.word	40539
	.byte	15
	.byte	'_Ifx_SCU_IOCR_Bits',0,8,243,4,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'PC0',0,1
	.word	226
	.byte	4,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	4,4,2,35,1,4
	.byte	'PC1',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_IOCR_Bits',0,8,250,4,3
	.word	40638
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,8,253,4,16,4,4
	.byte	'LBISTREQ',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'LBISTREQP',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PATTERNS',0,2
	.word	887
	.byte	14,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,8,131,5,3
	.word	40788
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,8,134,5,16,4,4
	.byte	'SEED',0,4
	.word	2618
	.byte	23,9,2,35,0,4
	.byte	'reserved_23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'SPLITSH',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'BODY',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'LBISTFREQU',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,8,141,5,3
	.word	40937
	.byte	15
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,8,144,5,16,4,4
	.byte	'SIGNATURE',0,4
	.word	2618
	.byte	24,8,2,35,0,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	7,1,2,35,3,4
	.byte	'LBISTDONE',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,8,149,5,3
	.word	41098
	.byte	15
	.byte	'_Ifx_SCU_LCLCON_Bits',0,8,152,5,16,4,4
	.byte	'reserved_0',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'LS',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,2
	.word	887
	.byte	14,1,2,35,2,4
	.byte	'LSEN',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_LCLCON_Bits',0,8,158,5,3
	.word	41228
	.byte	15
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,8,161,5,16,4,4
	.byte	'LCLT0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'LCLT1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_LCLTEST_Bits',0,8,166,5,3
	.word	41360
	.byte	15
	.byte	'_Ifx_SCU_MANID_Bits',0,8,169,5,16,4,4
	.byte	'DEPT',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'MANUF',0,2
	.word	887
	.byte	11,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_MANID_Bits',0,8,174,5,3
	.word	41475
	.byte	15
	.byte	'_Ifx_SCU_OMR_Bits',0,8,177,5,16,4,4
	.byte	'PS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,2
	.word	887
	.byte	14,0,2,35,0,4
	.byte	'PCL0',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'PCL1',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	887
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_SCU_OMR_Bits',0,8,185,5,3
	.word	41586
	.byte	15
	.byte	'_Ifx_SCU_OSCCON_Bits',0,8,188,5,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PLLLV',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'OSCRES',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'GAINSEL',0,1
	.word	226
	.byte	2,3,2,35,0,4
	.byte	'MODE',0,1
	.word	226
	.byte	2,1,2,35,0,4
	.byte	'SHBY',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PLLHV',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'X1D',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'X1DEN',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'OSCVAL',0,1
	.word	226
	.byte	5,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	226
	.byte	2,1,2,35,2,4
	.byte	'APREN',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'CAP0EN',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'CAP1EN',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'CAP2EN',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'CAP3EN',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_OSCCON_Bits',0,8,209,5,3
	.word	41744
	.byte	15
	.byte	'_Ifx_SCU_OUT_Bits',0,8,212,5,16,4,4
	.byte	'P0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'P1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_OUT_Bits',0,8,217,5,3
	.word	42156
	.byte	15
	.byte	'_Ifx_SCU_OVCCON_Bits',0,8,220,5,16,4,4
	.byte	'CSEL0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'CSEL1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'CSEL2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,2
	.word	887
	.byte	13,0,2,35,0,4
	.byte	'OVSTRT',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'OVSTP',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'DCINVAL',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'OVCONF',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'POVCONF',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	6,0,2,35,3,0,9
	.byte	'Ifx_SCU_OVCCON_Bits',0,8,233,5,3
	.word	42257
	.byte	15
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,8,236,5,16,4,4
	.byte	'OVEN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'OVEN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'OVEN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,4
	.word	2618
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,8,242,5,3
	.word	42524
	.byte	15
	.byte	'_Ifx_SCU_PDISC_Bits',0,8,245,5,16,4,4
	.byte	'PDIS0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PDIS1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDISC_Bits',0,8,250,5,3
	.word	42660
	.byte	15
	.byte	'_Ifx_SCU_PDR_Bits',0,8,253,5,16,4,4
	.byte	'PD0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'PL0',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PD1',0,1
	.word	226
	.byte	3,1,2,35,0,4
	.byte	'PL1',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDR_Bits',0,8,132,6,3
	.word	42771
	.byte	15
	.byte	'_Ifx_SCU_PDRR_Bits',0,8,135,6,16,4,4
	.byte	'PDR0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PDR1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PDR2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PDR3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PDR4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PDR5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PDR6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PDR7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PDRR_Bits',0,8,146,6,3
	.word	42904
	.byte	15
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,8,149,6,16,4,4
	.byte	'VCOBYP',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'VCOPWD',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'MODEN',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'SETFINDIS',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'CLRFINDIS',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'OSCDISCDIS',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,2
	.word	887
	.byte	2,7,2,35,0,4
	.byte	'NDIV',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'PLLPWD',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'RESLD',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'PDIV',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PLLCON0_Bits',0,8,166,6,3
	.word	43107
	.byte	15
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,8,169,6,16,4,4
	.byte	'K2DIV',0,1
	.word	226
	.byte	7,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'K3DIV',0,1
	.word	226
	.byte	7,1,2,35,1,4
	.byte	'reserved_15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'K1DIV',0,1
	.word	226
	.byte	7,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	887
	.byte	9,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLCON1_Bits',0,8,177,6,3
	.word	43463
	.byte	15
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,8,180,6,16,4,4
	.byte	'MODCFG',0,2
	.word	887
	.byte	16,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLCON2_Bits',0,8,184,6,3
	.word	43641
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,8,187,6,16,4,4
	.byte	'VCOBYP',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'VCOPWD',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'SETFINDIS',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'CLRFINDIS',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'OSCDISCDIS',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,2
	.word	887
	.byte	2,7,2,35,0,4
	.byte	'NDIV',0,1
	.word	226
	.byte	5,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'PLLPWD',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'RESLD',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	226
	.byte	5,0,2,35,2,4
	.byte	'PDIV',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,8,204,6,3
	.word	43741
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,8,207,6,16,4,4
	.byte	'K2DIV',0,1
	.word	226
	.byte	7,1,2,35,0,4
	.byte	'reserved_7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'K3DIV',0,1
	.word	226
	.byte	4,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'K1DIV',0,1
	.word	226
	.byte	7,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	887
	.byte	9,0,2,35,2,0,9
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,8,215,6,3
	.word	44111
	.byte	15
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,8,218,6,16,4,4
	.byte	'VCOBYST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PWDSTAT',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'VCOLOCK',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'FINDIS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'K1RDY',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'K2RDY',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,8,227,6,3
	.word	44297
	.byte	15
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,8,230,6,16,4,4
	.byte	'VCOBYST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'VCOLOCK',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'FINDIS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'K1RDY',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'K2RDY',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'MODRUN',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,8,241,6,3
	.word	44495
	.byte	15
	.byte	'_Ifx_SCU_PMCSR_Bits',0,8,244,6,16,4,4
	.byte	'REQSLP',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'SMUSLP',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	226
	.byte	5,0,2,35,0,4
	.byte	'PMST',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,4
	.word	2618
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_SCU_PMCSR_Bits',0,8,251,6,3
	.word	44728
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,8,254,6,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1WKEN',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PINAWKEN',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PINBWKEN',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'ESR0DFEN',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'ESR0EDCON',0,1
	.word	226
	.byte	2,1,2,35,0,4
	.byte	'ESR1DFEN',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'ESR1EDCON',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'PINADFEN',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'PINAEDCON',0,1
	.word	226
	.byte	2,3,2,35,1,4
	.byte	'PINBDFEN',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PINBEDCON',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'SCREN',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'STBYRAMSEL',0,1
	.word	226
	.byte	2,5,2,35,2,4
	.byte	'SCRCLKSEL',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'SCRWKEN',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'TRISTEN',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'TRISTREQ',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'PORSTDF',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'DCDCSYNC',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	3,3,2,35,3,4
	.byte	'ESR0TRIST',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,8,153,7,3
	.word	44880
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,8,156,7,16,4,4
	.byte	'SCRSTEN',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SCRSTREQ',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	6,0,2,35,0,4
	.byte	'CPUIDLSEL',0,1
	.word	226
	.byte	3,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'IRADIS',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	226
	.byte	3,0,2,35,1,4
	.byte	'SCRCFG',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'CPUSEL',0,1
	.word	226
	.byte	3,5,2,35,3,4
	.byte	'STBYEVEN',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'STBYEV',0,1
	.word	226
	.byte	3,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,8,170,7,3
	.word	45447
	.byte	15
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,8,173,7,16,4,4
	.byte	'SCRINT',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'BUSY',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'SCRECC',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'SCRWDT',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'SCRRST',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'reserved_12',0,1
	.word	226
	.byte	4,0,2,35,1,4
	.byte	'TCINT',0,1
	.word	226
	.byte	8,0,2,35,2,4
	.byte	'TCINTREQ',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'SMURST',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'RST',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	226
	.byte	4,1,2,35,3,4
	.byte	'LCK',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,8,187,7,3
	.word	45741
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,8,190,7,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'ESR1WKP',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'ESR1OVRUN',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PINAWKP',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PINAOVRUN',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PINBWKP',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PINBOVRUN',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PORSTDF',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'HWCFGEVR',0,1
	.word	226
	.byte	3,3,2,35,1,4
	.byte	'STBYRAM',0,1
	.word	226
	.byte	2,1,2,35,1,4
	.byte	'TRIST',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'SCRST',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'SCRWKP',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'SCR',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'SCRWKEN',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'ESR1WKEN',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'PINAWKEN',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'PINBWKEN',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	887
	.byte	4,5,2,35,2,4
	.byte	'ESR0TRIST',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'reserved_28',0,1
	.word	226
	.byte	4,0,2,35,3,0,9
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,8,214,7,3
	.word	46019
	.byte	15
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,8,217,7,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'ESR1WKPCLR',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'ESR1OVRUNCLR',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PINAWKPCLR',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'PINAOVRUNCLR',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PINBWKPCLR',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PINBOVRUNCLR',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'SCRSTCLR',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'SCRWKPCLR',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	887
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,8,230,7,3
	.word	46515
	.byte	15
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,8,233,7,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'CLRC',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,2
	.word	887
	.byte	10,4,2,35,0,4
	.byte	'CSS0',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'CSS1',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'CSS2',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'reserved_15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'USRINFO',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_RSTCON2_Bits',0,8,243,7,3
	.word	46828
	.byte	15
	.byte	'_Ifx_SCU_RSTCON_Bits',0,8,246,7,16,4,4
	.byte	'ESR0',0,1
	.word	226
	.byte	2,6,2,35,0,4
	.byte	'ESR1',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	226
	.byte	2,2,2,35,0,4
	.byte	'SMU',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'SW',0,1
	.word	226
	.byte	2,6,2,35,1,4
	.byte	'STM0',0,1
	.word	226
	.byte	2,4,2,35,1,4
	.byte	'STM1',0,1
	.word	226
	.byte	2,2,2,35,1,4
	.byte	'STM2',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_RSTCON_Bits',0,8,129,8,3
	.word	47037
	.byte	15
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,8,132,8,16,4,4
	.byte	'ESR0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SMU',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'SW',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'STM0',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'STM1',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'STM2',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'reserved_8',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'PORST',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'reserved_17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'CB0',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'CB1',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'CB3',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	226
	.byte	2,1,2,35,2,4
	.byte	'EVR13',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EVR33',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'SWD',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'reserved_26',0,1
	.word	226
	.byte	2,4,2,35,3,4
	.byte	'STBYR',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	226
	.byte	3,0,2,35,3,0,9
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,8,155,8,3
	.word	47248
	.byte	15
	.byte	'_Ifx_SCU_SAFECON_Bits',0,8,158,8,16,4,4
	.byte	'HBT',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_SCU_SAFECON_Bits',0,8,162,8,3
	.word	47680
	.byte	15
	.byte	'_Ifx_SCU_STSTAT_Bits',0,8,165,8,16,4,4
	.byte	'HWCFG',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'FTM',0,1
	.word	226
	.byte	7,1,2,35,1,4
	.byte	'MODE',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'FCBAE',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'LUDIS',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'TRSTL',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'SPDEN',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	226
	.byte	3,0,2,35,2,4
	.byte	'RAMINT',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'reserved_25',0,1
	.word	226
	.byte	7,0,2,35,3,0,9
	.byte	'Ifx_SCU_STSTAT_Bits',0,8,178,8,3
	.word	47776
	.byte	15
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,8,181,8,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SWRSTREQ',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,8,186,8,3
	.word	48036
	.byte	15
	.byte	'_Ifx_SCU_SYSCON_Bits',0,8,189,8,16,4,4
	.byte	'CCTRIG0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'RAMINTM',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'SETLUDIS',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	226
	.byte	3,0,2,35,0,4
	.byte	'DATM',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,4
	.word	2618
	.byte	23,0,2,35,0,0,9
	.byte	'Ifx_SCU_SYSCON_Bits',0,8,198,8,3
	.word	48161
	.byte	15
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,8,201,8,16,4,4
	.byte	'ESR0T',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,8,208,8,3
	.word	48358
	.byte	15
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,8,211,8,16,4,4
	.byte	'ESR0T',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,8,218,8,3
	.word	48511
	.byte	15
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,8,221,8,16,4,4
	.byte	'ESR0T',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSET_Bits',0,8,228,8,3
	.word	48664
	.byte	15
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,8,231,8,16,4,4
	.byte	'ESR0T',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'ESR1T',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SMUT',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,8,238,8,3
	.word	48817
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,8,247,8,3
	.word	3039
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,8,134,9,3
	.word	3175
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,8,150,9,3
	.word	3419
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,8,153,9,16,4,4
	.byte	'ENDINIT',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'LCK',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'PW',0,4
	.word	3023
	.byte	14,16,2,35,0,4
	.byte	'REL',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,8,159,9,3
	.word	49072
	.byte	15
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,8,162,9,16,4,4
	.byte	'CLRIRF',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'IR0',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'DR',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'IR1',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'UR',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PAR',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'TCR',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'TCTR',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,8,175,9,3
	.word	49198
	.byte	15
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,8,178,9,16,4,4
	.byte	'AE',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'OE',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'IS0',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'DS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'TO',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'IS1',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'US',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PAS',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'TCS',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'TCT',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'TIM',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,8,191,9,3
	.word	49450
	.byte	5,8,199,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	31482
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN0',0,8,204,9,3
	.word	49669
	.byte	5,8,207,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32039
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ACCEN1',0,8,212,9,3
	.word	49733
	.byte	5,8,215,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32116
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ARSTDIS',0,8,220,9,3
	.word	49797
	.byte	5,8,223,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32252
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON0',0,8,228,9,3
	.word	49862
	.byte	5,8,231,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32532
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON1',0,8,236,9,3
	.word	49927
	.byte	5,8,239,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32770
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON2',0,8,244,9,3
	.word	49992
	.byte	5,8,247,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	32898
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON3',0,8,252,9,3
	.word	50057
	.byte	5,8,255,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33141
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON4',0,8,132,10,3
	.word	50122
	.byte	5,8,135,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33376
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON5',0,8,140,10,3
	.word	50187
	.byte	5,8,143,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33504
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON6',0,8,148,10,3
	.word	50252
	.byte	5,8,151,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33604
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CCUCON7',0,8,156,10,3
	.word	50317
	.byte	5,8,159,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33704
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_CHIPID',0,8,164,10,3
	.word	50382
	.byte	5,8,167,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	33912
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSCON',0,8,172,10,3
	.word	50446
	.byte	5,8,175,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34077
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSLIM',0,8,180,10,3
	.word	50510
	.byte	5,8,183,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34260
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_DTSSTAT',0,8,188,10,3
	.word	50574
	.byte	5,8,191,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34414
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EICR',0,8,196,10,3
	.word	50639
	.byte	5,8,199,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34778
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EIFR',0,8,204,10,3
	.word	50701
	.byte	5,8,207,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	34989
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EMSR',0,8,212,10,3
	.word	50763
	.byte	5,8,215,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35241
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ESRCFG',0,8,220,10,3
	.word	50825
	.byte	5,8,223,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35359
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ESROCFG',0,8,228,10,3
	.word	50889
	.byte	5,8,231,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35470
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVR13CON',0,8,236,10,3
	.word	50954
	.byte	5,8,239,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35633
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVR33CON',0,8,244,10,3
	.word	51020
	.byte	5,8,247,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35796
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRADCSTAT',0,8,252,10,3
	.word	51086
	.byte	5,8,255,10,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	35954
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRDVSTAT',0,8,132,11,3
	.word	51154
	.byte	5,8,135,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36119
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRMONCTRL',0,8,140,11,3
	.word	51221
	.byte	5,8,143,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36448
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVROSCCTRL',0,8,148,11,3
	.word	51289
	.byte	5,8,151,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36669
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVROVMON',0,8,156,11,3
	.word	51357
	.byte	5,8,159,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	36832
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRRSTCON',0,8,164,11,3
	.word	51423
	.byte	5,8,167,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37104
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,8,172,11,3
	.word	51490
	.byte	5,8,175,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37257
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,8,180,11,3
	.word	51559
	.byte	5,8,183,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37413
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,8,188,11,3
	.word	51628
	.byte	5,8,191,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37575
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,8,196,11,3
	.word	51697
	.byte	5,8,199,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37718
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,8,204,11,3
	.word	51766
	.byte	5,8,207,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	37883
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,8,212,11,3
	.word	51835
	.byte	5,8,215,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38028
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL1',0,8,220,11,3
	.word	51904
	.byte	5,8,223,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38209
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL2',0,8,228,11,3
	.word	51972
	.byte	5,8,231,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38383
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL3',0,8,236,11,3
	.word	52040
	.byte	5,8,239,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38543
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSDCTRL4',0,8,244,11,3
	.word	52108
	.byte	5,8,247,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38687
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRSTAT',0,8,252,11,3
	.word	52176
	.byte	5,8,255,11,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	38961
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRTRIM',0,8,132,12,3
	.word	52241
	.byte	5,8,135,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39100
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EVRUVMON',0,8,140,12,3
	.word	52306
	.byte	5,8,143,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39263
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_EXTCON',0,8,148,12,3
	.word	52372
	.byte	5,8,151,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39481
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_FDR',0,8,156,12,3
	.word	52436
	.byte	5,8,159,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39644
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_FMR',0,8,164,12,3
	.word	52497
	.byte	5,8,167,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	39980
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_ID',0,8,172,12,3
	.word	52558
	.byte	5,8,175,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40087
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IGCR',0,8,180,12,3
	.word	52618
	.byte	5,8,183,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40539
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IN',0,8,188,12,3
	.word	52680
	.byte	5,8,191,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40638
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_IOCR',0,8,196,12,3
	.word	52740
	.byte	5,8,199,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40788
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL0',0,8,204,12,3
	.word	52802
	.byte	5,8,207,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	40937
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL1',0,8,212,12,3
	.word	52870
	.byte	5,8,215,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41098
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LBISTCTRL2',0,8,220,12,3
	.word	52938
	.byte	5,8,223,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41228
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LCLCON',0,8,228,12,3
	.word	53006
	.byte	5,8,231,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41360
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_LCLTEST',0,8,236,12,3
	.word	53070
	.byte	5,8,239,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41475
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_MANID',0,8,244,12,3
	.word	53135
	.byte	5,8,247,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41586
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OMR',0,8,252,12,3
	.word	53198
	.byte	5,8,255,12,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	41744
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OSCCON',0,8,132,13,3
	.word	53259
	.byte	5,8,135,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42156
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OUT',0,8,140,13,3
	.word	53323
	.byte	5,8,143,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42257
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OVCCON',0,8,148,13,3
	.word	53384
	.byte	5,8,151,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42524
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_OVCENABLE',0,8,156,13,3
	.word	53448
	.byte	5,8,159,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42660
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDISC',0,8,164,13,3
	.word	53515
	.byte	5,8,167,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42771
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDR',0,8,172,13,3
	.word	53578
	.byte	5,8,175,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	42904
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PDRR',0,8,180,13,3
	.word	53639
	.byte	5,8,183,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43107
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON0',0,8,188,13,3
	.word	53701
	.byte	5,8,191,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43463
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON1',0,8,196,13,3
	.word	53766
	.byte	5,8,199,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43641
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLCON2',0,8,204,13,3
	.word	53831
	.byte	5,8,207,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	43741
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYCON0',0,8,212,13,3
	.word	53896
	.byte	5,8,215,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44111
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYCON1',0,8,220,13,3
	.word	53965
	.byte	5,8,223,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44297
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLERAYSTAT',0,8,228,13,3
	.word	54034
	.byte	5,8,231,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44495
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PLLSTAT',0,8,236,13,3
	.word	54103
	.byte	5,8,239,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44728
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMCSR',0,8,244,13,3
	.word	54168
	.byte	5,8,247,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	44880
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR0',0,8,252,13,3
	.word	54231
	.byte	5,8,255,13,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45447
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR1',0,8,132,14,3
	.word	54296
	.byte	5,8,135,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	45741
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWCR2',0,8,140,14,3
	.word	54361
	.byte	5,8,143,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46019
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWSTAT',0,8,148,14,3
	.word	54426
	.byte	5,8,151,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46515
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_PMSWSTATCLR',0,8,156,14,3
	.word	54492
	.byte	5,8,159,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47037
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTCON',0,8,164,14,3
	.word	54561
	.byte	5,8,167,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	46828
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTCON2',0,8,172,14,3
	.word	54625
	.byte	5,8,175,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47248
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_RSTSTAT',0,8,180,14,3
	.word	54690
	.byte	5,8,183,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47680
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SAFECON',0,8,188,14,3
	.word	54755
	.byte	5,8,191,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	47776
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_STSTAT',0,8,196,14,3
	.word	54820
	.byte	5,8,199,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48036
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SWRSTCON',0,8,204,14,3
	.word	54884
	.byte	5,8,207,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48161
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_SYSCON',0,8,212,14,3
	.word	54950
	.byte	5,8,215,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48358
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPCLR',0,8,220,14,3
	.word	55014
	.byte	5,8,223,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48511
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPDIS',0,8,228,14,3
	.word	55079
	.byte	5,8,231,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48664
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSET',0,8,236,14,3
	.word	55144
	.byte	5,8,239,14,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	48817
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_TRAPSTAT',0,8,244,14,3
	.word	55209
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON0',0,8,252,14,3
	.word	3135
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_CON1',0,8,132,15,3
	.word	3379
	.byte	9
	.byte	'Ifx_SCU_WDTCPU_SR',0,8,140,15,3
	.word	3610
	.byte	5,8,143,15,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49072
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON0',0,8,148,15,3
	.word	55360
	.byte	5,8,151,15,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49198
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_CON1',0,8,156,15,3
	.word	55427
	.byte	5,8,159,15,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	49450
	.byte	4,2,35,0,0,9
	.byte	'Ifx_SCU_WDTS_SR',0,8,164,15,3
	.word	55494
	.byte	21
	.word	3650
	.byte	9
	.byte	'Ifx_SCU_WDTCPU',0,8,180,15,3
	.word	55559
	.byte	15
	.byte	'_Ifx_SCU_WDTS',0,8,183,15,25,12,6
	.byte	'CON0',0
	.word	55360
	.byte	4,2,35,0,6
	.byte	'CON1',0
	.word	55427
	.byte	4,2,35,4,6
	.byte	'SR',0
	.word	55494
	.byte	4,2,35,8,0,21
	.word	55588
	.byte	9
	.byte	'Ifx_SCU_WDTS',0,8,188,15,3
	.word	55649
	.byte	23,8
	.word	50825
	.byte	24,1,0,23,20
	.word	226
	.byte	24,19,0,23,8
	.word	54168
	.byte	24,1,0,21
	.word	55588
	.byte	23,24
	.word	3650
	.byte	24,1,0,21
	.word	55708
	.byte	23,28
	.word	226
	.byte	24,27,0,23,16
	.word	50639
	.byte	24,3,0,23,16
	.word	52618
	.byte	24,3,0,23,180,3
	.word	226
	.byte	24,179,3,0,15
	.byte	'_Ifx_SCU',0,8,201,15,25,128,8,6
	.byte	'reserved_0',0
	.word	7138
	.byte	8,2,35,0,6
	.byte	'ID',0
	.word	52558
	.byte	4,2,35,8,6
	.byte	'reserved_C',0
	.word	5319
	.byte	4,2,35,12,6
	.byte	'OSCCON',0
	.word	53259
	.byte	4,2,35,16,6
	.byte	'PLLSTAT',0
	.word	54103
	.byte	4,2,35,20,6
	.byte	'PLLCON0',0
	.word	53701
	.byte	4,2,35,24,6
	.byte	'PLLCON1',0
	.word	53766
	.byte	4,2,35,28,6
	.byte	'PLLCON2',0
	.word	53831
	.byte	4,2,35,32,6
	.byte	'PLLERAYSTAT',0
	.word	54034
	.byte	4,2,35,36,6
	.byte	'PLLERAYCON0',0
	.word	53896
	.byte	4,2,35,40,6
	.byte	'PLLERAYCON1',0
	.word	53965
	.byte	4,2,35,44,6
	.byte	'CCUCON0',0
	.word	49862
	.byte	4,2,35,48,6
	.byte	'CCUCON1',0
	.word	49927
	.byte	4,2,35,52,6
	.byte	'FDR',0
	.word	52436
	.byte	4,2,35,56,6
	.byte	'EXTCON',0
	.word	52372
	.byte	4,2,35,60,6
	.byte	'CCUCON2',0
	.word	49992
	.byte	4,2,35,64,6
	.byte	'CCUCON3',0
	.word	50057
	.byte	4,2,35,68,6
	.byte	'CCUCON4',0
	.word	50122
	.byte	4,2,35,72,6
	.byte	'CCUCON5',0
	.word	50187
	.byte	4,2,35,76,6
	.byte	'RSTSTAT',0
	.word	54690
	.byte	4,2,35,80,6
	.byte	'reserved_54',0
	.word	5319
	.byte	4,2,35,84,6
	.byte	'RSTCON',0
	.word	54561
	.byte	4,2,35,88,6
	.byte	'ARSTDIS',0
	.word	49797
	.byte	4,2,35,92,6
	.byte	'SWRSTCON',0
	.word	54884
	.byte	4,2,35,96,6
	.byte	'RSTCON2',0
	.word	54625
	.byte	4,2,35,100,6
	.byte	'reserved_68',0
	.word	5319
	.byte	4,2,35,104,6
	.byte	'EVRRSTCON',0
	.word	51423
	.byte	4,2,35,108,6
	.byte	'ESRCFG',0
	.word	55676
	.byte	8,2,35,112,6
	.byte	'ESROCFG',0
	.word	50889
	.byte	4,2,35,120,6
	.byte	'SYSCON',0
	.word	54950
	.byte	4,2,35,124,6
	.byte	'CCUCON6',0
	.word	50252
	.byte	4,3,35,128,1,6
	.byte	'CCUCON7',0
	.word	50317
	.byte	4,3,35,132,1,6
	.byte	'reserved_88',0
	.word	55685
	.byte	20,3,35,136,1,6
	.byte	'PDR',0
	.word	53578
	.byte	4,3,35,156,1,6
	.byte	'IOCR',0
	.word	52740
	.byte	4,3,35,160,1,6
	.byte	'OUT',0
	.word	53323
	.byte	4,3,35,164,1,6
	.byte	'OMR',0
	.word	53198
	.byte	4,3,35,168,1,6
	.byte	'IN',0
	.word	52680
	.byte	4,3,35,172,1,6
	.byte	'EVRSTAT',0
	.word	52176
	.byte	4,3,35,176,1,6
	.byte	'EVRDVSTAT',0
	.word	51154
	.byte	4,3,35,180,1,6
	.byte	'EVR13CON',0
	.word	50954
	.byte	4,3,35,184,1,6
	.byte	'EVR33CON',0
	.word	51020
	.byte	4,3,35,188,1,6
	.byte	'STSTAT',0
	.word	54820
	.byte	4,3,35,192,1,6
	.byte	'reserved_C4',0
	.word	5319
	.byte	4,3,35,196,1,6
	.byte	'PMSWCR0',0
	.word	54231
	.byte	4,3,35,200,1,6
	.byte	'PMSWSTAT',0
	.word	54426
	.byte	4,3,35,204,1,6
	.byte	'PMSWSTATCLR',0
	.word	54492
	.byte	4,3,35,208,1,6
	.byte	'PMCSR',0
	.word	55694
	.byte	8,3,35,212,1,6
	.byte	'reserved_DC',0
	.word	5319
	.byte	4,3,35,220,1,6
	.byte	'DTSSTAT',0
	.word	50574
	.byte	4,3,35,224,1,6
	.byte	'DTSCON',0
	.word	50446
	.byte	4,3,35,228,1,6
	.byte	'PMSWCR1',0
	.word	54296
	.byte	4,3,35,232,1,6
	.byte	'PMSWCR2',0
	.word	54361
	.byte	4,3,35,236,1,6
	.byte	'WDTS',0
	.word	55703
	.byte	12,3,35,240,1,6
	.byte	'EMSR',0
	.word	50763
	.byte	4,3,35,252,1,6
	.byte	'WDTCPU',0
	.word	55717
	.byte	24,3,35,128,2,6
	.byte	'reserved_118',0
	.word	7478
	.byte	12,3,35,152,2,6
	.byte	'TRAPSTAT',0
	.word	55209
	.byte	4,3,35,164,2,6
	.byte	'TRAPSET',0
	.word	55144
	.byte	4,3,35,168,2,6
	.byte	'TRAPCLR',0
	.word	55014
	.byte	4,3,35,172,2,6
	.byte	'TRAPDIS',0
	.word	55079
	.byte	4,3,35,176,2,6
	.byte	'reserved_134',0
	.word	5319
	.byte	4,3,35,180,2,6
	.byte	'LCLCON1',0
	.word	53006
	.byte	4,3,35,184,2,6
	.byte	'LCLTEST',0
	.word	53070
	.byte	4,3,35,188,2,6
	.byte	'CHIPID',0
	.word	50382
	.byte	4,3,35,192,2,6
	.byte	'MANID',0
	.word	53135
	.byte	4,3,35,196,2,6
	.byte	'reserved_148',0
	.word	7138
	.byte	8,3,35,200,2,6
	.byte	'SAFECON',0
	.word	54755
	.byte	4,3,35,208,2,6
	.byte	'reserved_154',0
	.word	26264
	.byte	16,3,35,212,2,6
	.byte	'LBISTCTRL0',0
	.word	52802
	.byte	4,3,35,228,2,6
	.byte	'LBISTCTRL1',0
	.word	52870
	.byte	4,3,35,232,2,6
	.byte	'LBISTCTRL2',0
	.word	52938
	.byte	4,3,35,236,2,6
	.byte	'reserved_170',0
	.word	55722
	.byte	28,3,35,240,2,6
	.byte	'PDISC',0
	.word	53515
	.byte	4,3,35,140,3,6
	.byte	'reserved_190',0
	.word	7138
	.byte	8,3,35,144,3,6
	.byte	'EVRTRIM',0
	.word	52241
	.byte	4,3,35,152,3,6
	.byte	'EVRADCSTAT',0
	.word	51086
	.byte	4,3,35,156,3,6
	.byte	'EVRUVMON',0
	.word	52306
	.byte	4,3,35,160,3,6
	.byte	'EVROVMON',0
	.word	51357
	.byte	4,3,35,164,3,6
	.byte	'EVRMONCTRL',0
	.word	51221
	.byte	4,3,35,168,3,6
	.byte	'reserved_1AC',0
	.word	5319
	.byte	4,3,35,172,3,6
	.byte	'EVRSDCTRL1',0
	.word	51904
	.byte	4,3,35,176,3,6
	.byte	'EVRSDCTRL2',0
	.word	51972
	.byte	4,3,35,180,3,6
	.byte	'EVRSDCTRL3',0
	.word	52040
	.byte	4,3,35,184,3,6
	.byte	'EVRSDCTRL4',0
	.word	52108
	.byte	4,3,35,188,3,6
	.byte	'EVRSDCOEFF1',0
	.word	51490
	.byte	4,3,35,192,3,6
	.byte	'EVRSDCOEFF2',0
	.word	51559
	.byte	4,3,35,196,3,6
	.byte	'EVRSDCOEFF3',0
	.word	51628
	.byte	4,3,35,200,3,6
	.byte	'EVRSDCOEFF4',0
	.word	51697
	.byte	4,3,35,204,3,6
	.byte	'EVRSDCOEFF5',0
	.word	51766
	.byte	4,3,35,208,3,6
	.byte	'EVRSDCOEFF6',0
	.word	51835
	.byte	4,3,35,212,3,6
	.byte	'EVROSCCTRL',0
	.word	51289
	.byte	4,3,35,216,3,6
	.byte	'reserved_1DC',0
	.word	5319
	.byte	4,3,35,220,3,6
	.byte	'OVCENABLE',0
	.word	53448
	.byte	4,3,35,224,3,6
	.byte	'OVCCON',0
	.word	53384
	.byte	4,3,35,228,3,6
	.byte	'reserved_1E8',0
	.word	25899
	.byte	40,3,35,232,3,6
	.byte	'EICR',0
	.word	55731
	.byte	16,3,35,144,4,6
	.byte	'EIFR',0
	.word	50701
	.byte	4,3,35,160,4,6
	.byte	'FMR',0
	.word	52497
	.byte	4,3,35,164,4,6
	.byte	'PDRR',0
	.word	53639
	.byte	4,3,35,168,4,6
	.byte	'IGCR',0
	.word	55740
	.byte	16,3,35,172,4,6
	.byte	'reserved_23C',0
	.word	5319
	.byte	4,3,35,188,4,6
	.byte	'DTSLIM',0
	.word	50510
	.byte	4,3,35,192,4,6
	.byte	'reserved_244',0
	.word	55749
	.byte	180,3,3,35,196,4,6
	.byte	'ACCEN1',0
	.word	49733
	.byte	4,3,35,248,7,6
	.byte	'ACCEN0',0
	.word	49669
	.byte	4,3,35,252,7,0,21
	.word	55760
	.byte	9
	.byte	'Ifx_SCU',0,8,181,16,3
	.word	57750
	.byte	15
	.byte	'_Ifx_CPU_A_Bits',0,25,45,16,4,4
	.byte	'ADDR',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_A_Bits',0,25,48,3
	.word	57772
	.byte	15
	.byte	'_Ifx_CPU_BIV_Bits',0,25,51,16,4,4
	.byte	'VSS',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'BIV',0,4
	.word	3023
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_BIV_Bits',0,25,55,3
	.word	57833
	.byte	15
	.byte	'_Ifx_CPU_BTV_Bits',0,25,58,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'BTV',0,4
	.word	3023
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_BTV_Bits',0,25,62,3
	.word	57912
	.byte	15
	.byte	'_Ifx_CPU_CCNT_Bits',0,25,65,16,4,4
	.byte	'CountValue',0,4
	.word	3023
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_CCNT_Bits',0,25,69,3
	.word	57998
	.byte	15
	.byte	'_Ifx_CPU_CCTRL_Bits',0,25,72,16,4,4
	.byte	'CM',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'CE',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'M1',0,4
	.word	3023
	.byte	3,27,2,35,0,4
	.byte	'M2',0,4
	.word	3023
	.byte	3,24,2,35,0,4
	.byte	'M3',0,4
	.word	3023
	.byte	3,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3023
	.byte	21,0,2,35,0,0,9
	.byte	'Ifx_CPU_CCTRL_Bits',0,25,80,3
	.word	58087
	.byte	15
	.byte	'_Ifx_CPU_COMPAT_Bits',0,25,83,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'RM',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'SP',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3023
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_COMPAT_Bits',0,25,89,3
	.word	58233
	.byte	15
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,25,92,16,4,4
	.byte	'CORE_ID',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CORE_ID_Bits',0,25,96,3
	.word	58360
	.byte	15
	.byte	'_Ifx_CPU_CPR_L_Bits',0,25,99,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'LOWBND',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_L_Bits',0,25,103,3
	.word	58458
	.byte	15
	.byte	'_Ifx_CPU_CPR_U_Bits',0,25,106,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'UPPBND',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_U_Bits',0,25,110,3
	.word	58551
	.byte	15
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,25,113,16,4,4
	.byte	'MODREV',0,4
	.word	3023
	.byte	8,24,2,35,0,4
	.byte	'MOD_32B',0,4
	.word	3023
	.byte	8,16,2,35,0,4
	.byte	'MOD',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPU_ID_Bits',0,25,118,3
	.word	58644
	.byte	15
	.byte	'_Ifx_CPU_CPXE_Bits',0,25,121,16,4,4
	.byte	'XE',0,4
	.word	3023
	.byte	8,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_CPXE_Bits',0,25,125,3
	.word	58751
	.byte	15
	.byte	'_Ifx_CPU_CREVT_Bits',0,25,128,1,16,4,4
	.byte	'EVTA',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3023
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_CREVT_Bits',0,25,136,1,3
	.word	58838
	.byte	15
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,25,139,1,16,4,4
	.byte	'CID',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_CUS_ID_Bits',0,25,143,1,3
	.word	58992
	.byte	15
	.byte	'_Ifx_CPU_D_Bits',0,25,146,1,16,4,4
	.byte	'DATA',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_D_Bits',0,25,149,1,3
	.word	59086
	.byte	15
	.byte	'_Ifx_CPU_DATR_Bits',0,25,152,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'SBE',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'reserved_4',0,4
	.word	3023
	.byte	5,23,2,35,0,4
	.byte	'CWE',0,4
	.word	3023
	.byte	1,22,2,35,0,4
	.byte	'CFE',0,4
	.word	3023
	.byte	1,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3023
	.byte	3,18,2,35,0,4
	.byte	'SOE',0,4
	.word	3023
	.byte	1,17,2,35,0,4
	.byte	'SME',0,4
	.word	3023
	.byte	1,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DATR_Bits',0,25,163,1,3
	.word	59149
	.byte	15
	.byte	'_Ifx_CPU_DBGSR_Bits',0,25,166,1,16,4,4
	.byte	'DE',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'HALT',0,4
	.word	3023
	.byte	2,29,2,35,0,4
	.byte	'SIH',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'SUSP',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'PREVSUSP',0,4
	.word	3023
	.byte	1,25,2,35,0,4
	.byte	'PEVT',0,4
	.word	3023
	.byte	1,24,2,35,0,4
	.byte	'EVTSRC',0,4
	.word	3023
	.byte	5,19,2,35,0,4
	.byte	'reserved_13',0,4
	.word	3023
	.byte	19,0,2,35,0,0,9
	.byte	'Ifx_CPU_DBGSR_Bits',0,25,177,1,3
	.word	59367
	.byte	15
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,25,180,1,16,4,4
	.byte	'DTA',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3023
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_DBGTCR_Bits',0,25,184,1,3
	.word	59582
	.byte	15
	.byte	'_Ifx_CPU_DCON0_Bits',0,25,187,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'DCBYP',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3023
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCON0_Bits',0,25,192,1,3
	.word	59676
	.byte	15
	.byte	'_Ifx_CPU_DCON2_Bits',0,25,195,1,16,4,4
	.byte	'DCACHE_SZE',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'DSCRATCH_SZE',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCON2_Bits',0,25,199,1,3
	.word	59792
	.byte	15
	.byte	'_Ifx_CPU_DCX_Bits',0,25,202,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	6,26,2,35,0,4
	.byte	'DCXValue',0,4
	.word	3023
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_CPU_DCX_Bits',0,25,206,1,3
	.word	59893
	.byte	15
	.byte	'_Ifx_CPU_DEADD_Bits',0,25,209,1,16,4,4
	.byte	'ERROR_ADDRESS',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_DEADD_Bits',0,25,212,1,3
	.word	59986
	.byte	15
	.byte	'_Ifx_CPU_DIEAR_Bits',0,25,215,1,16,4,4
	.byte	'TA',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_DIEAR_Bits',0,25,218,1,3
	.word	60066
	.byte	15
	.byte	'_Ifx_CPU_DIETR_Bits',0,25,221,1,16,4,4
	.byte	'IED',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'IE_T',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'IE_C',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'IE_S',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'IE_BI',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'E_INFO',0,4
	.word	3023
	.byte	6,21,2,35,0,4
	.byte	'IE_DUAL',0,4
	.word	3023
	.byte	1,20,2,35,0,4
	.byte	'IE_SP',0,4
	.word	3023
	.byte	1,19,2,35,0,4
	.byte	'IE_BS',0,4
	.word	3023
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3023
	.byte	18,0,2,35,0,0,9
	.byte	'Ifx_CPU_DIETR_Bits',0,25,233,1,3
	.word	60135
	.byte	15
	.byte	'_Ifx_CPU_DMS_Bits',0,25,236,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'DMSValue',0,4
	.word	3023
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_DMS_Bits',0,25,240,1,3
	.word	60364
	.byte	15
	.byte	'_Ifx_CPU_DPR_L_Bits',0,25,243,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'LOWBND',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_L_Bits',0,25,247,1,3
	.word	60457
	.byte	15
	.byte	'_Ifx_CPU_DPR_U_Bits',0,25,250,1,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'UPPBND',0,4
	.word	3023
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_U_Bits',0,25,254,1,3
	.word	60552
	.byte	15
	.byte	'_Ifx_CPU_DPRE_Bits',0,25,129,2,16,4,4
	.byte	'RE',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPRE_Bits',0,25,133,2,3
	.word	60647
	.byte	15
	.byte	'_Ifx_CPU_DPWE_Bits',0,25,136,2,16,4,4
	.byte	'WE',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_DPWE_Bits',0,25,140,2,3
	.word	60737
	.byte	15
	.byte	'_Ifx_CPU_DSTR_Bits',0,25,143,2,16,4,4
	.byte	'SRE',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'GAE',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'LBE',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	3,26,2,35,0,4
	.byte	'CRE',0,4
	.word	3023
	.byte	1,25,2,35,0,4
	.byte	'reserved_7',0,4
	.word	3023
	.byte	7,18,2,35,0,4
	.byte	'DTME',0,4
	.word	3023
	.byte	1,17,2,35,0,4
	.byte	'LOE',0,4
	.word	3023
	.byte	1,16,2,35,0,4
	.byte	'SDE',0,4
	.word	3023
	.byte	1,15,2,35,0,4
	.byte	'SCE',0,4
	.word	3023
	.byte	1,14,2,35,0,4
	.byte	'CAC',0,4
	.word	3023
	.byte	1,13,2,35,0,4
	.byte	'MPE',0,4
	.word	3023
	.byte	1,12,2,35,0,4
	.byte	'CLE',0,4
	.word	3023
	.byte	1,11,2,35,0,4
	.byte	'reserved_21',0,4
	.word	3023
	.byte	3,8,2,35,0,4
	.byte	'ALN',0,4
	.word	3023
	.byte	1,7,2,35,0,4
	.byte	'reserved_25',0,4
	.word	3023
	.byte	7,0,2,35,0,0,9
	.byte	'Ifx_CPU_DSTR_Bits',0,25,161,2,3
	.word	60827
	.byte	15
	.byte	'_Ifx_CPU_EXEVT_Bits',0,25,164,2,16,4,4
	.byte	'EVTA',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3023
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_EXEVT_Bits',0,25,172,2,3
	.word	61151
	.byte	15
	.byte	'_Ifx_CPU_FCX_Bits',0,25,175,2,16,4,4
	.byte	'FCXO',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'FCXS',0,4
	.word	3023
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3023
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_FCX_Bits',0,25,180,2,3
	.word	61305
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,25,183,2,16,4,4
	.byte	'TST',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'TCL',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3023
	.byte	6,24,2,35,0,4
	.byte	'RM',0,4
	.word	3023
	.byte	2,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3023
	.byte	8,14,2,35,0,4
	.byte	'FXE',0,4
	.word	3023
	.byte	1,13,2,35,0,4
	.byte	'FUE',0,4
	.word	3023
	.byte	1,12,2,35,0,4
	.byte	'FZE',0,4
	.word	3023
	.byte	1,11,2,35,0,4
	.byte	'FVE',0,4
	.word	3023
	.byte	1,10,2,35,0,4
	.byte	'FIE',0,4
	.word	3023
	.byte	1,9,2,35,0,4
	.byte	'reserved_23',0,4
	.word	3023
	.byte	3,6,2,35,0,4
	.byte	'FX',0,4
	.word	3023
	.byte	1,5,2,35,0,4
	.byte	'FU',0,4
	.word	3023
	.byte	1,4,2,35,0,4
	.byte	'FZ',0,4
	.word	3023
	.byte	1,3,2,35,0,4
	.byte	'FV',0,4
	.word	3023
	.byte	1,2,2,35,0,4
	.byte	'FI',0,4
	.word	3023
	.byte	1,1,2,35,0,4
	.byte	'reserved_31',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,25,202,2,3
	.word	61411
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,25,205,2,16,4,4
	.byte	'OPC',0,4
	.word	3023
	.byte	8,24,2,35,0,4
	.byte	'FMT',0,4
	.word	3023
	.byte	1,23,2,35,0,4
	.byte	'reserved_9',0,4
	.word	3023
	.byte	7,16,2,35,0,4
	.byte	'DREG',0,4
	.word	3023
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3023
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,25,212,2,3
	.word	61760
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,25,215,2,16,4,4
	.byte	'PC',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,25,218,2,3
	.word	61920
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,25,221,2,16,4,4
	.byte	'SRC1',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,25,224,2,3
	.word	62001
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,25,227,2,16,4,4
	.byte	'SRC2',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,25,230,2,3
	.word	62088
	.byte	15
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,25,233,2,16,4,4
	.byte	'SRC3',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,25,236,2,3
	.word	62175
	.byte	15
	.byte	'_Ifx_CPU_ICNT_Bits',0,25,239,2,16,4,4
	.byte	'CountValue',0,4
	.word	3023
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_ICNT_Bits',0,25,243,2,3
	.word	62262
	.byte	15
	.byte	'_Ifx_CPU_ICR_Bits',0,25,246,2,16,4,4
	.byte	'CCPN',0,4
	.word	3023
	.byte	10,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3023
	.byte	5,17,2,35,0,4
	.byte	'IE',0,4
	.word	3023
	.byte	1,16,2,35,0,4
	.byte	'PIPN',0,4
	.word	3023
	.byte	10,6,2,35,0,4
	.byte	'reserved_26',0,4
	.word	3023
	.byte	6,0,2,35,0,0,9
	.byte	'Ifx_CPU_ICR_Bits',0,25,253,2,3
	.word	62353
	.byte	15
	.byte	'_Ifx_CPU_ISP_Bits',0,25,128,3,16,4,4
	.byte	'ISP',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_ISP_Bits',0,25,131,3,3
	.word	62496
	.byte	15
	.byte	'_Ifx_CPU_LCX_Bits',0,25,134,3,16,4,4
	.byte	'LCXO',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'LCXS',0,4
	.word	3023
	.byte	4,12,2,35,0,4
	.byte	'reserved_20',0,4
	.word	3023
	.byte	12,0,2,35,0,0,9
	.byte	'Ifx_CPU_LCX_Bits',0,25,139,3,3
	.word	62562
	.byte	15
	.byte	'_Ifx_CPU_M1CNT_Bits',0,25,142,3,16,4,4
	.byte	'CountValue',0,4
	.word	3023
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M1CNT_Bits',0,25,146,3,3
	.word	62668
	.byte	15
	.byte	'_Ifx_CPU_M2CNT_Bits',0,25,149,3,16,4,4
	.byte	'CountValue',0,4
	.word	3023
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M2CNT_Bits',0,25,153,3,3
	.word	62761
	.byte	15
	.byte	'_Ifx_CPU_M3CNT_Bits',0,25,156,3,16,4,4
	.byte	'CountValue',0,4
	.word	3023
	.byte	31,1,2,35,0,4
	.byte	'SOvf',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_M3CNT_Bits',0,25,160,3,3
	.word	62854
	.byte	15
	.byte	'_Ifx_CPU_PC_Bits',0,25,163,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'PC',0,4
	.word	3023
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_CPU_PC_Bits',0,25,167,3,3
	.word	62947
	.byte	15
	.byte	'_Ifx_CPU_PCON0_Bits',0,25,170,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'PCBYP',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3023
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON0_Bits',0,25,175,3,3
	.word	63032
	.byte	15
	.byte	'_Ifx_CPU_PCON1_Bits',0,25,178,3,16,4,4
	.byte	'PCINV',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'PBINV',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'reserved_2',0,4
	.word	3023
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON1_Bits',0,25,183,3,3
	.word	63148
	.byte	15
	.byte	'_Ifx_CPU_PCON2_Bits',0,25,186,3,16,4,4
	.byte	'PCACHE_SZE',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'PSCRATCH_SZE',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCON2_Bits',0,25,190,3,3
	.word	63259
	.byte	15
	.byte	'_Ifx_CPU_PCXI_Bits',0,25,193,3,16,4,4
	.byte	'PCXO',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'PCXS',0,4
	.word	3023
	.byte	4,12,2,35,0,4
	.byte	'UL',0,4
	.word	3023
	.byte	1,11,2,35,0,4
	.byte	'PIE',0,4
	.word	3023
	.byte	1,10,2,35,0,4
	.byte	'PCPN',0,4
	.word	3023
	.byte	10,0,2,35,0,0,9
	.byte	'Ifx_CPU_PCXI_Bits',0,25,200,3,3
	.word	63360
	.byte	15
	.byte	'_Ifx_CPU_PIEAR_Bits',0,25,203,3,16,4,4
	.byte	'TA',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_PIEAR_Bits',0,25,206,3,3
	.word	63490
	.byte	15
	.byte	'_Ifx_CPU_PIETR_Bits',0,25,209,3,16,4,4
	.byte	'IED',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'IE_T',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'IE_C',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'IE_S',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'IE_BI',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'E_INFO',0,4
	.word	3023
	.byte	6,21,2,35,0,4
	.byte	'IE_DUAL',0,4
	.word	3023
	.byte	1,20,2,35,0,4
	.byte	'IE_SP',0,4
	.word	3023
	.byte	1,19,2,35,0,4
	.byte	'IE_BS',0,4
	.word	3023
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3023
	.byte	18,0,2,35,0,0,9
	.byte	'Ifx_CPU_PIETR_Bits',0,25,221,3,3
	.word	63559
	.byte	15
	.byte	'_Ifx_CPU_PMA0_Bits',0,25,224,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	13,19,2,35,0,4
	.byte	'DAC',0,4
	.word	3023
	.byte	3,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA0_Bits',0,25,229,3,3
	.word	63788
	.byte	15
	.byte	'_Ifx_CPU_PMA1_Bits',0,25,232,3,16,4,4
	.byte	'reserved_0',0,4
	.word	3023
	.byte	14,18,2,35,0,4
	.byte	'CAC',0,4
	.word	3023
	.byte	2,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA1_Bits',0,25,237,3,3
	.word	63901
	.byte	15
	.byte	'_Ifx_CPU_PMA2_Bits',0,25,240,3,16,4,4
	.byte	'PSI',0,4
	.word	3023
	.byte	16,16,2,35,0,4
	.byte	'reserved_16',0,4
	.word	3023
	.byte	16,0,2,35,0,0,9
	.byte	'Ifx_CPU_PMA2_Bits',0,25,244,3,3
	.word	64014
	.byte	15
	.byte	'_Ifx_CPU_PSTR_Bits',0,25,247,3,16,4,4
	.byte	'FRE',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'FBE',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	9,20,2,35,0,4
	.byte	'FPE',0,4
	.word	3023
	.byte	1,19,2,35,0,4
	.byte	'reserved_13',0,4
	.word	3023
	.byte	1,18,2,35,0,4
	.byte	'FME',0,4
	.word	3023
	.byte	1,17,2,35,0,4
	.byte	'reserved_15',0,4
	.word	3023
	.byte	17,0,2,35,0,0,9
	.byte	'Ifx_CPU_PSTR_Bits',0,25,129,4,3
	.word	64105
	.byte	15
	.byte	'_Ifx_CPU_PSW_Bits',0,25,132,4,16,4,4
	.byte	'CDC',0,4
	.word	3023
	.byte	7,25,2,35,0,4
	.byte	'CDE',0,4
	.word	3023
	.byte	1,24,2,35,0,4
	.byte	'GW',0,4
	.word	3023
	.byte	1,23,2,35,0,4
	.byte	'IS',0,4
	.word	3023
	.byte	1,22,2,35,0,4
	.byte	'IO',0,4
	.word	3023
	.byte	2,20,2,35,0,4
	.byte	'PRS',0,4
	.word	3023
	.byte	2,18,2,35,0,4
	.byte	'S',0,4
	.word	3023
	.byte	1,17,2,35,0,4
	.byte	'reserved_15',0,4
	.word	3023
	.byte	12,5,2,35,0,4
	.byte	'SAV',0,4
	.word	3023
	.byte	1,4,2,35,0,4
	.byte	'AV',0,4
	.word	3023
	.byte	1,3,2,35,0,4
	.byte	'SV',0,4
	.word	3023
	.byte	1,2,2,35,0,4
	.byte	'V',0,4
	.word	3023
	.byte	1,1,2,35,0,4
	.byte	'C',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_PSW_Bits',0,25,147,4,3
	.word	64308
	.byte	15
	.byte	'_Ifx_CPU_SEGEN_Bits',0,25,150,4,16,4,4
	.byte	'ADFLIP',0,4
	.word	3023
	.byte	8,24,2,35,0,4
	.byte	'ADTYPE',0,4
	.word	3023
	.byte	2,22,2,35,0,4
	.byte	'reserved_10',0,4
	.word	3023
	.byte	21,1,2,35,0,4
	.byte	'AE',0,4
	.word	3023
	.byte	1,0,2,35,0,0,9
	.byte	'Ifx_CPU_SEGEN_Bits',0,25,156,4,3
	.word	64551
	.byte	15
	.byte	'_Ifx_CPU_SMACON_Bits',0,25,159,4,16,4,4
	.byte	'PC',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'reserved_1',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'PT',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	5,24,2,35,0,4
	.byte	'DC',0,4
	.word	3023
	.byte	1,23,2,35,0,4
	.byte	'reserved_9',0,4
	.word	3023
	.byte	1,22,2,35,0,4
	.byte	'DT',0,4
	.word	3023
	.byte	1,21,2,35,0,4
	.byte	'reserved_11',0,4
	.word	3023
	.byte	13,8,2,35,0,4
	.byte	'IODT',0,4
	.word	3023
	.byte	1,7,2,35,0,4
	.byte	'reserved_25',0,4
	.word	3023
	.byte	7,0,2,35,0,0,9
	.byte	'Ifx_CPU_SMACON_Bits',0,25,171,4,3
	.word	64679
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,25,174,4,16,4,4
	.byte	'EN',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,25,177,4,3
	.word	64920
	.byte	15
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,25,180,4,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,25,183,4,3
	.word	65003
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,25,186,4,16,4,4
	.byte	'EN',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,25,189,4,3
	.word	65094
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,25,192,4,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,25,195,4,3
	.word	65185
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,25,198,4,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2618
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,25,202,4,3
	.word	65284
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,25,205,4,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2618
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,25,209,4,3
	.word	65391
	.byte	15
	.byte	'_Ifx_CPU_SWEVT_Bits',0,25,212,4,16,4,4
	.byte	'EVTA',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3023
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_SWEVT_Bits',0,25,220,4,3
	.word	65498
	.byte	15
	.byte	'_Ifx_CPU_SYSCON_Bits',0,25,223,4,16,4,4
	.byte	'FCDSF',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'PROTEN',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'TPROTEN',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'IS',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'IT',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3023
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_SYSCON_Bits',0,25,231,4,3
	.word	65652
	.byte	15
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,25,234,4,16,4,4
	.byte	'ASI',0,4
	.word	3023
	.byte	5,27,2,35,0,4
	.byte	'reserved_5',0,4
	.word	3023
	.byte	27,0,2,35,0,0,9
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,25,238,4,3
	.word	65813
	.byte	15
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,25,241,4,16,4,4
	.byte	'TEXP0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'TEXP1',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'TEXP2',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'reserved_3',0,4
	.word	3023
	.byte	13,16,2,35,0,4
	.byte	'TTRAP',0,4
	.word	3023
	.byte	1,15,2,35,0,4
	.byte	'reserved_17',0,4
	.word	3023
	.byte	15,0,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_CON_Bits',0,25,249,4,3
	.word	65911
	.byte	15
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,25,252,4,16,4,4
	.byte	'Timer',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,25,255,4,3
	.word	66083
	.byte	15
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,25,130,5,16,4,4
	.byte	'ADDR',0,4
	.word	3023
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_CPU_TR_ADR_Bits',0,25,133,5,3
	.word	66163
	.byte	15
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,25,136,5,16,4,4
	.byte	'EVTA',0,4
	.word	3023
	.byte	3,29,2,35,0,4
	.byte	'BBM',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'BOD',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'SUSP',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'CNT',0,4
	.word	3023
	.byte	2,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	4,20,2,35,0,4
	.byte	'TYP',0,4
	.word	3023
	.byte	1,19,2,35,0,4
	.byte	'RNG',0,4
	.word	3023
	.byte	1,18,2,35,0,4
	.byte	'reserved_14',0,4
	.word	3023
	.byte	1,17,2,35,0,4
	.byte	'ASI_EN',0,4
	.word	3023
	.byte	1,16,2,35,0,4
	.byte	'ASI',0,4
	.word	3023
	.byte	5,11,2,35,0,4
	.byte	'reserved_21',0,4
	.word	3023
	.byte	6,5,2,35,0,4
	.byte	'AST',0,4
	.word	3023
	.byte	1,4,2,35,0,4
	.byte	'ALD',0,4
	.word	3023
	.byte	1,3,2,35,0,4
	.byte	'reserved_29',0,4
	.word	3023
	.byte	3,0,2,35,0,0,9
	.byte	'Ifx_CPU_TR_EVT_Bits',0,25,153,5,3
	.word	66236
	.byte	15
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,25,156,5,16,4,4
	.byte	'T0',0,4
	.word	3023
	.byte	1,31,2,35,0,4
	.byte	'T1',0,4
	.word	3023
	.byte	1,30,2,35,0,4
	.byte	'T2',0,4
	.word	3023
	.byte	1,29,2,35,0,4
	.byte	'T3',0,4
	.word	3023
	.byte	1,28,2,35,0,4
	.byte	'T4',0,4
	.word	3023
	.byte	1,27,2,35,0,4
	.byte	'T5',0,4
	.word	3023
	.byte	1,26,2,35,0,4
	.byte	'T6',0,4
	.word	3023
	.byte	1,25,2,35,0,4
	.byte	'T7',0,4
	.word	3023
	.byte	1,24,2,35,0,4
	.byte	'reserved_8',0,4
	.word	3023
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,25,167,5,3
	.word	66554
	.byte	5,25,175,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	57772
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_A',0,25,180,5,3
	.word	66749
	.byte	5,25,183,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	57833
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_BIV',0,25,188,5,3
	.word	66808
	.byte	5,25,191,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	57912
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_BTV',0,25,196,5,3
	.word	66869
	.byte	5,25,199,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	57998
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CCNT',0,25,204,5,3
	.word	66930
	.byte	5,25,207,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58087
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CCTRL',0,25,212,5,3
	.word	66992
	.byte	5,25,215,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58233
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_COMPAT',0,25,220,5,3
	.word	67055
	.byte	5,25,223,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58360
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CORE_ID',0,25,228,5,3
	.word	67119
	.byte	5,25,231,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58458
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_L',0,25,236,5,3
	.word	67184
	.byte	5,25,239,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58551
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPR_U',0,25,244,5,3
	.word	67247
	.byte	5,25,247,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58644
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPU_ID',0,25,252,5,3
	.word	67310
	.byte	5,25,255,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58751
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CPXE',0,25,132,6,3
	.word	67374
	.byte	5,25,135,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58838
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CREVT',0,25,140,6,3
	.word	67436
	.byte	5,25,143,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	58992
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_CUS_ID',0,25,148,6,3
	.word	67499
	.byte	5,25,151,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59086
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_D',0,25,156,6,3
	.word	67563
	.byte	5,25,159,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59149
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DATR',0,25,164,6,3
	.word	67622
	.byte	5,25,167,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59367
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DBGSR',0,25,172,6,3
	.word	67684
	.byte	5,25,175,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59582
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DBGTCR',0,25,180,6,3
	.word	67747
	.byte	5,25,183,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59676
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCON0',0,25,188,6,3
	.word	67811
	.byte	5,25,191,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59792
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCON2',0,25,196,6,3
	.word	67874
	.byte	5,25,199,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59893
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DCX',0,25,204,6,3
	.word	67937
	.byte	5,25,207,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	59986
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DEADD',0,25,212,6,3
	.word	67998
	.byte	5,25,215,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60066
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DIEAR',0,25,220,6,3
	.word	68061
	.byte	5,25,223,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60135
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DIETR',0,25,228,6,3
	.word	68124
	.byte	5,25,231,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60364
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DMS',0,25,236,6,3
	.word	68187
	.byte	5,25,239,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60457
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_L',0,25,244,6,3
	.word	68248
	.byte	5,25,247,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60552
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPR_U',0,25,252,6,3
	.word	68311
	.byte	5,25,255,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60647
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPRE',0,25,132,7,3
	.word	68374
	.byte	5,25,135,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60737
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DPWE',0,25,140,7,3
	.word	68436
	.byte	5,25,143,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	60827
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_DSTR',0,25,148,7,3
	.word	68498
	.byte	5,25,151,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61151
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_EXEVT',0,25,156,7,3
	.word	68560
	.byte	5,25,159,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61305
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FCX',0,25,164,7,3
	.word	68623
	.byte	5,25,167,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61411
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,25,172,7,3
	.word	68684
	.byte	5,25,175,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61760
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,25,180,7,3
	.word	68754
	.byte	5,25,183,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	61920
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,25,188,7,3
	.word	68824
	.byte	5,25,191,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62001
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,25,196,7,3
	.word	68893
	.byte	5,25,199,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62088
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,25,204,7,3
	.word	68964
	.byte	5,25,207,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62175
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,25,212,7,3
	.word	69035
	.byte	5,25,215,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62262
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ICNT',0,25,220,7,3
	.word	69106
	.byte	5,25,223,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62353
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ICR',0,25,228,7,3
	.word	69168
	.byte	5,25,231,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62496
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_ISP',0,25,236,7,3
	.word	69229
	.byte	5,25,239,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62562
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_LCX',0,25,244,7,3
	.word	69290
	.byte	5,25,247,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62668
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M1CNT',0,25,252,7,3
	.word	69351
	.byte	5,25,255,7,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62761
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M2CNT',0,25,132,8,3
	.word	69414
	.byte	5,25,135,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62854
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_M3CNT',0,25,140,8,3
	.word	69477
	.byte	5,25,143,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	62947
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PC',0,25,148,8,3
	.word	69540
	.byte	5,25,151,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63032
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON0',0,25,156,8,3
	.word	69600
	.byte	5,25,159,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63148
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON1',0,25,164,8,3
	.word	69663
	.byte	5,25,167,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63259
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCON2',0,25,172,8,3
	.word	69726
	.byte	5,25,175,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63360
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PCXI',0,25,180,8,3
	.word	69789
	.byte	5,25,183,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63490
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PIEAR',0,25,188,8,3
	.word	69851
	.byte	5,25,191,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63559
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PIETR',0,25,196,8,3
	.word	69914
	.byte	5,25,199,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63788
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA0',0,25,204,8,3
	.word	69977
	.byte	5,25,207,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	63901
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA1',0,25,212,8,3
	.word	70039
	.byte	5,25,215,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64014
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PMA2',0,25,220,8,3
	.word	70101
	.byte	5,25,223,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64105
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PSTR',0,25,228,8,3
	.word	70163
	.byte	5,25,231,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64308
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_PSW',0,25,236,8,3
	.word	70225
	.byte	5,25,239,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64551
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SEGEN',0,25,244,8,3
	.word	70286
	.byte	5,25,247,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64679
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SMACON',0,25,252,8,3
	.word	70349
	.byte	5,25,255,8,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	64920
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENA',0,25,132,9,3
	.word	70413
	.byte	5,25,135,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65003
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_ACCENB',0,25,140,9,3
	.word	70483
	.byte	5,25,143,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65094
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,25,148,9,3
	.word	70553
	.byte	5,25,151,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65185
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,25,156,9,3
	.word	70627
	.byte	5,25,159,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65284
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,25,164,9,3
	.word	70701
	.byte	5,25,167,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65391
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,25,172,9,3
	.word	70771
	.byte	5,25,175,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65498
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SWEVT',0,25,180,9,3
	.word	70841
	.byte	5,25,183,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65652
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_SYSCON',0,25,188,9,3
	.word	70904
	.byte	5,25,191,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65813
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TASK_ASI',0,25,196,9,3
	.word	70968
	.byte	5,25,199,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	65911
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_CON',0,25,204,9,3
	.word	71034
	.byte	5,25,207,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66083
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TPS_TIMER',0,25,212,9,3
	.word	71099
	.byte	5,25,215,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66163
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TR_ADR',0,25,220,9,3
	.word	71166
	.byte	5,25,223,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66236
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TR_EVT',0,25,228,9,3
	.word	71230
	.byte	5,25,231,9,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	66554
	.byte	4,2,35,0,0,9
	.byte	'Ifx_CPU_TRIG_ACC',0,25,236,9,3
	.word	71294
	.byte	15
	.byte	'_Ifx_CPU_CPR',0,25,247,9,25,8,6
	.byte	'L',0
	.word	67184
	.byte	4,2,35,0,6
	.byte	'U',0
	.word	67247
	.byte	4,2,35,4,0,21
	.word	71360
	.byte	9
	.byte	'Ifx_CPU_CPR',0,25,251,9,3
	.word	71402
	.byte	15
	.byte	'_Ifx_CPU_DPR',0,25,254,9,25,8,6
	.byte	'L',0
	.word	68248
	.byte	4,2,35,0,6
	.byte	'U',0
	.word	68311
	.byte	4,2,35,4,0,21
	.word	71428
	.byte	9
	.byte	'Ifx_CPU_DPR',0,25,130,10,3
	.word	71470
	.byte	15
	.byte	'_Ifx_CPU_SPROT_RGN',0,25,133,10,25,16,6
	.byte	'LA',0
	.word	70701
	.byte	4,2,35,0,6
	.byte	'UA',0
	.word	70771
	.byte	4,2,35,4,6
	.byte	'ACCENA',0
	.word	70553
	.byte	4,2,35,8,6
	.byte	'ACCENB',0
	.word	70627
	.byte	4,2,35,12,0,21
	.word	71496
	.byte	9
	.byte	'Ifx_CPU_SPROT_RGN',0,25,139,10,3
	.word	71578
	.byte	23,12
	.word	71099
	.byte	24,2,0,15
	.byte	'_Ifx_CPU_TPS',0,25,142,10,25,16,6
	.byte	'CON',0
	.word	71034
	.byte	4,2,35,0,6
	.byte	'TIMER',0
	.word	71610
	.byte	12,2,35,4,0,21
	.word	71619
	.byte	9
	.byte	'Ifx_CPU_TPS',0,25,146,10,3
	.word	71667
	.byte	15
	.byte	'_Ifx_CPU_TR',0,25,149,10,25,8,6
	.byte	'EVT',0
	.word	71230
	.byte	4,2,35,0,6
	.byte	'ADR',0
	.word	71166
	.byte	4,2,35,4,0,21
	.word	71693
	.byte	9
	.byte	'Ifx_CPU_TR',0,25,153,10,3
	.word	71738
	.byte	23,176,32
	.word	226
	.byte	24,175,32,0,23,208,223,1
	.word	226
	.byte	24,207,223,1,0,23,248,1
	.word	226
	.byte	24,247,1,0,23,244,29
	.word	226
	.byte	24,243,29,0,23,188,3
	.word	226
	.byte	24,187,3,0,23,232,3
	.word	226
	.byte	24,231,3,0,23,252,23
	.word	226
	.byte	24,251,23,0,23,228,63
	.word	226
	.byte	24,227,63,0,23,128,1
	.word	71428
	.byte	24,15,0,21
	.word	71853
	.byte	23,128,31
	.word	226
	.byte	24,255,30,0,23,64
	.word	71360
	.byte	24,7,0,21
	.word	71879
	.byte	23,192,31
	.word	226
	.byte	24,191,31,0,23,16
	.word	67374
	.byte	24,3,0,23,16
	.word	68374
	.byte	24,3,0,23,16
	.word	68436
	.byte	24,3,0,23,208,7
	.word	226
	.byte	24,207,7,0,21
	.word	71619
	.byte	23,240,23
	.word	226
	.byte	24,239,23,0,23,64
	.word	71693
	.byte	24,7,0,21
	.word	71958
	.byte	23,192,23
	.word	226
	.byte	24,191,23,0,23,232,1
	.word	226
	.byte	24,231,1,0,23,180,1
	.word	226
	.byte	24,179,1,0,23,172,1
	.word	226
	.byte	24,171,1,0,23,64
	.word	67563
	.byte	24,15,0,23,64
	.word	226
	.byte	24,63,0,23,64
	.word	66749
	.byte	24,15,0,15
	.byte	'_Ifx_CPU',0,25,166,10,25,128,128,4,6
	.byte	'reserved_0',0
	.word	71763
	.byte	176,32,2,35,0,6
	.byte	'SEGEN',0
	.word	70286
	.byte	4,3,35,176,32,6
	.byte	'reserved_1034',0
	.word	71774
	.byte	208,223,1,3,35,180,32,6
	.byte	'TASK_ASI',0
	.word	70968
	.byte	4,4,35,132,128,2,6
	.byte	'reserved_8008',0
	.word	71787
	.byte	248,1,4,35,136,128,2,6
	.byte	'PMA0',0
	.word	69977
	.byte	4,4,35,128,130,2,6
	.byte	'PMA1',0
	.word	70039
	.byte	4,4,35,132,130,2,6
	.byte	'PMA2',0
	.word	70101
	.byte	4,4,35,136,130,2,6
	.byte	'reserved_810C',0
	.word	71798
	.byte	244,29,4,35,140,130,2,6
	.byte	'DCON2',0
	.word	67874
	.byte	4,4,35,128,160,2,6
	.byte	'reserved_9004',0
	.word	7138
	.byte	8,4,35,132,160,2,6
	.byte	'SMACON',0
	.word	70349
	.byte	4,4,35,140,160,2,6
	.byte	'DSTR',0
	.word	68498
	.byte	4,4,35,144,160,2,6
	.byte	'reserved_9014',0
	.word	5319
	.byte	4,4,35,148,160,2,6
	.byte	'DATR',0
	.word	67622
	.byte	4,4,35,152,160,2,6
	.byte	'DEADD',0
	.word	67998
	.byte	4,4,35,156,160,2,6
	.byte	'DIEAR',0
	.word	68061
	.byte	4,4,35,160,160,2,6
	.byte	'DIETR',0
	.word	68124
	.byte	4,4,35,164,160,2,6
	.byte	'reserved_9028',0
	.word	6509
	.byte	24,4,35,168,160,2,6
	.byte	'DCON0',0
	.word	67811
	.byte	4,4,35,192,160,2,6
	.byte	'reserved_9044',0
	.word	71809
	.byte	188,3,4,35,196,160,2,6
	.byte	'PSTR',0
	.word	70163
	.byte	4,4,35,128,164,2,6
	.byte	'PCON1',0
	.word	69663
	.byte	4,4,35,132,164,2,6
	.byte	'PCON2',0
	.word	69726
	.byte	4,4,35,136,164,2,6
	.byte	'PCON0',0
	.word	69600
	.byte	4,4,35,140,164,2,6
	.byte	'PIEAR',0
	.word	69851
	.byte	4,4,35,144,164,2,6
	.byte	'PIETR',0
	.word	69914
	.byte	4,4,35,148,164,2,6
	.byte	'reserved_9218',0
	.word	71820
	.byte	232,3,4,35,152,164,2,6
	.byte	'COMPAT',0
	.word	67055
	.byte	4,4,35,128,168,2,6
	.byte	'reserved_9404',0
	.word	71831
	.byte	252,23,4,35,132,168,2,6
	.byte	'FPU_TRAP_CON',0
	.word	68684
	.byte	4,4,35,128,192,2,6
	.byte	'FPU_TRAP_PC',0
	.word	68824
	.byte	4,4,35,132,192,2,6
	.byte	'FPU_TRAP_OPC',0
	.word	68754
	.byte	4,4,35,136,192,2,6
	.byte	'reserved_A00C',0
	.word	5319
	.byte	4,4,35,140,192,2,6
	.byte	'FPU_TRAP_SRC1',0
	.word	68893
	.byte	4,4,35,144,192,2,6
	.byte	'FPU_TRAP_SRC2',0
	.word	68964
	.byte	4,4,35,148,192,2,6
	.byte	'FPU_TRAP_SRC3',0
	.word	69035
	.byte	4,4,35,152,192,2,6
	.byte	'reserved_A01C',0
	.word	71842
	.byte	228,63,4,35,156,192,2,6
	.byte	'DPR',0
	.word	71863
	.byte	128,1,4,35,128,128,3,6
	.byte	'reserved_C080',0
	.word	71868
	.byte	128,31,4,35,128,129,3,6
	.byte	'CPR',0
	.word	71888
	.byte	64,4,35,128,160,3,6
	.byte	'reserved_D040',0
	.word	71893
	.byte	192,31,4,35,192,160,3,6
	.byte	'CPXE',0
	.word	71904
	.byte	16,4,35,128,192,3,6
	.byte	'DPRE',0
	.word	71913
	.byte	16,4,35,144,192,3,6
	.byte	'DPWE',0
	.word	71922
	.byte	16,4,35,160,192,3,6
	.byte	'reserved_E030',0
	.word	71931
	.byte	208,7,4,35,176,192,3,6
	.byte	'TPS',0
	.word	71942
	.byte	16,4,35,128,200,3,6
	.byte	'reserved_E410',0
	.word	71947
	.byte	240,23,4,35,144,200,3,6
	.byte	'TR',0
	.word	71967
	.byte	64,4,35,128,224,3,6
	.byte	'reserved_F040',0
	.word	71972
	.byte	192,23,4,35,192,224,3,6
	.byte	'CCTRL',0
	.word	66992
	.byte	4,4,35,128,248,3,6
	.byte	'CCNT',0
	.word	66930
	.byte	4,4,35,132,248,3,6
	.byte	'ICNT',0
	.word	69106
	.byte	4,4,35,136,248,3,6
	.byte	'M1CNT',0
	.word	69351
	.byte	4,4,35,140,248,3,6
	.byte	'M2CNT',0
	.word	69414
	.byte	4,4,35,144,248,3,6
	.byte	'M3CNT',0
	.word	69477
	.byte	4,4,35,148,248,3,6
	.byte	'reserved_FC18',0
	.word	71983
	.byte	232,1,4,35,152,248,3,6
	.byte	'DBGSR',0
	.word	67684
	.byte	4,4,35,128,250,3,6
	.byte	'reserved_FD04',0
	.word	5319
	.byte	4,4,35,132,250,3,6
	.byte	'EXEVT',0
	.word	68560
	.byte	4,4,35,136,250,3,6
	.byte	'CREVT',0
	.word	67436
	.byte	4,4,35,140,250,3,6
	.byte	'SWEVT',0
	.word	70841
	.byte	4,4,35,144,250,3,6
	.byte	'reserved_FD14',0
	.word	55722
	.byte	28,4,35,148,250,3,6
	.byte	'TRIG_ACC',0
	.word	71294
	.byte	4,4,35,176,250,3,6
	.byte	'reserved_FD34',0
	.word	7478
	.byte	12,4,35,180,250,3,6
	.byte	'DMS',0
	.word	68187
	.byte	4,4,35,192,250,3,6
	.byte	'DCX',0
	.word	67937
	.byte	4,4,35,196,250,3,6
	.byte	'DBGTCR',0
	.word	67747
	.byte	4,4,35,200,250,3,6
	.byte	'reserved_FD4C',0
	.word	71994
	.byte	180,1,4,35,204,250,3,6
	.byte	'PCXI',0
	.word	69789
	.byte	4,4,35,128,252,3,6
	.byte	'PSW',0
	.word	70225
	.byte	4,4,35,132,252,3,6
	.byte	'PC',0
	.word	69540
	.byte	4,4,35,136,252,3,6
	.byte	'reserved_FE0C',0
	.word	7138
	.byte	8,4,35,140,252,3,6
	.byte	'SYSCON',0
	.word	70904
	.byte	4,4,35,148,252,3,6
	.byte	'CPU_ID',0
	.word	67310
	.byte	4,4,35,152,252,3,6
	.byte	'CORE_ID',0
	.word	67119
	.byte	4,4,35,156,252,3,6
	.byte	'BIV',0
	.word	66808
	.byte	4,4,35,160,252,3,6
	.byte	'BTV',0
	.word	66869
	.byte	4,4,35,164,252,3,6
	.byte	'ISP',0
	.word	69229
	.byte	4,4,35,168,252,3,6
	.byte	'ICR',0
	.word	69168
	.byte	4,4,35,172,252,3,6
	.byte	'reserved_FE30',0
	.word	7138
	.byte	8,4,35,176,252,3,6
	.byte	'FCX',0
	.word	68623
	.byte	4,4,35,184,252,3,6
	.byte	'LCX',0
	.word	69290
	.byte	4,4,35,188,252,3,6
	.byte	'reserved_FE40',0
	.word	26264
	.byte	16,4,35,192,252,3,6
	.byte	'CUS_ID',0
	.word	67499
	.byte	4,4,35,208,252,3,6
	.byte	'reserved_FE54',0
	.word	72005
	.byte	172,1,4,35,212,252,3,6
	.byte	'D',0
	.word	72016
	.byte	64,4,35,128,254,3,6
	.byte	'reserved_FF40',0
	.word	72025
	.byte	64,4,35,192,254,3,6
	.byte	'A',0
	.word	72034
	.byte	64,4,35,128,255,3,6
	.byte	'reserved_FFC0',0
	.word	72025
	.byte	64,4,35,192,255,3,0,21
	.word	72043
	.byte	9
	.byte	'Ifx_CPU',0,25,130,11,3
	.word	73834
	.byte	13,10,127,9,1,14
	.byte	'IfxCpu_Id_0',0,0,14
	.byte	'IfxCpu_Id_1',0,1,14
	.byte	'IfxCpu_Id_none',0,2,0,9
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	73856
	.byte	9
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	3948
	.byte	15
	.byte	'_Ifx_STM_ACCEN0_Bits',0,26,45,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_STM_ACCEN0_Bits',0,26,79,3
	.word	73954
	.byte	15
	.byte	'_Ifx_STM_ACCEN1_Bits',0,26,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN1_Bits',0,26,85,3
	.word	74511
	.byte	15
	.byte	'_Ifx_STM_CAP_Bits',0,26,88,16,4,4
	.byte	'STMCAP63_32',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CAP_Bits',0,26,91,3
	.word	74588
	.byte	15
	.byte	'_Ifx_STM_CAPSV_Bits',0,26,94,16,4,4
	.byte	'STMCAP63_32',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CAPSV_Bits',0,26,97,3
	.word	74660
	.byte	15
	.byte	'_Ifx_STM_CLC_Bits',0,26,100,16,4,4
	.byte	'DISR',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'DISS',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EDIS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_STM_CLC_Bits',0,26,107,3
	.word	74736
	.byte	15
	.byte	'_Ifx_STM_CMCON_Bits',0,26,110,16,4,4
	.byte	'MSIZE0',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	226
	.byte	3,0,2,35,0,4
	.byte	'MSTART0',0,1
	.word	226
	.byte	5,3,2,35,1,4
	.byte	'reserved_13',0,1
	.word	226
	.byte	3,0,2,35,1,4
	.byte	'MSIZE1',0,1
	.word	226
	.byte	5,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	226
	.byte	3,0,2,35,2,4
	.byte	'MSTART1',0,1
	.word	226
	.byte	5,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	226
	.byte	3,0,2,35,3,0,9
	.byte	'Ifx_STM_CMCON_Bits',0,26,120,3
	.word	74877
	.byte	15
	.byte	'_Ifx_STM_CMP_Bits',0,26,123,16,4,4
	.byte	'CMPVAL',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_CMP_Bits',0,26,126,3
	.word	75095
	.byte	15
	.byte	'_Ifx_STM_ICR_Bits',0,26,129,1,16,4,4
	.byte	'CMP0EN',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'CMP0IR',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'CMP0OS',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'reserved_3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'CMP1EN',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'CMP1IR',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'CMP1OS',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'reserved_7',0,4
	.word	2618
	.byte	25,0,2,35,0,0,9
	.byte	'Ifx_STM_ICR_Bits',0,26,139,1,3
	.word	75162
	.byte	15
	.byte	'_Ifx_STM_ID_Bits',0,26,142,1,16,4,4
	.byte	'MODREV',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_STM_ID_Bits',0,26,147,1,3
	.word	75365
	.byte	15
	.byte	'_Ifx_STM_ISCR_Bits',0,26,150,1,16,4,4
	.byte	'CMP0IRR',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'CMP0IRS',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'CMP1IRR',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'CMP1IRS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_STM_ISCR_Bits',0,26,157,1,3
	.word	75472
	.byte	15
	.byte	'_Ifx_STM_KRST0_Bits',0,26,160,1,16,4,4
	.byte	'RST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'RSTSTAT',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,4
	.word	2618
	.byte	30,0,2,35,0,0,9
	.byte	'Ifx_STM_KRST0_Bits',0,26,165,1,3
	.word	75623
	.byte	15
	.byte	'_Ifx_STM_KRST1_Bits',0,26,168,1,16,4,4
	.byte	'RST',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_STM_KRST1_Bits',0,26,172,1,3
	.word	75734
	.byte	15
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,26,175,1,16,4,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_STM_KRSTCLR_Bits',0,26,179,1,3
	.word	75826
	.byte	15
	.byte	'_Ifx_STM_OCS_Bits',0,26,182,1,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	24,8,2,35,0,4
	.byte	'SUS',0,1
	.word	226
	.byte	4,4,2,35,3,4
	.byte	'SUS_P',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'SUSSTA',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'reserved_30',0,1
	.word	226
	.byte	2,0,2,35,3,0,9
	.byte	'Ifx_STM_OCS_Bits',0,26,189,1,3
	.word	75922
	.byte	15
	.byte	'_Ifx_STM_TIM0_Bits',0,26,192,1,16,4,4
	.byte	'STM31_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM0_Bits',0,26,195,1,3
	.word	76068
	.byte	15
	.byte	'_Ifx_STM_TIM0SV_Bits',0,26,198,1,16,4,4
	.byte	'STM31_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM0SV_Bits',0,26,201,1,3
	.word	76140
	.byte	15
	.byte	'_Ifx_STM_TIM1_Bits',0,26,204,1,16,4,4
	.byte	'STM35_4',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM1_Bits',0,26,207,1,3
	.word	76216
	.byte	15
	.byte	'_Ifx_STM_TIM2_Bits',0,26,210,1,16,4,4
	.byte	'STM39_8',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM2_Bits',0,26,213,1,3
	.word	76288
	.byte	15
	.byte	'_Ifx_STM_TIM3_Bits',0,26,216,1,16,4,4
	.byte	'STM43_12',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM3_Bits',0,26,219,1,3
	.word	76360
	.byte	15
	.byte	'_Ifx_STM_TIM4_Bits',0,26,222,1,16,4,4
	.byte	'STM47_16',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM4_Bits',0,26,225,1,3
	.word	76433
	.byte	15
	.byte	'_Ifx_STM_TIM5_Bits',0,26,228,1,16,4,4
	.byte	'STM51_20',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM5_Bits',0,26,231,1,3
	.word	76506
	.byte	15
	.byte	'_Ifx_STM_TIM6_Bits',0,26,234,1,16,4,4
	.byte	'STM63_32',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_STM_TIM6_Bits',0,26,237,1,3
	.word	76579
	.byte	5,26,245,1,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	73954
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN0',0,26,250,1,3
	.word	76652
	.byte	5,26,253,1,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	74511
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ACCEN1',0,26,130,2,3
	.word	76716
	.byte	5,26,133,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	74588
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CAP',0,26,138,2,3
	.word	76780
	.byte	5,26,141,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	74660
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CAPSV',0,26,146,2,3
	.word	76841
	.byte	5,26,149,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	74736
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CLC',0,26,154,2,3
	.word	76904
	.byte	5,26,157,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	74877
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CMCON',0,26,162,2,3
	.word	76965
	.byte	5,26,165,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75095
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_CMP',0,26,170,2,3
	.word	77028
	.byte	5,26,173,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75162
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ICR',0,26,178,2,3
	.word	77089
	.byte	5,26,181,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75365
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ID',0,26,186,2,3
	.word	77150
	.byte	5,26,189,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75472
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_ISCR',0,26,194,2,3
	.word	77210
	.byte	5,26,197,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75623
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRST0',0,26,202,2,3
	.word	77272
	.byte	5,26,205,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75734
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRST1',0,26,210,2,3
	.word	77335
	.byte	5,26,213,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75826
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_KRSTCLR',0,26,218,2,3
	.word	77398
	.byte	5,26,221,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	75922
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_OCS',0,26,226,2,3
	.word	77463
	.byte	5,26,229,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76068
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM0',0,26,234,2,3
	.word	77524
	.byte	5,26,237,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76140
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM0SV',0,26,242,2,3
	.word	77586
	.byte	5,26,245,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76216
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM1',0,26,250,2,3
	.word	77650
	.byte	5,26,253,2,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76288
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM2',0,26,130,3,3
	.word	77712
	.byte	5,26,133,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76360
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM3',0,26,138,3,3
	.word	77774
	.byte	5,26,141,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76433
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM4',0,26,146,3,3
	.word	77836
	.byte	5,26,149,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76506
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM5',0,26,154,3,3
	.word	77898
	.byte	5,26,157,3,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	76579
	.byte	4,2,35,0,0,9
	.byte	'Ifx_STM_TIM6',0,26,162,3,3
	.word	77960
	.byte	13,9,144,1,9,1,14
	.byte	'IfxCpu_CounterMode_normal',0,0,14
	.byte	'IfxCpu_CounterMode_task',0,1,0,9
	.byte	'IfxCpu_CounterMode',0,9,148,1,3
	.word	78022
	.byte	3,9,160,1,9,6,6
	.byte	'counter',0
	.word	205
	.byte	4,2,35,0,6
	.byte	'overlfow',0
	.word	226
	.byte	1,2,35,4,0,9
	.byte	'IfxCpu_Counter',0,9,164,1,3
	.word	78111
	.byte	3,9,172,1,9,32,6
	.byte	'instruction',0
	.word	78111
	.byte	6,2,35,0,6
	.byte	'clock',0
	.word	78111
	.byte	6,2,35,6,6
	.byte	'counter1',0
	.word	78111
	.byte	6,2,35,12,6
	.byte	'counter2',0
	.word	78111
	.byte	6,2,35,18,6
	.byte	'counter3',0
	.word	78111
	.byte	6,2,35,24,0,9
	.byte	'IfxCpu_Perf',0,9,179,1,3
	.word	78177
	.byte	15
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,27,45,16,4,4
	.byte	'EN0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'EN1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EN2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'EN3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'EN4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'EN5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'EN6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'EN7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'EN8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'EN9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'EN10',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'EN11',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'EN12',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'EN13',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'EN14',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'EN15',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'EN16',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'EN17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'EN18',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'EN19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'EN20',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'EN21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'EN22',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'EN23',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'EN24',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'EN25',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EN26',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'EN27',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'EN28',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'EN29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'EN30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EN31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,27,79,3
	.word	78295
	.byte	15
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,27,82,16,4,4
	.byte	'reserved_0',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,27,85,3
	.word	78856
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,27,88,16,4,4
	.byte	'SEL',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'DIS',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2618
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,27,95,3
	.word	78937
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,27,98,16,4,4
	.byte	'VLD0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'VLD1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'VLD2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'VLD3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'VLD4',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'VLD5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'VLD6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'VLD7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'VLD8',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'VLD9',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2618
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,27,111,3
	.word	79090
	.byte	15
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,27,114,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2618
	.byte	19,8,2,35,0,4
	.byte	'ERR',0,1
	.word	226
	.byte	6,2,2,35,3,4
	.byte	'VLD',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,27,121,3
	.word	79338
	.byte	15
	.byte	'_Ifx_FLASH_COMM0_Bits',0,27,124,16,4,4
	.byte	'STATUS',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'reserved_8',0,4
	.word	2618
	.byte	24,0,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM0_Bits',0,27,128,1,3
	.word	79484
	.byte	15
	.byte	'_Ifx_FLASH_COMM1_Bits',0,27,131,1,16,4,4
	.byte	'STATUS',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'DATA',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_COMM1_Bits',0,27,136,1,3
	.word	79582
	.byte	15
	.byte	'_Ifx_FLASH_COMM2_Bits',0,27,139,1,16,4,4
	.byte	'STATUS',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'DATA',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_COMM2_Bits',0,27,144,1,3
	.word	79698
	.byte	15
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,27,147,1,16,4,4
	.byte	'RCODE',0,4
	.word	2618
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	887
	.byte	8,2,2,35,2,4
	.byte	'EDCERRINJ',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'ECCORDIS',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCRD_Bits',0,27,153,1,3
	.word	79814
	.byte	15
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,27,156,1,16,4,4
	.byte	'RCODE',0,4
	.word	2618
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	887
	.byte	8,2,2,35,2,4
	.byte	'EDCERRINJ',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'ECCORDIS',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCRP_Bits',0,27,162,1,3
	.word	79954
	.byte	15
	.byte	'_Ifx_FLASH_ECCW_Bits',0,27,165,1,16,4,4
	.byte	'WCODE',0,4
	.word	2618
	.byte	22,10,2,35,0,4
	.byte	'reserved_22',0,2
	.word	887
	.byte	8,2,2,35,2,4
	.byte	'DECENCDIS',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'PECENCDIS',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_ECCW_Bits',0,27,171,1,3
	.word	80094
	.byte	15
	.byte	'_Ifx_FLASH_FCON_Bits',0,27,174,1,16,4,4
	.byte	'WSPFLASH',0,1
	.word	226
	.byte	4,4,2,35,0,4
	.byte	'WSECPF',0,1
	.word	226
	.byte	2,2,2,35,0,4
	.byte	'WSDFLASH',0,2
	.word	887
	.byte	6,4,2,35,0,4
	.byte	'WSECDF',0,1
	.word	226
	.byte	3,1,2,35,1,4
	.byte	'IDLE',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'ESLDIS',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'SLEEP',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'NSAFECC',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'STALL',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'RES21',0,1
	.word	226
	.byte	2,2,2,35,2,4
	.byte	'RES23',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'VOPERM',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'SQERM',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'PROERM',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	226
	.byte	3,2,2,35,3,4
	.byte	'PR5V',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'EOBM',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FCON_Bits',0,27,193,1,3
	.word	80233
	.byte	15
	.byte	'_Ifx_FLASH_FPRO_Bits',0,27,196,1,16,4,4
	.byte	'PROINP',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'PRODISP',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'PROIND',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'PRODISD',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'PROINHSMCOTP',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'RES5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'PROINOTP',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'RES7',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'PROINDBG',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PRODISDBG',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'PROINHSM',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'reserved_11',0,1
	.word	226
	.byte	5,0,2,35,1,4
	.byte	'DCFP',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'DDFP',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'DDFPX',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'reserved_19',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'DDFD',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'reserved_21',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'ENPE',0,1
	.word	226
	.byte	2,0,2,35,2,4
	.byte	'reserved_24',0,1
	.word	226
	.byte	8,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FPRO_Bits',0,27,218,1,3
	.word	80595
	.byte	15
	.byte	'_Ifx_FLASH_FSR_Bits',0,27,221,1,16,4,4
	.byte	'FABUSY',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'D0BUSY',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'RES1',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'P0BUSY',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'P1BUSY',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'RES5',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'RES6',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'PROG',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'ERASE',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'PFPAGE',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'DFPAGE',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'OPER',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'SQER',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'PROER',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'PFSBER',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'PFDBER',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'PFMBER',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'RES17',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'DFSBER',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'DFDBER',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'DFTBER',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'DFMBER',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'SRIADDERR',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'reserved_23',0,2
	.word	887
	.byte	2,7,2,35,2,4
	.byte	'PVER',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'EVER',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'SPND',0,1
	.word	226
	.byte	1,4,2,35,3,4
	.byte	'SLM',0,1
	.word	226
	.byte	1,3,2,35,3,4
	.byte	'reserved_29',0,1
	.word	226
	.byte	1,2,2,35,3,4
	.byte	'ORIER',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'reserved_31',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_FSR_Bits',0,27,254,1,3
	.word	81036
	.byte	15
	.byte	'_Ifx_FLASH_ID_Bits',0,27,129,2,16,4,4
	.byte	'MODREV',0,1
	.word	226
	.byte	8,0,2,35,0,4
	.byte	'MODTYPE',0,1
	.word	226
	.byte	8,0,2,35,1,4
	.byte	'MODNUMBER',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_ID_Bits',0,27,134,2,3
	.word	81642
	.byte	15
	.byte	'_Ifx_FLASH_MARD_Bits',0,27,137,2,16,4,4
	.byte	'HMARGIN',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SELD0',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'reserved_2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'SPND',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'SPNDERR',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,2
	.word	887
	.byte	10,1,2,35,0,4
	.byte	'TRAPDIS',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_MARD_Bits',0,27,147,2,3
	.word	81753
	.byte	15
	.byte	'_Ifx_FLASH_MARP_Bits',0,27,150,2,16,4,4
	.byte	'SELP0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SELP1',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'RES2',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'RES3',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'reserved_4',0,2
	.word	887
	.byte	11,1,2,35,0,4
	.byte	'TRAPDIS',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_MARP_Bits',0,27,159,2,3
	.word	81967
	.byte	15
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,27,162,2,16,4,4
	.byte	'L',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'NSAFECC',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'RAMIN',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'RAMINSEL',0,1
	.word	226
	.byte	4,0,2,35,0,4
	.byte	'OSCCFG',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'MODE',0,1
	.word	226
	.byte	2,5,2,35,1,4
	.byte	'APREN',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'CAP0EN',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'CAP1EN',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'CAP2EN',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'CAP3EN',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'ESR0CNT',0,2
	.word	887
	.byte	12,4,2,35,2,4
	.byte	'RES29',0,1
	.word	226
	.byte	2,2,2,35,3,4
	.byte	'RES30',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'RPRO',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCOND_Bits',0,27,179,2,3
	.word	82154
	.byte	15
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,27,182,2,16,4,4
	.byte	'OCDSDIS',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'DBGIFLCK',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'EDM',0,1
	.word	226
	.byte	2,4,2,35,0,4
	.byte	'reserved_4',0,4
	.word	2618
	.byte	28,0,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,27,188,2,3
	.word	82478
	.byte	15
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,27,191,2,16,4,4
	.byte	'HSMDBGDIS',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'DBGIFLCK',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'TSTIFLCK',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'HSMTSTDIS',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'RES15',0,2
	.word	887
	.byte	12,0,2,35,0,4
	.byte	'reserved_16',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,27,199,2,3
	.word	82621
	.byte	15
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,27,202,2,16,4,4
	.byte	'HSMBOOTEN',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'SSWWAIT',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'HSMDX',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'HSM6X',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'HSM16X',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'HSM17X',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'S6ROM',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'HSMENPINS',0,2
	.word	887
	.byte	2,7,2,35,0,4
	.byte	'HSMENRES',0,1
	.word	226
	.byte	2,5,2,35,1,4
	.byte	'DESTDBG',0,1
	.word	226
	.byte	2,3,2,35,1,4
	.byte	'BLKFLAN',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'reserved_14',0,1
	.word	226
	.byte	2,0,2,35,1,4
	.byte	'S16ROM',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'S17ROM',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'reserved_18',0,2
	.word	887
	.byte	14,0,2,35,2,0,9
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,27,219,2,3
	.word	82810
	.byte	15
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,27,222,2,16,4,4
	.byte	'S0ROM',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'S1ROM',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'S2ROM',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'S3ROM',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'S4ROM',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'S5ROM',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'S6ROM',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'S7ROM',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'S8ROM',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'S9ROM',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'S10ROM',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'S11ROM',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'S12ROM',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'S13ROM',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'S14ROM',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'S15ROM',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'S16ROM',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'S17ROM',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'S18ROM',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'S19ROM',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'S20ROM',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'S21ROM',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'S22ROM',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'S23ROM',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'S24ROM',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'S25ROM',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'S26ROM',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	226
	.byte	2,3,2,35,3,4
	.byte	'BML',0,1
	.word	226
	.byte	2,1,2,35,3,4
	.byte	'TP',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,27,254,2,3
	.word	83173
	.byte	15
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,27,129,3,16,4,4
	.byte	'S0L',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'S1L',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'S2L',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'S3L',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'S4L',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'S5L',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'S6L',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'S7L',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'S8L',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'S9L',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'S10L',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'S11L',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'S12L',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'S13L',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'S14L',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'S15L',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'S16L',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'S17L',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'S18L',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'S19L',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'S20L',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'S21L',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'S22L',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'S23L',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'S24L',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'S25L',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'S26L',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	226
	.byte	4,1,2,35,3,4
	.byte	'RPRO',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONP_Bits',0,27,160,3,3
	.word	83768
	.byte	15
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,27,163,3,16,4,4
	.byte	'S0WOP',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'S1WOP',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'S2WOP',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'S3WOP',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'S4WOP',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'S5WOP',0,1
	.word	226
	.byte	1,2,2,35,0,4
	.byte	'S6WOP',0,1
	.word	226
	.byte	1,1,2,35,0,4
	.byte	'S7WOP',0,1
	.word	226
	.byte	1,0,2,35,0,4
	.byte	'S8WOP',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'S9WOP',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'S10WOP',0,1
	.word	226
	.byte	1,5,2,35,1,4
	.byte	'S11WOP',0,1
	.word	226
	.byte	1,4,2,35,1,4
	.byte	'S12WOP',0,1
	.word	226
	.byte	1,3,2,35,1,4
	.byte	'S13WOP',0,1
	.word	226
	.byte	1,2,2,35,1,4
	.byte	'S14WOP',0,1
	.word	226
	.byte	1,1,2,35,1,4
	.byte	'S15WOP',0,1
	.word	226
	.byte	1,0,2,35,1,4
	.byte	'S16WOP',0,1
	.word	226
	.byte	1,7,2,35,2,4
	.byte	'S17WOP',0,1
	.word	226
	.byte	1,6,2,35,2,4
	.byte	'S18WOP',0,1
	.word	226
	.byte	1,5,2,35,2,4
	.byte	'S19WOP',0,1
	.word	226
	.byte	1,4,2,35,2,4
	.byte	'S20WOP',0,1
	.word	226
	.byte	1,3,2,35,2,4
	.byte	'S21WOP',0,1
	.word	226
	.byte	1,2,2,35,2,4
	.byte	'S22WOP',0,1
	.word	226
	.byte	1,1,2,35,2,4
	.byte	'S23WOP',0,1
	.word	226
	.byte	1,0,2,35,2,4
	.byte	'S24WOP',0,1
	.word	226
	.byte	1,7,2,35,3,4
	.byte	'S25WOP',0,1
	.word	226
	.byte	1,6,2,35,3,4
	.byte	'S26WOP',0,1
	.word	226
	.byte	1,5,2,35,3,4
	.byte	'reserved_27',0,1
	.word	226
	.byte	4,1,2,35,3,4
	.byte	'DATM',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,27,194,3,3
	.word	84292
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,27,197,3,16,4,4
	.byte	'TAG',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,27,201,3,3
	.word	84874
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,27,204,3,16,4,4
	.byte	'TAG',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,27,208,3,3
	.word	84976
	.byte	15
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,27,211,3,16,4,4
	.byte	'TAG',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,4
	.word	2618
	.byte	26,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,27,215,3,3
	.word	85078
	.byte	15
	.byte	'_Ifx_FLASH_RRAD_Bits',0,27,218,3,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	3,5,2,35,0,4
	.byte	'ADD',0,4
	.word	2618
	.byte	29,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRAD_Bits',0,27,222,3,3
	.word	85180
	.byte	15
	.byte	'_Ifx_FLASH_RRCT_Bits',0,27,225,3,16,4,4
	.byte	'STRT',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'STP',0,1
	.word	226
	.byte	1,6,2,35,0,4
	.byte	'BUSY',0,1
	.word	226
	.byte	1,5,2,35,0,4
	.byte	'DONE',0,1
	.word	226
	.byte	1,4,2,35,0,4
	.byte	'ERR',0,1
	.word	226
	.byte	1,3,2,35,0,4
	.byte	'reserved_5',0,1
	.word	226
	.byte	3,0,2,35,0,4
	.byte	'EOBM',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'reserved_9',0,1
	.word	226
	.byte	7,0,2,35,1,4
	.byte	'CNT',0,2
	.word	887
	.byte	16,0,2,35,2,0,9
	.byte	'Ifx_FLASH_RRCT_Bits',0,27,236,3,3
	.word	85274
	.byte	15
	.byte	'_Ifx_FLASH_RRD0_Bits',0,27,239,3,16,4,4
	.byte	'DATA',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD0_Bits',0,27,242,3,3
	.word	85484
	.byte	15
	.byte	'_Ifx_FLASH_RRD1_Bits',0,27,245,3,16,4,4
	.byte	'DATA',0,4
	.word	2618
	.byte	32,0,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD1_Bits',0,27,248,3,3
	.word	85557
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,27,251,3,16,4,4
	.byte	'SEL',0,1
	.word	226
	.byte	6,2,2,35,0,4
	.byte	'reserved_6',0,1
	.word	226
	.byte	2,0,2,35,0,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,7,2,35,1,4
	.byte	'DIS',0,1
	.word	226
	.byte	1,6,2,35,1,4
	.byte	'reserved_10',0,4
	.word	2618
	.byte	22,0,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,27,130,4,3
	.word	85630
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,27,133,4,16,4,4
	.byte	'VLD0',0,1
	.word	226
	.byte	1,7,2,35,0,4
	.byte	'reserved_1',0,4
	.word	2618
	.byte	31,0,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,27,137,4,3
	.word	85785
	.byte	15
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,27,140,4,16,4,4
	.byte	'reserved_0',0,1
	.word	226
	.byte	5,3,2,35,0,4
	.byte	'ADDR',0,4
	.word	2618
	.byte	19,8,2,35,0,4
	.byte	'ERR',0,1
	.word	226
	.byte	6,2,2,35,3,4
	.byte	'VLD',0,1
	.word	226
	.byte	1,1,2,35,3,4
	.byte	'CLR',0,1
	.word	226
	.byte	1,0,2,35,3,0,9
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,27,147,4,3
	.word	85890
	.byte	5,27,155,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	78295
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN0',0,27,160,4,3
	.word	86038
	.byte	5,27,163,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	78856
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ACCEN1',0,27,168,4,3
	.word	86104
	.byte	5,27,171,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	78937
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_CFG',0,27,176,4,3
	.word	86170
	.byte	5,27,179,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79090
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_STAT',0,27,184,4,3
	.word	86238
	.byte	5,27,187,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79338
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_CBAB_TOP',0,27,192,4,3
	.word	86307
	.byte	5,27,195,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79484
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM0',0,27,200,4,3
	.word	86375
	.byte	5,27,203,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79582
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM1',0,27,208,4,3
	.word	86440
	.byte	5,27,211,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79698
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_COMM2',0,27,216,4,3
	.word	86505
	.byte	5,27,219,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79814
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCRD',0,27,224,4,3
	.word	86570
	.byte	5,27,227,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	79954
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCRP',0,27,232,4,3
	.word	86635
	.byte	5,27,235,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80094
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ECCW',0,27,240,4,3
	.word	86700
	.byte	5,27,243,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80233
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FCON',0,27,248,4,3
	.word	86764
	.byte	5,27,251,4,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	80595
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FPRO',0,27,128,5,3
	.word	86828
	.byte	5,27,131,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81036
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_FSR',0,27,136,5,3
	.word	86892
	.byte	5,27,139,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81642
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_ID',0,27,144,5,3
	.word	86955
	.byte	5,27,147,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81753
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_MARD',0,27,152,5,3
	.word	87017
	.byte	5,27,155,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	81967
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_MARP',0,27,160,5,3
	.word	87081
	.byte	5,27,163,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82154
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCOND',0,27,168,5,3
	.word	87145
	.byte	5,27,171,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82478
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONDBG',0,27,176,5,3
	.word	87212
	.byte	5,27,179,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82621
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONHSM',0,27,184,5,3
	.word	87281
	.byte	5,27,187,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	82810
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,27,192,5,3
	.word	87350
	.byte	5,27,195,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83173
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONOTP',0,27,200,5,3
	.word	87423
	.byte	5,27,203,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	83768
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONP',0,27,208,5,3
	.word	87492
	.byte	5,27,211,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	84292
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_PROCONWOP',0,27,216,5,3
	.word	87559
	.byte	5,27,219,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	84874
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG0',0,27,224,5,3
	.word	87628
	.byte	5,27,227,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	84976
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG1',0,27,232,5,3
	.word	87696
	.byte	5,27,235,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85078
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RDB_CFG2',0,27,240,5,3
	.word	87764
	.byte	5,27,243,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85180
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRAD',0,27,248,5,3
	.word	87832
	.byte	5,27,251,5,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85274
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRCT',0,27,128,6,3
	.word	87896
	.byte	5,27,131,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85484
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD0',0,27,136,6,3
	.word	87960
	.byte	5,27,139,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85557
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_RRD1',0,27,144,6,3
	.word	88024
	.byte	5,27,147,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85630
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_CFG',0,27,152,6,3
	.word	88088
	.byte	5,27,155,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85785
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_STAT',0,27,160,6,3
	.word	88156
	.byte	5,27,163,6,9,4,6
	.byte	'U',0
	.word	2618
	.byte	4,2,35,0,6
	.byte	'I',0
	.word	2634
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	85890
	.byte	4,2,35,0,0,9
	.byte	'Ifx_FLASH_UBAB_TOP',0,27,168,6,3
	.word	88225
	.byte	15
	.byte	'_Ifx_FLASH_CBAB',0,27,179,6,25,12,6
	.byte	'CFG',0
	.word	86170
	.byte	4,2,35,0,6
	.byte	'STAT',0
	.word	86238
	.byte	4,2,35,4,6
	.byte	'TOP',0
	.word	86307
	.byte	4,2,35,8,0,21
	.word	88293
	.byte	9
	.byte	'Ifx_FLASH_CBAB',0,27,184,6,3
	.word	88356
	.byte	15
	.byte	'_Ifx_FLASH_RDB',0,27,187,6,25,12,6
	.byte	'CFG0',0
	.word	87628
	.byte	4,2,35,0,6
	.byte	'CFG1',0
	.word	87696
	.byte	4,2,35,4,6
	.byte	'CFG2',0
	.word	87764
	.byte	4,2,35,8,0,21
	.word	88385
	.byte	9
	.byte	'Ifx_FLASH_RDB',0,27,192,6,3
	.word	88449
	.byte	15
	.byte	'_Ifx_FLASH_UBAB',0,27,195,6,25,12,6
	.byte	'CFG',0
	.word	88088
	.byte	4,2,35,0,6
	.byte	'STAT',0
	.word	88156
	.byte	4,2,35,4,6
	.byte	'TOP',0
	.word	88225
	.byte	4,2,35,8,0,21
	.word	88477
	.byte	9
	.byte	'Ifx_FLASH_UBAB',0,27,200,6,3
	.word	88540
	.byte	9
	.byte	'Ifx_P_ACCEN0_Bits',0,12,79,3
	.word	10891
	.byte	9
	.byte	'Ifx_P_ACCEN1_Bits',0,12,85,3
	.word	10804
	.byte	9
	.byte	'Ifx_P_ESR_Bits',0,12,107,3
	.word	7147
	.byte	9
	.byte	'Ifx_P_ID_Bits',0,12,115,3
	.word	5200
	.byte	9
	.byte	'Ifx_P_IN_Bits',0,12,137,1,3
	.word	6195
	.byte	9
	.byte	'Ifx_P_IOCR0_Bits',0,12,150,1,3
	.word	5328
	.byte	9
	.byte	'Ifx_P_IOCR12_Bits',0,12,163,1,3
	.word	5975
	.byte	9
	.byte	'Ifx_P_IOCR4_Bits',0,12,176,1,3
	.word	5543
	.byte	9
	.byte	'Ifx_P_IOCR8_Bits',0,12,189,1,3
	.word	5758
	.byte	9
	.byte	'Ifx_P_LPCR0_Bits',0,12,197,1,3
	.word	10163
	.byte	9
	.byte	'Ifx_P_LPCR1_Bits',0,12,205,1,3
	.word	10287
	.byte	9
	.byte	'Ifx_P_LPCR1_P21_Bits',0,12,215,1,3
	.word	10371
	.byte	9
	.byte	'Ifx_P_LPCR2_Bits',0,12,229,1,3
	.word	10551
	.byte	9
	.byte	'Ifx_P_OMCR0_Bits',0,12,240,1,3
	.word	8802
	.byte	9
	.byte	'Ifx_P_OMCR12_Bits',0,12,250,1,3
	.word	9326
	.byte	9
	.byte	'Ifx_P_OMCR4_Bits',0,12,133,2,3
	.word	8976
	.byte	9
	.byte	'Ifx_P_OMCR8_Bits',0,12,144,2,3
	.word	9150
	.byte	9
	.byte	'Ifx_P_OMCR_Bits',0,12,166,2,3
	.word	9815
	.byte	9
	.byte	'Ifx_P_OMR_Bits',0,12,203,2,3
	.word	4629
	.byte	9
	.byte	'Ifx_P_OMSR0_Bits',0,12,213,2,3
	.word	8139
	.byte	9
	.byte	'Ifx_P_OMSR12_Bits',0,12,224,2,3
	.word	8627
	.byte	9
	.byte	'Ifx_P_OMSR4_Bits',0,12,235,2,3
	.word	8286
	.byte	9
	.byte	'Ifx_P_OMSR8_Bits',0,12,246,2,3
	.word	8455
	.byte	9
	.byte	'Ifx_P_OMSR_Bits',0,12,140,3,3
	.word	9482
	.byte	9
	.byte	'Ifx_P_OUT_Bits',0,12,162,3,3
	.word	4313
	.byte	9
	.byte	'Ifx_P_PCSR_Bits',0,12,180,3,3
	.word	7853
	.byte	9
	.byte	'Ifx_P_PDISC_Bits',0,12,202,3,3
	.word	7487
	.byte	9
	.byte	'Ifx_P_PDR0_Bits',0,12,223,3,3
	.word	6518
	.byte	9
	.byte	'Ifx_P_PDR1_Bits',0,12,244,3,3
	.word	6822
	.byte	9
	.byte	'Ifx_P_ACCEN0',0,12,129,4,3
	.word	11418
	.byte	9
	.byte	'Ifx_P_ACCEN1',0,12,137,4,3
	.word	10851
	.byte	9
	.byte	'Ifx_P_ESR',0,12,145,4,3
	.word	7438
	.byte	9
	.byte	'Ifx_P_ID',0,12,153,4,3
	.word	5279
	.byte	9
	.byte	'Ifx_P_IN',0,12,161,4,3
	.word	6469
	.byte	9
	.byte	'Ifx_P_IOCR0',0,12,169,4,3
	.word	5503
	.byte	9
	.byte	'Ifx_P_IOCR12',0,12,177,4,3
	.word	6155
	.byte	9
	.byte	'Ifx_P_IOCR4',0,12,185,4,3
	.word	5718
	.byte	9
	.byte	'Ifx_P_IOCR8',0,12,193,4,3
	.word	5935
	.byte	9
	.byte	'Ifx_P_LPCR0',0,12,201,4,3
	.word	10247
	.byte	9
	.byte	'Ifx_P_LPCR1',0,12,210,4,3
	.word	10496
	.byte	9
	.byte	'Ifx_P_LPCR2',0,12,218,4,3
	.word	10755
	.byte	9
	.byte	'Ifx_P_OMCR',0,12,226,4,3
	.word	10123
	.byte	9
	.byte	'Ifx_P_OMCR0',0,12,234,4,3
	.word	8936
	.byte	9
	.byte	'Ifx_P_OMCR12',0,12,242,4,3
	.word	9442
	.byte	9
	.byte	'Ifx_P_OMCR4',0,12,250,4,3
	.word	9110
	.byte	9
	.byte	'Ifx_P_OMCR8',0,12,130,5,3
	.word	9286
	.byte	9
	.byte	'Ifx_P_OMR',0,12,138,5,3
	.word	5160
	.byte	9
	.byte	'Ifx_P_OMSR',0,12,146,5,3
	.word	9775
	.byte	9
	.byte	'Ifx_P_OMSR0',0,12,154,5,3
	.word	8246
	.byte	9
	.byte	'Ifx_P_OMSR12',0,12,162,5,3
	.word	8762
	.byte	9
	.byte	'Ifx_P_OMSR4',0,12,170,5,3
	.word	8415
	.byte	9
	.byte	'Ifx_P_OMSR8',0,12,178,5,3
	.word	8587
	.byte	9
	.byte	'Ifx_P_OUT',0,12,186,5,3
	.word	4589
	.byte	9
	.byte	'Ifx_P_PCSR',0,12,194,5,3
	.word	8099
	.byte	9
	.byte	'Ifx_P_PDISC',0,12,202,5,3
	.word	7813
	.byte	9
	.byte	'Ifx_P_PDR0',0,12,210,5,3
	.word	6782
	.byte	9
	.byte	'Ifx_P_PDR1',0,12,218,5,3
	.word	7098
	.byte	21
	.word	11458
	.byte	9
	.byte	'Ifx_P',0,12,139,6,3
	.word	89887
	.byte	9
	.byte	'IfxPort_InputMode',0,11,89,3
	.word	12071
	.byte	9
	.byte	'IfxPort_OutputIdx',0,11,130,1,3
	.word	12346
	.byte	9
	.byte	'IfxPort_OutputMode',0,11,138,1,3
	.word	12276
	.byte	9
	.byte	'IfxPort_PadDriver',0,11,158,1,3
	.word	21317
	.byte	9
	.byte	'IfxPort_State',0,11,178,1,3
	.word	12659
	.byte	9
	.byte	'IfxPort_Pin',0,11,194,1,3
	.word	21083
	.byte	9
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,28,148,1,16
	.word	2377
	.byte	3,28,212,5,9,8,6
	.byte	'value',0
	.word	205
	.byte	4,2,35,0,6
	.byte	'mask',0
	.word	205
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_CcuconRegConfig',0,28,216,5,3
	.word	90099
	.byte	3,28,221,5,9,8,6
	.byte	'pDivider',0
	.word	226
	.byte	1,2,35,0,6
	.byte	'nDivider',0
	.word	226
	.byte	1,2,35,1,6
	.byte	'k2Initial',0
	.word	226
	.byte	1,2,35,2,6
	.byte	'waitTime',0
	.word	495
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_InitialStepConfig',0,28,227,5,3
	.word	90170
	.byte	3,28,231,5,9,12,6
	.byte	'k2Step',0
	.word	226
	.byte	1,2,35,0,6
	.byte	'waitTime',0
	.word	495
	.byte	4,2,35,2,6
	.byte	'hookFunction',0
	.word	90059
	.byte	4,2,35,8,0,9
	.byte	'IfxScuCcu_PllStepsConfig',0,28,236,5,3
	.word	90287
	.byte	8
	.word	2374
	.byte	3,28,244,5,9,48,6
	.byte	'ccucon0',0
	.word	90099
	.byte	8,2,35,0,6
	.byte	'ccucon1',0
	.word	90099
	.byte	8,2,35,8,6
	.byte	'ccucon2',0
	.word	90099
	.byte	8,2,35,16,6
	.byte	'ccucon5',0
	.word	90099
	.byte	8,2,35,24,6
	.byte	'ccucon6',0
	.word	90099
	.byte	8,2,35,32,6
	.byte	'ccucon7',0
	.word	90099
	.byte	8,2,35,40,0,9
	.byte	'IfxScuCcu_ClockDistributionConfig',0,28,252,5,3
	.word	90389
	.byte	3,28,128,6,9,8,6
	.byte	'value',0
	.word	205
	.byte	4,2,35,0,6
	.byte	'mask',0
	.word	205
	.byte	4,2,35,4,0,9
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,28,132,6,3
	.word	90541
	.byte	8
	.word	90287
	.byte	3,28,137,6,9,16,6
	.byte	'numOfPllDividerSteps',0
	.word	226
	.byte	1,2,35,0,6
	.byte	'pllDividerStep',0
	.word	90617
	.byte	4,2,35,4,6
	.byte	'pllInitialStep',0
	.word	90170
	.byte	8,2,35,8,0,9
	.byte	'IfxScuCcu_SysPllConfig',0,28,142,6,3
	.word	90622
	.byte	9
	.byte	'Ifx_GPT12_ACCEN0_Bits',0,14,79,3
	.word	16044
	.byte	9
	.byte	'Ifx_GPT12_ACCEN1_Bits',0,14,85,3
	.word	15953
	.byte	9
	.byte	'Ifx_GPT12_CAPREL_Bits',0,14,92,3
	.word	14818
	.byte	9
	.byte	'Ifx_GPT12_CLC_Bits',0,14,102,3
	.word	12840
	.byte	9
	.byte	'Ifx_GPT12_ID_Bits',0,14,110,3
	.word	13294
	.byte	9
	.byte	'Ifx_GPT12_KRST0_Bits',0,14,118,3
	.word	15829
	.byte	9
	.byte	'Ifx_GPT12_KRST1_Bits',0,14,125,3
	.word	15724
	.byte	9
	.byte	'Ifx_GPT12_KRSTCLR_Bits',0,14,132,1,3
	.word	15616
	.byte	9
	.byte	'Ifx_GPT12_OCS_Bits',0,14,142,1,3
	.word	15454
	.byte	9
	.byte	'Ifx_GPT12_PISEL_Bits',0,14,159,1,3
	.word	12998
	.byte	9
	.byte	'Ifx_GPT12_T2_Bits',0,14,166,1,3
	.word	14928
	.byte	9
	.byte	'Ifx_GPT12_T2CON_Bits',0,14,183,1,3
	.word	13417
	.byte	9
	.byte	'Ifx_GPT12_T3_Bits',0,14,190,1,3
	.word	15031
	.byte	9
	.byte	'Ifx_GPT12_T3CON_Bits',0,14,207,1,3
	.word	13700
	.byte	9
	.byte	'Ifx_GPT12_T4_Bits',0,14,214,1,3
	.word	15134
	.byte	9
	.byte	'Ifx_GPT12_T4CON_Bits',0,14,232,1,3
	.word	13974
	.byte	9
	.byte	'Ifx_GPT12_T5_Bits',0,14,239,1,3
	.word	15237
	.byte	9
	.byte	'Ifx_GPT12_T5CON_Bits',0,14,128,2,3
	.word	14272
	.byte	9
	.byte	'Ifx_GPT12_T6_Bits',0,14,135,2,3
	.word	15340
	.byte	9
	.byte	'Ifx_GPT12_T6CON_Bits',0,14,152,2,3
	.word	14543
	.byte	9
	.byte	'Ifx_GPT12_ACCEN0',0,14,165,2,3
	.word	16575
	.byte	9
	.byte	'Ifx_GPT12_ACCEN1',0,14,173,2,3
	.word	16004
	.byte	9
	.byte	'Ifx_GPT12_CAPREL',0,14,181,2,3
	.word	14888
	.byte	9
	.byte	'Ifx_GPT12_CLC',0,14,189,2,3
	.word	12958
	.byte	9
	.byte	'Ifx_GPT12_ID',0,14,197,2,3
	.word	13377
	.byte	9
	.byte	'Ifx_GPT12_KRST0',0,14,205,2,3
	.word	15913
	.byte	9
	.byte	'Ifx_GPT12_KRST1',0,14,213,2,3
	.word	15789
	.byte	9
	.byte	'Ifx_GPT12_KRSTCLR',0,14,221,2,3
	.word	15684
	.byte	9
	.byte	'Ifx_GPT12_OCS',0,14,229,2,3
	.word	15576
	.byte	9
	.byte	'Ifx_GPT12_PISEL',0,14,237,2,3
	.word	13254
	.byte	9
	.byte	'Ifx_GPT12_T2',0,14,245,2,3
	.word	14991
	.byte	9
	.byte	'Ifx_GPT12_T2CON',0,14,253,2,3
	.word	13660
	.byte	9
	.byte	'Ifx_GPT12_T3',0,14,133,3,3
	.word	15094
	.byte	9
	.byte	'Ifx_GPT12_T3CON',0,14,141,3,3
	.word	13934
	.byte	9
	.byte	'Ifx_GPT12_T4',0,14,149,3,3
	.word	15197
	.byte	9
	.byte	'Ifx_GPT12_T4CON',0,14,157,3,3
	.word	14232
	.byte	9
	.byte	'Ifx_GPT12_T5',0,14,165,3,3
	.word	15300
	.byte	9
	.byte	'Ifx_GPT12_T5CON',0,14,173,3,3
	.word	14503
	.byte	9
	.byte	'Ifx_GPT12_T6',0,14,181,3,3
	.word	15403
	.byte	9
	.byte	'Ifx_GPT12_T6CON',0,14,189,3,3
	.word	14778
	.byte	21
	.word	16615
	.byte	9
	.byte	'Ifx_GPT12',0,14,225,3,3
	.word	91798
	.byte	28
	.word	21241
	.byte	9
	.byte	'IfxGpt12_TxEud_In',0,15,73,3
	.word	91822
	.byte	28
	.word	21834
	.byte	9
	.byte	'IfxGpt12_TxIn_In',0,15,82,3
	.word	91853
	.byte	3,15,85,15,20,6
	.byte	'module',0
	.word	16991
	.byte	4,2,35,0,6
	.byte	'timer',0
	.word	226
	.byte	1,2,35,4,6
	.byte	'pin',0
	.word	21083
	.byte	8,2,35,8,6
	.byte	'select',0
	.word	12346
	.byte	1,2,35,16,0,28
	.word	91883
	.byte	9
	.byte	'IfxGpt12_TxOut_Out',0,15,91,3
	.word	91949
	.byte	28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T2EUDA_P00_8_IN',0,15,94,30
	.word	91981
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T2EUDB_P33_6_IN',0,15,95,30
	.word	92022
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T3EUDA_P02_7_IN',0,15,96,30
	.word	92063
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T4EUDA_P00_9_IN',0,15,98,30
	.word	92104
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T4EUDB_P33_5_IN',0,15,99,30
	.word	92145
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T5EUDA_P21_6_IN',0,15,100,30
	.word	92186
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T5EUDB_P10_1_IN',0,15,101,30
	.word	92227
	.byte	1,1,28
	.word	21241
	.byte	30
	.byte	'IfxGpt120_T6EUDA_P20_0_IN',0,15,102,30
	.word	92268
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T2INA_P00_7_IN',0,15,104,29
	.word	92309
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T2INB_P33_7_IN',0,15,105,29
	.word	92349
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T3INA_P02_6_IN',0,15,106,29
	.word	92389
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T4INA_P02_8_IN',0,15,108,29
	.word	92429
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T5INA_P21_7_IN',0,15,110,29
	.word	92469
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T5INB_P10_3_IN',0,15,111,29
	.word	92509
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T6INA_P20_3_IN',0,15,112,29
	.word	92549
	.byte	1,1,28
	.word	21834
	.byte	30
	.byte	'IfxGpt120_T6INB_P10_2_IN',0,15,113,29
	.word	92589
	.byte	1,1,9
	.byte	'IfxGpt12_CaptureInput',0,13,85,3
	.word	18696
	.byte	13,13,89,9,1,14
	.byte	'IfxGpt12_CaptureInputMode_none',0,0,14
	.byte	'IfxGpt12_CaptureInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_CaptureInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_CaptureInputMode_bothEdgesTxIN',0,3,0,9
	.byte	'IfxGpt12_CaptureInputMode',0,13,95,3
	.word	92659
	.byte	13,13,100,9,1,14
	.byte	'IfxGpt12_CaptureTrigger_capin',0,0,14
	.byte	'IfxGpt12_CaptureTrigger_t3inOrT3EUD',0,1,0,9
	.byte	'IfxGpt12_CaptureTrigger',0,13,104,3
	.word	92861
	.byte	13,13,109,9,1,14
	.byte	'IfxGpt12_CaptureTriggerMode_disabled',0,0,14
	.byte	'IfxGpt12_CaptureTriggerMode_risingEdge',0,1,14
	.byte	'IfxGpt12_CaptureTriggerMode_fallingEdge',0,2,14
	.byte	'IfxGpt12_CaptureTriggerMode_randomEdge',0,3,0,9
	.byte	'IfxGpt12_CaptureTriggerMode',0,13,115,3
	.word	92969
	.byte	9
	.byte	'IfxGpt12_CounterInputMode',0,13,129,1,3
	.word	16996
	.byte	13,13,134,1,9,1,14
	.byte	'IfxGpt12_EudInput_A',0,0,14
	.byte	'IfxGpt12_EudInput_B',0,1,14
	.byte	'IfxGpt12_EudInput_C',0,2,14
	.byte	'IfxGpt12_EudInput_D',0,3,0,9
	.byte	'IfxGpt12_EudInput',0,13,140,1,3
	.word	93209
	.byte	9
	.byte	'IfxGpt12_Gpt1BlockPrescaler',0,13,151,1,3
	.word	18871
	.byte	9
	.byte	'IfxGpt12_Gpt2BlockPrescaler',0,13,162,1,3
	.word	19078
	.byte	13,13,167,1,9,1,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_stopCounterTx',0,0,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxIN',0,1,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxEUD',0,2,14
	.byte	'IfxGpt12_IncrementalInterfaceInputMode_bothEdgesTxINOrTxEUD',0,3,0,9
	.byte	'IfxGpt12_IncrementalInterfaceInputMode',0,13,173,1,3
	.word	93405
	.byte	13,13,178,1,9,1,14
	.byte	'IfxGpt12_Input_A',0,0,14
	.byte	'IfxGpt12_Input_B',0,1,14
	.byte	'IfxGpt12_Input_C',0,2,14
	.byte	'IfxGpt12_Input_D',0,3,0,9
	.byte	'IfxGpt12_Input',0,13,184,1,3
	.word	93688
	.byte	9
	.byte	'IfxGpt12_Mode',0,13,199,1,3
	.word	17544
	.byte	13,13,204,1,9,1,14
	.byte	'IfxGpt12_ReloadInputMode_counterDisabled',0,0,14
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxIN',0,1,14
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxIN',0,2,14
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxIN',0,3,14
	.byte	'IfxGpt12_ReloadInputMode_risingEdgeTxOTL',0,5,14
	.byte	'IfxGpt12_ReloadInputMode_fallingEdgeTxOTL',0,6,14
	.byte	'IfxGpt12_ReloadInputMode_bothEdgesTxOTL',0,7,0,9
	.byte	'IfxGpt12_ReloadInputMode',0,13,213,1,3
	.word	93818
	.byte	13,13,218,1,9,1,14
	.byte	'IfxGpt12_SleepMode_enable',0,0,14
	.byte	'IfxGpt12_SleepMode_disable',0,1,0,9
	.byte	'IfxGpt12_SleepMode',0,13,222,1,3
	.word	94157
	.byte	13,13,226,1,9,1,14
	.byte	'IfxGpt12_SuspendMode_none',0,0,14
	.byte	'IfxGpt12_SuspendMode_hard',0,1,14
	.byte	'IfxGpt12_SuspendMode_soft',0,2,0,9
	.byte	'IfxGpt12_SuspendMode',0,13,231,1,3
	.word	94249
	.byte	13,13,236,1,9,1,14
	.byte	'IfxGpt12_TimerDirection_up',0,0,14
	.byte	'IfxGpt12_TimerDirection_down',0,1,0,9
	.byte	'IfxGpt12_TimerDirection',0,13,240,1,3
	.word	94370
	.byte	9
	.byte	'IfxGpt12_TimerDirectionSource',0,13,249,1,3
	.word	17383
	.byte	13,13,254,1,9,1,14
	.byte	'IfxGpt12_TimerInputPrescaler_1',0,0,14
	.byte	'IfxGpt12_TimerInputPrescaler_2',0,1,14
	.byte	'IfxGpt12_TimerInputPrescaler_4',0,2,14
	.byte	'IfxGpt12_TimerInputPrescaler_8',0,3,14
	.byte	'IfxGpt12_TimerInputPrescaler_16',0,4,14
	.byte	'IfxGpt12_TimerInputPrescaler_32',0,5,14
	.byte	'IfxGpt12_TimerInputPrescaler_64',0,6,14
	.byte	'IfxGpt12_TimerInputPrescaler_128',0,7,0,9
	.byte	'IfxGpt12_TimerInputPrescaler',0,13,136,2,3
	.word	94509
	.byte	13,13,141,2,9,1,14
	.byte	'IfxGpt12_TimerReloadMode_disable',0,0,14
	.byte	'IfxGpt12_TimerReloadMode_enable',0,1,0,9
	.byte	'IfxGpt12_TimerReloadMode',0,13,145,2,3
	.word	94823
	.byte	13,13,150,2,9,1,14
	.byte	'IfxGpt12_TimerRemoteControl_off',0,0,14
	.byte	'IfxGpt12_TimerRemoteControl_on',0,1,0,9
	.byte	'IfxGpt12_TimerRemoteControl',0,13,154,2,3
	.word	94933
	.byte	9
	.byte	'IfxGpt12_TimerRun',0,13,163,2,3
	.word	19559
	.byte	9
	.byte	'IfxGpt12_IncrEnc_Update',0,29,203,1,29
	.word	1459
	.byte	9
	.byte	'_iob_flag_t',0,30,82,25
	.word	887
	.byte	2
	.byte	'char',0,1,6,9
	.byte	'int8',0,31,54,29
	.word	95124
	.byte	9
	.byte	'int16',0,31,55,29
	.word	22087
	.byte	9
	.byte	'int32',0,31,56,29
	.word	2634
	.byte	9
	.byte	'int64',0,31,57,29
	.word	22818
	.byte	9
	.byte	'encoder_channel1_enum',0,18,56,2
	.word	22181
	.byte	9
	.byte	'encoder_channel2_enum',0,18,73,2
	.word	22387
	.byte	9
	.byte	'encoder_index_enum',0,18,82,2
	.word	22100
.L486:
	.byte	23,5
	.word	226
	.byte	24,4,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L108:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,5,23,1,58,15,59,15,57,15,11,15,0,0,6,13,0,3,8,73,19,11
	.byte	15,56,9,0,0,7,59,0,3,8,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57,15,73,19,0,0,10,21,1,54,15,39,12
	.byte	0,0,11,5,0,73,19,0,0,12,21,1,73,19,54,15,39,12,0,0,13,4,1,58,15,59,15,57,15,11,15,0,0,14,40,0,3,8,28,13
	.byte	0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0
	.byte	17,5,0,3,8,58,15,59,15,57,15,73,19,0,0,18,11,0,0,0,19,21,0,54,15,39,12,0,0,20,46,1,3,8,32,13,58,15,59
	.byte	15,57,15,54,15,39,12,0,0,21,53,0,73,19,0,0,22,11,1,0,0,23,1,1,11,15,73,19,0,0,24,33,0,47,15,0,0,25,46
	.byte	1,49,19,0,0,26,5,0,49,19,0,0,27,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,28,38,0,73,19,0
	.byte	0,29,21,0,54,15,0,0,30,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L109:
	.word	.L538-.L537
.L537:
	.half	3
	.word	.L540-.L539
.L539:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_Pos.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0
	.byte	'IfxGpt12_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxGpt12_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,2,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_encoder.h',0,0,0,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Math\\Ifx_LowPassPt1F32.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'IfxGpt12_IncrEnc.h',0,3,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,2,0,0,0
.L540:
.L538:
	.sdecl	'.debug_info',debug,cluster('encoder_get_count')
	.sect	'.debug_info'
.L110:
	.word	490
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L113,.L112
	.byte	2
	.word	.L106
	.byte	3
	.byte	'encoder_get_count',0,1,124,7
	.word	.L137
	.byte	1,1,1
	.word	.L99,.L138,.L98
	.byte	4
	.byte	'encoder_n',0,1,124,45
	.word	.L139,.L140
	.byte	5
	.word	.L99,.L138
	.byte	6
	.byte	'encoder_data',0,1,126,11
	.word	.L137,.L141
	.byte	7
	.word	.L142,.L45,.L51
	.byte	8
	.word	.L143,.L144
	.byte	9
	.word	.L145,.L45,.L51
	.byte	0,7
	.word	.L146,.L46,.L53
	.byte	8
	.word	.L147,.L148
	.byte	9
	.word	.L149,.L46,.L53
	.byte	0,7
	.word	.L150,.L47,.L55
	.byte	8
	.word	.L151,.L152
	.byte	9
	.word	.L153,.L47,.L55
	.byte	0,7
	.word	.L154,.L48,.L57
	.byte	8
	.word	.L155,.L156
	.byte	9
	.word	.L157,.L48,.L57
	.byte	0,7
	.word	.L158,.L49,.L59
	.byte	8
	.word	.L159,.L160
	.byte	9
	.word	.L161,.L49,.L59
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_get_count')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('encoder_get_count')
	.sect	'.debug_line'
.L112:
	.word	.L542-.L541
.L541:
	.half	3
	.word	.L544-.L543
.L543:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0,0
.L544:
	.byte	5,14,7,0,5,2
	.word	.L99
	.byte	3,128,1,1,9
	.half	.L545-.L99
	.byte	3,1,1,9
	.half	.L546-.L545
	.byte	3,1,1,9
	.half	.L547-.L546
	.byte	3,1,1,9
	.half	.L548-.L547
	.byte	3,1,1,4,2,5,29,9
	.half	.L45-.L548
	.byte	3,185,7,1,5,12,9
	.half	.L549-.L45
	.byte	1,5,5,9
	.half	.L550-.L549
	.byte	1,4,1,5,43,9
	.half	.L51-.L550
	.byte	3,195,120,1,5,93,9
	.half	.L520-.L51
	.byte	1,4,2,5,29,9
	.half	.L46-.L520
	.byte	3,172,8,1,5,12,9
	.half	.L551-.L46
	.byte	1,5,5,9
	.half	.L552-.L551
	.byte	1,4,1,5,43,9
	.half	.L53-.L552
	.byte	3,213,119,1,5,93,9
	.half	.L521-.L53
	.byte	1,4,2,5,29,9
	.half	.L47-.L521
	.byte	3,136,9,1,5,12,9
	.half	.L553-.L47
	.byte	1,5,5,9
	.half	.L554-.L553
	.byte	1,4,1,5,43,9
	.half	.L55-.L554
	.byte	3,249,118,1,5,93,9
	.half	.L522-.L55
	.byte	1,4,2,5,29,9
	.half	.L48-.L522
	.byte	3,244,9,1,5,12,9
	.half	.L555-.L48
	.byte	1,5,5,9
	.half	.L556-.L555
	.byte	1,4,1,5,43,9
	.half	.L57-.L556
	.byte	3,141,118,1,5,93,9
	.half	.L523-.L57
	.byte	1,4,2,5,29,9
	.half	.L49-.L523
	.byte	3,221,10,1,5,12,9
	.half	.L557-.L49
	.byte	1,5,5,9
	.half	.L558-.L557
	.byte	1,4,1,5,43,9
	.half	.L59-.L558
	.byte	3,164,117,1,5,93,9
	.half	.L524-.L59
	.byte	1,5,31,9
	.half	.L50-.L524
	.byte	3,1,1,5,13,9
	.half	.L52-.L50
	.byte	3,3,1,5,25,9
	.half	.L559-.L52
	.byte	1,5,5,9
	.half	.L560-.L559
	.byte	1,5,39,7,9
	.half	.L561-.L560
	.byte	3,2,1,5,37,9
	.half	.L562-.L561
	.byte	1,5,5,9
	.half	.L61-.L562
	.byte	3,3,1,5,1,9
	.half	.L62-.L61
	.byte	3,1,1,7,9
	.half	.L114-.L62
	.byte	0,1,1
.L542:
	.sdecl	'.debug_ranges',debug,cluster('encoder_get_count')
	.sect	'.debug_ranges'
.L113:
	.word	-1,.L99,0,.L114-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('encoder_clear_count')
	.sect	'.debug_info'
.L115:
	.word	510
	.half	3
	.word	.L116
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L118,.L117
	.byte	2
	.word	.L106
	.byte	3
	.byte	'encoder_clear_count',0,1,151,1,6,1,1,1
	.word	.L101,.L162,.L100
	.byte	4
	.byte	'encoder_n',0,1,151,1,46
	.word	.L139,.L163
	.byte	5
	.word	.L101,.L162
	.byte	6
	.word	.L164,.L165,.L166
	.byte	7
	.word	.L167,.L168
	.byte	7
	.word	.L169,.L170
	.byte	8
	.word	.L171,.L165,.L166
	.byte	0,6
	.word	.L172,.L173,.L174
	.byte	7
	.word	.L175,.L176
	.byte	7
	.word	.L177,.L178
	.byte	8
	.word	.L179,.L173,.L174
	.byte	0,6
	.word	.L180,.L181,.L182
	.byte	7
	.word	.L183,.L184
	.byte	7
	.word	.L185,.L186
	.byte	8
	.word	.L187,.L181,.L182
	.byte	0,6
	.word	.L188,.L189,.L190
	.byte	7
	.word	.L191,.L192
	.byte	7
	.word	.L193,.L194
	.byte	8
	.word	.L195,.L189,.L190
	.byte	0,6
	.word	.L196,.L197,.L198
	.byte	7
	.word	.L199,.L200
	.byte	7
	.word	.L201,.L202
	.byte	8
	.word	.L203,.L197,.L198
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_clear_count')
	.sect	'.debug_abbrev'
.L116:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('encoder_clear_count')
	.sect	'.debug_line'
.L117:
	.word	.L564-.L563
.L563:
	.half	3
	.word	.L566-.L565
.L565:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0,0
.L566:
	.byte	5,14,7,0,5,2
	.word	.L101
	.byte	3,154,1,1,9
	.half	.L567-.L101
	.byte	3,1,1,9
	.half	.L568-.L567
	.byte	3,1,1,9
	.half	.L569-.L568
	.byte	3,1,1,9
	.half	.L570-.L569
	.byte	3,1,1,5,70,9
	.half	.L63-.L570
	.byte	3,124,1,4,2,5,17,9
	.half	.L165-.L63
	.byte	3,249,7,1,4,1,5,74,9
	.half	.L166-.L165
	.byte	3,135,120,1,5,70,9
	.half	.L64-.L166
	.byte	3,1,1,4,2,5,17,9
	.half	.L173-.L64
	.byte	3,207,8,1,4,1,5,74,9
	.half	.L174-.L173
	.byte	3,177,119,1,5,70,9
	.half	.L65-.L174
	.byte	3,1,1,4,2,5,17,9
	.half	.L181-.L65
	.byte	3,193,9,1,4,1,5,74,9
	.half	.L182-.L181
	.byte	3,191,118,1,5,70,9
	.half	.L66-.L182
	.byte	3,1,1,4,2,5,17,9
	.half	.L189-.L66
	.byte	3,170,10,1,4,1,5,74,9
	.half	.L190-.L189
	.byte	3,214,117,1,5,70,9
	.half	.L67-.L190
	.byte	3,1,1,4,2,5,17,9
	.half	.L197-.L67
	.byte	3,130,11,1,4,1,5,74,9
	.half	.L198-.L197
	.byte	3,254,116,1,5,1,9
	.half	.L69-.L198
	.byte	3,2,1,7,9
	.half	.L119-.L69
	.byte	0,1,1
.L564:
	.sdecl	'.debug_ranges',debug,cluster('encoder_clear_count')
	.sect	'.debug_ranges'
.L118:
	.word	-1,.L101,0,.L119-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('encoder_quad_init')
	.sect	'.debug_info'
.L120:
	.word	1315
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L123,.L122
	.byte	2
	.word	.L106
	.byte	3
	.byte	'encoder_quad_init',0,1,172,1,6,1,1,1
	.word	.L103,.L204,.L102
	.byte	4
	.byte	'encoder_n',0,1,172,1,44
	.word	.L139,.L205
	.byte	4
	.byte	'ch1_pin',0,1,172,1,77
	.word	.L206,.L207
	.byte	4
	.byte	'ch2_pin',0,1,172,1,108
	.word	.L208,.L209
	.byte	5
	.word	.L103,.L204
	.byte	6
	.word	.L210,.L211,.L212
	.byte	7
	.word	.L213,.L214
	.byte	7
	.word	.L215,.L216
	.byte	8
	.word	.L217,.L211,.L212
	.byte	0,6
	.word	.L218,.L219,.L220
	.byte	7
	.word	.L221,.L222
	.byte	7
	.word	.L223,.L224
	.byte	8
	.word	.L225,.L219,.L220
	.byte	0,6
	.word	.L226,.L227,.L228
	.byte	7
	.word	.L229,.L230
	.byte	7
	.word	.L231,.L232
	.byte	8
	.word	.L233,.L227,.L228
	.byte	0,6
	.word	.L234,.L235,.L236
	.byte	7
	.word	.L237,.L238
	.byte	7
	.word	.L239,.L240
	.byte	8
	.word	.L241,.L235,.L236
	.byte	0,6
	.word	.L242,.L243,.L244
	.byte	7
	.word	.L245,.L246
	.byte	7
	.word	.L247,.L248
	.byte	8
	.word	.L249,.L243,.L244
	.byte	0,6
	.word	.L250,.L251,.L252
	.byte	7
	.word	.L253,.L254
	.byte	7
	.word	.L255,.L256
	.byte	8
	.word	.L257,.L251,.L252
	.byte	0,6
	.word	.L258,.L259,.L260
	.byte	7
	.word	.L261,.L262
	.byte	7
	.word	.L263,.L264
	.byte	8
	.word	.L265,.L259,.L260
	.byte	0,6
	.word	.L266,.L267,.L268
	.byte	7
	.word	.L269,.L270
	.byte	7
	.word	.L271,.L272
	.byte	8
	.word	.L273,.L267,.L268
	.byte	0,6
	.word	.L274,.L275,.L276
	.byte	7
	.word	.L277,.L278
	.byte	7
	.word	.L279,.L280
	.byte	8
	.word	.L281,.L275,.L276
	.byte	0,6
	.word	.L282,.L283,.L284
	.byte	7
	.word	.L285,.L286
	.byte	7
	.word	.L287,.L288
	.byte	8
	.word	.L289,.L283,.L284
	.byte	0,6
	.word	.L290,.L291,.L292
	.byte	7
	.word	.L293,.L294
	.byte	7
	.word	.L295,.L296
	.byte	8
	.word	.L297,.L291,.L292
	.byte	0,6
	.word	.L298,.L299,.L300
	.byte	7
	.word	.L301,.L302
	.byte	7
	.word	.L303,.L304
	.byte	8
	.word	.L305,.L299,.L300
	.byte	0,6
	.word	.L306,.L307,.L308
	.byte	7
	.word	.L309,.L310
	.byte	7
	.word	.L311,.L312
	.byte	8
	.word	.L313,.L307,.L308
	.byte	0,6
	.word	.L314,.L315,.L316
	.byte	7
	.word	.L317,.L318
	.byte	7
	.word	.L319,.L320
	.byte	8
	.word	.L321,.L315,.L316
	.byte	0,6
	.word	.L322,.L323,.L324
	.byte	7
	.word	.L325,.L326
	.byte	7
	.word	.L327,.L328
	.byte	8
	.word	.L329,.L323,.L324
	.byte	0,6
	.word	.L330,.L331,.L332
	.byte	7
	.word	.L333,.L334
	.byte	7
	.word	.L335,.L336
	.byte	8
	.word	.L337,.L331,.L332
	.byte	0,6
	.word	.L338,.L339,.L340
	.byte	7
	.word	.L341,.L342
	.byte	7
	.word	.L343,.L344
	.byte	8
	.word	.L345,.L339,.L340
	.byte	0,6
	.word	.L346,.L347,.L348
	.byte	7
	.word	.L349,.L350
	.byte	7
	.word	.L351,.L352
	.byte	8
	.word	.L353,.L347,.L348
	.byte	0,6
	.word	.L354,.L355,.L356
	.byte	7
	.word	.L357,.L358
	.byte	7
	.word	.L359,.L360
	.byte	8
	.word	.L361,.L355,.L356
	.byte	0,6
	.word	.L362,.L363,.L364
	.byte	7
	.word	.L365,.L366
	.byte	7
	.word	.L367,.L368
	.byte	8
	.word	.L369,.L363,.L364
	.byte	0,6
	.word	.L370,.L371,.L372
	.byte	7
	.word	.L373,.L374
	.byte	7
	.word	.L375,.L376
	.byte	8
	.word	.L377,.L371,.L372
	.byte	0,6
	.word	.L378,.L379,.L380
	.byte	7
	.word	.L381,.L382
	.byte	7
	.word	.L383,.L384
	.byte	8
	.word	.L385,.L379,.L380
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_quad_init')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('encoder_quad_init')
	.sect	'.debug_line'
.L122:
	.word	.L572-.L571
.L571:
	.half	3
	.word	.L574-.L573
.L573:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0,0
.L574:
	.byte	5,6,7,0,5,2
	.word	.L103
	.byte	3,171,1,1,5,28,9
	.half	.L575-.L103
	.byte	3,4,1,5,52,9
	.half	.L525-.L575
	.byte	3,1,1,4,2,5,19,9
	.half	.L211-.L525
	.byte	3,178,11,1,5,25,9
	.half	.L576-.L211
	.byte	1,4,1,5,52,9
	.half	.L212-.L576
	.byte	3,207,116,1,4,2,5,19,9
	.half	.L219-.L212
	.byte	3,183,11,1,5,25,9
	.half	.L577-.L219
	.byte	1,4,1,5,45,9
	.half	.L220-.L577
	.byte	3,202,116,1,5,14,9
	.half	.L530-.L220
	.byte	3,4,1,9
	.half	.L578-.L530
	.byte	3,8,1,9
	.half	.L579-.L578
	.byte	3,8,1,9
	.half	.L580-.L579
	.byte	3,8,1,9
	.half	.L581-.L580
	.byte	3,8,1,5,61,9
	.half	.L74-.L581
	.byte	3,98,1,4,2,5,19,9
	.half	.L227-.L74
	.byte	3,151,7,1,5,24,9
	.half	.L582-.L227
	.byte	1,4,1,5,61,9
	.half	.L228-.L582
	.byte	3,234,120,1,4,2,5,19,9
	.half	.L235-.L228
	.byte	3,156,7,1,5,26,9
	.half	.L583-.L235
	.byte	1,4,1,5,61,9
	.half	.L236-.L583
	.byte	3,229,120,1,4,2,5,19,9
	.half	.L243-.L236
	.byte	3,187,7,1,5,24,9
	.half	.L584-.L243
	.byte	1,4,1,5,61,9
	.half	.L244-.L584
	.byte	3,198,120,1,4,2,5,19,9
	.half	.L251-.L244
	.byte	3,136,7,1,5,24,9
	.half	.L585-.L251
	.byte	1,4,1,5,10,9
	.half	.L252-.L585
	.byte	3,249,120,1,5,61,9
	.half	.L75-.L252
	.byte	3,4,1,4,2,5,19,9
	.half	.L259-.L75
	.byte	3,249,7,1,5,24,9
	.half	.L586-.L259
	.byte	1,4,1,5,61,9
	.half	.L260-.L586
	.byte	3,136,120,1,4,2,5,19,9
	.half	.L267-.L260
	.byte	3,254,7,1,5,26,9
	.half	.L587-.L267
	.byte	1,4,1,5,61,9
	.half	.L268-.L587
	.byte	3,131,120,1,4,2,5,19,9
	.half	.L275-.L268
	.byte	3,150,8,1,5,24,9
	.half	.L588-.L275
	.byte	1,4,1,5,61,9
	.half	.L276-.L588
	.byte	3,235,119,1,4,2,5,19,9
	.half	.L283-.L276
	.byte	3,239,7,1,5,24,9
	.half	.L589-.L283
	.byte	1,4,1,5,10,9
	.half	.L284-.L589
	.byte	3,146,120,1,5,61,9
	.half	.L76-.L284
	.byte	3,4,1,4,2,5,19,9
	.half	.L291-.L76
	.byte	3,211,8,1,5,24,9
	.half	.L590-.L291
	.byte	1,4,1,5,61,9
	.half	.L292-.L590
	.byte	3,174,119,1,4,2,5,19,9
	.half	.L299-.L292
	.byte	3,216,8,1,5,26,9
	.half	.L591-.L299
	.byte	1,4,1,5,61,9
	.half	.L300-.L591
	.byte	3,169,119,1,4,2,5,19,9
	.half	.L307-.L300
	.byte	3,245,8,1,5,24,9
	.half	.L592-.L307
	.byte	1,4,1,5,61,9
	.half	.L308-.L592
	.byte	3,140,119,1,4,2,5,19,9
	.half	.L315-.L308
	.byte	3,196,8,1,5,24,9
	.half	.L593-.L315
	.byte	1,4,1,5,10,9
	.half	.L316-.L593
	.byte	3,189,119,1,5,61,9
	.half	.L77-.L316
	.byte	3,4,1,4,2,5,19,9
	.half	.L323-.L77
	.byte	3,196,9,1,5,24,9
	.half	.L594-.L323
	.byte	1,4,1,5,61,9
	.half	.L324-.L594
	.byte	3,189,118,1,4,2,5,19,9
	.half	.L331-.L324
	.byte	3,201,9,1,5,26,9
	.half	.L595-.L331
	.byte	1,4,1,5,61,9
	.half	.L332-.L595
	.byte	3,184,118,1,4,2,5,19,9
	.half	.L339-.L332
	.byte	3,221,9,1,5,24,9
	.half	.L596-.L339
	.byte	1,4,1,5,61,9
	.half	.L340-.L596
	.byte	3,164,118,1,4,2,5,19,9
	.half	.L347-.L340
	.byte	3,169,9,1,5,24,9
	.half	.L597-.L347
	.byte	1,4,1,5,10,9
	.half	.L348-.L597
	.byte	3,216,118,1,5,61,9
	.half	.L78-.L348
	.byte	3,4,1,4,2,5,19,9
	.half	.L355-.L78
	.byte	3,149,10,1,5,24,9
	.half	.L598-.L355
	.byte	1,4,1,5,61,9
	.half	.L356-.L598
	.byte	3,236,117,1,4,2,5,19,9
	.half	.L363-.L356
	.byte	3,154,10,1,5,26,9
	.half	.L599-.L363
	.byte	1,4,1,5,61,9
	.half	.L364-.L599
	.byte	3,231,117,1,4,2,5,19,9
	.half	.L371-.L364
	.byte	3,174,10,1,5,24,9
	.half	.L600-.L371
	.byte	1,4,1,5,61,9
	.half	.L372-.L600
	.byte	3,211,117,1,4,2,5,19,9
	.half	.L379-.L372
	.byte	3,139,10,1,5,24,9
	.half	.L601-.L379
	.byte	1,4,1,5,10,9
	.half	.L380-.L601
	.byte	3,246,117,1,5,5,9
	.half	.L80-.L380
	.byte	3,3,1,5,17,9
	.half	.L602-.L80
	.byte	1,5,31,9
	.half	.L603-.L602
	.byte	1,5,29,9
	.half	.L527-.L603
	.byte	1,5,1,9
	.half	.L604-.L527
	.byte	3,1,1,7,9
	.half	.L124-.L604
	.byte	0,1,1
.L572:
	.sdecl	'.debug_ranges',debug,cluster('encoder_quad_init')
	.sect	'.debug_ranges'
.L123:
	.word	-1,.L103,0,.L124-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('encoder_dir_init')
	.sect	'.debug_info'
.L125:
	.word	1316
	.half	3
	.word	.L126
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L128,.L127
	.byte	2
	.word	.L106
	.byte	3
	.byte	'encoder_dir_init',0,1,236,1,6,1,1,1
	.word	.L105,.L386,.L104
	.byte	4
	.byte	'encoder_n',0,1,236,1,43
	.word	.L139,.L387
	.byte	4
	.byte	'count_pin',0,1,236,1,76
	.word	.L206,.L388
	.byte	4
	.byte	'dir_pin',0,1,236,1,109
	.word	.L208,.L389
	.byte	5
	.word	.L105,.L386
	.byte	6
	.word	.L210,.L390,.L391
	.byte	7
	.word	.L213,.L392
	.byte	7
	.word	.L215,.L393
	.byte	8
	.word	.L217,.L390,.L391
	.byte	0,6
	.word	.L218,.L394,.L395
	.byte	7
	.word	.L221,.L396
	.byte	7
	.word	.L223,.L397
	.byte	8
	.word	.L225,.L394,.L395
	.byte	0,6
	.word	.L226,.L398,.L399
	.byte	7
	.word	.L229,.L400
	.byte	7
	.word	.L231,.L401
	.byte	8
	.word	.L233,.L398,.L399
	.byte	0,6
	.word	.L234,.L402,.L403
	.byte	7
	.word	.L237,.L404
	.byte	7
	.word	.L239,.L405
	.byte	8
	.word	.L241,.L402,.L403
	.byte	0,6
	.word	.L242,.L406,.L407
	.byte	7
	.word	.L245,.L408
	.byte	7
	.word	.L247,.L409
	.byte	8
	.word	.L249,.L406,.L407
	.byte	0,6
	.word	.L250,.L410,.L411
	.byte	7
	.word	.L253,.L412
	.byte	7
	.word	.L255,.L413
	.byte	8
	.word	.L257,.L410,.L411
	.byte	0,6
	.word	.L258,.L414,.L415
	.byte	7
	.word	.L261,.L416
	.byte	7
	.word	.L263,.L417
	.byte	8
	.word	.L265,.L414,.L415
	.byte	0,6
	.word	.L266,.L418,.L419
	.byte	7
	.word	.L269,.L420
	.byte	7
	.word	.L271,.L421
	.byte	8
	.word	.L273,.L418,.L419
	.byte	0,6
	.word	.L274,.L422,.L423
	.byte	7
	.word	.L277,.L424
	.byte	7
	.word	.L279,.L425
	.byte	8
	.word	.L281,.L422,.L423
	.byte	0,6
	.word	.L282,.L426,.L427
	.byte	7
	.word	.L285,.L428
	.byte	7
	.word	.L287,.L429
	.byte	8
	.word	.L289,.L426,.L427
	.byte	0,6
	.word	.L290,.L430,.L431
	.byte	7
	.word	.L293,.L432
	.byte	7
	.word	.L295,.L433
	.byte	8
	.word	.L297,.L430,.L431
	.byte	0,6
	.word	.L298,.L434,.L435
	.byte	7
	.word	.L301,.L436
	.byte	7
	.word	.L303,.L437
	.byte	8
	.word	.L305,.L434,.L435
	.byte	0,6
	.word	.L306,.L438,.L439
	.byte	7
	.word	.L309,.L440
	.byte	7
	.word	.L311,.L441
	.byte	8
	.word	.L313,.L438,.L439
	.byte	0,6
	.word	.L314,.L442,.L443
	.byte	7
	.word	.L317,.L444
	.byte	7
	.word	.L319,.L445
	.byte	8
	.word	.L321,.L442,.L443
	.byte	0,6
	.word	.L322,.L446,.L447
	.byte	7
	.word	.L325,.L448
	.byte	7
	.word	.L327,.L449
	.byte	8
	.word	.L329,.L446,.L447
	.byte	0,6
	.word	.L330,.L450,.L451
	.byte	7
	.word	.L333,.L452
	.byte	7
	.word	.L335,.L453
	.byte	8
	.word	.L337,.L450,.L451
	.byte	0,6
	.word	.L338,.L454,.L455
	.byte	7
	.word	.L341,.L456
	.byte	7
	.word	.L343,.L457
	.byte	8
	.word	.L345,.L454,.L455
	.byte	0,6
	.word	.L346,.L458,.L459
	.byte	7
	.word	.L349,.L460
	.byte	7
	.word	.L351,.L461
	.byte	8
	.word	.L353,.L458,.L459
	.byte	0,6
	.word	.L354,.L462,.L463
	.byte	7
	.word	.L357,.L464
	.byte	7
	.word	.L359,.L465
	.byte	8
	.word	.L361,.L462,.L463
	.byte	0,6
	.word	.L362,.L466,.L467
	.byte	7
	.word	.L365,.L468
	.byte	7
	.word	.L367,.L469
	.byte	8
	.word	.L369,.L466,.L467
	.byte	0,6
	.word	.L370,.L470,.L471
	.byte	7
	.word	.L373,.L472
	.byte	7
	.word	.L375,.L473
	.byte	8
	.word	.L377,.L470,.L471
	.byte	0,6
	.word	.L378,.L474,.L475
	.byte	7
	.word	.L381,.L476
	.byte	7
	.word	.L383,.L477
	.byte	8
	.word	.L385,.L474,.L475
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_dir_init')
	.sect	'.debug_abbrev'
.L126:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('encoder_dir_init')
	.sect	'.debug_line'
.L127:
	.word	.L606-.L605
.L605:
	.half	3
	.word	.L608-.L607
.L607:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Gpt12\\Std\\IfxGpt12.h',0
	.byte	0,0,0,0
.L608:
	.byte	5,6,7,0,5,2
	.word	.L105
	.byte	3,235,1,1,5,28,9
	.half	.L609-.L105
	.byte	3,2,1,5,52,9
	.half	.L531-.L609
	.byte	3,1,1,4,2,5,19,9
	.half	.L390-.L531
	.byte	3,244,10,1,5,25,9
	.half	.L610-.L390
	.byte	1,4,1,5,52,9
	.half	.L391-.L610
	.byte	3,141,117,1,4,2,5,19,9
	.half	.L394-.L391
	.byte	3,249,10,1,5,25,9
	.half	.L611-.L394
	.byte	1,4,1,5,47,9
	.half	.L395-.L611
	.byte	3,136,117,1,5,14,9
	.half	.L536-.L395
	.byte	3,4,1,9
	.half	.L612-.L536
	.byte	3,8,1,9
	.half	.L613-.L612
	.byte	3,8,1,9
	.half	.L614-.L613
	.byte	3,8,1,9
	.half	.L615-.L614
	.byte	3,8,1,5,61,9
	.half	.L85-.L615
	.byte	3,98,1,4,2,5,19,9
	.half	.L398-.L85
	.byte	3,217,6,1,5,24,9
	.half	.L616-.L398
	.byte	1,4,1,5,61,9
	.half	.L399-.L616
	.byte	3,168,121,1,4,2,5,19,9
	.half	.L402-.L399
	.byte	3,222,6,1,5,26,9
	.half	.L617-.L402
	.byte	1,4,1,5,61,9
	.half	.L403-.L617
	.byte	3,163,121,1,4,2,5,19,9
	.half	.L406-.L403
	.byte	3,253,6,1,5,24,9
	.half	.L618-.L406
	.byte	1,4,1,5,61,9
	.half	.L407-.L618
	.byte	3,132,121,1,4,2,5,19,9
	.half	.L410-.L407
	.byte	3,202,6,1,5,24,9
	.half	.L619-.L410
	.byte	1,4,1,5,10,9
	.half	.L411-.L619
	.byte	3,183,121,1,5,61,9
	.half	.L86-.L411
	.byte	3,4,1,4,2,5,19,9
	.half	.L414-.L86
	.byte	3,187,7,1,5,24,9
	.half	.L620-.L414
	.byte	1,4,1,5,61,9
	.half	.L415-.L620
	.byte	3,198,120,1,4,2,5,19,9
	.half	.L418-.L415
	.byte	3,192,7,1,5,26,9
	.half	.L621-.L418
	.byte	1,4,1,5,61,9
	.half	.L419-.L621
	.byte	3,193,120,1,4,2,5,19,9
	.half	.L422-.L419
	.byte	3,216,7,1,5,24,9
	.half	.L622-.L422
	.byte	1,4,1,5,61,9
	.half	.L423-.L622
	.byte	3,169,120,1,4,2,5,19,9
	.half	.L426-.L423
	.byte	3,177,7,1,5,24,9
	.half	.L623-.L426
	.byte	1,4,1,5,10,9
	.half	.L427-.L623
	.byte	3,208,120,1,5,61,9
	.half	.L87-.L427
	.byte	3,4,1,4,2,5,19,9
	.half	.L430-.L87
	.byte	3,149,8,1,5,24,9
	.half	.L624-.L430
	.byte	1,4,1,5,61,9
	.half	.L431-.L624
	.byte	3,236,119,1,4,2,5,19,9
	.half	.L434-.L431
	.byte	3,154,8,1,5,26,9
	.half	.L625-.L434
	.byte	1,4,1,5,61,9
	.half	.L435-.L625
	.byte	3,231,119,1,4,2,5,19,9
	.half	.L438-.L435
	.byte	3,183,8,1,5,24,9
	.half	.L626-.L438
	.byte	1,4,1,5,61,9
	.half	.L439-.L626
	.byte	3,202,119,1,4,2,5,19,9
	.half	.L442-.L439
	.byte	3,134,8,1,5,24,9
	.half	.L627-.L442
	.byte	1,4,1,5,10,9
	.half	.L443-.L627
	.byte	3,251,119,1,5,61,9
	.half	.L88-.L443
	.byte	3,4,1,4,2,5,19,9
	.half	.L446-.L88
	.byte	3,134,9,1,5,24,9
	.half	.L628-.L446
	.byte	1,4,1,5,61,9
	.half	.L447-.L628
	.byte	3,251,118,1,4,2,5,19,9
	.half	.L450-.L447
	.byte	3,139,9,1,5,26,9
	.half	.L629-.L450
	.byte	1,4,1,5,61,9
	.half	.L451-.L629
	.byte	3,246,118,1,4,2,5,19,9
	.half	.L454-.L451
	.byte	3,159,9,1,5,24,9
	.half	.L630-.L454
	.byte	1,4,1,5,61,9
	.half	.L455-.L630
	.byte	3,226,118,1,4,2,5,19,9
	.half	.L458-.L455
	.byte	3,235,8,1,5,24,9
	.half	.L631-.L458
	.byte	1,4,1,5,10,9
	.half	.L459-.L631
	.byte	3,150,119,1,5,61,9
	.half	.L89-.L459
	.byte	3,4,1,4,2,5,19,9
	.half	.L462-.L89
	.byte	3,215,9,1,5,24,9
	.half	.L632-.L462
	.byte	1,4,1,5,61,9
	.half	.L463-.L632
	.byte	3,170,118,1,4,2,5,19,9
	.half	.L466-.L463
	.byte	3,220,9,1,5,26,9
	.half	.L633-.L466
	.byte	1,4,1,5,61,9
	.half	.L467-.L633
	.byte	3,165,118,1,4,2,5,19,9
	.half	.L470-.L467
	.byte	3,240,9,1,5,24,9
	.half	.L634-.L470
	.byte	1,4,1,5,61,9
	.half	.L471-.L634
	.byte	3,145,118,1,4,2,5,19,9
	.half	.L474-.L471
	.byte	3,205,9,1,5,24,9
	.half	.L635-.L474
	.byte	1,4,1,5,10,9
	.half	.L475-.L635
	.byte	3,180,118,1,5,5,9
	.half	.L91-.L475
	.byte	3,3,1,5,17,9
	.half	.L636-.L91
	.byte	1,5,31,9
	.half	.L637-.L636
	.byte	1,5,29,9
	.half	.L533-.L637
	.byte	1,5,1,9
	.half	.L638-.L533
	.byte	3,1,1,7,9
	.half	.L129-.L638
	.byte	0,1,1
.L606:
	.sdecl	'.debug_ranges',debug,cluster('encoder_dir_init')
	.sect	'.debug_ranges'
.L128:
	.word	-1,.L105,0,.L129-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('encoder_mapping_set')
	.sect	'.debug_info'
.L130:
	.word	354
	.half	3
	.word	.L131
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L133,.L132
	.byte	2
	.word	.L106
	.byte	3
	.byte	'encoder_mapping_set',0,1,51,13,1,1
	.word	.L97,.L478,.L96
	.byte	4
	.byte	'encoder_n',0,1,51,53
	.word	.L139,.L479
	.byte	4
	.byte	'ch1_pin',0,1,51,86
	.word	.L206,.L480
	.byte	4
	.byte	'ch2_pin',0,1,51,117
	.word	.L208,.L481
	.byte	5
	.word	.L97,.L478
	.byte	6
	.byte	'ch1',0,1,53,24
	.word	.L482,.L483
	.byte	6
	.byte	'ch2',0,1,54,24
	.word	.L484,.L485
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_mapping_set')
	.sect	'.debug_abbrev'
.L131:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('encoder_mapping_set')
	.sect	'.debug_line'
.L132:
	.word	.L640-.L639
.L639:
	.half	3
	.word	.L642-.L641
.L641:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0,0,0,0,0
.L642:
	.byte	5,13,7,0,5,2
	.word	.L97
	.byte	3,50,1,5,14,9
	.half	.L489-.L97
	.byte	3,7,1,9
	.half	.L643-.L489
	.byte	3,11,1,9
	.half	.L644-.L643
	.byte	3,9,1,9
	.half	.L645-.L644
	.byte	3,10,1,9
	.half	.L646-.L645
	.byte	3,11,1,5,13,9
	.half	.L2-.L646
	.byte	3,89,1,5,70,7,9
	.half	.L647-.L2
	.byte	1,5,94,9
	.half	.L648-.L647
	.byte	1,5,18,9
	.half	.L8-.L648
	.byte	3,1,1,5,70,7,9
	.half	.L649-.L8
	.byte	1,5,94,9
	.half	.L650-.L649
	.byte	1,5,21,9
	.half	.L10-.L650
	.byte	3,1,1,5,13,9
	.half	.L9-.L10
	.byte	3,2,1,5,70,7,9
	.half	.L651-.L9
	.byte	1,5,95,9
	.half	.L652-.L651
	.byte	1,5,18,9
	.half	.L12-.L652
	.byte	3,1,1,5,70,7,9
	.half	.L653-.L12
	.byte	1,5,95,9
	.half	.L654-.L653
	.byte	1,5,21,9
	.half	.L14-.L654
	.byte	3,1,1,5,10,9
	.half	.L13-.L14
	.byte	3,1,1,5,13,9
	.half	.L3-.L13
	.byte	3,4,1,5,70,7,9
	.half	.L655-.L3
	.byte	1,5,94,9
	.half	.L656-.L655
	.byte	1,5,21,9
	.half	.L17-.L656
	.byte	3,1,1,5,13,9
	.half	.L18-.L17
	.byte	3,2,1,5,70,7,9
	.half	.L657-.L18
	.byte	1,5,95,9
	.half	.L658-.L657
	.byte	1,5,21,9
	.half	.L19-.L658
	.byte	3,1,1,5,10,9
	.half	.L20-.L19
	.byte	3,1,1,5,13,9
	.half	.L4-.L20
	.byte	3,4,1,5,70,7,9
	.half	.L659-.L4
	.byte	1,5,94,9
	.half	.L660-.L659
	.byte	1,5,21,9
	.half	.L22-.L660
	.byte	3,1,1,5,13,9
	.half	.L23-.L22
	.byte	3,2,1,5,70,7,9
	.half	.L661-.L23
	.byte	1,5,95,9
	.half	.L662-.L661
	.byte	1,5,18,9
	.half	.L24-.L662
	.byte	3,1,1,5,70,7,9
	.half	.L663-.L24
	.byte	1,5,95,9
	.half	.L664-.L663
	.byte	1,5,21,9
	.half	.L26-.L664
	.byte	3,1,1,5,10,9
	.half	.L25-.L26
	.byte	3,1,1,5,13,9
	.half	.L5-.L25
	.byte	3,4,1,5,70,7,9
	.half	.L665-.L5
	.byte	1,5,94,9
	.half	.L666-.L665
	.byte	1,5,18,9
	.half	.L29-.L666
	.byte	3,1,1,5,70,7,9
	.half	.L667-.L29
	.byte	1,5,94,9
	.half	.L668-.L667
	.byte	1,5,21,9
	.half	.L31-.L668
	.byte	3,1,1,5,13,9
	.half	.L30-.L31
	.byte	3,2,1,5,70,7,9
	.half	.L669-.L30
	.byte	1,5,95,9
	.half	.L670-.L669
	.byte	1,5,18,9
	.half	.L33-.L670
	.byte	3,1,1,5,70,7,9
	.half	.L671-.L33
	.byte	1,5,95,9
	.half	.L672-.L671
	.byte	1,5,21,9
	.half	.L35-.L672
	.byte	3,1,1,5,10,9
	.half	.L34-.L35
	.byte	3,1,1,5,13,9
	.half	.L6-.L34
	.byte	3,4,1,5,70,7,9
	.half	.L673-.L6
	.byte	1,5,94,9
	.half	.L674-.L673
	.byte	1,5,18,9
	.half	.L38-.L674
	.byte	3,1,1,5,70,7,9
	.half	.L675-.L38
	.byte	1,5,94,9
	.half	.L676-.L675
	.byte	1,5,21,9
	.half	.L40-.L676
	.byte	3,1,1,5,13,9
	.half	.L39-.L40
	.byte	3,2,1,5,70,7,9
	.half	.L677-.L39
	.byte	1,5,95,9
	.half	.L678-.L677
	.byte	1,5,21,9
	.half	.L42-.L678
	.byte	3,1,1,5,10,9
	.half	.L43-.L42
	.byte	3,1,1,5,44,9
	.half	.L16-.L43
	.byte	3,4,1,5,70,9
	.half	.L679-.L16
	.byte	1,5,46,9
	.half	.L516-.L679
	.byte	3,1,1,5,72,9
	.half	.L680-.L516
	.byte	1,5,1,9
	.half	.L519-.L680
	.byte	3,3,1,7,9
	.half	.L134-.L519
	.byte	0,1,1
.L640:
	.sdecl	'.debug_ranges',debug,cluster('encoder_mapping_set')
	.sect	'.debug_ranges'
.L133:
	.word	-1,.L97,0,.L134-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('encoder_mode')
	.sect	'.debug_info'
.L135:
	.word	230
	.half	3
	.word	.L136
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_encoder.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L106
	.byte	3
	.byte	'encoder_mode',0,19,40,14
	.word	.L486
	.byte	5,3
	.word	encoder_mode
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('encoder_mode')
	.sect	'.debug_abbrev'
.L136:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('encoder_clear_count')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L162-.L101
	.half	2
	.byte	138,0
	.word	0,0
.L163:
	.word	-1,.L101,0,.L162-.L101
	.half	1
	.byte	84
	.word	0,0
.L168:
	.word	0,0
.L176:
	.word	0,0
.L184:
	.word	0,0
.L192:
	.word	0,0
.L200:
	.word	0,0
.L170:
	.word	0,0
.L178:
	.word	0,0
.L186:
	.word	0,0
.L194:
	.word	0,0
.L202:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('encoder_dir_init')
	.sect	'.debug_loc'
.L393:
	.word	0,0
.L397:
	.word	0,0
.L388:
	.word	-1,.L105,0,.L531-.L105
	.half	1
	.byte	85
	.word	.L395-.L105,.L534-.L105
	.half	1
	.byte	88
	.word	0,0
.L389:
	.word	-1,.L105,0,.L531-.L105
	.half	1
	.byte	86
	.word	.L534-.L105,.L535-.L105
	.half	1
	.byte	89
	.word	.L535-.L105,.L536-.L105
	.half	1
	.byte	86
	.word	0,0
.L104:
	.word	-1,.L105,0,.L386-.L105
	.half	2
	.byte	138,0
	.word	0,0
.L387:
	.word	-1,.L105,0,.L531-.L105
	.half	1
	.byte	84
	.word	.L532-.L105,.L533-.L105
	.half	1
	.byte	95
	.word	0,0
.L412:
	.word	0,0
.L400:
	.word	0,0
.L404:
	.word	0,0
.L408:
	.word	0,0
.L428:
	.word	0,0
.L416:
	.word	0,0
.L420:
	.word	0,0
.L424:
	.word	0,0
.L444:
	.word	0,0
.L432:
	.word	0,0
.L436:
	.word	0,0
.L440:
	.word	0,0
.L460:
	.word	0,0
.L448:
	.word	0,0
.L452:
	.word	0,0
.L456:
	.word	0,0
.L476:
	.word	0,0
.L464:
	.word	0,0
.L468:
	.word	0,0
.L472:
	.word	0,0
.L392:
	.word	0,0
.L396:
	.word	0,0
.L401:
	.word	0,0
.L417:
	.word	0,0
.L433:
	.word	0,0
.L449:
	.word	0,0
.L465:
	.word	0,0
.L409:
	.word	0,0
.L425:
	.word	0,0
.L441:
	.word	0,0
.L457:
	.word	0,0
.L473:
	.word	0,0
.L413:
	.word	0,0
.L429:
	.word	0,0
.L445:
	.word	0,0
.L461:
	.word	0,0
.L477:
	.word	0,0
.L405:
	.word	0,0
.L421:
	.word	0,0
.L437:
	.word	0,0
.L453:
	.word	0,0
.L469:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('encoder_get_count')
	.sect	'.debug_loc'
.L141:
	.word	-1,.L99,.L520-.L99,.L46-.L99
	.half	1
	.byte	82
	.word	.L521-.L99,.L47-.L99
	.half	1
	.byte	82
	.word	.L522-.L99,.L48-.L99
	.half	1
	.byte	82
	.word	.L523-.L99,.L49-.L99
	.half	1
	.byte	82
	.word	.L524-.L99,.L50-.L99
	.half	1
	.byte	82
	.word	.L52-.L99,.L138-.L99
	.half	1
	.byte	82
	.word	0,0
.L98:
	.word	-1,.L99,0,.L138-.L99
	.half	2
	.byte	138,0
	.word	0,0
.L140:
	.word	-1,.L99,0,.L138-.L99
	.half	1
	.byte	84
	.word	0,0
.L144:
	.word	0,0
.L148:
	.word	0,0
.L152:
	.word	0,0
.L156:
	.word	0,0
.L160:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('encoder_mapping_set')
	.sect	'.debug_loc'
.L483:
	.word	-1,.L97,.L490-.L97,.L8-.L97
	.half	1
	.byte	111
	.word	.L491-.L97,.L10-.L97
	.half	1
	.byte	111
	.word	.L496-.L97,.L17-.L97
	.half	1
	.byte	111
	.word	.L500-.L97,.L22-.L97
	.half	1
	.byte	111
	.word	.L505-.L97,.L29-.L97
	.half	1
	.byte	111
	.word	.L506-.L97,.L31-.L97
	.half	1
	.byte	111
	.word	.L511-.L97,.L38-.L97
	.half	1
	.byte	111
	.word	.L512-.L97,.L40-.L97
	.half	1
	.byte	111
	.word	.L514-.L97,.L515-.L97
	.half	1
	.byte	111
	.word	.L515-.L97,.L516-.L97
	.half	1
	.byte	100
	.word	0,0
.L480:
	.word	-1,.L97,0,.L487-.L97
	.half	1
	.byte	85
	.word	.L3-.L97,.L494-.L97
	.half	1
	.byte	85
	.word	.L4-.L97,.L498-.L97
	.half	1
	.byte	85
	.word	.L5-.L97,.L503-.L97
	.half	1
	.byte	85
	.word	.L6-.L97,.L509-.L97
	.half	1
	.byte	85
	.word	0,0
.L485:
	.word	-1,.L97,.L492-.L97,.L12-.L97
	.half	1
	.byte	108
	.word	.L493-.L97,.L14-.L97
	.half	1
	.byte	108
	.word	.L497-.L97,.L19-.L97
	.half	1
	.byte	108
	.word	.L501-.L97,.L24-.L97
	.half	1
	.byte	108
	.word	.L502-.L97,.L26-.L97
	.half	1
	.byte	108
	.word	.L507-.L97,.L33-.L97
	.half	1
	.byte	108
	.word	.L508-.L97,.L35-.L97
	.half	1
	.byte	108
	.word	.L513-.L97,.L42-.L97
	.half	1
	.byte	108
	.word	.L517-.L97,.L518-.L97
	.half	1
	.byte	108
	.word	.L518-.L97,.L519-.L97
	.half	1
	.byte	100
	.word	0,0
.L481:
	.word	-1,.L97,0,.L9-.L97
	.half	1
	.byte	86
	.word	.L489-.L97,.L478-.L97
	.half	1
	.byte	95
	.word	.L3-.L97,.L18-.L97
	.half	1
	.byte	86
	.word	.L4-.L97,.L23-.L97
	.half	1
	.byte	86
	.word	.L5-.L97,.L30-.L97
	.half	1
	.byte	86
	.word	.L6-.L97,.L39-.L97
	.half	1
	.byte	86
	.word	0,0
.L96:
	.word	-1,.L97,0,.L478-.L97
	.half	2
	.byte	138,0
	.word	0,0
.L479:
	.word	-1,.L97,0,.L488-.L97
	.half	1
	.byte	84
	.word	.L3-.L97,.L495-.L97
	.half	1
	.byte	84
	.word	.L4-.L97,.L499-.L97
	.half	1
	.byte	84
	.word	.L5-.L97,.L504-.L97
	.half	1
	.byte	84
	.word	.L6-.L97,.L510-.L97
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('encoder_quad_init')
	.sect	'.debug_loc'
.L216:
	.word	0,0
.L224:
	.word	0,0
.L207:
	.word	-1,.L103,0,.L525-.L103
	.half	1
	.byte	85
	.word	.L220-.L103,.L528-.L103
	.half	1
	.byte	88
	.word	0,0
.L209:
	.word	-1,.L103,0,.L525-.L103
	.half	1
	.byte	86
	.word	.L528-.L103,.L529-.L103
	.half	1
	.byte	89
	.word	.L529-.L103,.L530-.L103
	.half	1
	.byte	86
	.word	0,0
.L205:
	.word	-1,.L103,0,.L525-.L103
	.half	1
	.byte	84
	.word	.L526-.L103,.L527-.L103
	.half	1
	.byte	95
	.word	0,0
.L102:
	.word	-1,.L103,0,.L204-.L103
	.half	2
	.byte	138,0
	.word	0,0
.L254:
	.word	0,0
.L230:
	.word	0,0
.L238:
	.word	0,0
.L246:
	.word	0,0
.L286:
	.word	0,0
.L262:
	.word	0,0
.L270:
	.word	0,0
.L278:
	.word	0,0
.L318:
	.word	0,0
.L294:
	.word	0,0
.L302:
	.word	0,0
.L310:
	.word	0,0
.L350:
	.word	0,0
.L326:
	.word	0,0
.L334:
	.word	0,0
.L342:
	.word	0,0
.L382:
	.word	0,0
.L358:
	.word	0,0
.L366:
	.word	0,0
.L374:
	.word	0,0
.L214:
	.word	0,0
.L222:
	.word	0,0
.L232:
	.word	0,0
.L264:
	.word	0,0
.L296:
	.word	0,0
.L328:
	.word	0,0
.L360:
	.word	0,0
.L248:
	.word	0,0
.L280:
	.word	0,0
.L312:
	.word	0,0
.L344:
	.word	0,0
.L376:
	.word	0,0
.L256:
	.word	0,0
.L288:
	.word	0,0
.L320:
	.word	0,0
.L352:
	.word	0,0
.L384:
	.word	0,0
.L240:
	.word	0,0
.L272:
	.word	0,0
.L304:
	.word	0,0
.L336:
	.word	0,0
.L368:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L681:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('encoder_mapping_set')
	.sect	'.debug_frame'
	.word	12
	.word	.L681,.L97,.L478-.L97
	.sdecl	'.debug_frame',debug,cluster('encoder_get_count')
	.sect	'.debug_frame'
	.word	24
	.word	.L681,.L99,.L138-.L99
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('encoder_clear_count')
	.sect	'.debug_frame'
	.word	24
	.word	.L681,.L101,.L162-.L101
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('encoder_quad_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L681,.L103,.L204-.L103
	.sdecl	'.debug_frame',debug,cluster('encoder_dir_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L681,.L105,.L386-.L105
	; Module end
