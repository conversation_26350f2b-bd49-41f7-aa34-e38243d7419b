	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33200a --dep-file=zf_driver_adc.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_adc.src ../libraries/zf_driver/zf_driver_adc.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_adc.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_adc.adc_convert',code,cluster('adc_convert')
	.sect	'.text.zf_driver_adc.adc_convert'
	.align	2
	
	.global	adc_convert
; Function adc_convert
adc_convert:	.type	func
.L2:
	mov	d15,#16
.L151:
	div	e0,d4,d15
.L152:
	mov	d15,#1024
	mul	d0,d15
	mov.a	a15,d0
	movh.a	a3,#61442
	add.a	a3,a15
	lea	a15,[a3]1152
.L153:
	mov	d15,#16
.L154:
	div	e0,d4,d15
.L45:
	mul	d15,d1,#4
	addsc.a	a15,a15,d15,#0
.L155:
	ld.w	d15,[a15]640
.L118:
	j	.L3
.L3:
	extr.u	d0,d15,#24,#8
.L46:
	jz.t	d0:7,.L2
.L156:
	movh.a	a15,#@his(adc_resolution)
	lea	a15,[a15]@los(adc_resolution)
.L157:
	addsc.a	a15,a15,d4,#0
	ld.bu	d0,[a15]
.L158:
	mul	d0,d0,#2
.L159:
	rsub	d0,d0,#4
	extr.u	d0,d0,#0,#8
.L53:
	insert	d2,d15,#0,#12,#20
.L54:
	rsub	d0,#0
	sh	d2,d2,d0
.L160:
	j	.L4
.L4:
	ret
.L37:
	
__adc_convert_function_end:
	.size	adc_convert,__adc_convert_function_end-adc_convert
.L23:
	; End of function
	
	.sdecl	'.text.zf_driver_adc.adc_mean_filter_convert',code,cluster('adc_mean_filter_convert')
	.sect	'.text.zf_driver_adc.adc_mean_filter_convert'
	.align	2
	
	.global	adc_mean_filter_convert
; Function adc_mean_filter_convert
.L12:
adc_mean_filter_convert:	.type	func
	mov	d15,d4
.L121:
	mov	d8,d5
.L122:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#78
.L119:
	mov	d4,d8
.L120:
	call	debug_assert_handler
.L123:
	mov	d10,#0
.L124:
	mov	d9,#0
.L125:
	j	.L5
.L6:
	mov	d4,d15
.L127:
	call	adc_convert
.L128:
	add	d10,d2
.L165:
	add	d9,#1
.L126:
	extr.u	d9,d9,#0,#8
.L5:
	jlt.u	d9,d8,.L6
.L166:
	div.u	e0,d10,d8
.L129:
	extr.u	d2,d0,#0,#16
.L167:
	j	.L7
.L7:
	ret
.L56:
	
__adc_mean_filter_convert_function_end:
	.size	adc_mean_filter_convert,__adc_mean_filter_convert_function_end-adc_mean_filter_convert
.L28:
	; End of function
	
	.sdecl	'.text.zf_driver_adc.adc_init',code,cluster('adc_init')
	.sect	'.text.zf_driver_adc.adc_init'
	.align	2
	
	.global	adc_init
; Function adc_init
.L14:
adc_init:	.type	func
	sub.a	a10,#136
.L130:
	mov	e8,d5,d4
.L172:
	lea	a4,[a10]16
.L173:
	movh.a	a5,#61442
	call	IfxVadc_Adc_initModuleConfig
.L131:
	movh.a	a15,#@his(_999001_mudule_init_flag)
	lea	a15,[a15]@los(_999001_mudule_init_flag)
	ld.bu	d15,[a15]
	jne	d15,#0,.L8
.L174:
	movh.a	a15,#@his(_999001_mudule_init_flag)
	lea	a15,[a15]@los(_999001_mudule_init_flag)
.L175:
	mov	d0,#1
.L176:
	st.b	[a15],d0
.L177:
	lea	a4,[a10]0
.L178:
	lea	a5,[a10]16
	call	IfxVadc_Adc_initModule
.L179:
	j	.L9
.L8:
	ld.a	a15,[a10]16
.L180:
	st.a	[a10],a15
.L9:
	lea	a4,[a10]48
.L181:
	lea	a5,[a10]0
	call	IfxVadc_Adc_initGroupConfig
.L182:
	mov	d15,#16
.L132:
	div	e0,d8,d15
.L133:
	st.b	[a10]52,d0
.L183:
	ld.bu	d0,[a10]52
.L184:
	st.b	[a10]53,d0
.L185:
	mov	d15,#1
.L186:
	st.b	[a10]97,d15
.L187:
	mov	d15,#1
.L188:
	st.b	[a10]84,d15
.L189:
	mov	d15,#1
.L190:
	st.b	[a10]88,d15
.L191:
	mov	d15,#0
.L192:
	st.b	[a10]58,d15
.L193:
	mov.u	d15,#49045
	addih	d15,d15,#13270
.L194:
	st.w	[a10]54,d15
.L195:
	mov	d15,#0
.L196:
	st.b	[a10]64,d15
.L197:
	mov.u	d15,#49045
	addih	d15,d15,#13270
.L198:
	st.w	[a10]60,d15
.L199:
	lea	a4,[a10]4
.L200:
	lea	a5,[a10]48
	call	IfxVadc_Adc_initGroup
.L74:
	lea	a4,[a10]100
.L201:
	lea	a5,[a10]4
	call	IfxVadc_Adc_initChannelConfig
.L202:
	mov	d15,#16
.L134:
	div	e0,d8,d15
.L135:
	st.b	[a10]112,d1
.L203:
	mov	d15,#16
.L136:
	div	e0,d8,d15
.L137:
	st.b	[a10]115,d1
.L204:
	mov	d15,#1
.L205:
	st.b	[a10]102,d15
.L206:
	lea	a4,[a10]124
.L207:
	lea	a5,[a10]100
	call	IfxVadc_Adc_initChannel
.L79:
	mov	d0,#1
.L208:
	ld.b	d15,[a10]112
.L209:
	sha	d0,d0,d15
.L138:
	lea	a15,[a10]0
.L84:
	ld.a	a15,[a15]
.L210:
	ld.bu	d1,[a10]12
.L96:
	mul	d15,d1,#4
	addsc.a	a2,a15,d15,#0
.L211:
	ld.w	d15,[a2]384
.L212:
	mov	d2,#-1
	xor	d2,d0
.L213:
	and	d15,d2
.L214:
	or	d0,d15
.L139:
	mul	d15,d1,#4
	addsc.a	a15,a15,d15,#0
.L215:
	st.w	[a15]384,d0
.L85:
	lea	a15,[a10]0
.L107:
	ld.a	a15,[a15]
.L113:
	ld.bu	d15,[a15]517
.L216:
	or	d15,#2
	st.b	[a15]517,d15
.L108:
	movh.a	a15,#@his(adc_resolution)
	lea	a15,[a15]@los(adc_resolution)
.L140:
	addsc.a	a15,a15,d8,#0
.L141:
	st.b	[a15],d9
.L142:
	ret
.L62:
	
__adc_init_function_end:
	.size	adc_init,__adc_init_function_end-adc_init
.L33:
	; End of function
	
	.sdecl	'.bss.zf_driver_adc.adc_resolution',data,cluster('adc_resolution')
	.sect	'.bss.zf_driver_adc.adc_resolution'
	.global	adc_resolution
adc_resolution:	.type	object
	.size	adc_resolution,50
	.space	50
	.sdecl	'.rodata.zf_driver_adc..1.str',data,rom
	.sect	'.rodata.zf_driver_adc..1.str'
.1.str:	.type	object
	.size	.1.str,39
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,114,105,118,101,114,47,122
	.byte	102,95,100,114,105,118,101,114
	.byte	95,97,100,99
	.byte	46,99
	.space	1
	.sdecl	'.data.zf_driver_adc._999001_mudule_init_flag',data,cluster('_999001_mudule_init_flag')
	.sect	'.data.zf_driver_adc._999001_mudule_init_flag'
_999001_mudule_init_flag:	.type	object
	.size	_999001_mudule_init_flag,1
	.space	1
	.calls	'adc_mean_filter_convert','debug_assert_handler'
	.calls	'adc_mean_filter_convert','adc_convert'
	.calls	'adc_init','IfxVadc_Adc_initModuleConfig'
	.calls	'adc_init','IfxVadc_Adc_initModule'
	.calls	'adc_init','IfxVadc_Adc_initGroupConfig'
	.calls	'adc_init','IfxVadc_Adc_initGroup'
	.calls	'adc_init','IfxVadc_Adc_initChannelConfig'
	.calls	'adc_init','IfxVadc_Adc_initChannel'
	.calls	'adc_convert','',0
	.calls	'adc_mean_filter_convert','',0
	.extern	IfxVadc_Adc_initModule
	.extern	IfxVadc_Adc_initModuleConfig
	.extern	IfxVadc_Adc_initGroup
	.extern	IfxVadc_Adc_initGroupConfig
	.extern	IfxVadc_Adc_initChannel
	.extern	IfxVadc_Adc_initChannelConfig
	.extern	debug_assert_handler
	.calls	'adc_init','',136
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L16:
	.word	120944
	.half	3
	.word	.L17
	.byte	4
.L15:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L18
	.byte	2,1,1,3
	.word	201
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	204
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	249
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	261
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	373
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	347
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	379
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	379
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	347
	.byte	6,0
.L80:
	.byte	7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L42:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	505
	.byte	4,2,35,0,0,14
	.word	795
	.byte	3
	.word	834
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	839
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	903
	.byte	4,2,35,0,0
.L36:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1305
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	999
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1265
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1496
	.byte	4,2,35,8,0,14
	.word	1536
	.byte	3
	.word	1599
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1604
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1039
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1604
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1039
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1039
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1604
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1834
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2150
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2721
	.byte	4,2,35,0,0,15,4
	.word	488
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2849
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3064
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3279
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3716
	.byte	4,2,35,0,0,15,24
	.word	488
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4039
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	488
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4343
	.byte	4,2,35,0,0,15,8
	.word	488
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4668
	.byte	4,2,35,0,0,15,12
	.word	488
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5008
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5374
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5660
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5807
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6148
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6323
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6497
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6671
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7003
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7336
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7684
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7808
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7892
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8072
	.byte	4,2,35,0,0,15,76
	.word	488
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8325
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8412
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2110
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2681
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2800
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2840
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3024
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3239
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3456
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3676
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2840
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3990
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4030
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4303
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4619
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4659
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4959
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4999
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5334
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5620
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4659
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5767
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5936
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6108
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6283
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6457
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6631
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6807
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6963
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7296
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7644
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4659
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7768
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8017
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8276
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8316
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8372
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8939
	.byte	4,3,35,252,1,0,14
	.word	8979
	.byte	3
	.word	9582
	.byte	17,7,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,7,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9587
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	488
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9592
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	9662
	.byte	6,0,17,7,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9587
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	488
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	9975
	.byte	6,0,17,10,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,9,141,6,31
	.word	10156
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,9,139,5,20
	.word	488
	.byte	1,1,6,0
.L60:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,9,161,6,19
	.word	10311
	.byte	1,1,5
	.byte	'address',0,9,161,6,55
	.word	1039
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,9,190,6,20
	.word	488
	.byte	1,1,5
	.byte	'address',0,9,190,6,70
	.word	1039
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,9,172,8,17,1,1,5
	.byte	'address',0,9,172,8,56
	.word	10311
	.byte	5
	.byte	'count',0,9,172,8,72
	.word	10311
	.byte	19,6,0,0,10
	.byte	'_Ifx_VADC_G_ARBCFG_Bits',0,12,191,1,16,4,11
	.byte	'ANONC',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'ARBRND',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ARBM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'ANONS',0,1
	.word	488
	.byte	2,6,2,35,2,11
	.byte	'CSRC',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	1039
	.byte	5,7,2,35,2,11
	.byte	'SYNRUN',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'CAL',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'CALS',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'SAMPLE',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,239,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_ARBPR_Bits',0,12,211,1,16,4,11
	.byte	'PRIO0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CSM0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PRIO1',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CSM1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PRIO2',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'CSM2',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	12,8,2,35,0,11
	.byte	'ASEN0',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'ASEN1',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'ASEN2',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,12,247,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_CHASS_Bits',0,12,156,3,16,4,11
	.byte	'ASSCH0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ASSCH1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ASSCH2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ASSCH3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ASSCH4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ASSCH5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ASSCH6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ASSCH7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ASSCH8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ASSCH9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'ASSCH10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'ASSCH11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'ASSCH12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'ASSCH13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'ASSCH14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ASSCH15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ASSCH16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'ASSCH17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'ASSCH18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'ASSCH19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'ASSCH20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'ASSCH21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'ASSCH22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'ASSCH23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'ASSCH24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'ASSCH25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'ASSCH26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'ASSCH27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'ASSCH28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'ASSCH29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'ASSCH30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ASSCH31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,231,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11218
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_RRASS_Bits',0,12,157,5,16,4,11
	.byte	'ASSRR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ASSRR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ASSRR2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ASSRR3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ASSRR4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ASSRR5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ASSRR6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ASSRR7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ASSRR8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ASSRR9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'ASSRR10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'ASSRR11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'ASSRR12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'ASSRR13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'ASSRR14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ASSRR15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11886
	.byte	4,2,35,0,0,15,16
	.word	488
	.byte	16,15,0,10
	.byte	'_Ifx_VADC_ICLASS_Bits',0,12,226,6,16,4,11
	.byte	'STCS',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'CMS',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'STCE',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'CME',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	5,0,2,35,3,0,12,12,223,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12282
	.byte	4,2,35,0,0,15,8
	.word	12464
	.byte	16,1,0,10
	.byte	'_Ifx_VADC_G_ALIAS_Bits',0,12,182,1,16,4,11
	.byte	'ALIAS0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'ALIAS1',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,4
	.word	465
	.byte	19,0,2,35,0,0,12,12,231,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_BOUND_Bits',0,12,205,2,16,4,11
	.byte	'BOUNDARY0',0,2
	.word	1039
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'BOUNDARY1',0,2
	.word	1039
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,191,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12664
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_SYNCTR_Bits',0,12,218,5,16,4,11
	.byte	'STSEL',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'EVALR1',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EVALR2',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EVALR3',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	465
	.byte	25,0,2,35,0,0,12,12,143,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12822
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_BFL_Bits',0,12,150,2,16,4,11
	.byte	'BFL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'BFL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'BFL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'BFL3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'BFA0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'BFA1',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'BFA2',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'BFA3',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'BFI0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'BFI1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'BFI2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'BFI3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,12,159,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13008
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_BFLS_Bits',0,12,190,2,16,4,11
	.byte	'BFC0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'BFC1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'BFC2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'BFC3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'BFS0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'BFS1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'BFS2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'BFS3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,12,183,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13336
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_BFLC_Bits',0,12,170,2,16,4,11
	.byte	'BFM0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'BFM1',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'BFM2',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'BFM3',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,167,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13578
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_BFLNP_Bits',0,12,180,2,16,4,11
	.byte	'BFL0NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'BFL1NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'BFL2NP',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'BFL3NP',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,175,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13734
	.byte	4,2,35,0,0,15,40
	.word	488
	.byte	16,39,0,10
	.byte	'_Ifx_VADC_G_QCTRL0_Bits',0,12,250,3,16,4,11
	.byte	'SRCRESREG',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'TMEN',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'TMWC',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,143,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13908
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_QMR0_Bits',0,12,151,4,16,4,11
	.byte	'ENGT',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'CLRV',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TREV',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'FLUSH',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'CEV',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	15,0,2,35,2,0,12,12,159,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14241
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_QSR0_Bits',0,12,166,4,16,4,11
	.byte	'FILL',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EMPTY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EV',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,12,12,167,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14492
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_Q0R0_Bits',0,12,228,3,16,4,11
	.byte	'REQCHNR',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'V',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,12,12,255,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14691
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_QBUR0_Bits',0,12,239,3,16,4,11
	.byte	'REQCHNR',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'V',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,12,12,135,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14860
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_QINR0_Bits',0,12,141,4,16,4,11
	.byte	'REQCHNR',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'RF',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ENSI',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EXTR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,12,151,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15030
	.byte	4,2,35,0,0,12,12,175,11,5,4,13
	.byte	'QBUR0',0
	.word	14990
	.byte	4,2,35,0,13
	.byte	'QINR0',0
	.word	15147
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_ASCTRL_Bits',0,12,230,1,16,4,11
	.byte	'SRCRESREG',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'TMEN',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'TMWC',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,255,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15224
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_ASMR_Bits',0,12,249,1,16,4,11
	.byte	'ENGT',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ENSI',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SCAN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'LDM',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CLRPND',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'LDEV',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	15,0,2,35,2,0,12,12,135,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15557
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_ASSEL_Bits',0,12,144,2,16,4,11
	.byte	'CHSEL',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,151,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15842
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_ASPND_Bits',0,12,138,2,16,4,11
	.byte	'CHPND',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,143,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15929
	.byte	4,2,35,0,0,15,80
	.word	488
	.byte	16,79,0,10
	.byte	'_Ifx_VADC_G_CEFLAG_Bits',0,12,236,2,16,4,11
	.byte	'CEV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CEV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CEV2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CEV3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CEV4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CEV5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CEV6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CEV7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CEV8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'CEV9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'CEV10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'CEV11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CEV12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CEV13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CEV14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'CEV15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,207,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16025
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_REFLAG_Bits',0,12,213,4,16,4,11
	.byte	'REV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'REV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'REV2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'REV3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'REV4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'REV5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'REV6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'REV7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'REV8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'REV9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'REV10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'REV11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'REV12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'REV13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'REV14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'REV15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,191,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_SEFLAG_Bits',0,12,187,5,16,4,11
	.byte	'SEV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SEV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,12,247,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_CEFCLR_Bits',0,12,214,2,16,4,11
	.byte	'CEV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CEV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CEV2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CEV3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CEV4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CEV5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CEV6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CEV7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CEV8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'CEV9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'CEV10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'CEV11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CEV12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CEV13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CEV14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'CEV15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,199,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16862
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_REFCLR_Bits',0,12,191,4,16,4,11
	.byte	'REV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'REV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'REV2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'REV3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'REV4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'REV5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'REV6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'REV7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'REV8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'REV9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'REV10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'REV11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'REV12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'REV13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'REV14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'REV15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,183,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17218
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_SEFCLR_Bits',0,12,179,5,16,4,11
	.byte	'SEV0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SEV1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,12,239,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17574
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_CEVNP0_Bits',0,12,130,3,16,4,11
	.byte	'CEV0NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'CEV1NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'CEV2NP',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'CEV3NP',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'CEV4NP',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CEV5NP',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'CEV6NP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'CEV7NP',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,215,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17699
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_CEVNP1_Bits',0,12,143,3,16,4,11
	.byte	'CEV8NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'CEV9NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'CEV10NP',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'CEV11NP',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'CEV12NP',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CEV13NP',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'CEV14NP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'CEV15NP',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,223,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_REVNP0_Bits',0,12,131,5,16,4,11
	.byte	'REV0NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'REV1NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'REV2NP',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'REV3NP',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'REV4NP',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'REV5NP',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'REV6NP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'REV7NP',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18135
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_REVNP1_Bits',0,12,144,5,16,4,11
	.byte	'REV8NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'REV9NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'REV10NP',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'REV11NP',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'REV12NP',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'REV13NP',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'REV14NP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'REV15NP',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18350
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_SEVNP_Bits',0,12,195,5,16,4,11
	.byte	'SEV0NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'SEV1NP',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,12,255,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_SRACT_Bits',0,12,203,5,16,4,11
	.byte	'AGSR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'AGSR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'AGSR2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'AGSR3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'ASSR0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ASSR1',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'ASSR2',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'ASSR3',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,12,12,135,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18699
	.byte	4,2,35,0,0,15,36
	.word	488
	.byte	16,35,0,10
	.byte	'_Ifx_VADC_G_EMUXCTR_Bits',0,12,213,3,16,4,11
	.byte	'EMUXSET',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'EMUXACT',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'EMUXCH',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'EMUXMODE',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'EMXCOD',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EMXST',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EMXCSS',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EMXWC',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,247,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18959
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_VFR_Bits',0,12,229,5,16,4,11
	.byte	'VF0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VF1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VF2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'VF3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'VF4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'VF5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'VF6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'VF7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'VF8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'VF9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'VF10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'VF11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'VF12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'VF13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'VF14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'VF15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,151,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19222
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_G_CHCTR_Bits',0,12,193,3,16,4,11
	.byte	'ICLSEL',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'BNDSELL',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'BNDSELU',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CHEVMODE',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'SYNC',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'REFSEL',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'BNDSELX',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'RESREG',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'RESTBS',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'RESPOS',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	6,4,2,35,2,11
	.byte	'BWDCH',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'BWDEN',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,239,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19559
	.byte	4,2,35,0,0,15,64
	.word	19874
	.byte	16,15,0,15,64
	.word	488
	.byte	16,63,0,10
	.byte	'_Ifx_VADC_G_RCR_Bits',0,12,178,4,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'DRCTR',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'DMM',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'WFR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'FEN',0,1
	.word	488
	.byte	2,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'SRGEN',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,175,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19932
	.byte	4,2,35,0,0,15,64
	.word	20107
	.byte	16,15,0,10
	.byte	'_Ifx_VADC_G_RES_Bits',0,12,235,4,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'DRC',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	1039
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	488
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	488
	.byte	1,0,2,35,3,0
.L40:
	.byte	12,12,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20156
	.byte	4,2,35,0,0,15,64
	.word	20293
	.byte	16,15,0,10
	.byte	'_Ifx_VADC_G_RESD_Bits',0,12,247,4,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'DRC',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	1039
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	488
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20342
	.byte	4,2,35,0,0,15,64
	.word	20480
	.byte	16,15,0,15,192,1
	.word	488
	.byte	16,191,1,0,10
	.byte	'_Ifx_VADC_G',0,12,151,11,25,128,8,13
	.byte	'ARBCFG',0
	.word	10845
	.byte	4,2,35,0,13
	.byte	'ARBPR',0
	.word	11178
	.byte	4,2,35,4,13
	.byte	'CHASS',0
	.word	11846
	.byte	4,2,35,8,13
	.byte	'RRASS',0
	.word	12233
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	12273
	.byte	16,2,35,16,13
	.byte	'ICLASS',0
	.word	12504
	.byte	8,2,35,32,13
	.byte	'reserved_28',0
	.word	4659
	.byte	8,2,35,40,13
	.byte	'ALIAS',0
	.word	12624
	.byte	4,2,35,48,13
	.byte	'reserved_34',0
	.word	2840
	.byte	4,2,35,52,13
	.byte	'BOUND',0
	.word	12782
	.byte	4,2,35,56,13
	.byte	'reserved_3C',0
	.word	2840
	.byte	4,2,35,60,13
	.byte	'SYNCTR',0
	.word	12968
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	2840
	.byte	4,2,35,68,13
	.byte	'BFL',0
	.word	13296
	.byte	4,2,35,72,13
	.byte	'BFLS',0
	.word	13538
	.byte	4,2,35,76,13
	.byte	'BFLC',0
	.word	13694
	.byte	4,2,35,80,13
	.byte	'BFLNP',0
	.word	13859
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	13899
	.byte	40,2,35,88,13
	.byte	'QCTRL0',0
	.word	14201
	.byte	4,3,35,128,1,13
	.byte	'QMR0',0
	.word	14452
	.byte	4,3,35,132,1,13
	.byte	'QSR0',0
	.word	14651
	.byte	4,3,35,136,1,13
	.byte	'Q0R0',0
	.word	14820
	.byte	4,3,35,140,1,20
	.word	15187
	.byte	4,3,35,144,1,13
	.byte	'reserved_94',0
	.word	4999
	.byte	12,3,35,148,1,13
	.byte	'ASCTRL',0
	.word	15517
	.byte	4,3,35,160,1,13
	.byte	'ASMR',0
	.word	15802
	.byte	4,3,35,164,1,13
	.byte	'ASSEL',0
	.word	15889
	.byte	4,3,35,168,1,13
	.byte	'ASPND',0
	.word	15976
	.byte	4,3,35,172,1,13
	.byte	'reserved_B0',0
	.word	16016
	.byte	80,3,35,176,1,13
	.byte	'CEFLAG',0
	.word	16341
	.byte	4,3,35,128,2,13
	.byte	'REFLAG',0
	.word	16697
	.byte	4,3,35,132,2,13
	.byte	'SEFLAG',0
	.word	16822
	.byte	4,3,35,136,2,13
	.byte	'reserved_10C',0
	.word	2840
	.byte	4,3,35,140,2,13
	.byte	'CEFCLR',0
	.word	17178
	.byte	4,3,35,144,2,13
	.byte	'REFCLR',0
	.word	17534
	.byte	4,3,35,148,2,13
	.byte	'SEFCLR',0
	.word	17659
	.byte	4,3,35,152,2,13
	.byte	'reserved_11C',0
	.word	2840
	.byte	4,3,35,156,2,13
	.byte	'CEVNP0',0
	.word	17874
	.byte	4,3,35,160,2,13
	.byte	'CEVNP1',0
	.word	18095
	.byte	4,3,35,164,2,13
	.byte	'reserved_128',0
	.word	4659
	.byte	8,3,35,168,2,13
	.byte	'REVNP0',0
	.word	18310
	.byte	4,3,35,176,2,13
	.byte	'REVNP1',0
	.word	18531
	.byte	4,3,35,180,2,13
	.byte	'reserved_138',0
	.word	4659
	.byte	8,3,35,184,2,13
	.byte	'SEVNP',0
	.word	18659
	.byte	4,3,35,192,2,13
	.byte	'reserved_144',0
	.word	2840
	.byte	4,3,35,196,2,13
	.byte	'SRACT',0
	.word	18910
	.byte	4,3,35,200,2,13
	.byte	'reserved_14C',0
	.word	18950
	.byte	36,3,35,204,2,13
	.byte	'EMUXCTR',0
	.word	19182
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	2840
	.byte	4,3,35,244,2,13
	.byte	'VFR',0
	.word	19519
	.byte	4,3,35,248,2,13
	.byte	'reserved_17C',0
	.word	2840
	.byte	4,3,35,252,2,13
	.byte	'CHCTR',0
	.word	19914
	.byte	64,3,35,128,3,13
	.byte	'reserved_1C0',0
	.word	19923
	.byte	64,3,35,192,3,13
	.byte	'RCR',0
	.word	20147
	.byte	64,3,35,128,4,13
	.byte	'reserved_240',0
	.word	19923
	.byte	64,3,35,192,4,13
	.byte	'RES',0
	.word	20333
	.byte	64,3,35,128,5,13
	.byte	'reserved_2C0',0
	.word	19923
	.byte	64,3,35,192,5,13
	.byte	'RESD',0
	.word	20520
	.byte	64,3,35,128,6,13
	.byte	'reserved_340',0
	.word	20529
	.byte	192,1,3,35,192,6,0,14
	.word	20540
	.byte	3
	.word	21615
	.byte	4
	.byte	'IfxVadc_configureWaitForReadMode',0,3,11,238,13,17,1,1,5
	.byte	'group',0,11,238,13,62
	.word	21620
	.byte	5
	.byte	'resultIdx',0,11,238,13,76
	.word	10311
	.byte	5
	.byte	'waitForRead',0,11,238,13,95
	.word	488
	.byte	6,0,10
	.byte	'_Ifx_VADC_CLC_Bits',0,12,164,1,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,12,12,215,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21723
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_ID_Bits',0,12,239,6,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,12,12,231,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21881
	.byte	4,2,35,0,0,15,28
	.word	488
	.byte	16,27,0,10
	.byte	'_Ifx_VADC_OCS_Bits',0,12,141,7,16,4,11
	.byte	'TGS',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0,12,12,135,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22013
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_KRSTCLR_Bits',0,12,134,7,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,255,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22220
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_KRST1_Bits',0,12,255,6,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,12,12,247,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22327
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_KRST0_Bits',0,12,247,6,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,12,12,239,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22432
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_ACCEN0_Bits',0,12,49,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,159,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_GLOBCFG_Bits',0,12,132,6,16,4,11
	.byte	'DIVA',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'DCMSB',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'DIVD',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'REFPC',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'LOSUP',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'DIVWC',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'DPCAL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DPCAL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DPCAL2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'DPCAL3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	11,1,2,35,2,11
	.byte	'SUCAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,167,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23126
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_ACCPROT0_Bits',0,12,86,16,4,11
	.byte	'APC0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'APC1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'APC2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'APC3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	11,1,2,35,0,11
	.byte	'APEM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'API0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'API1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'API2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'API3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	11,1,2,35,2,11
	.byte	'APGC',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,167,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23476
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_ACCPROT1_Bits',0,12,103,16,4,11
	.byte	'APS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'APS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'APS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'APS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	11,1,2,35,0,11
	.byte	'APTF',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'APR0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'APR1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'APR2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'APR3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,12,175,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23751
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_GLOBBOUND_Bits',0,12,251,5,16,4,11
	.byte	'BOUNDARY0',0,2
	.word	1039
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'BOUNDARY1',0,2
	.word	1039
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,12,12,159,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24010
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_GLOBEFLAG_Bits',0,12,152,6,16,4,11
	.byte	'SEVGLB',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	7,0,2,35,0,11
	.byte	'REVGLB',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'SEVGLBCLR',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	7,0,2,35,2,11
	.byte	'REVGLBCLR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	7,0,2,35,3,0,12,12,175,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24170
	.byte	4,2,35,0,0,15,92
	.word	488
	.byte	16,91,0,10
	.byte	'_Ifx_VADC_GLOBEVNP_Bits',0,12,165,6,16,4,11
	.byte	'SEV0NP',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'REV0NP',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1039
	.byte	12,0,2,35,2,0,12,12,183,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24419
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_GLOBTF_Bits',0,12,209,6,16,4,11
	.byte	'CDCH',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'CDGR',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'CDEN',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'CDSEL',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	4,1,2,35,1,11
	.byte	'CDWC',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PDD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'MDPD',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'MDPU',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	4,1,2,35,2,11
	.byte	'MDWC',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,215,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_BRSSEL_Bits',0,12,158,1,16,4,11
	.byte	'CHSELGy',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,207,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24853
	.byte	4,2,35,0,0,15,16
	.word	24901
	.byte	16,3,0,15,48
	.word	488
	.byte	16,47,0,10
	.byte	'_Ifx_VADC_BRSPND_Bits',0,12,152,1,16,4,11
	.byte	'CHPNDGy',0,4
	.word	465
	.byte	32,0,2,35,0,0,12,12,199,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24959
	.byte	4,2,35,0,0,15,16
	.word	25007
	.byte	16,3,0,10
	.byte	'_Ifx_VADC_BRSCTRL_Bits',0,12,119,16,4,11
	.byte	'SRCRESREG',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'XTSEL',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'XTLVL',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'XTMODE',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'XTWC',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'GTSEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'GTLVL',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'GTWC',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,12,12,183,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25056
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_BRSMR_Bits',0,12,135,1,16,4,11
	.byte	'ENGT',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ENTR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ENSI',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SCAN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'LDM',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'REQGT',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CLRPND',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'LDEV',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'RPTDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	15,0,2,35,2,0,12,12,191,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25332
	.byte	4,2,35,0,0,15,120
	.word	488
	.byte	16,119,0,10
	.byte	'_Ifx_VADC_GLOBRCR_Bits',0,12,174,6,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'DRCTR',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'WFR',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	6,1,2,35,3,11
	.byte	'SRGEN',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,191,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25625
	.byte	4,2,35,0,0,15,124
	.word	488
	.byte	16,123,0,10
	.byte	'_Ifx_VADC_GLOBRES_Bits',0,12,185,6,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'GNR',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	1039
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	488
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,199,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25821
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_VADC_GLOBRESD_Bits',0,12,197,6,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'GNR',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'CHNR',0,2
	.word	1039
	.byte	5,7,2,35,2,11
	.byte	'EMUX',0,1
	.word	488
	.byte	3,4,2,35,3,11
	.byte	'CRS',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'FCR',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'VF',0,1
	.word	488
	.byte	1,0,2,35,3,0,12,12,207,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26000
	.byte	4,2,35,0,0,15,108
	.word	488
	.byte	16,107,0,10
	.byte	'_Ifx_VADC_EMUXSEL_Bits',0,12,174,1,16,4,11
	.byte	'EMUXGRP0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'EMUXGRP1',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,12,12,223,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26189
	.byte	4,2,35,0,0,15,140,1
	.word	488
	.byte	16,139,1,0,15,128,32
	.word	20540
	.byte	16,3,0,14
	.word	26332
	.byte	15,128,87
	.word	488
	.byte	16,255,86,0,10
	.byte	'_Ifx_VADC',0,12,230,11,25,128,128,1,13
	.byte	'CLC',0
	.word	21841
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2840
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	21964
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	22004
	.byte	28,2,35,12,13
	.byte	'OCS',0
	.word	22180
	.byte	4,2,35,40,13
	.byte	'KRSTCLR',0
	.word	22287
	.byte	4,2,35,44,13
	.byte	'KRST1',0
	.word	22392
	.byte	4,2,35,48,13
	.byte	'KRST0',0
	.word	22516
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	2840
	.byte	4,2,35,56,13
	.byte	'ACCEN0',0
	.word	23086
	.byte	4,2,35,60,13
	.byte	'reserved_40',0
	.word	19923
	.byte	64,2,35,64,13
	.byte	'GLOBCFG',0
	.word	23436
	.byte	4,3,35,128,1,13
	.byte	'reserved_84',0
	.word	2840
	.byte	4,3,35,132,1,13
	.byte	'ACCPROT0',0
	.word	23711
	.byte	4,3,35,136,1,13
	.byte	'ACCPROT1',0
	.word	23970
	.byte	4,3,35,140,1,13
	.byte	'reserved_90',0
	.word	12273
	.byte	16,3,35,144,1,13
	.byte	'GLOBICLASS',0
	.word	12504
	.byte	8,3,35,160,1,13
	.byte	'reserved_A8',0
	.word	12273
	.byte	16,3,35,168,1,13
	.byte	'GLOBBOUND',0
	.word	24130
	.byte	4,3,35,184,1,13
	.byte	'reserved_BC',0
	.word	18950
	.byte	36,3,35,188,1,13
	.byte	'GLOBEFLAG',0
	.word	24370
	.byte	4,3,35,224,1,13
	.byte	'reserved_E4',0
	.word	24410
	.byte	92,3,35,228,1,13
	.byte	'GLOBEVNP',0
	.word	24531
	.byte	4,3,35,192,2,13
	.byte	'reserved_144',0
	.word	22004
	.byte	28,3,35,196,2,13
	.byte	'GLOBTF',0
	.word	24813
	.byte	4,3,35,224,2,13
	.byte	'reserved_164',0
	.word	22004
	.byte	28,3,35,228,2,13
	.byte	'BRSSEL',0
	.word	24941
	.byte	16,3,35,128,3,13
	.byte	'reserved_190',0
	.word	24950
	.byte	48,3,35,144,3,13
	.byte	'BRSPND',0
	.word	25047
	.byte	16,3,35,192,3,13
	.byte	'reserved_1D0',0
	.word	24950
	.byte	48,3,35,208,3,13
	.byte	'BRSCTRL',0
	.word	25292
	.byte	4,3,35,128,4,13
	.byte	'BRSMR',0
	.word	25576
	.byte	4,3,35,132,4,13
	.byte	'reserved_208',0
	.word	25616
	.byte	120,3,35,136,4,13
	.byte	'GLOBRCR',0
	.word	25772
	.byte	4,3,35,128,5,13
	.byte	'reserved_284',0
	.word	25812
	.byte	124,3,35,132,5,13
	.byte	'GLOBRES',0
	.word	25960
	.byte	4,3,35,128,6,13
	.byte	'reserved_304',0
	.word	25812
	.byte	124,3,35,132,6,13
	.byte	'GLOBRESD',0
	.word	26140
	.byte	4,3,35,128,7,13
	.byte	'reserved_384',0
	.word	26180
	.byte	108,3,35,132,7,13
	.byte	'EMUXSEL',0
	.word	26281
	.byte	4,3,35,240,7,13
	.byte	'reserved_3F4',0
	.word	26321
	.byte	140,1,3,35,244,7,13
	.byte	'G',0
	.word	26342
	.byte	128,32,3,35,128,9,13
	.byte	'reserved_1480',0
	.word	26347
	.byte	128,87,3,35,128,41,0,14
	.word	26358
	.byte	3
	.word	27210
	.byte	4
	.byte	'IfxVadc_configureWaitForReadModeForGlobalResultRegister',0,3,11,244,13,17,1,1,5
	.byte	'vadc',0,11,244,13,83
	.word	27215
	.byte	5
	.byte	'waitForRead',0,11,244,13,97
	.word	488
	.byte	6,0,17,11,173,1,9,1,18
	.byte	'IfxVadc_ChannelResolution_12bit',0,0,18
	.byte	'IfxVadc_ChannelResolution_10bit',0,1,18
	.byte	'IfxVadc_ChannelResolution_8bit',0,2,18
	.byte	'IfxVadc_ChannelResolution_10bitFast',0,5,0,8
	.byte	'IfxVadc_getGlobalResolution',0,3,11,191,15,38
	.word	27321
	.byte	1,1,5
	.byte	'vadc',0,11,191,15,76
	.word	27215
	.byte	5
	.byte	'inputClassNum',0,11,191,15,88
	.word	488
	.byte	6,0,8
	.byte	'IfxVadc_getGlobalResult',0,3,11,197,15,29
	.word	25960
	.byte	1,1,5
	.byte	'vadc',0,11,197,15,63
	.word	27215
	.byte	6,0,8
	.byte	'IfxVadc_getGlobalSampleTime',0,3,11,207,15,20
	.word	261
	.byte	1,1,5
	.byte	'vadc',0,11,207,15,58
	.word	27215
	.byte	5
	.byte	'inputClassNum',0,11,207,15,70
	.word	488
	.byte	5
	.byte	'analogFrequency',0,11,207,15,93
	.word	261
	.byte	6,0,3
	.word	20293
	.byte	4
	.byte	'IfxVadc_getGroupResult',0,3,11,226,15,17,1,1,5
	.byte	'group',0,11,226,15,52
	.word	21620
	.byte	5
	.byte	'results',0,11,226,15,73
	.word	27702
	.byte	5
	.byte	'resultOffset',0,11,226,15,89
	.word	10311
	.byte	5
	.byte	'numResults',0,11,226,15,110
	.word	10311
	.byte	6,0
.L44:
	.byte	8
	.byte	'IfxVadc_getResult',0,3,11,166,16,25
	.word	20293
	.byte	1,1
.L47:
	.byte	5
	.byte	'group',0,11,166,16,55
	.word	21620
.L49:
	.byte	5
	.byte	'resultIdx',0,11,166,16,69
	.word	10311
.L51:
	.byte	6,0,17,13,119,9,1,18
	.byte	'IfxVadc_GroupId_0',0,0,18
	.byte	'IfxVadc_GroupId_1',0,1,18
	.byte	'IfxVadc_GroupId_2',0,2,18
	.byte	'IfxVadc_GroupId_3',0,3,18
	.byte	'IfxVadc_GroupId_global0',0,4,18
	.byte	'IfxVadc_GroupId_global1',0,5,0
.L95:
	.byte	4
	.byte	'IfxVadc_setBackgroundScan',0,3,11,182,17,17,1,1
.L97:
	.byte	5
	.byte	'vadc',0,11,182,17,53
	.word	27215
.L99:
	.byte	5
	.byte	'groupId',0,11,182,17,75
	.word	27880
.L101:
	.byte	5
	.byte	'channels',0,11,182,17,91
	.word	10311
.L103:
	.byte	5
	.byte	'mask',0,11,182,17,108
	.word	10311
.L105:
	.byte	6,0
.L112:
	.byte	4
	.byte	'IfxVadc_startBackgroundScan',0,3,11,158,19,17,1,1
.L114:
	.byte	5
	.byte	'vadc',0,11,158,19,55
	.word	27215
.L116:
	.byte	6,0,8
	.byte	'IfxVadc_getDebugResult',0,3,11,182,19,26
	.word	20480
	.byte	1,1,5
	.byte	'group',0,11,182,19,61
	.word	21620
	.byte	5
	.byte	'resultIdx',0,11,182,19,75
	.word	10311
	.byte	6,0,4
	.byte	'IfxVadc_startScan',0,3,11,170,19,17,1,1,5
	.byte	'group',0,11,170,19,47
	.word	21620
	.byte	6,0,17,11,142,1,9,1,18
	.byte	'IfxVadc_ChannelId_none',0,127,18
	.byte	'IfxVadc_ChannelId_0',0,0,18
	.byte	'IfxVadc_ChannelId_1',0,1,18
	.byte	'IfxVadc_ChannelId_2',0,2,18
	.byte	'IfxVadc_ChannelId_3',0,3,18
	.byte	'IfxVadc_ChannelId_4',0,4,18
	.byte	'IfxVadc_ChannelId_5',0,5,18
	.byte	'IfxVadc_ChannelId_6',0,6,18
	.byte	'IfxVadc_ChannelId_7',0,7,18
	.byte	'IfxVadc_ChannelId_8',0,8,18
	.byte	'IfxVadc_ChannelId_9',0,9,18
	.byte	'IfxVadc_ChannelId_10',0,10,18
	.byte	'IfxVadc_ChannelId_11',0,11,18
	.byte	'IfxVadc_ChannelId_12',0,12,18
	.byte	'IfxVadc_ChannelId_13',0,13,18
	.byte	'IfxVadc_ChannelId_14',0,14,18
	.byte	'IfxVadc_ChannelId_15',0,15,0,4
	.byte	'IfxVadc_addToQueue',0,3,11,197,13,17,1,1,5
	.byte	'group',0,11,197,13,48
	.word	21620
	.byte	5
	.byte	'channel',0,11,197,13,73
	.word	28283
	.byte	5
	.byte	'options',0,11,197,13,89
	.word	10311
	.byte	6,0,4
	.byte	'IfxVadc_clearQueue',0,3,11,232,13,17,1,1,5
	.byte	'vadcG',0,11,232,13,48
	.word	21620
	.byte	5
	.byte	'flushQueue',0,11,232,13,63
	.word	488
	.byte	6,0,4
	.byte	'IfxVadc_startQueue',0,3,11,164,19,17,1,1,5
	.byte	'group',0,11,164,19,48
	.word	21620
	.byte	6,0,8
	.byte	'IfxVadc_calculateSampleTime',0,3,11,203,13,19
	.word	10311
	.byte	1,1,5
	.byte	'analogFrequency',0,11,203,13,55
	.word	261
	.byte	5
	.byte	'sampleTime',0,11,203,13,80
	.word	261
	.byte	6,0,8
	.byte	'IfxVadc_getStartupCalibration',0,3,11,212,16,20
	.word	488
	.byte	1,1,5
	.byte	'vadc',0,11,212,16,60
	.word	27215
	.byte	6,0
.L66:
	.byte	21,14,252,2,9,4,13
	.byte	'vadc',0
	.word	27215
	.byte	4,2,35,0,0,3
	.word	29004
.L68:
	.byte	21,14,173,3,9,12,13
	.byte	'module',0
	.word	29004
	.byte	4,2,35,0,13
	.byte	'group',0
	.word	21620
	.byte	4,2,35,4,13
	.byte	'groupId',0
	.word	27880
	.byte	1,2,35,8,0,3
	.word	29030
.L83:
	.byte	4
	.byte	'IfxVadc_Adc_setBackgroundScan',0,3,14,253,9,17,1,1
.L86:
	.byte	5
	.byte	'vadc',0,14,253,9,60
	.word	29025
.L88:
	.byte	5
	.byte	'group',0,14,253,9,85
	.word	29085
.L90:
	.byte	5
	.byte	'channels',0,14,253,9,99
	.word	10311
.L92:
	.byte	5
	.byte	'mask',0,14,253,9,116
	.word	10311
.L94:
	.byte	19,6,0,0
.L106:
	.byte	4
	.byte	'IfxVadc_Adc_startBackgroundScan',0,3,14,137,10,17,1,1
.L109:
	.byte	5
	.byte	'vadc',0,14,137,10,62
	.word	29025
.L111:
	.byte	19,6,0,0,22
	.word	209
	.byte	23
	.word	235
	.byte	6,0,22
	.word	270
	.byte	23
	.word	302
	.byte	6,0,22
	.word	315
	.byte	6,0,22
	.word	384
	.byte	23
	.word	403
	.byte	6,0,22
	.word	419
	.byte	23
	.word	434
	.byte	23
	.word	448
	.byte	6,0,22
	.word	844
	.byte	23
	.word	872
	.byte	6,0,22
	.word	1609
	.byte	23
	.word	1649
	.byte	23
	.word	1667
	.byte	6,0,22
	.word	1687
	.byte	23
	.word	1725
	.byte	23
	.word	1743
	.byte	6,0,22
	.word	1763
	.byte	23
	.word	1814
	.byte	6,0,22
	.word	9879
	.byte	23
	.word	9912
	.byte	23
	.word	9926
	.byte	23
	.word	9944
	.byte	23
	.word	9958
	.byte	6,0,22
	.word	10078
	.byte	23
	.word	10106
	.byte	23
	.word	10120
	.byte	23
	.word	10138
	.byte	6,0,22
	.word	10235
	.byte	6,0,22
	.word	10269
	.byte	6,0,22
	.word	10332
	.byte	23
	.word	10373
	.byte	6,0,22
	.word	10392
	.byte	23
	.word	10447
	.byte	6,0,22
	.word	10466
	.byte	23
	.word	10506
	.byte	23
	.word	10523
	.byte	19,6,0,0,22
	.word	21625
	.byte	23
	.word	21666
	.byte	23
	.word	21681
	.byte	23
	.word	21700
	.byte	6,0,22
	.word	27220
	.byte	23
	.word	27284
	.byte	23
	.word	27298
	.byte	6,0,22
	.word	27467
	.byte	23
	.word	27507
	.byte	23
	.word	27521
	.byte	6,0,22
	.word	27546
	.byte	23
	.word	27582
	.byte	6,0,22
	.word	27598
	.byte	23
	.word	27638
	.byte	23
	.word	27652
	.byte	23
	.word	27675
	.byte	6,0,22
	.word	27707
	.byte	23
	.word	27738
	.byte	23
	.word	27753
	.byte	23
	.word	27770
	.byte	23
	.word	27792
	.byte	6,0,22
	.word	27814
	.byte	23
	.word	27844
	.byte	23
	.word	27859
	.byte	6,0,22
	.word	28018
	.byte	23
	.word	28052
	.byte	23
	.word	28066
	.byte	23
	.word	28083
	.byte	23
	.word	28101
	.byte	6,0,22
	.word	28117
	.byte	23
	.word	28153
	.byte	6,0,22
	.word	28169
	.byte	23
	.word	28204
	.byte	23
	.word	28219
	.byte	6,0,22
	.word	28240
	.byte	23
	.word	28266
	.byte	6,0,22
	.word	28673
	.byte	23
	.word	28700
	.byte	23
	.word	28715
	.byte	23
	.word	28732
	.byte	6,0,22
	.word	28751
	.byte	23
	.word	28778
	.byte	23
	.word	28793
	.byte	6,0,22
	.word	28815
	.byte	23
	.word	28842
	.byte	6,0,22
	.word	28859
	.byte	23
	.word	28899
	.byte	23
	.word	28924
	.byte	6,0,22
	.word	28946
	.byte	23
	.word	28988
	.byte	6,0,17,11,209,3,9,1,18
	.byte	'IfxVadc_Status_noError',0,0,18
	.byte	'IfxVadc_Status_notInitialised',0,1,18
	.byte	'IfxVadc_Status_invalidGroup',0,2,18
	.byte	'IfxVadc_Status_invalidChannel',0,3,18
	.byte	'IfxVadc_Status_queueFull',0,4,18
	.byte	'IfxVadc_Status_noAccess',0,5,18
	.byte	'IfxVadc_Status_channelsStillPending',0,6,0,21,14,165,3,9,6,13
	.byte	'sampleTime',0
	.word	261
	.byte	4,2,35,0,13
	.byte	'resolution',0
	.word	27321
	.byte	1,2,35,4,0,15,12
	.word	29974
	.byte	16,1,0,17,11,208,2,9,1,18
	.byte	'IfxVadc_LowSupplyVoltageSelect_5V',0,0,18
	.byte	'IfxVadc_LowSupplyVoltageSelect_3V',0,1,0
.L70:
	.byte	21,14,247,3,9,32,13
	.byte	'vadc',0
	.word	27215
	.byte	4,2,35,0,13
	.byte	'globalInputClass',0
	.word	30021
	.byte	12,2,35,4,13
	.byte	'digitalFrequency',0
	.word	261
	.byte	4,2,35,16,13
	.byte	'analogFrequency',0
	.word	261
	.byte	4,2,35,20,13
	.byte	'moduleFrequency',0
	.word	261
	.byte	4,2,35,24,13
	.byte	'startupCalibration',0
	.word	488
	.byte	1,2,35,28,13
	.byte	'supplyVoltage',0
	.word	30030
	.byte	1,2,35,29,0,24
	.word	30109
	.byte	3
	.word	30283
	.byte	25
	.byte	'IfxVadc_Adc_initModule',0,14,150,5,27
	.word	29757
	.byte	1,1,1,1,5
	.byte	'vadc',0,14,150,5,63
	.word	29025
	.byte	5
	.byte	'config',0,14,150,5,95
	.word	30288
	.byte	0,3
	.word	30109
	.byte	26
	.byte	'IfxVadc_Adc_initModuleConfig',0,14,160,5,17,1,1,1,1,5
	.byte	'config',0,14,160,5,66
	.word	30360
	.byte	5
	.byte	'vadc',0,14,160,5,84
	.word	27215
	.byte	0,24
	.word	29004
	.byte	3
	.word	30434
	.byte	17,11,166,2,9,1,18
	.byte	'IfxVadc_GatingSource_0',0,0,18
	.byte	'IfxVadc_GatingSource_1',0,1,18
	.byte	'IfxVadc_GatingSource_2',0,2,18
	.byte	'IfxVadc_GatingSource_3',0,3,18
	.byte	'IfxVadc_GatingSource_4',0,4,18
	.byte	'IfxVadc_GatingSource_5',0,5,18
	.byte	'IfxVadc_GatingSource_6',0,6,18
	.byte	'IfxVadc_GatingSource_7',0,7,18
	.byte	'IfxVadc_GatingSource_8',0,8,18
	.byte	'IfxVadc_GatingSource_9',0,9,18
	.byte	'IfxVadc_GatingSource_10',0,10,18
	.byte	'IfxVadc_GatingSource_11',0,11,18
	.byte	'IfxVadc_GatingSource_12',0,12,18
	.byte	'IfxVadc_GatingSource_13',0,13,18
	.byte	'IfxVadc_GatingSource_14',0,14,18
	.byte	'IfxVadc_GatingSource_15',0,15,0,17,11,232,3,9,1,18
	.byte	'IfxVadc_TriggerSource_0',0,0,18
	.byte	'IfxVadc_TriggerSource_1',0,1,18
	.byte	'IfxVadc_TriggerSource_2',0,2,18
	.byte	'IfxVadc_TriggerSource_3',0,3,18
	.byte	'IfxVadc_TriggerSource_4',0,4,18
	.byte	'IfxVadc_TriggerSource_5',0,5,18
	.byte	'IfxVadc_TriggerSource_6',0,6,18
	.byte	'IfxVadc_TriggerSource_7',0,7,18
	.byte	'IfxVadc_TriggerSource_8',0,8,18
	.byte	'IfxVadc_TriggerSource_9',0,9,18
	.byte	'IfxVadc_TriggerSource_10',0,10,18
	.byte	'IfxVadc_TriggerSource_11',0,11,18
	.byte	'IfxVadc_TriggerSource_12',0,12,18
	.byte	'IfxVadc_TriggerSource_13',0,13,18
	.byte	'IfxVadc_TriggerSource_14',0,14,18
	.byte	'IfxVadc_TriggerSource_15',0,15,0,17,11,156,2,9,1,18
	.byte	'IfxVadc_GatingMode_disabled',0,0,18
	.byte	'IfxVadc_GatingMode_always',0,1,18
	.byte	'IfxVadc_GatingMode_gatingHigh',0,2,18
	.byte	'IfxVadc_GatingMode_gatingLow',0,3,0,17,11,222,3,9,1,18
	.byte	'IfxVadc_TriggerMode_noExternalTrigger',0,0,18
	.byte	'IfxVadc_TriggerMode_uponFallingEdge',0,1,18
	.byte	'IfxVadc_TriggerMode_uponRisingEdge',0,2,18
	.byte	'IfxVadc_TriggerMode_uponAnyEdge',0,3,0,21,14,131,3,9,4,13
	.byte	'gatingSource',0
	.word	30444
	.byte	1,2,35,0,13
	.byte	'triggerSource',0
	.word	30857
	.byte	1,2,35,1,13
	.byte	'gatingMode',0
	.word	31286
	.byte	1,2,35,2,13
	.byte	'triggerMode',0
	.word	31414
	.byte	1,2,35,3,0,17,11,157,3,9,1,18
	.byte	'IfxVadc_RequestSlotPriority_lowest',0,0,18
	.byte	'IfxVadc_RequestSlotPriority_low',0,1,18
	.byte	'IfxVadc_RequestSlotPriority_high',0,2,18
	.byte	'IfxVadc_RequestSlotPriority_highest',0,3,0,17,11,167,3,9,1,18
	.byte	'IfxVadc_RequestSlotStartMode_waitForStart',0,0,18
	.byte	'IfxVadc_RequestSlotStartMode_cancelInjectRepeat',0,1,0,21,14,192,3,9,8,13
	.byte	'autoscanEnabled',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'triggerConfig',0
	.word	31570
	.byte	4,2,35,2,13
	.byte	'requestSlotPrio',0
	.word	31663
	.byte	1,2,35,6,13
	.byte	'requestSlotStartMode',0
	.word	31814
	.byte	1,2,35,7,0,21,14,182,3,9,8,13
	.byte	'flushQueueAfterInit',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'triggerConfig',0
	.word	31570
	.byte	4,2,35,2,13
	.byte	'requestSlotPrio',0
	.word	31663
	.byte	1,2,35,6,13
	.byte	'requestSlotStartMode',0
	.word	31814
	.byte	1,2,35,7,0,21,14,155,3,9,8,13
	.byte	'autoBackgroundScanEnabled',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'triggerConfig',0
	.word	31570
	.byte	4,2,35,2,13
	.byte	'requestSlotPrio',0
	.word	31663
	.byte	1,2,35,6,13
	.byte	'requestSlotStartMode',0
	.word	31814
	.byte	1,2,35,7,0,17,11,100,9,1,18
	.byte	'IfxVadc_ArbitrationRounds_4_slots',0,0,18
	.byte	'IfxVadc_ArbitrationRounds_8_slots',0,1,18
	.byte	'IfxVadc_ArbitrationRounds_16_slots',0,2,18
	.byte	'IfxVadc_ArbitrationRounds_20_slots',0,3,0,21,14,145,3,9,4,13
	.byte	'arbiterRoundLength',0
	.word	32259
	.byte	1,2,35,0,13
	.byte	'requestSlotQueueEnabled',0
	.word	488
	.byte	1,2,35,1,13
	.byte	'requestSlotScanEnabled',0
	.word	488
	.byte	1,2,35,2,13
	.byte	'requestSlotBackgroundScanEnabled',0
	.word	488
	.byte	1,2,35,3,0
.L72:
	.byte	21,14,151,4,9,52,13
	.byte	'module',0
	.word	30439
	.byte	4,2,35,0,13
	.byte	'groupId',0
	.word	27880
	.byte	1,2,35,4,13
	.byte	'master',0
	.word	27880
	.byte	1,2,35,5,13
	.byte	'inputClass',0
	.word	30021
	.byte	12,2,35,6,13
	.byte	'scanRequest',0
	.word	31915
	.byte	8,2,35,20,13
	.byte	'queueRequest',0
	.word	32025
	.byte	8,2,35,28,13
	.byte	'backgroundScanRequest',0
	.word	32139
	.byte	8,2,35,36,13
	.byte	'disablePostCalibration',0
	.word	488
	.byte	1,2,35,44,13
	.byte	'arbiter',0
	.word	32411
	.byte	4,2,35,46,0,24
	.word	32553
	.byte	3
	.word	32752
	.byte	25
	.byte	'IfxVadc_Adc_initGroup',0,14,199,6,27
	.word	29757
	.byte	1,1,1,1,5
	.byte	'group',0,14,199,6,68
	.word	29085
	.byte	5
	.byte	'config',0,14,199,6,106
	.word	32757
	.byte	0,3
	.word	32553
	.byte	26
	.byte	'IfxVadc_Adc_initGroupConfig',0,14,209,6,17,1,1,1,1,5
	.byte	'config',0,14,209,6,70
	.word	32829
	.byte	5
	.byte	'vadc',0,14,209,6,91
	.word	29025
	.byte	0,17,11,183,1,9,1,18
	.byte	'IfxVadc_ChannelResult_0',0,0,18
	.byte	'IfxVadc_ChannelResult_1',0,1,18
	.byte	'IfxVadc_ChannelResult_2',0,2,18
	.byte	'IfxVadc_ChannelResult_3',0,3,18
	.byte	'IfxVadc_ChannelResult_4',0,4,18
	.byte	'IfxVadc_ChannelResult_5',0,5,18
	.byte	'IfxVadc_ChannelResult_6',0,6,18
	.byte	'IfxVadc_ChannelResult_7',0,7,18
	.byte	'IfxVadc_ChannelResult_8',0,8,18
	.byte	'IfxVadc_ChannelResult_9',0,9,18
	.byte	'IfxVadc_ChannelResult_10',0,10,18
	.byte	'IfxVadc_ChannelResult_11',0,11,18
	.byte	'IfxVadc_ChannelResult_12',0,12,18
	.byte	'IfxVadc_ChannelResult_13',0,13,18
	.byte	'IfxVadc_ChannelResult_14',0,14,18
	.byte	'IfxVadc_ChannelResult_15',0,15,0,24
	.word	29030
	.byte	3
	.word	33331
.L77:
	.byte	21,14,213,3,9,8,13
	.byte	'channel',0
	.word	28283
	.byte	1,2,35,0,13
	.byte	'resultreg',0
	.word	32902
	.byte	1,2,35,1,13
	.byte	'group',0
	.word	33336
	.byte	4,2,35,4,0,3
	.word	33341
	.byte	17,15,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,17,11,193,3,9,1,18
	.byte	'IfxVadc_SrcNr_group0',0,0,18
	.byte	'IfxVadc_SrcNr_group1',0,1,18
	.byte	'IfxVadc_SrcNr_group2',0,2,18
	.byte	'IfxVadc_SrcNr_group3',0,3,18
	.byte	'IfxVadc_SrcNr_shared0',0,4,18
	.byte	'IfxVadc_SrcNr_shared1',0,5,18
	.byte	'IfxVadc_SrcNr_shared2',0,6,18
	.byte	'IfxVadc_SrcNr_shared3',0,7,0,17,11,188,2,9,1,18
	.byte	'IfxVadc_InputClasses_group0',0,0,18
	.byte	'IfxVadc_InputClasses_group1',0,1,18
	.byte	'IfxVadc_InputClasses_global0',0,2,18
	.byte	'IfxVadc_InputClasses_global1',0,3,0,17,11,165,1,9,1,18
	.byte	'IfxVadc_ChannelReference_standard',0,0,18
	.byte	'IfxVadc_ChannelReference_channel0',0,1,0,17,11,132,1,9,1,18
	.byte	'IfxVadc_BoundarySelection_group0',0,0,18
	.byte	'IfxVadc_BoundarySelection_group1',0,1,18
	.byte	'IfxVadc_BoundarySelection_global0',0,2,18
	.byte	'IfxVadc_BoundarySelection_global1',0,3,0,17,11,110,9,1,18
	.byte	'IfxVadc_BoundaryExtension_standard',0,0,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult1',0,1,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult2',0,2,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult3',0,3,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult4',0,4,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult5',0,5,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult6',0,6,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult7',0,7,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult8',0,8,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult9',0,9,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult10',0,10,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult11',0,11,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult12',0,12,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult13',0,13,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult14',0,14,18
	.byte	'IfxVadc_BoundaryExtension_fastCompareResult15',0,15,0,17,11,198,2,9,1,18
	.byte	'IfxVadc_LimitCheck_noCheck',0,0,18
	.byte	'IfxVadc_LimitCheck_eventIfInArea',0,1,18
	.byte	'IfxVadc_LimitCheck_eventIfOutsideArea',0,2,18
	.byte	'IfxVadc_LimitCheck_always',0,3,0
.L75:
	.byte	21,14,222,3,9,24,13
	.byte	'globalResultUsage',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'synchonize',0
	.word	488
	.byte	1,2,35,1,13
	.byte	'backgroundChannel',0
	.word	488
	.byte	1,2,35,2,13
	.byte	'rightAlignedStorage',0
	.word	488
	.byte	1,2,35,3,13
	.byte	'resultPriority',0
	.word	1039
	.byte	2,2,35,4,13
	.byte	'channelPriority',0
	.word	1039
	.byte	2,2,35,6,13
	.byte	'resultServProvider',0
	.word	33404
	.byte	1,2,35,8,13
	.byte	'channelServProvider',0
	.word	33404
	.byte	1,2,35,9,13
	.byte	'resultSrcNr',0
	.word	33463
	.byte	1,2,35,10,13
	.byte	'channelSrcNr',0
	.word	33463
	.byte	1,2,35,11,13
	.byte	'channelId',0
	.word	28283
	.byte	1,2,35,12,13
	.byte	'inputClass',0
	.word	33658
	.byte	1,2,35,13,13
	.byte	'reference',0
	.word	33787
	.byte	1,2,35,14,13
	.byte	'resultRegister',0
	.word	32902
	.byte	1,2,35,15,13
	.byte	'lowerBoundary',0
	.word	33866
	.byte	1,2,35,16,13
	.byte	'upperBoundary',0
	.word	33866
	.byte	1,2,35,17,13
	.byte	'boundaryMode',0
	.word	34015
	.byte	1,2,35,18,13
	.byte	'limitCheck',0
	.word	34769
	.byte	1,2,35,19,13
	.byte	'group',0
	.word	33336
	.byte	4,2,35,20,0,24
	.word	34908
	.byte	3
	.word	35352
	.byte	25
	.byte	'IfxVadc_Adc_initChannel',0,14,146,7,27
	.word	29757
	.byte	1,1,1,1,5
	.byte	'channel',0,14,146,7,72
	.word	33399
	.byte	5
	.byte	'config',0,14,146,7,114
	.word	35357
	.byte	0,3
	.word	34908
	.byte	26
	.byte	'IfxVadc_Adc_initChannelConfig',0,14,156,7,17,1,1,1,1,5
	.byte	'config',0,14,156,7,74
	.word	35433
	.byte	5
	.byte	'group',0,14,156,7,107
	.word	33336
	.byte	0,22
	.word	29090
	.byte	23
	.word	29128
	.byte	23
	.word	29142
	.byte	23
	.word	29157
	.byte	23
	.word	29175
	.byte	19,27
	.word	28018
	.byte	23
	.word	28052
	.byte	23
	.word	28066
	.byte	23
	.word	28083
	.byte	23
	.word	28101
	.byte	28
	.word	28115
	.byte	0,6,0,0,22
	.word	29193
	.byte	23
	.word	29233
	.byte	19,27
	.word	28117
	.byte	23
	.word	28153
	.byte	28
	.word	28167
	.byte	0,6,0,0,7
	.byte	'char',0,1,6,3
	.word	35599
	.byte	26
	.byte	'debug_assert_handler',0,16,112,9,1,1,1,1,5
	.byte	'pass',0,16,112,47
	.word	488
	.byte	5
	.byte	'file',0,16,112,59
	.word	35607
	.byte	5
	.byte	'line',0,16,112,69
	.word	481
	.byte	0
.L38:
	.byte	17,17,41,9,1,18
	.byte	'ADC0_CH0_A0',0,0,18
	.byte	'ADC0_CH1_A1',0,1,18
	.byte	'ADC0_CH2_A2',0,2,18
	.byte	'ADC0_CH3_A3',0,3,18
	.byte	'ADC0_CH4_A4',0,4,18
	.byte	'ADC0_CH5_A5',0,5,18
	.byte	'ADC0_CH6_A6',0,6,18
	.byte	'ADC0_CH7_A7',0,7,18
	.byte	'ADC0_CH8_A8',0,8,18
	.byte	'ADC0_CH10_A10',0,10,18
	.byte	'ADC0_CH11_A11',0,11,18
	.byte	'ADC0_CH12_A12',0,12,18
	.byte	'ADC0_CH13_A13',0,13,18
	.byte	'ADC1_CH0_A16',0,16,18
	.byte	'ADC1_CH1_A17',0,17,18
	.byte	'ADC1_CH4_A20',0,20,18
	.byte	'ADC1_CH5_A21',0,21,18
	.byte	'ADC1_CH8_A24',0,24,18
	.byte	'ADC1_CH9_A25',0,25,18
	.byte	'ADC2_CH3_A35',0,35,18
	.byte	'ADC2_CH4_A36',0,36,18
	.byte	'ADC2_CH5_A37',0,37,18
	.byte	'ADC2_CH6_A38',0,38,18
	.byte	'ADC2_CH7_A39',0,39,18
	.byte	'ADC2_CH10_A44',0,42,18
	.byte	'ADC2_CH11_A45',0,43,18
	.byte	'ADC2_CH12_A46',0,44,18
	.byte	'ADC2_CH13_A47',0,45,18
	.byte	'ADC2_CH14_A48',0,46,18
	.byte	'ADC2_CH15_A49',0,47,0
.L64:
	.byte	17,17,81,9,1,18
	.byte	'ADC_8BIT',0,0,18
	.byte	'ADC_10BIT',0,1,18
	.byte	'ADC_12BIT',0,2,0,7
	.byte	'short int',0,2,5,29
	.byte	'__wchar_t',0,18,1,1
	.word	36179
	.byte	29
	.byte	'__size_t',0,18,1,1
	.word	465
	.byte	29
	.byte	'__ptrdiff_t',0,18,1,1
	.word	481
	.byte	30,1,3
	.word	36247
	.byte	29
	.byte	'__codeptr',0,18,1,1
	.word	36249
	.byte	29
	.byte	'__intptr_t',0,18,1,1
	.word	481
	.byte	29
	.byte	'__uintptr_t',0,18,1,1
	.word	465
	.byte	29
	.byte	'IfxSrc_Tos',0,15,74,3
	.word	33404
	.byte	29
	.byte	'boolean',0,19,101,29
	.word	488
	.byte	29
	.byte	'uint8',0,19,105,29
	.word	488
	.byte	29
	.byte	'uint16',0,19,109,29
	.word	1039
	.byte	29
	.byte	'uint32',0,19,113,29
	.word	10311
	.byte	29
	.byte	'uint64',0,19,118,29
	.word	347
	.byte	29
	.byte	'sint16',0,19,126,29
	.word	36179
	.byte	7
	.byte	'long int',0,4,5,29
	.byte	'sint32',0,19,131,1,29
	.word	36420
	.byte	7
	.byte	'long long int',0,8,5,29
	.byte	'sint64',0,19,138,1,29
	.word	36448
	.byte	29
	.byte	'float32',0,19,167,1,29
	.word	261
	.byte	29
	.byte	'pvoid',0,20,57,28
	.word	379
	.byte	29
	.byte	'Ifx_TickTime',0,20,79,28
	.word	36448
	.byte	29
	.byte	'Ifx_Priority',0,20,103,16
	.word	1039
	.byte	17,20,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,29
	.byte	'Ifx_RxSel',0,20,140,1,3
	.word	36554
	.byte	29
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	505
	.byte	29
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	795
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	36739
	.byte	29
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	36771
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	36797
	.byte	29
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	36856
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	36884
	.byte	29
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	36921
	.byte	15,64
	.word	795
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	36949
	.byte	64,2,35,0,0,14
	.word	36958
	.byte	29
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	36990
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	37015
	.byte	29
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	37087
	.byte	15,8
	.word	795
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	37113
	.byte	8,2,35,0,0,14
	.word	37122
	.byte	29
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	37158
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	37188
	.byte	29
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	37261
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	37287
	.byte	29
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	37322
	.byte	15,192,1
	.word	795
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4999
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	37348
	.byte	192,1,2,35,16,0,14
	.word	37358
	.byte	29
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	37425
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	37451
	.byte	29
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	37499
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	37527
	.byte	29
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	37560
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	37113
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	37113
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	37113
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	37113
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	795
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	795
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	13899
	.byte	40,2,35,40,0,14
	.word	37587
	.byte	29
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	37714
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	37741
	.byte	29
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	37773
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	37799
	.byte	29
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	37831
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	795
	.byte	4,2,35,8,0,14
	.word	37857
	.byte	29
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	37917
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	12273
	.byte	16,2,35,16,0,14
	.word	37943
	.byte	29
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	38037
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4030
	.byte	24,2,35,24,0,14
	.word	38064
	.byte	29
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	38181
	.byte	15,12
	.word	795
	.byte	16,2,0,15,32
	.word	795
	.byte	16,7,0,15,32
	.word	38218
	.byte	16,0,0,15,88
	.word	488
	.byte	16,87,0,15,108
	.word	795
	.byte	16,26,0,15,96
	.word	488
	.byte	16,95,0,15,96
	.word	38218
	.byte	16,2,0,15,160,3
	.word	488
	.byte	16,159,3,0,15,64
	.word	38218
	.byte	16,1,0,15,192,3
	.word	488
	.byte	16,191,3,0,15,16
	.word	795
	.byte	16,3,0,15,64
	.word	38303
	.byte	16,3,0,15,192,2
	.word	488
	.byte	16,191,2,0,15,52
	.word	488
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	38209
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2840
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	795
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	37113
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4659
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	38227
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	38236
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	38245
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	38254
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	795
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4999
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	38263
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	38272
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	38263
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	38272
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	38283
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	38292
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	38312
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	38321
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	38209
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	38332
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	38209
	.byte	12,3,35,192,18,0,14
	.word	38341
	.byte	29
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	38801
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	38827
	.byte	29
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	38860
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	38887
	.byte	29
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	38960
	.byte	15,56
	.word	488
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	795
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	38987
	.byte	56,2,35,24,0,14
	.word	38996
	.byte	29
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	39119
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	39145
	.byte	29
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	39177
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	795
	.byte	4,2,35,16,0,14
	.word	39203
	.byte	29
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	39288
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	39314
	.byte	29
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	39346
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	38218
	.byte	32,2,35,0,0,14
	.word	39372
	.byte	29
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	39405
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	38218
	.byte	32,2,35,0,0,14
	.word	39432
	.byte	29
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	39466
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	795
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	795
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	795
	.byte	4,2,35,20,0,14
	.word	39494
	.byte	29
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	39587
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	39614
	.byte	29
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	39646
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	38303
	.byte	16,2,35,4,0,14
	.word	39672
	.byte	29
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	39718
	.byte	15,24
	.word	795
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	39744
	.byte	24,2,35,0,0,14
	.word	39753
	.byte	29
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	39786
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	38209
	.byte	12,2,35,0,0,14
	.word	39813
	.byte	29
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	39845
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,0,14
	.word	39871
	.byte	29
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	39917
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	39943
	.byte	29
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	40018
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	795
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	795
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	795
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	795
	.byte	4,2,35,12,0,14
	.word	40047
	.byte	29
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	40121
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	795
	.byte	4,2,35,0,0,14
	.word	40149
	.byte	29
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	40183
	.byte	15,4
	.word	36739
	.byte	16,0,0,14
	.word	40210
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	40219
	.byte	4,2,35,0,0,14
	.word	40224
	.byte	29
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	40260
	.byte	15,48
	.word	36797
	.byte	16,3,0,14
	.word	40288
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	40297
	.byte	48,2,35,0,0,14
	.word	40302
	.byte	29
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	40342
	.byte	14
	.word	36884
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	40372
	.byte	4,2,35,0,0,14
	.word	40377
	.byte	29
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	40411
	.byte	15,64
	.word	36958
	.byte	16,0,0,14
	.word	40438
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	40447
	.byte	64,2,35,0,0,14
	.word	40452
	.byte	29
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	40486
	.byte	15,32
	.word	37015
	.byte	16,1,0,14
	.word	40513
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	40522
	.byte	32,2,35,0,0,14
	.word	40527
	.byte	29
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	40563
	.byte	14
	.word	37122
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	40591
	.byte	8,2,35,0,0,14
	.word	40596
	.byte	29
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	40640
	.byte	15,16
	.word	37188
	.byte	16,0,0,14
	.word	40672
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	40681
	.byte	16,2,35,0,0,14
	.word	40686
	.byte	29
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	40720
	.byte	15,8
	.word	37287
	.byte	16,1,0,14
	.word	40747
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	40756
	.byte	8,2,35,0,0,14
	.word	40761
	.byte	29
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	40795
	.byte	15,208,1
	.word	37358
	.byte	16,0,0,14
	.word	40822
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	40832
	.byte	208,1,2,35,0,0,14
	.word	40837
	.byte	29
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	40873
	.byte	14
	.word	37451
	.byte	14
	.word	37451
	.byte	14
	.word	37451
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	40900
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4659
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	40905
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	40910
	.byte	8,2,35,24,0,14
	.word	40915
	.byte	29
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	41006
	.byte	15,4
	.word	37527
	.byte	16,0,0,14
	.word	41035
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	41044
	.byte	4,2,35,0,0,14
	.word	41049
	.byte	29
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	41085
	.byte	15,80
	.word	37587
	.byte	16,0,0,14
	.word	41113
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	41122
	.byte	80,2,35,0,0,14
	.word	41127
	.byte	29
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	41163
	.byte	15,4
	.word	37741
	.byte	16,0,0,14
	.word	41191
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	41200
	.byte	4,2,35,0,0,14
	.word	41205
	.byte	29
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	41239
	.byte	15,4
	.word	37799
	.byte	16,0,0,14
	.word	41266
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	41275
	.byte	4,2,35,0,0,14
	.word	41280
	.byte	29
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	41314
	.byte	15,12
	.word	37857
	.byte	16,0,0,14
	.word	41341
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	41350
	.byte	12,2,35,0,0,14
	.word	41355
	.byte	29
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	41389
	.byte	15,64
	.word	37943
	.byte	16,1,0,14
	.word	41416
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	41425
	.byte	64,2,35,0,0,14
	.word	41430
	.byte	29
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	41466
	.byte	15,48
	.word	38064
	.byte	16,0,0,14
	.word	41494
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	41503
	.byte	48,2,35,0,0,14
	.word	41508
	.byte	29
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	41546
	.byte	15,204,18
	.word	38341
	.byte	16,0,0,14
	.word	41575
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	41585
	.byte	204,18,2,35,0,0,14
	.word	41590
	.byte	29
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	41626
	.byte	15,4
	.word	38827
	.byte	16,0,0,14
	.word	41653
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	41662
	.byte	4,2,35,0,0,14
	.word	41667
	.byte	29
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	41703
	.byte	15,64
	.word	38887
	.byte	16,3,0,14
	.word	41731
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	41740
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	795
	.byte	4,2,35,64,0,14
	.word	41745
	.byte	29
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	41794
	.byte	15,80
	.word	38996
	.byte	16,0,0,14
	.word	41822
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	41831
	.byte	80,2,35,0,0,14
	.word	41836
	.byte	29
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	41870
	.byte	15,4
	.word	39145
	.byte	16,0,0,14
	.word	41897
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	41906
	.byte	4,2,35,0,0,14
	.word	41911
	.byte	29
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	41945
	.byte	15,40
	.word	39203
	.byte	16,1,0,14
	.word	41972
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	41981
	.byte	40,2,35,0,0,14
	.word	41986
	.byte	29
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	42020
	.byte	15,8
	.word	39314
	.byte	16,1,0,14
	.word	42047
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	42056
	.byte	8,2,35,0,0,14
	.word	42061
	.byte	29
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	42095
	.byte	15,32
	.word	39372
	.byte	16,0,0,14
	.word	42122
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	42131
	.byte	32,2,35,0,0,14
	.word	42136
	.byte	29
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	42172
	.byte	15,32
	.word	39432
	.byte	16,0,0,14
	.word	42200
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	42209
	.byte	32,2,35,0,0,14
	.word	42214
	.byte	29
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	42252
	.byte	15,96
	.word	39494
	.byte	16,3,0,14
	.word	42281
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	42290
	.byte	96,2,35,0,0,14
	.word	42295
	.byte	29
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	42331
	.byte	15,4
	.word	39614
	.byte	16,0,0,14
	.word	42359
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	42368
	.byte	4,2,35,0,0,14
	.word	42373
	.byte	29
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	42407
	.byte	14
	.word	39672
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	42434
	.byte	20,2,35,0,0,14
	.word	42439
	.byte	29
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	42473
	.byte	15,24
	.word	39753
	.byte	16,0,0,14
	.word	42500
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	42509
	.byte	24,2,35,0,0,14
	.word	42514
	.byte	29
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	42550
	.byte	15,12
	.word	39813
	.byte	16,0,0,14
	.word	42578
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	42587
	.byte	12,2,35,0,0,14
	.word	42592
	.byte	29
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	42626
	.byte	15,16
	.word	39871
	.byte	16,1,0,14
	.word	42653
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	42662
	.byte	16,2,35,0,0,14
	.word	42667
	.byte	29
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	42701
	.byte	15,64
	.word	40047
	.byte	16,3,0,14
	.word	42728
	.byte	15,224,1
	.word	488
	.byte	16,223,1,0,15,32
	.word	39943
	.byte	16,1,0,14
	.word	42753
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	42737
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	42742
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	42762
	.byte	32,3,35,160,2,0,14
	.word	42767
	.byte	29
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	42836
	.byte	14
	.word	40149
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	42864
	.byte	4,2,35,0,0,14
	.word	42869
	.byte	29
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	42905
	.byte	29
	.byte	'IfxVadc_GroupId',0,13,127,3
	.word	27880
	.byte	29
	.byte	'Ifx_VADC_ACCEN0_Bits',0,12,83,3
	.word	22556
	.byte	29
	.byte	'Ifx_VADC_ACCPROT0_Bits',0,12,100,3
	.word	23476
	.byte	29
	.byte	'Ifx_VADC_ACCPROT1_Bits',0,12,116,3
	.word	23751
	.byte	29
	.byte	'Ifx_VADC_BRSCTRL_Bits',0,12,132,1,3
	.word	25056
	.byte	29
	.byte	'Ifx_VADC_BRSMR_Bits',0,12,149,1,3
	.word	25332
	.byte	29
	.byte	'Ifx_VADC_BRSPND_Bits',0,12,155,1,3
	.word	24959
	.byte	29
	.byte	'Ifx_VADC_BRSSEL_Bits',0,12,161,1,3
	.word	24853
	.byte	29
	.byte	'Ifx_VADC_CLC_Bits',0,12,171,1,3
	.word	21723
	.byte	29
	.byte	'Ifx_VADC_EMUXSEL_Bits',0,12,179,1,3
	.word	26189
	.byte	29
	.byte	'Ifx_VADC_G_ALIAS_Bits',0,12,188,1,3
	.word	12513
	.byte	29
	.byte	'Ifx_VADC_G_ARBCFG_Bits',0,12,208,1,3
	.word	10542
	.byte	29
	.byte	'Ifx_VADC_G_ARBPR_Bits',0,12,227,1,3
	.word	10885
	.byte	29
	.byte	'Ifx_VADC_G_ASCTRL_Bits',0,12,246,1,3
	.word	15224
	.byte	29
	.byte	'Ifx_VADC_G_ASMR_Bits',0,12,135,2,3
	.word	15557
	.byte	29
	.byte	'Ifx_VADC_G_ASPND_Bits',0,12,141,2,3
	.word	15929
	.byte	29
	.byte	'Ifx_VADC_G_ASSEL_Bits',0,12,147,2,3
	.word	15842
	.byte	29
	.byte	'Ifx_VADC_G_BFL_Bits',0,12,167,2,3
	.word	13008
	.byte	29
	.byte	'Ifx_VADC_G_BFLC_Bits',0,12,177,2,3
	.word	13578
	.byte	29
	.byte	'Ifx_VADC_G_BFLNP_Bits',0,12,187,2,3
	.word	13734
	.byte	29
	.byte	'Ifx_VADC_G_BFLS_Bits',0,12,202,2,3
	.word	13336
	.byte	29
	.byte	'Ifx_VADC_G_BOUND_Bits',0,12,211,2,3
	.word	12664
	.byte	29
	.byte	'Ifx_VADC_G_CEFCLR_Bits',0,12,233,2,3
	.word	16862
	.byte	29
	.byte	'Ifx_VADC_G_CEFLAG_Bits',0,12,255,2,3
	.word	16025
	.byte	29
	.byte	'Ifx_VADC_G_CEVNP0_Bits',0,12,140,3,3
	.word	17699
	.byte	29
	.byte	'Ifx_VADC_G_CEVNP1_Bits',0,12,153,3,3
	.word	17914
	.byte	29
	.byte	'Ifx_VADC_G_CHASS_Bits',0,12,190,3,3
	.word	11218
	.byte	29
	.byte	'Ifx_VADC_G_CHCTR_Bits',0,12,210,3,3
	.word	19559
	.byte	29
	.byte	'Ifx_VADC_G_EMUXCTR_Bits',0,12,225,3,3
	.word	18959
	.byte	29
	.byte	'Ifx_VADC_G_Q0R0_Bits',0,12,236,3,3
	.word	14691
	.byte	29
	.byte	'Ifx_VADC_G_QBUR0_Bits',0,12,247,3,3
	.word	14860
	.byte	29
	.byte	'Ifx_VADC_G_QCTRL0_Bits',0,12,138,4,3
	.word	13908
	.byte	29
	.byte	'Ifx_VADC_G_QINR0_Bits',0,12,148,4,3
	.word	15030
	.byte	29
	.byte	'Ifx_VADC_G_QMR0_Bits',0,12,163,4,3
	.word	14241
	.byte	29
	.byte	'Ifx_VADC_G_QSR0_Bits',0,12,175,4,3
	.word	14492
	.byte	29
	.byte	'Ifx_VADC_G_RCR_Bits',0,12,188,4,3
	.word	19932
	.byte	29
	.byte	'Ifx_VADC_G_REFCLR_Bits',0,12,210,4,3
	.word	17218
	.byte	29
	.byte	'Ifx_VADC_G_REFLAG_Bits',0,12,232,4,3
	.word	16381
	.byte	29
	.byte	'Ifx_VADC_G_RES_Bits',0,12,244,4,3
	.word	20156
	.byte	29
	.byte	'Ifx_VADC_G_RESD_Bits',0,12,128,5,3
	.word	20342
	.byte	29
	.byte	'Ifx_VADC_G_REVNP0_Bits',0,12,141,5,3
	.word	18135
	.byte	29
	.byte	'Ifx_VADC_G_REVNP1_Bits',0,12,154,5,3
	.word	18350
	.byte	29
	.byte	'Ifx_VADC_G_RRASS_Bits',0,12,176,5,3
	.word	11886
	.byte	29
	.byte	'Ifx_VADC_G_SEFCLR_Bits',0,12,184,5,3
	.word	17574
	.byte	29
	.byte	'Ifx_VADC_G_SEFLAG_Bits',0,12,192,5,3
	.word	16737
	.byte	29
	.byte	'Ifx_VADC_G_SEVNP_Bits',0,12,200,5,3
	.word	18571
	.byte	29
	.byte	'Ifx_VADC_G_SRACT_Bits',0,12,215,5,3
	.word	18699
	.byte	29
	.byte	'Ifx_VADC_G_SYNCTR_Bits',0,12,226,5,3
	.word	12822
	.byte	29
	.byte	'Ifx_VADC_G_VFR_Bits',0,12,248,5,3
	.word	19222
	.byte	29
	.byte	'Ifx_VADC_GLOBBOUND_Bits',0,12,129,6,3
	.word	24010
	.byte	29
	.byte	'Ifx_VADC_GLOBCFG_Bits',0,12,149,6,3
	.word	23126
	.byte	29
	.byte	'Ifx_VADC_GLOBEFLAG_Bits',0,12,162,6,3
	.word	24170
	.byte	29
	.byte	'Ifx_VADC_GLOBEVNP_Bits',0,12,171,6,3
	.word	24419
	.byte	29
	.byte	'Ifx_VADC_GLOBRCR_Bits',0,12,182,6,3
	.word	25625
	.byte	29
	.byte	'Ifx_VADC_GLOBRES_Bits',0,12,194,6,3
	.word	25821
	.byte	29
	.byte	'Ifx_VADC_GLOBRESD_Bits',0,12,206,6,3
	.word	26000
	.byte	29
	.byte	'Ifx_VADC_GLOBTF_Bits',0,12,223,6,3
	.word	24571
	.byte	29
	.byte	'Ifx_VADC_ICLASS_Bits',0,12,236,6,3
	.word	12282
	.byte	29
	.byte	'Ifx_VADC_ID_Bits',0,12,244,6,3
	.word	21881
	.byte	29
	.byte	'Ifx_VADC_KRST0_Bits',0,12,252,6,3
	.word	22432
	.byte	29
	.byte	'Ifx_VADC_KRST1_Bits',0,12,131,7,3
	.word	22327
	.byte	29
	.byte	'Ifx_VADC_KRSTCLR_Bits',0,12,138,7,3
	.word	22220
	.byte	29
	.byte	'Ifx_VADC_OCS_Bits',0,12,151,7,3
	.word	22013
	.byte	29
	.byte	'Ifx_VADC_ACCEN0',0,12,164,7,3
	.word	23086
	.byte	29
	.byte	'Ifx_VADC_ACCPROT0',0,12,172,7,3
	.word	23711
	.byte	29
	.byte	'Ifx_VADC_ACCPROT1',0,12,180,7,3
	.word	23970
	.byte	29
	.byte	'Ifx_VADC_BRSCTRL',0,12,188,7,3
	.word	25292
	.byte	29
	.byte	'Ifx_VADC_BRSMR',0,12,196,7,3
	.word	25576
	.byte	29
	.byte	'Ifx_VADC_BRSPND',0,12,204,7,3
	.word	25007
	.byte	29
	.byte	'Ifx_VADC_BRSSEL',0,12,212,7,3
	.word	24901
	.byte	29
	.byte	'Ifx_VADC_CLC',0,12,220,7,3
	.word	21841
	.byte	29
	.byte	'Ifx_VADC_EMUXSEL',0,12,228,7,3
	.word	26281
	.byte	29
	.byte	'Ifx_VADC_G_ALIAS',0,12,236,7,3
	.word	12624
	.byte	29
	.byte	'Ifx_VADC_G_ARBCFG',0,12,244,7,3
	.word	10845
	.byte	29
	.byte	'Ifx_VADC_G_ARBPR',0,12,252,7,3
	.word	11178
	.byte	29
	.byte	'Ifx_VADC_G_ASCTRL',0,12,132,8,3
	.word	15517
	.byte	29
	.byte	'Ifx_VADC_G_ASMR',0,12,140,8,3
	.word	15802
	.byte	29
	.byte	'Ifx_VADC_G_ASPND',0,12,148,8,3
	.word	15976
	.byte	29
	.byte	'Ifx_VADC_G_ASSEL',0,12,156,8,3
	.word	15889
	.byte	29
	.byte	'Ifx_VADC_G_BFL',0,12,164,8,3
	.word	13296
	.byte	29
	.byte	'Ifx_VADC_G_BFLC',0,12,172,8,3
	.word	13694
	.byte	29
	.byte	'Ifx_VADC_G_BFLNP',0,12,180,8,3
	.word	13859
	.byte	29
	.byte	'Ifx_VADC_G_BFLS',0,12,188,8,3
	.word	13538
	.byte	29
	.byte	'Ifx_VADC_G_BOUND',0,12,196,8,3
	.word	12782
	.byte	29
	.byte	'Ifx_VADC_G_CEFCLR',0,12,204,8,3
	.word	17178
	.byte	29
	.byte	'Ifx_VADC_G_CEFLAG',0,12,212,8,3
	.word	16341
	.byte	29
	.byte	'Ifx_VADC_G_CEVNP0',0,12,220,8,3
	.word	17874
	.byte	29
	.byte	'Ifx_VADC_G_CEVNP1',0,12,228,8,3
	.word	18095
	.byte	29
	.byte	'Ifx_VADC_G_CHASS',0,12,236,8,3
	.word	11846
	.byte	29
	.byte	'Ifx_VADC_G_CHCTR',0,12,244,8,3
	.word	19874
	.byte	29
	.byte	'Ifx_VADC_G_EMUXCTR',0,12,252,8,3
	.word	19182
	.byte	29
	.byte	'Ifx_VADC_G_Q0R0',0,12,132,9,3
	.word	14820
	.byte	29
	.byte	'Ifx_VADC_G_QBUR0',0,12,140,9,3
	.word	14990
	.byte	29
	.byte	'Ifx_VADC_G_QCTRL0',0,12,148,9,3
	.word	14201
	.byte	29
	.byte	'Ifx_VADC_G_QINR0',0,12,156,9,3
	.word	15147
	.byte	29
	.byte	'Ifx_VADC_G_QMR0',0,12,164,9,3
	.word	14452
	.byte	29
	.byte	'Ifx_VADC_G_QSR0',0,12,172,9,3
	.word	14651
	.byte	29
	.byte	'Ifx_VADC_G_RCR',0,12,180,9,3
	.word	20107
	.byte	29
	.byte	'Ifx_VADC_G_REFCLR',0,12,188,9,3
	.word	17534
	.byte	29
	.byte	'Ifx_VADC_G_REFLAG',0,12,196,9,3
	.word	16697
	.byte	29
	.byte	'Ifx_VADC_G_RES',0,12,204,9,3
	.word	20293
	.byte	29
	.byte	'Ifx_VADC_G_RESD',0,12,212,9,3
	.word	20480
	.byte	29
	.byte	'Ifx_VADC_G_REVNP0',0,12,220,9,3
	.word	18310
	.byte	29
	.byte	'Ifx_VADC_G_REVNP1',0,12,228,9,3
	.word	18531
	.byte	29
	.byte	'Ifx_VADC_G_RRASS',0,12,236,9,3
	.word	12233
	.byte	29
	.byte	'Ifx_VADC_G_SEFCLR',0,12,244,9,3
	.word	17659
	.byte	29
	.byte	'Ifx_VADC_G_SEFLAG',0,12,252,9,3
	.word	16822
	.byte	29
	.byte	'Ifx_VADC_G_SEVNP',0,12,132,10,3
	.word	18659
	.byte	29
	.byte	'Ifx_VADC_G_SRACT',0,12,140,10,3
	.word	18910
	.byte	29
	.byte	'Ifx_VADC_G_SYNCTR',0,12,148,10,3
	.word	12968
	.byte	29
	.byte	'Ifx_VADC_G_VFR',0,12,156,10,3
	.word	19519
	.byte	29
	.byte	'Ifx_VADC_GLOBBOUND',0,12,164,10,3
	.word	24130
	.byte	29
	.byte	'Ifx_VADC_GLOBCFG',0,12,172,10,3
	.word	23436
	.byte	29
	.byte	'Ifx_VADC_GLOBEFLAG',0,12,180,10,3
	.word	24370
	.byte	29
	.byte	'Ifx_VADC_GLOBEVNP',0,12,188,10,3
	.word	24531
	.byte	29
	.byte	'Ifx_VADC_GLOBRCR',0,12,196,10,3
	.word	25772
	.byte	29
	.byte	'Ifx_VADC_GLOBRES',0,12,204,10,3
	.word	25960
	.byte	29
	.byte	'Ifx_VADC_GLOBRESD',0,12,212,10,3
	.word	26140
	.byte	29
	.byte	'Ifx_VADC_GLOBTF',0,12,220,10,3
	.word	24813
	.byte	29
	.byte	'Ifx_VADC_ICLASS',0,12,228,10,3
	.word	12464
	.byte	29
	.byte	'Ifx_VADC_ID',0,12,236,10,3
	.word	21964
	.byte	29
	.byte	'Ifx_VADC_KRST0',0,12,244,10,3
	.word	22516
	.byte	29
	.byte	'Ifx_VADC_KRST1',0,12,252,10,3
	.word	22392
	.byte	29
	.byte	'Ifx_VADC_KRSTCLR',0,12,132,11,3
	.word	22287
	.byte	29
	.byte	'Ifx_VADC_OCS',0,12,140,11,3
	.word	22180
	.byte	14
	.word	20540
	.byte	29
	.byte	'Ifx_VADC_G',0,12,217,11,3
	.word	46458
	.byte	14
	.word	26358
	.byte	29
	.byte	'Ifx_VADC',0,12,147,12,3
	.word	46483
	.byte	29
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8412
	.byte	29
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8325
	.byte	29
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4668
	.byte	29
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2721
	.byte	29
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3716
	.byte	29
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	2849
	.byte	29
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3496
	.byte	29
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3064
	.byte	29
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3279
	.byte	29
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7684
	.byte	29
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7808
	.byte	29
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	7892
	.byte	29
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8072
	.byte	29
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6323
	.byte	29
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	6847
	.byte	29
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6497
	.byte	29
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6671
	.byte	29
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7336
	.byte	29
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2150
	.byte	29
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5660
	.byte	29
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6148
	.byte	29
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5807
	.byte	29
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	5976
	.byte	29
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7003
	.byte	29
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1834
	.byte	29
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5374
	.byte	29
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5008
	.byte	29
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4039
	.byte	29
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4343
	.byte	29
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	8939
	.byte	29
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8372
	.byte	29
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	4959
	.byte	29
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2800
	.byte	29
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	3990
	.byte	29
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3024
	.byte	29
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3676
	.byte	29
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3239
	.byte	29
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3456
	.byte	29
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7768
	.byte	29
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8017
	.byte	29
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8276
	.byte	29
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7644
	.byte	29
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6457
	.byte	29
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	6963
	.byte	29
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6631
	.byte	29
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6807
	.byte	29
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2681
	.byte	29
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7296
	.byte	29
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5767
	.byte	29
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6283
	.byte	29
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	5936
	.byte	29
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6108
	.byte	29
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2110
	.byte	29
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5620
	.byte	29
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5334
	.byte	29
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4303
	.byte	29
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4619
	.byte	14
	.word	8979
	.byte	29
	.byte	'Ifx_P',0,8,139,6,3
	.word	47824
	.byte	17,21,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,29
	.byte	'IfxScu_CCUCON0_CLKSEL',0,21,240,10,3
	.word	47844
	.byte	17,21,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,29
	.byte	'IfxScu_WDTCON1_IR',0,21,255,10,3
	.word	47941
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	48063
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	48620
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	48697
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	48833
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	488
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	49113
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	49351
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	49479
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	488
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	49722
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	49957
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	50085
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	50185
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	488
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	50285
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	50493
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	50658
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	50841
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	488
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	50995
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	51359
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	488
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	51570
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	51822
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	51940
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	52051
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	52214
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	52377
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	52535
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	488
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	10,0,2,35,2,0,29
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	52700
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	488
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	488
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1039
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	53029
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	53250
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	53413
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	53685
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	53838
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	53994
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	54156
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	54299
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	54464
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	54609
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	54790
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	54964
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	55124
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	55268
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	55542
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	55681
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	488
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1039
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	488
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	55844
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1039
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1039
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	56062
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	56225
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	56561
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	488
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	56668
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	57120
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	57219
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	57369
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	465
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	57518
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	57679
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1039
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	57809
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	57941
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1039
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	58056
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	58167
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	488
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	58325
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	58737
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1039
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	6,0,2,35,3,0,29
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	58838
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	59105
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	59241
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	488
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	59352
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	59485
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	59688
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	60044
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	60222
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	488
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	60322
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	488
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	488
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	9,0,2,35,2,0,29
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	60692
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	60878
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	61076
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	61309
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	488
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	61461
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	488
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	488
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	62028
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	488
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	488
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	62322
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	488
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	488
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	488
	.byte	4,0,2,35,3,0,29
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	62600
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	63096
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1039
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	63409
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	63618
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	488
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	63829
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	64261
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	488
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	488
	.byte	7,0,2,35,3,0,29
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	64357
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	64617
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	64742
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	64939
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	65092
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	65245
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	65398
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	903
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1061
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1305
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	887
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	65653
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	65779
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	66031
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48063
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	66250
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48620
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	66314
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48697
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	66378
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48833
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	66443
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49113
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	66508
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49351
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	66573
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49479
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	66638
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49722
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	66703
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49957
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	66768
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50085
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	66833
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50185
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	66898
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50285
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	66963
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50493
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	67027
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50658
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	67091
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50841
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	67155
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50995
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	67220
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51359
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	67282
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51570
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	67344
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51822
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	67406
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51940
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	67470
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52051
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	67535
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52214
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	67601
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52377
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	67667
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52535
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	67735
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52700
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	67802
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53029
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	67870
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53250
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	67938
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53413
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	68004
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53685
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	68071
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53838
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	68140
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53994
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	68209
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54156
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	68278
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54299
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	68347
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54464
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	68416
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54609
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	68485
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54790
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	68553
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54964
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	68621
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55124
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	68689
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55268
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	68757
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55542
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	68822
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55681
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	68887
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55844
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	68953
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56062
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	69017
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56225
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	69078
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56561
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	69139
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56668
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	69199
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57120
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	69261
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57219
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	69321
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57369
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	69383
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57518
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	69451
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57679
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	69519
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57809
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	69587
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57941
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	69651
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58056
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	69716
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58167
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	69779
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58325
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	69840
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58737
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	69904
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58838
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	69965
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59105
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	70029
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59241
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	70096
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59352
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	70159
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59485
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	70220
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59688
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	70282
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60044
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	70347
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60222
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	70412
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60322
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	70477
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60692
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	70546
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60878
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	70615
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61076
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	70684
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61309
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	70749
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61461
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	70812
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62028
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	70877
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62322
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	70942
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62600
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	71007
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63096
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	71073
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63618
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	71142
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63409
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	71206
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63829
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	71271
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64261
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	71336
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64357
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	71401
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64617
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	71465
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64742
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	71531
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64939
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	71595
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65092
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	71660
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65245
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	71725
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65398
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	71790
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	999
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1265
	.byte	29
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1496
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65653
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	71941
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65779
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	72008
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66031
	.byte	4,2,35,0,0,29
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	72075
	.byte	14
	.word	1536
	.byte	29
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	72140
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	71941
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	72008
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	72075
	.byte	4,2,35,8,0,14
	.word	72169
	.byte	29
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	72230
	.byte	15,8
	.word	67406
	.byte	16,1,0,15,20
	.word	488
	.byte	16,19,0,15,8
	.word	70749
	.byte	16,1,0,14
	.word	72169
	.byte	15,24
	.word	1536
	.byte	16,1,0,14
	.word	72289
	.byte	15,16
	.word	67220
	.byte	16,3,0,15,16
	.word	69199
	.byte	16,3,0,15,180,3
	.word	488
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4659
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	69139
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2840
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	69840
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	70684
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	70282
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	70347
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	70412
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	70615
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	70477
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	70546
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	66443
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	66508
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	69017
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	68953
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	66573
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	66638
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	66703
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	66768
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	71271
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2840
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	71142
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	66378
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	71465
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	71206
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2840
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	68004
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	72257
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	67470
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	71531
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	66833
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	66898
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	72266
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	70159
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	69321
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	69904
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	69779
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	69261
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	68757
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	67735
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	67535
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	67601
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	71401
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2840
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	70812
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	71007
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	71073
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	72275
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2840
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	67155
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	67027
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	70877
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	70942
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	72284
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	67344
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	72298
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4999
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	71790
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	71725
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	71595
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	71660
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2840
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	69587
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	69651
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	66963
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	69716
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4659
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	71336
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	12273
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	69383
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	69451
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	69519
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	22004
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	70096
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4659
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	68822
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	67667
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	68887
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	67938
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	67802
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2840
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	68485
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	68553
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	68621
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	68689
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	68071
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	68140
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	68209
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	68278
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	68347
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	68416
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	67870
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2840
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	70029
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	69965
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	13899
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	72303
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	67282
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	69078
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	70220
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	72312
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2840
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	67091
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	72321
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	66314
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	66250
	.byte	4,3,35,252,7,0,14
	.word	72332
	.byte	29
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	74322
	.byte	17,7,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,29
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	74344
	.byte	29
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	9662
	.byte	29
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9592
	.byte	17,7,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,29
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	74550
	.byte	29
	.byte	'IfxPort_State',0,7,178,1,3
	.word	9975
	.byte	21,7,190,1,9,8,13
	.byte	'port',0
	.word	9587
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	488
	.byte	1,2,35,4,0,29
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	75015
	.byte	21,22,59,15,20,13
	.byte	'module',0
	.word	27215
	.byte	4,2,35,0,13
	.byte	'groupId',0
	.word	27880
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	75015
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	9662
	.byte	1,2,35,16,0,24
	.word	75075
	.byte	29
	.byte	'IfxVadc_GxBfl_Out',0,22,65,3
	.word	75143
	.byte	21,22,68,15,16,13
	.byte	'module',0
	.word	27215
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	75015
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	9662
	.byte	1,2,35,12,0,24
	.word	75174
	.byte	29
	.byte	'IfxVadc_Emux_Out',0,22,73,3
	.word	75225
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,23,45,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_A_Bits',0,23,48,3
	.word	75255
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,23,51,16,4,11
	.byte	'VSS',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BIV_Bits',0,23,55,3
	.word	75316
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,23,58,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_BTV_Bits',0,23,62,3
	.word	75395
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,23,65,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT_Bits',0,23,69,3
	.word	75481
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,23,72,16,4,11
	.byte	'CM',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	887
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	887
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	887
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL_Bits',0,23,80,3
	.word	75570
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,23,83,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT_Bits',0,23,89,3
	.word	75716
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,23,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID_Bits',0,23,96,3
	.word	75843
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,23,99,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L_Bits',0,23,103,3
	.word	75941
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,23,106,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U_Bits',0,23,110,3
	.word	76034
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,23,113,16,4,11
	.byte	'MODREV',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	887
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID_Bits',0,23,118,3
	.word	76127
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,23,121,16,4,11
	.byte	'XE',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE_Bits',0,23,125,3
	.word	76234
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,23,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT_Bits',0,23,136,1,3
	.word	76321
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,23,139,1,16,4,11
	.byte	'CID',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID_Bits',0,23,143,1,3
	.word	76475
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,23,146,1,16,4,11
	.byte	'DATA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_D_Bits',0,23,149,1,3
	.word	76569
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,23,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	887
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DATR_Bits',0,23,163,1,3
	.word	76632
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,23,166,1,16,4,11
	.byte	'DE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	887
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	887
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	19,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR_Bits',0,23,177,1,3
	.word	76850
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,23,180,1,16,4,11
	.byte	'DTA',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR_Bits',0,23,184,1,3
	.word	77065
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,23,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0_Bits',0,23,192,1,3
	.word	77159
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,23,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2_Bits',0,23,199,1,3
	.word	77275
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,23,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	887
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_CPU_DCX_Bits',0,23,206,1,3
	.word	77376
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,23,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD_Bits',0,23,212,1,3
	.word	77469
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,23,215,1,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR_Bits',0,23,218,1,3
	.word	77549
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,23,221,1,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR_Bits',0,23,233,1,3
	.word	77618
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,23,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_DMS_Bits',0,23,240,1,3
	.word	77847
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,23,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L_Bits',0,23,247,1,3
	.word	77940
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,23,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	887
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U_Bits',0,23,254,1,3
	.word	78035
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,23,129,2,16,4,11
	.byte	'RE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE_Bits',0,23,133,2,3
	.word	78130
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,23,136,2,16,4,11
	.byte	'WE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE_Bits',0,23,140,2,3
	.word	78220
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,23,143,2,16,4,11
	.byte	'SRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	887
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	887
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR_Bits',0,23,161,2,3
	.word	78310
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,23,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT_Bits',0,23,172,2,3
	.word	78634
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,23,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FCX_Bits',0,23,180,2,3
	.word	78788
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,23,183,2,16,4,11
	.byte	'TST',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	887
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	887
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	887
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	887
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	887
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,23,202,2,3
	.word	78894
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,23,205,2,16,4,11
	.byte	'OPC',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,23,212,2,3
	.word	79243
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,23,215,2,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,23,218,2,3
	.word	79403
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,23,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,23,224,2,3
	.word	79484
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,23,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,23,230,2,3
	.word	79571
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,23,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,23,236,2,3
	.word	79658
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,23,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT_Bits',0,23,243,2,3
	.word	79745
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,23,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	887
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	887
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	887
	.byte	6,0,2,35,0,0,29
	.byte	'Ifx_CPU_ICR_Bits',0,23,253,2,3
	.word	79836
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,23,128,3,16,4,11
	.byte	'ISP',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_ISP_Bits',0,23,131,3,3
	.word	79979
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,23,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	887
	.byte	12,0,2,35,0,0,29
	.byte	'Ifx_CPU_LCX_Bits',0,23,139,3,3
	.word	80045
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,23,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT_Bits',0,23,146,3,3
	.word	80151
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,23,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT_Bits',0,23,153,3,3
	.word	80244
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,23,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	887
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT_Bits',0,23,160,3,3
	.word	80337
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,23,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	887
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CPU_PC_Bits',0,23,167,3,3
	.word	80430
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,23,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0_Bits',0,23,175,3,3
	.word	80515
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,23,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	887
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1_Bits',0,23,183,3,3
	.word	80631
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,23,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2_Bits',0,23,190,3,3
	.word	80742
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,23,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	887
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	887
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	887
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	887
	.byte	10,0,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI_Bits',0,23,200,3,3
	.word	80843
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,23,203,3,16,4,11
	.byte	'TA',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR_Bits',0,23,206,3,3
	.word	80973
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,23,209,3,16,4,11
	.byte	'IED',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	887
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	887
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR_Bits',0,23,221,3,3
	.word	81042
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,23,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	887
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0_Bits',0,23,229,3,3
	.word	81271
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,23,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	887
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	887
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1_Bits',0,23,237,3,3
	.word	81384
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,23,240,3,16,4,11
	.byte	'PSI',0,4
	.word	887
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	887
	.byte	16,0,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2_Bits',0,23,244,3,3
	.word	81497
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,23,247,3,16,4,11
	.byte	'FRE',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR_Bits',0,23,129,4,3
	.word	81588
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,23,132,4,16,4,11
	.byte	'CDC',0,4
	.word	887
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	887
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	887
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	887
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	887
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	887
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_PSW_Bits',0,23,147,4,3
	.word	81791
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,23,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	887
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	887
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	887
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	887
	.byte	1,0,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN_Bits',0,23,156,4,3
	.word	82034
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,23,159,4,16,4,11
	.byte	'PC',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	887
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	887
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	887
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	887
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	887
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	887
	.byte	7,0,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON_Bits',0,23,171,4,3
	.word	82162
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,23,174,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,23,177,4,3
	.word	82403
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,23,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,23,183,4,3
	.word	82486
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,23,186,4,16,4,11
	.byte	'EN',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,23,189,4,3
	.word	82577
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,23,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,23,195,4,3
	.word	82668
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,23,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,23,202,4,3
	.word	82767
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,23,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,23,209,4,3
	.word	82874
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,23,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT_Bits',0,23,220,4,3
	.word	82981
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,23,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON_Bits',0,23,231,4,3
	.word	83135
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,23,234,4,16,4,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	887
	.byte	27,0,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,23,238,4,3
	.word	83296
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,23,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	887
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	887
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	887
	.byte	15,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON_Bits',0,23,249,4,3
	.word	83394
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,23,252,4,16,4,11
	.byte	'Timer',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,23,255,4,3
	.word	83566
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,23,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	887
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR_Bits',0,23,133,5,3
	.word	83646
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,23,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	887
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	887
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	887
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	887
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	887
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	887
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	887
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	887
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	887
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	887
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	887
	.byte	3,0,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT_Bits',0,23,153,5,3
	.word	83719
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,23,156,5,16,4,11
	.byte	'T0',0,4
	.word	887
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	887
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	887
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	887
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	887
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	887
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	887
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	887
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	887
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,23,167,5,3
	.word	84037
	.byte	12,23,175,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75255
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_A',0,23,180,5,3
	.word	84232
	.byte	12,23,183,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75316
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BIV',0,23,188,5,3
	.word	84291
	.byte	12,23,191,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75395
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_BTV',0,23,196,5,3
	.word	84352
	.byte	12,23,199,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75481
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCNT',0,23,204,5,3
	.word	84413
	.byte	12,23,207,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75570
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CCTRL',0,23,212,5,3
	.word	84475
	.byte	12,23,215,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75716
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_COMPAT',0,23,220,5,3
	.word	84538
	.byte	12,23,223,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75843
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CORE_ID',0,23,228,5,3
	.word	84602
	.byte	12,23,231,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75941
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_L',0,23,236,5,3
	.word	84667
	.byte	12,23,239,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76034
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPR_U',0,23,244,5,3
	.word	84730
	.byte	12,23,247,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76127
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPU_ID',0,23,252,5,3
	.word	84793
	.byte	12,23,255,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76234
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CPXE',0,23,132,6,3
	.word	84857
	.byte	12,23,135,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76321
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CREVT',0,23,140,6,3
	.word	84919
	.byte	12,23,143,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76475
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_CUS_ID',0,23,148,6,3
	.word	84982
	.byte	12,23,151,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76569
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_D',0,23,156,6,3
	.word	85046
	.byte	12,23,159,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76632
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DATR',0,23,164,6,3
	.word	85105
	.byte	12,23,167,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76850
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGSR',0,23,172,6,3
	.word	85167
	.byte	12,23,175,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77065
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DBGTCR',0,23,180,6,3
	.word	85230
	.byte	12,23,183,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77159
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON0',0,23,188,6,3
	.word	85294
	.byte	12,23,191,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77275
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCON2',0,23,196,6,3
	.word	85357
	.byte	12,23,199,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77376
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DCX',0,23,204,6,3
	.word	85420
	.byte	12,23,207,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77469
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DEADD',0,23,212,6,3
	.word	85481
	.byte	12,23,215,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77549
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIEAR',0,23,220,6,3
	.word	85544
	.byte	12,23,223,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77618
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DIETR',0,23,228,6,3
	.word	85607
	.byte	12,23,231,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77847
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DMS',0,23,236,6,3
	.word	85670
	.byte	12,23,239,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77940
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_L',0,23,244,6,3
	.word	85731
	.byte	12,23,247,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78035
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPR_U',0,23,252,6,3
	.word	85794
	.byte	12,23,255,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78130
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPRE',0,23,132,7,3
	.word	85857
	.byte	12,23,135,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78220
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DPWE',0,23,140,7,3
	.word	85919
	.byte	12,23,143,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78310
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_DSTR',0,23,148,7,3
	.word	85981
	.byte	12,23,151,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78634
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_EXEVT',0,23,156,7,3
	.word	86043
	.byte	12,23,159,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78788
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FCX',0,23,164,7,3
	.word	86106
	.byte	12,23,167,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78894
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,23,172,7,3
	.word	86167
	.byte	12,23,175,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79243
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,23,180,7,3
	.word	86237
	.byte	12,23,183,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79403
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,23,188,7,3
	.word	86307
	.byte	12,23,191,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79484
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,23,196,7,3
	.word	86376
	.byte	12,23,199,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79571
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,23,204,7,3
	.word	86447
	.byte	12,23,207,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79658
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,23,212,7,3
	.word	86518
	.byte	12,23,215,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79745
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICNT',0,23,220,7,3
	.word	86589
	.byte	12,23,223,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79836
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ICR',0,23,228,7,3
	.word	86651
	.byte	12,23,231,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79979
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_ISP',0,23,236,7,3
	.word	86712
	.byte	12,23,239,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80045
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_LCX',0,23,244,7,3
	.word	86773
	.byte	12,23,247,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80151
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M1CNT',0,23,252,7,3
	.word	86834
	.byte	12,23,255,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80244
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M2CNT',0,23,132,8,3
	.word	86897
	.byte	12,23,135,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80337
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_M3CNT',0,23,140,8,3
	.word	86960
	.byte	12,23,143,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80430
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PC',0,23,148,8,3
	.word	87023
	.byte	12,23,151,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON0',0,23,156,8,3
	.word	87083
	.byte	12,23,159,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80631
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON1',0,23,164,8,3
	.word	87146
	.byte	12,23,167,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80742
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCON2',0,23,172,8,3
	.word	87209
	.byte	12,23,175,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80843
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PCXI',0,23,180,8,3
	.word	87272
	.byte	12,23,183,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80973
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIEAR',0,23,188,8,3
	.word	87334
	.byte	12,23,191,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81042
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PIETR',0,23,196,8,3
	.word	87397
	.byte	12,23,199,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81271
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA0',0,23,204,8,3
	.word	87460
	.byte	12,23,207,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81384
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA1',0,23,212,8,3
	.word	87522
	.byte	12,23,215,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81497
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PMA2',0,23,220,8,3
	.word	87584
	.byte	12,23,223,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81588
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSTR',0,23,228,8,3
	.word	87646
	.byte	12,23,231,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81791
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_PSW',0,23,236,8,3
	.word	87708
	.byte	12,23,239,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82034
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SEGEN',0,23,244,8,3
	.word	87769
	.byte	12,23,247,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82162
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SMACON',0,23,252,8,3
	.word	87832
	.byte	12,23,255,8,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82403
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENA',0,23,132,9,3
	.word	87896
	.byte	12,23,135,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82486
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_ACCENB',0,23,140,9,3
	.word	87966
	.byte	12,23,143,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82577
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,23,148,9,3
	.word	88036
	.byte	12,23,151,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82668
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,23,156,9,3
	.word	88110
	.byte	12,23,159,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82767
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,23,164,9,3
	.word	88184
	.byte	12,23,167,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82874
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,23,172,9,3
	.word	88254
	.byte	12,23,175,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82981
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SWEVT',0,23,180,9,3
	.word	88324
	.byte	12,23,183,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83135
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_SYSCON',0,23,188,9,3
	.word	88387
	.byte	12,23,191,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83296
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TASK_ASI',0,23,196,9,3
	.word	88451
	.byte	12,23,199,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83394
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_CON',0,23,204,9,3
	.word	88517
	.byte	12,23,207,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83566
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TPS_TIMER',0,23,212,9,3
	.word	88582
	.byte	12,23,215,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83646
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_ADR',0,23,220,9,3
	.word	88649
	.byte	12,23,223,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83719
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TR_EVT',0,23,228,9,3
	.word	88713
	.byte	12,23,231,9,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84037
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CPU_TRIG_ACC',0,23,236,9,3
	.word	88777
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,23,247,9,25,8,13
	.byte	'L',0
	.word	84667
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	84730
	.byte	4,2,35,4,0,14
	.word	88843
	.byte	29
	.byte	'Ifx_CPU_CPR',0,23,251,9,3
	.word	88885
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,23,254,9,25,8,13
	.byte	'L',0
	.word	85731
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	85794
	.byte	4,2,35,4,0,14
	.word	88911
	.byte	29
	.byte	'Ifx_CPU_DPR',0,23,130,10,3
	.word	88953
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,23,133,10,25,16,13
	.byte	'LA',0
	.word	88184
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	88254
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	88036
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	88110
	.byte	4,2,35,12,0,14
	.word	88979
	.byte	29
	.byte	'Ifx_CPU_SPROT_RGN',0,23,139,10,3
	.word	89061
	.byte	15,12
	.word	88582
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,23,142,10,25,16,13
	.byte	'CON',0
	.word	88517
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	89093
	.byte	12,2,35,4,0,14
	.word	89102
	.byte	29
	.byte	'Ifx_CPU_TPS',0,23,146,10,3
	.word	89150
	.byte	10
	.byte	'_Ifx_CPU_TR',0,23,149,10,25,8,13
	.byte	'EVT',0
	.word	88713
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	88649
	.byte	4,2,35,4,0,14
	.word	89176
	.byte	29
	.byte	'Ifx_CPU_TR',0,23,153,10,3
	.word	89221
	.byte	15,176,32
	.word	488
	.byte	16,175,32,0,15,208,223,1
	.word	488
	.byte	16,207,223,1,0,15,248,1
	.word	488
	.byte	16,247,1,0,15,244,29
	.word	488
	.byte	16,243,29,0,15,188,3
	.word	488
	.byte	16,187,3,0,15,232,3
	.word	488
	.byte	16,231,3,0,15,252,23
	.word	488
	.byte	16,251,23,0,15,228,63
	.word	488
	.byte	16,227,63,0,15,128,1
	.word	88911
	.byte	16,15,0,14
	.word	89336
	.byte	15,128,31
	.word	488
	.byte	16,255,30,0,15,64
	.word	88843
	.byte	16,7,0,14
	.word	89362
	.byte	15,192,31
	.word	488
	.byte	16,191,31,0,15,16
	.word	84857
	.byte	16,3,0,15,16
	.word	85857
	.byte	16,3,0,15,16
	.word	85919
	.byte	16,3,0,15,208,7
	.word	488
	.byte	16,207,7,0,14
	.word	89102
	.byte	15,240,23
	.word	488
	.byte	16,239,23,0,15,64
	.word	89176
	.byte	16,7,0,14
	.word	89441
	.byte	15,192,23
	.word	488
	.byte	16,191,23,0,15,232,1
	.word	488
	.byte	16,231,1,0,15,180,1
	.word	488
	.byte	16,179,1,0,15,172,1
	.word	488
	.byte	16,171,1,0,15,64
	.word	85046
	.byte	16,15,0,15,64
	.word	84232
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,23,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	89246
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	87769
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	89257
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	88451
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	89270
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	87460
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	87522
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	87584
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	89281
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	85357
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4659
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	87832
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	85981
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2840
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	85105
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	85481
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	85544
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	85607
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4030
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	85294
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	89292
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	87646
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	87146
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	87209
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	87083
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	87334
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	87397
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	89303
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	84538
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	89314
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	86167
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	86307
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	86237
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2840
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	86376
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	86447
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	86518
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	89325
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	89346
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	89351
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	89371
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	89376
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	89387
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	89396
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	89405
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	89414
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	89425
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	89430
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	89450
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	89455
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	84475
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	84413
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	86589
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	86834
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	86897
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	86960
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	89466
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	85167
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2840
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	86043
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	84919
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	88324
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	22004
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	88777
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4999
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	85670
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	85420
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	85230
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	89477
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	87272
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	87708
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	87023
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4659
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	88387
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	84793
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	84602
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	84291
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	84352
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	86712
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	86651
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4659
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	86106
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	86773
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	12273
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	84982
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	89488
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	89499
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	19923
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	89508
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	19923
	.byte	64,4,35,192,255,3,0,14
	.word	89517
	.byte	29
	.byte	'Ifx_CPU',0,23,130,11,3
	.word	91308
	.byte	17,10,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,29
	.byte	'IfxCpu_Id',0,10,132,1,3
	.word	91330
	.byte	29
	.byte	'IfxCpu_ResourceCpu',0,10,161,1,3
	.word	10156
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,24,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_STM_ACCEN0_Bits',0,24,79,3
	.word	91428
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,24,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1_Bits',0,24,85,3
	.word	91985
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,24,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAP_Bits',0,24,91,3
	.word	92062
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,24,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV_Bits',0,24,97,3
	.word	92134
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,24,100,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_CLC_Bits',0,24,107,3
	.word	92210
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,24,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	488
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	488
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	488
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	488
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	3,0,2,35,3,0,29
	.byte	'Ifx_STM_CMCON_Bits',0,24,120,3
	.word	92351
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,24,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_CMP_Bits',0,24,126,3
	.word	92569
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,24,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	465
	.byte	25,0,2,35,0,0,29
	.byte	'Ifx_STM_ICR_Bits',0,24,139,1,3
	.word	92636
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,24,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_STM_ID_Bits',0,24,147,1,3
	.word	92839
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,24,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_STM_ISCR_Bits',0,24,157,1,3
	.word	92946
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,24,160,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST0_Bits',0,24,165,1,3
	.word	93097
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,24,168,1,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRST1_Bits',0,24,172,1,3
	.word	93208
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,24,175,1,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR_Bits',0,24,179,1,3
	.word	93300
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,24,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_STM_OCS_Bits',0,24,189,1,3
	.word	93396
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,24,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0_Bits',0,24,195,1,3
	.word	93542
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,24,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV_Bits',0,24,201,1,3
	.word	93614
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,24,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM1_Bits',0,24,207,1,3
	.word	93690
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,24,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM2_Bits',0,24,213,1,3
	.word	93762
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,24,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM3_Bits',0,24,219,1,3
	.word	93834
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,24,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM4_Bits',0,24,225,1,3
	.word	93907
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,24,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM5_Bits',0,24,231,1,3
	.word	93980
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,24,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_STM_TIM6_Bits',0,24,237,1,3
	.word	94053
	.byte	12,24,245,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91428
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN0',0,24,250,1,3
	.word	94126
	.byte	12,24,253,1,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	91985
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ACCEN1',0,24,130,2,3
	.word	94190
	.byte	12,24,133,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92062
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAP',0,24,138,2,3
	.word	94254
	.byte	12,24,141,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92134
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CAPSV',0,24,146,2,3
	.word	94315
	.byte	12,24,149,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92210
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CLC',0,24,154,2,3
	.word	94378
	.byte	12,24,157,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92351
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMCON',0,24,162,2,3
	.word	94439
	.byte	12,24,165,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92569
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_CMP',0,24,170,2,3
	.word	94502
	.byte	12,24,173,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92636
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ICR',0,24,178,2,3
	.word	94563
	.byte	12,24,181,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92839
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ID',0,24,186,2,3
	.word	94624
	.byte	12,24,189,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	92946
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_ISCR',0,24,194,2,3
	.word	94684
	.byte	12,24,197,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93097
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST0',0,24,202,2,3
	.word	94746
	.byte	12,24,205,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93208
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRST1',0,24,210,2,3
	.word	94809
	.byte	12,24,213,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93300
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_KRSTCLR',0,24,218,2,3
	.word	94872
	.byte	12,24,221,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93396
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_OCS',0,24,226,2,3
	.word	94937
	.byte	12,24,229,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93542
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0',0,24,234,2,3
	.word	94998
	.byte	12,24,237,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93614
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM0SV',0,24,242,2,3
	.word	95060
	.byte	12,24,245,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93690
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM1',0,24,250,2,3
	.word	95124
	.byte	12,24,253,2,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93762
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM2',0,24,130,3,3
	.word	95186
	.byte	12,24,133,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93834
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM3',0,24,138,3,3
	.word	95248
	.byte	12,24,141,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93907
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM4',0,24,146,3,3
	.word	95310
	.byte	12,24,149,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	93980
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM5',0,24,154,3,3
	.word	95372
	.byte	12,24,157,3,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	94053
	.byte	4,2,35,0,0,29
	.byte	'Ifx_STM_TIM6',0,24,162,3,3
	.word	95434
	.byte	17,9,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,29
	.byte	'IfxCpu_CounterMode',0,9,148,1,3
	.word	95496
	.byte	21,9,160,1,9,6,13
	.byte	'counter',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	488
	.byte	1,2,35,4,0,29
	.byte	'IfxCpu_Counter',0,9,164,1,3
	.word	95585
	.byte	21,9,172,1,9,32,13
	.byte	'instruction',0
	.word	95585
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	95585
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	95585
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	95585
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	95585
	.byte	6,2,35,24,0,29
	.byte	'IfxCpu_Perf',0,9,179,1,3
	.word	95651
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,25,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,25,79,3
	.word	95769
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,25,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,25,85,3
	.word	96330
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,25,88,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,25,95,3
	.word	96411
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,25,98,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,25,111,3
	.word	96564
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,25,114,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,25,121,3
	.word	96812
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,25,124,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0_Bits',0,25,128,1,3
	.word	96958
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,25,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM1_Bits',0,25,136,1,3
	.word	97056
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,25,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_COMM2_Bits',0,25,144,1,3
	.word	97172
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,25,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRD_Bits',0,25,153,1,3
	.word	97288
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,25,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCRP_Bits',0,25,162,1,3
	.word	97428
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,25,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	465
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1039
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_ECCW_Bits',0,25,171,1,3
	.word	97568
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,25,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1039
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	488
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FCON_Bits',0,25,193,1,3
	.word	97707
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,25,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	488
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	488
	.byte	8,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FPRO_Bits',0,25,218,1,3
	.word	98069
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,25,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1039
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_FSR_Bits',0,25,254,1,3
	.word	98510
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,25,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_ID_Bits',0,25,134,2,3
	.word	99116
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,25,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1039
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARD_Bits',0,25,147,2,3
	.word	99227
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,25,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1039
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_MARP_Bits',0,25,159,2,3
	.word	99441
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,25,162,2,16,4,11
	.byte	'L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1039
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	488
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCOND_Bits',0,25,179,2,3
	.word	99628
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,25,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,25,188,2,3
	.word	99952
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,25,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1039
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,25,199,2,3
	.word	100095
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1039
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	488
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1039
	.byte	14,0,2,35,2,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,25,219,2,3
	.word	100284
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,25,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	488
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,25,254,2,3
	.word	100647
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,25,129,3,16,4,11
	.byte	'S0L',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONP_Bits',0,25,160,3,3
	.word	101242
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,25,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	488
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,25,194,3,3
	.word	101766
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,25,197,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,25,201,3,3
	.word	102348
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,25,204,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,25,208,3,3
	.word	102450
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,25,211,3,16,4,11
	.byte	'TAG',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	465
	.byte	26,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,25,215,3,3
	.word	102552
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,25,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD_Bits',0,25,222,3,3
	.word	102654
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,25,225,3,16,4,11
	.byte	'STRT',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	488
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	488
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_FLASH_RRCT_Bits',0,25,236,3,3
	.word	102748
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,25,239,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0_Bits',0,25,242,3,3
	.word	102958
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,25,245,3,16,4,11
	.byte	'DATA',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1_Bits',0,25,248,3,3
	.word	103031
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,25,251,3,16,4,11
	.byte	'SEL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,25,130,4,3
	.word	103104
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,25,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,25,137,4,3
	.word	103259
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,25,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	465
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	488
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,25,147,4,3
	.word	103364
	.byte	12,25,155,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	95769
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN0',0,25,160,4,3
	.word	103512
	.byte	12,25,163,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96330
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ACCEN1',0,25,168,4,3
	.word	103578
	.byte	12,25,171,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96411
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_CFG',0,25,176,4,3
	.word	103644
	.byte	12,25,179,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96564
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_STAT',0,25,184,4,3
	.word	103712
	.byte	12,25,187,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96812
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_CBAB_TOP',0,25,192,4,3
	.word	103781
	.byte	12,25,195,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	96958
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM0',0,25,200,4,3
	.word	103849
	.byte	12,25,203,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97056
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM1',0,25,208,4,3
	.word	103914
	.byte	12,25,211,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97172
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_COMM2',0,25,216,4,3
	.word	103979
	.byte	12,25,219,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97288
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRD',0,25,224,4,3
	.word	104044
	.byte	12,25,227,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97428
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCRP',0,25,232,4,3
	.word	104109
	.byte	12,25,235,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97568
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ECCW',0,25,240,4,3
	.word	104174
	.byte	12,25,243,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	97707
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FCON',0,25,248,4,3
	.word	104238
	.byte	12,25,251,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98069
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FPRO',0,25,128,5,3
	.word	104302
	.byte	12,25,131,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	98510
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_FSR',0,25,136,5,3
	.word	104366
	.byte	12,25,139,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99116
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_ID',0,25,144,5,3
	.word	104429
	.byte	12,25,147,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99227
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARD',0,25,152,5,3
	.word	104491
	.byte	12,25,155,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99441
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_MARP',0,25,160,5,3
	.word	104555
	.byte	12,25,163,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99628
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCOND',0,25,168,5,3
	.word	104619
	.byte	12,25,171,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	99952
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONDBG',0,25,176,5,3
	.word	104686
	.byte	12,25,179,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100095
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSM',0,25,184,5,3
	.word	104755
	.byte	12,25,187,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100284
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,25,192,5,3
	.word	104824
	.byte	12,25,195,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	100647
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONOTP',0,25,200,5,3
	.word	104897
	.byte	12,25,203,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101242
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONP',0,25,208,5,3
	.word	104966
	.byte	12,25,211,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	101766
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_PROCONWOP',0,25,216,5,3
	.word	105033
	.byte	12,25,219,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102348
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG0',0,25,224,5,3
	.word	105102
	.byte	12,25,227,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102450
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG1',0,25,232,5,3
	.word	105170
	.byte	12,25,235,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102552
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RDB_CFG2',0,25,240,5,3
	.word	105238
	.byte	12,25,243,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102654
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRAD',0,25,248,5,3
	.word	105306
	.byte	12,25,251,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102748
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRCT',0,25,128,6,3
	.word	105370
	.byte	12,25,131,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	102958
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD0',0,25,136,6,3
	.word	105434
	.byte	12,25,139,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103031
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_RRD1',0,25,144,6,3
	.word	105498
	.byte	12,25,147,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103104
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_CFG',0,25,152,6,3
	.word	105562
	.byte	12,25,155,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103259
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_STAT',0,25,160,6,3
	.word	105630
	.byte	12,25,163,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	103364
	.byte	4,2,35,0,0,29
	.byte	'Ifx_FLASH_UBAB_TOP',0,25,168,6,3
	.word	105699
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,25,179,6,25,12,13
	.byte	'CFG',0
	.word	103644
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	103712
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	103781
	.byte	4,2,35,8,0,14
	.word	105767
	.byte	29
	.byte	'Ifx_FLASH_CBAB',0,25,184,6,3
	.word	105830
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,25,187,6,25,12,13
	.byte	'CFG0',0
	.word	105102
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	105170
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	105238
	.byte	4,2,35,8,0,14
	.word	105859
	.byte	29
	.byte	'Ifx_FLASH_RDB',0,25,192,6,3
	.word	105923
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,25,195,6,25,12,13
	.byte	'CFG',0
	.word	105562
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	105630
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	105699
	.byte	4,2,35,8,0,14
	.word	105951
	.byte	29
	.byte	'Ifx_FLASH_UBAB',0,25,200,6,3
	.word	106014
	.byte	29
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,26,148,1,16
	.word	204
	.byte	21,26,212,5,9,8,13
	.byte	'value',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10311
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_CcuconRegConfig',0,26,216,5,3
	.word	106083
	.byte	21,26,221,5,9,8,13
	.byte	'pDivider',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	488
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	488
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_InitialStepConfig',0,26,227,5,3
	.word	106154
	.byte	21,26,231,5,9,12,13
	.byte	'k2Step',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	261
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	106043
	.byte	4,2,35,8,0,29
	.byte	'IfxScuCcu_PllStepsConfig',0,26,236,5,3
	.word	106271
	.byte	3
	.word	201
	.byte	21,26,244,5,9,48,13
	.byte	'ccucon0',0
	.word	106083
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	106083
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	106083
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	106083
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	106083
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	106083
	.byte	8,2,35,40,0,29
	.byte	'IfxScuCcu_ClockDistributionConfig',0,26,252,5,3
	.word	106373
	.byte	21,26,128,6,9,8,13
	.byte	'value',0
	.word	10311
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	10311
	.byte	4,2,35,4,0,29
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,26,132,6,3
	.word	106525
	.byte	3
	.word	106271
	.byte	21,26,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	488
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	106601
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	106154
	.byte	8,2,35,8,0,29
	.byte	'IfxScuCcu_SysPllConfig',0,26,142,6,3
	.word	106606
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN0_Bits',0,27,45,16,4,11
	.byte	'EN0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	488
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	488
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	488
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	488
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	488
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	488
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	488
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	488
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	488
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	488
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	488
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	488
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	488
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	488
	.byte	1,0,2,35,3,0,29
	.byte	'Ifx_CCU6_ACCEN0_Bits',0,27,79,3
	.word	106723
	.byte	10
	.byte	'_Ifx_CCU6_ACCEN1_Bits',0,27,82,16,4,11
	.byte	'reserved_0',0,4
	.word	465
	.byte	32,0,2,35,0,0,29
	.byte	'Ifx_CCU6_ACCEN1_Bits',0,27,85,3
	.word	107282
	.byte	10
	.byte	'_Ifx_CCU6_CC60R_Bits',0,27,88,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC60R_Bits',0,27,92,3
	.word	107361
	.byte	10
	.byte	'_Ifx_CCU6_CC60SR_Bits',0,27,95,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC60SR_Bits',0,27,99,3
	.word	107454
	.byte	10
	.byte	'_Ifx_CCU6_CC61R_Bits',0,27,102,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC61R_Bits',0,27,106,3
	.word	107549
	.byte	10
	.byte	'_Ifx_CCU6_CC61SR_Bits',0,27,109,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC61SR_Bits',0,27,113,3
	.word	107642
	.byte	10
	.byte	'_Ifx_CCU6_CC62R_Bits',0,27,116,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC62R_Bits',0,27,120,3
	.word	107737
	.byte	10
	.byte	'_Ifx_CCU6_CC62SR_Bits',0,27,123,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC62SR_Bits',0,27,127,3
	.word	107830
	.byte	10
	.byte	'_Ifx_CCU6_CC63R_Bits',0,27,130,1,16,4,11
	.byte	'CCV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC63R_Bits',0,27,134,1,3
	.word	107925
	.byte	10
	.byte	'_Ifx_CCU6_CC63SR_Bits',0,27,137,1,16,4,11
	.byte	'CCS',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CC63SR_Bits',0,27,141,1,3
	.word	108020
	.byte	10
	.byte	'_Ifx_CCU6_CLC_Bits',0,27,144,1,16,4,11
	.byte	'DISR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_CCU6_CLC_Bits',0,27,151,1,3
	.word	108117
	.byte	10
	.byte	'_Ifx_CCU6_CMPMODIF_Bits',0,27,154,1,16,4,11
	.byte	'MCC60S',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'MCC61S',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MCC62S',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	3,2,2,35,0,11
	.byte	'MCC63S',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'MCC60R',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'MCC61R',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'MCC62R',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'MCC63R',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CCU6_CMPMODIF_Bits',0,27,168,1,3
	.word	108262
	.byte	10
	.byte	'_Ifx_CCU6_CMPSTAT_Bits',0,27,171,1,16,4,11
	.byte	'CC60ST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CC61ST',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CC62ST',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS60',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CCPOS61',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CCPOS62',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC63ST',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'CC60PS',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'COUT60PS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'CC61PS',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'COUT61PS',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CC62PS',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'COUT62PS',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'COUT63PS',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'T13IM',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_CMPSTAT_Bits',0,27,190,1,3
	.word	108559
	.byte	10
	.byte	'_Ifx_CCU6_ID_Bits',0,27,193,1,16,4,11
	.byte	'MODREV',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'MODNUMBER',0,1
	.word	488
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_ID_Bits',0,27,198,1,3
	.word	108945
	.byte	10
	.byte	'_Ifx_CCU6_IEN_Bits',0,27,201,1,16,4,11
	.byte	'ENCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ENCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ENCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ENCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ENCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ENCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ENT12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ENT12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ENT13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ENT13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'ENTRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'ENCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'ENWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'ENIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ENSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_IEN_Bits',0,27,220,1,3
	.word	109058
	.byte	10
	.byte	'_Ifx_CCU6_IMON_Bits',0,27,223,1,16,4,11
	.byte	'LBE',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0I',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1I',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2I',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CC60INI',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CC61INI',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC62INI',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTRAPI',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12HRI',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13HRI',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	465
	.byte	22,0,2,35,0,0,29
	.byte	'Ifx_CCU6_IMON_Bits',0,27,236,1,3
	.word	109434
	.byte	10
	.byte	'_Ifx_CCU6_INP_Bits',0,27,239,1,16,4,11
	.byte	'INPCC60',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'INPCC61',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'INPCC62',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'INPCHE',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'INPERR',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'INPT12',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'INPT13',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CCU6_INP_Bits',0,27,249,1,3
	.word	109695
	.byte	10
	.byte	'_Ifx_CCU6_IS_Bits',0,27,252,1,16,4,11
	.byte	'ICC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'ICC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'ICC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'ICC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'ICC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'ICC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'T12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'TRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'TRPS',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'CHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'WHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'IDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'STR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_IS_Bits',0,27,143,2,3
	.word	109900
	.byte	10
	.byte	'_Ifx_CCU6_ISR_Bits',0,27,146,2,16,4,11
	.byte	'RCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'RCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'RCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'RCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'RCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'RT12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'RT12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'RT13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'RT13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'RTRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'RCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'RWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'RIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'RSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_ISR_Bits',0,27,165,2,3
	.word	110243
	.byte	10
	.byte	'_Ifx_CCU6_ISS_Bits',0,27,168,2,16,4,11
	.byte	'SCC60R',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SCC60F',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SCC61R',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SCC61F',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SCC62R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'SCC62F',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'ST12OM',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'ST12PM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'ST13CM',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'ST13PM',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'STRPF',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'SWHC',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'SCHE',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'SWHE',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'SIDLE',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'SSTR',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_ISS_Bits',0,27,187,2,3
	.word	110604
	.byte	10
	.byte	'_Ifx_CCU6_KRST0_Bits',0,27,190,2,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	465
	.byte	30,0,2,35,0,0,29
	.byte	'Ifx_CCU6_KRST0_Bits',0,27,195,2,3
	.word	110958
	.byte	10
	.byte	'_Ifx_CCU6_KRST1_Bits',0,27,198,2,16,4,11
	.byte	'RST',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CCU6_KRST1_Bits',0,27,202,2,3
	.word	111071
	.byte	10
	.byte	'_Ifx_CCU6_KRSTCLR_Bits',0,27,205,2,16,4,11
	.byte	'CLR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	465
	.byte	31,0,2,35,0,0,29
	.byte	'Ifx_CCU6_KRSTCLR_Bits',0,27,209,2,3
	.word	111165
	.byte	10
	.byte	'_Ifx_CCU6_KSCSR_Bits',0,27,212,2,16,4,11
	.byte	'SB0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'SB1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'SB2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'SB3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	28,0,2,35,0,0,29
	.byte	'Ifx_CCU6_KSCSR_Bits',0,27,219,2,3
	.word	111263
	.byte	10
	.byte	'_Ifx_CCU6_LI_Bits',0,27,222,2,16,4,11
	.byte	'reserved_0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'CCPOS0EN',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'CCPOS1EN',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'CCPOS2EN',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'CC60INEN',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'CC61INEN',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CC62INEN',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTRAPEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12HREN',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13HREN',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	488
	.byte	3,3,2,35,1,11
	.byte	'LBEEN',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'INPLBE',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_LI_Bits',0,27,238,2,3
	.word	111402
	.byte	10
	.byte	'_Ifx_CCU6_MCFG_Bits',0,27,241,2,16,4,11
	.byte	'T12',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T13',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'MCM',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	465
	.byte	29,0,2,35,0,0,29
	.byte	'Ifx_CCU6_MCFG_Bits',0,27,247,2,3
	.word	111733
	.byte	10
	.byte	'_Ifx_CCU6_MCMCTR_Bits',0,27,250,2,16,4,11
	.byte	'SWSEL',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'SWSYN',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'STE12U',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'STE12D',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'STE13U',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	465
	.byte	21,0,2,35,0,0,29
	.byte	'Ifx_CCU6_MCMCTR_Bits',0,27,132,3,3
	.word	111855
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUT_Bits',0,27,135,3,16,4,11
	.byte	'MCMP',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'R',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXPH',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'CURH',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CCU6_MCMOUT_Bits',0,27,143,3,3
	.word	112069
	.byte	10
	.byte	'_Ifx_CCU6_MCMOUTS_Bits',0,27,146,3,16,4,11
	.byte	'MCMPS',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'STRMCM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'EXPHS',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'CURHS',0,1
	.word	488
	.byte	3,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'STRHP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_MCMOUTS_Bits',0,27,156,3,3
	.word	112234
	.byte	10
	.byte	'_Ifx_CCU6_MODCTR_Bits',0,27,159,3,16,4,11
	.byte	'T12MODEN',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'MCMEN',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13MODEN',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'ECT13O',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_MODCTR_Bits',0,27,168,3,3
	.word	112449
	.byte	10
	.byte	'_Ifx_CCU6_MOSEL_Bits',0,27,171,3,16,4,11
	.byte	'TRIG0SEL',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'TRIG1SEL',0,1
	.word	488
	.byte	3,2,2,35,0,11
	.byte	'TRIG2SEL',0,2
	.word	1039
	.byte	3,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	465
	.byte	23,0,2,35,0,0,29
	.byte	'Ifx_CCU6_MOSEL_Bits',0,27,177,3,3
	.word	112651
	.byte	10
	.byte	'_Ifx_CCU6_OCS_Bits',0,27,180,3,16,4,11
	.byte	'TGS',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	465
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	488
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	488
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	488
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	488
	.byte	2,0,2,35,3,0,29
	.byte	'Ifx_CCU6_OCS_Bits',0,27,190,3,3
	.word	112790
	.byte	10
	.byte	'_Ifx_CCU6_PISEL0_Bits',0,27,193,3,16,4,11
	.byte	'ISCC60',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ISCC61',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'ISCC62',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'ISTRP',0,1
	.word	488
	.byte	2,0,2,35,0,11
	.byte	'ISPOS0',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'ISPOS1',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'ISPOS2',0,1
	.word	488
	.byte	2,2,2,35,1,11
	.byte	'IST12HR',0,1
	.word	488
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_PISEL0_Bits',0,27,204,3,3
	.word	112984
	.byte	10
	.byte	'_Ifx_CCU6_PISEL2_Bits',0,27,207,3,16,4,11
	.byte	'IST13HR',0,1
	.word	488
	.byte	2,6,2,35,0,11
	.byte	'ISCNT12',0,1
	.word	488
	.byte	2,4,2,35,0,11
	.byte	'ISCNT13',0,1
	.word	488
	.byte	2,2,2,35,0,11
	.byte	'T12EXT',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T13EXT',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CCU6_PISEL2_Bits',0,27,215,3,3
	.word	113210
	.byte	10
	.byte	'_Ifx_CCU6_PSLR_Bits',0,27,218,3,16,4,11
	.byte	'PSL',0,1
	.word	488
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'PSL63',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	465
	.byte	24,0,2,35,0,0,29
	.byte	'Ifx_CCU6_PSLR_Bits',0,27,224,3,3
	.word	113384
	.byte	10
	.byte	'_Ifx_CCU6_T12_Bits',0,27,227,3,16,4,11
	.byte	'T12CV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_T12_Bits',0,27,231,3,3
	.word	113515
	.byte	10
	.byte	'_Ifx_CCU6_T12DTC_Bits',0,27,234,3,16,4,11
	.byte	'DTM',0,1
	.word	488
	.byte	8,0,2,35,0,11
	.byte	'DTE0',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'DTE1',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'DTE2',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'DTR0',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'DTR1',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'DTR2',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	465
	.byte	17,0,2,35,0,0,29
	.byte	'Ifx_CCU6_T12DTC_Bits',0,27,245,3,3
	.word	113608
	.byte	10
	.byte	'_Ifx_CCU6_T12MSEL_Bits',0,27,248,3,16,4,11
	.byte	'MSEL60',0,1
	.word	488
	.byte	4,4,2,35,0,11
	.byte	'MSEL61',0,1
	.word	488
	.byte	4,0,2,35,0,11
	.byte	'MSEL62',0,1
	.word	488
	.byte	4,4,2,35,1,11
	.byte	'HSYNC',0,1
	.word	488
	.byte	3,1,2,35,1,11
	.byte	'DBYP',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_T12MSEL_Bits',0,27,128,4,3
	.word	113824
	.byte	10
	.byte	'_Ifx_CCU6_T12PR_Bits',0,27,131,4,16,4,11
	.byte	'T12PV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_T12PR_Bits',0,27,135,4,3
	.word	113995
	.byte	10
	.byte	'_Ifx_CCU6_T13_Bits',0,27,138,4,16,4,11
	.byte	'T13CV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_T13_Bits',0,27,142,4,3
	.word	114092
	.byte	10
	.byte	'_Ifx_CCU6_T13PR_Bits',0,27,145,4,16,4,11
	.byte	'T13PV',0,2
	.word	1039
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_T13PR_Bits',0,27,149,4,3
	.word	114185
	.byte	10
	.byte	'_Ifx_CCU6_TCTR0_Bits',0,27,152,4,16,4,11
	.byte	'T12CLK',0,1
	.word	488
	.byte	3,5,2,35,0,11
	.byte	'T12PRE',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'T12R',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'STE12',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'CDIR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'CTM',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13CLK',0,1
	.word	488
	.byte	3,5,2,35,1,11
	.byte	'T13PRE',0,1
	.word	488
	.byte	1,4,2,35,1,11
	.byte	'T13R',0,1
	.word	488
	.byte	1,3,2,35,1,11
	.byte	'STE13',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	465
	.byte	18,0,2,35,0,0,29
	.byte	'Ifx_CCU6_TCTR0_Bits',0,27,165,4,3
	.word	114282
	.byte	10
	.byte	'_Ifx_CCU6_TCTR2_Bits',0,27,168,4,16,4,11
	.byte	'T12SSC',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T13SSC',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'T13TEC',0,1
	.word	488
	.byte	3,3,2,35,0,11
	.byte	'T13TED',0,1
	.word	488
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T12RSEL',0,1
	.word	488
	.byte	2,6,2,35,1,11
	.byte	'T13RSEL',0,1
	.word	488
	.byte	2,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	465
	.byte	20,0,2,35,0,0,29
	.byte	'Ifx_CCU6_TCTR2_Bits',0,27,178,4,3
	.word	114531
	.byte	10
	.byte	'_Ifx_CCU6_TCTR4_Bits',0,27,181,4,16,4,11
	.byte	'T12RR',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'T12RS',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'T12RES',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'DTRES',0,1
	.word	488
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	488
	.byte	1,3,2,35,0,11
	.byte	'T12CNT',0,1
	.word	488
	.byte	1,2,2,35,0,11
	.byte	'T12STR',0,1
	.word	488
	.byte	1,1,2,35,0,11
	.byte	'T12STD',0,1
	.word	488
	.byte	1,0,2,35,0,11
	.byte	'T13RR',0,1
	.word	488
	.byte	1,7,2,35,1,11
	.byte	'T13RS',0,1
	.word	488
	.byte	1,6,2,35,1,11
	.byte	'T13RES',0,1
	.word	488
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	488
	.byte	2,3,2,35,1,11
	.byte	'T13CNT',0,1
	.word	488
	.byte	1,2,2,35,1,11
	.byte	'T13STR',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'T13STD',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_TCTR4_Bits',0,27,199,4,3
	.word	114743
	.byte	10
	.byte	'_Ifx_CCU6_TRPCTR_Bits',0,27,202,4,16,4,11
	.byte	'TRPM0',0,1
	.word	488
	.byte	1,7,2,35,0,11
	.byte	'TRPM1',0,1
	.word	488
	.byte	1,6,2,35,0,11
	.byte	'TRPM2',0,1
	.word	488
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	488
	.byte	5,0,2,35,0,11
	.byte	'TRPEN',0,1
	.word	488
	.byte	6,2,2,35,1,11
	.byte	'TRPEN13',0,1
	.word	488
	.byte	1,1,2,35,1,11
	.byte	'TRPPEN',0,1
	.word	488
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1039
	.byte	16,0,2,35,2,0,29
	.byte	'Ifx_CCU6_TRPCTR_Bits',0,27,212,4,3
	.word	115097
	.byte	12,27,220,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	106723
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_ACCEN0',0,27,225,4,3
	.word	115306
	.byte	12,27,228,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107282
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_ACCEN1',0,27,233,4,3
	.word	115371
	.byte	12,27,236,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107361
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC60R',0,27,241,4,3
	.word	115436
	.byte	12,27,244,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107454
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC60SR',0,27,249,4,3
	.word	115500
	.byte	12,27,252,4,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107549
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC61R',0,27,129,5,3
	.word	115565
	.byte	12,27,132,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107642
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC61SR',0,27,137,5,3
	.word	115629
	.byte	12,27,140,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107737
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC62R',0,27,145,5,3
	.word	115694
	.byte	12,27,148,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107830
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC62SR',0,27,153,5,3
	.word	115758
	.byte	12,27,156,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	107925
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC63R',0,27,161,5,3
	.word	115823
	.byte	12,27,164,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	108020
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CC63SR',0,27,169,5,3
	.word	115887
	.byte	12,27,172,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	108117
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CLC',0,27,177,5,3
	.word	115952
	.byte	12,27,180,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	108262
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CMPMODIF',0,27,185,5,3
	.word	116014
	.byte	12,27,188,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	108559
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_CMPSTAT',0,27,193,5,3
	.word	116081
	.byte	12,27,196,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	108945
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_ID',0,27,201,5,3
	.word	116147
	.byte	12,27,204,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	109058
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_IEN',0,27,209,5,3
	.word	116208
	.byte	12,27,212,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	109434
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_IMON',0,27,217,5,3
	.word	116270
	.byte	12,27,220,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	109695
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_INP',0,27,225,5,3
	.word	116333
	.byte	12,27,228,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	109900
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_IS',0,27,233,5,3
	.word	116395
	.byte	12,27,236,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	110243
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_ISR',0,27,241,5,3
	.word	116456
	.byte	12,27,244,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	110604
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_ISS',0,27,249,5,3
	.word	116518
	.byte	12,27,252,5,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	110958
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_KRST0',0,27,129,6,3
	.word	116580
	.byte	12,27,132,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111071
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_KRST1',0,27,137,6,3
	.word	116644
	.byte	12,27,140,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111165
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_KRSTCLR',0,27,145,6,3
	.word	116708
	.byte	12,27,148,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111263
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_KSCSR',0,27,153,6,3
	.word	116774
	.byte	12,27,156,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111402
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_LI',0,27,161,6,3
	.word	116838
	.byte	12,27,164,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111733
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MCFG',0,27,169,6,3
	.word	116899
	.byte	12,27,172,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	111855
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MCMCTR',0,27,177,6,3
	.word	116962
	.byte	12,27,180,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112069
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MCMOUT',0,27,185,6,3
	.word	117027
	.byte	12,27,188,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112234
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MCMOUTS',0,27,193,6,3
	.word	117092
	.byte	12,27,196,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112449
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MODCTR',0,27,201,6,3
	.word	117158
	.byte	12,27,204,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112651
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_MOSEL',0,27,209,6,3
	.word	117223
	.byte	12,27,212,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112790
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_OCS',0,27,217,6,3
	.word	117287
	.byte	12,27,220,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	112984
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_PISEL0',0,27,225,6,3
	.word	117349
	.byte	12,27,228,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113210
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_PISEL2',0,27,233,6,3
	.word	117414
	.byte	12,27,236,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113384
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_PSLR',0,27,241,6,3
	.word	117479
	.byte	12,27,244,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113515
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T12',0,27,249,6,3
	.word	117542
	.byte	12,27,252,6,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113608
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T12DTC',0,27,129,7,3
	.word	117604
	.byte	12,27,132,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113824
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T12MSEL',0,27,137,7,3
	.word	117669
	.byte	12,27,140,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	113995
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T12PR',0,27,145,7,3
	.word	117735
	.byte	12,27,148,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114092
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T13',0,27,153,7,3
	.word	117799
	.byte	12,27,156,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114185
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_T13PR',0,27,161,7,3
	.word	117861
	.byte	12,27,164,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114282
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_TCTR0',0,27,169,7,3
	.word	117925
	.byte	12,27,172,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114531
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_TCTR2',0,27,177,7,3
	.word	117989
	.byte	12,27,180,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	114743
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_TCTR4',0,27,185,7,3
	.word	118053
	.byte	12,27,188,7,9,4,13
	.byte	'U',0
	.word	465
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	481
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	115097
	.byte	4,2,35,0,0,29
	.byte	'Ifx_CCU6_TRPCTR',0,27,193,7,3
	.word	118117
	.byte	17,11,90,9,1,18
	.byte	'IfxVadc_AnalogConverterMode_off',0,0,18
	.byte	'IfxVadc_AnalogConverterMode_slowStandby',0,1,18
	.byte	'IfxVadc_AnalogConverterMode_fastStandby',0,2,18
	.byte	'IfxVadc_AnalogConverterMode_normalOperation',0,3,0,29
	.byte	'IfxVadc_AnalogConverterMode',0,11,96,3
	.word	118182
	.byte	29
	.byte	'IfxVadc_ArbitrationRounds',0,11,106,3
	.word	32259
	.byte	29
	.byte	'IfxVadc_BoundaryExtension',0,11,128,1,3
	.word	34015
	.byte	29
	.byte	'IfxVadc_BoundarySelection',0,11,138,1,3
	.word	33866
	.byte	29
	.byte	'IfxVadc_ChannelId',0,11,161,1,3
	.word	28283
	.byte	29
	.byte	'IfxVadc_ChannelReference',0,11,169,1,3
	.word	33787
	.byte	29
	.byte	'IfxVadc_ChannelResolution',0,11,179,1,3
	.word	27321
	.byte	29
	.byte	'IfxVadc_ChannelResult',0,11,201,1,3
	.word	32902
	.byte	17,11,206,1,9,1,18
	.byte	'IfxVadc_ChannelSelectionStyle_channelNumber',0,0,18
	.byte	'IfxVadc_ChannelSelectionStyle_binary',0,1,0,29
	.byte	'IfxVadc_ChannelSelectionStyle',0,11,211,1,3
	.word	118619
	.byte	17,11,223,1,9,1,18
	.byte	'IfxVadc_EmuxCodingScheme_binary',0,0,18
	.byte	'IfxVadc_EmuxCodingScheme_gray',0,1,0,29
	.byte	'IfxVadc_EmuxCodingScheme',0,11,227,1,3
	.word	118750
	.byte	17,11,231,1,9,1,18
	.byte	'IfxVadc_EmuxInterface_0',0,0,18
	.byte	'IfxVadc_EmuxInterface_1',0,1,0,29
	.byte	'IfxVadc_EmuxInterface',0,11,235,1,3
	.word	118857
	.byte	17,11,240,1,9,1,18
	.byte	'IfxVadc_EmuxSampleTimeControl_settingChanges',0,0,18
	.byte	'IfxVadc_EmuxSampleTimeControl_always',0,1,0,29
	.byte	'IfxVadc_EmuxSampleTimeControl',0,11,244,1,3
	.word	118947
	.byte	17,11,249,1,9,1,18
	.byte	'IfxVadc_EmuxSelectValue_0',0,0,18
	.byte	'IfxVadc_EmuxSelectValue_1',0,1,18
	.byte	'IfxVadc_EmuxSelectValue_2',0,2,18
	.byte	'IfxVadc_EmuxSelectValue_3',0,3,18
	.byte	'IfxVadc_EmuxSelectValue_4',0,4,18
	.byte	'IfxVadc_EmuxSelectValue_5',0,5,18
	.byte	'IfxVadc_EmuxSelectValue_6',0,6,18
	.byte	'IfxVadc_EmuxSelectValue_7',0,7,0,29
	.byte	'IfxVadc_EmuxSelectValue',0,11,131,2,3
	.word	119079
	.byte	17,11,136,2,9,1,18
	.byte	'IfxVadc_ExternalMultiplexerMode_softwareControl',0,0,18
	.byte	'IfxVadc_ExternalMultiplexerMode_steady',0,1,18
	.byte	'IfxVadc_ExternalMultiplexerMode_singleStep',0,2,18
	.byte	'IfxVadc_ExternalMultiplexerMode_sequence',0,3,0,29
	.byte	'IfxVadc_ExternalMultiplexerMode',0,11,142,2,3
	.word	119343
	.byte	17,11,146,2,9,1,18
	.byte	'IfxVadc_FifoMode_seperateResultRegister',0,0,18
	.byte	'IfxVadc_FifoMode_fifoStructure',0,1,18
	.byte	'IfxVadc_FifoMode_maximumMode',0,2,18
	.byte	'IfxVadc_FifoMode_minimumMode',0,3,0,29
	.byte	'IfxVadc_FifoMode',0,11,152,2,3
	.word	119570
	.byte	29
	.byte	'IfxVadc_GatingMode',0,11,162,2,3
	.word	31286
	.byte	29
	.byte	'IfxVadc_GatingSource',0,11,184,2,3
	.word	30444
	.byte	29
	.byte	'IfxVadc_InputClasses',0,11,194,2,3
	.word	33658
	.byte	29
	.byte	'IfxVadc_LimitCheck',0,11,204,2,3
	.word	34769
	.byte	29
	.byte	'IfxVadc_LowSupplyVoltageSelect',0,11,212,2,3
	.word	30030
	.byte	29
	.byte	'IfxVadc_RequestSlotPriority',0,11,163,3,3
	.word	31663
	.byte	29
	.byte	'IfxVadc_RequestSlotStartMode',0,11,171,3,3
	.word	31814
	.byte	17,11,175,3,9,1,18
	.byte	'IfxVadc_RequestSource_queue',0,0,18
	.byte	'IfxVadc_RequestSource_scan',0,1,18
	.byte	'IfxVadc_RequestSource_background',0,2,0,29
	.byte	'IfxVadc_RequestSource',0,11,180,3,3
	.word	119971
	.byte	17,11,185,3,9,1,18
	.byte	'IfxVadc_SleepMode_enable',0,0,18
	.byte	'IfxVadc_SleepMode_disable',0,1,0,29
	.byte	'IfxVadc_SleepMode',0,11,189,3,3
	.word	120103
	.byte	29
	.byte	'IfxVadc_SrcNr',0,11,203,3,3
	.word	33463
	.byte	29
	.byte	'IfxVadc_Status',0,11,218,3,3
	.word	29757
	.byte	29
	.byte	'IfxVadc_TriggerMode',0,11,228,3,3
	.word	31414
	.byte	29
	.byte	'IfxVadc_TriggerSource',0,11,250,3,3
	.word	30857
	.byte	29
	.byte	'IfxVadc_Adc',0,14,255,2,3
	.word	29004
	.byte	29
	.byte	'IfxVadc_Adc_GatingTriggerConfig',0,14,137,3,3
	.word	31570
	.byte	29
	.byte	'IfxVadc_Adc_ArbiterConfig',0,14,151,3,3
	.word	32411
	.byte	29
	.byte	'IfxVadc_Adc_BackgroundScanConfig',0,14,161,3,3
	.word	32139
	.byte	29
	.byte	'IfxVadc_Adc_ClassConfig',0,14,169,3,3
	.word	29974
	.byte	29
	.byte	'IfxVadc_Adc_Group',0,14,178,3,3
	.word	29030
	.byte	29
	.byte	'IfxVadc_Adc_QueueConfig',0,14,188,3,3
	.word	32025
	.byte	29
	.byte	'IfxVadc_Adc_ScanConfig',0,14,198,3,3
	.word	31915
	.byte	24
	.word	75174
	.byte	3
	.word	120563
	.byte	15,12
	.word	120568
	.byte	16,2,0,21,14,202,3,9,16,13
	.byte	'pins',0
	.word	120573
	.byte	12,2,35,0,13
	.byte	'outputMode',0
	.word	9592
	.byte	1,2,35,12,13
	.byte	'padDriver',0
	.word	74550
	.byte	1,2,35,13,0,29
	.byte	'IfxVadc_Adc_EmuxPinConfig',0,14,207,3,3
	.word	120582
	.byte	29
	.byte	'IfxVadc_Adc_Channel',0,14,218,3,3
	.word	33341
	.byte	29
	.byte	'IfxVadc_Adc_ChannelConfig',0,14,243,3,3
	.word	34908
	.byte	29
	.byte	'IfxVadc_Adc_Config',0,14,130,4,3
	.word	30109
	.byte	29
	.byte	'IfxVadc_Adc_GroupConfig',0,14,162,4,3
	.word	32553
	.byte	29
	.byte	'_iob_flag_t',0,28,82,25
	.word	1039
	.byte	7
	.byte	'char',0,1,6,29
	.byte	'int8',0,29,54,29
	.word	120822
	.byte	29
	.byte	'int16',0,29,55,29
	.word	36179
	.byte	29
	.byte	'int32',0,29,56,29
	.word	481
	.byte	29
	.byte	'int64',0,29,57,29
	.word	36448
	.byte	29
	.byte	'adc_channel_enum',0,17,78,2
	.word	35681
	.byte	29
	.byte	'adc_resolution_enum',0,17,86,2
	.word	36138
.L117:
	.byte	15,50
	.word	488
	.byte	16,49,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,13,0,73,19,11,15,56,9,0,0,21,19,1,58,15,59,15
	.byte	57,15,11,15,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,38,0,73,19,0,0,25,46,1,3,8,58,15,59,15,57,15,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,26,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,27,29,1,49,19
	.byte	0,0,28,11,0,49,19,0,0,29,22,0,3,8,58,15,59,15,57,15,73,19,0,0,30,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L18:
	.word	.L144-.L143
.L143:
	.half	3
	.word	.L146-.L145
.L145:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Vadc\\Std\\IfxVadc.h',0
	.byte	0,0,0
	.byte	'IfxVadc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxVadc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Vadc\\Adc\\IfxVadc_Adc.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'zf_common_debug.h',0,2,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_adc.h',0,0,0,0
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxVadc_PinMap.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'IfxCcu6_regdef.h',0,1,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'zf_common_typedef.h',0,2,0,0,0
.L146:
.L144:
	.sdecl	'.debug_info',debug,cluster('adc_convert')
	.sect	'.debug_info'
.L19:
	.word	407
	.half	3
	.word	.L20
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L22,.L21
	.byte	2
	.word	.L15
	.byte	3
	.byte	'adc_convert',0,1,51,8
	.word	.L36
	.byte	1,1,1
	.word	.L2,.L37,.L10
	.byte	4
	.byte	'vadc_chn',0,1,51,38
	.word	.L38,.L39
	.byte	5
	.word	.L2,.L37
	.byte	6
	.byte	'result',0,1,53,15
	.word	.L40,.L41
	.byte	6
	.byte	'temp',0,1,54,8
	.word	.L42,.L43
	.byte	7
	.word	.L44,.L45,.L46
	.byte	8
	.word	.L47,.L48
	.byte	8
	.word	.L49,.L50
	.byte	9
	.word	.L51,.L52
	.byte	6
	.byte	'tmpResult',0,2,168,16,18
	.word	.L40,.L55
	.byte	0,0,7
	.word	.L44,.L53,.L54
	.byte	8
	.word	.L47,.L48
	.byte	8
	.word	.L49,.L50
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('adc_convert')
	.sect	'.debug_abbrev'
.L20:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,85
	.byte	6,0,0,0
	.sdecl	'.debug_line',debug,cluster('adc_convert')
	.sect	'.debug_line'
.L21:
	.word	.L148-.L147
.L147:
	.half	3
	.word	.L150-.L149
.L149:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Vadc\\Std\\IfxVadc.h',0
	.byte	0,0,0,0
.L150:
	.byte	5,57,7,0,5,2
	.word	.L2
	.byte	3,56,1,5,55,9
	.half	.L151-.L2
	.byte	1,5,44,9
	.half	.L152-.L151
	.byte	1,5,72,9
	.half	.L153-.L152
	.byte	1,5,71,9
	.half	.L154-.L153
	.byte	1,4,2,5,29,9
	.half	.L45-.L154
	.byte	3,241,15,1,5,40,9
	.half	.L155-.L45
	.byte	1,5,5,9
	.half	.L118-.L155
	.byte	3,2,1,4,1,5,19,9
	.half	.L3-.L118
	.byte	3,142,112,1,5,11,9
	.half	.L46-.L3
	.byte	1,5,14,7,9
	.half	.L156-.L46
	.byte	3,2,1,5,28,9
	.half	.L157-.L156
	.byte	1,5,39,9
	.half	.L158-.L157
	.byte	1,5,11,9
	.half	.L159-.L158
	.byte	1,5,18,9
	.half	.L53-.L159
	.byte	3,2,1,5,26,9
	.half	.L54-.L53
	.byte	1,5,2,9
	.half	.L160-.L54
	.byte	1,5,1,9
	.half	.L4-.L160
	.byte	3,1,1,7,9
	.half	.L23-.L4
	.byte	0,1,1
.L148:
	.sdecl	'.debug_ranges',debug,cluster('adc_convert')
	.sect	'.debug_ranges'
.L22:
	.word	-1,.L2,0,.L23-.L2,0,0
.L52:
	.word	-1,.L2,.L45-.L2,.L46-.L2,.L53-.L2,.L54-.L2,0,0
	.sdecl	'.debug_info',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_info'
.L24:
	.word	334
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L27,.L26
	.byte	2
	.word	.L15
	.byte	3
	.byte	'adc_mean_filter_convert',0,1,73,8
	.word	.L36
	.byte	1,1,1
	.word	.L12,.L56,.L11
	.byte	4
	.byte	'vadc_chn',0,1,73,50
	.word	.L38,.L57
	.byte	4
	.byte	'count',0,1,73,66
	.word	.L42,.L58
	.byte	5
	.word	.L12,.L56
	.byte	6
	.byte	'i',0,1,75,11
	.word	.L42,.L59
	.byte	6
	.byte	'sum',0,1,76,12
	.word	.L60,.L61
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_line'
.L26:
	.word	.L162-.L161
.L161:
	.half	3
	.word	.L164-.L163
.L163:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0,0,0,0,0
.L164:
	.byte	5,8,7,0,5,2
	.word	.L12
	.byte	3,200,0,1,5,5,9
	.half	.L122-.L12
	.byte	3,5,1,5,9,9
	.half	.L123-.L122
	.byte	3,2,1,5,10,9
	.half	.L124-.L123
	.byte	3,1,1,5,21,9
	.half	.L125-.L124
	.byte	1,5,28,9
	.half	.L6-.L125
	.byte	3,2,1,5,13,9
	.half	.L128-.L6
	.byte	1,5,24,9
	.half	.L165-.L128
	.byte	3,126,1,5,21,9
	.half	.L5-.L165
	.byte	1,5,14,7,9
	.half	.L166-.L5
	.byte	3,5,1,5,12,9
	.half	.L129-.L166
	.byte	3,2,1,5,5,9
	.half	.L167-.L129
	.byte	1,5,1,9
	.half	.L7-.L167
	.byte	3,1,1,7,9
	.half	.L28-.L7
	.byte	0,1,1
.L162:
	.sdecl	'.debug_ranges',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_ranges'
.L27:
	.word	-1,.L12,0,.L28-.L12,0,0
	.sdecl	'.debug_info',debug,cluster('adc_init')
	.sect	'.debug_info'
.L29:
	.word	732
	.half	3
	.word	.L30
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L32,.L31
	.byte	2
	.word	.L15
	.byte	3
	.byte	'adc_init',0,1,100,6,1,1,1
	.word	.L14,.L62,.L13
	.byte	4
	.byte	'vadc_chn',0,1,100,33
	.word	.L38,.L63
	.byte	4
	.byte	'resolution',0,1,100,63
	.word	.L64,.L65
	.byte	5
	.word	.L14,.L62
	.byte	6
	.byte	'mudule_init_flag',0,1,102,18
	.word	.L42
	.byte	5,3
	.word	_999001_mudule_init_flag
	.byte	7
	.byte	'vadc',0,1,103,17
	.word	.L66,.L67
	.byte	7
	.byte	'adcGroup',0,1,104,23
	.word	.L68,.L69
	.byte	7
	.byte	'adcConfig',0,1,105,24
	.word	.L70,.L71
	.byte	5
	.word	.L9,.L62
	.byte	7
	.byte	'adcGroupConfig',0,1,120,29
	.word	.L72,.L73
	.byte	5
	.word	.L74,.L62
	.byte	7
	.byte	'adcChannelConfig',0,1,135,1,31
	.word	.L75,.L76
	.byte	7
	.byte	'adcChannel',0,1,136,1,31
	.word	.L77,.L78
	.byte	5
	.word	.L79,.L62
	.byte	7
	.byte	'channels',0,1,145,1,14
	.word	.L80,.L81
	.byte	7
	.byte	'mask',0,1,146,1,14
	.word	.L80,.L82
	.byte	8
	.word	.L83,.L84,.L85
	.byte	9
	.word	.L86,.L87
	.byte	9
	.word	.L88,.L89
	.byte	9
	.word	.L90,.L91
	.byte	9
	.word	.L92,.L93
	.byte	10
	.word	.L94,.L84,.L85
	.byte	8
	.word	.L95,.L96,.L85
	.byte	9
	.word	.L97,.L98
	.byte	9
	.word	.L99,.L100
	.byte	9
	.word	.L101,.L102
	.byte	9
	.word	.L103,.L104
	.byte	11
	.word	.L105,.L96,.L85
	.byte	0,0,0,8
	.word	.L106,.L107,.L108
	.byte	9
	.word	.L109,.L110
	.byte	10
	.word	.L111,.L107,.L108
	.byte	8
	.word	.L112,.L113,.L108
	.byte	9
	.word	.L114,.L115
	.byte	11
	.word	.L116,.L113,.L108
	.byte	0,0,0,0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('adc_init')
	.sect	'.debug_abbrev'
.L30:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,9,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,29,1,49,16,17,1,18,1,0
	.byte	0,9,5,0,49,16,2,6,0,0,10,11,1,49,16,17,1,18,1,0,0,11,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('adc_init')
	.sect	'.debug_line'
.L31:
	.word	.L169-.L168
.L168:
	.half	3
	.word	.L171-.L170
.L170:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Vadc\\Adc\\IfxVadc_Adc.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Vadc\\Std\\IfxVadc.h',0
	.byte	0,0,0,0
.L171:
	.byte	5,6,7,0,5,2
	.word	.L14
	.byte	3,227,0,1,5,35,9
	.half	.L172-.L14
	.byte	3,7,1,5,47,9
	.half	.L173-.L172
	.byte	1,5,9,9
	.half	.L131-.L173
	.byte	3,2,1,9
	.half	.L174-.L131
	.byte	3,2,1,5,28,9
	.half	.L175-.L174
	.byte	1,5,26,9
	.half	.L176-.L175
	.byte	1,5,33,9
	.half	.L177-.L176
	.byte	3,1,1,5,40,9
	.half	.L178-.L177
	.byte	1,5,29,9
	.half	.L179-.L178
	.byte	3,127,1,5,30,9
	.half	.L8-.L179
	.byte	3,6,1,5,19,9
	.half	.L180-.L8
	.byte	1,5,34,9
	.half	.L9-.L180
	.byte	3,4,1,5,51,9
	.half	.L181-.L9
	.byte	1,5,59,9
	.half	.L182-.L181
	.byte	3,2,1,5,57,9
	.half	.L132-.L182
	.byte	1,5,28,9
	.half	.L133-.L132
	.byte	1,5,44,9
	.half	.L183-.L133
	.byte	3,1,1,5,28,9
	.half	.L184-.L183
	.byte	1,5,63,9
	.half	.L185-.L184
	.byte	3,1,1,5,61,9
	.half	.L186-.L185
	.byte	1,5,70,9
	.half	.L187-.L186
	.byte	3,1,1,5,68,9
	.half	.L188-.L187
	.byte	1,5,69,9
	.half	.L189-.L188
	.byte	3,1,1,5,67,9
	.half	.L190-.L189
	.byte	1,5,47,9
	.half	.L191-.L190
	.byte	3,1,1,5,45,9
	.half	.L192-.L191
	.byte	1,5,51,9
	.half	.L193-.L192
	.byte	3,1,1,5,45,9
	.half	.L194-.L193
	.byte	1,5,47,9
	.half	.L195-.L194
	.byte	3,1,1,5,45,9
	.half	.L196-.L195
	.byte	1,5,51,9
	.half	.L197-.L196
	.byte	3,1,1,5,45,9
	.half	.L198-.L197
	.byte	1,5,28,9
	.half	.L199-.L198
	.byte	3,2,1,5,39,9
	.half	.L200-.L199
	.byte	1,5,36,9
	.half	.L74-.L200
	.byte	3,4,1,5,55,9
	.half	.L201-.L74
	.byte	1,5,71,9
	.half	.L202-.L201
	.byte	3,2,1,5,70,9
	.half	.L134-.L202
	.byte	1,5,40,9
	.half	.L135-.L134
	.byte	1,5,75,9
	.half	.L203-.L135
	.byte	3,1,1,5,74,9
	.half	.L136-.L203
	.byte	1,5,40,9
	.half	.L137-.L136
	.byte	1,5,42,9
	.half	.L204-.L137
	.byte	3,1,1,5,40,9
	.half	.L205-.L204
	.byte	1,5,30,9
	.half	.L206-.L205
	.byte	3,2,1,5,43,9
	.half	.L207-.L206
	.byte	1,5,26,9
	.half	.L79-.L207
	.byte	3,2,1,5,47,9
	.half	.L208-.L79
	.byte	1,5,28,9
	.half	.L209-.L208
	.byte	1,5,36,9
	.half	.L138-.L209
	.byte	3,2,1,4,2,5,35,9
	.half	.L84-.L138
	.byte	3,236,8,1,5,48,9
	.half	.L210-.L84
	.byte	1,4,3,5,44,9
	.half	.L96-.L210
	.byte	3,185,7,1,5,53,9
	.half	.L211-.L96
	.byte	1,5,58,9
	.half	.L212-.L211
	.byte	1,5,56,9
	.half	.L213-.L212
	.byte	1,5,65,9
	.half	.L214-.L213
	.byte	1,5,17,9
	.half	.L139-.L214
	.byte	3,1,1,5,29,9
	.half	.L215-.L139
	.byte	1,4,1,5,38,9
	.half	.L85-.L215
	.byte	3,220,111,1,4,2,5,37,9
	.half	.L107-.L85
	.byte	3,246,8,1,4,3,5,18,9
	.half	.L113-.L107
	.byte	3,149,9,1,5,24,9
	.half	.L216-.L113
	.byte	1,4,1,5,5,9
	.half	.L108-.L216
	.byte	3,247,109,1,5,19,9
	.half	.L140-.L108
	.byte	1,5,30,9
	.half	.L141-.L140
	.byte	1,5,1,9
	.half	.L142-.L141
	.byte	3,1,1,7,9
	.half	.L33-.L142
	.byte	0,1,1
.L169:
	.sdecl	'.debug_ranges',debug,cluster('adc_init')
	.sect	'.debug_ranges'
.L32:
	.word	-1,.L14,0,.L33-.L14,0,0
	.sdecl	'.debug_info',debug,cluster('adc_resolution')
	.sect	'.debug_info'
.L34:
	.word	229
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_adc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L15
	.byte	3
	.byte	'adc_resolution',0,18,42,7
	.word	.L117
	.byte	1,5,3
	.word	adc_resolution
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('adc_resolution')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('adc_convert')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L2,0,.L37-.L2
	.half	2
	.byte	138,0
	.word	0,0
.L48:
	.word	0,0
.L41:
	.word	0,0
.L50:
	.word	0,0
.L43:
	.word	-1,.L2,.L53-.L2,.L37-.L2
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L55:
	.word	-1,.L2,.L118-.L2,.L37-.L2
	.half	1
	.byte	95
	.word	0,0
.L39:
	.word	-1,.L2,0,.L37-.L2
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('adc_init')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L14,0,.L62-.L14
	.half	2
	.byte	145,116
	.word	0,0
.L76:
	.word	-1,.L14,0,.L62-.L14
	.half	2
	.byte	145,92
	.word	0,0
.L71:
	.word	-1,.L14,0,.L62-.L14
	.half	3
	.byte	145,136,127
	.word	0,0
.L69:
	.word	-1,.L14,0,.L62-.L14
	.half	3
	.byte	145,252,126
	.word	0,0
.L73:
	.word	-1,.L14,0,.L62-.L14
	.half	3
	.byte	145,168,127
	.word	0,0
.L13:
	.word	-1,.L14,0,.L130-.L14
	.half	2
	.byte	138,0
	.word	.L130-.L14,.L62-.L14
	.half	3
	.byte	138,136,1
	.word	.L62-.L14,.L62-.L14
	.half	2
	.byte	138,0
	.word	0,0
.L81:
	.word	-1,.L14,.L138-.L14,.L139-.L14
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L91:
	.word	0,0
.L102:
	.word	0,0
.L89:
	.word	0,0
.L100:
	.word	0,0
.L82:
	.word	0,0
.L93:
	.word	0,0
.L104:
	.word	0,0
.L65:
	.word	-1,.L14,0,.L131-.L14
	.half	1
	.byte	85
	.word	.L141-.L14,.L142-.L14
	.half	1
	.byte	89
	.word	0,0
.L67:
	.word	-1,.L14,0,.L62-.L14
	.half	3
	.byte	145,248,126
	.word	0,0
.L87:
	.word	0,0
.L110:
	.word	0,0
.L98:
	.word	0,0
.L115:
	.word	0,0
.L63:
	.word	-1,.L14,0,.L131-.L14
	.half	1
	.byte	84
	.word	.L132-.L14,.L133-.L14
	.half	1
	.byte	88
	.word	.L134-.L14,.L135-.L14
	.half	1
	.byte	88
	.word	.L136-.L14,.L137-.L14
	.half	1
	.byte	88
	.word	.L140-.L14,.L141-.L14
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_loc'
.L11:
	.word	-1,.L12,0,.L56-.L12
	.half	2
	.byte	138,0
	.word	0,0
.L58:
	.word	-1,.L12,0,.L119-.L12
	.half	1
	.byte	85
	.word	.L122-.L12,.L56-.L12
	.half	1
	.byte	88
	.word	.L120-.L12,.L123-.L12
	.half	1
	.byte	84
	.word	0,0
.L59:
	.word	-1,.L12,.L125-.L12,.L126-.L12
	.half	1
	.byte	89
	.word	.L5-.L12,.L56-.L12
	.half	1
	.byte	89
	.word	0,0
.L61:
	.word	-1,.L12,.L124-.L12,.L56-.L12
	.half	1
	.byte	90
	.word	.L129-.L12,.L56-.L12
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L57:
	.word	-1,.L12,0,.L120-.L12
	.half	1
	.byte	84
	.word	.L121-.L12,.L56-.L12
	.half	1
	.byte	95
	.word	.L127-.L12,.L128-.L12
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L217:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('adc_convert')
	.sect	'.debug_frame'
	.word	24
	.word	.L217,.L2,.L37-.L2
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('adc_mean_filter_convert')
	.sect	'.debug_frame'
	.word	12
	.word	.L217,.L12,.L56-.L12
	.sdecl	'.debug_frame',debug,cluster('adc_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L217,.L14,.L62-.L14
	.byte	4
	.word	(.L130-.L14)/2
	.byte	19,136,1,22,26,4,19,138,136,1,4
	.word	(.L62-.L130)/2
	.byte	19,0,8,26
	; Module end
