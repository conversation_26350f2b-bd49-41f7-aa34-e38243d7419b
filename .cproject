<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.infineon.aurix.buildsystem.managed.configuration.binary.157769496">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.infineon.aurix.buildsystem.managed.configuration.binary.157769496" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="com.tasking.managedbuilder.TASKING_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.infineon.aurix.buildsystem.managed.TaskingErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="Mark_1" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" description="" id="com.infineon.aurix.buildsystem.managed.configuration.binary.157769496" name="Debug" parent="com.infineon.aurix.buildsystem.managed.configuration.binary">
					<folderInfo id="com.infineon.aurix.buildsystem.managed.configuration.binary.157769496." name="/" resourcePath="">
						<toolChain id="com.infineon.aurix.buildsystem.managed.toolChain.tasking.exe.binary.1417966933" name="AURIX Toolchain" superClass="com.infineon.aurix.buildsystem.managed.toolChain.tasking.exe.binary">
							<option id="com.tasking.ctc.cpu.1440237608" name="Processor" superClass="com.tasking.ctc.cpu" value="tc26xb" valueType="string"/>
							<option id="com.tasking.ctc.package.1072088046" name="Device package" superClass="com.tasking.ctc.package" value="bga292" valueType="string"/>
							<targetPlatform archList="all" binaryParser="com.tasking.managedbuilder.TASKING_ELF" id="com.infineon.aurix.buildsystem.managed.tasking.targetPlatform.983834169" isAbstract="false" osList="all" superClass="com.infineon.aurix.buildsystem.managed.tasking.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Mark_1}/Debug" id="com.infineon.aurix.buildsystem.managed.tasking.builder.2145844482" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.infineon.aurix.buildsystem.managed.tasking.builder"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.debug.705721940" name="TASKING C/C++ Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.debug">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.include.1255529901" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.include" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/code}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/code/user1}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/code/user2}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/code/user2/xf_asr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/zf_common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/zf_components}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/zf_device}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/zf_driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/user}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.preprocessor.definedSymbols.968491594" name="Defined symbols (-D)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.preprocessor.definedSymbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__CPU__=tc26xb"/>
								</option>
								<option id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.tradeoff.1099986582" name="Trade-off between speed and size: (--tradeoff=)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.tradeoff" useByScannerDiscovery="false" value="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.tradeoff.4" valueType="enumerated"/>
								<option id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.level.debug.925611446" name="Optimization level: (-Ox)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.level.debug" useByScannerDiscovery="false" value="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.optimization.level.0" valueType="enumerated"/>
								<option id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.fpmodel.316131961" name="Floating-point model: (--fp-model=)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.fpmodel" useByScannerDiscovery="false" value="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.fpmodel.fastDouble" valueType="enumerated"/>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.cpp.1003247724" name="C++" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.cpp"/>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.90086057" name="C" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.665570010" name="TASKING Assembler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking">
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.input.1059808323" superClass="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.input"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.1265474075" name="TASKING Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.libraries.libraries.1395469003" name="Libraries (${value})" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.libraries.libraries" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/libraries/zf_device/zf_device_config.a}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.otherOptions.1137678308" name="Other options" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.otherOptions" useByScannerDiscovery="false" valueType="stringList">
									<listOptionValue builtIn="false" value="-lc_fpu"/>
									<listOptionValue builtIn="false" value="-lcs_fpu"/>
									<listOptionValue builtIn="false" value="-lfp_fpu"/>
									<listOptionValue builtIn="false" value="-lrt"/>
									<listOptionValue builtIn="false" value="--lsl-core=vtc"/>
								</option>
								<option id="com.infineon.aurix.buildsystem.managed.c.linker.tasking.fpmodel.1863246561" name="Floating-point model: (--fp-model=)" superClass="com.infineon.aurix.buildsystem.managed.c.linker.tasking.fpmodel" useByScannerDiscovery="false" value="com.infineon.aurix.buildsystem.managed.c.linker.tasking.fpmodel.fastDouble" valueType="enumerated"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.printsize.tasking.518174043" name="Print Size" superClass="com.infineon.aurix.buildsystem.managed.tool.printsize.tasking"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="libraries/infineon_libraries/iLLD/TC26B/Tricore/Msc/Msc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Lin|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fce/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Cif/Cam|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dts|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dsadc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Sent|libraries/infineon_libraries/Service/CpuGeneric/SysSe/Time|libraries/infineon_libraries/iLLD/TC26B/Tricore/I2c/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eray|libraries/infineon_libraries/iLLD/TC26B/Tricore/Hssl|libraries/infineon_libraries/iLLD/TC26B/Tricore/Iom|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dsadc/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/Trap|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tim/In|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eray/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5s/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Smu/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/Icu|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eth|libraries/infineon_libraries/iLLD/TC26B/Tricore/_Lib/InternalMux|libraries/infineon_libraries/iLLD/TC26B/Tricore/Hssl/Std|libraries/doc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tim|libraries/infineon_libraries/iLLD/TC26B/Tricore/I2c|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eth/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Msc/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fce/Crc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Asclin/Spi|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fft/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fft|libraries/infineon_libraries/iLLD/TC26B/Tricore/Iom/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dsadc/Rdc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Emem|libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl|libraries/infineon_libraries/iLLD/TC26B/Tricore/Multican/Can|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Trig|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm|libraries/infineon_libraries/iLLD/TC26B/Tricore/Cif|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5s|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer|libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dts/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/TPwm|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tom|libraries/infineon_libraries/iLLD/TC26B/Tricore/Smu|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5/Psi5|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071|libraries/infineon_libraries/iLLD/TC26B/Tricore/Cif/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl|libraries/infineon_libraries/iLLD/TC26B/Tricore/Hssl/Hssl|libraries/infineon_libraries/iLLD/TC26B/Tricore/Iom/Driver|libraries/infineon_libraries/iLLD/TC26B/Tricore/Sent/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl|libraries/infineon_libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc|libraries/infineon_libraries/iLLD/TC26B/Tricore/Multican|libraries/infineon_libraries/iLLD/TC26B/Tricore/Msc|libraries/infineon_libraries/iLLD/TC26B/Tricore/I2c/I2c|libraries/infineon_libraries/iLLD/TC26B/Tricore/Multican/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Stm/Timer|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fft/Fft|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Dts/Dts|libraries/infineon_libraries/iLLD/TC26B/Tricore/Eray/Eray|libraries/infineon_libraries/iLLD/TC26B/Tricore/_Build|libraries/infineon_libraries/iLLD/TC26B/Tricore/Sent/Sent|libraries/infineon_libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s|libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave|libraries/infineon_libraries/Service/CpuGeneric/SysSe/Comm|libraries/infineon_libraries/iLLD/TC26B/Tricore/Port/Io|libraries/infineon_libraries/iLLD/TC26B/Tricore/Emem/Std|libraries/infineon_libraries/iLLD/TC26B/Tricore/Fce|libraries/infineon_libraries/Service/CpuGeneric/SysSe/General" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.tasking.flash.settings"/>
			<storageModule moduleId="com.infineon.aurix.buildsystem.build.booster.settings">
				<never-exclude-from-build>
					<path>libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart</path>
				</never-exclude-from-build>
				<libraries-roots>
					<path>libraries</path>
				</libraries-roots>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="com.infineon.aurix.buildsystem.managed.configuration.binary.release.303972758">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.infineon.aurix.buildsystem.managed.configuration.binary.release.303972758" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="com.tasking.managedbuilder.TASKING_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.infineon.aurix.buildsystem.managed.TaskingErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="Mark_1" buildProperties="org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" description="" id="com.infineon.aurix.buildsystem.managed.configuration.binary.release.303972758" name="Release" parent="com.infineon.aurix.buildsystem.managed.configuration.binary.release">
					<folderInfo id="com.infineon.aurix.buildsystem.managed.configuration.binary.release.303972758." name="/" resourcePath="">
						<toolChain id="com.infineon.aurix.buildsystem.managed.toolChain.tasking.exe.binary.release.1981339473" name="AURIX Toolchain" superClass="com.infineon.aurix.buildsystem.managed.toolChain.tasking.exe.binary.release">
							<option id="com.tasking.ctc.cpu.390821544" name="Processor" superClass="com.tasking.ctc.cpu" value="tc26xb" valueType="string"/>
							<option id="com.tasking.ctc.package.677607717" name="Device package" superClass="com.tasking.ctc.package" value="bga292" valueType="string"/>
							<targetPlatform archList="all" binaryParser="com.tasking.managedbuilder.TASKING_ELF" id="com.infineon.aurix.buildsystem.managed.tasking.targetPlatform.610227280" isAbstract="false" osList="all" superClass="com.infineon.aurix.buildsystem.managed.tasking.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Mark_1}/Release" id="com.infineon.aurix.buildsystem.managed.tasking.builder.1083539901" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" superClass="com.infineon.aurix.buildsystem.managed.tasking.builder"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.release.821162238" name="TASKING C/C++ Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.release">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.include.2131821072" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.include" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.preprocessor.definedSymbols.2097735517" name="Defined symbols (-D)" superClass="com.infineon.aurix.buildsystem.managed.c.compiler.tasking.preprocessor.definedSymbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="__CPU__=tc26xb"/>
								</option>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.cpp.1247658865" name="C++" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.cpp"/>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input.2064550736" name="C" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.tasking.input"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.428800927" name="TASKING Assembler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking">
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.input.315437909" superClass="com.infineon.aurix.buildsystem.managed.tool.c.assembler.tasking.input"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking.1202360284" name="TASKING Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker.tasking"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.printsize.tasking.1738906373" name="Print Size" superClass="com.infineon.aurix.buildsystem.managed.tool.printsize.tasking"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.infineon.aurix.buildsystem.build.booster.settings">
				<never-exclude-from-build>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/Trap</path>
					<path>/Configurations/Debug</path>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/CStart</path>
					<path>/Configurations</path>
				</never-exclude-from-build>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320" moduleId="org.eclipse.cdt.core.settings" name="External GCC - Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="Mark_1" buildArtefactType="com.infineon.aurix.buildsystem.managed.buildArtefactType.elf" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.infineon.aurix.buildsystem.managed.buildArtefactType.elf,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" description="" id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320" name="External GCC - Debug" parent="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug">
					<folderInfo id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320." name="/" resourcePath="">
						<toolChain id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.807132113" name="AURIX External GCC Toolchain" superClass="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.infineon.aurix.buildsystem.managed.gcc.targetPlatform.2052408218" isAbstract="false" osList="all" superClass="com.infineon.aurix.buildsystem.managed.gcc.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Mark_1}/External GCC - Debug" id="com.infineon.aurix.buildsystem.managed.gcc.builder.2147081726" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" superClass="com.infineon.aurix.buildsystem.managed.gcc.builder"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.94379325" name="AURIX GCC Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler">
								<option defaultValue="gnu.c.optimization.level.none" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.optimization.level.1797235229" name="Optimization Level" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.optimization.level" valueType="enumerated"/>
								<option defaultValue="gnu.c.debugging.level.max" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.debugging.level.1516389868" name="Debug Level" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.debugging.level" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.include.paths.698942616" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
								</option>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType.900208350" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.928902527" name="AURIX G++ Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler">
								<option id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.optimization.level.534388573" name="Optimization Level" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.none" valueType="enumerated"/>
								<option defaultValue="gnu.cpp.compiler.debugging.level.max" id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.debugging.level.1951346791" name="Debug Level" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.debugging.level" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.include.paths.1623645803" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
								</option>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType.526877604" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.assembler.1641836066" name="AURIX GCC Assembler" superClass="com.infineon.aurix.buildsystem.managed.tool.assembler">
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.assembler.inputType.911375432" name="Assembler Input" superClass="com.infineon.aurix.buildsystem.managed.tool.assembler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.linker.2123635868" name="AURIX GCC Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.cpp.linker.1365090837" name="AURIX G++ Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.linker">
								<inputType id="com.infineon.aurix.buildsystem.managed.cpp.linker.inputType.1476181866" superClass="com.infineon.aurix.buildsystem.managed.cpp.linker.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.objcopy.894944927" name="AURIX Object Copy" superClass="com.infineon.aurix.buildsystem.managed.tool.c.objcopy"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.objdump.1254246831" name="AURIX Create Listing" superClass="com.infineon.aurix.buildsystem.managed.tool.objdump"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.printsize.414841921" name="AURIX Print Size" superClass="com.infineon.aurix.buildsystem.managed.tool.printsize"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.infineon.aurix.buildsystem.build.booster.settings">
				<never-exclude-from-build>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/Trap</path>
					<path>/Configurations/Debug</path>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/CStart</path>
					<path>/Configurations</path>
				</never-exclude-from-build>
			</storageModule>
		</cconfiguration>
		<cconfiguration id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753" moduleId="org.eclipse.cdt.core.settings" name="External GCC - Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="Mark_1" buildArtefactType="com.infineon.aurix.buildsystem.managed.buildArtefactType.elf" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=com.infineon.aurix.buildsystem.managed.buildArtefactType.elf,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" description="" id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753" name="External GCC - Release" parent="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release">
					<folderInfo id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753." name="/" resourcePath="">
						<toolChain id="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.115949784" name="AURIX External GCC Toolchain" superClass="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release">
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.infineon.aurix.buildsystem.managed.gcc.targetPlatform.1905588857" isAbstract="false" osList="all" superClass="com.infineon.aurix.buildsystem.managed.gcc.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Mark_1}/External GCC - Release" id="com.infineon.aurix.buildsystem.managed.gcc.builder.1766460356" keepEnvironmentInBuildfile="false" name="Gnu Make Builder" superClass="com.infineon.aurix.buildsystem.managed.gcc.builder"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.**********" name="AURIX GCC Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler">
								<option defaultValue="gnu.c.optimization.level.most" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.optimization.level.1531097760" name="Optimization Level" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.optimization.level" valueType="enumerated"/>
								<option defaultValue="gnu.c.debugging.level.none" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.debugging.level.1315712702" name="Debug Level" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.debugging.level" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.include.paths.62889514" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
								</option>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType.**********" superClass="com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.1628724899" name="AURIX G++ Compiler" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler">
								<option id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.optimization.level.211313135" name="Optimization Level" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="gnu.cpp.compiler.optimization.level.most" valueType="enumerated"/>
								<option defaultValue="gnu.cpp.compiler.debugging.level.none" id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.debugging.level.823569879" name="Debug Level" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.debugging.level" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.include.paths.610250600" name="Include paths (-I)" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Configurations}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Platform/Tricore/Compilers}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Infra/Sfr/TC26B/_Reg}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/If/Ccu6If}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/StdIf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Comm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/General}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Math}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/SysSe/Time}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/Service/CpuGeneric/_Utilities}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Asc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Lin}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Spi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Asclin/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Icu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmBc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TPwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Ccu6/TimerWithTrigger}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Cam}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cif/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/CStart}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Irq}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Cpu/Trap}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Dma}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dma/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Dsadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Rdc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dsadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Dts}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Dts/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Emem/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Eray}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eray/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Phy_Pef7071}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Eth/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Crc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fce/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Fft}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Fft/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Flash/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/IncrEnc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gpt12/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Atom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tim/In}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Pwm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/PwmHl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Tom/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Gtm/Trig}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Hssl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Hssl/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/I2c}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/I2c/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Driver}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Iom/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Msc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Msc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Mtu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Can}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Multican/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Io}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Port/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Psi5}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Psi5s}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Psi5s/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiMaster}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/SpiSlave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Qspi/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Scu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Sent}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Sent/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Smu/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Src/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Stm/Timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Adc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/Vadc/Std}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Impl}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/DataHandling}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_Lib/InternalMux}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/Mark_1/Libraries/iLLD/TC26B/Tricore/_PinMap}&quot;"/>
								</option>
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType.330026677" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.assembler.1006947320" name="AURIX GCC Assembler" superClass="com.infineon.aurix.buildsystem.managed.tool.assembler">
								<inputType id="com.infineon.aurix.buildsystem.managed.tool.assembler.inputType.194137049" name="Assembler Input" superClass="com.infineon.aurix.buildsystem.managed.tool.assembler.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.linker.1447963722" name="AURIX GCC Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.c.linker"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.cpp.linker.2091646596" name="AURIX G++ Linker" superClass="com.infineon.aurix.buildsystem.managed.tool.cpp.linker">
								<inputType id="com.infineon.aurix.buildsystem.managed.cpp.linker.inputType.704069118" superClass="com.infineon.aurix.buildsystem.managed.cpp.linker.inputType"/>
							</tool>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.c.objcopy.1577362320" name="AURIX Object Copy" superClass="com.infineon.aurix.buildsystem.managed.tool.c.objcopy"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.objdump.182703204" name="AURIX Create Listing" superClass="com.infineon.aurix.buildsystem.managed.tool.objdump"/>
							<tool id="com.infineon.aurix.buildsystem.managed.tool.printsize.1544864303" name="AURIX Print Size" superClass="com.infineon.aurix.buildsystem.managed.tool.printsize"/>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.infineon.aurix.buildsystem.build.booster.settings">
				<never-exclude-from-build>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/Trap</path>
					<path>/Configurations/Debug</path>
					<path>/Libraries/iLLD/TC26B/Tricore/Cpu/CStart</path>
					<path>/Configurations</path>
				</never-exclude-from-build>
			</storageModule>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="Mark_1.com.infineon.aurix.buildsystem.managed.projectType.2002468362" name="AURIX Project" projectType="com.infineon.aurix.buildsystem.managed.projectType"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320;com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320.;com.infineon.aurix.buildsystem.managed.tool.c.compiler.94379325;com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType.900208350">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753;com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753.;com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.1628724899;com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType.330026677">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320;com.infineon.aurix.buildsystem.managed.external.gcc.configuration.debug.56824320.;com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.928902527;com.infineon.aurix.buildsystem.managed.tool.cpp.compiler.inputType.526877604">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753;com.infineon.aurix.buildsystem.managed.external.gcc.configuration.release.653421753.;com.infineon.aurix.buildsystem.managed.tool.c.compiler.**********;com.infineon.aurix.buildsystem.managed.tool.c.compiler.inputType.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.core.language.mapping">
		<project-mappings>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cHeader" language="com.tasking.dbg.ctc.clanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cSource" language="com.tasking.dbg.ctc.clanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxHeader" language="com.tasking.dbg.ctc.cpplanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxSource" language="com.tasking.dbg.ctc.cpplanguage"/>
		</project-mappings>
	</storageModule>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="External GCC - Debug">
			<resource resourceType="PROJECT" workspacePath="/Mark_1"/>
		</configuration>
		<configuration configurationName="External GCC - Release">
			<resource resourceType="PROJECT" workspacePath="/Mark_1"/>
		</configuration>
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/Mark_1"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/Mark_1"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
</cproject>