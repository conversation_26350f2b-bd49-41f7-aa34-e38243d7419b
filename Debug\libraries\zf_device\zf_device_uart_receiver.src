	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc1236a --dep-file=zf_device_uart_receiver.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_uart_receiver.src ../libraries/zf_device/zf_device_uart_receiver.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_uart_receiver.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_uart_receiver.uart_receiver_interval_time',code,cluster('uart_receiver_interval_time')
	.sect	'.text.zf_device_uart_receiver.uart_receiver_interval_time'
	.align	2
	
; Function uart_receiver_interval_time
.L16:
uart_receiver_interval_time:	.type	func
	mfcr	d15,#65052
.L89:
	and	d15,#7
.L90:
	j	.L2
.L2:
	extr	d4,d15,#0,#8
	call	IfxStm_getAddress
.L62:
	jz.a	a2,.L3
.L3:
	call	IfxScuCcu_getSourceFrequency
.L117:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	and	d15,#15
	itof	d15,d15
.L118:
	div.f	d15,d2,d15
.L119:
	j	.L4
.L4:
	j	.L5
.L5:
	ftouz	d8,d15
.L63:
	mfcr	d15,#65052
.L91:
	and	d15,#7
.L92:
	j	.L6
.L6:
	extr	d4,d15,#0,#8
	call	IfxStm_getAddress
.L74:
	ld.w	d15,[a2]16
.L120:
	j	.L7
.L7:
	movh.a	a15,#@his(_999001_time_last)
	lea	a15,[a15]@los(_999001_time_last)
	ld.w	d0,[a15]
.L121:
	sub	d4,d15,d0
.L93:
	movh.a	a15,#@his(_999001_time_last)
	lea	a15,[a15]@los(_999001_time_last)
.L122:
	st.w	[a15],d15
.L123:
	mov	d5,#0
.L124:
	mov.u	d6,#16960
	addih	d6,d6,#15
	mov	d7,#0
.L125:
	call	__ll_mul64
.L94:
	mov	e4,d3,d2
.L126:
	mov	d7,#0
	mov	d6,d8
.L95:
	call	__ll_udiv64
.L96:
	j	.L8
.L8:
	ret
.L53:
	
__uart_receiver_interval_time_function_end:
	.size	uart_receiver_interval_time,__uart_receiver_interval_time_function_end-uart_receiver_interval_time
.L36:
	; End of function
	
	.sdecl	'.text.zf_device_uart_receiver.uart_receiver_analysis',code,cluster('uart_receiver_analysis')
	.sect	'.text.zf_device_uart_receiver.uart_receiver_analysis'
	.align	2
	
; Function uart_receiver_analysis
.L18:
uart_receiver_analysis:	.type	func
	mov	d0,#0
.L97:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L131:
	ld.bu	d1,[a5]1
.L132:
	ld.bu	d15,[a5]2
.L133:
	sha	d15,d15,#8
.L134:
	or	d1,d15
.L135:
	insert	d15,d1,#0,#11,#21
.L136:
	st.h	[a15],d15
.L137:
	add	d0,#1
.L138:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L139:
	ld.bu	d15,[a5]2
.L140:
	sha	d1,d15,#-3
.L141:
	ld.bu	d15,[a5]3
.L142:
	sha	d15,#5
.L143:
	or	d1,d15
.L144:
	insert	d15,d1,#0,#11,#21
.L145:
	st.h	[a15],d15
.L146:
	add	d0,#1
.L147:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L148:
	ld.bu	d15,[a5]3
.L149:
	sha	d1,d15,#-6
.L150:
	ld.bu	d15,[a5]4
.L151:
	sha	d15,#2
.L152:
	or	d1,d15
.L153:
	ld.bu	d15,[a5]5
.L154:
	sha	d15,d15,#10
.L155:
	or	d1,d15
.L156:
	insert	d15,d1,#0,#11,#21
.L157:
	st.h	[a15],d15
.L158:
	add	d0,#1
.L159:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L160:
	ld.bu	d15,[a5]5
.L161:
	sha	d1,d15,#-1
.L162:
	ld.bu	d15,[a5]6
.L163:
	sha	d15,#7
.L164:
	or	d1,d15
.L165:
	insert	d15,d1,#0,#11,#21
.L166:
	st.h	[a15],d15
.L167:
	add	d0,#1
.L168:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L169:
	ld.bu	d15,[a5]6
.L170:
	sha	d1,d15,#-4
.L171:
	ld.bu	d15,[a5]7
.L172:
	sha	d15,#4
.L173:
	or	d1,d15
.L174:
	insert	d15,d1,#0,#11,#21
.L175:
	st.h	[a15],d15
.L176:
	add	d0,#1
.L177:
	mul	d15,d0,#2
	addsc.a	a15,a4,d15,#0
.L178:
	ld.bu	d15,[a5]7
.L179:
	sha	d0,d15,#-7
.L98:
	ld.bu	d15,[a5]8
.L180:
	sha	d15,#1
.L181:
	or	d0,d15
.L182:
	ld.bu	d15,[a5]9
.L183:
	sha	d15,d15,#9
.L184:
	or	d0,d15
.L185:
	insert	d15,d0,#0,#11,#21
.L186:
	st.h	[a15],d15
.L187:
	ld.bu	d15,[a5]23
.L188:
	jne	d15,#3,.L9
.L189:
	mov	d15,#1
.L190:
	j	.L10
.L9:
	mov	d15,#0
.L10:
	st.b	[a4]12,d15
.L191:
	movh.a	a15,#@his(uart_receiver)
	lea	a15,[a15]@los(uart_receiver)
.L192:
	mov	d15,#1
.L193:
	st.b	[a15]13,d15
.L194:
	ret
.L78:
	
__uart_receiver_analysis_function_end:
	.size	uart_receiver_analysis,__uart_receiver_analysis_function_end-uart_receiver_analysis
.L41:
	; End of function
	
	.sdecl	'.text.zf_device_uart_receiver.uart_receiver_callback',code,cluster('uart_receiver_callback')
	.sect	'.text.zf_device_uart_receiver.uart_receiver_callback'
	.align	2
	
	.global	uart_receiver_callback
; Function uart_receiver_callback
.L20:
uart_receiver_callback:	.type	func
	call	uart_receiver_interval_time
.L199:
	mov	d15,#3000
.L200:
	jge.u	d15,d2,.L11
.L201:
	movh.a	a15,#@his(_999002_length)
	lea	a15,[a15]@los(_999002_length)
.L202:
	mov	d15,#0
.L203:
	st.b	[a15],d15
.L11:
	movh.a	a15,#@his(_999002_length)
	lea	a15,[a15]@los(_999002_length)
	ld.bu	d15,[a15]
.L204:
	movh.a	a15,#@his(uart_receiver_data)
	lea	a15,[a15]@los(uart_receiver_data)
.L205:
	addsc.a	a15,a15,d15,#0
.L206:
	mov	d4,#2
	call	uart_read_byte
.L207:
	st.b	[a15],d2
.L208:
	movh.a	a15,#@his(_999002_length)
	lea	a15,[a15]@los(_999002_length)
	movh.a	a2,#@his(_999002_length)
	lea	a2,[a2]@los(_999002_length)
	ld.bu	d15,[a2]
.L209:
	add	d15,#1
	st.b	[a15],d15
.L210:
	movh.a	a15,#@his(_999002_length)
	lea	a15,[a15]@los(_999002_length)
	ld.bu	d0,[a15]
.L211:
	mov	d15,#25
.L212:
	jne	d15,d0,.L12
.L213:
	movh.a	a15,#@his(uart_receiver_data)
	lea	a15,[a15]@los(uart_receiver_data)
.L214:
	ld.bu	d0,[a15]
.L215:
	mov	d15,#15
.L216:
	jne	d15,d0,.L13
.L217:
	movh.a	a15,#@his(uart_receiver_data)
	lea	a15,[a15]@los(uart_receiver_data)
.L218:
	ld.bu	d15,[a15]24
.L219:
	jne	d15,#0,.L14
.L220:
	movh.a	a4,#@his(uart_receiver)
	lea	a4,[a4]@los(uart_receiver)
.L221:
	movh.a	a5,#@his(uart_receiver_data)
	lea	a5,[a5]@los(uart_receiver_data)
	call	uart_receiver_analysis
.L14:
.L13:
.L12:
	ret
.L85:
	
__uart_receiver_callback_function_end:
	.size	uart_receiver_callback,__uart_receiver_callback_function_end-uart_receiver_callback
.L46:
	; End of function
	
	.sdecl	'.text.zf_device_uart_receiver.uart_receiver_init',code,cluster('uart_receiver_init')
	.sect	'.text.zf_device_uart_receiver.uart_receiver_init'
	.align	2
	
	.global	uart_receiver_init
; Function uart_receiver_init
.L22:
uart_receiver_init:	.type	func
	mov	d4,#2
.L107:
	mov	d5,#3125
	sh	d5,#5
.L108:
	mov	d6,#14
.L109:
	mov	d7,#10
	call	uart_sbus_init
.L110:
	mov	d4,#5
.L111:
	movh.a	a4,#@his(uart_receiver_callback)
	lea	a4,[a4]@los(uart_receiver_callback)
	call	set_wireless_type
.L112:
	ret
.L51:
	
__uart_receiver_init_function_end:
	.size	uart_receiver_init,__uart_receiver_init_function_end-uart_receiver_init
.L31:
	; End of function
	
	.sdecl	'.bss.zf_device_uart_receiver.uart_receiver',data,cluster('uart_receiver')
	.sect	'.bss.zf_device_uart_receiver.uart_receiver'
	.global	uart_receiver
	.align	4
uart_receiver:	.type	object
	.size	uart_receiver,16
	.space	16
	.sdecl	'.data.zf_device_uart_receiver.uart_receiver_data',data,cluster('uart_receiver_data')
	.sect	'.data.zf_device_uart_receiver.uart_receiver_data'
	.global	uart_receiver_data
uart_receiver_data:	.type	object
	.size	uart_receiver_data,25
	.space	25
	.sdecl	'.data.zf_device_uart_receiver._999001_time_last',data,cluster('_999001_time_last')
	.sect	'.data.zf_device_uart_receiver._999001_time_last'
	.align	2
_999001_time_last:	.type	object
	.size	_999001_time_last,4
	.space	4
	.sdecl	'.data.zf_device_uart_receiver._999002_length',data,cluster('_999002_length')
	.sect	'.data.zf_device_uart_receiver._999002_length'
_999002_length:	.type	object
	.size	_999002_length,1
	.space	1
	.calls	'uart_receiver_interval_time','__ll_mul64'
	.calls	'uart_receiver_interval_time','__ll_udiv64'
	.calls	'__INDIRECT__','uart_receiver_callback'
	.calls	'uart_receiver_interval_time','IfxStm_getAddress'
	.calls	'uart_receiver_interval_time','IfxScuCcu_getSourceFrequency'
	.calls	'uart_receiver_callback','uart_receiver_interval_time'
	.calls	'uart_receiver_callback','uart_read_byte'
	.calls	'uart_receiver_callback','uart_receiver_analysis'
	.calls	'uart_receiver_init','uart_sbus_init'
	.calls	'uart_receiver_init','set_wireless_type'
	.calls	'uart_receiver_interval_time','',0
	.calls	'uart_receiver_analysis','',0
	.calls	'uart_receiver_callback','',0
	.extern	set_wireless_type
	.extern	IfxScuCcu_getSourceFrequency
	.extern	IfxStm_getAddress
	.extern	uart_read_byte
	.extern	uart_sbus_init
	.extern	__ll_mul64
	.extern	__ll_udiv64
	.extern	__INDIRECT__
	.calls	'uart_receiver_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L24:
	.word	101433
	.half	3
	.word	.L25
	.byte	4
.L23:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L26
	.byte	2,1,1,3
	.word	211
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	214
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L68:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	259
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	271
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	383
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	357
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	389
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	389
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	357
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	498
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	498
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	514
	.byte	4,2,35,0,0
.L83:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	689
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	933
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	610
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	893
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1124
	.byte	4,2,35,8,0,14
	.word	1164
	.byte	3
	.word	1227
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1232
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	667
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1232
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	667
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	667
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1232
	.byte	6,0,15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0
.L57:
	.byte	8
	.byte	'IfxCpu_getCoreId',0,3,5,133,6,22
	.word	1462
	.byte	1,1
.L58:
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1544
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	650
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	650
	.byte	1,1,17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1,5
	.byte	'enabled',0,5,168,7,50
	.word	650
	.byte	6,0
.L52:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1866
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	667
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	650
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	667
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1866
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1866
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2097
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2984
	.byte	4,2,35,0,0,18,4
	.word	650
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3112
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3327
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3542
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	650
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3759
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3979
	.byte	4,2,35,0,0,18,24
	.word	650
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4302
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	650
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4606
	.byte	4,2,35,0,0,18,8
	.word	650
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4931
	.byte	4,2,35,0,0,18,12
	.word	650
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5271
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	475
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5637
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5923
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6070
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	475
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6411
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	667
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6760
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6934
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7266
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7599
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7947
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8071
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8155
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8335
	.byte	4,2,35,0,0,18,76
	.word	650
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8588
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8675
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2373
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2944
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3063
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3103
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3287
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3502
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3719
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3939
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3103
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4253
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4293
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4566
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4882
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4922
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5222
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5262
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5597
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5883
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4922
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6030
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6199
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6371
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6546
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6720
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6894
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7070
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7226
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7559
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7907
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4922
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8031
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8280
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8539
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8579
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8635
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9202
	.byte	4,3,35,252,1,0,14
	.word	9242
	.byte	3
	.word	9845
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9850
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	650
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9855
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9850
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	650
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	10060
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	10130
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9850
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	650
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10443
	.byte	6,0
.L70:
	.byte	8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	271
	.byte	1,1
.L71:
	.byte	6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10664
	.byte	4,2,35,0,0,14
	.word	10954
	.byte	3
	.word	10993
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10998
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11046
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	667
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11205
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	667
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11625
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	667
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11850
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	650
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12091
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	667
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	650
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12577
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	667
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12774
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12931
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13085
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13285
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13399
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13245
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13359
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13474
	.byte	4,2,35,8,0,14
	.word	13514
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13587
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14073
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14586
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15101
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15566
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15653
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	475
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15740
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15863
	.byte	4,2,35,0,0,18,148,1
	.word	650
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15962
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16125
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16234
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16341
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16467
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16559
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11165
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11460
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11585
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11810
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	12051
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12272
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12537
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12734
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12891
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	13045
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13582
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	14033
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14546
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	15061
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15526
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15613
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15700
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15823
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15911
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15951
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	16085
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16194
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16301
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16427
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16519
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	17091
	.byte	4,3,35,252,1,0,14
	.word	17131
	.byte	3
	.word	17573
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17578
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	650
	.byte	6,0,15,12,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17578
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17645
	.byte	6,0,15,12,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17578
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17829
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18121
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18134
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18134
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18121
	.byte	2,2,35,10,0,14
	.word	650
	.byte	14
	.word	650
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	389
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18146
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18121
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18121
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18121
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18121
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18227
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18232
	.byte	1,2,35,25,0,3
	.word	18237
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18121
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18396
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18448
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18604
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18726
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18811
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18896
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19067
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19153
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19239
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19325
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19412
	.byte	4,2,35,0,0,18,8
	.word	19454
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	650
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19503
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	475
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19734
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19951
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20115
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20202
	.byte	4,2,35,0,0,18,144,1
	.word	650
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20302
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20568
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20795
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20884
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18564
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3103
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18686
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3103
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18771
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18856
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18941
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	19027
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19113
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19199
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19285
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19372
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19494
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19694
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19911
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	20075
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5262
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20162
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20251
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20291
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20422
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20528
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20632
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20755
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20844
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21413
	.byte	4,3,35,252,1,0,14
	.word	21453
	.byte	3
	.word	21873
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	357
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21878
	.byte	6,0
.L61:
	.byte	8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	271
	.byte	1,1
.L64:
	.byte	5
	.byte	'stm',0,15,179,4,49
	.word	21878
.L66:
	.byte	17
.L67:
	.byte	6,6,0,0
.L73:
	.byte	8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	1866
	.byte	1,1
.L75:
	.byte	5
	.byte	'stm',0,15,190,4,44
	.word	21878
.L77:
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	650
	.byte	1,1,17,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	650
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	22094
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	22094
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	650
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	22094
	.byte	17,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	22094
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	22094
	.byte	1,1,17,6,0,0,14
	.word	491
	.byte	21
	.byte	'__mfcr',0
	.word	22265
	.byte	1,1,1,1,22
	.word	491
	.byte	0,15,18,53,9,1,16
	.byte	'NO_WIRELESS',0,0,16
	.byte	'WIRELESS_UART',0,1,16
	.byte	'BLE6A20',0,2,16
	.byte	'BLUETOOTH_CH9141',0,3,16
	.byte	'WIFI_UART',0,4,16
	.byte	'RECEIVER_UART',0,5,0,23
	.byte	'callback_function',0,18,73,16
	.word	214
	.byte	24
	.byte	'set_wireless_type',0,18,91,8,1,1,1,1,5
	.byte	'type_set',0,18,91,53
	.word	22292
	.byte	5
	.byte	'wireless_callback',0,18,91,81
	.word	22385
	.byte	0,3
	.word	211
	.byte	25
	.word	219
	.byte	26
	.word	245
	.byte	6,0,25
	.word	280
	.byte	26
	.word	312
	.byte	6,0,25
	.word	325
	.byte	6,0,25
	.word	394
	.byte	26
	.word	413
	.byte	6,0,25
	.word	429
	.byte	26
	.word	444
	.byte	26
	.word	458
	.byte	6,0,25
	.word	1237
	.byte	26
	.word	1277
	.byte	26
	.word	1295
	.byte	6,0,25
	.word	1315
	.byte	26
	.word	1353
	.byte	26
	.word	1371
	.byte	6,0,25
	.word	1391
	.byte	26
	.word	1442
	.byte	6,0,25
	.word	1513
	.byte	6,0,25
	.word	1623
	.byte	6,0,25
	.word	1657
	.byte	6,0,25
	.word	1699
	.byte	17,27
	.word	1657
	.byte	28
	.word	1697
	.byte	0,6,0,0,25
	.word	1740
	.byte	6,0,25
	.word	1774
	.byte	6,0,25
	.word	1814
	.byte	26
	.word	1847
	.byte	6,0,25
	.word	1887
	.byte	26
	.word	1928
	.byte	6,0,25
	.word	1947
	.byte	26
	.word	2002
	.byte	6,0,25
	.word	2021
	.byte	26
	.word	2061
	.byte	26
	.word	2078
	.byte	17,6,0,0,25
	.word	9980
	.byte	26
	.word	10012
	.byte	26
	.word	10026
	.byte	26
	.word	10044
	.byte	6,0,25
	.word	10347
	.byte	26
	.word	10380
	.byte	26
	.word	10394
	.byte	26
	.word	10412
	.byte	26
	.word	10426
	.byte	6,0,25
	.word	10546
	.byte	26
	.word	10574
	.byte	26
	.word	10588
	.byte	26
	.word	10606
	.byte	6,0,25
	.word	10624
	.byte	6,0,29
	.byte	'IfxScuCcu_getSourceFrequency',0,9,173,7,20
	.word	271
	.byte	1,1,1,1,25
	.word	11003
	.byte	26
	.word	11031
	.byte	6,0,25
	.word	17583
	.byte	26
	.word	17611
	.byte	26
	.word	17627
	.byte	6,0,25
	.word	17767
	.byte	26
	.word	17797
	.byte	26
	.word	17813
	.byte	6,0,25
	.word	18060
	.byte	26
	.word	18089
	.byte	26
	.word	18105
	.byte	6,0,25
	.word	18401
	.byte	26
	.word	18432
	.byte	6,0,25
	.word	21883
	.byte	26
	.word	21906
	.byte	6,0,25
	.word	21921
	.byte	26
	.word	21953
	.byte	17,17,27
	.word	10624
	.byte	28
	.word	10662
	.byte	0,0,6,0,0,15,19,87,9,1,16
	.byte	'IfxStm_Index_none',0,127,16
	.byte	'IfxStm_Index_0',0,0,16
	.byte	'IfxStm_Index_1',0,1,0,30
	.byte	'IfxStm_getAddress',0,15,209,2,21
	.word	21878
	.byte	1,1,1,1,5
	.byte	'stm',0,15,209,2,52
	.word	22936
	.byte	0,25
	.word	21971
	.byte	26
	.word	21999
	.byte	6,0,25
	.word	22014
	.byte	17,27
	.word	1699
	.byte	31
	.word	1736
	.byte	27
	.word	1657
	.byte	28
	.word	1697
	.byte	0,28
	.word	1737
	.byte	0,0,6,0,0,25
	.word	22047
	.byte	26
	.word	22073
	.byte	17,27
	.word	1814
	.byte	26
	.word	1847
	.byte	28
	.word	1864
	.byte	0,6,0,0,25
	.word	22111
	.byte	26
	.word	22135
	.byte	17,27
	.word	22201
	.byte	31
	.word	22217
	.byte	27
	.word	22014
	.byte	31
	.word	22043
	.byte	27
	.word	1699
	.byte	31
	.word	1736
	.byte	27
	.word	1657
	.byte	28
	.word	1697
	.byte	0,28
	.word	1737
	.byte	0,0,28
	.word	22044
	.byte	0,0,28
	.word	22218
	.byte	27
	.word	22047
	.byte	26
	.word	22073
	.byte	31
	.word	22090
	.byte	27
	.word	1814
	.byte	26
	.word	1847
	.byte	28
	.word	1864
	.byte	0,28
	.word	22091
	.byte	0,0,28
	.word	22219
	.byte	27
	.word	21883
	.byte	26
	.word	21906
	.byte	28
	.word	21919
	.byte	0,28
	.word	22220
	.byte	0,0,6,0,0,25
	.word	22156
	.byte	26
	.word	22179
	.byte	17,27
	.word	22201
	.byte	31
	.word	22217
	.byte	27
	.word	22014
	.byte	31
	.word	22043
	.byte	27
	.word	1699
	.byte	31
	.word	1736
	.byte	27
	.word	1657
	.byte	28
	.word	1697
	.byte	0,28
	.word	1737
	.byte	0,0,28
	.word	22044
	.byte	0,0,28
	.word	22218
	.byte	27
	.word	22047
	.byte	26
	.word	22073
	.byte	31
	.word	22090
	.byte	27
	.word	1814
	.byte	26
	.word	1847
	.byte	28
	.word	1864
	.byte	0,28
	.word	22091
	.byte	0,0,28
	.word	22219
	.byte	27
	.word	21883
	.byte	26
	.word	21906
	.byte	28
	.word	21919
	.byte	0,28
	.word	22220
	.byte	0,0,6,0,0,25
	.word	22201
	.byte	17,27
	.word	22014
	.byte	31
	.word	22043
	.byte	27
	.word	1699
	.byte	31
	.word	1736
	.byte	27
	.word	1657
	.byte	28
	.word	1697
	.byte	0,28
	.word	1737
	.byte	0,0,28
	.word	22044
	.byte	0,0,6,27
	.word	22047
	.byte	26
	.word	22073
	.byte	31
	.word	22090
	.byte	27
	.word	1814
	.byte	26
	.word	1847
	.byte	28
	.word	1864
	.byte	0,28
	.word	22091
	.byte	0,0,6,27
	.word	21883
	.byte	26
	.word	21906
	.byte	28
	.word	21919
	.byte	0,6,0,0,25
	.word	22223
	.byte	17,27
	.word	21883
	.byte	26
	.word	21906
	.byte	28
	.word	21919
	.byte	0,6,0,0,15,20,103,9,1,16
	.byte	'UART_0',0,0,16
	.byte	'UART_1',0,1,16
	.byte	'UART_2',0,2,16
	.byte	'UART_3',0,3,0,30
	.byte	'uart_read_byte',0,20,122,9
	.word	650
	.byte	1,1,1,1,5
	.byte	'uartn',0,20,122,62
	.word	23535
	.byte	0,15,20,43,9,1,16
	.byte	'UART0_TX_P14_0',0,0,16
	.byte	'UART0_TX_P14_1',0,1,16
	.byte	'UART0_TX_P15_2',0,2,16
	.byte	'UART0_TX_P15_3',0,3,16
	.byte	'UART1_TX_P02_2',0,4,16
	.byte	'UART1_TX_P11_12',0,5,16
	.byte	'UART1_TX_P15_0',0,6,16
	.byte	'UART1_TX_P15_1',0,7,16
	.byte	'UART1_TX_P15_4',0,8,16
	.byte	'UART1_TX_P15_5',0,9,16
	.byte	'UART1_TX_P20_10',0,10,16
	.byte	'UART1_TX_P33_12',0,11,16
	.byte	'UART1_TX_P33_13',0,12,16
	.byte	'UART2_TX_P02_0',0,13,16
	.byte	'UART2_TX_P10_5',0,14,16
	.byte	'UART2_TX_P14_2',0,15,16
	.byte	'UART2_TX_P14_3',0,16,16
	.byte	'UART2_TX_P33_8',0,17,16
	.byte	'UART2_TX_P33_9',0,18,16
	.byte	'UART3_TX_P00_0',0,19,16
	.byte	'UART3_TX_P00_1',0,20,16
	.byte	'UART3_TX_P15_6',0,21,16
	.byte	'UART3_TX_P15_7',0,22,16
	.byte	'UART3_TX_P20_0',0,23,16
	.byte	'UART3_TX_P20_3',0,24,16
	.byte	'UART3_TX_P21_7',0,25,0,15,20,77,9,1,16
	.byte	'UART0_RX_P14_1',0,0,16
	.byte	'UART0_RX_P15_3',0,1,16
	.byte	'UART1_RX_P02_3',0,2,16
	.byte	'UART1_RX_P11_10',0,3,16
	.byte	'UART1_RX_P15_1',0,4,16
	.byte	'UART1_RX_P15_5',0,5,16
	.byte	'UART1_RX_P20_9',0,6,16
	.byte	'UART1_RX_P33_13',0,7,16
	.byte	'UART2_RX_P02_0',0,8,16
	.byte	'UART2_RX_P02_1',0,9,16
	.byte	'UART2_RX_P10_6',0,10,16
	.byte	'UART2_RX_P14_3',0,11,16
	.byte	'UART2_RX_P33_8',0,12,16
	.byte	'UART3_RX_P00_1',0,13,16
	.byte	'UART3_RX_P15_7',0,14,16
	.byte	'UART3_RX_P20_3',0,15,16
	.byte	'UART3_RX_P21_6',0,16,0,24
	.byte	'uart_sbus_init',0,20,128,1,9,1,1,1,1,5
	.byte	'uartn',0,20,128,1,62
	.word	23535
	.byte	5
	.byte	'baud',0,20,128,1,76
	.word	1866
	.byte	5
	.byte	'tx_pin',0,20,128,1,99
	.word	23619
	.byte	5
	.byte	'rx_pin',0,20,128,1,124
	.word	24071
	.byte	0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,21,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0
.L59:
	.byte	12,21,223,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24454
	.byte	4,2,35,0,0,18,12
	.word	667
	.byte	19,5,0
.L87:
	.byte	20,22,61,9,16,13
	.byte	'channel',0
	.word	24563
	.byte	12,2,35,0,13
	.byte	'state',0
	.word	650
	.byte	1,2,35,12,13
	.byte	'finsh_flag',0
	.word	650
	.byte	1,2,35,13,0
.L79:
	.byte	3
	.word	24572
.L81:
	.byte	3
	.word	650
.L86:
	.byte	14
	.word	650
	.byte	32
	.byte	'__INDIRECT__',0,23,1,1,1,1,1,23
	.byte	'__wchar_t',0,23,1,1
	.word	18121
	.byte	23
	.byte	'__size_t',0,23,1,1
	.word	475
	.byte	23
	.byte	'__ptrdiff_t',0,23,1,1
	.word	491
	.byte	33,1,3
	.word	24720
	.byte	23
	.byte	'__codeptr',0,23,1,1
	.word	24722
	.byte	23
	.byte	'__intptr_t',0,23,1,1
	.word	491
	.byte	23
	.byte	'__uintptr_t',0,23,1,1
	.word	475
	.byte	23
	.byte	'_iob_flag_t',0,24,82,25
	.word	667
	.byte	23
	.byte	'boolean',0,25,101,29
	.word	650
	.byte	23
	.byte	'uint8',0,25,105,29
	.word	650
	.byte	23
	.byte	'uint16',0,25,109,29
	.word	667
	.byte	23
	.byte	'uint32',0,25,113,29
	.word	1866
	.byte	23
	.byte	'uint64',0,25,118,29
	.word	357
	.byte	23
	.byte	'sint16',0,25,126,29
	.word	18121
	.byte	23
	.byte	'sint32',0,25,131,1,29
	.word	18134
	.byte	23
	.byte	'sint64',0,25,138,1,29
	.word	22094
	.byte	23
	.byte	'float32',0,25,167,1,29
	.word	271
	.byte	23
	.byte	'pvoid',0,26,57,28
	.word	389
	.byte	23
	.byte	'Ifx_TickTime',0,26,79,28
	.word	22094
	.byte	23
	.byte	'Ifx_SizeT',0,26,92,16
	.word	18121
	.byte	23
	.byte	'Ifx_Priority',0,26,103,16
	.word	667
	.byte	15,26,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,23
	.byte	'Ifx_RxSel',0,26,140,1,3
	.word	25017
	.byte	15,26,164,1,9,1,16
	.byte	'Ifx_DataBufferMode_normal',0,0,16
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,23
	.byte	'Ifx_DataBufferMode',0,26,169,1,2
	.word	25155
	.byte	7
	.byte	'char',0,1,6,23
	.byte	'int8',0,27,54,29
	.word	25255
	.byte	23
	.byte	'int16',0,27,55,29
	.word	18121
	.byte	23
	.byte	'int32',0,27,56,29
	.word	491
	.byte	23
	.byte	'int64',0,27,57,29
	.word	22094
	.byte	23
	.byte	'wireless_type_enum',0,18,61,2
	.word	22292
	.byte	23
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16559
	.byte	23
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16467
	.byte	23
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	12091
	.byte	23
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12931
	.byte	23
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12774
	.byte	23
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	11046
	.byte	23
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15740
	.byte	23
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12577
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13587
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14586
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	15101
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	14073
	.byte	23
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12312
	.byte	23
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11500
	.byte	23
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11205
	.byte	23
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16341
	.byte	23
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16234
	.byte	23
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16125
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13285
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	13085
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13399
	.byte	23
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15962
	.byte	23
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15653
	.byte	23
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15863
	.byte	23
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11850
	.byte	23
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15566
	.byte	23
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11625
	.byte	23
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	17091
	.byte	23
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16519
	.byte	23
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12272
	.byte	23
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	13045
	.byte	23
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12891
	.byte	23
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11165
	.byte	23
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15823
	.byte	23
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12734
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	14033
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	15061
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15526
	.byte	23
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14546
	.byte	23
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12537
	.byte	23
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11585
	.byte	23
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11460
	.byte	23
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16427
	.byte	23
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16301
	.byte	23
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16194
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13359
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13245
	.byte	23
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13474
	.byte	23
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	16085
	.byte	23
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15700
	.byte	23
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15911
	.byte	23
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	12051
	.byte	23
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15613
	.byte	23
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11810
	.byte	14
	.word	13514
	.byte	23
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	26944
	.byte	14
	.word	17131
	.byte	23
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	26973
	.byte	15,28,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,23
	.byte	'IfxScu_CCUCON0_CLKSEL',0,28,240,10,3
	.word	26998
	.byte	15,28,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,23
	.byte	'IfxScu_WDTCON1_IR',0,28,255,10,3
	.word	27095
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	27217
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	27774
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	475
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	27851
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	650
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	27987
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	650
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	28267
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	28505
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	650
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	28633
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	650
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	650
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	28876
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	29111
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	29239
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	29339
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	650
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	29439
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	475
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	29647
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	667
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	29812
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	29995
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	475
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	650
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	30149
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	30513
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	650
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	30724
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	475
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	30976
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	31094
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	31205
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	31368
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	31531
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	31689
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	650
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	650
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	10,0,2,35,2,0,23
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	31854
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	650
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	650
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	667
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	32183
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	32404
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	32567
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	32839
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	32992
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	33148
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	33310
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	33453
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	33618
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	33763
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	33944
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	34118
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	34278
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	34422
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	34696
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	34835
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	650
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	667
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	650
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	650
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	34998
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	667
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	650
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	667
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	35216
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	35379
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	35715
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	650
	.byte	2,0,2,35,3,0,23
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	35822
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	36274
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	36373
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	667
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	36523
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	475
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	36672
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	475
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	36833
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	667
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	36963
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	37095
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	667
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	37210
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	667
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	37321
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	650
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	650
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	37479
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	37891
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	667
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	6,0,2,35,3,0,23
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	37992
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	475
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	38259
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	38395
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	650
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	38506
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	38639
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	38842
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	650
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	650
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	39198
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	667
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	39376
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	650
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	650
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	39476
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	650
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	650
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	650
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	9,0,2,35,2,0,23
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	39846
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	40032
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	40230
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	650
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	475
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	40463
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	650
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	650
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	40615
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	650
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	650
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	650
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	650
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	41182
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	650
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	650
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	41476
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	650
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	650
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	650
	.byte	4,0,2,35,3,0,23
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	41754
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	42250
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	667
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	42563
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	650
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	650
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	650
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	650
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	42772
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	650
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	3,0,2,35,3,0,23
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	42983
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	43415
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	650
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	650
	.byte	7,0,2,35,3,0,23
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	43511
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	475
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	43771
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	475
	.byte	23,0,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	43896
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	44093
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	44246
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	44399
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	44552
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	514
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	689
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	933
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	498
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	44807
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	44933
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	45185
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27217
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	45404
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27774
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	45468
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27851
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	45532
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27987
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	45597
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28267
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	45662
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28505
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	45727
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28633
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	45792
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28876
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	45857
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29111
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	45922
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29239
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	45987
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29339
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	46052
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29439
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	46117
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29647
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	46181
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29812
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	46245
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29995
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	46309
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30149
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	46374
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30513
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	46436
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30724
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	46498
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30976
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	46560
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31094
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	46624
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31205
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	46689
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31368
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	46755
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31531
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	46821
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31689
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	46889
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31854
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	46956
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32183
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	47024
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32404
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	47092
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32567
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	47158
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32839
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	47225
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32992
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	47294
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33148
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	47363
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33310
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	47432
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33453
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	47501
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33618
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	47570
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33763
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	47639
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33944
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	47707
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34118
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	47775
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34278
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	47843
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34422
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	47911
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34696
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	47976
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34835
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	48041
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34998
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	48107
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35216
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	48171
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35379
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	48232
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35715
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	48293
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35822
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	48353
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36274
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	48415
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36373
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	48475
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36523
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	48537
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36672
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	48605
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36833
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	48673
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36963
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	48741
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37095
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	48805
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37210
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	48870
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37321
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	48933
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37479
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	48994
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37891
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	49058
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37992
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	49119
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38259
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	49183
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38395
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	49250
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38506
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	49313
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38639
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	49374
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38842
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	49436
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39198
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	49501
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39376
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	49566
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39476
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	49631
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39846
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	49700
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40032
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	49769
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40230
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	49838
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40463
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	49903
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40615
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	49966
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41182
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	50031
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41476
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	50096
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41754
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	50161
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42250
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	50227
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42772
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	50296
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42563
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	50360
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42983
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	50425
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43415
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	50490
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43511
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	50555
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43771
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	50619
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43896
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	50685
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44093
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	50749
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44246
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	50814
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44399
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	50879
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44552
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	50944
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	610
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	893
	.byte	23
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1124
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44807
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	51095
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44933
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	51162
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45185
	.byte	4,2,35,0,0,23
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	51229
	.byte	14
	.word	1164
	.byte	23
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	51294
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	51095
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	51162
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	51229
	.byte	4,2,35,8,0,14
	.word	51323
	.byte	23
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	51384
	.byte	18,8
	.word	46560
	.byte	19,1,0,18,20
	.word	650
	.byte	19,19,0,18,8
	.word	49903
	.byte	19,1,0,14
	.word	51323
	.byte	18,24
	.word	1164
	.byte	19,1,0,14
	.word	51443
	.byte	18,16
	.word	650
	.byte	19,15,0,18,28
	.word	650
	.byte	19,27,0,18,40
	.word	650
	.byte	19,39,0,18,16
	.word	46374
	.byte	19,3,0,18,16
	.word	48353
	.byte	19,3,0,18,180,3
	.word	650
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4922
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	48293
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3103
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	48994
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	49838
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	49436
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	49501
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	49566
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	49769
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	49631
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	49700
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	45597
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	45662
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	48171
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	48107
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	45727
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	45792
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	45857
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	45922
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	50425
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3103
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	50296
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	45532
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	50619
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	50360
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3103
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	47158
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	51411
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	46624
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	50685
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	45987
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	46052
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	51420
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	49313
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	48475
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	49058
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	48933
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	48415
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	47911
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	46889
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	46689
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	46755
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	50555
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3103
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	49966
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	50161
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	50227
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	51429
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3103
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	46309
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	46181
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	50031
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	50096
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	51438
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	46498
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	51452
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5262
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	50944
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	50879
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	50749
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	50814
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3103
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	48741
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	48805
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	46117
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	48870
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4922
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	50490
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	51457
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	48537
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	48605
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	48673
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	51466
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	49250
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4922
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	47976
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	46821
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	48041
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	47092
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	46956
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3103
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	47639
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	47707
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	47775
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	47843
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	47225
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	47294
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	47363
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	47432
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	47501
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	47570
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	47024
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3103
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	49183
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	49119
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	51475
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	51484
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	46436
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	48232
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	49374
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	51493
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3103
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	46245
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	51502
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	45468
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	45404
	.byte	4,3,35,252,7,0,14
	.word	51513
	.byte	23
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	53503
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,21,45,16,4,11
	.byte	'ADDR',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_A_Bits',0,21,48,3
	.word	53525
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,21,51,16,4,11
	.byte	'VSS',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	498
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_CPU_BIV_Bits',0,21,55,3
	.word	53586
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,21,58,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	498
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_CPU_BTV_Bits',0,21,62,3
	.word	53665
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,21,65,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_CCNT_Bits',0,21,69,3
	.word	53751
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,21,72,16,4,11
	.byte	'CM',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	498
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	498
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	498
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	21,0,2,35,0,0,23
	.byte	'Ifx_CPU_CCTRL_Bits',0,21,80,3
	.word	53840
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,21,83,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,23
	.byte	'Ifx_CPU_COMPAT_Bits',0,21,89,3
	.word	53986
	.byte	23
	.byte	'Ifx_CPU_CORE_ID_Bits',0,21,96,3
	.word	24454
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,21,99,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_CPU_CPR_L_Bits',0,21,103,3
	.word	54142
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,21,106,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_CPU_CPR_U_Bits',0,21,110,3
	.word	54235
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,21,113,16,4,11
	.byte	'MODREV',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	498
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_CPU_ID_Bits',0,21,118,3
	.word	54328
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,21,121,16,4,11
	.byte	'XE',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_CPU_CPXE_Bits',0,21,125,3
	.word	54435
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,21,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_CPU_CREVT_Bits',0,21,136,1,3
	.word	54522
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,21,139,1,16,4,11
	.byte	'CID',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_CPU_CUS_ID_Bits',0,21,143,1,3
	.word	54676
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,21,146,1,16,4,11
	.byte	'DATA',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_D_Bits',0,21,149,1,3
	.word	54770
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,21,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	498
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	498
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_DATR_Bits',0,21,163,1,3
	.word	54833
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,21,166,1,16,4,11
	.byte	'DE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	498
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	498
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	498
	.byte	19,0,2,35,0,0,23
	.byte	'Ifx_CPU_DBGSR_Bits',0,21,177,1,3
	.word	55051
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,21,180,1,16,4,11
	.byte	'DTA',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_CPU_DBGTCR_Bits',0,21,184,1,3
	.word	55266
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,21,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_CPU_DCON0_Bits',0,21,192,1,3
	.word	55360
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,21,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_DCON2_Bits',0,21,199,1,3
	.word	55476
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,21,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	498
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_CPU_DCX_Bits',0,21,206,1,3
	.word	55577
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,21,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_DEADD_Bits',0,21,212,1,3
	.word	55670
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,21,215,1,16,4,11
	.byte	'TA',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_DIEAR_Bits',0,21,218,1,3
	.word	55750
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,21,221,1,16,4,11
	.byte	'IED',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	498
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	498
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	18,0,2,35,0,0,23
	.byte	'Ifx_CPU_DIETR_Bits',0,21,233,1,3
	.word	55819
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,21,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	498
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_CPU_DMS_Bits',0,21,240,1,3
	.word	56048
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,21,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_CPU_DPR_L_Bits',0,21,247,1,3
	.word	56141
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,21,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	498
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_CPU_DPR_U_Bits',0,21,254,1,3
	.word	56236
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,21,129,2,16,4,11
	.byte	'RE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_DPRE_Bits',0,21,133,2,3
	.word	56331
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,21,136,2,16,4,11
	.byte	'WE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_DPWE_Bits',0,21,140,2,3
	.word	56421
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,21,143,2,16,4,11
	.byte	'SRE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	498
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	498
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	498
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	498
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	498
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	498
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	498
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	498
	.byte	7,0,2,35,0,0,23
	.byte	'Ifx_CPU_DSTR_Bits',0,21,161,2,3
	.word	56511
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,21,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_CPU_EXEVT_Bits',0,21,172,2,3
	.word	56835
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,21,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,23
	.byte	'Ifx_CPU_FCX_Bits',0,21,180,2,3
	.word	56989
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,21,183,2,16,4,11
	.byte	'TST',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	498
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	498
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	498
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	498
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	498
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	498
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	498
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	498
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	498
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,21,202,2,3
	.word	57095
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,205,2,16,4,11
	.byte	'OPC',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,212,2,3
	.word	57444
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,21,215,2,16,4,11
	.byte	'PC',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,21,218,2,3
	.word	57604
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,224,2,3
	.word	57685
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,230,2,3
	.word	57772
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,236,2,3
	.word	57859
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,21,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_ICNT_Bits',0,21,243,2,3
	.word	57946
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,21,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	498
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	498
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	498
	.byte	6,0,2,35,0,0,23
	.byte	'Ifx_CPU_ICR_Bits',0,21,253,2,3
	.word	58037
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,21,128,3,16,4,11
	.byte	'ISP',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_ISP_Bits',0,21,131,3,3
	.word	58180
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,21,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	498
	.byte	12,0,2,35,0,0,23
	.byte	'Ifx_CPU_LCX_Bits',0,21,139,3,3
	.word	58246
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,21,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_M1CNT_Bits',0,21,146,3,3
	.word	58352
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,21,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_M2CNT_Bits',0,21,153,3,3
	.word	58445
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,21,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	498
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_M3CNT_Bits',0,21,160,3,3
	.word	58538
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,21,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	498
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_CPU_PC_Bits',0,21,167,3,3
	.word	58631
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,21,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_CPU_PCON0_Bits',0,21,175,3,3
	.word	58716
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,21,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	498
	.byte	30,0,2,35,0,0,23
	.byte	'Ifx_CPU_PCON1_Bits',0,21,183,3,3
	.word	58832
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,21,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_PCON2_Bits',0,21,190,3,3
	.word	58943
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,21,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	498
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	498
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	498
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	498
	.byte	10,0,2,35,0,0,23
	.byte	'Ifx_CPU_PCXI_Bits',0,21,200,3,3
	.word	59044
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,21,203,3,16,4,11
	.byte	'TA',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_PIEAR_Bits',0,21,206,3,3
	.word	59174
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,21,209,3,16,4,11
	.byte	'IED',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	498
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	498
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	18,0,2,35,0,0,23
	.byte	'Ifx_CPU_PIETR_Bits',0,21,221,3,3
	.word	59243
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,21,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	498
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_PMA0_Bits',0,21,229,3,3
	.word	59472
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,21,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	498
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	498
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_PMA1_Bits',0,21,237,3,3
	.word	59585
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,21,240,3,16,4,11
	.byte	'PSI',0,4
	.word	498
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	498
	.byte	16,0,2,35,0,0,23
	.byte	'Ifx_CPU_PMA2_Bits',0,21,244,3,3
	.word	59698
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,21,247,3,16,4,11
	.byte	'FRE',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	17,0,2,35,0,0,23
	.byte	'Ifx_CPU_PSTR_Bits',0,21,129,4,3
	.word	59789
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,21,132,4,16,4,11
	.byte	'CDC',0,4
	.word	498
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	498
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	498
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	498
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	498
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	498
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_PSW_Bits',0,21,147,4,3
	.word	59992
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,21,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	498
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	498
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	498
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	498
	.byte	1,0,2,35,0,0,23
	.byte	'Ifx_CPU_SEGEN_Bits',0,21,156,4,3
	.word	60235
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,21,159,4,16,4,11
	.byte	'PC',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	498
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	498
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	498
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	498
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	498
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	498
	.byte	7,0,2,35,0,0,23
	.byte	'Ifx_CPU_SMACON_Bits',0,21,171,4,3
	.word	60363
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,21,174,4,16,4,11
	.byte	'EN',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,21,177,4,3
	.word	60604
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,21,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,21,183,4,3
	.word	60687
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,186,4,16,4,11
	.byte	'EN',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,189,4,3
	.word	60778
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,195,4,3
	.word	60869
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,21,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	27,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,21,202,4,3
	.word	60968
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,21,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	27,0,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,21,209,4,3
	.word	61075
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,21,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_CPU_SWEVT_Bits',0,21,220,4,3
	.word	61182
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,21,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,23
	.byte	'Ifx_CPU_SYSCON_Bits',0,21,231,4,3
	.word	61336
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,21,234,4,16,4,11
	.byte	'ASI',0,4
	.word	498
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	498
	.byte	27,0,2,35,0,0,23
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,21,238,4,3
	.word	61497
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,21,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	498
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	498
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	498
	.byte	15,0,2,35,0,0,23
	.byte	'Ifx_CPU_TPS_CON_Bits',0,21,249,4,3
	.word	61595
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,21,252,4,16,4,11
	.byte	'Timer',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,21,255,4,3
	.word	61767
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,21,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	498
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_CPU_TR_ADR_Bits',0,21,133,5,3
	.word	61847
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,21,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	498
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	498
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	498
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	498
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	498
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	498
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	498
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	498
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	498
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	498
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	498
	.byte	3,0,2,35,0,0,23
	.byte	'Ifx_CPU_TR_EVT_Bits',0,21,153,5,3
	.word	61920
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,21,156,5,16,4,11
	.byte	'T0',0,4
	.word	498
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	498
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	498
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	498
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	498
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	498
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	498
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	498
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	498
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,21,167,5,3
	.word	62238
	.byte	12,21,175,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53525
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_A',0,21,180,5,3
	.word	62433
	.byte	12,21,183,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53586
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_BIV',0,21,188,5,3
	.word	62492
	.byte	12,21,191,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53665
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_BTV',0,21,196,5,3
	.word	62553
	.byte	12,21,199,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53751
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CCNT',0,21,204,5,3
	.word	62614
	.byte	12,21,207,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53840
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CCTRL',0,21,212,5,3
	.word	62676
	.byte	12,21,215,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53986
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_COMPAT',0,21,220,5,3
	.word	62739
	.byte	23
	.byte	'Ifx_CPU_CORE_ID',0,21,228,5,3
	.word	24523
	.byte	12,21,231,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54142
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CPR_L',0,21,236,5,3
	.word	62828
	.byte	12,21,239,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54235
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CPR_U',0,21,244,5,3
	.word	62891
	.byte	12,21,247,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54328
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CPU_ID',0,21,252,5,3
	.word	62954
	.byte	12,21,255,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54435
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CPXE',0,21,132,6,3
	.word	63018
	.byte	12,21,135,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54522
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CREVT',0,21,140,6,3
	.word	63080
	.byte	12,21,143,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54676
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_CUS_ID',0,21,148,6,3
	.word	63143
	.byte	12,21,151,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54770
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_D',0,21,156,6,3
	.word	63207
	.byte	12,21,159,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54833
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DATR',0,21,164,6,3
	.word	63266
	.byte	12,21,167,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55051
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DBGSR',0,21,172,6,3
	.word	63328
	.byte	12,21,175,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55266
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DBGTCR',0,21,180,6,3
	.word	63391
	.byte	12,21,183,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55360
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DCON0',0,21,188,6,3
	.word	63455
	.byte	12,21,191,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55476
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DCON2',0,21,196,6,3
	.word	63518
	.byte	12,21,199,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55577
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DCX',0,21,204,6,3
	.word	63581
	.byte	12,21,207,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55670
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DEADD',0,21,212,6,3
	.word	63642
	.byte	12,21,215,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55750
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DIEAR',0,21,220,6,3
	.word	63705
	.byte	12,21,223,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55819
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DIETR',0,21,228,6,3
	.word	63768
	.byte	12,21,231,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56048
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DMS',0,21,236,6,3
	.word	63831
	.byte	12,21,239,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56141
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DPR_L',0,21,244,6,3
	.word	63892
	.byte	12,21,247,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56236
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DPR_U',0,21,252,6,3
	.word	63955
	.byte	12,21,255,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56331
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DPRE',0,21,132,7,3
	.word	64018
	.byte	12,21,135,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56421
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DPWE',0,21,140,7,3
	.word	64080
	.byte	12,21,143,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56511
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_DSTR',0,21,148,7,3
	.word	64142
	.byte	12,21,151,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56835
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_EXEVT',0,21,156,7,3
	.word	64204
	.byte	12,21,159,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56989
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FCX',0,21,164,7,3
	.word	64267
	.byte	12,21,167,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57095
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,21,172,7,3
	.word	64328
	.byte	12,21,175,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57444
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,21,180,7,3
	.word	64398
	.byte	12,21,183,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57604
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,21,188,7,3
	.word	64468
	.byte	12,21,191,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57685
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,21,196,7,3
	.word	64537
	.byte	12,21,199,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57772
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,21,204,7,3
	.word	64608
	.byte	12,21,207,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57859
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,21,212,7,3
	.word	64679
	.byte	12,21,215,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57946
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_ICNT',0,21,220,7,3
	.word	64750
	.byte	12,21,223,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58037
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_ICR',0,21,228,7,3
	.word	64812
	.byte	12,21,231,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58180
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_ISP',0,21,236,7,3
	.word	64873
	.byte	12,21,239,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58246
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_LCX',0,21,244,7,3
	.word	64934
	.byte	12,21,247,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58352
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_M1CNT',0,21,252,7,3
	.word	64995
	.byte	12,21,255,7,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58445
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_M2CNT',0,21,132,8,3
	.word	65058
	.byte	12,21,135,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58538
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_M3CNT',0,21,140,8,3
	.word	65121
	.byte	12,21,143,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58631
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PC',0,21,148,8,3
	.word	65184
	.byte	12,21,151,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58716
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PCON0',0,21,156,8,3
	.word	65244
	.byte	12,21,159,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58832
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PCON1',0,21,164,8,3
	.word	65307
	.byte	12,21,167,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58943
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PCON2',0,21,172,8,3
	.word	65370
	.byte	12,21,175,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59044
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PCXI',0,21,180,8,3
	.word	65433
	.byte	12,21,183,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59174
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PIEAR',0,21,188,8,3
	.word	65495
	.byte	12,21,191,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59243
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PIETR',0,21,196,8,3
	.word	65558
	.byte	12,21,199,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59472
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PMA0',0,21,204,8,3
	.word	65621
	.byte	12,21,207,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59585
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PMA1',0,21,212,8,3
	.word	65683
	.byte	12,21,215,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59698
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PMA2',0,21,220,8,3
	.word	65745
	.byte	12,21,223,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59789
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PSTR',0,21,228,8,3
	.word	65807
	.byte	12,21,231,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59992
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_PSW',0,21,236,8,3
	.word	65869
	.byte	12,21,239,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60235
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SEGEN',0,21,244,8,3
	.word	65930
	.byte	12,21,247,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60363
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SMACON',0,21,252,8,3
	.word	65993
	.byte	12,21,255,8,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60604
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_ACCENA',0,21,132,9,3
	.word	66057
	.byte	12,21,135,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60687
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_ACCENB',0,21,140,9,3
	.word	66127
	.byte	12,21,143,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60778
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,21,148,9,3
	.word	66197
	.byte	12,21,151,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60869
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,21,156,9,3
	.word	66271
	.byte	12,21,159,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60968
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,21,164,9,3
	.word	66345
	.byte	12,21,167,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61075
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,21,172,9,3
	.word	66415
	.byte	12,21,175,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61182
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SWEVT',0,21,180,9,3
	.word	66485
	.byte	12,21,183,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61336
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_SYSCON',0,21,188,9,3
	.word	66548
	.byte	12,21,191,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61497
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TASK_ASI',0,21,196,9,3
	.word	66612
	.byte	12,21,199,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61595
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TPS_CON',0,21,204,9,3
	.word	66678
	.byte	12,21,207,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61767
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TPS_TIMER',0,21,212,9,3
	.word	66743
	.byte	12,21,215,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61847
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TR_ADR',0,21,220,9,3
	.word	66810
	.byte	12,21,223,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61920
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TR_EVT',0,21,228,9,3
	.word	66874
	.byte	12,21,231,9,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62238
	.byte	4,2,35,0,0,23
	.byte	'Ifx_CPU_TRIG_ACC',0,21,236,9,3
	.word	66938
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,21,247,9,25,8,13
	.byte	'L',0
	.word	62828
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	62891
	.byte	4,2,35,4,0,14
	.word	67004
	.byte	23
	.byte	'Ifx_CPU_CPR',0,21,251,9,3
	.word	67046
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,21,254,9,25,8,13
	.byte	'L',0
	.word	63892
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	63955
	.byte	4,2,35,4,0,14
	.word	67072
	.byte	23
	.byte	'Ifx_CPU_DPR',0,21,130,10,3
	.word	67114
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,21,133,10,25,16,13
	.byte	'LA',0
	.word	66345
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	66415
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	66197
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	66271
	.byte	4,2,35,12,0,14
	.word	67140
	.byte	23
	.byte	'Ifx_CPU_SPROT_RGN',0,21,139,10,3
	.word	67222
	.byte	18,12
	.word	66743
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,21,142,10,25,16,13
	.byte	'CON',0
	.word	66678
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	67254
	.byte	12,2,35,4,0,14
	.word	67263
	.byte	23
	.byte	'Ifx_CPU_TPS',0,21,146,10,3
	.word	67311
	.byte	10
	.byte	'_Ifx_CPU_TR',0,21,149,10,25,8,13
	.byte	'EVT',0
	.word	66874
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	66810
	.byte	4,2,35,4,0,14
	.word	67337
	.byte	23
	.byte	'Ifx_CPU_TR',0,21,153,10,3
	.word	67382
	.byte	18,176,32
	.word	650
	.byte	19,175,32,0,18,208,223,1
	.word	650
	.byte	19,207,223,1,0,18,248,1
	.word	650
	.byte	19,247,1,0,18,244,29
	.word	650
	.byte	19,243,29,0,18,188,3
	.word	650
	.byte	19,187,3,0,18,232,3
	.word	650
	.byte	19,231,3,0,18,252,23
	.word	650
	.byte	19,251,23,0,18,228,63
	.word	650
	.byte	19,227,63,0,18,128,1
	.word	67072
	.byte	19,15,0,14
	.word	67497
	.byte	18,128,31
	.word	650
	.byte	19,255,30,0,18,64
	.word	67004
	.byte	19,7,0,14
	.word	67523
	.byte	18,192,31
	.word	650
	.byte	19,191,31,0,18,16
	.word	63018
	.byte	19,3,0,18,16
	.word	64018
	.byte	19,3,0,18,16
	.word	64080
	.byte	19,3,0,18,208,7
	.word	650
	.byte	19,207,7,0,14
	.word	67263
	.byte	18,240,23
	.word	650
	.byte	19,239,23,0,18,64
	.word	67337
	.byte	19,7,0,14
	.word	67602
	.byte	18,192,23
	.word	650
	.byte	19,191,23,0,18,232,1
	.word	650
	.byte	19,231,1,0,18,180,1
	.word	650
	.byte	19,179,1,0,18,172,1
	.word	650
	.byte	19,171,1,0,18,64
	.word	63207
	.byte	19,15,0,18,64
	.word	650
	.byte	19,63,0,18,64
	.word	62433
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,21,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	67407
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	65930
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	67418
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	66612
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	67431
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	65621
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	65683
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	65745
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	67442
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	63518
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4922
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	65993
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	64142
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3103
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	63266
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	63642
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	63705
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	63768
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4293
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	63455
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	67453
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	65807
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	65307
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	65370
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	65244
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	65495
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	65558
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	67464
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	62739
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	67475
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	64328
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	64468
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	64398
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3103
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	64537
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	64608
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	64679
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	67486
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	67507
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	67512
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	67532
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	67537
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	67548
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	67557
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	67566
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	67575
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	67586
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	67591
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	67611
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	67616
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	62676
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	62614
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	64750
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	64995
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	65058
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	65121
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	67627
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	63328
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3103
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	64204
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	63080
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	66485
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	51466
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	66938
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5262
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	63831
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	63581
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	63391
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	67638
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	65433
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	65869
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	65184
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4922
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	66548
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	62954
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	24523
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	62492
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	62553
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	64873
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	64812
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4922
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	64267
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	64934
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	51457
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	63143
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	67649
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	67660
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	67669
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	67678
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	67669
	.byte	64,4,35,192,255,3,0,14
	.word	67687
	.byte	23
	.byte	'Ifx_CPU',0,21,130,11,3
	.word	69478
	.byte	23
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	1462
	.byte	23
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1544
	.byte	23
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10664
	.byte	23
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10954
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	69594
	.byte	23
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	69626
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,8,0,14
	.word	69652
	.byte	23
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	69711
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	69739
	.byte	23
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	69776
	.byte	18,64
	.word	10954
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	69804
	.byte	64,2,35,0,0,14
	.word	69813
	.byte	23
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	69845
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10954
	.byte	4,2,35,12,0,14
	.word	69870
	.byte	23
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	69942
	.byte	18,8
	.word	10954
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	69968
	.byte	8,2,35,0,0,14
	.word	69977
	.byte	23
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	70013
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10954
	.byte	4,2,35,12,0,14
	.word	70043
	.byte	23
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	70116
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	70142
	.byte	23
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	70177
	.byte	18,192,1
	.word	10954
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5262
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	70203
	.byte	192,1,2,35,16,0,14
	.word	70213
	.byte	23
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	70280
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10954
	.byte	4,2,35,4,0,14
	.word	70306
	.byte	23
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	70354
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	70382
	.byte	23
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	70415
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	69968
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	69968
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	69968
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	69968
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10954
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10954
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	51475
	.byte	40,2,35,40,0,14
	.word	70442
	.byte	23
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	70569
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	70596
	.byte	23
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	70628
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	70654
	.byte	23
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	70686
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10954
	.byte	4,2,35,8,0,14
	.word	70712
	.byte	23
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	70772
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10954
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	51457
	.byte	16,2,35,16,0,14
	.word	70798
	.byte	23
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	70892
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10954
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10954
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10954
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4293
	.byte	24,2,35,24,0,14
	.word	70919
	.byte	23
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	71036
	.byte	18,12
	.word	10954
	.byte	19,2,0,18,32
	.word	10954
	.byte	19,7,0,18,32
	.word	71073
	.byte	19,0,0,18,88
	.word	650
	.byte	19,87,0,18,108
	.word	10954
	.byte	19,26,0,18,96
	.word	650
	.byte	19,95,0,18,96
	.word	71073
	.byte	19,2,0,18,160,3
	.word	650
	.byte	19,159,3,0,18,64
	.word	71073
	.byte	19,1,0,18,192,3
	.word	650
	.byte	19,191,3,0,18,16
	.word	10954
	.byte	19,3,0,18,64
	.word	71158
	.byte	19,3,0,18,192,2
	.word	650
	.byte	19,191,2,0,18,52
	.word	650
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	71064
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3103
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10954
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10954
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	69968
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4922
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	71082
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	71091
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	71100
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	71109
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10954
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5262
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	71118
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	71127
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	71118
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	71127
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	71138
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	71147
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	71167
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	71176
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	71064
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	71187
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	71064
	.byte	12,3,35,192,18,0,14
	.word	71196
	.byte	23
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	71656
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	71682
	.byte	23
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	71715
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10954
	.byte	4,2,35,12,0,14
	.word	71742
	.byte	23
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	71815
	.byte	18,56
	.word	650
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10954
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10954
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	71842
	.byte	56,2,35,24,0,14
	.word	71851
	.byte	23
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	71974
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	72000
	.byte	23
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	72032
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10954
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10954
	.byte	4,2,35,16,0,14
	.word	72058
	.byte	23
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	72143
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	72169
	.byte	23
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	72201
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	71073
	.byte	32,2,35,0,0,14
	.word	72227
	.byte	23
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	72260
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	71073
	.byte	32,2,35,0,0,14
	.word	72287
	.byte	23
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	72321
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10954
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10954
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10954
	.byte	4,2,35,20,0,14
	.word	72349
	.byte	23
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	72442
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	72469
	.byte	23
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	72501
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	71158
	.byte	16,2,35,4,0,14
	.word	72527
	.byte	23
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	72573
	.byte	18,24
	.word	10954
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	72599
	.byte	24,2,35,0,0,14
	.word	72608
	.byte	23
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	72641
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	71064
	.byte	12,2,35,0,0,14
	.word	72668
	.byte	23
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	72700
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,0,14
	.word	72726
	.byte	23
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	72772
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10954
	.byte	4,2,35,12,0,14
	.word	72798
	.byte	23
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	72873
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10954
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10954
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10954
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10954
	.byte	4,2,35,12,0,14
	.word	72902
	.byte	23
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	72976
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10954
	.byte	4,2,35,0,0,14
	.word	73004
	.byte	23
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	73038
	.byte	18,4
	.word	69594
	.byte	19,0,0,14
	.word	73065
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	73074
	.byte	4,2,35,0,0,14
	.word	73079
	.byte	23
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	73115
	.byte	18,48
	.word	69652
	.byte	19,3,0,14
	.word	73143
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	73152
	.byte	48,2,35,0,0,14
	.word	73157
	.byte	23
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	73197
	.byte	14
	.word	69739
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	73227
	.byte	4,2,35,0,0,14
	.word	73232
	.byte	23
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	73266
	.byte	18,64
	.word	69813
	.byte	19,0,0,14
	.word	73293
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	73302
	.byte	64,2,35,0,0,14
	.word	73307
	.byte	23
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	73341
	.byte	18,32
	.word	69870
	.byte	19,1,0,14
	.word	73368
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	73377
	.byte	32,2,35,0,0,14
	.word	73382
	.byte	23
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	73418
	.byte	14
	.word	69977
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	73446
	.byte	8,2,35,0,0,14
	.word	73451
	.byte	23
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	73495
	.byte	18,16
	.word	70043
	.byte	19,0,0,14
	.word	73527
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	73536
	.byte	16,2,35,0,0,14
	.word	73541
	.byte	23
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	73575
	.byte	18,8
	.word	70142
	.byte	19,1,0,14
	.word	73602
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	73611
	.byte	8,2,35,0,0,14
	.word	73616
	.byte	23
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	73650
	.byte	18,208,1
	.word	70213
	.byte	19,0,0,14
	.word	73677
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	73687
	.byte	208,1,2,35,0,0,14
	.word	73692
	.byte	23
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	73728
	.byte	14
	.word	70306
	.byte	14
	.word	70306
	.byte	14
	.word	70306
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	73755
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4922
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	73760
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	73765
	.byte	8,2,35,24,0,14
	.word	73770
	.byte	23
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	73861
	.byte	18,4
	.word	70382
	.byte	19,0,0,14
	.word	73890
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	73899
	.byte	4,2,35,0,0,14
	.word	73904
	.byte	23
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	73940
	.byte	18,80
	.word	70442
	.byte	19,0,0,14
	.word	73968
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	73977
	.byte	80,2,35,0,0,14
	.word	73982
	.byte	23
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	74018
	.byte	18,4
	.word	70596
	.byte	19,0,0,14
	.word	74046
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	74055
	.byte	4,2,35,0,0,14
	.word	74060
	.byte	23
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	74094
	.byte	18,4
	.word	70654
	.byte	19,0,0,14
	.word	74121
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	74130
	.byte	4,2,35,0,0,14
	.word	74135
	.byte	23
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	74169
	.byte	18,12
	.word	70712
	.byte	19,0,0,14
	.word	74196
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	74205
	.byte	12,2,35,0,0,14
	.word	74210
	.byte	23
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	74244
	.byte	18,64
	.word	70798
	.byte	19,1,0,14
	.word	74271
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	74280
	.byte	64,2,35,0,0,14
	.word	74285
	.byte	23
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	74321
	.byte	18,48
	.word	70919
	.byte	19,0,0,14
	.word	74349
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	74358
	.byte	48,2,35,0,0,14
	.word	74363
	.byte	23
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	74401
	.byte	18,204,18
	.word	71196
	.byte	19,0,0,14
	.word	74430
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	74440
	.byte	204,18,2,35,0,0,14
	.word	74445
	.byte	23
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	74481
	.byte	18,4
	.word	71682
	.byte	19,0,0,14
	.word	74508
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	74517
	.byte	4,2,35,0,0,14
	.word	74522
	.byte	23
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	74558
	.byte	18,64
	.word	71742
	.byte	19,3,0,14
	.word	74586
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	74595
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10954
	.byte	4,2,35,64,0,14
	.word	74600
	.byte	23
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	74649
	.byte	18,80
	.word	71851
	.byte	19,0,0,14
	.word	74677
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	74686
	.byte	80,2,35,0,0,14
	.word	74691
	.byte	23
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	74725
	.byte	18,4
	.word	72000
	.byte	19,0,0,14
	.word	74752
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	74761
	.byte	4,2,35,0,0,14
	.word	74766
	.byte	23
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	74800
	.byte	18,40
	.word	72058
	.byte	19,1,0,14
	.word	74827
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	74836
	.byte	40,2,35,0,0,14
	.word	74841
	.byte	23
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	74875
	.byte	18,8
	.word	72169
	.byte	19,1,0,14
	.word	74902
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	74911
	.byte	8,2,35,0,0,14
	.word	74916
	.byte	23
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	74950
	.byte	18,32
	.word	72227
	.byte	19,0,0,14
	.word	74977
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	74986
	.byte	32,2,35,0,0,14
	.word	74991
	.byte	23
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	75027
	.byte	18,32
	.word	72287
	.byte	19,0,0,14
	.word	75055
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	75064
	.byte	32,2,35,0,0,14
	.word	75069
	.byte	23
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	75107
	.byte	18,96
	.word	72349
	.byte	19,3,0,14
	.word	75136
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	75145
	.byte	96,2,35,0,0,14
	.word	75150
	.byte	23
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	75186
	.byte	18,4
	.word	72469
	.byte	19,0,0,14
	.word	75214
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	75223
	.byte	4,2,35,0,0,14
	.word	75228
	.byte	23
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	75262
	.byte	14
	.word	72527
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	75289
	.byte	20,2,35,0,0,14
	.word	75294
	.byte	23
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	75328
	.byte	18,24
	.word	72608
	.byte	19,0,0,14
	.word	75355
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	75364
	.byte	24,2,35,0,0,14
	.word	75369
	.byte	23
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	75405
	.byte	18,12
	.word	72668
	.byte	19,0,0,14
	.word	75433
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	75442
	.byte	12,2,35,0,0,14
	.word	75447
	.byte	23
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	75481
	.byte	18,16
	.word	72726
	.byte	19,1,0,14
	.word	75508
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	75517
	.byte	16,2,35,0,0,14
	.word	75522
	.byte	23
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	75556
	.byte	18,64
	.word	72902
	.byte	19,3,0,14
	.word	75583
	.byte	18,224,1
	.word	650
	.byte	19,223,1,0,18,32
	.word	72798
	.byte	19,1,0,14
	.word	75608
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	75592
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	75597
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	75617
	.byte	32,3,35,160,2,0,14
	.word	75622
	.byte	23
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	75691
	.byte	14
	.word	73004
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	75719
	.byte	4,2,35,0,0,14
	.word	75724
	.byte	23
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	75760
	.byte	23
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20884
	.byte	23
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20795
	.byte	23
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19325
	.byte	23
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20202
	.byte	23
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18448
	.byte	23
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19503
	.byte	23
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19412
	.byte	23
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19734
	.byte	23
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18604
	.byte	23
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19951
	.byte	23
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20672
	.byte	23
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20568
	.byte	23
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20462
	.byte	23
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20302
	.byte	23
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18726
	.byte	23
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20115
	.byte	23
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18811
	.byte	23
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18896
	.byte	23
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18981
	.byte	23
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	19067
	.byte	23
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19153
	.byte	23
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19239
	.byte	23
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21413
	.byte	23
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20844
	.byte	23
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19372
	.byte	23
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20251
	.byte	23
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18564
	.byte	23
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19694
	.byte	23
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19454
	.byte	23
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19911
	.byte	23
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18686
	.byte	23
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	20075
	.byte	23
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20755
	.byte	23
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20632
	.byte	23
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20528
	.byte	23
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20422
	.byte	23
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18771
	.byte	23
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20162
	.byte	23
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18856
	.byte	23
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18941
	.byte	23
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	19027
	.byte	23
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19113
	.byte	23
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19199
	.byte	23
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19285
	.byte	14
	.word	21453
	.byte	23
	.byte	'Ifx_STM',0,16,201,3,3
	.word	76871
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,23
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	76893
	.byte	20,5,160,1,9,6,13
	.byte	'counter',0
	.word	1866
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	650
	.byte	1,2,35,4,0,23
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	76982
	.byte	20,5,172,1,9,32,13
	.byte	'instruction',0
	.word	76982
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	76982
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	76982
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	76982
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	76982
	.byte	6,2,35,24,0,23
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	77048
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,29,45,16,4,11
	.byte	'EN0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,29,79,3
	.word	77166
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,29,82,16,4,11
	.byte	'reserved_0',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,29,85,3
	.word	77727
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,29,88,16,4,11
	.byte	'SEL',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,23
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,29,95,3
	.word	77808
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,29,98,16,4,11
	.byte	'VLD0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,23
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,29,111,3
	.word	77961
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,29,114,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,29,121,3
	.word	78209
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,29,124,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	475
	.byte	24,0,2,35,0,0,23
	.byte	'Ifx_FLASH_COMM0_Bits',0,29,128,1,3
	.word	78355
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,29,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_COMM1_Bits',0,29,136,1,3
	.word	78453
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,29,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_COMM2_Bits',0,29,144,1,3
	.word	78569
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,29,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_ECCRD_Bits',0,29,153,1,3
	.word	78685
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,29,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_ECCRP_Bits',0,29,162,1,3
	.word	78825
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,29,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	475
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	667
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_ECCW_Bits',0,29,171,1,3
	.word	78965
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,29,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	650
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	650
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	667
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	650
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	650
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_FCON_Bits',0,29,193,1,3
	.word	79104
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,29,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	650
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	650
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	650
	.byte	8,0,2,35,3,0,23
	.byte	'Ifx_FLASH_FPRO_Bits',0,29,218,1,3
	.word	79466
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,29,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	667
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	650
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	650
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	650
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_FSR_Bits',0,29,254,1,3
	.word	79907
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,29,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	650
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	650
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_ID_Bits',0,29,134,2,3
	.word	80513
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,29,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	667
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_MARD_Bits',0,29,147,2,3
	.word	80624
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,29,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	667
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_MARP_Bits',0,29,159,2,3
	.word	80838
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,29,162,2,16,4,11
	.byte	'L',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	650
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	650
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	667
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	650
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_PROCOND_Bits',0,29,179,2,3
	.word	81025
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,29,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	650
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	475
	.byte	28,0,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,29,188,2,3
	.word	81349
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,29,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	667
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,29,199,2,3
	.word	81492
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	667
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	650
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	650
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	650
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	667
	.byte	14,0,2,35,2,0,23
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,29,219,2,3
	.word	81681
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,29,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	650
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,29,254,2,3
	.word	82044
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,29,129,3,16,4,11
	.byte	'S0L',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_PROCONP_Bits',0,29,160,3,3
	.word	82639
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,29,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	650
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	650
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	650
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	650
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	650
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	650
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	650
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	650
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	650
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	650
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	650
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	650
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	650
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	650
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	650
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	650
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	650
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	650
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	650
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	650
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	650
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,29,194,3,3
	.word	83163
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,29,197,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,29,201,3,3
	.word	83745
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,29,204,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,29,208,3,3
	.word	83847
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,29,211,3,16,4,11
	.byte	'TAG',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	475
	.byte	26,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,29,215,3,3
	.word	83949
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,29,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	475
	.byte	29,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RRAD_Bits',0,29,222,3,3
	.word	84051
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,29,225,3,16,4,11
	.byte	'STRT',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	650
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	650
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	667
	.byte	16,0,2,35,2,0,23
	.byte	'Ifx_FLASH_RRCT_Bits',0,29,236,3,3
	.word	84145
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,29,239,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RRD0_Bits',0,29,242,3,3
	.word	84355
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,29,245,3,16,4,11
	.byte	'DATA',0,4
	.word	475
	.byte	32,0,2,35,0,0,23
	.byte	'Ifx_FLASH_RRD1_Bits',0,29,248,3,3
	.word	84428
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,29,251,3,16,4,11
	.byte	'SEL',0,1
	.word	650
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	650
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	650
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	475
	.byte	22,0,2,35,0,0,23
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,29,130,4,3
	.word	84501
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,29,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	475
	.byte	31,0,2,35,0,0,23
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,29,137,4,3
	.word	84656
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,29,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	650
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	475
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	650
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	650
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	650
	.byte	1,0,2,35,3,0,23
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,29,147,4,3
	.word	84761
	.byte	12,29,155,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77166
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ACCEN0',0,29,160,4,3
	.word	84909
	.byte	12,29,163,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77727
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ACCEN1',0,29,168,4,3
	.word	84975
	.byte	12,29,171,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77808
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_CBAB_CFG',0,29,176,4,3
	.word	85041
	.byte	12,29,179,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77961
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_CBAB_STAT',0,29,184,4,3
	.word	85109
	.byte	12,29,187,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78209
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_CBAB_TOP',0,29,192,4,3
	.word	85178
	.byte	12,29,195,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78355
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_COMM0',0,29,200,4,3
	.word	85246
	.byte	12,29,203,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78453
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_COMM1',0,29,208,4,3
	.word	85311
	.byte	12,29,211,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78569
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_COMM2',0,29,216,4,3
	.word	85376
	.byte	12,29,219,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78685
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ECCRD',0,29,224,4,3
	.word	85441
	.byte	12,29,227,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78825
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ECCRP',0,29,232,4,3
	.word	85506
	.byte	12,29,235,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78965
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ECCW',0,29,240,4,3
	.word	85571
	.byte	12,29,243,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79104
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_FCON',0,29,248,4,3
	.word	85635
	.byte	12,29,251,4,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79466
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_FPRO',0,29,128,5,3
	.word	85699
	.byte	12,29,131,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79907
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_FSR',0,29,136,5,3
	.word	85763
	.byte	12,29,139,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80513
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_ID',0,29,144,5,3
	.word	85826
	.byte	12,29,147,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80624
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_MARD',0,29,152,5,3
	.word	85888
	.byte	12,29,155,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80838
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_MARP',0,29,160,5,3
	.word	85952
	.byte	12,29,163,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81025
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCOND',0,29,168,5,3
	.word	86016
	.byte	12,29,171,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81349
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONDBG',0,29,176,5,3
	.word	86083
	.byte	12,29,179,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81492
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONHSM',0,29,184,5,3
	.word	86152
	.byte	12,29,187,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81681
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,29,192,5,3
	.word	86221
	.byte	12,29,195,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82044
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONOTP',0,29,200,5,3
	.word	86294
	.byte	12,29,203,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82639
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONP',0,29,208,5,3
	.word	86363
	.byte	12,29,211,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83163
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_PROCONWOP',0,29,216,5,3
	.word	86430
	.byte	12,29,219,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83745
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG0',0,29,224,5,3
	.word	86499
	.byte	12,29,227,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83847
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG1',0,29,232,5,3
	.word	86567
	.byte	12,29,235,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83949
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RDB_CFG2',0,29,240,5,3
	.word	86635
	.byte	12,29,243,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84051
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RRAD',0,29,248,5,3
	.word	86703
	.byte	12,29,251,5,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84145
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RRCT',0,29,128,6,3
	.word	86767
	.byte	12,29,131,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84355
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RRD0',0,29,136,6,3
	.word	86831
	.byte	12,29,139,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84428
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_RRD1',0,29,144,6,3
	.word	86895
	.byte	12,29,147,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84501
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_UBAB_CFG',0,29,152,6,3
	.word	86959
	.byte	12,29,155,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84656
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_UBAB_STAT',0,29,160,6,3
	.word	87027
	.byte	12,29,163,6,9,4,13
	.byte	'U',0
	.word	475
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	491
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84761
	.byte	4,2,35,0,0,23
	.byte	'Ifx_FLASH_UBAB_TOP',0,29,168,6,3
	.word	87096
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,29,179,6,25,12,13
	.byte	'CFG',0
	.word	85041
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	85109
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	85178
	.byte	4,2,35,8,0,14
	.word	87164
	.byte	23
	.byte	'Ifx_FLASH_CBAB',0,29,184,6,3
	.word	87227
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,29,187,6,25,12,13
	.byte	'CFG0',0
	.word	86499
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	86567
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	86635
	.byte	4,2,35,8,0,14
	.word	87256
	.byte	23
	.byte	'Ifx_FLASH_RDB',0,29,192,6,3
	.word	87320
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,29,195,6,25,12,13
	.byte	'CFG',0
	.word	86959
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	87027
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	87096
	.byte	4,2,35,8,0,14
	.word	87348
	.byte	23
	.byte	'Ifx_FLASH_UBAB',0,29,200,6,3
	.word	87411
	.byte	23
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8675
	.byte	23
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8588
	.byte	23
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4931
	.byte	23
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2984
	.byte	23
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3979
	.byte	23
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3112
	.byte	23
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3759
	.byte	23
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3327
	.byte	23
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3542
	.byte	23
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7947
	.byte	23
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	8071
	.byte	23
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8155
	.byte	23
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8335
	.byte	23
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6586
	.byte	23
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7110
	.byte	23
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6760
	.byte	23
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6934
	.byte	23
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7599
	.byte	23
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2413
	.byte	23
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5923
	.byte	23
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6411
	.byte	23
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	6070
	.byte	23
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6239
	.byte	23
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7266
	.byte	23
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	2097
	.byte	23
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5637
	.byte	23
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5271
	.byte	23
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4302
	.byte	23
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4606
	.byte	23
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9202
	.byte	23
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8635
	.byte	23
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5222
	.byte	23
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	3063
	.byte	23
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4253
	.byte	23
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3287
	.byte	23
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3939
	.byte	23
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3502
	.byte	23
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3719
	.byte	23
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	8031
	.byte	23
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8280
	.byte	23
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8539
	.byte	23
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7907
	.byte	23
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6720
	.byte	23
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7226
	.byte	23
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6894
	.byte	23
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	7070
	.byte	23
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2944
	.byte	23
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7559
	.byte	23
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	6030
	.byte	23
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6546
	.byte	23
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6199
	.byte	23
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6371
	.byte	23
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2373
	.byte	23
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5883
	.byte	23
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5597
	.byte	23
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4566
	.byte	23
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4882
	.byte	14
	.word	9242
	.byte	23
	.byte	'Ifx_P',0,8,139,6,3
	.word	88758
	.byte	23
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9855
	.byte	23
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	10130
	.byte	23
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	10060
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,23
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	88859
	.byte	23
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10443
	.byte	20,7,190,1,9,8,13
	.byte	'port',0
	.word	9850
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	650
	.byte	1,2,35,4,0,23
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	89324
	.byte	23
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	214
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	1866
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1866
	.byte	4,2,35,4,0,23
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	89424
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	650
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	650
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	271
	.byte	4,2,35,4,0,23
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	89495
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	271
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	89384
	.byte	4,2,35,8,0,23
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	89612
	.byte	3
	.word	211
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	89424
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	89424
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	89424
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	89424
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	89424
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	89424
	.byte	8,2,35,40,0,23
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	89714
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	1866
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1866
	.byte	4,2,35,4,0,23
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	89866
	.byte	3
	.word	89612
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	89942
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	89495
	.byte	8,2,35,8,0,23
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	89947
	.byte	15,30,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,23
	.byte	'IfxSrc_Tos',0,30,74,3
	.word	90064
	.byte	20,31,59,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	25017
	.byte	1,2,35,12,0,34
	.word	90142
	.byte	23
	.byte	'IfxAsclin_Cts_In',0,31,64,3
	.word	90193
	.byte	20,31,67,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	25017
	.byte	1,2,35,12,0,34
	.word	90223
	.byte	23
	.byte	'IfxAsclin_Rx_In',0,31,72,3
	.word	90274
	.byte	20,31,75,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10130
	.byte	1,2,35,12,0,34
	.word	90303
	.byte	23
	.byte	'IfxAsclin_Rts_Out',0,31,80,3
	.word	90354
	.byte	20,31,83,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10130
	.byte	1,2,35,12,0,34
	.word	90385
	.byte	23
	.byte	'IfxAsclin_Sclk_Out',0,31,88,3
	.word	90436
	.byte	20,31,91,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10130
	.byte	1,2,35,12,0,34
	.word	90468
	.byte	23
	.byte	'IfxAsclin_Slso_Out',0,31,96,3
	.word	90519
	.byte	20,31,99,15,16,13
	.byte	'module',0
	.word	17578
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89324
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10130
	.byte	1,2,35,12,0,34
	.word	90551
	.byte	23
	.byte	'IfxAsclin_Tx_Out',0,31,104,3
	.word	90602
	.byte	15,12,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,23
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	90632
	.byte	15,12,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,23
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	90724
	.byte	15,12,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,23
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	90845
	.byte	15,12,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,23
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	90952
	.byte	23
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17645
	.byte	15,12,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,23
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	91241
	.byte	15,12,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,23
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	91685
	.byte	15,12,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,23
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	91832
	.byte	15,12,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,23
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	91974
	.byte	15,12,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,23
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	92202
	.byte	15,12,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,23
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	92430
	.byte	15,12,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,23
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	92517
	.byte	15,12,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,23
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	92665
	.byte	15,12,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,23
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	93146
	.byte	15,12,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,23
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	93238
	.byte	15,12,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,23
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	93358
	.byte	15,12,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,23
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	93474
	.byte	15,12,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,23
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	94088
	.byte	23
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17829
	.byte	15,12,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,23
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	94293
	.byte	15,12,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,23
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	94855
	.byte	15,12,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,23
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	94957
	.byte	15,12,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,23
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	95070
	.byte	15,12,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,23
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	95179
	.byte	15,12,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,23
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	95274
	.byte	15,12,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,23
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	95484
	.byte	15,12,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,23
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	95609
	.byte	15,12,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,23
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	95776
	.byte	23
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18146
	.byte	23
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18237
	.byte	23
	.byte	'IfxStm_Index',0,19,92,3
	.word	22936
	.byte	15,15,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,23
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	96451
	.byte	15,15,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,23
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	96529
	.byte	15,15,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,23
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	96638
	.byte	15,15,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,23
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	97596
	.byte	15,15,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,23
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	98616
	.byte	15,15,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,23
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	98702
	.byte	23
	.byte	'IfxStdIf_InterfaceDriver',0,32,118,15
	.word	389
	.byte	3
	.word	18121
	.byte	35
	.word	650
	.byte	1,1,22
	.word	389
	.byte	22
	.word	389
	.byte	22
	.word	98848
	.byte	22
	.word	22094
	.byte	0,3
	.word	98853
	.byte	23
	.byte	'IfxStdIf_DPipe_Write',0,33,92,19
	.word	98881
	.byte	23
	.byte	'IfxStdIf_DPipe_Read',0,33,107,19
	.word	98881
	.byte	35
	.word	18134
	.byte	1,1,22
	.word	389
	.byte	0,3
	.word	98943
	.byte	23
	.byte	'IfxStdIf_DPipe_GetReadCount',0,33,115,18
	.word	98956
	.byte	14
	.word	650
	.byte	3
	.word	98997
	.byte	35
	.word	99002
	.byte	1,1,22
	.word	389
	.byte	0,3
	.word	99007
	.byte	23
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,33,123,36
	.word	99020
	.byte	23
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,33,147,1,18
	.word	98956
	.byte	3
	.word	99007
	.byte	23
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,33,155,1,37
	.word	99099
	.byte	35
	.word	650
	.byte	1,1,22
	.word	389
	.byte	22
	.word	18121
	.byte	22
	.word	22094
	.byte	0,3
	.word	99142
	.byte	23
	.byte	'IfxStdIf_DPipe_CanReadCount',0,33,166,1,19
	.word	99165
	.byte	23
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,33,177,1,19
	.word	99165
	.byte	35
	.word	650
	.byte	1,1,22
	.word	389
	.byte	22
	.word	22094
	.byte	0,3
	.word	99245
	.byte	23
	.byte	'IfxStdIf_DPipe_FlushTx',0,33,186,1,19
	.word	99263
	.byte	36,1,1,22
	.word	389
	.byte	0,3
	.word	99300
	.byte	23
	.byte	'IfxStdIf_DPipe_ClearTx',0,33,200,1,16
	.word	99309
	.byte	23
	.byte	'IfxStdIf_DPipe_ClearRx',0,33,193,1,16
	.word	99309
	.byte	23
	.byte	'IfxStdIf_DPipe_OnReceive',0,33,208,1,16
	.word	99309
	.byte	23
	.byte	'IfxStdIf_DPipe_OnTransmit',0,33,215,1,16
	.word	99309
	.byte	23
	.byte	'IfxStdIf_DPipe_OnError',0,33,222,1,16
	.word	99309
	.byte	35
	.word	1866
	.byte	1,1,22
	.word	389
	.byte	0,3
	.word	99479
	.byte	23
	.byte	'IfxStdIf_DPipe_GetSendCount',0,33,131,1,18
	.word	99492
	.byte	35
	.word	22094
	.byte	1,1,22
	.word	389
	.byte	0,3
	.word	99534
	.byte	23
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,33,139,1,24
	.word	99547
	.byte	23
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,33,229,1,16
	.word	99309
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,33,233,1,8,76,13
	.byte	'driver',0
	.word	98815
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	650
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	98886
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	98915
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	98961
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	99025
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	99061
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	99104
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	99170
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	99207
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	99268
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	99314
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	99346
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	99378
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	99412
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	99447
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	99497
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	99552
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	99591
	.byte	4,2,35,72,0,23
	.byte	'IfxStdIf_DPipe',0,33,71,32
	.word	99630
	.byte	3
	.word	383
	.byte	3
	.word	98853
	.byte	3
	.word	98853
	.byte	3
	.word	98943
	.byte	3
	.word	99007
	.byte	3
	.word	98943
	.byte	3
	.word	99007
	.byte	3
	.word	99142
	.byte	3
	.word	99142
	.byte	3
	.word	99245
	.byte	3
	.word	99300
	.byte	3
	.word	99300
	.byte	3
	.word	99300
	.byte	3
	.word	99300
	.byte	3
	.word	99300
	.byte	3
	.word	99479
	.byte	3
	.word	99534
	.byte	3
	.word	99300
	.byte	14
	.word	650
	.byte	3
	.word	100143
	.byte	23
	.byte	'IfxStdIf_DPipe_WriteEvent',0,33,73,32
	.word	100148
	.byte	23
	.byte	'IfxStdIf_DPipe_ReadEvent',0,33,74,32
	.word	100148
	.byte	20,34,252,1,9,1,11
	.byte	'parityError',0,1
	.word	650
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	650
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	650
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	650
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	650
	.byte	1,3,2,35,0,0,23
	.byte	'IfxAsclin_Asc_ErrorFlags',0,34,131,2,3
	.word	100220
	.byte	20,34,137,2,9,8,13
	.byte	'baudrate',0
	.word	271
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	667
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	92665
	.byte	1,2,35,6,0,23
	.byte	'IfxAsclin_Asc_BaudRate',0,34,142,2,3
	.word	100385
	.byte	20,34,146,2,9,2,13
	.byte	'medianFilter',0
	.word	94855
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	94293
	.byte	1,2,35,1,0,23
	.byte	'IfxAsclin_Asc_BitTimingControl',0,34,150,2,3
	.word	100483
	.byte	20,34,154,2,9,6,13
	.byte	'inWidth',0
	.word	95609
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	94088
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	95776
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	93474
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	93238
	.byte	1,2,35,4,0,23
	.byte	'IfxAsclin_Asc_FifoControl',0,34,161,2,3
	.word	100581
	.byte	20,34,165,2,9,8,13
	.byte	'idleDelay',0
	.word	91974
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	95274
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	91685
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	94957
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	93146
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	91241
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	650
	.byte	1,2,35,6,0,23
	.byte	'IfxAsclin_Asc_FrameControl',0,34,174,2,3
	.word	100736
	.byte	20,34,178,2,9,8,13
	.byte	'txPriority',0
	.word	667
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	667
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	667
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	90064
	.byte	1,2,35,6,0,23
	.byte	'IfxAsclin_Asc_InterruptConfig',0,34,184,2,3
	.word	100911
	.byte	34
	.word	90142
	.byte	3
	.word	101040
	.byte	34
	.word	90223
	.byte	3
	.word	101050
	.byte	34
	.word	90303
	.byte	3
	.word	101060
	.byte	34
	.word	90551
	.byte	3
	.word	101070
	.byte	20,34,188,2,9,32,13
	.byte	'cts',0
	.word	101045
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9855
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	101055
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9855
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	101065
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	10060
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	101075
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	10060
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	88859
	.byte	1,2,35,29,0,23
	.byte	'IfxAsclin_Asc_Pins',0,34,199,2,3
	.word	101080
	.byte	12,34,205,2,9,1,13
	.byte	'ALL',0
	.word	650
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	100220
	.byte	1,2,35,0,0,23
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,34,209,2,3
	.word	101250
	.byte	23
	.byte	'uart_tx_pin_enum',0,20,74,2
	.word	23619
	.byte	23
	.byte	'uart_rx_pin_enum',0,20,100,2
	.word	24071
	.byte	23
	.byte	'uart_index_enum',0,20,109,2
	.word	23535
	.byte	23
	.byte	'uart_receiver_struct',0,22,66,2
	.word	24572
.L88:
	.byte	18,25
	.word	650
	.byte	19,24,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,3,8,73
	.byte	19,54,15,39,12,63,12,60,12,0,0,22,5,0,73,19,0,0,23,22,0,3,8,58,15,59,15,57,15,73,19,0,0,24,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,46,1,49,19,0,0,26,5,0,49,19,0,0,27,29,1,49,19,0,0,28,11
	.byte	0,49,19,0,0,29,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,30,46,1,3,8,58,15,59,15,57
	.byte	15,73,19,54,15,39,12,63,12,60,12,0,0,31,11,1,49,19,0,0,32,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12
	.byte	0,0,33,21,0,54,15,0,0,34,38,0,73,19,0,0,35,21,1,73,19,54,15,39,12,0,0,36,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L26:
	.word	.L100-.L99
.L99:
	.half	3
	.word	.L102-.L101
.L101:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_type.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxStm_cfg.h',0
	.byte	0,0,0
	.byte	'zf_driver_uart.h',0,2,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_uart_receiver.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,4,0,0
	.byte	'ifx_types.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,5,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,6,0,0,0
.L102:
.L100:
	.sdecl	'.debug_info',debug,cluster('uart_receiver_init')
	.sect	'.debug_info'
.L27:
	.word	266
	.half	3
	.word	.L28
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L30,.L29
	.byte	2
	.word	.L23
	.byte	3
	.byte	'uart_receiver_init',0,1,128,1,6,1,1,1
	.word	.L22,.L51,.L21
	.byte	4
	.word	.L22,.L51
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver_init')
	.sect	'.debug_abbrev'
.L28:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_receiver_init')
	.sect	'.debug_line'
.L29:
	.word	.L104-.L103
.L103:
	.half	3
	.word	.L106-.L105
.L105:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0,0,0,0,0
.L106:
	.byte	5,20,7,0,5,2
	.word	.L22
	.byte	3,129,1,1,5,46,9
	.half	.L107-.L22
	.byte	1,5,66,9
	.half	.L108-.L107
	.byte	1,5,88,9
	.half	.L109-.L108
	.byte	1,5,23,9
	.half	.L110-.L109
	.byte	3,3,1,5,38,9
	.half	.L111-.L110
	.byte	1,5,1,9
	.half	.L112-.L111
	.byte	3,1,1,7,9
	.half	.L31-.L112
	.byte	0,1,1
.L104:
	.sdecl	'.debug_ranges',debug,cluster('uart_receiver_init')
	.sect	'.debug_ranges'
.L30:
	.word	-1,.L22,0,.L31-.L22,0,0
	.sdecl	'.debug_info',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_info'
.L32:
	.word	589
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L35,.L34
	.byte	2
	.word	.L23
	.byte	3
	.byte	'uart_receiver_interval_time',0,1,61,15
	.word	.L52
	.byte	1,1
	.word	.L16,.L53,.L15
	.byte	4
	.word	.L16,.L53
	.byte	5
	.byte	'time_last',0,1,63,19
	.word	.L52
	.byte	5,3
	.word	_999001_time_last
	.byte	6
	.byte	'time',0,1,64,12
	.word	.L52,.L54
	.byte	6
	.byte	'interval_time',0,1,64,18
	.word	.L52,.L55
	.byte	6
	.byte	'stm_clk',0,1,65,12
	.word	.L52,.L56
	.byte	7
	.word	.L57,.L16,.L2
	.byte	8
	.word	.L58,.L16,.L2
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L59,.L60
	.byte	0,0,7
	.word	.L61,.L62,.L63
	.byte	9
	.word	.L64,.L65
	.byte	8
	.word	.L66,.L62,.L63
	.byte	8
	.word	.L67,.L3,.L5
	.byte	6
	.byte	'result',0,3,182,4,13
	.word	.L68,.L69
	.byte	7
	.word	.L70,.L3,.L4
	.byte	10
	.word	.L71,.L3,.L4
	.byte	0,0,0,0,7
	.word	.L57,.L63,.L6
	.byte	8
	.word	.L58,.L63,.L6
	.byte	6
	.byte	'reg',0,2,135,6,21
	.word	.L59,.L72
	.byte	0,0,7
	.word	.L73,.L74,.L7
	.byte	9
	.word	.L75,.L76
	.byte	10
	.word	.L77,.L74,.L7
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,6,52,0
	.byte	3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16
	.byte	2,6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_line'
.L34:
	.word	.L114-.L113
.L113:
	.half	3
	.word	.L116-.L115
.L115:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L116:
	.byte	4,2,5,19,7,0,5,2
	.word	.L16
	.byte	3,135,6,1,5,28,9
	.half	.L89-.L16
	.byte	3,1,1,5,5,9
	.half	.L90-.L89
	.byte	1,4,1,5,53,9
	.half	.L2-.L90
	.byte	3,186,122,1,4,3,5,5,9
	.half	.L62-.L2
	.byte	3,242,3,1,4,4,5,40,7,9
	.half	.L3-.L62
	.byte	3,175,4,1,5,58,9
	.half	.L117-.L3
	.byte	1,5,43,9
	.half	.L118-.L117
	.byte	1,5,5,9
	.half	.L119-.L118
	.byte	1,4,3,9
	.half	.L4-.L119
	.byte	3,214,123,1,4,1,5,34,9
	.half	.L5-.L4
	.byte	3,137,124,1,4,2,5,19,9
	.half	.L63-.L5
	.byte	3,197,5,1,5,28,9
	.half	.L91-.L63
	.byte	3,1,1,5,5,9
	.half	.L92-.L91
	.byte	1,4,1,5,46,9
	.half	.L6-.L92
	.byte	3,188,122,1,4,3,5,21,9
	.half	.L74-.L6
	.byte	3,251,3,1,5,5,9
	.half	.L120-.L74
	.byte	1,4,1,5,28,9
	.half	.L7-.L120
	.byte	3,134,124,1,5,26,9
	.half	.L121-.L7
	.byte	1,5,5,9
	.half	.L93-.L121
	.byte	3,1,1,5,15,9
	.half	.L122-.L93
	.byte	1,5,30,9
	.half	.L123-.L122
	.byte	3,1,1,5,54,9
	.half	.L124-.L123
	.byte	1,5,52,9
	.half	.L125-.L124
	.byte	1,5,64,9
	.half	.L126-.L125
	.byte	1,5,62,9
	.half	.L95-.L126
	.byte	1,5,5,9
	.half	.L96-.L95
	.byte	3,2,1,5,1,9
	.half	.L8-.L96
	.byte	3,1,1,7,9
	.half	.L36-.L8
	.byte	0,1,1
.L114:
	.sdecl	'.debug_ranges',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_ranges'
.L35:
	.word	-1,.L16,0,.L36-.L16,0,0
	.sdecl	'.debug_info',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_info'
.L37:
	.word	328
	.half	3
	.word	.L38
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L40,.L39
	.byte	2
	.word	.L23
	.byte	3
	.byte	'uart_receiver_analysis',0,1,85,13,1,1
	.word	.L18,.L78,.L17
	.byte	4
	.byte	'remote_data',0,1,85,59
	.word	.L79,.L80
	.byte	4
	.byte	'buffer',0,1,85,79
	.word	.L81,.L82
	.byte	5
	.word	.L18,.L78
	.byte	6
	.byte	'num',0,1,87,11
	.word	.L83,.L84
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_abbrev'
.L38:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_line'
.L39:
	.word	.L128-.L127
.L127:
	.half	3
	.word	.L130-.L129
.L129:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0,0,0,0,0
.L130:
	.byte	5,15,7,0,5,2
	.word	.L18
	.byte	3,214,0,1,5,25,9
	.half	.L97-.L18
	.byte	3,1,1,5,42,9
	.half	.L131-.L97
	.byte	1,5,53,9
	.half	.L132-.L131
	.byte	1,5,58,9
	.half	.L133-.L132
	.byte	1,5,46,9
	.half	.L134-.L133
	.byte	1,5,65,9
	.half	.L135-.L134
	.byte	1,5,33,9
	.half	.L136-.L135
	.byte	1,5,29,9
	.half	.L137-.L136
	.byte	1,5,25,9
	.half	.L138-.L137
	.byte	3,1,1,5,42,9
	.half	.L139-.L138
	.byte	1,5,46,9
	.half	.L140-.L139
	.byte	1,5,59,9
	.half	.L141-.L140
	.byte	1,5,63,9
	.half	.L142-.L141
	.byte	1,5,51,9
	.half	.L143-.L142
	.byte	1,5,70,9
	.half	.L144-.L143
	.byte	1,5,33,9
	.half	.L145-.L144
	.byte	1,5,29,9
	.half	.L146-.L145
	.byte	1,5,25,9
	.half	.L147-.L146
	.byte	3,1,1,5,42,9
	.half	.L148-.L147
	.byte	1,5,46,9
	.half	.L149-.L148
	.byte	1,5,59,9
	.half	.L150-.L149
	.byte	1,5,63,9
	.half	.L151-.L150
	.byte	1,5,51,9
	.half	.L152-.L151
	.byte	1,5,76,9
	.half	.L153-.L152
	.byte	1,5,80,9
	.half	.L154-.L153
	.byte	1,5,68,9
	.half	.L155-.L154
	.byte	1,5,88,9
	.half	.L156-.L155
	.byte	1,5,33,9
	.half	.L157-.L156
	.byte	1,5,29,9
	.half	.L158-.L157
	.byte	1,5,25,9
	.half	.L159-.L158
	.byte	3,1,1,5,42,9
	.half	.L160-.L159
	.byte	1,5,46,9
	.half	.L161-.L160
	.byte	1,5,59,9
	.half	.L162-.L161
	.byte	1,5,63,9
	.half	.L163-.L162
	.byte	1,5,51,9
	.half	.L164-.L163
	.byte	1,5,70,9
	.half	.L165-.L164
	.byte	1,5,33,9
	.half	.L166-.L165
	.byte	1,5,29,9
	.half	.L167-.L166
	.byte	1,5,25,9
	.half	.L168-.L167
	.byte	3,1,1,5,42,9
	.half	.L169-.L168
	.byte	1,5,46,9
	.half	.L170-.L169
	.byte	1,5,59,9
	.half	.L171-.L170
	.byte	1,5,63,9
	.half	.L172-.L171
	.byte	1,5,51,9
	.half	.L173-.L172
	.byte	1,5,70,9
	.half	.L174-.L173
	.byte	1,5,33,9
	.half	.L175-.L174
	.byte	1,5,29,9
	.half	.L176-.L175
	.byte	1,5,25,9
	.half	.L177-.L176
	.byte	3,1,1,5,42,9
	.half	.L178-.L177
	.byte	1,5,46,9
	.half	.L179-.L178
	.byte	1,5,59,9
	.half	.L98-.L179
	.byte	1,5,63,9
	.half	.L180-.L98
	.byte	1,5,51,9
	.half	.L181-.L180
	.byte	1,5,76,9
	.half	.L182-.L181
	.byte	1,5,80,9
	.half	.L183-.L182
	.byte	1,5,68,9
	.half	.L184-.L183
	.byte	1,5,87,9
	.half	.L185-.L184
	.byte	1,5,33,9
	.half	.L186-.L185
	.byte	1,5,54,9
	.half	.L187-.L186
	.byte	3,1,1,5,26,9
	.half	.L188-.L187
	.byte	1,5,60,7,9
	.half	.L189-.L188
	.byte	1,5,64,9
	.half	.L190-.L189
	.byte	1,5,60,9
	.half	.L9-.L190
	.byte	1,5,24,9
	.half	.L10-.L9
	.byte	1,5,5,9
	.half	.L191-.L10
	.byte	3,1,1,5,33,9
	.half	.L192-.L191
	.byte	1,5,31,9
	.half	.L193-.L192
	.byte	1,5,1,9
	.half	.L194-.L193
	.byte	3,1,1,7,9
	.half	.L41-.L194
	.byte	0,1,1
.L128:
	.sdecl	'.debug_ranges',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_ranges'
.L40:
	.word	-1,.L18,0,.L41-.L18,0,0
	.sdecl	'.debug_info',debug,cluster('uart_receiver_callback')
	.sect	'.debug_info'
.L42:
	.word	291
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L45,.L44
	.byte	2
	.word	.L23
	.byte	3
	.byte	'uart_receiver_callback',0,1,105,6,1,1,1
	.word	.L20,.L85,.L19
	.byte	4
	.word	.L20,.L85
	.byte	5
	.byte	'length',0,1,107,19
	.word	.L86
	.byte	5,3
	.word	_999002_length
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver_callback')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_line',debug,cluster('uart_receiver_callback')
	.sect	'.debug_line'
.L44:
	.word	.L196-.L195
.L195:
	.half	3
	.word	.L198-.L197
.L197:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0,0,0,0,0
.L198:
	.byte	5,36,7,0,5,2
	.word	.L20
	.byte	3,236,0,1,5,41,9
	.half	.L199-.L20
	.byte	1,5,5,9
	.half	.L200-.L199
	.byte	1,5,9,7,9
	.half	.L201-.L200
	.byte	3,2,1,5,18,9
	.half	.L202-.L201
	.byte	1,5,16,9
	.half	.L203-.L202
	.byte	1,5,24,9
	.half	.L11-.L203
	.byte	3,2,1,5,5,9
	.half	.L204-.L11
	.byte	1,5,23,9
	.half	.L205-.L204
	.byte	1,5,51,9
	.half	.L206-.L205
	.byte	1,5,34,9
	.half	.L207-.L206
	.byte	1,5,24,9
	.half	.L208-.L207
	.byte	1,5,30,9
	.half	.L209-.L208
	.byte	1,5,29,9
	.half	.L210-.L209
	.byte	3,1,1,5,12,9
	.half	.L211-.L210
	.byte	1,5,11,9
	.half	.L212-.L211
	.byte	1,5,29,7,9
	.half	.L213-.L212
	.byte	3,1,1,5,47,9
	.half	.L214-.L213
	.byte	1,5,13,9
	.half	.L215-.L214
	.byte	1,5,26,9
	.half	.L216-.L215
	.byte	1,5,29,7,9
	.half	.L217-.L216
	.byte	3,1,1,5,47,9
	.half	.L218-.L217
	.byte	1,5,26,9
	.half	.L219-.L218
	.byte	1,5,33,7,9
	.half	.L220-.L219
	.byte	3,2,1,5,48,9
	.half	.L221-.L220
	.byte	1,5,1,9
	.half	.L12-.L221
	.byte	3,2,1,7,9
	.half	.L46-.L12
	.byte	0,1,1
.L196:
	.sdecl	'.debug_ranges',debug,cluster('uart_receiver_callback')
	.sect	'.debug_ranges'
.L45:
	.word	-1,.L20,0,.L46-.L20,0,0
	.sdecl	'.debug_info',debug,cluster('uart_receiver')
	.sect	'.debug_info'
.L47:
	.word	238
	.half	3
	.word	.L48
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'uart_receiver',0,23,50,25
	.word	.L87
	.byte	1,5,3
	.word	uart_receiver
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver')
	.sect	'.debug_abbrev'
.L48:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('uart_receiver_data')
	.sect	'.debug_info'
.L49:
	.word	243
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_uart_receiver.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L23
	.byte	3
	.byte	'uart_receiver_data',0,23,52,9
	.word	.L88
	.byte	1,5,3
	.word	uart_receiver_data
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('uart_receiver_data')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_loc',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L18,0,.L78-.L18
	.half	1
	.byte	101
	.word	0,0
.L84:
	.word	-1,.L18,.L97-.L18,.L98-.L18
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L80:
	.word	-1,.L18,0,.L78-.L18
	.half	1
	.byte	100
	.word	0,0
.L17:
	.word	-1,.L18,0,.L78-.L18
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_receiver_callback')
	.sect	'.debug_loc'
.L19:
	.word	-1,.L20,0,.L85-.L20
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_receiver_init')
	.sect	'.debug_loc'
.L21:
	.word	-1,.L22,0,.L51-.L22
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_loc'
.L55:
	.word	-1,.L16,.L93-.L16,.L94-.L16
	.half	1
	.byte	84
	.word	0,0
.L72:
	.word	-1,.L16,.L91-.L16,.L92-.L16
	.half	1
	.byte	95
	.word	0,0
.L60:
	.word	-1,.L16,.L89-.L16,.L90-.L16
	.half	1
	.byte	95
	.word	0,0
.L69:
	.word	0,0
.L65:
	.word	0,0
.L76:
	.word	0,0
.L56:
	.word	-1,.L16,.L63-.L16,.L53-.L16
	.half	1
	.byte	88
	.word	.L95-.L16,.L96-.L16
	.half	1
	.byte	86
	.word	0,0
.L54:
	.word	0,0
.L15:
	.word	-1,.L16,0,.L53-.L16
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L222:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('uart_receiver_interval_time')
	.sect	'.debug_frame'
	.word	12
	.word	.L222,.L16,.L53-.L16
	.sdecl	'.debug_frame',debug,cluster('uart_receiver_analysis')
	.sect	'.debug_frame'
	.word	20
	.word	.L222,.L18,.L78-.L18
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('uart_receiver_callback')
	.sect	'.debug_frame'
	.word	12
	.word	.L222,.L20,.L85-.L20
	.sdecl	'.debug_frame',debug,cluster('uart_receiver_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L222,.L22,.L51-.L22
	; Module end
