	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44992a --dep-file=IfxMtu_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxMtu_cfg.IfxMtu_sramTable',data,rom,cluster('IfxMtu_sramTable')
	.sect	'.rodata.IfxMtu_cfg.IfxMtu_sramTable'
	.global	IfxMtu_sramTable
	.align	4
IfxMtu_sramTable:	.type	object
	.size	IfxMtu_sramTable,1056
	.space	72
	.byte	8
	.space	1
	.half	16
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	8192
	.space	12
	.byte	2
	.space	1
	.half	20
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	128
	.byte	2
	.space	1
	.half	64
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	3072
	.space	12
	.byte	2
	.space	1
	.half	20
	.byte	5
	.space	1
	.byte	1
	.space	1
	.word	256
	.space	24
	.byte	8
	.space	1
	.half	16
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	8192
	.space	12
	.byte	2
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	3072
	.byte	2
	.space	1
	.half	20
	.byte	5
	.space	1
	.byte	1
	.space	1
	.word	256
	.space	48
	.byte	2
	.space	1
	.half	35
	.byte	7
	.space	1
	.byte	1
	.space	1
	.word	1024
	.space	36
	.byte	1
	.space	1
	.half	64
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	4096
	.space	12
	.byte	1
	.space	1
	.half	29
	.byte	7
	.space	1
	.byte	1
	.space	1
	.word	1024
	.byte	3
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	1024
	.byte	3
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	512
	.byte	1
	.space	1
	.half	24
	.byte	7
	.space	1
	.byte	1
	.space	1
	.word	128
	.byte	1
	.space	1
	.half	24
	.byte	7
	.space	1
	.byte	1
	.space	1
	.word	384
	.byte	1
	.space	1
	.half	24
	.byte	7
	.space	1
	.byte	1
	.space	1
	.word	1024
	.byte	1
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	192
	.space	12
	.byte	1
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	2496
	.space	12
	.byte	2
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	64
	.byte	4
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	128
	.byte	1
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	4096
	.space	36
	.byte	2
	.space	1
	.half	8
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	40960
	.byte	4
	.space	1
	.half	32
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	1024
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	2048
	.space	288
	.byte	4
	.space	1
	.half	8
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	5120
	.space	12
	.byte	2
	.space	1
	.half	8
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	384
	.byte	1
	.space	1
	.half	36
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	512
	.byte	1
	.space	1
	.half	8
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	20480
	.byte	4
	.space	1
	.half	64
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	256
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	256
	.byte	2
	.space	1
	.half	128
	.byte	9
	.space	1
	.byte	1
	.space	1
	.word	256
	.byte	4
	.space	1
	.half	64
	.byte	8
	.space	1
	.byte	1
	.space	1
	.word	1024
	.byte	1
	.space	1
	.half	16
	.byte	6
	.space	1
	.byte	1
	.space	1
	.word	512
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	84736
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	404
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	378
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	410
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	410
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	378
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	519
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	519
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	535
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	710
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	954
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	631
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	914
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1145
	.byte	4,2,35,8,0,14
	.word	1185
	.byte	3
	.word	1248
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1253
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	688
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1253
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	688
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	688
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1253
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1483
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1799
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2370
	.byte	4,2,35,0,0,15,4
	.word	671
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	671
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	671
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	671
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	671
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2713
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	671
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	671
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2928
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	671
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	671
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3145
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,15,24
	.word	671
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	671
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	671
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	671
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	671
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3688
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	671
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	671
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	671
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	671
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	671
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3992
	.byte	4,2,35,0,0,15,8
	.word	671
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4317
	.byte	4,2,35,0,0,15,12
	.word	671
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4657
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	496
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5309
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5456
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	496
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5625
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	688
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5797
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	688
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	688
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5972
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6146
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6320
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6652
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	688
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7333
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7457
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7541
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7721
	.byte	4,2,35,0,0,15,76
	.word	671
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7974
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	671
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8061
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1759
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2330
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2449
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2489
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2673
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2888
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3105
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3325
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2489
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3639
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3679
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3952
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4268
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4308
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4608
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4648
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4983
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5269
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4308
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5416
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5585
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5757
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5932
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6106
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6280
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6456
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6612
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6945
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7293
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4308
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7417
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7666
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7925
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7965
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8021
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8588
	.byte	4,3,35,252,1,0,14
	.word	8628
	.byte	3
	.word	9231
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9236
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	671
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9241
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9422
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	671
	.byte	1,1,6,0,7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9577
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	688
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	671
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	688
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9577
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9577
	.byte	19,6,0,0,20
	.word	240
	.byte	21
	.word	266
	.byte	6,0,20
	.word	301
	.byte	21
	.word	333
	.byte	6,0,20
	.word	346
	.byte	6,0,20
	.word	415
	.byte	21
	.word	434
	.byte	6,0,20
	.word	450
	.byte	21
	.word	465
	.byte	21
	.word	479
	.byte	6,0,20
	.word	1258
	.byte	21
	.word	1298
	.byte	21
	.word	1316
	.byte	6,0,20
	.word	1336
	.byte	21
	.word	1374
	.byte	21
	.word	1392
	.byte	6,0,20
	.word	1412
	.byte	21
	.word	1463
	.byte	6,0,20
	.word	9344
	.byte	21
	.word	9372
	.byte	21
	.word	9386
	.byte	21
	.word	9404
	.byte	6,0,20
	.word	9501
	.byte	6,0,20
	.word	9535
	.byte	6,0,20
	.word	9598
	.byte	21
	.word	9639
	.byte	6,0,20
	.word	9658
	.byte	21
	.word	9713
	.byte	6,0,20
	.word	9732
	.byte	21
	.word	9772
	.byte	21
	.word	9789
	.byte	19,6,0,0,7
	.byte	'short int',0,2,5,22
	.byte	'__wchar_t',0,9,1,1
	.word	9993
	.byte	22
	.byte	'__size_t',0,9,1,1
	.word	496
	.byte	22
	.byte	'__ptrdiff_t',0,9,1,1
	.word	512
	.byte	23,1,3
	.word	10061
	.byte	22
	.byte	'__codeptr',0,9,1,1
	.word	10063
	.byte	22
	.byte	'boolean',0,10,101,29
	.word	671
	.byte	22
	.byte	'uint8',0,10,105,29
	.word	671
	.byte	22
	.byte	'uint16',0,10,109,29
	.word	688
	.byte	22
	.byte	'uint32',0,10,113,29
	.word	9577
	.byte	22
	.byte	'uint64',0,10,118,29
	.word	378
	.byte	22
	.byte	'sint16',0,10,126,29
	.word	9993
	.byte	7
	.byte	'long int',0,4,5,22
	.byte	'sint32',0,10,131,1,29
	.word	10176
	.byte	7
	.byte	'long long int',0,8,5,22
	.byte	'sint64',0,10,138,1,29
	.word	10204
	.byte	22
	.byte	'float32',0,10,167,1,29
	.word	292
	.byte	22
	.byte	'pvoid',0,11,57,28
	.word	410
	.byte	22
	.byte	'Ifx_TickTime',0,11,79,28
	.word	10204
	.byte	17,11,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,22
	.byte	'Ifx_RxSel',0,11,140,1,3
	.word	10289
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,12,45,16,4,11
	.byte	'ADDR',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_A_Bits',0,12,48,3
	.word	10427
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,12,51,16,4,11
	.byte	'VSS',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	519
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BIV_Bits',0,12,55,3
	.word	10488
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,12,58,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	519
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_BTV_Bits',0,12,62,3
	.word	10567
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,12,65,16,4,11
	.byte	'CountValue',0,4
	.word	519
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT_Bits',0,12,69,3
	.word	10653
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,12,72,16,4,11
	.byte	'CM',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	519
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	519
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	519
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	519
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL_Bits',0,12,80,3
	.word	10742
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,12,83,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	519
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT_Bits',0,12,89,3
	.word	10888
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,12,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID_Bits',0,12,96,3
	.word	11015
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,12,99,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L_Bits',0,12,103,3
	.word	11113
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,12,106,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U_Bits',0,12,110,3
	.word	11206
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,12,113,16,4,11
	.byte	'MODREV',0,4
	.word	519
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	519
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID_Bits',0,12,118,3
	.word	11299
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,12,121,16,4,11
	.byte	'XE',0,4
	.word	519
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE_Bits',0,12,125,3
	.word	11406
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,12,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	519
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT_Bits',0,12,136,1,3
	.word	11493
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,12,139,1,16,4,11
	.byte	'CID',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID_Bits',0,12,143,1,3
	.word	11647
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,12,146,1,16,4,11
	.byte	'DATA',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_D_Bits',0,12,149,1,3
	.word	11741
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,12,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	519
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	519
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	519
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	519
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	519
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	519
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DATR_Bits',0,12,163,1,3
	.word	11804
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,12,166,1,16,4,11
	.byte	'DE',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	519
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	519
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	519
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	519
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	519
	.byte	19,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR_Bits',0,12,177,1,3
	.word	12022
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,12,180,1,16,4,11
	.byte	'DTA',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	519
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR_Bits',0,12,184,1,3
	.word	12237
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,12,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	519
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0_Bits',0,12,192,1,3
	.word	12331
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,12,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2_Bits',0,12,199,1,3
	.word	12447
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,12,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	519
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_CPU_DCX_Bits',0,12,206,1,3
	.word	12548
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,12,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD_Bits',0,12,212,1,3
	.word	12641
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,12,215,1,16,4,11
	.byte	'TA',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR_Bits',0,12,218,1,3
	.word	12721
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,12,221,1,16,4,11
	.byte	'IED',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	519
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	519
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	519
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	519
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	519
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR_Bits',0,12,233,1,3
	.word	12790
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,12,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	519
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_DMS_Bits',0,12,240,1,3
	.word	13019
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,12,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L_Bits',0,12,247,1,3
	.word	13112
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,12,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	519
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U_Bits',0,12,254,1,3
	.word	13207
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,12,129,2,16,4,11
	.byte	'RE',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE_Bits',0,12,133,2,3
	.word	13302
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,12,136,2,16,4,11
	.byte	'WE',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE_Bits',0,12,140,2,3
	.word	13392
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,12,143,2,16,4,11
	.byte	'SRE',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	519
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	519
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	519
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	519
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	519
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	519
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	519
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	519
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	519
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	519
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	519
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR_Bits',0,12,161,2,3
	.word	13482
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,12,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	519
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT_Bits',0,12,172,2,3
	.word	13806
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,12,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	519
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	519
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FCX_Bits',0,12,180,2,3
	.word	13960
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,12,183,2,16,4,11
	.byte	'TST',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	519
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	519
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	519
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	519
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	519
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	519
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	519
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	519
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	519
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,12,202,2,3
	.word	14066
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,12,205,2,16,4,11
	.byte	'OPC',0,4
	.word	519
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	519
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	519
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	519
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	519
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,12,212,2,3
	.word	14415
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,12,215,2,16,4,11
	.byte	'PC',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,12,218,2,3
	.word	14575
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,12,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,12,224,2,3
	.word	14656
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,12,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,12,230,2,3
	.word	14743
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,12,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,12,236,2,3
	.word	14830
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,12,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	519
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT_Bits',0,12,243,2,3
	.word	14917
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,12,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	519
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	519
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	519
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	519
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	519
	.byte	6,0,2,35,0,0,22
	.byte	'Ifx_CPU_ICR_Bits',0,12,253,2,3
	.word	15008
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,12,128,3,16,4,11
	.byte	'ISP',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_ISP_Bits',0,12,131,3,3
	.word	15151
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,12,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	519
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	519
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_CPU_LCX_Bits',0,12,139,3,3
	.word	15217
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,12,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	519
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT_Bits',0,12,146,3,3
	.word	15323
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,12,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	519
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT_Bits',0,12,153,3,3
	.word	15416
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,12,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	519
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT_Bits',0,12,160,3,3
	.word	15509
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,12,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	519
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_CPU_PC_Bits',0,12,167,3,3
	.word	15602
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,12,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	519
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0_Bits',0,12,175,3,3
	.word	15687
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,12,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	519
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1_Bits',0,12,183,3,3
	.word	15803
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,12,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2_Bits',0,12,190,3,3
	.word	15914
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,12,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	519
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	519
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	519
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	519
	.byte	10,0,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI_Bits',0,12,200,3,3
	.word	16015
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,12,203,3,16,4,11
	.byte	'TA',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR_Bits',0,12,206,3,3
	.word	16145
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,12,209,3,16,4,11
	.byte	'IED',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	519
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	519
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	519
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	519
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	519
	.byte	18,0,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR_Bits',0,12,221,3,3
	.word	16214
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,12,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	519
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0_Bits',0,12,229,3,3
	.word	16443
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,12,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	519
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	519
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1_Bits',0,12,237,3,3
	.word	16556
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,12,240,3,16,4,11
	.byte	'PSI',0,4
	.word	519
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2_Bits',0,12,244,3,3
	.word	16669
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,12,247,3,16,4,11
	.byte	'FRE',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	519
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	519
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	519
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	519
	.byte	17,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR_Bits',0,12,129,4,3
	.word	16760
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,12,132,4,16,4,11
	.byte	'CDC',0,4
	.word	519
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	519
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	519
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	519
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	519
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	519
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	519
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	519
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_PSW_Bits',0,12,147,4,3
	.word	16963
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,12,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	519
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	519
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	519
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	519
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN_Bits',0,12,156,4,3
	.word	17206
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,12,159,4,16,4,11
	.byte	'PC',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	519
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	519
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	519
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	519
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	519
	.byte	7,0,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON_Bits',0,12,171,4,3
	.word	17334
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,12,174,4,16,4,11
	.byte	'EN',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,12,177,4,3
	.word	17575
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,12,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,12,183,4,3
	.word	17658
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,12,186,4,16,4,11
	.byte	'EN',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,12,189,4,3
	.word	17749
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,12,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,12,195,4,3
	.word	17840
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,12,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,12,202,4,3
	.word	17939
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,12,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,12,209,4,3
	.word	18046
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,12,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	519
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT_Bits',0,12,220,4,3
	.word	18153
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,12,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	519
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON_Bits',0,12,231,4,3
	.word	18307
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,12,234,4,16,4,11
	.byte	'ASI',0,4
	.word	519
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	519
	.byte	27,0,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,12,238,4,3
	.word	18468
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,12,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	519
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	519
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	519
	.byte	15,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON_Bits',0,12,249,4,3
	.word	18566
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,12,252,4,16,4,11
	.byte	'Timer',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,12,255,4,3
	.word	18738
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,12,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	519
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR_Bits',0,12,133,5,3
	.word	18818
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,12,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	519
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	519
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	519
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	519
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	519
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	519
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	519
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	519
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	519
	.byte	3,0,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT_Bits',0,12,153,5,3
	.word	18891
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,12,156,5,16,4,11
	.byte	'T0',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	519
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	519
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	519
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	519
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	519
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	519
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	519
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,12,167,5,3
	.word	19209
	.byte	12,12,175,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10427
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_A',0,12,180,5,3
	.word	19404
	.byte	12,12,183,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10488
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BIV',0,12,188,5,3
	.word	19463
	.byte	12,12,191,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10567
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_BTV',0,12,196,5,3
	.word	19524
	.byte	12,12,199,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10653
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCNT',0,12,204,5,3
	.word	19585
	.byte	12,12,207,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10742
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CCTRL',0,12,212,5,3
	.word	19647
	.byte	12,12,215,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10888
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_COMPAT',0,12,220,5,3
	.word	19710
	.byte	12,12,223,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11015
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CORE_ID',0,12,228,5,3
	.word	19774
	.byte	12,12,231,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11113
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_L',0,12,236,5,3
	.word	19839
	.byte	12,12,239,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11206
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPR_U',0,12,244,5,3
	.word	19902
	.byte	12,12,247,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11299
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPU_ID',0,12,252,5,3
	.word	19965
	.byte	12,12,255,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11406
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CPXE',0,12,132,6,3
	.word	20029
	.byte	12,12,135,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11493
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CREVT',0,12,140,6,3
	.word	20091
	.byte	12,12,143,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11647
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_CUS_ID',0,12,148,6,3
	.word	20154
	.byte	12,12,151,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11741
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_D',0,12,156,6,3
	.word	20218
	.byte	12,12,159,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11804
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DATR',0,12,164,6,3
	.word	20277
	.byte	12,12,167,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12022
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGSR',0,12,172,6,3
	.word	20339
	.byte	12,12,175,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12237
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DBGTCR',0,12,180,6,3
	.word	20402
	.byte	12,12,183,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12331
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON0',0,12,188,6,3
	.word	20466
	.byte	12,12,191,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12447
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCON2',0,12,196,6,3
	.word	20529
	.byte	12,12,199,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12548
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DCX',0,12,204,6,3
	.word	20592
	.byte	12,12,207,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12641
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DEADD',0,12,212,6,3
	.word	20653
	.byte	12,12,215,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12721
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIEAR',0,12,220,6,3
	.word	20716
	.byte	12,12,223,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12790
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DIETR',0,12,228,6,3
	.word	20779
	.byte	12,12,231,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13019
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DMS',0,12,236,6,3
	.word	20842
	.byte	12,12,239,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13112
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_L',0,12,244,6,3
	.word	20903
	.byte	12,12,247,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13207
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPR_U',0,12,252,6,3
	.word	20966
	.byte	12,12,255,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13302
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPRE',0,12,132,7,3
	.word	21029
	.byte	12,12,135,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13392
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DPWE',0,12,140,7,3
	.word	21091
	.byte	12,12,143,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13482
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_DSTR',0,12,148,7,3
	.word	21153
	.byte	12,12,151,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13806
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_EXEVT',0,12,156,7,3
	.word	21215
	.byte	12,12,159,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13960
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FCX',0,12,164,7,3
	.word	21278
	.byte	12,12,167,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14066
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,12,172,7,3
	.word	21339
	.byte	12,12,175,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14415
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,12,180,7,3
	.word	21409
	.byte	12,12,183,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14575
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,12,188,7,3
	.word	21479
	.byte	12,12,191,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14656
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,12,196,7,3
	.word	21548
	.byte	12,12,199,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14743
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,12,204,7,3
	.word	21619
	.byte	12,12,207,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14830
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,12,212,7,3
	.word	21690
	.byte	12,12,215,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14917
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICNT',0,12,220,7,3
	.word	21761
	.byte	12,12,223,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15008
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ICR',0,12,228,7,3
	.word	21823
	.byte	12,12,231,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15151
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_ISP',0,12,236,7,3
	.word	21884
	.byte	12,12,239,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15217
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_LCX',0,12,244,7,3
	.word	21945
	.byte	12,12,247,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15323
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M1CNT',0,12,252,7,3
	.word	22006
	.byte	12,12,255,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15416
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M2CNT',0,12,132,8,3
	.word	22069
	.byte	12,12,135,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15509
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_M3CNT',0,12,140,8,3
	.word	22132
	.byte	12,12,143,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15602
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PC',0,12,148,8,3
	.word	22195
	.byte	12,12,151,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15687
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON0',0,12,156,8,3
	.word	22255
	.byte	12,12,159,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15803
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON1',0,12,164,8,3
	.word	22318
	.byte	12,12,167,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15914
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCON2',0,12,172,8,3
	.word	22381
	.byte	12,12,175,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16015
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PCXI',0,12,180,8,3
	.word	22444
	.byte	12,12,183,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16145
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIEAR',0,12,188,8,3
	.word	22506
	.byte	12,12,191,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16214
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PIETR',0,12,196,8,3
	.word	22569
	.byte	12,12,199,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16443
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA0',0,12,204,8,3
	.word	22632
	.byte	12,12,207,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16556
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA1',0,12,212,8,3
	.word	22694
	.byte	12,12,215,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16669
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PMA2',0,12,220,8,3
	.word	22756
	.byte	12,12,223,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16760
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSTR',0,12,228,8,3
	.word	22818
	.byte	12,12,231,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16963
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_PSW',0,12,236,8,3
	.word	22880
	.byte	12,12,239,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17206
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SEGEN',0,12,244,8,3
	.word	22941
	.byte	12,12,247,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17334
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SMACON',0,12,252,8,3
	.word	23004
	.byte	12,12,255,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17575
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENA',0,12,132,9,3
	.word	23068
	.byte	12,12,135,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17658
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_ACCENB',0,12,140,9,3
	.word	23138
	.byte	12,12,143,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17749
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,12,148,9,3
	.word	23208
	.byte	12,12,151,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17840
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,12,156,9,3
	.word	23282
	.byte	12,12,159,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17939
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,12,164,9,3
	.word	23356
	.byte	12,12,167,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18046
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,12,172,9,3
	.word	23426
	.byte	12,12,175,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18153
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SWEVT',0,12,180,9,3
	.word	23496
	.byte	12,12,183,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18307
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_SYSCON',0,12,188,9,3
	.word	23559
	.byte	12,12,191,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18468
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TASK_ASI',0,12,196,9,3
	.word	23623
	.byte	12,12,199,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18566
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_CON',0,12,204,9,3
	.word	23689
	.byte	12,12,207,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18738
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TPS_TIMER',0,12,212,9,3
	.word	23754
	.byte	12,12,215,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18818
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_ADR',0,12,220,9,3
	.word	23821
	.byte	12,12,223,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18891
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TR_EVT',0,12,228,9,3
	.word	23885
	.byte	12,12,231,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19209
	.byte	4,2,35,0,0,22
	.byte	'Ifx_CPU_TRIG_ACC',0,12,236,9,3
	.word	23949
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,12,247,9,25,8,13
	.byte	'L',0
	.word	19839
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	19902
	.byte	4,2,35,4,0,14
	.word	24015
	.byte	22
	.byte	'Ifx_CPU_CPR',0,12,251,9,3
	.word	24057
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,12,254,9,25,8,13
	.byte	'L',0
	.word	20903
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	20966
	.byte	4,2,35,4,0,14
	.word	24083
	.byte	22
	.byte	'Ifx_CPU_DPR',0,12,130,10,3
	.word	24125
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,12,133,10,25,16,13
	.byte	'LA',0
	.word	23356
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	23426
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	23208
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	23282
	.byte	4,2,35,12,0,14
	.word	24151
	.byte	22
	.byte	'Ifx_CPU_SPROT_RGN',0,12,139,10,3
	.word	24233
	.byte	15,12
	.word	23754
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,12,142,10,25,16,13
	.byte	'CON',0
	.word	23689
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	24265
	.byte	12,2,35,4,0,14
	.word	24274
	.byte	22
	.byte	'Ifx_CPU_TPS',0,12,146,10,3
	.word	24322
	.byte	10
	.byte	'_Ifx_CPU_TR',0,12,149,10,25,8,13
	.byte	'EVT',0
	.word	23885
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	23821
	.byte	4,2,35,4,0,14
	.word	24348
	.byte	22
	.byte	'Ifx_CPU_TR',0,12,153,10,3
	.word	24393
	.byte	15,176,32
	.word	671
	.byte	16,175,32,0,15,208,223,1
	.word	671
	.byte	16,207,223,1,0,15,248,1
	.word	671
	.byte	16,247,1,0,15,244,29
	.word	671
	.byte	16,243,29,0,15,188,3
	.word	671
	.byte	16,187,3,0,15,232,3
	.word	671
	.byte	16,231,3,0,15,252,23
	.word	671
	.byte	16,251,23,0,15,228,63
	.word	671
	.byte	16,227,63,0,15,128,1
	.word	24083
	.byte	16,15,0,14
	.word	24508
	.byte	15,128,31
	.word	671
	.byte	16,255,30,0,15,64
	.word	24015
	.byte	16,7,0,14
	.word	24534
	.byte	15,192,31
	.word	671
	.byte	16,191,31,0,15,16
	.word	20029
	.byte	16,3,0,15,16
	.word	21029
	.byte	16,3,0,15,16
	.word	21091
	.byte	16,3,0,15,208,7
	.word	671
	.byte	16,207,7,0,14
	.word	24274
	.byte	15,240,23
	.word	671
	.byte	16,239,23,0,15,64
	.word	24348
	.byte	16,7,0,14
	.word	24613
	.byte	15,192,23
	.word	671
	.byte	16,191,23,0,15,232,1
	.word	671
	.byte	16,231,1,0,15,28
	.word	671
	.byte	16,27,0,15,180,1
	.word	671
	.byte	16,179,1,0,15,16
	.word	671
	.byte	16,15,0,15,172,1
	.word	671
	.byte	16,171,1,0,15,64
	.word	20218
	.byte	16,15,0,15,64
	.word	671
	.byte	16,63,0,15,64
	.word	19404
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,12,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	24418
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	22941
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	24429
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	23623
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	24442
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	22632
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	22694
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	22756
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	24453
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	20529
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4308
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	23004
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	21153
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2489
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	20277
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	20653
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	20716
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	20779
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3679
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	20466
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	24464
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	22818
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	22318
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	22381
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	22255
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	22506
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	22569
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	24475
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	19710
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	24486
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	21339
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	21479
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	21409
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2489
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	21548
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	21619
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	21690
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	24497
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	24518
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	24523
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	24543
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	24548
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	24559
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	24568
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	24577
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	24586
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	24597
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	24602
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	24622
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	24627
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	19647
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	19585
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	21761
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	22006
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	22069
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	22132
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	24638
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	20339
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2489
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	21215
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	20091
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	23496
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	24649
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	23949
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4648
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	20842
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	20592
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	20402
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	24658
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	22444
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	22880
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	22195
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4308
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	23559
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	19965
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	19774
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	19463
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	19524
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	21884
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	21823
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4308
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	21278
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	21945
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	24669
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	20154
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	24678
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	24689
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	24698
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	24707
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	24698
	.byte	64,4,35,192,255,3,0,14
	.word	24716
	.byte	22
	.byte	'Ifx_CPU',0,12,130,11,3
	.word	26507
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,22
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	26529
	.byte	22
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9422
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,13,45,16,4,11
	.byte	'SRPN',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	671
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	671
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	671
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SRC_SRCR_Bits',0,13,62,3
	.word	26627
	.byte	12,13,70,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26627
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SRC_SRCR',0,13,75,3
	.word	26943
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,13,86,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	27003
	.byte	22
	.byte	'Ifx_SRC_AGBT',0,13,89,3
	.word	27035
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,13,92,25,12,13
	.byte	'TX',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,8,0,14
	.word	27061
	.byte	22
	.byte	'Ifx_SRC_ASCLIN',0,13,97,3
	.word	27120
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,13,100,25,4,13
	.byte	'SBSRC',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	27148
	.byte	22
	.byte	'Ifx_SRC_BCUSPB',0,13,103,3
	.word	27185
	.byte	15,64
	.word	26943
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,13,106,25,64,13
	.byte	'INT',0
	.word	27213
	.byte	64,2,35,0,0,14
	.word	27222
	.byte	22
	.byte	'Ifx_SRC_CAN',0,13,109,3
	.word	27254
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,13,112,25,16,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	26943
	.byte	4,2,35,12,0,14
	.word	27279
	.byte	22
	.byte	'Ifx_SRC_CCU6',0,13,118,3
	.word	27351
	.byte	15,8
	.word	26943
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,13,121,25,8,13
	.byte	'SR',0
	.word	27377
	.byte	8,2,35,0,0,14
	.word	27386
	.byte	22
	.byte	'Ifx_SRC_CERBERUS',0,13,124,3
	.word	27422
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,13,127,25,16,13
	.byte	'MI',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	26943
	.byte	4,2,35,12,0,14
	.word	27452
	.byte	22
	.byte	'Ifx_SRC_CIF',0,13,133,1,3
	.word	27525
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,13,136,1,25,4,13
	.byte	'SBSRC',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	27551
	.byte	22
	.byte	'Ifx_SRC_CPU',0,13,139,1,3
	.word	27586
	.byte	15,192,1
	.word	26943
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,13,142,1,25,208,1,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4648
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	27612
	.byte	192,1,2,35,16,0,14
	.word	27622
	.byte	22
	.byte	'Ifx_SRC_DMA',0,13,147,1,3
	.word	27689
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,13,150,1,25,8,13
	.byte	'SRM',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	26943
	.byte	4,2,35,4,0,14
	.word	27715
	.byte	22
	.byte	'Ifx_SRC_DSADC',0,13,154,1,3
	.word	27763
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,13,157,1,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	27791
	.byte	22
	.byte	'Ifx_SRC_EMEM',0,13,160,1,3
	.word	27824
	.byte	15,40
	.word	671
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,13,163,1,25,80,13
	.byte	'INT',0
	.word	27377
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	27377
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	27377
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	27377
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	26943
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	26943
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	27851
	.byte	40,2,35,40,0,14
	.word	27860
	.byte	22
	.byte	'Ifx_SRC_ERAY',0,13,172,1,3
	.word	27987
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,13,175,1,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	28014
	.byte	22
	.byte	'Ifx_SRC_ETH',0,13,178,1,3
	.word	28046
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,13,181,1,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	28072
	.byte	22
	.byte	'Ifx_SRC_FCE',0,13,184,1,3
	.word	28104
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,13,187,1,25,12,13
	.byte	'DONE',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	26943
	.byte	4,2,35,8,0,14
	.word	28130
	.byte	22
	.byte	'Ifx_SRC_FFT',0,13,192,1,3
	.word	28190
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,13,195,1,25,32,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	26943
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	24669
	.byte	16,2,35,16,0,14
	.word	28216
	.byte	22
	.byte	'Ifx_SRC_GPSR',0,13,202,1,3
	.word	28310
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,13,205,1,25,48,13
	.byte	'CIRQ',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	26943
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	26943
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	26943
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3679
	.byte	24,2,35,24,0,14
	.word	28337
	.byte	22
	.byte	'Ifx_SRC_GPT12',0,13,214,1,3
	.word	28454
	.byte	15,12
	.word	26943
	.byte	16,2,0,15,32
	.word	26943
	.byte	16,7,0,15,32
	.word	28491
	.byte	16,0,0,15,88
	.word	671
	.byte	16,87,0,15,108
	.word	26943
	.byte	16,26,0,15,96
	.word	671
	.byte	16,95,0,15,96
	.word	28491
	.byte	16,2,0,15,160,3
	.word	671
	.byte	16,159,3,0,15,64
	.word	28491
	.byte	16,1,0,15,192,3
	.word	671
	.byte	16,191,3,0,15,16
	.word	26943
	.byte	16,3,0,15,64
	.word	28576
	.byte	16,3,0,15,192,2
	.word	671
	.byte	16,191,2,0,15,52
	.word	671
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,13,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	28482
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2489
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	26943
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	26943
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	27377
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4308
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	28500
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	28509
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	28518
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	28527
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	26943
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4648
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	28536
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	28545
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	28536
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	28545
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	28556
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	28565
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	28585
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	28594
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	28482
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	28605
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	28482
	.byte	12,3,35,192,18,0,14
	.word	28614
	.byte	22
	.byte	'Ifx_SRC_GTM',0,13,243,1,3
	.word	29074
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,13,246,1,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	29100
	.byte	22
	.byte	'Ifx_SRC_HSCT',0,13,249,1,3
	.word	29133
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,13,252,1,25,16,13
	.byte	'COK',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	26943
	.byte	4,2,35,12,0,14
	.word	29160
	.byte	22
	.byte	'Ifx_SRC_HSSL',0,13,130,2,3
	.word	29233
	.byte	15,56
	.word	671
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,13,133,2,25,80,13
	.byte	'BREQ',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	26943
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	26943
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	29260
	.byte	56,2,35,24,0,14
	.word	29269
	.byte	22
	.byte	'Ifx_SRC_I2C',0,13,142,2,3
	.word	29392
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,13,145,2,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	29418
	.byte	22
	.byte	'Ifx_SRC_LMU',0,13,148,2,3
	.word	29450
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,13,151,2,25,20,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	26943
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	26943
	.byte	4,2,35,16,0,14
	.word	29476
	.byte	22
	.byte	'Ifx_SRC_MSC',0,13,158,2,3
	.word	29561
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,13,161,2,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	29587
	.byte	22
	.byte	'Ifx_SRC_PMU',0,13,164,2,3
	.word	29619
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,13,167,2,25,32,13
	.byte	'SR',0
	.word	28491
	.byte	32,2,35,0,0,14
	.word	29645
	.byte	22
	.byte	'Ifx_SRC_PSI5',0,13,170,2,3
	.word	29678
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,13,173,2,25,32,13
	.byte	'SR',0
	.word	28491
	.byte	32,2,35,0,0,14
	.word	29705
	.byte	22
	.byte	'Ifx_SRC_PSI5S',0,13,176,2,3
	.word	29739
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,13,179,2,25,24,13
	.byte	'TX',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	26943
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	26943
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	26943
	.byte	4,2,35,20,0,14
	.word	29767
	.byte	22
	.byte	'Ifx_SRC_QSPI',0,13,187,2,3
	.word	29860
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,13,190,2,25,4,13
	.byte	'SR',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	29887
	.byte	22
	.byte	'Ifx_SRC_SCR',0,13,193,2,3
	.word	29919
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,13,196,2,25,20,13
	.byte	'DTS',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	28576
	.byte	16,2,35,4,0,14
	.word	29945
	.byte	22
	.byte	'Ifx_SRC_SCU',0,13,200,2,3
	.word	29991
	.byte	15,24
	.word	26943
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,13,203,2,25,24,13
	.byte	'SR',0
	.word	30017
	.byte	24,2,35,0,0,14
	.word	30026
	.byte	22
	.byte	'Ifx_SRC_SENT',0,13,206,2,3
	.word	30059
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,13,209,2,25,12,13
	.byte	'SR',0
	.word	28482
	.byte	12,2,35,0,0,14
	.word	30086
	.byte	22
	.byte	'Ifx_SRC_SMU',0,13,212,2,3
	.word	30118
	.byte	10
	.byte	'_Ifx_SRC_STM',0,13,215,2,25,8,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,0,14
	.word	30144
	.byte	22
	.byte	'Ifx_SRC_STM',0,13,219,2,3
	.word	30190
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,13,222,2,25,16,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	26943
	.byte	4,2,35,12,0,14
	.word	30216
	.byte	22
	.byte	'Ifx_SRC_VADCCG',0,13,228,2,3
	.word	30291
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,13,231,2,25,16,13
	.byte	'SR0',0
	.word	26943
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	26943
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	26943
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	26943
	.byte	4,2,35,12,0,14
	.word	30320
	.byte	22
	.byte	'Ifx_SRC_VADCG',0,13,237,2,3
	.word	30394
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,13,240,2,25,4,13
	.byte	'SRC',0
	.word	26943
	.byte	4,2,35,0,0,14
	.word	30422
	.byte	22
	.byte	'Ifx_SRC_XBAR',0,13,243,2,3
	.word	30456
	.byte	15,4
	.word	27003
	.byte	16,0,0,14
	.word	30483
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,13,128,3,25,4,13
	.byte	'AGBT',0
	.word	30492
	.byte	4,2,35,0,0,14
	.word	30497
	.byte	22
	.byte	'Ifx_SRC_GAGBT',0,13,131,3,3
	.word	30533
	.byte	15,48
	.word	27061
	.byte	16,3,0,14
	.word	30561
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,13,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	30570
	.byte	48,2,35,0,0,14
	.word	30575
	.byte	22
	.byte	'Ifx_SRC_GASCLIN',0,13,137,3,3
	.word	30615
	.byte	14
	.word	27148
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,13,140,3,25,4,13
	.byte	'SPB',0
	.word	30645
	.byte	4,2,35,0,0,14
	.word	30650
	.byte	22
	.byte	'Ifx_SRC_GBCU',0,13,143,3,3
	.word	30684
	.byte	15,64
	.word	27222
	.byte	16,0,0,14
	.word	30711
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,13,146,3,25,64,13
	.byte	'CAN',0
	.word	30720
	.byte	64,2,35,0,0,14
	.word	30725
	.byte	22
	.byte	'Ifx_SRC_GCAN',0,13,149,3,3
	.word	30759
	.byte	15,32
	.word	27279
	.byte	16,1,0,14
	.word	30786
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,13,152,3,25,32,13
	.byte	'CCU6',0
	.word	30795
	.byte	32,2,35,0,0,14
	.word	30800
	.byte	22
	.byte	'Ifx_SRC_GCCU6',0,13,155,3,3
	.word	30836
	.byte	14
	.word	27386
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,13,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	30864
	.byte	8,2,35,0,0,14
	.word	30869
	.byte	22
	.byte	'Ifx_SRC_GCERBERUS',0,13,161,3,3
	.word	30913
	.byte	15,16
	.word	27452
	.byte	16,0,0,14
	.word	30945
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,13,164,3,25,16,13
	.byte	'CIF',0
	.word	30954
	.byte	16,2,35,0,0,14
	.word	30959
	.byte	22
	.byte	'Ifx_SRC_GCIF',0,13,167,3,3
	.word	30993
	.byte	15,8
	.word	27551
	.byte	16,1,0,14
	.word	31020
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,13,170,3,25,8,13
	.byte	'CPU',0
	.word	31029
	.byte	8,2,35,0,0,14
	.word	31034
	.byte	22
	.byte	'Ifx_SRC_GCPU',0,13,173,3,3
	.word	31068
	.byte	15,208,1
	.word	27622
	.byte	16,0,0,14
	.word	31095
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,13,176,3,25,208,1,13
	.byte	'DMA',0
	.word	31105
	.byte	208,1,2,35,0,0,14
	.word	31110
	.byte	22
	.byte	'Ifx_SRC_GDMA',0,13,179,3,3
	.word	31146
	.byte	14
	.word	27715
	.byte	14
	.word	27715
	.byte	14
	.word	27715
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,13,182,3,25,32,13
	.byte	'DSADC0',0
	.word	31173
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4308
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	31178
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	31183
	.byte	8,2,35,24,0,14
	.word	31188
	.byte	22
	.byte	'Ifx_SRC_GDSADC',0,13,188,3,3
	.word	31279
	.byte	15,4
	.word	27791
	.byte	16,0,0,14
	.word	31308
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,13,191,3,25,4,13
	.byte	'EMEM',0
	.word	31317
	.byte	4,2,35,0,0,14
	.word	31322
	.byte	22
	.byte	'Ifx_SRC_GEMEM',0,13,194,3,3
	.word	31358
	.byte	15,80
	.word	27860
	.byte	16,0,0,14
	.word	31386
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,13,197,3,25,80,13
	.byte	'ERAY',0
	.word	31395
	.byte	80,2,35,0,0,14
	.word	31400
	.byte	22
	.byte	'Ifx_SRC_GERAY',0,13,200,3,3
	.word	31436
	.byte	15,4
	.word	28014
	.byte	16,0,0,14
	.word	31464
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,13,203,3,25,4,13
	.byte	'ETH',0
	.word	31473
	.byte	4,2,35,0,0,14
	.word	31478
	.byte	22
	.byte	'Ifx_SRC_GETH',0,13,206,3,3
	.word	31512
	.byte	15,4
	.word	28072
	.byte	16,0,0,14
	.word	31539
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,13,209,3,25,4,13
	.byte	'FCE',0
	.word	31548
	.byte	4,2,35,0,0,14
	.word	31553
	.byte	22
	.byte	'Ifx_SRC_GFCE',0,13,212,3,3
	.word	31587
	.byte	15,12
	.word	28130
	.byte	16,0,0,14
	.word	31614
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,13,215,3,25,12,13
	.byte	'FFT',0
	.word	31623
	.byte	12,2,35,0,0,14
	.word	31628
	.byte	22
	.byte	'Ifx_SRC_GFFT',0,13,218,3,3
	.word	31662
	.byte	15,64
	.word	28216
	.byte	16,1,0,14
	.word	31689
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,13,221,3,25,64,13
	.byte	'GPSR',0
	.word	31698
	.byte	64,2,35,0,0,14
	.word	31703
	.byte	22
	.byte	'Ifx_SRC_GGPSR',0,13,224,3,3
	.word	31739
	.byte	15,48
	.word	28337
	.byte	16,0,0,14
	.word	31767
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,13,227,3,25,48,13
	.byte	'GPT12',0
	.word	31776
	.byte	48,2,35,0,0,14
	.word	31781
	.byte	22
	.byte	'Ifx_SRC_GGPT12',0,13,230,3,3
	.word	31819
	.byte	15,204,18
	.word	28614
	.byte	16,0,0,14
	.word	31848
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,13,233,3,25,204,18,13
	.byte	'GTM',0
	.word	31858
	.byte	204,18,2,35,0,0,14
	.word	31863
	.byte	22
	.byte	'Ifx_SRC_GGTM',0,13,236,3,3
	.word	31899
	.byte	15,4
	.word	29100
	.byte	16,0,0,14
	.word	31926
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,13,239,3,25,4,13
	.byte	'HSCT',0
	.word	31935
	.byte	4,2,35,0,0,14
	.word	31940
	.byte	22
	.byte	'Ifx_SRC_GHSCT',0,13,242,3,3
	.word	31976
	.byte	15,64
	.word	29160
	.byte	16,3,0,14
	.word	32004
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,13,245,3,25,68,13
	.byte	'HSSL',0
	.word	32013
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	26943
	.byte	4,2,35,64,0,14
	.word	32018
	.byte	22
	.byte	'Ifx_SRC_GHSSL',0,13,249,3,3
	.word	32067
	.byte	15,80
	.word	29269
	.byte	16,0,0,14
	.word	32095
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,13,252,3,25,80,13
	.byte	'I2C',0
	.word	32104
	.byte	80,2,35,0,0,14
	.word	32109
	.byte	22
	.byte	'Ifx_SRC_GI2C',0,13,255,3,3
	.word	32143
	.byte	15,4
	.word	29418
	.byte	16,0,0,14
	.word	32170
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,13,130,4,25,4,13
	.byte	'LMU',0
	.word	32179
	.byte	4,2,35,0,0,14
	.word	32184
	.byte	22
	.byte	'Ifx_SRC_GLMU',0,13,133,4,3
	.word	32218
	.byte	15,40
	.word	29476
	.byte	16,1,0,14
	.word	32245
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,13,136,4,25,40,13
	.byte	'MSC',0
	.word	32254
	.byte	40,2,35,0,0,14
	.word	32259
	.byte	22
	.byte	'Ifx_SRC_GMSC',0,13,139,4,3
	.word	32293
	.byte	15,8
	.word	29587
	.byte	16,1,0,14
	.word	32320
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,13,142,4,25,8,13
	.byte	'PMU',0
	.word	32329
	.byte	8,2,35,0,0,14
	.word	32334
	.byte	22
	.byte	'Ifx_SRC_GPMU',0,13,145,4,3
	.word	32368
	.byte	15,32
	.word	29645
	.byte	16,0,0,14
	.word	32395
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,13,148,4,25,32,13
	.byte	'PSI5',0
	.word	32404
	.byte	32,2,35,0,0,14
	.word	32409
	.byte	22
	.byte	'Ifx_SRC_GPSI5',0,13,151,4,3
	.word	32445
	.byte	15,32
	.word	29705
	.byte	16,0,0,14
	.word	32473
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,13,154,4,25,32,13
	.byte	'PSI5S',0
	.word	32482
	.byte	32,2,35,0,0,14
	.word	32487
	.byte	22
	.byte	'Ifx_SRC_GPSI5S',0,13,157,4,3
	.word	32525
	.byte	15,96
	.word	29767
	.byte	16,3,0,14
	.word	32554
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,13,160,4,25,96,13
	.byte	'QSPI',0
	.word	32563
	.byte	96,2,35,0,0,14
	.word	32568
	.byte	22
	.byte	'Ifx_SRC_GQSPI',0,13,163,4,3
	.word	32604
	.byte	15,4
	.word	29887
	.byte	16,0,0,14
	.word	32632
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,13,166,4,25,4,13
	.byte	'SCR',0
	.word	32641
	.byte	4,2,35,0,0,14
	.word	32646
	.byte	22
	.byte	'Ifx_SRC_GSCR',0,13,169,4,3
	.word	32680
	.byte	14
	.word	29945
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,13,172,4,25,20,13
	.byte	'SCU',0
	.word	32707
	.byte	20,2,35,0,0,14
	.word	32712
	.byte	22
	.byte	'Ifx_SRC_GSCU',0,13,175,4,3
	.word	32746
	.byte	15,24
	.word	30026
	.byte	16,0,0,14
	.word	32773
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,13,178,4,25,24,13
	.byte	'SENT',0
	.word	32782
	.byte	24,2,35,0,0,14
	.word	32787
	.byte	22
	.byte	'Ifx_SRC_GSENT',0,13,181,4,3
	.word	32823
	.byte	15,12
	.word	30086
	.byte	16,0,0,14
	.word	32851
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,13,184,4,25,12,13
	.byte	'SMU',0
	.word	32860
	.byte	12,2,35,0,0,14
	.word	32865
	.byte	22
	.byte	'Ifx_SRC_GSMU',0,13,187,4,3
	.word	32899
	.byte	15,16
	.word	30144
	.byte	16,1,0,14
	.word	32926
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,13,190,4,25,16,13
	.byte	'STM',0
	.word	32935
	.byte	16,2,35,0,0,14
	.word	32940
	.byte	22
	.byte	'Ifx_SRC_GSTM',0,13,193,4,3
	.word	32974
	.byte	15,64
	.word	30320
	.byte	16,3,0,14
	.word	33001
	.byte	15,224,1
	.word	671
	.byte	16,223,1,0,15,32
	.word	30216
	.byte	16,1,0,14
	.word	33026
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,13,196,4,25,192,2,13
	.byte	'G',0
	.word	33010
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	33015
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	33035
	.byte	32,3,35,160,2,0,14
	.word	33040
	.byte	22
	.byte	'Ifx_SRC_GVADC',0,13,201,4,3
	.word	33109
	.byte	14
	.word	30422
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,13,204,4,25,4,13
	.byte	'XBAR',0
	.word	33137
	.byte	4,2,35,0,0,14
	.word	33142
	.byte	22
	.byte	'Ifx_SRC_GXBAR',0,13,207,4,3
	.word	33178
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	33206
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	33763
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	496
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	33840
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	671
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	671
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	671
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	671
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	671
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	671
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	33976
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	671
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	671
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	671
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	671
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	671
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	34256
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	34494
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	671
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	671
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	34622
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	671
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	671
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	34865
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	35100
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	35228
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	35328
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	671
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	671
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	35428
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	496
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	35636
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	688
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	688
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	35801
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	688
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	35984
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	671
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	671
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	496
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	671
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	671
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	36138
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	36502
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	688
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	671
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	671
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	671
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	36713
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	688
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	496
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	36965
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	37083
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	37194
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	37357
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	37520
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	37678
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	671
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	671
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	671
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	671
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	671
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	671
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	688
	.byte	10,0,2,35,2,0,22
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	37843
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	688
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	671
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	671
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	688
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	671
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	38172
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	38393
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	38556
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	38828
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	38981
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	39137
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	39299
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	39442
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	39607
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	688
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	39752
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	671
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	39933
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	40107
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	40267
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	40411
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	40685
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	40824
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	671
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	688
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	671
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	671
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	40987
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	688
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	688
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	41205
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	41368
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	41704
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	671
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	41811
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	42263
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	671
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	42362
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	688
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	42512
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	496
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	42661
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	42822
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	688
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	688
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	42952
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	43084
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	688
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	43199
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	688
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	688
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	43310
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	671
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	671
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	671
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	671
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	43468
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	43880
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	688
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	6,0,2,35,3,0,22
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	43981
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	496
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	44248
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	44384
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	671
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	44495
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	44628
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	688
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	44831
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	671
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	671
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	671
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	688
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	45187
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	688
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	45365
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	688
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	671
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	671
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	45465
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	671
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	671
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	671
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	688
	.byte	9,0,2,35,2,0,22
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	45835
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	46021
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	46219
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	671
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	21,0,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	46452
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	671
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	671
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	671
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	671
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	46604
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	671
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	671
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	671
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	671
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	47171
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	671
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	671
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	671
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	47465
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	671
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	671
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	688
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	47743
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	688
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	48239
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	688
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	48552
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	671
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	671
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	671
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	671
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	671
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	48761
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	671
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	671
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	671
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	48972
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	49404
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	671
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	671
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	671
	.byte	7,0,2,35,3,0,22
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	49500
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	49760
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	671
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	496
	.byte	23,0,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	49885
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	50082
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	50235
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	50388
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	50541
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	535
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	710
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	954
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	519
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	519
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	519
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	519
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	50796
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	50922
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	51174
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33206
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	51393
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33763
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	51457
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33840
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	51521
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33976
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	51586
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34256
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	51651
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34494
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	51716
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34622
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	51781
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34865
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	51846
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35100
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	51911
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35228
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	51976
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35328
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	52041
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35428
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	52106
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35636
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	52170
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35801
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	52234
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35984
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	52298
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36138
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	52363
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36502
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	52425
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36713
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	52487
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36965
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	52549
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37083
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	52613
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37194
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	52678
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37357
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	52744
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37520
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	52810
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37678
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	52878
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37843
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	52945
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38172
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	53013
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38393
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	53081
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38556
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	53147
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38828
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	53214
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38981
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	53283
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39137
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	53352
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39299
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	53421
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39442
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	53490
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39607
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	53559
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39752
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	53628
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39933
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	53696
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40107
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	53764
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40267
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	53832
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40411
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	53900
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40685
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	53965
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40824
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	54030
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40987
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	54096
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41205
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	54160
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41368
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	54221
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41704
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	54282
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41811
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	54342
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42263
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	54404
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42362
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	54464
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42512
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	54526
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42661
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	54594
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42822
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	54662
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42952
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	54730
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43084
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	54794
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43199
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	54859
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43310
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	54922
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43468
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	54983
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43880
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	55047
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43981
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	55108
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44248
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	55172
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44384
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	55239
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44495
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	55302
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44628
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	55363
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44831
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	55425
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45187
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	55490
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45365
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	55555
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45465
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	55620
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45835
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	55689
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46021
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	55758
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46219
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	55827
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46452
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	55892
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46604
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	55955
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47171
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	56020
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47465
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	56085
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47743
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	56150
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48239
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	56216
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48761
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	56285
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48552
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	56349
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48972
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	56414
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49404
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	56479
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49500
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	56544
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49760
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	56608
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49885
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	56674
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50082
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	56738
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50235
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	56803
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50388
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	56868
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50541
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	56933
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	631
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	914
	.byte	22
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1145
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50796
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	57084
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50922
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	57151
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51174
	.byte	4,2,35,0,0,22
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	57218
	.byte	14
	.word	1185
	.byte	22
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	57283
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	57084
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	57151
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	57218
	.byte	4,2,35,8,0,14
	.word	57312
	.byte	22
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	57373
	.byte	15,8
	.word	52549
	.byte	16,1,0,15,20
	.word	671
	.byte	16,19,0,15,8
	.word	55892
	.byte	16,1,0,14
	.word	57312
	.byte	15,24
	.word	1185
	.byte	16,1,0,14
	.word	57432
	.byte	15,16
	.word	52363
	.byte	16,3,0,15,16
	.word	54342
	.byte	16,3,0,15,180,3
	.word	671
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4308
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	54282
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2489
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	54983
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	55827
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	55425
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	55490
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	55555
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	55758
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	55620
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	55689
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	51586
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	51651
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	54160
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	54096
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	51716
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	51781
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	51846
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	51911
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	56414
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2489
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	56285
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	51521
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	56608
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	56349
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2489
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	53147
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	57400
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	52613
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	56674
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	51976
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	52041
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	57409
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	55302
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	54464
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	55047
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	54922
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	54404
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	53900
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	52878
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	52678
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	52744
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	56544
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2489
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	55955
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	56150
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	56216
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	57418
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2489
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	52298
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	52170
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	56020
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	56085
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	57427
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	52487
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	57441
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4648
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	56933
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	56868
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	56738
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	56803
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2489
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	54730
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	54794
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	52106
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	54859
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4308
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	56479
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	24669
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	54526
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	54594
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	54662
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	24649
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	55239
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4308
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	53965
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	52810
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	54030
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	53081
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	52945
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2489
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	53628
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	53696
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	53764
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	53832
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	53214
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	53283
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	53352
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	53421
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	53490
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	53559
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	53013
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2489
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	55172
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	55108
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	27851
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	57446
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	52425
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	54221
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	55363
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	57455
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2489
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	52234
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	57464
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	51457
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	51393
	.byte	4,3,35,252,7,0,14
	.word	57475
	.byte	22
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	59465
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_STM_ACCEN0_Bits',0,14,79,3
	.word	59487
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1_Bits',0,14,85,3
	.word	60044
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,14,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAP_Bits',0,14,91,3
	.word	60121
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,14,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV_Bits',0,14,97,3
	.word	60193
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,14,100,16,4,11
	.byte	'DISR',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_CLC_Bits',0,14,107,3
	.word	60269
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,14,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	671
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	671
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	671
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	671
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	671
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	671
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	671
	.byte	3,0,2,35,3,0,22
	.byte	'Ifx_STM_CMCON_Bits',0,14,120,3
	.word	60410
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,14,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_CMP_Bits',0,14,126,3
	.word	60628
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,14,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	496
	.byte	25,0,2,35,0,0,22
	.byte	'Ifx_STM_ICR_Bits',0,14,139,1,3
	.word	60695
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,14,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_STM_ID_Bits',0,14,147,1,3
	.word	60898
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,14,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_STM_ISCR_Bits',0,14,157,1,3
	.word	61005
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,14,160,1,16,4,11
	.byte	'RST',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST0_Bits',0,14,165,1,3
	.word	61156
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,14,168,1,16,4,11
	.byte	'RST',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRST1_Bits',0,14,172,1,3
	.word	61267
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,14,175,1,16,4,11
	.byte	'CLR',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR_Bits',0,14,179,1,3
	.word	61359
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,14,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	671
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	671
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_STM_OCS_Bits',0,14,189,1,3
	.word	61455
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,14,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0_Bits',0,14,195,1,3
	.word	61601
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,14,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV_Bits',0,14,201,1,3
	.word	61673
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,14,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM1_Bits',0,14,207,1,3
	.word	61749
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,14,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM2_Bits',0,14,213,1,3
	.word	61821
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,14,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM3_Bits',0,14,219,1,3
	.word	61893
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,14,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM4_Bits',0,14,225,1,3
	.word	61966
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,14,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM5_Bits',0,14,231,1,3
	.word	62039
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,14,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_STM_TIM6_Bits',0,14,237,1,3
	.word	62112
	.byte	12,14,245,1,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59487
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN0',0,14,250,1,3
	.word	62185
	.byte	12,14,253,1,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60044
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ACCEN1',0,14,130,2,3
	.word	62249
	.byte	12,14,133,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60121
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAP',0,14,138,2,3
	.word	62313
	.byte	12,14,141,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60193
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CAPSV',0,14,146,2,3
	.word	62374
	.byte	12,14,149,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60269
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CLC',0,14,154,2,3
	.word	62437
	.byte	12,14,157,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60410
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMCON',0,14,162,2,3
	.word	62498
	.byte	12,14,165,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60628
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_CMP',0,14,170,2,3
	.word	62561
	.byte	12,14,173,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60695
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ICR',0,14,178,2,3
	.word	62622
	.byte	12,14,181,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60898
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ID',0,14,186,2,3
	.word	62683
	.byte	12,14,189,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61005
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_ISCR',0,14,194,2,3
	.word	62743
	.byte	12,14,197,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61156
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST0',0,14,202,2,3
	.word	62805
	.byte	12,14,205,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61267
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRST1',0,14,210,2,3
	.word	62868
	.byte	12,14,213,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61359
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_KRSTCLR',0,14,218,2,3
	.word	62931
	.byte	12,14,221,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61455
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_OCS',0,14,226,2,3
	.word	62996
	.byte	12,14,229,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61601
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0',0,14,234,2,3
	.word	63057
	.byte	12,14,237,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61673
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM0SV',0,14,242,2,3
	.word	63119
	.byte	12,14,245,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61749
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM1',0,14,250,2,3
	.word	63183
	.byte	12,14,253,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61821
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM2',0,14,130,3,3
	.word	63245
	.byte	12,14,133,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61893
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM3',0,14,138,3,3
	.word	63307
	.byte	12,14,141,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61966
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM4',0,14,146,3,3
	.word	63369
	.byte	12,14,149,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62039
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM5',0,14,154,3,3
	.word	63431
	.byte	12,14,157,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62112
	.byte	4,2,35,0,0,22
	.byte	'Ifx_STM_TIM6',0,14,162,3,3
	.word	63493
	.byte	17,15,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,22
	.byte	'IfxScu_CCUCON0_CLKSEL',0,15,240,10,3
	.word	63555
	.byte	17,15,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,22
	.byte	'IfxScu_WDTCON1_IR',0,15,255,10,3
	.word	63652
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,16,79,3
	.word	63774
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,16,85,3
	.word	64335
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,16,88,16,4,11
	.byte	'SEL',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,16,95,3
	.word	64416
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,16,98,16,4,11
	.byte	'VLD0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,16,111,3
	.word	64569
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,16,114,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	671
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,16,121,3
	.word	64817
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,16,124,16,4,11
	.byte	'STATUS',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0_Bits',0,16,128,1,3
	.word	64963
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,16,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM1_Bits',0,16,136,1,3
	.word	65061
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,16,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_COMM2_Bits',0,16,144,1,3
	.word	65177
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,16,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	688
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRD_Bits',0,16,153,1,3
	.word	65293
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,16,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	688
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCRP_Bits',0,16,162,1,3
	.word	65433
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,16,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	688
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_ECCW_Bits',0,16,171,1,3
	.word	65573
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,16,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	671
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	671
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	688
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	671
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	671
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	671
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FCON_Bits',0,16,193,1,3
	.word	65712
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,16,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	671
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	671
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FPRO_Bits',0,16,218,1,3
	.word	66074
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,16,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	688
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_FSR_Bits',0,16,254,1,3
	.word	66515
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,16,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_ID_Bits',0,16,134,2,3
	.word	67121
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,16,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	688
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARD_Bits',0,16,147,2,3
	.word	67232
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,16,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	688
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_MARP_Bits',0,16,159,2,3
	.word	67446
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,16,162,2,16,4,11
	.byte	'L',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	671
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	671
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	688
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	671
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCOND_Bits',0,16,179,2,3
	.word	67633
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,16,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	671
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,16,188,2,3
	.word	67957
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,16,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	688
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,16,199,2,3
	.word	68100
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	688
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	671
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	671
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	671
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	688
	.byte	14,0,2,35,2,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,219,2,3
	.word	68289
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,16,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	671
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	671
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,16,254,2,3
	.word	68652
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,16,129,3,16,4,11
	.byte	'S0L',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	671
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONP_Bits',0,16,160,3,3
	.word	69247
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,16,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	671
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,16,194,3,3
	.word	69771
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,16,197,3,16,4,11
	.byte	'TAG',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,16,201,3,3
	.word	70353
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,16,204,3,16,4,11
	.byte	'TAG',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,16,208,3,3
	.word	70455
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,16,211,3,16,4,11
	.byte	'TAG',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,16,215,3,3
	.word	70557
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,16,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	496
	.byte	29,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD_Bits',0,16,222,3,3
	.word	70659
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,16,225,3,16,4,11
	.byte	'STRT',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	671
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	671
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_FLASH_RRCT_Bits',0,16,236,3,3
	.word	70753
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,16,239,3,16,4,11
	.byte	'DATA',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0_Bits',0,16,242,3,3
	.word	70963
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,16,245,3,16,4,11
	.byte	'DATA',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1_Bits',0,16,248,3,3
	.word	71036
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,16,251,3,16,4,11
	.byte	'SEL',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	671
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,16,130,4,3
	.word	71109
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,16,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,16,137,4,3
	.word	71264
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,16,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	671
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,16,147,4,3
	.word	71369
	.byte	12,16,155,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63774
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN0',0,16,160,4,3
	.word	71517
	.byte	12,16,163,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64335
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ACCEN1',0,16,168,4,3
	.word	71583
	.byte	12,16,171,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64416
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_CFG',0,16,176,4,3
	.word	71649
	.byte	12,16,179,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64569
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_STAT',0,16,184,4,3
	.word	71717
	.byte	12,16,187,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64817
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_CBAB_TOP',0,16,192,4,3
	.word	71786
	.byte	12,16,195,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64963
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM0',0,16,200,4,3
	.word	71854
	.byte	12,16,203,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65061
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM1',0,16,208,4,3
	.word	71919
	.byte	12,16,211,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65177
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_COMM2',0,16,216,4,3
	.word	71984
	.byte	12,16,219,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65293
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRD',0,16,224,4,3
	.word	72049
	.byte	12,16,227,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65433
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCRP',0,16,232,4,3
	.word	72114
	.byte	12,16,235,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65573
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ECCW',0,16,240,4,3
	.word	72179
	.byte	12,16,243,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65712
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FCON',0,16,248,4,3
	.word	72243
	.byte	12,16,251,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66074
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FPRO',0,16,128,5,3
	.word	72307
	.byte	12,16,131,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66515
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_FSR',0,16,136,5,3
	.word	72371
	.byte	12,16,139,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67121
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_ID',0,16,144,5,3
	.word	72434
	.byte	12,16,147,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67232
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARD',0,16,152,5,3
	.word	72496
	.byte	12,16,155,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67446
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_MARP',0,16,160,5,3
	.word	72560
	.byte	12,16,163,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67633
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCOND',0,16,168,5,3
	.word	72624
	.byte	12,16,171,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67957
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONDBG',0,16,176,5,3
	.word	72691
	.byte	12,16,179,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68100
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSM',0,16,184,5,3
	.word	72760
	.byte	12,16,187,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68289
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,16,192,5,3
	.word	72829
	.byte	12,16,195,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68652
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONOTP',0,16,200,5,3
	.word	72902
	.byte	12,16,203,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69247
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONP',0,16,208,5,3
	.word	72971
	.byte	12,16,211,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69771
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_PROCONWOP',0,16,216,5,3
	.word	73038
	.byte	12,16,219,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70353
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG0',0,16,224,5,3
	.word	73107
	.byte	12,16,227,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70455
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG1',0,16,232,5,3
	.word	73175
	.byte	12,16,235,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70557
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RDB_CFG2',0,16,240,5,3
	.word	73243
	.byte	12,16,243,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70659
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRAD',0,16,248,5,3
	.word	73311
	.byte	12,16,251,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70753
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRCT',0,16,128,6,3
	.word	73375
	.byte	12,16,131,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70963
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD0',0,16,136,6,3
	.word	73439
	.byte	12,16,139,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71036
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_RRD1',0,16,144,6,3
	.word	73503
	.byte	12,16,147,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71109
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_CFG',0,16,152,6,3
	.word	73567
	.byte	12,16,155,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71264
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_STAT',0,16,160,6,3
	.word	73635
	.byte	12,16,163,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71369
	.byte	4,2,35,0,0,22
	.byte	'Ifx_FLASH_UBAB_TOP',0,16,168,6,3
	.word	73704
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,16,179,6,25,12,13
	.byte	'CFG',0
	.word	71649
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	71717
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	71786
	.byte	4,2,35,8,0,14
	.word	73772
	.byte	22
	.byte	'Ifx_FLASH_CBAB',0,16,184,6,3
	.word	73835
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,16,187,6,25,12,13
	.byte	'CFG0',0
	.word	73107
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	73175
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	73243
	.byte	4,2,35,8,0,14
	.word	73864
	.byte	22
	.byte	'Ifx_FLASH_RDB',0,16,192,6,3
	.word	73928
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,16,195,6,25,12,13
	.byte	'CFG',0
	.word	73567
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	73635
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	73704
	.byte	4,2,35,8,0,14
	.word	73956
	.byte	22
	.byte	'Ifx_FLASH_UBAB',0,16,200,6,3
	.word	74019
	.byte	22
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8061
	.byte	22
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7974
	.byte	22
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4317
	.byte	22
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2370
	.byte	22
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3365
	.byte	22
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2498
	.byte	22
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3145
	.byte	22
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2713
	.byte	22
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2928
	.byte	22
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7333
	.byte	22
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7457
	.byte	22
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7541
	.byte	22
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7721
	.byte	22
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5972
	.byte	22
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6496
	.byte	22
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6146
	.byte	22
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6320
	.byte	22
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6985
	.byte	22
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1799
	.byte	22
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5309
	.byte	22
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5797
	.byte	22
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5456
	.byte	22
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5625
	.byte	22
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6652
	.byte	22
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1483
	.byte	22
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5023
	.byte	22
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4657
	.byte	22
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3688
	.byte	22
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3992
	.byte	22
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8588
	.byte	22
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8021
	.byte	22
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4608
	.byte	22
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2449
	.byte	22
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3639
	.byte	22
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2673
	.byte	22
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3325
	.byte	22
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2888
	.byte	22
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3105
	.byte	22
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7417
	.byte	22
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7666
	.byte	22
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7925
	.byte	22
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7293
	.byte	22
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6106
	.byte	22
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6612
	.byte	22
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6280
	.byte	22
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6456
	.byte	22
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2330
	.byte	22
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6945
	.byte	22
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5416
	.byte	22
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5932
	.byte	22
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5585
	.byte	22
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5757
	.byte	22
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1759
	.byte	22
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5269
	.byte	22
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4983
	.byte	22
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3952
	.byte	22
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4268
	.byte	14
	.word	8628
	.byte	22
	.byte	'Ifx_P',0,6,139,6,3
	.word	75366
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,22
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	75386
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,22
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	75537
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,22
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	75781
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,22
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	75879
	.byte	22
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9241
	.byte	24,5,190,1,9,8,13
	.byte	'port',0
	.word	9236
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	671
	.byte	1,2,35,4,0,22
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	76344
	.byte	22
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,17,148,1,16
	.word	235
	.byte	24,17,212,5,9,8,13
	.byte	'value',0
	.word	9577
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9577
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_CcuconRegConfig',0,17,216,5,3
	.word	76444
	.byte	24,17,221,5,9,8,13
	.byte	'pDivider',0
	.word	671
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	671
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	671
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_InitialStepConfig',0,17,227,5,3
	.word	76515
	.byte	24,17,231,5,9,12,13
	.byte	'k2Step',0
	.word	671
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	76404
	.byte	4,2,35,8,0,22
	.byte	'IfxScuCcu_PllStepsConfig',0,17,236,5,3
	.word	76632
	.byte	3
	.word	232
	.byte	24,17,244,5,9,48,13
	.byte	'ccucon0',0
	.word	76444
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	76444
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	76444
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	76444
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	76444
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	76444
	.byte	8,2,35,40,0,22
	.byte	'IfxScuCcu_ClockDistributionConfig',0,17,252,5,3
	.word	76734
	.byte	24,17,128,6,9,8,13
	.byte	'value',0
	.word	9577
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9577
	.byte	4,2,35,4,0,22
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,17,132,6,3
	.word	76886
	.byte	3
	.word	76632
	.byte	24,17,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	671
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	76962
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	76515
	.byte	8,2,35,8,0,22
	.byte	'IfxScuCcu_SysPllConfig',0,17,142,6,3
	.word	76967
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,22
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	77084
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	9577
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	671
	.byte	1,2,35,4,0,22
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	77173
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	77173
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	77173
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	77173
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	77173
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	77173
	.byte	6,2,35,24,0,22
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	77239
	.byte	10
	.byte	'_Ifx_MTU_ACCEN0_Bits',0,18,45,16,4,11
	.byte	'EN0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_MTU_ACCEN0_Bits',0,18,79,3
	.word	77357
	.byte	10
	.byte	'_Ifx_MTU_ACCEN1_Bits',0,18,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_MTU_ACCEN1_Bits',0,18,85,3
	.word	77914
	.byte	10
	.byte	'_Ifx_MTU_CLC_Bits',0,18,88,16,4,11
	.byte	'DISR',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'Resvd',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,22
	.byte	'Ifx_MTU_CLC_Bits',0,18,95,3
	.word	77991
	.byte	10
	.byte	'_Ifx_MTU_ID_Bits',0,18,98,16,4,11
	.byte	'MODREV',0,1
	.word	671
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	671
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	688
	.byte	16,0,2,35,2,0,22
	.byte	'Ifx_MTU_ID_Bits',0,18,103,3
	.word	78127
	.byte	10
	.byte	'_Ifx_MTU_MEMMAP_Bits',0,18,106,16,4,11
	.byte	'reserved_0',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'CPU2DxMAP',0,1
	.word	671
	.byte	2,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'CPU2PxMAP',0,1
	.word	671
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'CPU1DCMAP',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTMAP',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'CPU1PCMAP',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTMAP',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	671
	.byte	3,1,2,35,1,11
	.byte	'CPU0PCMAP',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTMAP',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'CPU0DxMAP',0,1
	.word	671
	.byte	2,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	688
	.byte	12,0,2,35,2,0,22
	.byte	'Ifx_MTU_MEMMAP_Bits',0,18,124,3
	.word	78232
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT0_Bits',0,18,127,16,4,11
	.byte	'CPU2DSAIU',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'CPU2DTAIU',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'CPU2PSAIU',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'CPU2PTAIU',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'CPU1DSAIU',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTAIU',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'CPU1PSAIU',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTAIU',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	671
	.byte	2,2,2,35,1,11
	.byte	'CPU0DSAIU',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'CPU0PSAIU',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTAIU',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'CPU0DxAIU',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'CPU1DS2AIU',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'CPU2DS2AIU',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'HSMCAIU',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'HSMTAIU',0,1
	.word	671
	.byte	1,7,2,35,3,11
	.byte	'HSMRAIU',0,1
	.word	671
	.byte	1,6,2,35,3,11
	.byte	'FSI0AIU',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'CPU0DS2AIU',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	671
	.byte	4,0,2,35,3,0,22
	.byte	'Ifx_MTU_MEMSTAT0_Bits',0,18,157,1,3
	.word	78633
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT1_Bits',0,18,160,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_MTU_MEMSTAT1_Bits',0,18,163,1,3
	.word	79291
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT2_Bits',0,18,166,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,22
	.byte	'Ifx_MTU_MEMSTAT2_Bits',0,18,169,1,3
	.word	79374
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST0_Bits',0,18,172,1,16,4,11
	.byte	'CPU2XEN',0,1
	.word	671
	.byte	6,2,2,35,0,11
	.byte	'CPU1DSEN',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'Res',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTEN',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'CPU1PSEN',0,1
	.word	671
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	671
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTEN',0,1
	.word	671
	.byte	1,4,2,35,1,11
	.byte	'LMUEN',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'MMCDSEN',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'CPU0DSEN',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'CPU0PSEN',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTEN',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'CPU0DTEN',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'CPUXDS2EN',0,1
	.word	671
	.byte	2,2,2,35,2,11
	.byte	'ETHEN',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	688
	.byte	3,6,2,35,2,11
	.byte	'FSI0EN',0,1
	.word	671
	.byte	1,5,2,35,3,11
	.byte	'CPU0DS2EN',0,1
	.word	671
	.byte	1,4,2,35,3,11
	.byte	'GTMFEN',0,1
	.word	671
	.byte	1,3,2,35,3,11
	.byte	'GTMM0EN',0,1
	.word	671
	.byte	1,2,2,35,3,11
	.byte	'GTMM1EN',0,1
	.word	671
	.byte	1,1,2,35,3,11
	.byte	'GTM1AEN',0,1
	.word	671
	.byte	1,0,2,35,3,0,22
	.byte	'Ifx_MTU_MEMTEST0_Bits',0,18,198,1,3
	.word	79457
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST1_Bits',0,18,201,1,16,4,11
	.byte	'GTM1BEN',0,1
	.word	671
	.byte	1,7,2,35,0,11
	.byte	'GTM2EN',0,1
	.word	671
	.byte	1,6,2,35,0,11
	.byte	'PSI5EN',0,1
	.word	671
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	671
	.byte	1,4,2,35,0,11
	.byte	'MCAN0EN',0,1
	.word	671
	.byte	1,3,2,35,0,11
	.byte	'MCAN1EN',0,1
	.word	671
	.byte	1,2,2,35,0,11
	.byte	'ERAY0OEN',0,1
	.word	671
	.byte	1,1,2,35,0,11
	.byte	'ERAY0TEN',0,1
	.word	671
	.byte	1,0,2,35,0,11
	.byte	'ERAY0MEN',0,1
	.word	671
	.byte	1,7,2,35,1,11
	.byte	'ERAY1XEN',0,1
	.word	671
	.byte	3,4,2,35,1,11
	.byte	'STBY1EN',0,1
	.word	671
	.byte	1,3,2,35,1,11
	.byte	'MCDSEN',0,1
	.word	671
	.byte	1,2,2,35,1,11
	.byte	'EMEML0EN',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'EMEML1EN',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'EMEML2EN',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'EMEML3EN',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'EMEML4EN',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'EMEML5EN',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'EMEML6EN',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'EMEML7EN',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'EMEMLXEN',0,2
	.word	688
	.byte	8,2,2,35,2,11
	.byte	'EMEMUXEN',0,1
	.word	671
	.byte	2,0,2,35,3,0,22
	.byte	'Ifx_MTU_MEMTEST1_Bits',0,18,225,1,3
	.word	79992
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST2_Bits',0,18,228,1,16,4,11
	.byte	'EMEMUxEN',0,2
	.word	688
	.byte	14,2,2,35,0,11
	.byte	'CIF0EN',0,1
	.word	671
	.byte	1,1,2,35,1,11
	.byte	'DAMEN',0,1
	.word	671
	.byte	1,0,2,35,1,11
	.byte	'CIF1EN',0,1
	.word	671
	.byte	1,7,2,35,2,11
	.byte	'CIF2EN',0,1
	.word	671
	.byte	1,6,2,35,2,11
	.byte	'STBY2EN',0,1
	.word	671
	.byte	1,5,2,35,2,11
	.byte	'DMAEN',0,1
	.word	671
	.byte	1,4,2,35,2,11
	.byte	'XTM0EN',0,1
	.word	671
	.byte	1,3,2,35,2,11
	.byte	'XTM1EN',0,1
	.word	671
	.byte	1,2,2,35,2,11
	.byte	'FFT0EN',0,1
	.word	671
	.byte	1,1,2,35,2,11
	.byte	'FFT1EN',0,1
	.word	671
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	671
	.byte	8,0,2,35,3,0,22
	.byte	'Ifx_MTU_MEMTEST2_Bits',0,18,242,1,3
	.word	80485
	.byte	12,18,250,1,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77357
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_ACCEN0',0,18,255,1,3
	.word	80768
	.byte	12,18,130,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77914
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_ACCEN1',0,18,135,2,3
	.word	80832
	.byte	12,18,138,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77991
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_CLC',0,18,143,2,3
	.word	80896
	.byte	12,18,146,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78127
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_ID',0,18,151,2,3
	.word	80957
	.byte	12,18,154,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78232
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMMAP',0,18,159,2,3
	.word	81017
	.byte	12,18,162,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78633
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMSTAT0',0,18,167,2,3
	.word	81081
	.byte	12,18,170,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79291
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMSTAT1',0,18,175,2,3
	.word	81147
	.byte	12,18,178,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79374
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMSTAT2',0,18,183,2,3
	.word	81213
	.byte	12,18,186,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79457
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMTEST0',0,18,191,2,3
	.word	81279
	.byte	12,18,194,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79992
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMTEST1',0,18,199,2,3
	.word	81345
	.byte	12,18,202,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80485
	.byte	4,2,35,0,0,22
	.byte	'Ifx_MTU_MEMTEST2',0,18,207,2,3
	.word	81411
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_MC_CONFIG0_Bits',0,19,45,16,2,11
	.byte	'ACCSTYPE',0,2
	.word	81477
	.byte	8,8,2,35,0,11
	.byte	'reserved_8',0,2
	.word	81477
	.byte	4,4,2,35,0,11
	.byte	'NUMACCS',0,2
	.word	81477
	.byte	4,0,2,35,0,0,22
	.byte	'Ifx_MC_CONFIG0_Bits',0,19,50,3
	.word	81499
	.byte	10
	.byte	'_Ifx_MC_CONFIG1_Bits',0,19,53,16,2,11
	.byte	'ACCSPAT',0,2
	.word	81477
	.byte	8,8,2,35,0,11
	.byte	'SELFASTB',0,2
	.word	81477
	.byte	4,4,2,35,0,11
	.byte	'AG_MOD',0,2
	.word	81477
	.byte	4,0,2,35,0,0,22
	.byte	'Ifx_MC_CONFIG1_Bits',0,19,58,3
	.word	81615
	.byte	10
	.byte	'_Ifx_MC_ECCD_Bits',0,19,61,16,2,11
	.byte	'SERR',0,2
	.word	81477
	.byte	1,15,2,35,0,11
	.byte	'CERR',0,2
	.word	81477
	.byte	1,14,2,35,0,11
	.byte	'UERR',0,2
	.word	81477
	.byte	1,13,2,35,0,11
	.byte	'AERR',0,2
	.word	81477
	.byte	1,12,2,35,0,11
	.byte	'TRC',0,2
	.word	81477
	.byte	1,11,2,35,0,11
	.byte	'VAL',0,2
	.word	81477
	.byte	5,6,2,35,0,11
	.byte	'RARVAL',0,2
	.word	81477
	.byte	1,5,2,35,0,11
	.byte	'CENE',0,2
	.word	81477
	.byte	1,4,2,35,0,11
	.byte	'UENE',0,2
	.word	81477
	.byte	1,3,2,35,0,11
	.byte	'AENE',0,2
	.word	81477
	.byte	1,2,2,35,0,11
	.byte	'ECE',0,2
	.word	81477
	.byte	1,1,2,35,0,11
	.byte	'EOV',0,2
	.word	81477
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_MC_ECCD_Bits',0,19,75,3
	.word	81727
	.byte	10
	.byte	'_Ifx_MC_ECCS_Bits',0,19,78,16,2,11
	.byte	'CENE',0,2
	.word	81477
	.byte	1,15,2,35,0,11
	.byte	'UENE',0,2
	.word	81477
	.byte	1,14,2,35,0,11
	.byte	'AENE',0,2
	.word	81477
	.byte	1,13,2,35,0,11
	.byte	'ECE',0,2
	.word	81477
	.byte	1,12,2,35,0,11
	.byte	'TRE',0,2
	.word	81477
	.byte	1,11,2,35,0,11
	.byte	'BFLE',0,2
	.word	81477
	.byte	1,10,2,35,0,11
	.byte	'SFLE',0,2
	.word	81477
	.byte	2,8,2,35,0,11
	.byte	'ECCMAP',0,2
	.word	81477
	.byte	2,6,2,35,0,11
	.byte	'TC_WAY_SEL',0,2
	.word	81477
	.byte	2,4,2,35,0,11
	.byte	'reserved_12',0,2
	.word	81477
	.byte	4,0,2,35,0,0,22
	.byte	'Ifx_MC_ECCS_Bits',0,19,90,3
	.word	81966
	.byte	10
	.byte	'_Ifx_MC_ETRR_Bits',0,19,93,16,2,11
	.byte	'ADDR',0,2
	.word	81477
	.byte	13,3,2,35,0,11
	.byte	'MBI',0,2
	.word	81477
	.byte	3,0,2,35,0,0,22
	.byte	'Ifx_MC_ETRR_Bits',0,19,97,3
	.word	82188
	.byte	10
	.byte	'_Ifx_MC_MCONTROL_Bits',0,19,100,16,2,11
	.byte	'START',0,2
	.word	81477
	.byte	1,15,2,35,0,11
	.byte	'RESUME',0,2
	.word	81477
	.byte	1,14,2,35,0,11
	.byte	'ESTF',0,2
	.word	81477
	.byte	1,13,2,35,0,11
	.byte	'DIR',0,2
	.word	81477
	.byte	1,12,2,35,0,11
	.byte	'DINIT',0,2
	.word	81477
	.byte	1,11,2,35,0,11
	.byte	'RCADR',0,2
	.word	81477
	.byte	1,10,2,35,0,11
	.byte	'ROWTOG',0,2
	.word	81477
	.byte	1,9,2,35,0,11
	.byte	'BITTOG',0,2
	.word	81477
	.byte	1,8,2,35,0,11
	.byte	'GP_BASE',0,2
	.word	81477
	.byte	1,7,2,35,0,11
	.byte	'FAILDMP',0,2
	.word	81477
	.byte	1,6,2,35,0,11
	.byte	'reserved_10',0,2
	.word	81477
	.byte	6,0,2,35,0,0,22
	.byte	'Ifx_MC_MCONTROL_Bits',0,19,113,3
	.word	82268
	.byte	10
	.byte	'_Ifx_MC_MSTATUS_Bits',0,19,116,16,2,11
	.byte	'DONE',0,2
	.word	81477
	.byte	1,15,2,35,0,11
	.byte	'FAIL',0,2
	.word	81477
	.byte	1,14,2,35,0,11
	.byte	'FDA',0,2
	.word	81477
	.byte	1,13,2,35,0,11
	.byte	'SFAIL',0,2
	.word	81477
	.byte	1,12,2,35,0,11
	.byte	'reserved_4',0,2
	.word	81477
	.byte	12,0,2,35,0,0,22
	.byte	'Ifx_MC_MSTATUS_Bits',0,19,123,3
	.word	82522
	.byte	10
	.byte	'_Ifx_MC_RANGE_Bits',0,19,126,16,2,11
	.byte	'ADDR',0,2
	.word	81477
	.byte	15,1,2,35,0,11
	.byte	'RAEN',0,2
	.word	81477
	.byte	1,0,2,35,0,0,22
	.byte	'Ifx_MC_RANGE_Bits',0,19,130,1,3
	.word	82663
	.byte	10
	.byte	'_Ifx_MC_RDBFL_Bits',0,19,133,1,16,2,11
	.byte	'WDATA',0,2
	.word	81477
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_MC_RDBFL_Bits',0,19,136,1,3
	.word	82747
	.byte	10
	.byte	'_Ifx_MC_REVID_Bits',0,19,139,1,16,2,11
	.byte	'REV_ID',0,2
	.word	81477
	.byte	16,0,2,35,0,0,22
	.byte	'Ifx_MC_REVID_Bits',0,19,142,1,3
	.word	82817
	.byte	12,19,150,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	81499
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_CONFIG0',0,19,155,1,3
	.word	82888
	.byte	12,19,158,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	81615
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_CONFIG1',0,19,163,1,3
	.word	82952
	.byte	12,19,166,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	81727
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_ECCD',0,19,171,1,3
	.word	83016
	.byte	12,19,174,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	81966
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_ECCS',0,19,179,1,3
	.word	83077
	.byte	12,19,182,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82188
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_ETRR',0,19,187,1,3
	.word	83138
	.byte	12,19,190,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82268
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_MCONTROL',0,19,195,1,3
	.word	83199
	.byte	12,19,198,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82522
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_MSTATUS',0,19,203,1,3
	.word	83264
	.byte	12,19,206,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82663
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_RANGE',0,19,211,1,3
	.word	83328
	.byte	12,19,214,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82747
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_RDBFL',0,19,219,1,3
	.word	83390
	.byte	12,19,222,1,9,2,13
	.byte	'U',0
	.word	688
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	9993
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	82817
	.byte	2,2,35,0,0,22
	.byte	'Ifx_MC_REVID',0,19,227,1,3
	.word	83452
	.byte	17,20,86,9,1,18
	.byte	'IfxMtu_MbistSel_none',0,127,18
	.byte	'IfxMtu_MbistSel_cpu1Dspr',0,6,18
	.byte	'IfxMtu_MbistSel_cpu1Dtag',0,8,18
	.byte	'IfxMtu_MbistSel_cpu1Pspr',0,9,18
	.byte	'IfxMtu_MbistSel_cpu1Ptag',0,11,18
	.byte	'IfxMtu_MbistSel_cpu0Dspr',0,14,18
	.byte	'IfxMtu_MbistSel_cpu0Pspr',0,16,18
	.byte	'IfxMtu_MbistSel_cpu0Ptag',0,17,18
	.byte	'IfxMtu_MbistSel_ethermac',0,22,18
	.byte	'IfxMtu_MbistSel_mod4',0,26,18
	.byte	'IfxMtu_MbistSel_gtmFifo',0,28,18
	.byte	'IfxMtu_MbistSel_gtmMcs0',0,29,18
	.byte	'IfxMtu_MbistSel_gtmMcs1',0,30,18
	.byte	'IfxMtu_MbistSel_gtmDpll1a',0,31,18
	.byte	'IfxMtu_MbistSel_gtmDpll1b',0,32,18
	.byte	'IfxMtu_MbistSel_gtmDpll2',0,33,18
	.byte	'IfxMtu_MbistSel_psi5',0,34,18
	.byte	'IfxMtu_MbistSel_mcan',0,36,18
	.byte	'IfxMtu_MbistSel_erayObf',0,38,18
	.byte	'IfxMtu_MbistSel_erayIbfTbf',0,39,18
	.byte	'IfxMtu_MbistSel_erayMbf',0,40,18
	.byte	'IfxMtu_MbistSel_stdbyRam1',0,44,18
	.byte	'IfxMtu_MbistSel_mcds',0,45,18
	.byte	'IfxMtu_MbistSel_emem0',0,46,18
	.byte	'IfxMtu_MbistSel_emem1',0,47,18
	.byte	'IfxMtu_MbistSel_emem2',0,48,18
	.byte	'IfxMtu_MbistSel_emem3',0,49,18
	.byte	'IfxMtu_MbistSel_emem4',0,50,18
	.byte	'IfxMtu_MbistSel_emem5',0,51,18
	.byte	'IfxMtu_MbistSel_emem6',0,52,18
	.byte	'IfxMtu_MbistSel_emem7',0,53,18
	.byte	'IfxMtu_MbistSel_cifJpeg1_4',0,206,0,18
	.byte	'IfxMtu_MbistSel_cifJpeg3',0,208,0,18
	.byte	'IfxMtu_MbistSel_cifCif',0,209,0,18
	.byte	'IfxMtu_MbistSel_stdbyRam2',0,210,0,18
	.byte	'IfxMtu_MbistSel_dma',0,211,0,18
	.byte	'IfxMtu_MbistSel_ememXtm0',0,212,0,18
	.byte	'IfxMtu_MbistSel_ememXtm1',0,213,0,18
	.byte	'IfxMtu_MbistSel_fft0',0,214,0,18
	.byte	'IfxMtu_MbistSel_fft1',0,215,0,0,22
	.byte	'IfxMtu_MbistSel',0,20,128,1,3
	.word	83514
	.byte	24,20,136,1,9,12,13
	.byte	'numBlocks',0
	.word	671
	.byte	1,2,35,0,13
	.byte	'dataSize',0
	.word	688
	.byte	2,2,35,2,13
	.byte	'eccSize',0
	.word	671
	.byte	1,2,35,4,13
	.byte	'eccInvPos0',0
	.word	671
	.byte	1,2,35,5,13
	.byte	'eccInvPos1',0
	.word	671
	.byte	1,2,35,6,13
	.byte	'mbistDelay',0
	.word	9577
	.byte	4,2,35,8,0,22
	.byte	'IfxMtu_SramItem',0,20,144,1,3
	.word	84578
	.byte	15,160,8
	.word	84578
	.byte	16,87,0
.L8:
	.byte	25
	.word	84724
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,49,19,0,0,21,5,0,49,19,0,0,22,22,0,3,8,58
	.byte	15,59,15,57,15,73,19,0,0,23,21,0,54,15,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'IfxMtu_regdef.h',0,1,0,0
	.byte	'IfxMc_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_Impl\\IfxMtu_cfg.h',0,0,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxMtu_sramTable')
	.sect	'.debug_info'
.L6:
	.word	262
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMtu_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMtu_sramTable',0,9,56,23
	.word	.L8
	.byte	1,5,3
	.word	IfxMtu_sramTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_sramTable')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
