/**
 * \file IfxMtu_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Mtu_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Mtu
 * 
 */
#ifndef IFXMTU_BF_H
#define IFXMTU_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Mtu_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN0 */
#define IFX_MTU_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN0 */
#define IFX_MTU_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN0 */
#define IFX_MTU_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN10 */
#define IFX_MTU_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN10 */
#define IFX_MTU_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN10 */
#define IFX_MTU_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN11 */
#define IFX_MTU_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN11 */
#define IFX_MTU_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN11 */
#define IFX_MTU_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN12 */
#define IFX_MTU_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN12 */
#define IFX_MTU_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN12 */
#define IFX_MTU_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN13 */
#define IFX_MTU_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN13 */
#define IFX_MTU_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN13 */
#define IFX_MTU_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN14 */
#define IFX_MTU_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN14 */
#define IFX_MTU_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN14 */
#define IFX_MTU_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN15 */
#define IFX_MTU_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN15 */
#define IFX_MTU_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN15 */
#define IFX_MTU_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN16 */
#define IFX_MTU_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN16 */
#define IFX_MTU_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN16 */
#define IFX_MTU_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN17 */
#define IFX_MTU_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN17 */
#define IFX_MTU_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN17 */
#define IFX_MTU_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN18 */
#define IFX_MTU_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN18 */
#define IFX_MTU_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN18 */
#define IFX_MTU_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN19 */
#define IFX_MTU_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN19 */
#define IFX_MTU_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN19 */
#define IFX_MTU_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN1 */
#define IFX_MTU_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN1 */
#define IFX_MTU_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN1 */
#define IFX_MTU_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN20 */
#define IFX_MTU_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN20 */
#define IFX_MTU_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN20 */
#define IFX_MTU_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN21 */
#define IFX_MTU_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN21 */
#define IFX_MTU_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN21 */
#define IFX_MTU_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN22 */
#define IFX_MTU_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN22 */
#define IFX_MTU_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN22 */
#define IFX_MTU_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN23 */
#define IFX_MTU_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN23 */
#define IFX_MTU_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN23 */
#define IFX_MTU_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN24 */
#define IFX_MTU_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN24 */
#define IFX_MTU_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN24 */
#define IFX_MTU_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN25 */
#define IFX_MTU_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN25 */
#define IFX_MTU_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN25 */
#define IFX_MTU_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN26 */
#define IFX_MTU_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN26 */
#define IFX_MTU_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN26 */
#define IFX_MTU_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN27 */
#define IFX_MTU_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN27 */
#define IFX_MTU_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN27 */
#define IFX_MTU_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN28 */
#define IFX_MTU_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN28 */
#define IFX_MTU_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN28 */
#define IFX_MTU_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN29 */
#define IFX_MTU_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN29 */
#define IFX_MTU_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN29 */
#define IFX_MTU_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN2 */
#define IFX_MTU_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN2 */
#define IFX_MTU_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN2 */
#define IFX_MTU_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN30 */
#define IFX_MTU_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN30 */
#define IFX_MTU_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN30 */
#define IFX_MTU_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN31 */
#define IFX_MTU_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN31 */
#define IFX_MTU_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN31 */
#define IFX_MTU_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN3 */
#define IFX_MTU_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN3 */
#define IFX_MTU_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN3 */
#define IFX_MTU_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN4 */
#define IFX_MTU_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN4 */
#define IFX_MTU_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN4 */
#define IFX_MTU_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN5 */
#define IFX_MTU_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN5 */
#define IFX_MTU_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN5 */
#define IFX_MTU_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN6 */
#define IFX_MTU_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN6 */
#define IFX_MTU_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN6 */
#define IFX_MTU_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN7 */
#define IFX_MTU_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN7 */
#define IFX_MTU_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN7 */
#define IFX_MTU_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN8 */
#define IFX_MTU_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN8 */
#define IFX_MTU_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN8 */
#define IFX_MTU_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_MTU_ACCEN0_Bits.EN9 */
#define IFX_MTU_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_MTU_ACCEN0_Bits.EN9 */
#define IFX_MTU_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_ACCEN0_Bits.EN9 */
#define IFX_MTU_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_MTU_CLC_Bits.DISR */
#define IFX_MTU_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_MTU_CLC_Bits.DISR */
#define IFX_MTU_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_CLC_Bits.DISR */
#define IFX_MTU_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_MTU_CLC_Bits.DISS */
#define IFX_MTU_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_MTU_CLC_Bits.DISS */
#define IFX_MTU_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_CLC_Bits.DISS */
#define IFX_MTU_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_MTU_CLC_Bits.EDIS */
#define IFX_MTU_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_MTU_CLC_Bits.EDIS */
#define IFX_MTU_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_CLC_Bits.EDIS */
#define IFX_MTU_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_MTU_CLC_Bits.Resvd */
#define IFX_MTU_CLC_RESVD_LEN (1u)

/** \brief  Mask for Ifx_MTU_CLC_Bits.Resvd */
#define IFX_MTU_CLC_RESVD_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_CLC_Bits.Resvd */
#define IFX_MTU_CLC_RESVD_OFF (2u)

/** \brief  Length for Ifx_MTU_ID_Bits.MODNUMBER */
#define IFX_MTU_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_MTU_ID_Bits.MODNUMBER */
#define IFX_MTU_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_MTU_ID_Bits.MODNUMBER */
#define IFX_MTU_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_MTU_ID_Bits.MODREV */
#define IFX_MTU_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_MTU_ID_Bits.MODREV */
#define IFX_MTU_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_MTU_ID_Bits.MODREV */
#define IFX_MTU_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_MTU_ID_Bits.MODTYPE */
#define IFX_MTU_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_MTU_ID_Bits.MODTYPE */
#define IFX_MTU_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_MTU_ID_Bits.MODTYPE */
#define IFX_MTU_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU0DxMAP */
#define IFX_MTU_MEMMAP_CPU0DXMAP_LEN (2u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU0DxMAP */
#define IFX_MTU_MEMMAP_CPU0DXMAP_MSK (0x3u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU0DxMAP */
#define IFX_MTU_MEMMAP_CPU0DXMAP_OFF (18u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU0PCMAP */
#define IFX_MTU_MEMMAP_CPU0PCMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU0PCMAP */
#define IFX_MTU_MEMMAP_CPU0PCMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU0PCMAP */
#define IFX_MTU_MEMMAP_CPU0PCMAP_OFF (15u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU0PTMAP */
#define IFX_MTU_MEMMAP_CPU0PTMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU0PTMAP */
#define IFX_MTU_MEMMAP_CPU0PTMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU0PTMAP */
#define IFX_MTU_MEMMAP_CPU0PTMAP_OFF (17u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU1DCMAP */
#define IFX_MTU_MEMMAP_CPU1DCMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU1DCMAP */
#define IFX_MTU_MEMMAP_CPU1DCMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU1DCMAP */
#define IFX_MTU_MEMMAP_CPU1DCMAP_OFF (7u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU1DTMAP */
#define IFX_MTU_MEMMAP_CPU1DTMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU1DTMAP */
#define IFX_MTU_MEMMAP_CPU1DTMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU1DTMAP */
#define IFX_MTU_MEMMAP_CPU1DTMAP_OFF (8u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU1PCMAP */
#define IFX_MTU_MEMMAP_CPU1PCMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU1PCMAP */
#define IFX_MTU_MEMMAP_CPU1PCMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU1PCMAP */
#define IFX_MTU_MEMMAP_CPU1PCMAP_OFF (10u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU1PTMAP */
#define IFX_MTU_MEMMAP_CPU1PTMAP_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU1PTMAP */
#define IFX_MTU_MEMMAP_CPU1PTMAP_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU1PTMAP */
#define IFX_MTU_MEMMAP_CPU1PTMAP_OFF (11u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU2DxMAP */
#define IFX_MTU_MEMMAP_CPU2DXMAP_LEN (2u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU2DxMAP */
#define IFX_MTU_MEMMAP_CPU2DXMAP_MSK (0x3u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU2DxMAP */
#define IFX_MTU_MEMMAP_CPU2DXMAP_OFF (1u)

/** \brief  Length for Ifx_MTU_MEMMAP_Bits.CPU2PxMAP */
#define IFX_MTU_MEMMAP_CPU2PXMAP_LEN (2u)

/** \brief  Mask for Ifx_MTU_MEMMAP_Bits.CPU2PxMAP */
#define IFX_MTU_MEMMAP_CPU2PXMAP_MSK (0x3u)

/** \brief  Offset for Ifx_MTU_MEMMAP_Bits.CPU2PxMAP */
#define IFX_MTU_MEMMAP_CPU2PXMAP_OFF (4u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU0DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU0DS2AIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU0DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU0DS2AIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU0DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU0DS2AIU_OFF (27u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU0DSAIU */
#define IFX_MTU_MEMSTAT0_CPU0DSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU0DSAIU */
#define IFX_MTU_MEMSTAT0_CPU0DSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU0DSAIU */
#define IFX_MTU_MEMSTAT0_CPU0DSAIU_OFF (14u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU0DxAIU */
#define IFX_MTU_MEMSTAT0_CPU0DXAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU0DxAIU */
#define IFX_MTU_MEMSTAT0_CPU0DXAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU0DxAIU */
#define IFX_MTU_MEMSTAT0_CPU0DXAIU_OFF (19u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU0PSAIU */
#define IFX_MTU_MEMSTAT0_CPU0PSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU0PSAIU */
#define IFX_MTU_MEMSTAT0_CPU0PSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU0PSAIU */
#define IFX_MTU_MEMSTAT0_CPU0PSAIU_OFF (16u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU0PTAIU */
#define IFX_MTU_MEMSTAT0_CPU0PTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU0PTAIU */
#define IFX_MTU_MEMSTAT0_CPU0PTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU0PTAIU */
#define IFX_MTU_MEMSTAT0_CPU0PTAIU_OFF (17u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU1DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU1DS2AIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU1DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU1DS2AIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU1DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU1DS2AIU_OFF (20u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU1DSAIU */
#define IFX_MTU_MEMSTAT0_CPU1DSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU1DSAIU */
#define IFX_MTU_MEMSTAT0_CPU1DSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU1DSAIU */
#define IFX_MTU_MEMSTAT0_CPU1DSAIU_OFF (6u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU1DTAIU */
#define IFX_MTU_MEMSTAT0_CPU1DTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU1DTAIU */
#define IFX_MTU_MEMSTAT0_CPU1DTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU1DTAIU */
#define IFX_MTU_MEMSTAT0_CPU1DTAIU_OFF (8u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU1PSAIU */
#define IFX_MTU_MEMSTAT0_CPU1PSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU1PSAIU */
#define IFX_MTU_MEMSTAT0_CPU1PSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU1PSAIU */
#define IFX_MTU_MEMSTAT0_CPU1PSAIU_OFF (9u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU1PTAIU */
#define IFX_MTU_MEMSTAT0_CPU1PTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU1PTAIU */
#define IFX_MTU_MEMSTAT0_CPU1PTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU1PTAIU */
#define IFX_MTU_MEMSTAT0_CPU1PTAIU_OFF (11u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU2DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU2DS2AIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU2DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU2DS2AIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU2DS2AIU */
#define IFX_MTU_MEMSTAT0_CPU2DS2AIU_OFF (21u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU2DSAIU */
#define IFX_MTU_MEMSTAT0_CPU2DSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU2DSAIU */
#define IFX_MTU_MEMSTAT0_CPU2DSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU2DSAIU */
#define IFX_MTU_MEMSTAT0_CPU2DSAIU_OFF (0u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU2DTAIU */
#define IFX_MTU_MEMSTAT0_CPU2DTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU2DTAIU */
#define IFX_MTU_MEMSTAT0_CPU2DTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU2DTAIU */
#define IFX_MTU_MEMSTAT0_CPU2DTAIU_OFF (2u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU2PSAIU */
#define IFX_MTU_MEMSTAT0_CPU2PSAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU2PSAIU */
#define IFX_MTU_MEMSTAT0_CPU2PSAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU2PSAIU */
#define IFX_MTU_MEMSTAT0_CPU2PSAIU_OFF (3u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.CPU2PTAIU */
#define IFX_MTU_MEMSTAT0_CPU2PTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.CPU2PTAIU */
#define IFX_MTU_MEMSTAT0_CPU2PTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.CPU2PTAIU */
#define IFX_MTU_MEMSTAT0_CPU2PTAIU_OFF (5u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.FSI0AIU */
#define IFX_MTU_MEMSTAT0_FSI0AIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.FSI0AIU */
#define IFX_MTU_MEMSTAT0_FSI0AIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.FSI0AIU */
#define IFX_MTU_MEMSTAT0_FSI0AIU_OFF (26u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.HSMCAIU */
#define IFX_MTU_MEMSTAT0_HSMCAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.HSMCAIU */
#define IFX_MTU_MEMSTAT0_HSMCAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.HSMCAIU */
#define IFX_MTU_MEMSTAT0_HSMCAIU_OFF (23u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.HSMRAIU */
#define IFX_MTU_MEMSTAT0_HSMRAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.HSMRAIU */
#define IFX_MTU_MEMSTAT0_HSMRAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.HSMRAIU */
#define IFX_MTU_MEMSTAT0_HSMRAIU_OFF (25u)

/** \brief  Length for Ifx_MTU_MEMSTAT0_Bits.HSMTAIU */
#define IFX_MTU_MEMSTAT0_HSMTAIU_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMSTAT0_Bits.HSMTAIU */
#define IFX_MTU_MEMSTAT0_HSMTAIU_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMSTAT0_Bits.HSMTAIU */
#define IFX_MTU_MEMSTAT0_HSMTAIU_OFF (24u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU0DS2EN */
#define IFX_MTU_MEMTEST0_CPU0DS2EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU0DS2EN */
#define IFX_MTU_MEMTEST0_CPU0DS2EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU0DS2EN */
#define IFX_MTU_MEMTEST0_CPU0DS2EN_OFF (27u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU0DSEN */
#define IFX_MTU_MEMTEST0_CPU0DSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU0DSEN */
#define IFX_MTU_MEMTEST0_CPU0DSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU0DSEN */
#define IFX_MTU_MEMTEST0_CPU0DSEN_OFF (14u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU0DTEN */
#define IFX_MTU_MEMTEST0_CPU0DTEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU0DTEN */
#define IFX_MTU_MEMTEST0_CPU0DTEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU0DTEN */
#define IFX_MTU_MEMTEST0_CPU0DTEN_OFF (19u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU0PSEN */
#define IFX_MTU_MEMTEST0_CPU0PSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU0PSEN */
#define IFX_MTU_MEMTEST0_CPU0PSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU0PSEN */
#define IFX_MTU_MEMTEST0_CPU0PSEN_OFF (16u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU0PTEN */
#define IFX_MTU_MEMTEST0_CPU0PTEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU0PTEN */
#define IFX_MTU_MEMTEST0_CPU0PTEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU0PTEN */
#define IFX_MTU_MEMTEST0_CPU0PTEN_OFF (17u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU1DSEN */
#define IFX_MTU_MEMTEST0_CPU1DSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU1DSEN */
#define IFX_MTU_MEMTEST0_CPU1DSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU1DSEN */
#define IFX_MTU_MEMTEST0_CPU1DSEN_OFF (6u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU1DTEN */
#define IFX_MTU_MEMTEST0_CPU1DTEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU1DTEN */
#define IFX_MTU_MEMTEST0_CPU1DTEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU1DTEN */
#define IFX_MTU_MEMTEST0_CPU1DTEN_OFF (8u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU1PSEN */
#define IFX_MTU_MEMTEST0_CPU1PSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU1PSEN */
#define IFX_MTU_MEMTEST0_CPU1PSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU1PSEN */
#define IFX_MTU_MEMTEST0_CPU1PSEN_OFF (9u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU1PTEN */
#define IFX_MTU_MEMTEST0_CPU1PTEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU1PTEN */
#define IFX_MTU_MEMTEST0_CPU1PTEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU1PTEN */
#define IFX_MTU_MEMTEST0_CPU1PTEN_OFF (11u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPU2XEN */
#define IFX_MTU_MEMTEST0_CPU2XEN_LEN (6u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPU2XEN */
#define IFX_MTU_MEMTEST0_CPU2XEN_MSK (0x3fu)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPU2XEN */
#define IFX_MTU_MEMTEST0_CPU2XEN_OFF (0u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.CPUXDS2EN */
#define IFX_MTU_MEMTEST0_CPUXDS2EN_LEN (2u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.CPUXDS2EN */
#define IFX_MTU_MEMTEST0_CPUXDS2EN_MSK (0x3u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.CPUXDS2EN */
#define IFX_MTU_MEMTEST0_CPUXDS2EN_OFF (20u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.ETHEN */
#define IFX_MTU_MEMTEST0_ETHEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.ETHEN */
#define IFX_MTU_MEMTEST0_ETHEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.ETHEN */
#define IFX_MTU_MEMTEST0_ETHEN_OFF (22u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.FSI0EN */
#define IFX_MTU_MEMTEST0_FSI0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.FSI0EN */
#define IFX_MTU_MEMTEST0_FSI0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.FSI0EN */
#define IFX_MTU_MEMTEST0_FSI0EN_OFF (26u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.GTM1AEN */
#define IFX_MTU_MEMTEST0_GTM1AEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.GTM1AEN */
#define IFX_MTU_MEMTEST0_GTM1AEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.GTM1AEN */
#define IFX_MTU_MEMTEST0_GTM1AEN_OFF (31u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.GTMFEN */
#define IFX_MTU_MEMTEST0_GTMFEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.GTMFEN */
#define IFX_MTU_MEMTEST0_GTMFEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.GTMFEN */
#define IFX_MTU_MEMTEST0_GTMFEN_OFF (28u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.GTMM0EN */
#define IFX_MTU_MEMTEST0_GTMM0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.GTMM0EN */
#define IFX_MTU_MEMTEST0_GTMM0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.GTMM0EN */
#define IFX_MTU_MEMTEST0_GTMM0EN_OFF (29u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.GTMM1EN */
#define IFX_MTU_MEMTEST0_GTMM1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.GTMM1EN */
#define IFX_MTU_MEMTEST0_GTMM1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.GTMM1EN */
#define IFX_MTU_MEMTEST0_GTMM1EN_OFF (30u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.LMUEN */
#define IFX_MTU_MEMTEST0_LMUEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.LMUEN */
#define IFX_MTU_MEMTEST0_LMUEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.LMUEN */
#define IFX_MTU_MEMTEST0_LMUEN_OFF (12u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.MMCDSEN */
#define IFX_MTU_MEMTEST0_MMCDSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.MMCDSEN */
#define IFX_MTU_MEMTEST0_MMCDSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.MMCDSEN */
#define IFX_MTU_MEMTEST0_MMCDSEN_OFF (13u)

/** \brief  Length for Ifx_MTU_MEMTEST0_Bits.Res */
#define IFX_MTU_MEMTEST0_RES_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST0_Bits.Res */
#define IFX_MTU_MEMTEST0_RES_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST0_Bits.Res */
#define IFX_MTU_MEMTEST0_RES_OFF (7u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML0EN */
#define IFX_MTU_MEMTEST1_EMEML0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML0EN */
#define IFX_MTU_MEMTEST1_EMEML0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML0EN */
#define IFX_MTU_MEMTEST1_EMEML0EN_OFF (14u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML1EN */
#define IFX_MTU_MEMTEST1_EMEML1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML1EN */
#define IFX_MTU_MEMTEST1_EMEML1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML1EN */
#define IFX_MTU_MEMTEST1_EMEML1EN_OFF (15u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML2EN */
#define IFX_MTU_MEMTEST1_EMEML2EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML2EN */
#define IFX_MTU_MEMTEST1_EMEML2EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML2EN */
#define IFX_MTU_MEMTEST1_EMEML2EN_OFF (16u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML3EN */
#define IFX_MTU_MEMTEST1_EMEML3EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML3EN */
#define IFX_MTU_MEMTEST1_EMEML3EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML3EN */
#define IFX_MTU_MEMTEST1_EMEML3EN_OFF (17u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML4EN */
#define IFX_MTU_MEMTEST1_EMEML4EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML4EN */
#define IFX_MTU_MEMTEST1_EMEML4EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML4EN */
#define IFX_MTU_MEMTEST1_EMEML4EN_OFF (18u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML5EN */
#define IFX_MTU_MEMTEST1_EMEML5EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML5EN */
#define IFX_MTU_MEMTEST1_EMEML5EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML5EN */
#define IFX_MTU_MEMTEST1_EMEML5EN_OFF (19u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML6EN */
#define IFX_MTU_MEMTEST1_EMEML6EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML6EN */
#define IFX_MTU_MEMTEST1_EMEML6EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML6EN */
#define IFX_MTU_MEMTEST1_EMEML6EN_OFF (20u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEML7EN */
#define IFX_MTU_MEMTEST1_EMEML7EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEML7EN */
#define IFX_MTU_MEMTEST1_EMEML7EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEML7EN */
#define IFX_MTU_MEMTEST1_EMEML7EN_OFF (21u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEMLXEN */
#define IFX_MTU_MEMTEST1_EMEMLXEN_LEN (8u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEMLXEN */
#define IFX_MTU_MEMTEST1_EMEMLXEN_MSK (0xffu)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEMLXEN */
#define IFX_MTU_MEMTEST1_EMEMLXEN_OFF (22u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.EMEMUXEN */
#define IFX_MTU_MEMTEST1_EMEMUXEN_LEN (2u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.EMEMUXEN */
#define IFX_MTU_MEMTEST1_EMEMUXEN_MSK (0x3u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.EMEMUXEN */
#define IFX_MTU_MEMTEST1_EMEMUXEN_OFF (30u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.ERAY0MEN */
#define IFX_MTU_MEMTEST1_ERAY0MEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.ERAY0MEN */
#define IFX_MTU_MEMTEST1_ERAY0MEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.ERAY0MEN */
#define IFX_MTU_MEMTEST1_ERAY0MEN_OFF (8u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.ERAY0OEN */
#define IFX_MTU_MEMTEST1_ERAY0OEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.ERAY0OEN */
#define IFX_MTU_MEMTEST1_ERAY0OEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.ERAY0OEN */
#define IFX_MTU_MEMTEST1_ERAY0OEN_OFF (6u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.ERAY0TEN */
#define IFX_MTU_MEMTEST1_ERAY0TEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.ERAY0TEN */
#define IFX_MTU_MEMTEST1_ERAY0TEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.ERAY0TEN */
#define IFX_MTU_MEMTEST1_ERAY0TEN_OFF (7u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.ERAY1XEN */
#define IFX_MTU_MEMTEST1_ERAY1XEN_LEN (3u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.ERAY1XEN */
#define IFX_MTU_MEMTEST1_ERAY1XEN_MSK (0x7u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.ERAY1XEN */
#define IFX_MTU_MEMTEST1_ERAY1XEN_OFF (9u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.GTM1BEN */
#define IFX_MTU_MEMTEST1_GTM1BEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.GTM1BEN */
#define IFX_MTU_MEMTEST1_GTM1BEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.GTM1BEN */
#define IFX_MTU_MEMTEST1_GTM1BEN_OFF (0u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.GTM2EN */
#define IFX_MTU_MEMTEST1_GTM2EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.GTM2EN */
#define IFX_MTU_MEMTEST1_GTM2EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.GTM2EN */
#define IFX_MTU_MEMTEST1_GTM2EN_OFF (1u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.MCAN0EN */
#define IFX_MTU_MEMTEST1_MCAN0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.MCAN0EN */
#define IFX_MTU_MEMTEST1_MCAN0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.MCAN0EN */
#define IFX_MTU_MEMTEST1_MCAN0EN_OFF (4u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.MCAN1EN */
#define IFX_MTU_MEMTEST1_MCAN1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.MCAN1EN */
#define IFX_MTU_MEMTEST1_MCAN1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.MCAN1EN */
#define IFX_MTU_MEMTEST1_MCAN1EN_OFF (5u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.MCDSEN */
#define IFX_MTU_MEMTEST1_MCDSEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.MCDSEN */
#define IFX_MTU_MEMTEST1_MCDSEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.MCDSEN */
#define IFX_MTU_MEMTEST1_MCDSEN_OFF (13u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.PSI5EN */
#define IFX_MTU_MEMTEST1_PSI5EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.PSI5EN */
#define IFX_MTU_MEMTEST1_PSI5EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.PSI5EN */
#define IFX_MTU_MEMTEST1_PSI5EN_OFF (2u)

/** \brief  Length for Ifx_MTU_MEMTEST1_Bits.STBY1EN */
#define IFX_MTU_MEMTEST1_STBY1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST1_Bits.STBY1EN */
#define IFX_MTU_MEMTEST1_STBY1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST1_Bits.STBY1EN */
#define IFX_MTU_MEMTEST1_STBY1EN_OFF (12u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.CIF0EN */
#define IFX_MTU_MEMTEST2_CIF0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.CIF0EN */
#define IFX_MTU_MEMTEST2_CIF0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.CIF0EN */
#define IFX_MTU_MEMTEST2_CIF0EN_OFF (14u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.CIF1EN */
#define IFX_MTU_MEMTEST2_CIF1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.CIF1EN */
#define IFX_MTU_MEMTEST2_CIF1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.CIF1EN */
#define IFX_MTU_MEMTEST2_CIF1EN_OFF (16u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.CIF2EN */
#define IFX_MTU_MEMTEST2_CIF2EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.CIF2EN */
#define IFX_MTU_MEMTEST2_CIF2EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.CIF2EN */
#define IFX_MTU_MEMTEST2_CIF2EN_OFF (17u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.DAMEN */
#define IFX_MTU_MEMTEST2_DAMEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.DAMEN */
#define IFX_MTU_MEMTEST2_DAMEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.DAMEN */
#define IFX_MTU_MEMTEST2_DAMEN_OFF (15u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.DMAEN */
#define IFX_MTU_MEMTEST2_DMAEN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.DMAEN */
#define IFX_MTU_MEMTEST2_DMAEN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.DMAEN */
#define IFX_MTU_MEMTEST2_DMAEN_OFF (19u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.EMEMUxEN */
#define IFX_MTU_MEMTEST2_EMEMUXEN_LEN (14u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.EMEMUxEN */
#define IFX_MTU_MEMTEST2_EMEMUXEN_MSK (0x3fffu)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.EMEMUxEN */
#define IFX_MTU_MEMTEST2_EMEMUXEN_OFF (0u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.FFT0EN */
#define IFX_MTU_MEMTEST2_FFT0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.FFT0EN */
#define IFX_MTU_MEMTEST2_FFT0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.FFT0EN */
#define IFX_MTU_MEMTEST2_FFT0EN_OFF (22u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.FFT1EN */
#define IFX_MTU_MEMTEST2_FFT1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.FFT1EN */
#define IFX_MTU_MEMTEST2_FFT1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.FFT1EN */
#define IFX_MTU_MEMTEST2_FFT1EN_OFF (23u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.STBY2EN */
#define IFX_MTU_MEMTEST2_STBY2EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.STBY2EN */
#define IFX_MTU_MEMTEST2_STBY2EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.STBY2EN */
#define IFX_MTU_MEMTEST2_STBY2EN_OFF (18u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.XTM0EN */
#define IFX_MTU_MEMTEST2_XTM0EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.XTM0EN */
#define IFX_MTU_MEMTEST2_XTM0EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.XTM0EN */
#define IFX_MTU_MEMTEST2_XTM0EN_OFF (20u)

/** \brief  Length for Ifx_MTU_MEMTEST2_Bits.XTM1EN */
#define IFX_MTU_MEMTEST2_XTM1EN_LEN (1u)

/** \brief  Mask for Ifx_MTU_MEMTEST2_Bits.XTM1EN */
#define IFX_MTU_MEMTEST2_XTM1EN_MSK (0x1u)

/** \brief  Offset for Ifx_MTU_MEMTEST2_Bits.XTM1EN */
#define IFX_MTU_MEMTEST2_XTM1EN_OFF (21u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXMTU_BF_H */
