	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18580a --dep-file=IfxEth_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_CRSDVA_P11_11_IN',data,rom,cluster('IfxEth_CRSDVA_P11_11_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_CRSDVA_P11_11_IN'
	.global	IfxEth_CRSDVA_P11_11_IN
	.align	4
IfxEth_CRSDVA_P11_11_IN:	.type	object
	.size	IfxEth_CRSDVA_P11_11_IN,16
	.word	-268316672,-268193536
	.byte	11
	.space	7
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDC_P02_8_OUT',data,rom,cluster('IfxEth_MDC_P02_8_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDC_P02_8_OUT'
	.global	IfxEth_MDC_P02_8_OUT
	.align	4
IfxEth_MDC_P02_8_OUT:	.type	object
	.size	IfxEth_MDC_P02_8_OUT,16
	.word	-268316672,-268197376
	.byte	8
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDC_P21_0_OUT',data,rom,cluster('IfxEth_MDC_P21_0_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDC_P21_0_OUT'
	.global	IfxEth_MDC_P21_0_OUT
	.align	4
IfxEth_MDC_P21_0_OUT:	.type	object
	.size	IfxEth_MDC_P21_0_OUT,16
	.word	-268316672,-268189440
	.space	4
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDC_P21_2_OUT',data,rom,cluster('IfxEth_MDC_P21_2_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDC_P21_2_OUT'
	.global	IfxEth_MDC_P21_2_OUT
	.align	4
IfxEth_MDC_P21_2_OUT:	.type	object
	.size	IfxEth_MDC_P21_2_OUT,16
	.word	-268316672,-268189440
	.byte	2
	.space	3
	.byte	168
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDIOA_P00_0_INOUT',data,rom,cluster('IfxEth_MDIOA_P00_0_INOUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDIOA_P00_0_INOUT'
	.global	IfxEth_MDIOA_P00_0_INOUT
	.align	4
IfxEth_MDIOA_P00_0_INOUT:	.type	object
	.size	IfxEth_MDIOA_P00_0_INOUT,16
	.word	-268316672,-268197888
	.space	5
	.byte	136
	.space	2
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDIOD_P21_3_INOUT',data,rom,cluster('IfxEth_MDIOD_P21_3_INOUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDIOD_P21_3_INOUT'
	.global	IfxEth_MDIOD_P21_3_INOUT
	.align	4
IfxEth_MDIOD_P21_3_INOUT:	.type	object
	.size	IfxEth_MDIOD_P21_3_INOUT,16
	.word	-268316672,-268189440
	.byte	3
	.space	3
	.byte	3,136
	.space	2
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_MDIO_P21_1_INOUT',data,rom,cluster('IfxEth_MDIO_P21_1_INOUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_MDIO_P21_1_INOUT'
	.global	IfxEth_MDIO_P21_1_INOUT
	.align	4
IfxEth_MDIO_P21_1_INOUT:	.type	object
	.size	IfxEth_MDIO_P21_1_INOUT,16
	.word	-268316672,-268189440
	.byte	1
	.space	3
	.byte	1,176
	.space	2
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_REFCLK_P11_12_IN',data,rom,cluster('IfxEth_REFCLK_P11_12_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_REFCLK_P11_12_IN'
	.global	IfxEth_REFCLK_P11_12_IN
	.align	4
IfxEth_REFCLK_P11_12_IN:	.type	object
	.size	IfxEth_REFCLK_P11_12_IN,16
	.word	-268316672,-268193536
	.byte	12
	.space	7
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_RXCLKA_P11_12_IN',data,rom,cluster('IfxEth_RXCLKA_P11_12_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_RXCLKA_P11_12_IN'
	.global	IfxEth_RXCLKA_P11_12_IN
	.align	4
IfxEth_RXCLKA_P11_12_IN:	.type	object
	.size	IfxEth_RXCLKA_P11_12_IN,16
	.word	-268316672,-268193536
	.byte	12
	.space	7
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_RXD0_P11_10_IN',data,rom,cluster('IfxEth_RXD0_P11_10_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_RXD0_P11_10_IN'
	.global	IfxEth_RXD0_P11_10_IN
	.align	4
IfxEth_RXD0_P11_10_IN:	.type	object
	.size	IfxEth_RXD0_P11_10_IN,16
	.word	-268316672,-268193536
	.byte	10
	.space	7
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_RXD1_P11_9_IN',data,rom,cluster('IfxEth_RXD1_P11_9_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_RXD1_P11_9_IN'
	.global	IfxEth_RXD1_P11_9_IN
	.align	4
IfxEth_RXD1_P11_9_IN:	.type	object
	.size	IfxEth_RXD1_P11_9_IN,16
	.word	-268316672,-268193536
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_RXERB_P21_7_IN',data,rom,cluster('IfxEth_RXERB_P21_7_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_RXERB_P21_7_IN'
	.global	IfxEth_RXERB_P21_7_IN
	.align	4
IfxEth_RXERB_P21_7_IN:	.type	object
	.size	IfxEth_RXERB_P21_7_IN,16
	.word	-268316672,-268189440
	.byte	7
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_TXCLKB_P11_12_IN',data,rom,cluster('IfxEth_TXCLKB_P11_12_IN')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_TXCLKB_P11_12_IN'
	.global	IfxEth_TXCLKB_P11_12_IN
	.align	4
IfxEth_TXCLKB_P11_12_IN:	.type	object
	.size	IfxEth_TXCLKB_P11_12_IN,16
	.word	-268316672,-268193536
	.byte	12
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_TXD0_P11_3_OUT',data,rom,cluster('IfxEth_TXD0_P11_3_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_TXD0_P11_3_OUT'
	.global	IfxEth_TXD0_P11_3_OUT
	.align	4
IfxEth_TXD0_P11_3_OUT:	.type	object
	.size	IfxEth_TXD0_P11_3_OUT,16
	.word	-268316672,-268193536
	.byte	3
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_TXD1_P11_2_OUT',data,rom,cluster('IfxEth_TXD1_P11_2_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_TXD1_P11_2_OUT'
	.global	IfxEth_TXD1_P11_2_OUT
	.align	4
IfxEth_TXD1_P11_2_OUT:	.type	object
	.size	IfxEth_TXD1_P11_2_OUT,16
	.word	-268316672,-268193536
	.byte	2
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxEth_PinMap.IfxEth_TXEN_P11_6_OUT',data,rom,cluster('IfxEth_TXEN_P11_6_OUT')
	.sect	'.rodata.IfxEth_PinMap.IfxEth_TXEN_P11_6_OUT'
	.global	IfxEth_TXEN_P11_6_OUT
	.align	4
IfxEth_TXEN_P11_6_OUT:	.type	object
	.size	IfxEth_TXEN_P11_6_OUT,16
	.word	-268316672,-268193536
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Crsdv_In_pinTable',data,cluster('IfxEth_Crsdv_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Crsdv_In_pinTable'
	.global	IfxEth_Crsdv_In_pinTable
	.align	4
IfxEth_Crsdv_In_pinTable:	.type	object
	.size	IfxEth_Crsdv_In_pinTable,4
	.word	IfxEth_CRSDVA_P11_11_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Mdc_Out_pinTable',data,cluster('IfxEth_Mdc_Out_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Mdc_Out_pinTable'
	.global	IfxEth_Mdc_Out_pinTable
	.align	4
IfxEth_Mdc_Out_pinTable:	.type	object
	.size	IfxEth_Mdc_Out_pinTable,12
	.word	IfxEth_MDC_P02_8_OUT,IfxEth_MDC_P21_0_OUT,IfxEth_MDC_P21_2_OUT
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Mdio_InOut_pinTable',data,cluster('IfxEth_Mdio_InOut_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Mdio_InOut_pinTable'
	.global	IfxEth_Mdio_InOut_pinTable
	.align	4
IfxEth_Mdio_InOut_pinTable:	.type	object
	.size	IfxEth_Mdio_InOut_pinTable,28
	.space	4
	.word	IfxEth_MDIOA_P00_0_INOUT
	.space	16
	.word	IfxEth_MDIO_P21_1_INOUT
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Refclk_In_pinTable',data,cluster('IfxEth_Refclk_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Refclk_In_pinTable'
	.global	IfxEth_Refclk_In_pinTable
	.align	4
IfxEth_Refclk_In_pinTable:	.type	object
	.size	IfxEth_Refclk_In_pinTable,4
	.word	IfxEth_REFCLK_P11_12_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Rxclk_In_pinTable',data,cluster('IfxEth_Rxclk_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Rxclk_In_pinTable'
	.global	IfxEth_Rxclk_In_pinTable
	.align	4
IfxEth_Rxclk_In_pinTable:	.type	object
	.size	IfxEth_Rxclk_In_pinTable,4
	.word	IfxEth_RXCLKA_P11_12_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Rxd_In_pinTable',data,cluster('IfxEth_Rxd_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Rxd_In_pinTable'
	.global	IfxEth_Rxd_In_pinTable
	.align	4
IfxEth_Rxd_In_pinTable:	.type	object
	.size	IfxEth_Rxd_In_pinTable,4
	.word	IfxEth_RXD1_P11_9_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Rxer_In_pinTable',data,cluster('IfxEth_Rxer_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Rxer_In_pinTable'
	.global	IfxEth_Rxer_In_pinTable
	.align	4
IfxEth_Rxer_In_pinTable:	.type	object
	.size	IfxEth_Rxer_In_pinTable,8
	.space	4
	.word	IfxEth_RXERB_P21_7_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Txclk_In_pinTable',data,cluster('IfxEth_Txclk_In_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Txclk_In_pinTable'
	.global	IfxEth_Txclk_In_pinTable
	.align	4
IfxEth_Txclk_In_pinTable:	.type	object
	.size	IfxEth_Txclk_In_pinTable,8
	.space	4
	.word	IfxEth_TXCLKB_P11_12_IN
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Txd_Out_pinTable',data,cluster('IfxEth_Txd_Out_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Txd_Out_pinTable'
	.global	IfxEth_Txd_Out_pinTable
	.align	4
IfxEth_Txd_Out_pinTable:	.type	object
	.size	IfxEth_Txd_Out_pinTable,8
	.word	IfxEth_TXD1_P11_2_OUT,IfxEth_TXD0_P11_3_OUT
	.sdecl	'.data.IfxEth_PinMap.IfxEth_Txen_Out_pinTable',data,cluster('IfxEth_Txen_Out_pinTable')
	.sect	'.data.IfxEth_PinMap.IfxEth_Txen_Out_pinTable'
	.global	IfxEth_Txen_Out_pinTable
	.align	4
IfxEth_Txen_Out_pinTable:	.type	object
	.size	IfxEth_Txen_Out_pinTable,4
	.word	IfxEth_TXEN_P11_6_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	78497
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1418
	.byte	4,2,35,0,0,14,4
	.word	492
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,14,24
	.word	492
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3040
	.byte	4,2,35,0,0,14,8
	.word	492
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,14,12
	.word	492
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6589
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6769
	.byte	4,2,35,0,0,14,76
	.word	492
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	807
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1497
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1721
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1936
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2153
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2373
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1537
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2687
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2727
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3000
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3316
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3356
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3656
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3696
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4031
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4317
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3356
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4464
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4633
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4805
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4980
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5154
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5328
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5504
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5660
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5993
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6341
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3356
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6465
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6714
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6973
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7013
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7069
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7636
	.byte	4,3,35,252,1,0,16
	.word	7676
	.byte	3
	.word	8279
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8284
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	492
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8289
	.byte	6,0,19
	.word	245
	.byte	20
	.word	271
	.byte	6,0,19
	.word	306
	.byte	20
	.word	338
	.byte	6,0,19
	.word	388
	.byte	20
	.word	407
	.byte	6,0,19
	.word	423
	.byte	20
	.word	438
	.byte	20
	.word	452
	.byte	6,0,19
	.word	8392
	.byte	20
	.word	8420
	.byte	20
	.word	8434
	.byte	20
	.word	8452
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8545
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	469
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	485
	.byte	22,1,3
	.word	8613
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8615
	.byte	10
	.byte	'_Ifx_ETH_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_ACCEN0_Bits',0,6,79,3
	.word	8638
	.byte	10
	.byte	'_Ifx_ETH_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_ACCEN1_Bits',0,6,85,3
	.word	9195
	.byte	10
	.byte	'_Ifx_ETH_AHB_OR_AXI_STATUS_Bits',0,6,88,16,4,11
	.byte	'AXWHSTS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'AXIRDSTS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ETH_AHB_OR_AXI_STATUS_Bits',0,6,93,3
	.word	9272
	.byte	10
	.byte	'_Ifx_ETH_BUS_MODE_Bits',0,6,96,16,4,11
	.byte	'SWR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'DA',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'DSL',0,1
	.word	492
	.byte	5,1,2,35,0,11
	.byte	'ATDS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PBL',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PR',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'FB',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RPBL',0,1
	.word	492
	.byte	6,1,2,35,2,11
	.byte	'USP',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PBLx8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'AAL',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'MB',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'TXPR',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PRWG',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ETH_BUS_MODE_Bits',0,6,113,3
	.word	9410
	.byte	10
	.byte	'_Ifx_ETH_CLC_Bits',0,6,116,16,4,11
	.byte	'DISR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ETH_CLC_Bits',0,6,121,3
	.word	9704
	.byte	10
	.byte	'_Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_Bits',0,6,124,16,4,11
	.byte	'CURRBUFAPTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS_Bits',0,6,127,3
	.word	9807
	.byte	10
	.byte	'_Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_Bits',0,6,130,1,16,4,11
	.byte	'CURRDESAPTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR_Bits',0,6,133,1,3
	.word	9943
	.byte	10
	.byte	'_Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_Bits',0,6,136,1,16,4,11
	.byte	'CURTBUFAPTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS_Bits',0,6,139,1,3
	.word	10073
	.byte	10
	.byte	'_Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_Bits',0,6,142,1,16,4,11
	.byte	'CURTDESAPTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR_Bits',0,6,145,1,3
	.word	10213
	.byte	10
	.byte	'_Ifx_ETH_DEBUG_Bits',0,6,148,1,16,4,11
	.byte	'RPESTS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RFCFCSTS',0,1
	.word	492
	.byte	2,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'RWCSTS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'RRCSTS',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RXFSTS',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'TPESTS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'TFCSTS',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'TXPAUSED',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'TRCSTS',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'TWCSTS',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'TXFSTS',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'TXSTSFSTS',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_DEBUG_Bits',0,6,167,1,3
	.word	10345
	.byte	10
	.byte	'_Ifx_ETH_FLOW_CONTROL_Bits',0,6,170,1,16,4,11
	.byte	'FCA_BPA',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TFE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RFE',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PLT',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'DZPQ',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PT',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_FLOW_CONTROL_Bits',0,6,181,1,3
	.word	10736
	.byte	10
	.byte	'_Ifx_ETH_GMII_ADDRESS_Bits',0,6,184,1,16,4,11
	.byte	'GB',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'GW',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CR',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'GR',0,2
	.word	509
	.byte	5,5,2,35,0,11
	.byte	'PA',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_GMII_ADDRESS_Bits',0,6,192,1,3
	.word	10957
	.byte	10
	.byte	'_Ifx_ETH_GMII_DATA_Bits',0,6,195,1,16,4,11
	.byte	'GD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_GMII_DATA_Bits',0,6,199,1,3
	.word	11119
	.byte	10
	.byte	'_Ifx_ETH_GPCTL_Bits',0,6,202,1,16,4,11
	.byte	'ALTI0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ALTI1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'ALTI2',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'ALTI3',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'ALTI4',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'ALTI5',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'ALTI6',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'ALTI7',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'ALTI8',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'ALTI9',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'ALTI10',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'EPR',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DIV',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_GPCTL_Bits',0,6,219,1,3
	.word	11219
	.byte	10
	.byte	'_Ifx_ETH_HASH_TABLE_HIGH_Bits',0,6,222,1,16,4,11
	.byte	'HTH',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_HASH_TABLE_HIGH_Bits',0,6,225,1,3
	.word	11538
	.byte	10
	.byte	'_Ifx_ETH_HASH_TABLE_LOW_Bits',0,6,228,1,16,4,11
	.byte	'HTL',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_HASH_TABLE_LOW_Bits',0,6,231,1,3
	.word	11628
	.byte	10
	.byte	'_Ifx_ETH_HW_FEATURE_Bits',0,6,234,1,16,4,11
	.byte	'MIISEL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'GMIISEL',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'HDSEL',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EXTHASHEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'HASHSEL',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ADDMACADRSEL',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PCSSEL',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'L3L4FLTREN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'SMASEL',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RWKSEL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'MGKSEL',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'MMCSEL',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'TSVER1SEL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TSVER2SEL',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EEESEL',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'AVSEL',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'TXCOESEL',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RXTYP1COE',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RXTYP2COE',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'RXFIFOSIZE',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'RXCHCNT',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'TXCHCNT',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'ENHDESSEL',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'INTTSEN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'FLEXIPPSEN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'SAVLANINS',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'ACTPHYIF',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_HW_FEATURE_Bits',0,6,136,2,3
	.word	11716
	.byte	10
	.byte	'_Ifx_ETH_ID_Bits',0,6,139,2,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_ID_Bits',0,6,144,2,3
	.word	12336
	.byte	10
	.byte	'_Ifx_ETH_INTERRUPT_ENABLE_Bits',0,6,147,2,16,4,11
	.byte	'TIE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TSE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TUE',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TJE',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVE',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UNE',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RIE',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RUE',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RSE',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RWE',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'ETE',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'FBE',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'ERE',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'AIE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'NIE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	15,0,2,35,2,0,21
	.byte	'Ifx_ETH_INTERRUPT_ENABLE_Bits',0,6,166,2,3
	.word	12443
	.byte	10
	.byte	'_Ifx_ETH_INTERRUPT_MASK_Bits',0,6,169,2,16,4,11
	.byte	'RGSMIIIM',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PCSLCHGIM',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PCSANCIM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PMTIM',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	5,7,2,35,0,11
	.byte	'TSIM',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LPIIM',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_ETH_INTERRUPT_MASK_Bits',0,6,179,2,3
	.word	12791
	.byte	10
	.byte	'_Ifx_ETH_INTERRUPT_STATUS_Bits',0,6,182,2,16,4,11
	.byte	'RGSMIIIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PCSLCHGIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PCSANCIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PMTIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'MMCIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'MMCRXIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'MMCTXIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MMCRXIPIS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TSIS',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LPIIS',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_ETH_INTERRUPT_STATUS_Bits',0,6,196,2,3
	.word	13020
	.byte	10
	.byte	'_Ifx_ETH_KRST0_Bits',0,6,199,2,16,4,11
	.byte	'RST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ETH_KRST0_Bits',0,6,204,2,3
	.word	13329
	.byte	10
	.byte	'_Ifx_ETH_KRST1_Bits',0,6,207,2,16,4,11
	.byte	'RST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ETH_KRST1_Bits',0,6,211,2,3
	.word	13440
	.byte	10
	.byte	'_Ifx_ETH_KRSTCLR_Bits',0,6,214,2,16,4,11
	.byte	'CLR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_ETH_KRSTCLR_Bits',0,6,218,2,3
	.word	13532
	.byte	10
	.byte	'_Ifx_ETH_MAC_ADDRESS_HIGH_Bits',0,6,221,2,16,4,11
	.byte	'ADDRHI',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'MBC',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'SA',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'AE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_MAC_ADDRESS_HIGH_Bits',0,6,228,2,3
	.word	13628
	.byte	10
	.byte	'_Ifx_ETH_MAC_ADDRESS_LOW_Bits',0,6,231,2,16,4,11
	.byte	'ADDRLO',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_MAC_ADDRESS_LOW_Bits',0,6,234,2,3
	.word	13789
	.byte	10
	.byte	'_Ifx_ETH_MAC_CONFIGURATION_Bits',0,6,237,2,16,4,11
	.byte	'PRELEN',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'RE',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TE',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'DC',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'BL',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ACS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'LUD',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'DR',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'IPC',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'LM',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'DO',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'FES',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'DCRS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IFG',0,1
	.word	492
	.byte	3,4,2,35,2,11
	.byte	'JE',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'BE',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'JD',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'WD',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'TC',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'SFTERR',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'TWOKPE',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'SARC',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_MAC_CONFIGURATION_Bits',0,6,137,3,3
	.word	13882
	.byte	10
	.byte	'_Ifx_ETH_MAC_FRAME_FILTER_Bits',0,6,140,3,16,4,11
	.byte	'PR',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'HUC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'HMC',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DAIF',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PM',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'DBF',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PCF',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SAIF',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SAF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HPF',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'VTFE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	3,4,2,35,2,11
	.byte	'IPFE',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'DNTU',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	9,1,2,35,2,11
	.byte	'RA',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_MAC_FRAME_FILTER_Bits',0,6,159,3,3
	.word	14356
	.byte	10
	.byte	'_Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits',0,6,162,3,16,4,11
	.byte	'MISFRMCNT',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'MISCNTOVF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVFFRMCNT',0,2
	.word	509
	.byte	11,4,2,35,2,11
	.byte	'OVFCNTOVF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER_Bits',0,6,169,3,3
	.word	14714
	.byte	10
	.byte	'_Ifx_ETH_MMC_CONTROL_Bits',0,6,172,3,16,4,11
	.byte	'CNTRST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CNTSTOPRO',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RSTONRD',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'CNTFREEZ',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'CNTPRST',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CNTPRSTLVL',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'UCDBC',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_CONTROL_Bits',0,6,183,3,3
	.word	14946
	.byte	10
	.byte	'_Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits',0,6,186,3,16,4,11
	.byte	'RXIPV4GFIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RXIPV4HERFIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RXIPV4NOPAYFIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'RXIPV4FRAGFIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'RXIPV4UDSBLFIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'RXIPV6GFIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RXIPV6HERFIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RXIPV6NOPAYFIS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RXUDPGFIS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RXUDPERFIS',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'RXTCPGFIS',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'RXTCPERFIS',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'RXICMPGFIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'RXICMPERFIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RXIPV4GOIS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RXIPV4HEROIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RXIPV4NOPAYOIS',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'RXIPV4FRAGOIS',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'RXIPV4UDSBLOIS',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'RXIPV6GOIS',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'RXIPV6HEROIS',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'RXIPV6NOPAYOIS',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'RXUDPGOIS',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'RXUDPEROIS',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RXTCPGOIS',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'RXTCPEROIS',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RXICMPGOIS',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'RXICMPEROIS',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_Bits',0,6,218,3,3
	.word	15193
	.byte	10
	.byte	'_Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits',0,6,221,3,16,4,11
	.byte	'RXIPV4GFIM',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RXIPV4HERFIM',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RXIPV4NOPAYFIM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'RXIPV4FRAGFIM',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'RXIPV4UDSBLFIM',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'RXIPV6GFIM',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RXIPV6HERFIM',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RXIPV6NOPAYFIM',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RXUDPGFIM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RXUDPERFIM',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'RXTCPGFIM',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'RXTCPERFIM',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'RXICMPGFIM',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'RXICMPERFIM',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RXIPV4GOIM',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RXIPV4HEROIM',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RXIPV4NOPAYOIM',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'RXIPV4FRAGOIM',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'RXIPV4UDSBLOIM',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'RXIPV6GOIM',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'RXIPV6HEROIM',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'RXIPV6NOPAYOIM',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'RXUDPGOIM',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'RXUDPEROIM',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RXTCPGOIM',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'RXTCPEROIM',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RXICMPGOIM',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'RXICMPEROIM',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK_Bits',0,6,253,3,3
	.word	15986
	.byte	10
	.byte	'_Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits',0,6,128,4,16,4,11
	.byte	'RXGBFRMIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RXGBOCTIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RXGOCTIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'RXBCGFIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'RXMCGFIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'RXCRCERFIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RXALGNERFIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RXRUNTFIS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RXJABERFIS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RXUSIZEGFIS',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'RXOSIZEGFIS',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'RX64OCTGBFIS',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'RX65T127OCTGBFIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'RX128T255OCTGBFIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'RX256T511OCTGBFIS',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'RX512T1023OCTGBFIS',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'RX1024TMAXOCTGBFIS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RXUCGFIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RXLENERFIS',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'RXORANGEFIS',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'RXPAUSFIS',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'RXFOVFIS',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'RXVLANGBFIS',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'RXWDOGFIS',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'RXRCVERRFIS',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'RXCTRLFIS',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_RECEIVE_INTERRUPT_Bits',0,6,157,4,3
	.word	16789
	.byte	10
	.byte	'_Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits',0,6,160,4,16,4,11
	.byte	'RXGBFRMIM',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RXGBOCTIM',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RXGOCTIM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'RXBCGFIM',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'RXMCGFIM',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'RXCRCERFIM',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RXALGNERFIM',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RXRUNTFIM',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RXJABERFIM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RXUSIZEGFIM',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'RXOSIZEGFIM',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'RX64OCTGBFIM',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'RX65T127OCTGBFIM',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'RX128T255OCTGBFIM',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'RX256T511OCTGBFIM',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'RX512T1023OCTGBFIM',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'RX1024TMAXOCTGBFIM',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RXUCGFIM',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RXLENERFIM',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'RXORANGEFIM',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'RXPAUSFIM',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'RXFOVFIM',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'RXVLANGBFIM',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'RXWDOGFIM',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'RXRCVERRFIM',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'RXCTRLFIM',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK_Bits',0,6,189,4,3
	.word	17499
	.byte	10
	.byte	'_Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits',0,6,192,4,16,4,11
	.byte	'TXGBOCTIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TXGBFRMIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TXBCGFIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TXMCGFIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TX64OCTGBFIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'TX65T127OCTGBFIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'TX128T255OCTGBFIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'TX256T511OCTGBFIS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TX512T1023OCTGBFIS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TX1024TMAXOCTGBFIS',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'TXUCGBFIS',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'TXMCGBFIS',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'TXBCGBFIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TXUFLOWERFIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TXSCOLGFIS',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TXMCOLGFIS',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'TXDEFFIS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'TXLATCOLFIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'TXEXCOLFIS',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TXCARERFIS',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'TXGOCTIS',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TXGFRMIS',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TXEXDEFFIS',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'TXPAUSFIS',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'TXVLANGFIS',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'TXOSIZEGFIS',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_TRANSMIT_INTERRUPT_Bits',0,6,221,4,3
	.word	18219
	.byte	10
	.byte	'_Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits',0,6,224,4,16,4,11
	.byte	'TXGBOCTIM',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TXGBFRMIM',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TXBCGFIM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TXMCGFIM',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TX64OCTGBFIM',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'TX65T127OCTGBFIM',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'TX128T255OCTGBFIM',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'TX256T511OCTGBFIM',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TX512T1023OCTGBFIM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TX1024TMAXOCTGBFIM',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'TXUCGBFIM',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'TXMCGBFIM',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'TXBCGBFIM',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TXUFLOWERFIM',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TXSCOLGFIM',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TXMCOLGFIM',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'TXDEFFIM',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'TXLATCOLFIM',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'TXEXCOLFIM',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TXCARERFIM',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'TXGOCTIM',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TXGFRMIM',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TXEXDEFFIM',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'TXPAUSFIM',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'TXVLANGFIM',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'TXOSIZEGFIM',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK_Bits',0,6,253,4,3
	.word	18929
	.byte	10
	.byte	'_Ifx_ETH_OPERATION_MODE_Bits',0,6,128,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SR',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSF',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'RTC',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FUF',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FEF',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EFC',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RFA',0,1
	.word	492
	.byte	2,5,2,35,1,11
	.byte	'RFD',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'ST',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TTC',0,4
	.word	469
	.byte	3,15,2,35,0,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	3,4,2,35,2,11
	.byte	'FTF',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TSF',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'RFD_2',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'RFA_2',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'DFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'RSF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'DT',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	5,0,2,35,3,0,21
	.byte	'Ifx_ETH_OPERATION_MODE_Bits',0,6,151,5,3
	.word	19649
	.byte	10
	.byte	'_Ifx_ETH_PMT_CONTROL_STATUS_Bits',0,6,154,5,16,4,11
	.byte	'PWRDWN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MGKPKTEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RWKPKTEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MGKPRCVD',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RWKPRCVD',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'GLBLUCAST',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	469
	.byte	21,1,2,35,0,11
	.byte	'RWKFILTRST',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_PMT_CONTROL_STATUS_Bits',0,6,166,5,3
	.word	20068
	.byte	10
	.byte	'_Ifx_ETH_PPS_CONTROL_Bits',0,6,169,5,16,4,11
	.byte	'PPSCTRL_PPSCMD',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PPSEN0',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'TRGTMODSEL0',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PPSCMD1',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'TRGTMODSEL1',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PPSCMD2',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	2,3,2,35,2,11
	.byte	'TRGTMODSEL2',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PPSCMD3',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	2,3,2,35,3,11
	.byte	'TRGTMODSEL3',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_PPS_CONTROL_Bits',0,6,187,5,3
	.word	20357
	.byte	10
	.byte	'_Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_Bits',0,6,190,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'RDESLA',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS_Bits',0,6,194,5,3
	.word	20777
	.byte	10
	.byte	'_Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_Bits',0,6,197,5,16,4,11
	.byte	'RIWT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER_Bits',0,6,201,5,3
	.word	20924
	.byte	10
	.byte	'_Ifx_ETH_RECEIVE_POLL_DEMAND_Bits',0,6,204,5,16,4,11
	.byte	'RPD',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_POLL_DEMAND_Bits',0,6,207,5,3
	.word	21071
	.byte	10
	.byte	'_Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER_Bits',0,6,210,5,16,4,11
	.byte	'WKUPFRMFTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER_Bits',0,6,213,5,3
	.word	21169
	.byte	10
	.byte	'_Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits',0,6,217,5,16,4,11
	.byte	'RX1024_MAXOCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits',0,6,220,5,3
	.word	21290
	.byte	10
	.byte	'_Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits',0,6,224,5,16,4,11
	.byte	'RX128_255OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits',0,6,227,5,3
	.word	21430
	.byte	10
	.byte	'_Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits',0,6,231,5,16,4,11
	.byte	'RX256_511OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits',0,6,234,5,3
	.word	21567
	.byte	10
	.byte	'_Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits',0,6,238,5,16,4,11
	.byte	'RX512_1023OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits',0,6,241,5,3
	.word	21704
	.byte	10
	.byte	'_Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_Bits',0,6,244,5,16,4,11
	.byte	'RX64OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD_Bits',0,6,247,5,3
	.word	21844
	.byte	10
	.byte	'_Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits',0,6,251,5,16,4,11
	.byte	'RX65_127OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits',0,6,254,5,3
	.word	21964
	.byte	10
	.byte	'_Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES_Bits',0,6,129,6,16,4,11
	.byte	'RXALGNERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES_Bits',0,6,132,6,3
	.word	22098
	.byte	10
	.byte	'_Ifx_ETH_RX_BROADCAST_FRAMES_GOOD_Bits',0,6,135,6,16,4,11
	.byte	'RXBCASTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_BROADCAST_FRAMES_GOOD_Bits',0,6,138,6,3
	.word	22214
	.byte	10
	.byte	'_Ifx_ETH_RX_CONTROL_FRAMES_GOOD_Bits',0,6,141,6,16,4,11
	.byte	'RXCTRLG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_CONTROL_FRAMES_GOOD_Bits',0,6,144,6,3
	.word	22327
	.byte	10
	.byte	'_Ifx_ETH_RX_CRC_ERROR_FRAMES_Bits',0,6,147,6,16,4,11
	.byte	'RXCRCERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_CRC_ERROR_FRAMES_Bits',0,6,150,6,3
	.word	22435
	.byte	10
	.byte	'_Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES_Bits',0,6,153,6,16,4,11
	.byte	'RXFIFOOVFL',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES_Bits',0,6,156,6,3
	.word	22538
	.byte	10
	.byte	'_Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD_Bits',0,6,159,6,16,4,11
	.byte	'RXFRMGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD_Bits',0,6,162,6,3
	.word	22651
	.byte	10
	.byte	'_Ifx_ETH_RX_JABBER_ERROR_FRAMES_Bits',0,6,165,6,16,4,11
	.byte	'RXJABERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_JABBER_ERROR_FRAMES_Bits',0,6,168,6,3
	.word	22763
	.byte	10
	.byte	'_Ifx_ETH_RX_LENGTH_ERROR_FRAMES_Bits',0,6,171,6,16,4,11
	.byte	'RXLENERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_LENGTH_ERROR_FRAMES_Bits',0,6,174,6,3
	.word	22872
	.byte	10
	.byte	'_Ifx_ETH_RX_MULTICAST_FRAMES_GOOD_Bits',0,6,177,6,16,4,11
	.byte	'RXMCASTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_MULTICAST_FRAMES_GOOD_Bits',0,6,180,6,3
	.word	22981
	.byte	10
	.byte	'_Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD_Bits',0,6,183,6,16,4,11
	.byte	'RXOCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD_Bits',0,6,186,6,3
	.word	23094
	.byte	10
	.byte	'_Ifx_ETH_RX_OCTET_COUNT_GOOD_Bits',0,6,189,6,16,4,11
	.byte	'RXOCTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OCTET_COUNT_GOOD_Bits',0,6,192,6,3
	.word	23204
	.byte	10
	.byte	'_Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_Bits',0,6,195,6,16,4,11
	.byte	'RXOUTOFRNG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES_Bits',0,6,198,6,3
	.word	23305
	.byte	10
	.byte	'_Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD_Bits',0,6,201,6,16,4,11
	.byte	'RXOVERSZG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD_Bits',0,6,204,6,3
	.word	23426
	.byte	10
	.byte	'_Ifx_ETH_RX_PAUSE_FRAMES_Bits',0,6,207,6,16,4,11
	.byte	'RXPAUSEFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_PAUSE_FRAMES_Bits',0,6,210,6,3
	.word	23538
	.byte	10
	.byte	'_Ifx_ETH_RX_RECEIVE_ERROR_FRAMES_Bits',0,6,213,6,16,4,11
	.byte	'RXRCVERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_RECEIVE_ERROR_FRAMES_Bits',0,6,216,6,3
	.word	23635
	.byte	10
	.byte	'_Ifx_ETH_RX_RUNT_ERROR_FRAMES_Bits',0,6,219,6,16,4,11
	.byte	'RXRUNTERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_RUNT_ERROR_FRAMES_Bits',0,6,222,6,3
	.word	23746
	.byte	10
	.byte	'_Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD_Bits',0,6,225,6,16,4,11
	.byte	'RXUNDERSZG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD_Bits',0,6,228,6,3
	.word	23852
	.byte	10
	.byte	'_Ifx_ETH_RX_UNICAST_FRAMES_GOOD_Bits',0,6,231,6,16,4,11
	.byte	'RXUCASTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_UNICAST_FRAMES_GOOD_Bits',0,6,234,6,3
	.word	23967
	.byte	10
	.byte	'_Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD_Bits',0,6,237,6,16,4,11
	.byte	'RXVLANFRGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD_Bits',0,6,240,6,3
	.word	24076
	.byte	10
	.byte	'_Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES_Bits',0,6,243,6,16,4,11
	.byte	'RXWDGERR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES_Bits',0,6,246,6,3
	.word	24189
	.byte	10
	.byte	'_Ifx_ETH_RXICMP_ERROR_FRAMES_Bits',0,6,249,6,16,4,11
	.byte	'RXICMPERRFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_ERROR_FRAMES_Bits',0,6,252,6,3
	.word	24302
	.byte	10
	.byte	'_Ifx_ETH_RXICMP_ERROR_OCTETS_Bits',0,6,255,6,16,4,11
	.byte	'RXICMPERROCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_ERROR_OCTETS_Bits',0,6,130,7,3
	.word	24409
	.byte	10
	.byte	'_Ifx_ETH_RXICMP_GOOD_FRAMES_Bits',0,6,133,7,16,4,11
	.byte	'RXICMPGDFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_GOOD_FRAMES_Bits',0,6,136,7,3
	.word	24516
	.byte	10
	.byte	'_Ifx_ETH_RXICMP_GOOD_OCTETS_Bits',0,6,139,7,16,4,11
	.byte	'RXICMPGDOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_GOOD_OCTETS_Bits',0,6,142,7,3
	.word	24620
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES_Bits',0,6,145,7,16,4,11
	.byte	'RXIPV4FRAGFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES_Bits',0,6,148,7,3
	.word	24724
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS_Bits',0,6,151,7,16,4,11
	.byte	'RXIPV4FRAGOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS_Bits',0,6,154,7,3
	.word	24842
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_GOOD_FRAMES_Bits',0,6,157,7,16,4,11
	.byte	'RXIPV4GDFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_GOOD_FRAMES_Bits',0,6,160,7,3
	.word	24960
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_GOOD_OCTETS_Bits',0,6,163,7,16,4,11
	.byte	'RXIPV4GDOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_GOOD_OCTETS_Bits',0,6,166,7,3
	.word	25064
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES_Bits',0,6,169,7,16,4,11
	.byte	'RXIPV4HDRERRFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES_Bits',0,6,172,7,3
	.word	25168
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS_Bits',0,6,175,7,16,4,11
	.byte	'RXIPV4HDRERROCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS_Bits',0,6,178,7,3
	.word	25292
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES_Bits',0,6,181,7,16,4,11
	.byte	'RXIPV4NOPAYFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES_Bits',0,6,184,7,3
	.word	25416
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS_Bits',0,6,187,7,16,4,11
	.byte	'RXIPV4NOPAYOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS_Bits',0,6,190,7,3
	.word	25535
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_Bits',0,6,193,7,16,4,11
	.byte	'RXIPV4UDSBLOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS_Bits',0,6,196,7,3
	.word	25654
	.byte	10
	.byte	'_Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_Bits',0,6,200,7,16,4,11
	.byte	'RXIPV4UDSBLFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES_Bits',0,6,203,7,3
	.word	25793
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_GOOD_FRAMES_Bits',0,6,206,7,16,4,11
	.byte	'RXIPV6GDFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_GOOD_FRAMES_Bits',0,6,209,7,3
	.word	25934
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_GOOD_OCTETS_Bits',0,6,212,7,16,4,11
	.byte	'RXIPV6GDOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_GOOD_OCTETS_Bits',0,6,215,7,3
	.word	26038
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES_Bits',0,6,218,7,16,4,11
	.byte	'RXIPV6HDRERRFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES_Bits',0,6,221,7,3
	.word	26142
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS_Bits',0,6,224,7,16,4,11
	.byte	'RXIPV6HDRERROCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS_Bits',0,6,227,7,3
	.word	26266
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES_Bits',0,6,230,7,16,4,11
	.byte	'RXIPV6NOPAYFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES_Bits',0,6,233,7,3
	.word	26390
	.byte	10
	.byte	'_Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS_Bits',0,6,236,7,16,4,11
	.byte	'RXIPV6NOPAYOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS_Bits',0,6,239,7,3
	.word	26509
	.byte	10
	.byte	'_Ifx_ETH_RXTCP_ERROR_FRAMES_Bits',0,6,242,7,16,4,11
	.byte	'RXTCPERRFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_ERROR_FRAMES_Bits',0,6,245,7,3
	.word	26628
	.byte	10
	.byte	'_Ifx_ETH_RXTCP_ERROR_OCTETS_Bits',0,6,248,7,16,4,11
	.byte	'RXTCPERROCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_ERROR_OCTETS_Bits',0,6,251,7,3
	.word	26732
	.byte	10
	.byte	'_Ifx_ETH_RXTCP_GOOD_FRAMES_Bits',0,6,254,7,16,4,11
	.byte	'RXTCPGDFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_GOOD_FRAMES_Bits',0,6,129,8,3
	.word	26836
	.byte	10
	.byte	'_Ifx_ETH_RXTCP_GOOD_OCTETS_Bits',0,6,132,8,16,4,11
	.byte	'RXTCPGDOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_GOOD_OCTETS_Bits',0,6,135,8,3
	.word	26937
	.byte	10
	.byte	'_Ifx_ETH_RXUDP_ERROR_FRAMES_Bits',0,6,138,8,16,4,11
	.byte	'RXUDPERRFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_ERROR_FRAMES_Bits',0,6,141,8,3
	.word	27038
	.byte	10
	.byte	'_Ifx_ETH_RXUDP_ERROR_OCTETS_Bits',0,6,144,8,16,4,11
	.byte	'RXUDPERROCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_ERROR_OCTETS_Bits',0,6,147,8,3
	.word	27142
	.byte	10
	.byte	'_Ifx_ETH_RXUDP_GOOD_FRAMES_Bits',0,6,150,8,16,4,11
	.byte	'RXUDPGDFRM',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_GOOD_FRAMES_Bits',0,6,153,8,3
	.word	27246
	.byte	10
	.byte	'_Ifx_ETH_RXUDP_GOOD_OCTETS_Bits',0,6,156,8,16,4,11
	.byte	'RXUDPGDOCT',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_GOOD_OCTETS_Bits',0,6,159,8,3
	.word	27347
	.byte	10
	.byte	'_Ifx_ETH_STATUS_Bits',0,6,162,8,16,4,11
	.byte	'TI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TPS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TU',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TJT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVF',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UNF',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'RI',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'RU',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'RPS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'RWT',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'ETI',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'FBI',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'ERI',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'AIS',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'NIS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'RS',0,1
	.word	492
	.byte	3,4,2,35,2,11
	.byte	'TS',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'EB',0,2
	.word	509
	.byte	3,6,2,35,2,11
	.byte	'GLI',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'GMI',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'GPI',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'TTI',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'GLPII',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_STATUS_Bits',0,6,189,8,3
	.word	27448
	.byte	10
	.byte	'_Ifx_ETH_SUB_SECOND_INCREMENT_Bits',0,6,192,8,16,4,11
	.byte	'SSINC',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_ETH_SUB_SECOND_INCREMENT_Bits',0,6,196,8,3
	.word	27891
	.byte	10
	.byte	'_Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_Bits',0,6,199,8,16,4,11
	.byte	'TSHWR',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS_Bits',0,6,203,8,3
	.word	28015
	.byte	10
	.byte	'_Ifx_ETH_SYSTEM_TIME_NANOSECONDS_Bits',0,6,206,8,16,4,11
	.byte	'TSSS',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_NANOSECONDS_Bits',0,6,210,8,3
	.word	28162
	.byte	10
	.byte	'_Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits',0,6,213,8,16,4,11
	.byte	'TSSS',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'ADDSUB',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE_Bits',0,6,217,8,3
	.word	28292
	.byte	10
	.byte	'_Ifx_ETH_SYSTEM_TIME_SECONDS_Bits',0,6,220,8,16,4,11
	.byte	'TSS',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_SECONDS_Bits',0,6,223,8,3
	.word	28431
	.byte	10
	.byte	'_Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE_Bits',0,6,226,8,16,4,11
	.byte	'TSS',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE_Bits',0,6,229,8,3
	.word	28529
	.byte	10
	.byte	'_Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits',0,6,232,8,16,4,11
	.byte	'TTSLO',0,4
	.word	469
	.byte	31,1,2,35,0,11
	.byte	'TRGTBUSY',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_ETH_TARGET_TIME_NANOSECONDS_Bits',0,6,236,8,3
	.word	28641
	.byte	10
	.byte	'_Ifx_ETH_TARGET_TIME_SECONDS_Bits',0,6,239,8,16,4,11
	.byte	'TSTR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TARGET_TIME_SECONDS_Bits',0,6,242,8,3
	.word	28769
	.byte	10
	.byte	'_Ifx_ETH_TIMESTAMP_ADDEND_Bits',0,6,245,8,16,4,11
	.byte	'TSAR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TIMESTAMP_ADDEND_Bits',0,6,248,8,3
	.word	28868
	.byte	10
	.byte	'_Ifx_ETH_TIMESTAMP_CONTROL_Bits',0,6,251,8,16,4,11
	.byte	'TSENA',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TSCFUPDT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TSINIT',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TSUPDT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TSTRIG',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'TSADDREG',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'TSENALL',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TSCTRLSSR',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'TSVER2ENA',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'TSIPENA',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'TSIPV6ENA',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TSIPV4ENA',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TSEVNTENA',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TSMSTRENA',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SNAPTYPSEL',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'TSENMACADDR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'ATSFC',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'ATSEN0',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'ATSEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'ATSEN2',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'ATSEN3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_ETH_TIMESTAMP_CONTROL_Bits',0,6,149,9,3
	.word	28961
	.byte	10
	.byte	'_Ifx_ETH_TIMESTAMP_STATUS_Bits',0,6,152,9,16,4,11
	.byte	'TSSOVF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'TSTARGT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'AUXTSTRIG',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'TSTRGTERR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TSTARGT1',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'TSTRGTERR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'TSTARGT2',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'TSTRGTERR2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TSTARGT3',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TSTRGTERR3',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'ATSSTN',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ATSSTM',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'ATSNS',0,1
	.word	492
	.byte	5,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_ETH_TIMESTAMP_STATUS_Bits',0,6,170,9,3
	.word	29517
	.byte	10
	.byte	'_Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_Bits',0,6,173,9,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'TDESLA',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS_Bits',0,6,177,9,3
	.word	29921
	.byte	10
	.byte	'_Ifx_ETH_TRANSMIT_POLL_DEMAND_Bits',0,6,180,9,16,4,11
	.byte	'TPD',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TRANSMIT_POLL_DEMAND_Bits',0,6,183,9,3
	.word	30070
	.byte	10
	.byte	'_Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits',0,6,187,9,16,4,11
	.byte	'TX1024_MAXOCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD_Bits',0,6,190,9,3
	.word	30170
	.byte	10
	.byte	'_Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits',0,6,194,9,16,4,11
	.byte	'TX128_255OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD_Bits',0,6,197,9,3
	.word	30310
	.byte	10
	.byte	'_Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits',0,6,201,9,16,4,11
	.byte	'TX256_511OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD_Bits',0,6,204,9,3
	.word	30447
	.byte	10
	.byte	'_Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits',0,6,208,9,16,4,11
	.byte	'TX512_1023OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD_Bits',0,6,211,9,3
	.word	30584
	.byte	10
	.byte	'_Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_Bits',0,6,214,9,16,4,11
	.byte	'TX64OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD_Bits',0,6,217,9,3
	.word	30724
	.byte	10
	.byte	'_Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits',0,6,221,9,16,4,11
	.byte	'TX65_127OCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD_Bits',0,6,224,9,3
	.word	30844
	.byte	10
	.byte	'_Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_Bits',0,6,227,9,16,4,11
	.byte	'TXBCASTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD_Bits',0,6,230,9,3
	.word	30978
	.byte	10
	.byte	'_Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_Bits',0,6,233,9,16,4,11
	.byte	'TXBCASTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_Bits',0,6,236,9,3
	.word	31100
	.byte	10
	.byte	'_Ifx_ETH_TX_CARRIER_ERROR_FRAMES_Bits',0,6,239,9,16,4,11
	.byte	'TXCARR',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_CARRIER_ERROR_FRAMES_Bits',0,6,242,9,3
	.word	31213
	.byte	10
	.byte	'_Ifx_ETH_TX_DEFERRED_FRAMES_Bits',0,6,245,9,16,4,11
	.byte	'TXDEFRD',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_DEFERRED_FRAMES_Bits',0,6,248,9,3
	.word	31322
	.byte	10
	.byte	'_Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES_Bits',0,6,252,9,16,4,11
	.byte	'TXEXSCOL',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES_Bits',0,6,255,9,3
	.word	31422
	.byte	10
	.byte	'_Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_Bits',0,6,131,10,16,4,11
	.byte	'TXEXSDEF',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR_Bits',0,6,134,10,3
	.word	31545
	.byte	10
	.byte	'_Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD_Bits',0,6,137,10,16,4,11
	.byte	'TXFRMGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD_Bits',0,6,140,10,3
	.word	31664
	.byte	10
	.byte	'_Ifx_ETH_TX_FRAME_COUNT_GOOD_Bits',0,6,143,10,16,4,11
	.byte	'TXFRMG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_FRAME_COUNT_GOOD_Bits',0,6,146,10,3
	.word	31774
	.byte	10
	.byte	'_Ifx_ETH_TX_LATE_COLLISION_FRAMES_Bits',0,6,149,10,16,4,11
	.byte	'TXLATECOL',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_LATE_COLLISION_FRAMES_Bits',0,6,152,10,3
	.word	31875
	.byte	10
	.byte	'_Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_Bits',0,6,155,10,16,4,11
	.byte	'TXMCASTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD_Bits',0,6,158,10,3
	.word	31989
	.byte	10
	.byte	'_Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_Bits',0,6,161,10,16,4,11
	.byte	'TXMCASTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_Bits',0,6,164,10,3
	.word	32111
	.byte	10
	.byte	'_Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_Bits',0,6,168,10,16,4,11
	.byte	'TXMULTCOLG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES_Bits',0,6,171,10,3
	.word	32224
	.byte	10
	.byte	'_Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD_Bits',0,6,174,10,16,4,11
	.byte	'TXOCTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD_Bits',0,6,177,10,3
	.word	32357
	.byte	10
	.byte	'_Ifx_ETH_TX_OCTET_COUNT_GOOD_Bits',0,6,180,10,16,4,11
	.byte	'TXOCTG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OCTET_COUNT_GOOD_Bits',0,6,183,10,3
	.word	32467
	.byte	10
	.byte	'_Ifx_ETH_TX_OSIZE_FRAMES_GOOD_Bits',0,6,186,10,16,4,11
	.byte	'TXOSIZG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OSIZE_FRAMES_GOOD_Bits',0,6,189,10,3
	.word	32568
	.byte	10
	.byte	'_Ifx_ETH_TX_PAUSE_FRAMES_Bits',0,6,192,10,16,4,11
	.byte	'TXPAUSE',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_PAUSE_FRAMES_Bits',0,6,195,10,3
	.word	32672
	.byte	10
	.byte	'_Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_Bits',0,6,199,10,16,4,11
	.byte	'TXSNGLCOLG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES_Bits',0,6,202,10,3
	.word	32766
	.byte	10
	.byte	'_Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES_Bits',0,6,205,10,16,4,11
	.byte	'TXUNDRFLW',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES_Bits',0,6,208,10,3
	.word	32895
	.byte	10
	.byte	'_Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD_Bits',0,6,211,10,16,4,11
	.byte	'TXUCASTGB',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD_Bits',0,6,214,10,3
	.word	33011
	.byte	10
	.byte	'_Ifx_ETH_TX_VLAN_FRAMES_GOOD_Bits',0,6,217,10,16,4,11
	.byte	'TXVLANG',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_ETH_TX_VLAN_FRAMES_GOOD_Bits',0,6,220,10,3
	.word	33129
	.byte	10
	.byte	'_Ifx_ETH_VERSION_Bits',0,6,223,10,16,4,11
	.byte	'SNPSVER',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'USERVER',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_ETH_VERSION_Bits',0,6,228,10,3
	.word	33231
	.byte	10
	.byte	'_Ifx_ETH_VLAN_TAG_Bits',0,6,231,10,16,4,11
	.byte	'VL',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'ETV',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'VTIM',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'ESVL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'VTHM',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_ETH_VLAN_TAG_Bits',0,6,239,10,3
	.word	33351
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8638
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_ACCEN0',0,6,252,10,3
	.word	33512
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9195
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_ACCEN1',0,6,132,11,3
	.word	33576
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9272
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_AHB_OR_AXI_STATUS',0,6,140,11,3
	.word	33640
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9410
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_BUS_MODE',0,6,148,11,3
	.word	33715
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9704
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_CLC',0,6,156,11,3
	.word	33781
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9807
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_RECEIVE_BUFFER_ADDRESS',0,6,164,11,3
	.word	33842
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9943
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_RECEIVE_DESCRIPTOR',0,6,172,11,3
	.word	33935
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10073
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS',0,6,180,11,3
	.word	34024
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_CURRENT_HOST_TRANSMIT_DESCRIPTOR',0,6,188,11,3
	.word	34118
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10345
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_DEBUG',0,6,196,11,3
	.word	34208
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10736
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_FLOW_CONTROL',0,6,204,11,3
	.word	34271
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_GMII_ADDRESS',0,6,212,11,3
	.word	34341
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11119
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_GMII_DATA',0,6,220,11,3
	.word	34411
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_GPCTL',0,6,228,11,3
	.word	34478
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11538
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_HASH_TABLE_HIGH',0,6,236,11,3
	.word	34541
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11628
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_HASH_TABLE_LOW',0,6,244,11,3
	.word	34614
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11716
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_HW_FEATURE',0,6,252,11,3
	.word	34686
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12336
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_ID',0,6,132,12,3
	.word	34754
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12443
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_INTERRUPT_ENABLE',0,6,140,12,3
	.word	34814
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12791
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_INTERRUPT_MASK',0,6,148,12,3
	.word	34888
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13020
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_INTERRUPT_STATUS',0,6,156,12,3
	.word	34960
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13329
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_KRST0',0,6,164,12,3
	.word	35034
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13440
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_KRST1',0,6,172,12,3
	.word	35097
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13532
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_KRSTCLR',0,6,180,12,3
	.word	35160
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13628
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MAC_ADDRESS_HIGH',0,6,188,12,3
	.word	35225
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MAC_ADDRESS_LOW',0,6,196,12,3
	.word	35299
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13882
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MAC_CONFIGURATION',0,6,204,12,3
	.word	35372
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14356
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MAC_FRAME_FILTER',0,6,212,12,3
	.word	35447
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14714
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER',0,6,220,12,3
	.word	35521
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_CONTROL',0,6,228,12,3
	.word	35619
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15193
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT',0,6,236,12,3
	.word	35688
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15986
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_IPC_RECEIVE_INTERRUPT_MASK',0,6,244,12,3
	.word	35771
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16789
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_RECEIVE_INTERRUPT',0,6,252,12,3
	.word	35859
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17499
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_RECEIVE_INTERRUPT_MASK',0,6,132,13,3
	.word	35938
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18219
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_TRANSMIT_INTERRUPT',0,6,140,13,3
	.word	36022
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18929
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_MMC_TRANSMIT_INTERRUPT_MASK',0,6,148,13,3
	.word	36102
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19649
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_OPERATION_MODE',0,6,156,13,3
	.word	36187
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20068
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_PMT_CONTROL_STATUS',0,6,164,13,3
	.word	36259
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20357
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_PPS_CONTROL',0,6,172,13,3
	.word	36335
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20777
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_DESCRIPTOR_LIST_ADDRESS',0,6,180,13,3
	.word	36404
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20924
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_INTERRUPT_WATCHDOG_TIMER',0,6,188,13,3
	.word	36493
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21071
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RECEIVE_POLL_DEMAND',0,6,196,13,3
	.word	36583
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21169
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_REMOTE_WAKE_UP_FRAME_FILTER',0,6,204,13,3
	.word	36660
	.byte	12,6,208,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21290
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD',0,6,213,13,3
	.word	36745
	.byte	12,6,217,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21430
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_128TO255OCTETS_FRAMES_GOOD_BAD',0,6,222,13,3
	.word	36837
	.byte	12,6,226,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21567
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_256TO511OCTETS_FRAMES_GOOD_BAD',0,6,231,13,3
	.word	36928
	.byte	12,6,235,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21704
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_512TO1023OCTETS_FRAMES_GOOD_BAD',0,6,240,13,3
	.word	37019
	.byte	12,6,243,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21844
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_64OCTETS_FRAMES_GOOD_BAD',0,6,248,13,3
	.word	37111
	.byte	12,6,252,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21964
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_65TO127OCTETS_FRAMES_GOOD_BAD',0,6,129,14,3
	.word	37196
	.byte	12,6,132,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22098
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_ALIGNMENT_ERROR_FRAMES',0,6,137,14,3
	.word	37286
	.byte	12,6,140,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22214
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_BROADCAST_FRAMES_GOOD',0,6,145,14,3
	.word	37369
	.byte	12,6,148,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22327
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_CONTROL_FRAMES_GOOD',0,6,153,14,3
	.word	37451
	.byte	12,6,156,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22435
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_CRC_ERROR_FRAMES',0,6,161,14,3
	.word	37531
	.byte	12,6,164,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22538
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_FIFO_OVERFLOW_FRAMES',0,6,169,14,3
	.word	37608
	.byte	12,6,172,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22651
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_FRAMES_COUNT_GOOD_BAD',0,6,177,14,3
	.word	37689
	.byte	12,6,180,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22763
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_JABBER_ERROR_FRAMES',0,6,185,14,3
	.word	37771
	.byte	12,6,188,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22872
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_LENGTH_ERROR_FRAMES',0,6,193,14,3
	.word	37851
	.byte	12,6,196,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22981
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_MULTICAST_FRAMES_GOOD',0,6,201,14,3
	.word	37931
	.byte	12,6,204,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23204
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OCTET_COUNT_GOOD',0,6,209,14,3
	.word	38013
	.byte	12,6,212,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23094
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OCTET_COUNT_GOOD_BAD',0,6,217,14,3
	.word	38090
	.byte	12,6,220,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23305
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OUT_OF_RANGE_TYPE_FRAMES',0,6,225,14,3
	.word	38171
	.byte	12,6,228,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23426
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_OVERSIZE_FRAMES_GOOD',0,6,233,14,3
	.word	38256
	.byte	12,6,236,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23538
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_PAUSE_FRAMES',0,6,241,14,3
	.word	38337
	.byte	12,6,244,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23635
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_RECEIVE_ERROR_FRAMES',0,6,249,14,3
	.word	38410
	.byte	12,6,252,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_RUNT_ERROR_FRAMES',0,6,129,15,3
	.word	38491
	.byte	12,6,132,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23852
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_UNDERSIZE_FRAMES_GOOD',0,6,137,15,3
	.word	38569
	.byte	12,6,140,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23967
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_UNICAST_FRAMES_GOOD',0,6,145,15,3
	.word	38651
	.byte	12,6,148,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24076
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_VLAN_FRAMES_GOOD_BAD',0,6,153,15,3
	.word	38731
	.byte	12,6,156,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24189
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RX_WATCHDOG_ERROR_FRAMES',0,6,161,15,3
	.word	38812
	.byte	12,6,164,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_ERROR_FRAMES',0,6,169,15,3
	.word	38894
	.byte	12,6,172,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24409
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_ERROR_OCTETS',0,6,177,15,3
	.word	38971
	.byte	12,6,180,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24516
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_GOOD_FRAMES',0,6,185,15,3
	.word	39048
	.byte	12,6,188,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24620
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXICMP_GOOD_OCTETS',0,6,193,15,3
	.word	39124
	.byte	12,6,196,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24724
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_FRAGMENTED_FRAMES',0,6,201,15,3
	.word	39200
	.byte	12,6,204,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24842
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_FRAGMENTED_OCTETS',0,6,209,15,3
	.word	39282
	.byte	12,6,212,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24960
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_GOOD_FRAMES',0,6,217,15,3
	.word	39364
	.byte	12,6,220,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25064
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_GOOD_OCTETS',0,6,225,15,3
	.word	39440
	.byte	12,6,228,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25168
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_HEADER_ERROR_FRAMES',0,6,233,15,3
	.word	39516
	.byte	12,6,236,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25292
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_HEADER_ERROR_OCTETS',0,6,241,15,3
	.word	39600
	.byte	12,6,244,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25416
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_NO_PAYLOAD_FRAMES',0,6,249,15,3
	.word	39684
	.byte	12,6,252,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25535
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_NO_PAYLOAD_OCTETS',0,6,129,16,3
	.word	39766
	.byte	12,6,132,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS',0,6,137,16,3
	.word	39848
	.byte	12,6,141,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25793
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES',0,6,146,16,3
	.word	39940
	.byte	12,6,149,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25934
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_GOOD_FRAMES',0,6,154,16,3
	.word	40033
	.byte	12,6,157,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26038
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_GOOD_OCTETS',0,6,162,16,3
	.word	40109
	.byte	12,6,165,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26142
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_HEADER_ERROR_FRAMES',0,6,170,16,3
	.word	40185
	.byte	12,6,173,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26266
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_HEADER_ERROR_OCTETS',0,6,178,16,3
	.word	40269
	.byte	12,6,181,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26390
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_NO_PAYLOAD_FRAMES',0,6,186,16,3
	.word	40353
	.byte	12,6,189,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26509
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXIPV6_NO_PAYLOAD_OCTETS',0,6,194,16,3
	.word	40435
	.byte	12,6,197,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26628
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_ERROR_FRAMES',0,6,202,16,3
	.word	40517
	.byte	12,6,205,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26732
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_ERROR_OCTETS',0,6,210,16,3
	.word	40593
	.byte	12,6,213,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26836
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_GOOD_FRAMES',0,6,218,16,3
	.word	40669
	.byte	12,6,221,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26937
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXTCP_GOOD_OCTETS',0,6,226,16,3
	.word	40744
	.byte	12,6,229,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27038
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_ERROR_FRAMES',0,6,234,16,3
	.word	40819
	.byte	12,6,237,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27142
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_ERROR_OCTETS',0,6,242,16,3
	.word	40895
	.byte	12,6,245,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27246
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_GOOD_FRAMES',0,6,250,16,3
	.word	40971
	.byte	12,6,253,16,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27347
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_RXUDP_GOOD_OCTETS',0,6,130,17,3
	.word	41046
	.byte	12,6,133,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27448
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_STATUS',0,6,138,17,3
	.word	41121
	.byte	12,6,141,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27891
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SUB_SECOND_INCREMENT',0,6,146,17,3
	.word	41185
	.byte	12,6,149,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28015
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_HIGHER_WORD_SECONDS',0,6,154,17,3
	.word	41263
	.byte	12,6,157,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28162
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_NANOSECONDS',0,6,162,17,3
	.word	41352
	.byte	12,6,165,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28292
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_NANOSECONDS_UPDATE',0,6,170,17,3
	.word	41433
	.byte	12,6,173,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28431
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_SECONDS',0,6,178,17,3
	.word	41521
	.byte	12,6,181,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28529
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_SYSTEM_TIME_SECONDS_UPDATE',0,6,186,17,3
	.word	41598
	.byte	12,6,189,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28641
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TARGET_TIME_NANOSECONDS',0,6,194,17,3
	.word	41682
	.byte	12,6,197,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28769
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TARGET_TIME_SECONDS',0,6,202,17,3
	.word	41763
	.byte	12,6,205,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28868
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TIMESTAMP_ADDEND',0,6,210,17,3
	.word	41840
	.byte	12,6,213,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28961
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TIMESTAMP_CONTROL',0,6,218,17,3
	.word	41914
	.byte	12,6,221,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29517
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TIMESTAMP_STATUS',0,6,226,17,3
	.word	41989
	.byte	12,6,229,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29921
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TRANSMIT_DESCRIPTOR_LIST_ADDRESS',0,6,234,17,3
	.word	42063
	.byte	12,6,237,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30070
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TRANSMIT_POLL_DEMAND',0,6,242,17,3
	.word	42153
	.byte	12,6,246,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30170
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD',0,6,251,17,3
	.word	42231
	.byte	12,6,255,17,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30310
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_128TO255OCTETS_FRAMES_GOOD_BAD',0,6,132,18,3
	.word	42323
	.byte	12,6,136,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30447
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_256TO511OCTETS_FRAMES_GOOD_BAD',0,6,141,18,3
	.word	42414
	.byte	12,6,145,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30584
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_512TO1023OCTETS_FRAMES_GOOD_BAD',0,6,150,18,3
	.word	42505
	.byte	12,6,153,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30724
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_64OCTETS_FRAMES_GOOD_BAD',0,6,158,18,3
	.word	42597
	.byte	12,6,162,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30844
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_65TO127OCTETS_FRAMES_GOOD_BAD',0,6,167,18,3
	.word	42682
	.byte	12,6,170,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31100
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_BROADCAST_FRAMES_GOOD',0,6,175,18,3
	.word	42772
	.byte	12,6,178,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30978
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_BROADCAST_FRAMES_GOOD_BAD',0,6,183,18,3
	.word	42854
	.byte	12,6,186,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31213
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_CARRIER_ERROR_FRAMES',0,6,191,18,3
	.word	42940
	.byte	12,6,194,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_DEFERRED_FRAMES',0,6,199,18,3
	.word	43021
	.byte	12,6,203,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31422
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_EXCESSIVE_COLLISION_FRAMES',0,6,208,18,3
	.word	43097
	.byte	12,6,212,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31545
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_EXCESSIVE_DEFERRAL_ERROR',0,6,217,18,3
	.word	43184
	.byte	12,6,220,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31774
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_FRAME_COUNT_GOOD',0,6,225,18,3
	.word	43269
	.byte	12,6,228,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31664
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_FRAME_COUNT_GOOD_BAD',0,6,233,18,3
	.word	43346
	.byte	12,6,236,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31875
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_LATE_COLLISION_FRAMES',0,6,241,18,3
	.word	43427
	.byte	12,6,244,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32111
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTICAST_FRAMES_GOOD',0,6,249,18,3
	.word	43509
	.byte	12,6,252,18,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31989
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTICAST_FRAMES_GOOD_BAD',0,6,129,19,3
	.word	43591
	.byte	12,6,133,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32224
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_MULTIPLE_COLLISION_GOOD_FRAMES',0,6,138,19,3
	.word	43677
	.byte	12,6,141,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32467
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OCTET_COUNT_GOOD',0,6,146,19,3
	.word	43768
	.byte	12,6,149,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32357
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OCTET_COUNT_GOOD_BAD',0,6,154,19,3
	.word	43845
	.byte	12,6,157,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32568
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_OSIZE_FRAMES_GOOD',0,6,162,19,3
	.word	43926
	.byte	12,6,165,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32672
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_PAUSE_FRAMES',0,6,170,19,3
	.word	44004
	.byte	12,6,174,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32766
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_SINGLE_COLLISION_GOOD_FRAMES',0,6,179,19,3
	.word	44077
	.byte	12,6,182,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32895
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_UNDERFLOW_ERROR_FRAMES',0,6,187,19,3
	.word	44166
	.byte	12,6,190,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33011
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_UNICAST_FRAMES_GOOD_BAD',0,6,195,19,3
	.word	44249
	.byte	12,6,198,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33129
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_TX_VLAN_FRAMES_GOOD',0,6,203,19,3
	.word	44333
	.byte	12,6,206,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33231
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_VERSION',0,6,211,19,3
	.word	44410
	.byte	12,6,214,19,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33351
	.byte	4,2,35,0,0,21
	.byte	'Ifx_ETH_VLAN_TAG',0,6,219,19,3
	.word	44475
	.byte	10
	.byte	'_Ifx_ETH_MAC_ADDRESS',0,6,230,19,25,8,13
	.byte	'HIGH',0
	.word	35225
	.byte	4,2,35,0,13
	.byte	'LOW',0
	.word	35299
	.byte	4,2,35,4,0,16
	.word	44541
	.byte	21
	.byte	'Ifx_ETH_MAC_ADDRESS',0,6,234,19,3
	.word	44596
	.byte	14,224,31
	.word	492
	.byte	15,223,31,0,14,128,1
	.word	44541
	.byte	15,15,0,16
	.word	44641
	.byte	14,64
	.word	492
	.byte	15,63,0,14,248,8
	.word	492
	.byte	15,247,8,0,14,208,1
	.word	492
	.byte	15,207,1,0,16
	.word	44641
	.byte	14,128,15
	.word	492
	.byte	15,255,14,0,14,164,1
	.word	492
	.byte	15,163,1,0,10
	.byte	'_Ifx_ETH',0,6,247,19,25,128,66,13
	.byte	'CLC',0
	.word	33781
	.byte	4,2,35,0,13
	.byte	'ID',0
	.word	34754
	.byte	4,2,35,4,13
	.byte	'GPCTL',0
	.word	34478
	.byte	4,2,35,8,13
	.byte	'ACCEN0',0
	.word	33512
	.byte	4,2,35,12,13
	.byte	'ACCEN1',0
	.word	33576
	.byte	4,2,35,16,13
	.byte	'KRST0',0
	.word	35034
	.byte	4,2,35,20,13
	.byte	'KRST1',0
	.word	35097
	.byte	4,2,35,24,13
	.byte	'KRSTCLR',0
	.word	35160
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	44630
	.byte	224,31,2,35,32,13
	.byte	'MAC_CONFIGURATION',0
	.word	35372
	.byte	4,3,35,128,32,13
	.byte	'MAC_FRAME_FILTER',0
	.word	35447
	.byte	4,3,35,132,32,13
	.byte	'HASH_TABLE_HIGH',0
	.word	34541
	.byte	4,3,35,136,32,13
	.byte	'HASH_TABLE_LOW',0
	.word	34614
	.byte	4,3,35,140,32,13
	.byte	'GMII_ADDRESS',0
	.word	34341
	.byte	4,3,35,144,32,13
	.byte	'GMII_DATA',0
	.word	34411
	.byte	4,3,35,148,32,13
	.byte	'FLOW_CONTROL',0
	.word	34271
	.byte	4,3,35,152,32,13
	.byte	'VLAN_TAG',0
	.word	44475
	.byte	4,3,35,156,32,13
	.byte	'VERSION',0
	.word	44410
	.byte	4,3,35,160,32,13
	.byte	'DEBUG',0
	.word	34208
	.byte	4,3,35,164,32,13
	.byte	'REMOTE_WAKE_UP_FRAME_FILTER',0
	.word	36660
	.byte	4,3,35,168,32,13
	.byte	'PMT_CONTROL_STATUS',0
	.word	36259
	.byte	4,3,35,172,32,13
	.byte	'reserved_1030',0
	.word	3356
	.byte	8,3,35,176,32,13
	.byte	'INTERRUPT_STATUS',0
	.word	34960
	.byte	4,3,35,184,32,13
	.byte	'INTERRUPT_MASK',0
	.word	34888
	.byte	4,3,35,188,32,13
	.byte	'MAC_ADDRESS_G0',0
	.word	44651
	.byte	128,1,3,35,192,32,13
	.byte	'reserved_10C0',0
	.word	44656
	.byte	64,3,35,192,33,13
	.byte	'MMC_CONTROL',0
	.word	35619
	.byte	4,3,35,128,34,13
	.byte	'MMC_RECEIVE_INTERRUPT',0
	.word	35859
	.byte	4,3,35,132,34,13
	.byte	'MMC_TRANSMIT_INTERRUPT',0
	.word	36022
	.byte	4,3,35,136,34,13
	.byte	'MMC_RECEIVE_INTERRUPT_MASK',0
	.word	35938
	.byte	4,3,35,140,34,13
	.byte	'MMC_TRANSMIT_INTERRUPT_MASK',0
	.word	36102
	.byte	4,3,35,144,34,13
	.byte	'TX_OCTET_COUNT_GOOD_BAD',0
	.word	43845
	.byte	4,3,35,148,34,13
	.byte	'TX_FRAME_COUNT_GOOD_BAD',0
	.word	43346
	.byte	4,3,35,152,34,13
	.byte	'TX_BROADCAST_FRAMES_GOOD',0
	.word	42772
	.byte	4,3,35,156,34,13
	.byte	'TX_MULTICAST_FRAMES_GOOD',0
	.word	43509
	.byte	4,3,35,160,34,13
	.byte	'TX_64OCTETS_FRAMES_GOOD_BAD',0
	.word	42597
	.byte	4,3,35,164,34,13
	.byte	'TX_65TO127OCTETS_FRAMES_GOOD_BAD',0
	.word	42682
	.byte	4,3,35,168,34,13
	.byte	'TX_128TO255OCTETS_FRAMES_GOOD_BAD',0
	.word	42323
	.byte	4,3,35,172,34,13
	.byte	'TX_256TO511OCTETS_FRAMES_GOOD_BAD',0
	.word	42414
	.byte	4,3,35,176,34,13
	.byte	'TX_512TO1023OCTETS_FRAMES_GOOD_BAD',0
	.word	42505
	.byte	4,3,35,180,34,13
	.byte	'TX_1024TOMAXOCTETS_FRAMES_GOOD_BAD',0
	.word	42231
	.byte	4,3,35,184,34,13
	.byte	'TX_UNICAST_FRAMES_GOOD_BAD',0
	.word	44249
	.byte	4,3,35,188,34,13
	.byte	'TX_MULTICAST_FRAMES_GOOD_BAD',0
	.word	43591
	.byte	4,3,35,192,34,13
	.byte	'TX_BROADCAST_FRAMES_GOOD_BAD',0
	.word	42854
	.byte	4,3,35,196,34,13
	.byte	'TX_UNDERFLOW_ERROR_FRAMES',0
	.word	44166
	.byte	4,3,35,200,34,13
	.byte	'TX_SINGLE_COLLISION_GOOD_FRAMES',0
	.word	44077
	.byte	4,3,35,204,34,13
	.byte	'TX_MULTIPLE_COLLISION_GOOD_FRAMES',0
	.word	43677
	.byte	4,3,35,208,34,13
	.byte	'TX_DEFERRED_FRAMES',0
	.word	43021
	.byte	4,3,35,212,34,13
	.byte	'TX_LATE_COLLISION_FRAMES',0
	.word	43427
	.byte	4,3,35,216,34,13
	.byte	'TX_EXCESSIVE_COLLISION_FRAMES',0
	.word	43097
	.byte	4,3,35,220,34,13
	.byte	'TX_CARRIER_ERROR_FRAMES',0
	.word	42940
	.byte	4,3,35,224,34,13
	.byte	'TX_OCTET_COUNT_GOOD',0
	.word	43768
	.byte	4,3,35,228,34,13
	.byte	'TX_FRAME_COUNT_GOOD',0
	.word	43269
	.byte	4,3,35,232,34,13
	.byte	'TX_EXCESSIVE_DEFERRAL_ERROR',0
	.word	43184
	.byte	4,3,35,236,34,13
	.byte	'TX_PAUSE_FRAMES',0
	.word	44004
	.byte	4,3,35,240,34,13
	.byte	'TX_VLAN_FRAMES_GOOD',0
	.word	44333
	.byte	4,3,35,244,34,13
	.byte	'TX_OSIZE_FRAMES_GOOD',0
	.word	43926
	.byte	4,3,35,248,34,13
	.byte	'reserved_117C',0
	.word	1537
	.byte	4,3,35,252,34,13
	.byte	'RX_FRAMES_COUNT_GOOD_BAD',0
	.word	37689
	.byte	4,3,35,128,35,13
	.byte	'RX_OCTET_COUNT_GOOD_BAD',0
	.word	38090
	.byte	4,3,35,132,35,13
	.byte	'RX_OCTET_COUNT_GOOD',0
	.word	38013
	.byte	4,3,35,136,35,13
	.byte	'RX_BROADCAST_FRAMES_GOOD',0
	.word	37369
	.byte	4,3,35,140,35,13
	.byte	'RX_MULTICAST_FRAMES_GOOD',0
	.word	37931
	.byte	4,3,35,144,35,13
	.byte	'RX_CRC_ERROR_FRAMES',0
	.word	37531
	.byte	4,3,35,148,35,13
	.byte	'RX_ALIGNMENT_ERROR_FRAMES',0
	.word	37286
	.byte	4,3,35,152,35,13
	.byte	'RX_RUNT_ERROR_FRAMES',0
	.word	38491
	.byte	4,3,35,156,35,13
	.byte	'RX_JABBER_ERROR_FRAMES',0
	.word	37771
	.byte	4,3,35,160,35,13
	.byte	'RX_UNDERSIZE_FRAMES_GOOD',0
	.word	38569
	.byte	4,3,35,164,35,13
	.byte	'RX_OVERSIZE_FRAMES_GOOD',0
	.word	38256
	.byte	4,3,35,168,35,13
	.byte	'RX_64OCTETS_FRAMES_GOOD_BAD',0
	.word	37111
	.byte	4,3,35,172,35,13
	.byte	'RX_65TO127OCTETS_FRAMES_GOOD_BAD',0
	.word	37196
	.byte	4,3,35,176,35,13
	.byte	'RX_128TO255OCTETS_FRAMES_GOOD_BAD',0
	.word	36837
	.byte	4,3,35,180,35,13
	.byte	'RX_256TO511OCTETS_FRAMES_GOOD_BAD',0
	.word	36928
	.byte	4,3,35,184,35,13
	.byte	'RX_512TO1023OCTETS_FRAMES_GOOD_BAD',0
	.word	37019
	.byte	4,3,35,188,35,13
	.byte	'RX_1024TOMAXOCTETS_FRAMES_GOOD_BAD',0
	.word	36745
	.byte	4,3,35,192,35,13
	.byte	'RX_UNICAST_FRAMES_GOOD',0
	.word	38651
	.byte	4,3,35,196,35,13
	.byte	'RX_LENGTH_ERROR_FRAMES',0
	.word	37851
	.byte	4,3,35,200,35,13
	.byte	'RX_OUT_OF_RANGE_TYPE_FRAMES',0
	.word	38171
	.byte	4,3,35,204,35,13
	.byte	'RX_PAUSE_FRAMES',0
	.word	38337
	.byte	4,3,35,208,35,13
	.byte	'RX_FIFO_OVERFLOW_FRAMES',0
	.word	37608
	.byte	4,3,35,212,35,13
	.byte	'RX_VLAN_FRAMES_GOOD_BAD',0
	.word	38731
	.byte	4,3,35,216,35,13
	.byte	'RX_WATCHDOG_ERROR_FRAMES',0
	.word	38812
	.byte	4,3,35,220,35,13
	.byte	'RX_RECEIVE_ERROR_FRAMES',0
	.word	38410
	.byte	4,3,35,224,35,13
	.byte	'RX_CONTROL_FRAMES_GOOD',0
	.word	37451
	.byte	4,3,35,228,35,13
	.byte	'reserved_11E8',0
	.word	2727
	.byte	24,3,35,232,35,13
	.byte	'MMC_IPC_RECEIVE_INTERRUPT_MASK',0
	.word	35771
	.byte	4,3,35,128,36,13
	.byte	'reserved_1204',0
	.word	1537
	.byte	4,3,35,132,36,13
	.byte	'MMC_IPC_RECEIVE_INTERRUPT',0
	.word	35688
	.byte	4,3,35,136,36,13
	.byte	'reserved_120C',0
	.word	1537
	.byte	4,3,35,140,36,13
	.byte	'RXIPV4_GOOD_FRAMES',0
	.word	39364
	.byte	4,3,35,144,36,13
	.byte	'RXIPV4_HEADER_ERROR_FRAMES',0
	.word	39516
	.byte	4,3,35,148,36,13
	.byte	'RXIPV4_NO_PAYLOAD_FRAMES',0
	.word	39684
	.byte	4,3,35,152,36,13
	.byte	'RXIPV4_FRAGMENTED_FRAMES',0
	.word	39200
	.byte	4,3,35,156,36,13
	.byte	'RXIPV4_UDP_CHECKSUM_DISABLED_FRAMES',0
	.word	39940
	.byte	4,3,35,160,36,13
	.byte	'RXIPV6_GOOD_FRAMES',0
	.word	40033
	.byte	4,3,35,164,36,13
	.byte	'RXIPV6_HEADER_ERROR_FRAMES',0
	.word	40185
	.byte	4,3,35,168,36,13
	.byte	'RXIPV6_NO_PAYLOAD_FRAMES',0
	.word	40353
	.byte	4,3,35,172,36,13
	.byte	'RXUDP_GOOD_FRAMES',0
	.word	40971
	.byte	4,3,35,176,36,13
	.byte	'RXUDP_ERROR_FRAMES',0
	.word	40819
	.byte	4,3,35,180,36,13
	.byte	'RXTCP_GOOD_FRAMES',0
	.word	40669
	.byte	4,3,35,184,36,13
	.byte	'RXTCP_ERROR_FRAMES',0
	.word	40517
	.byte	4,3,35,188,36,13
	.byte	'RXICMP_GOOD_FRAMES',0
	.word	39048
	.byte	4,3,35,192,36,13
	.byte	'RXICMP_ERROR_FRAMES',0
	.word	38894
	.byte	4,3,35,196,36,13
	.byte	'reserved_1248',0
	.word	3356
	.byte	8,3,35,200,36,13
	.byte	'RXIPV4_GOOD_OCTETS',0
	.word	39440
	.byte	4,3,35,208,36,13
	.byte	'RXIPV4_HEADER_ERROR_OCTETS',0
	.word	39600
	.byte	4,3,35,212,36,13
	.byte	'RXIPV4_NO_PAYLOAD_OCTETS',0
	.word	39766
	.byte	4,3,35,216,36,13
	.byte	'RXIPV4_FRAGMENTED_OCTETS',0
	.word	39282
	.byte	4,3,35,220,36,13
	.byte	'RXIPV4_UDP_CHECKSUM_DISABLE_OCTETS',0
	.word	39848
	.byte	4,3,35,224,36,13
	.byte	'RXIPV6_GOOD_OCTETS',0
	.word	40109
	.byte	4,3,35,228,36,13
	.byte	'RXIPV6_HEADER_ERROR_OCTETS',0
	.word	40269
	.byte	4,3,35,232,36,13
	.byte	'RXIPV6_NO_PAYLOAD_OCTETS',0
	.word	40435
	.byte	4,3,35,236,36,13
	.byte	'RXUDP_GOOD_OCTETS',0
	.word	41046
	.byte	4,3,35,240,36,13
	.byte	'RXUDP_ERROR_OCTETS',0
	.word	40895
	.byte	4,3,35,244,36,13
	.byte	'RXTCP_GOOD_OCTETS',0
	.word	40744
	.byte	4,3,35,248,36,13
	.byte	'RXTCP_ERROR_OCTETS',0
	.word	40593
	.byte	4,3,35,252,36,13
	.byte	'RXICMP_GOOD_OCTETS',0
	.word	39124
	.byte	4,3,35,128,37,13
	.byte	'RXICMP_ERROR_OCTETS',0
	.word	38971
	.byte	4,3,35,132,37,13
	.byte	'reserved_1288',0
	.word	44665
	.byte	248,8,3,35,136,37,13
	.byte	'TIMESTAMP_CONTROL',0
	.word	41914
	.byte	4,3,35,128,46,13
	.byte	'SUB_SECOND_INCREMENT',0
	.word	41185
	.byte	4,3,35,132,46,13
	.byte	'SYSTEM_TIME_SECONDS',0
	.word	41521
	.byte	4,3,35,136,46,13
	.byte	'SYSTEM_TIME_NANOSECONDS',0
	.word	41352
	.byte	4,3,35,140,46,13
	.byte	'SYSTEM_TIME_SECONDS_UPDATE',0
	.word	41598
	.byte	4,3,35,144,46,13
	.byte	'SYSTEM_TIME_NANOSECONDS_UPDATE',0
	.word	41433
	.byte	4,3,35,148,46,13
	.byte	'TIMESTAMP_ADDEND',0
	.word	41840
	.byte	4,3,35,152,46,13
	.byte	'TARGET_TIME_SECONDS',0
	.word	41763
	.byte	4,3,35,156,46,13
	.byte	'TARGET_TIME_NANOSECONDS',0
	.word	41682
	.byte	4,3,35,160,46,13
	.byte	'SYSTEM_TIME_HIGHER_WORD_SECONDS',0
	.word	41263
	.byte	4,3,35,164,46,13
	.byte	'TIMESTAMP_STATUS',0
	.word	41989
	.byte	4,3,35,168,46,13
	.byte	'PPS_CONTROL',0
	.word	36335
	.byte	4,3,35,172,46,13
	.byte	'reserved_1730',0
	.word	44676
	.byte	208,1,3,35,176,46,13
	.byte	'MAC_ADDRESS_G1',0
	.word	44687
	.byte	128,1,3,35,128,48,13
	.byte	'reserved_1880',0
	.word	44692
	.byte	128,15,3,35,128,49,13
	.byte	'BUS_MODE',0
	.word	33715
	.byte	4,3,35,128,64,13
	.byte	'TRANSMIT_POLL_DEMAND',0
	.word	42153
	.byte	4,3,35,132,64,13
	.byte	'RECEIVE_POLL_DEMAND',0
	.word	36583
	.byte	4,3,35,136,64,13
	.byte	'RECEIVE_DESCRIPTOR_LIST_ADDRESS',0
	.word	36404
	.byte	4,3,35,140,64,13
	.byte	'TRANSMIT_DESCRIPTOR_LIST_ADDRESS',0
	.word	42063
	.byte	4,3,35,144,64,13
	.byte	'STATUS',0
	.word	41121
	.byte	4,3,35,148,64,13
	.byte	'OPERATION_MODE',0
	.word	36187
	.byte	4,3,35,152,64,13
	.byte	'INTERRUPT_ENABLE',0
	.word	34814
	.byte	4,3,35,156,64,13
	.byte	'MISSED_FRAME_AND_BUFFER_OVERFLOW_COUNTER',0
	.word	35521
	.byte	4,3,35,160,64,13
	.byte	'RECEIVE_INTERRUPT_WATCHDOG_TIMER',0
	.word	36493
	.byte	4,3,35,164,64,13
	.byte	'reserved_2028',0
	.word	1537
	.byte	4,3,35,168,64,13
	.byte	'AHB_OR_AXI_STATUS',0
	.word	33640
	.byte	4,3,35,172,64,13
	.byte	'reserved_2030',0
	.word	2727
	.byte	24,3,35,176,64,13
	.byte	'CURRENT_HOST_TRANSMIT_DESCRIPTOR',0
	.word	34118
	.byte	4,3,35,200,64,13
	.byte	'CURRENT_HOST_RECEIVE_DESCRIPTOR',0
	.word	33935
	.byte	4,3,35,204,64,13
	.byte	'CURRENT_HOST_TRANSMIT_BUFFER_ADDRESS',0
	.word	34024
	.byte	4,3,35,208,64,13
	.byte	'CURRENT_HOST_RECEIVE_BUFFER_ADDRESS',0
	.word	33842
	.byte	4,3,35,212,64,13
	.byte	'HW_FEATURE',0
	.word	34686
	.byte	4,3,35,216,64,13
	.byte	'reserved_205C',0
	.word	44703
	.byte	164,1,3,35,220,64,0,16
	.word	44714
	.byte	21
	.byte	'Ifx_ETH',0,6,146,21,3
	.word	49556
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	492
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	492
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	509
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	49623
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	351
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8545
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	49689
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	49717
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	297
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	383
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	49717
	.byte	17,8,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,8,140,1,3
	.word	49802
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7109
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7022
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3365
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1418
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2413
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1546
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2193
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1976
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6381
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6505
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6589
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6769
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5020
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5544
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5194
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5368
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6033
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	847
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4357
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4845
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4504
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4673
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5700
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	531
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4071
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3705
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2736
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3040
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7636
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7069
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3656
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1497
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2687
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1721
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2373
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1936
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2153
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6465
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6714
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6973
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6341
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5154
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5660
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5328
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5504
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1378
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5993
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4464
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4980
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4633
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4805
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	807
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4317
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4031
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3000
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3316
	.byte	16
	.word	7676
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	51258
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	51278
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	51400
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	51957
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	52034
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	52170
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	52450
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	52688
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	52816
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	53059
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	53294
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	53422
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	53522
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	492
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	53622
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	53830
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	53995
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	54178
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	54332
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	54696
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	54907
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	55159
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	55277
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	55388
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	55551
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	55714
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	55872
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	56037
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	509
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	56366
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	56587
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	56750
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	57022
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	57175
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	57331
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	57493
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	57636
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	57801
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	57946
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	58127
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	58301
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	58461
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	58605
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	58879
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	59018
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	492
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	59181
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	59399
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	59562
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	59898
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	60005
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	60457
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	60556
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	60706
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	60855
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	61016
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	61146
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	61278
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	492
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	509
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	61393
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	61504
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	61662
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	62074
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	509
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	62175
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	62442
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	62578
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	62689
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	62822
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	63025
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	63381
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	63559
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	63659
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	64029
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	64215
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	64413
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	64646
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	64798
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	492
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	65365
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	65659
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	492
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	65937
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	66433
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	66746
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	66955
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	67166
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	67598
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	492
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	67694
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	67954
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	68079
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	68276
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	68429
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	68582
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	68735
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	68890
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	68890
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	68890
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	68890
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	68906
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	69036
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	69274
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	68890
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	68890
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	68890
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	68890
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	69497
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	69623
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	69875
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51400
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	70094
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51957
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	70158
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52034
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	70222
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52170
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	70287
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52450
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	70352
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52688
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	70417
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52816
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	70482
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53059
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	70547
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53294
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	70612
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53422
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	70677
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53522
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	70742
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53622
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	70807
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53830
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	70871
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53995
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	70935
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54178
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	70999
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54332
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	71064
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54696
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	71126
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54907
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	71188
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55159
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	71250
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	71314
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55388
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	71379
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	71445
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55714
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	71511
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55872
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	71579
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56037
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	71646
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56366
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	71714
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56587
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	71782
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56750
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	71848
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57022
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	71915
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	71984
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57331
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	72053
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57493
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	72122
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57636
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	72191
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57801
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	72260
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	72329
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58127
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	72397
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58301
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	72465
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58461
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	72533
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58605
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	72601
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58879
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	72666
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59018
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	72731
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59181
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	72797
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59399
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	72861
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59562
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	72922
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59898
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	72983
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60005
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	73043
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60457
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	73105
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60556
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	73165
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60706
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	73227
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60855
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	73295
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61016
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	73363
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61146
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	73431
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61278
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	73495
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61393
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	73560
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61504
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	73623
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61662
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	73684
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62074
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	73748
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	73809
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62442
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	73873
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	73940
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62689
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	74003
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62822
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	74064
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63025
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	74126
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63381
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	74191
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63559
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	74256
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63659
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	74321
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64029
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	74390
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64215
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	74459
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	74528
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64646
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	74593
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64798
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	74656
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65365
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	74721
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65659
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	74786
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65937
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	74851
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	74917
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66955
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	74986
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66746
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	75050
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67166
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	75115
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67598
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	75180
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67694
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	75245
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67954
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	75309
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68079
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	75375
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68276
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	75439
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68429
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	75504
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68582
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	75569
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68735
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	75634
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68906
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	75700
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69036
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	75769
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69274
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	75838
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69497
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	75905
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69623
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	75972
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69875
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	76039
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	75700
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	75769
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	75838
	.byte	4,2,35,8,0,16
	.word	76104
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	76167
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	75905
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	75972
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	76039
	.byte	4,2,35,8,0,16
	.word	76196
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	76257
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	76284
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	76435
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	76679
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	76777
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8289
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8284
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	492
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	77242
	.byte	16
	.word	44714
	.byte	3
	.word	77302
	.byte	23,11,67,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	77312
	.byte	21
	.byte	'IfxEth_Crsdv_In',0,11,72,3
	.word	77363
	.byte	23,11,83,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	77392
	.byte	21
	.byte	'IfxEth_Refclk_In',0,11,88,3
	.word	77443
	.byte	23,11,91,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	77473
	.byte	21
	.byte	'IfxEth_Rxclk_In',0,11,96,3
	.word	77524
	.byte	23,11,99,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	77553
	.byte	21
	.byte	'IfxEth_Txclk_In',0,11,104,3
	.word	77604
	.byte	23,11,107,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	77633
	.byte	21
	.byte	'IfxEth_Rxd_In',0,11,112,3
	.word	77684
	.byte	23,11,123,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	76435
	.byte	1,2,35,12,0,24
	.word	77711
	.byte	21
	.byte	'IfxEth_Mdc_Out',0,11,128,1,3
	.word	77762
	.byte	23,11,131,1,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'inSelect',0
	.word	49802
	.byte	1,2,35,12,13
	.byte	'outSelect',0
	.word	76435
	.byte	1,2,35,13,0,24
	.word	77791
	.byte	21
	.byte	'IfxEth_Mdio_InOut',0,11,137,1,3
	.word	77864
	.byte	23,11,140,1,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	76435
	.byte	1,2,35,12,0,24
	.word	77896
	.byte	21
	.byte	'IfxEth_Txd_Out',0,11,145,1,3
	.word	77948
	.byte	23,11,148,1,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	76435
	.byte	1,2,35,12,0,24
	.word	77977
	.byte	21
	.byte	'IfxEth_Txen_Out',0,11,153,1,3
	.word	78029
	.byte	23,11,164,1,15,16,13
	.byte	'module',0
	.word	77307
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	77242
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	49802
	.byte	1,2,35,12,0,24
	.word	78059
	.byte	21
	.byte	'IfxEth_Rxer_In',0,11,169,1,3
	.word	78111
.L58:
	.byte	24
	.word	77312
.L59:
	.byte	24
	.word	77711
.L60:
	.byte	24
	.word	77711
.L61:
	.byte	24
	.word	77711
.L62:
	.byte	24
	.word	77791
.L63:
	.byte	24
	.word	77791
.L64:
	.byte	24
	.word	77791
.L65:
	.byte	24
	.word	77392
.L66:
	.byte	24
	.word	77473
.L67:
	.byte	24
	.word	77633
.L68:
	.byte	24
	.word	77633
.L69:
	.byte	24
	.word	78059
.L70:
	.byte	24
	.word	77553
.L71:
	.byte	24
	.word	77896
.L72:
	.byte	24
	.word	77896
.L73:
	.byte	24
	.word	77977
	.byte	24
	.word	77312
	.byte	3
	.word	78220
	.byte	14,4
	.word	78225
	.byte	15,0,0
.L74:
	.byte	14,4
	.word	78230
	.byte	15,0,0,24
	.word	77711
	.byte	3
	.word	78248
	.byte	14,12
	.word	78253
	.byte	15,2,0
.L75:
	.byte	14,12
	.word	78258
	.byte	15,0,0,24
	.word	77791
	.byte	3
	.word	78276
	.byte	14,28
	.word	78281
	.byte	15,6,0
.L76:
	.byte	14,28
	.word	78286
	.byte	15,0,0,24
	.word	77392
	.byte	3
	.word	78304
	.byte	14,4
	.word	78309
	.byte	15,0,0
.L77:
	.byte	14,4
	.word	78314
	.byte	15,0,0,24
	.word	77473
	.byte	3
	.word	78332
	.byte	14,4
	.word	78337
	.byte	15,0,0
.L78:
	.byte	14,4
	.word	78342
	.byte	15,0,0,24
	.word	77633
	.byte	3
	.word	78360
	.byte	14,4
	.word	78365
	.byte	15,0,0
.L79:
	.byte	14,4
	.word	78370
	.byte	15,0,0,24
	.word	78059
	.byte	3
	.word	78388
	.byte	14,8
	.word	78393
	.byte	15,1,0
.L80:
	.byte	14,8
	.word	78398
	.byte	15,0,0,24
	.word	77553
	.byte	3
	.word	78416
	.byte	14,8
	.word	78421
	.byte	15,1,0
.L81:
	.byte	14,8
	.word	78426
	.byte	15,0,0,24
	.word	77896
	.byte	3
	.word	78444
	.byte	14,8
	.word	78449
	.byte	15,1,0
.L82:
	.byte	14,8
	.word	78454
	.byte	15,0,0,24
	.word	77977
	.byte	3
	.word	78472
	.byte	14,4
	.word	78477
	.byte	15,0,0
.L83:
	.byte	14,4
	.word	78482
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L85-.L84
.L84:
	.half	3
	.word	.L87-.L86
.L86:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0,0,0,0
	.byte	'IfxEth_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxEth_PinMap.h',0,0,0,0,0
.L87:
.L85:
	.sdecl	'.debug_info',debug,cluster('IfxEth_CRSDVA_P11_11_IN')
	.sect	'.debug_info'
.L6:
	.word	274
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_CRSDVA_P11_11_IN',0,5,48,17
	.word	.L58
	.byte	1,5,3
	.word	IfxEth_CRSDVA_P11_11_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_CRSDVA_P11_11_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDC_P02_8_OUT')
	.sect	'.debug_info'
.L8:
	.word	271
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDC_P02_8_OUT',0,5,49,16
	.word	.L59
	.byte	1,5,3
	.word	IfxEth_MDC_P02_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDC_P02_8_OUT')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDC_P21_0_OUT')
	.sect	'.debug_info'
.L10:
	.word	271
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDC_P21_0_OUT',0,5,50,16
	.word	.L60
	.byte	1,5,3
	.word	IfxEth_MDC_P21_0_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDC_P21_0_OUT')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDC_P21_2_OUT')
	.sect	'.debug_info'
.L12:
	.word	271
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDC_P21_2_OUT',0,5,51,16
	.word	.L61
	.byte	1,5,3
	.word	IfxEth_MDC_P21_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDC_P21_2_OUT')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDIOA_P00_0_INOUT')
	.sect	'.debug_info'
.L14:
	.word	275
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDIOA_P00_0_INOUT',0,5,52,19
	.word	.L62
	.byte	1,5,3
	.word	IfxEth_MDIOA_P00_0_INOUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDIOA_P00_0_INOUT')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDIOD_P21_3_INOUT')
	.sect	'.debug_info'
.L16:
	.word	275
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDIOD_P21_3_INOUT',0,5,53,19
	.word	.L63
	.byte	1,5,3
	.word	IfxEth_MDIOD_P21_3_INOUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDIOD_P21_3_INOUT')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_MDIO_P21_1_INOUT')
	.sect	'.debug_info'
.L18:
	.word	274
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_MDIO_P21_1_INOUT',0,5,54,19
	.word	.L64
	.byte	1,5,3
	.word	IfxEth_MDIO_P21_1_INOUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_MDIO_P21_1_INOUT')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_REFCLK_P11_12_IN')
	.sect	'.debug_info'
.L20:
	.word	274
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_REFCLK_P11_12_IN',0,5,55,18
	.word	.L65
	.byte	1,5,3
	.word	IfxEth_REFCLK_P11_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_REFCLK_P11_12_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_RXCLKA_P11_12_IN')
	.sect	'.debug_info'
.L22:
	.word	274
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_RXCLKA_P11_12_IN',0,5,56,17
	.word	.L66
	.byte	1,5,3
	.word	IfxEth_RXCLKA_P11_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_RXCLKA_P11_12_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_RXD0_P11_10_IN')
	.sect	'.debug_info'
.L24:
	.word	272
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_RXD0_P11_10_IN',0,5,57,15
	.word	.L67
	.byte	1,5,3
	.word	IfxEth_RXD0_P11_10_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_RXD0_P11_10_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_RXD1_P11_9_IN')
	.sect	'.debug_info'
.L26:
	.word	271
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_RXD1_P11_9_IN',0,5,58,15
	.word	.L68
	.byte	1,5,3
	.word	IfxEth_RXD1_P11_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_RXD1_P11_9_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_RXERB_P21_7_IN')
	.sect	'.debug_info'
.L28:
	.word	272
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_RXERB_P21_7_IN',0,5,59,16
	.word	.L69
	.byte	1,5,3
	.word	IfxEth_RXERB_P21_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_RXERB_P21_7_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_TXCLKB_P11_12_IN')
	.sect	'.debug_info'
.L30:
	.word	274
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_TXCLKB_P11_12_IN',0,5,60,17
	.word	.L70
	.byte	1,5,3
	.word	IfxEth_TXCLKB_P11_12_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_TXCLKB_P11_12_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_TXD0_P11_3_OUT')
	.sect	'.debug_info'
.L32:
	.word	272
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_TXD0_P11_3_OUT',0,5,61,16
	.word	.L71
	.byte	1,5,3
	.word	IfxEth_TXD0_P11_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_TXD0_P11_3_OUT')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_TXD1_P11_2_OUT')
	.sect	'.debug_info'
.L34:
	.word	272
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_TXD1_P11_2_OUT',0,5,62,16
	.word	.L72
	.byte	1,5,3
	.word	IfxEth_TXD1_P11_2_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_TXD1_P11_2_OUT')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_TXEN_P11_6_OUT')
	.sect	'.debug_info'
.L36:
	.word	272
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_TXEN_P11_6_OUT',0,5,63,17
	.word	.L73
	.byte	1,5,3
	.word	IfxEth_TXEN_P11_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_TXEN_P11_6_OUT')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Crsdv_In_pinTable')
	.sect	'.debug_info'
.L38:
	.word	275
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Crsdv_In_pinTable',0,5,66,24
	.word	.L74
	.byte	1,5,3
	.word	IfxEth_Crsdv_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Crsdv_In_pinTable')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Mdc_Out_pinTable')
	.sect	'.debug_info'
.L40:
	.word	274
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Mdc_Out_pinTable',0,5,72,23
	.word	.L75
	.byte	1,5,3
	.word	IfxEth_Mdc_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Mdc_Out_pinTable')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Mdio_InOut_pinTable')
	.sect	'.debug_info'
.L42:
	.word	277
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Mdio_InOut_pinTable',0,5,80,26
	.word	.L76
	.byte	1,5,3
	.word	IfxEth_Mdio_InOut_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Mdio_InOut_pinTable')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Refclk_In_pinTable')
	.sect	'.debug_info'
.L44:
	.word	276
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Refclk_In_pinTable',0,5,92,25
	.word	.L77
	.byte	1,5,3
	.word	IfxEth_Refclk_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Refclk_In_pinTable')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Rxclk_In_pinTable')
	.sect	'.debug_info'
.L46:
	.word	275
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Rxclk_In_pinTable',0,5,98,24
	.word	.L78
	.byte	1,5,3
	.word	IfxEth_Rxclk_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Rxclk_In_pinTable')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Rxd_In_pinTable')
	.sect	'.debug_info'
.L48:
	.word	273
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Rxd_In_pinTable',0,5,104,22
	.word	.L79
	.byte	1,5,3
	.word	IfxEth_Rxd_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Rxd_In_pinTable')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Rxer_In_pinTable')
	.sect	'.debug_info'
.L50:
	.word	274
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Rxer_In_pinTable',0,5,110,23
	.word	.L80
	.byte	1,5,3
	.word	IfxEth_Rxer_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Rxer_In_pinTable')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Txclk_In_pinTable')
	.sect	'.debug_info'
.L52:
	.word	275
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Txclk_In_pinTable',0,5,117,24
	.word	.L81
	.byte	1,5,3
	.word	IfxEth_Txclk_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Txclk_In_pinTable')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Txd_Out_pinTable')
	.sect	'.debug_info'
.L54:
	.word	274
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Txd_Out_pinTable',0,5,124,23
	.word	.L82
	.byte	1,5,3
	.word	IfxEth_Txd_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Txd_Out_pinTable')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxEth_Txen_Out_pinTable')
	.sect	'.debug_info'
.L56:
	.word	276
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxEth_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxEth_Txen_Out_pinTable',0,5,131,1,24
	.word	.L83
	.byte	1,5,3
	.word	IfxEth_Txen_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxEth_Txen_Out_pinTable')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
