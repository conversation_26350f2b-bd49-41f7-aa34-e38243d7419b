	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc6804a --dep-file=IfxMultican_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxMultican_cfg.IfxMultican_cfg_indexMap',data,rom,cluster('IfxMultican_cfg_indexMap')
	.sect	'.rodata.IfxMultican_cfg.IfxMultican_cfg_indexMap'
	.global	IfxMultican_cfg_indexMap
	.align	4
IfxMultican_cfg_indexMap:	.type	object
	.size	IfxMultican_cfg_indexMap,8
	.word	-268337152
	.space	4
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	12178
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	237
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	268
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	301
	.byte	4,1,5
	.word	328
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	330
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	353
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	384
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	421
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	237
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	472
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	500
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	533
	.byte	6
	.byte	'void',0,5
	.word	559
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	565
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	500
	.byte	7
	.word	559
	.byte	5
	.word	605
	.byte	8,3,143,1,9,8,9
	.byte	'module',0
	.word	610
	.byte	4,2,35,0,9
	.byte	'index',0
	.word	472
	.byte	4,2,35,4,0,3
	.byte	'IfxModule_IndexMap',0,3,147,1,3
	.word	615
	.byte	10
	.byte	'_Ifx_CAN_ACCEN0_Bits',0,4,49,16,4,11
	.byte	'EN0',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	353
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	353
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	353
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	353
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	353
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	353
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	353
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	353
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	353
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	353
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	353
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	353
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	353
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	353
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	353
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	353
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	353
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	353
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	353
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	353
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_CAN_ACCEN0_Bits',0,4,83,3
	.word	681
	.byte	10
	.byte	'_Ifx_CAN_ACCEN1_Bits',0,4,86,16,4,11
	.byte	'reserved_0',0,4
	.word	268
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_CAN_ACCEN1_Bits',0,4,89,3
	.word	1238
	.byte	10
	.byte	'_Ifx_CAN_CLC_Bits',0,4,92,16,4,11
	.byte	'DISR',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	268
	.byte	28,0,2,35,0,0,3
	.byte	'Ifx_CAN_CLC_Bits',0,4,99,3
	.word	1315
	.byte	10
	.byte	'_Ifx_CAN_FDR_Bits',0,4,102,16,4,11
	.byte	'STEP',0,2
	.word	384
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	353
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	353
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_FDR_Bits',0,4,108,3
	.word	1456
	.byte	10
	.byte	'_Ifx_CAN_ID_Bits',0,4,111,16,4,11
	.byte	'MODREV',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_ID_Bits',0,4,116,3
	.word	1581
	.byte	10
	.byte	'_Ifx_CAN_KRST0_Bits',0,4,119,16,4,11
	.byte	'RST',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	268
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_CAN_KRST0_Bits',0,4,124,3
	.word	1686
	.byte	10
	.byte	'_Ifx_CAN_KRST1_Bits',0,4,127,16,4,11
	.byte	'RST',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	268
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_CAN_KRST1_Bits',0,4,131,1,3
	.word	1795
	.byte	10
	.byte	'_Ifx_CAN_KRSTCLR_Bits',0,4,134,1,16,4,11
	.byte	'CLR',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	268
	.byte	31,0,2,35,0,0,3
	.byte	'Ifx_CAN_KRSTCLR_Bits',0,4,138,1,3
	.word	1886
	.byte	10
	.byte	'_Ifx_CAN_LIST_Bits',0,4,141,1,16,4,11
	.byte	'BEGIN',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'END',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'SIZE',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'EMPTY',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	353
	.byte	7,0,2,35,3,0,3
	.byte	'Ifx_CAN_LIST_Bits',0,4,148,1,3
	.word	1982
	.byte	10
	.byte	'_Ifx_CAN_MCR_Bits',0,4,151,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	353
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	384
	.byte	8,4,2,35,0,11
	.byte	'MPSEL',0,1
	.word	353
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_MCR_Bits',0,4,157,1,3
	.word	2123
	.byte	10
	.byte	'_Ifx_CAN_MECR_Bits',0,4,160,1,16,4,11
	.byte	'TH',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'INP',0,1
	.word	353
	.byte	4,4,2,35,2,11
	.byte	'NODE',0,1
	.word	353
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'ANYED',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'CAPEIE',0,1
	.word	353
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	353
	.byte	1,5,2,35,3,11
	.byte	'DEPTH',0,1
	.word	353
	.byte	3,2,2,35,3,11
	.byte	'SOF',0,1
	.word	353
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	353
	.byte	1,0,2,35,3,0,3
	.byte	'Ifx_CAN_MECR_Bits',0,4,172,1,3
	.word	2254
	.byte	10
	.byte	'_Ifx_CAN_MESTAT_Bits',0,4,175,1,16,4,11
	.byte	'CAPT',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'CAPRED',0,1
	.word	353
	.byte	1,7,2,35,2,11
	.byte	'CAPE',0,1
	.word	353
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	384
	.byte	14,0,2,35,2,0,3
	.byte	'Ifx_CAN_MESTAT_Bits',0,4,181,1,3
	.word	2488
	.byte	10
	.byte	'_Ifx_CAN_MITR_Bits',0,4,184,1,16,4,11
	.byte	'IT',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_MITR_Bits',0,4,188,1,3
	.word	2618
	.byte	10
	.byte	'_Ifx_CAN_MO_AMR_Bits',0,4,191,1,16,4,11
	.byte	'AM',0,4
	.word	268
	.byte	29,3,2,35,0,11
	.byte	'MIDE',0,1
	.word	353
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	353
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_AMR_Bits',0,4,196,1,3
	.word	2708
	.byte	10
	.byte	'_Ifx_CAN_MO_AR_Bits',0,4,199,1,16,4,11
	.byte	'ID',0,4
	.word	268
	.byte	29,3,2,35,0,11
	.byte	'IDE',0,1
	.word	353
	.byte	1,2,2,35,3,11
	.byte	'PRI',0,1
	.word	353
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_AR_Bits',0,4,204,1,3
	.word	2818
	.byte	10
	.byte	'_Ifx_CAN_MO_CTR_Bits',0,4,207,1,16,4,11
	.byte	'RESRXPND',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'RESTXPND',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'RESRXUPD',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'RESNEWDAT',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'RESMSGLST',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'RESMSGVAL',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'RESRTSEL',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'RESRXEN',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'RESTXRQ',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'RESTXEN0',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'RESTXEN1',0,1
	.word	353
	.byte	1,5,2,35,1,11
	.byte	'RESDIR',0,1
	.word	353
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	353
	.byte	4,0,2,35,1,11
	.byte	'SETRXPND',0,1
	.word	353
	.byte	1,7,2,35,2,11
	.byte	'SETTXPND',0,1
	.word	353
	.byte	1,6,2,35,2,11
	.byte	'SETRXUPD',0,1
	.word	353
	.byte	1,5,2,35,2,11
	.byte	'SETNEWDAT',0,1
	.word	353
	.byte	1,4,2,35,2,11
	.byte	'SETMSGLST',0,1
	.word	353
	.byte	1,3,2,35,2,11
	.byte	'SETMSGVAL',0,1
	.word	353
	.byte	1,2,2,35,2,11
	.byte	'SETRTSEL',0,1
	.word	353
	.byte	1,1,2,35,2,11
	.byte	'SETRXEN',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'SETTXRQ',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'SETTXEN0',0,1
	.word	353
	.byte	1,6,2,35,3,11
	.byte	'SETTXEN1',0,1
	.word	353
	.byte	1,5,2,35,3,11
	.byte	'SETDIR',0,1
	.word	353
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	353
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_CTR_Bits',0,4,235,1,3
	.word	2917
	.byte	10
	.byte	'_Ifx_CAN_MO_DATAH_Bits',0,4,238,1,16,4,11
	.byte	'DB4',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB5',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB6',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB7',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_DATAH_Bits',0,4,244,1,3
	.word	3498
	.byte	10
	.byte	'_Ifx_CAN_MO_DATAL_Bits',0,4,247,1,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_DATAL_Bits',0,4,253,1,3
	.word	3619
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA0_Bits',0,4,128,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA0_Bits',0,4,134,2,3
	.word	3740
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA1_Bits',0,4,137,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA1_Bits',0,4,143,2,3
	.word	3863
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA2_Bits',0,4,146,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA2_Bits',0,4,152,2,3
	.word	3986
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA3_Bits',0,4,155,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA3_Bits',0,4,161,2,3
	.word	4109
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA4_Bits',0,4,164,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA4_Bits',0,4,170,2,3
	.word	4232
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA5_Bits',0,4,173,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA5_Bits',0,4,179,2,3
	.word	4355
	.byte	10
	.byte	'_Ifx_CAN_MO_EDATA6_Bits',0,4,182,2,16,4,11
	.byte	'DB0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'DB1',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'DB2',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'DB3',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_EDATA6_Bits',0,4,188,2,3
	.word	4478
	.byte	10
	.byte	'_Ifx_CAN_MO_FCR_Bits',0,4,191,2,16,4,11
	.byte	'MMC',0,1
	.word	353
	.byte	4,4,2,35,0,11
	.byte	'RXTOE',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'BRS',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'FDF',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'GDFS',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'IDC',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'DLCC',0,1
	.word	353
	.byte	1,5,2,35,1,11
	.byte	'DATC',0,1
	.word	353
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	353
	.byte	4,0,2,35,1,11
	.byte	'RXIE',0,1
	.word	353
	.byte	1,7,2,35,2,11
	.byte	'TXIE',0,1
	.word	353
	.byte	1,6,2,35,2,11
	.byte	'OVIE',0,1
	.word	353
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	353
	.byte	1,4,2,35,2,11
	.byte	'FRREN',0,1
	.word	353
	.byte	1,3,2,35,2,11
	.byte	'RMM',0,1
	.word	353
	.byte	1,2,2,35,2,11
	.byte	'SDT',0,1
	.word	353
	.byte	1,1,2,35,2,11
	.byte	'STT',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'DLC',0,1
	.word	353
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	353
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_FCR_Bits',0,4,213,2,3
	.word	4601
	.byte	10
	.byte	'_Ifx_CAN_MO_FGPR_Bits',0,4,216,2,16,4,11
	.byte	'BOT',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'TOP',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'CUR',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'SEL',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_FGPR_Bits',0,4,222,2,3
	.word	4999
	.byte	10
	.byte	'_Ifx_CAN_MO_IPR_Bits',0,4,225,2,16,4,11
	.byte	'RXINP',0,1
	.word	353
	.byte	4,4,2,35,0,11
	.byte	'TXINP',0,1
	.word	353
	.byte	4,0,2,35,0,11
	.byte	'MPN',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'CFCVAL',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_MO_IPR_Bits',0,4,231,2,3
	.word	5118
	.byte	10
	.byte	'_Ifx_CAN_MO_STAT_Bits',0,4,234,2,16,4,11
	.byte	'RXPND',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'TXPND',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'RXUPD',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'NEWDAT',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'MSGLST',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'MSGVAL',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'RTSEL',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'RXEN',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'TXRQ',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'TXEN0',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'TXEN1',0,1
	.word	353
	.byte	1,5,2,35,1,11
	.byte	'DIR',0,1
	.word	353
	.byte	1,4,2,35,1,11
	.byte	'LIST',0,1
	.word	353
	.byte	4,0,2,35,1,11
	.byte	'PPREV',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'PNEXT',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_MO_STAT_Bits',0,4,251,2,3
	.word	5242
	.byte	10
	.byte	'_Ifx_CAN_MSID_Bits',0,4,254,2,16,4,11
	.byte	'INDEX',0,1
	.word	353
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	268
	.byte	26,0,2,35,0,0,3
	.byte	'Ifx_CAN_MSID_Bits',0,4,130,3,3
	.word	5554
	.byte	10
	.byte	'_Ifx_CAN_MSIMASK_Bits',0,4,133,3,16,4,11
	.byte	'IM',0,4
	.word	268
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_CAN_MSIMASK_Bits',0,4,136,3,3
	.word	5646
	.byte	10
	.byte	'_Ifx_CAN_MSPND_Bits',0,4,139,3,16,4,11
	.byte	'PND',0,4
	.word	268
	.byte	32,0,2,35,0,0,3
	.byte	'Ifx_CAN_MSPND_Bits',0,4,142,3,3
	.word	5719
	.byte	10
	.byte	'_Ifx_CAN_N_BTEVR_Bits',0,4,145,3,16,4,11
	.byte	'BRP',0,1
	.word	353
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	353
	.byte	2,0,2,35,0,11
	.byte	'SJW',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	353
	.byte	3,1,2,35,1,11
	.byte	'DIV8',0,1
	.word	353
	.byte	1,0,2,35,1,11
	.byte	'TSEG2',0,1
	.word	353
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	353
	.byte	1,2,2,35,2,11
	.byte	'TSEG1',0,2
	.word	384
	.byte	6,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	353
	.byte	4,0,2,35,3,0,3
	.byte	'Ifx_CAN_N_BTEVR_Bits',0,4,156,3,3
	.word	5789
	.byte	10
	.byte	'_Ifx_CAN_N_BTR_Bits',0,4,159,3,16,4,11
	.byte	'BRP',0,1
	.word	353
	.byte	6,2,2,35,0,11
	.byte	'SJW',0,1
	.word	353
	.byte	2,0,2,35,0,11
	.byte	'TSEG1',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'TSEG2',0,1
	.word	353
	.byte	3,1,2,35,1,11
	.byte	'DIV8',0,1
	.word	353
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_N_BTR_Bits',0,4,167,3,3
	.word	6019
	.byte	10
	.byte	'_Ifx_CAN_N_CR_Bits',0,4,170,3,16,4,11
	.byte	'INIT',0,1
	.word	353
	.byte	1,7,2,35,0,11
	.byte	'TRIE',0,1
	.word	353
	.byte	1,6,2,35,0,11
	.byte	'LECIE',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'ALIE',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'CANDIS',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'TXDIS',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'CCE',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'CALM',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'SUSEN',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'FDEN',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	268
	.byte	22,0,2,35,0,0,3
	.byte	'Ifx_CAN_N_CR_Bits',0,4,183,3,3
	.word	6177
	.byte	10
	.byte	'_Ifx_CAN_N_ECNT_Bits',0,4,186,3,16,4,11
	.byte	'REC',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'TEC',0,1
	.word	353
	.byte	8,0,2,35,1,11
	.byte	'EWRNLVL',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'LETD',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'LEINC',0,1
	.word	353
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	353
	.byte	6,0,2,35,3,0,3
	.byte	'Ifx_CAN_N_ECNT_Bits',0,4,194,3,3
	.word	6417
	.byte	10
	.byte	'_Ifx_CAN_N_FBTR_Bits',0,4,197,3,16,4,11
	.byte	'FBRP',0,1
	.word	353
	.byte	6,2,2,35,0,11
	.byte	'FSJW',0,1
	.word	353
	.byte	2,0,2,35,0,11
	.byte	'FTSEG1',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'FTSEG2',0,1
	.word	353
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	268
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_CAN_N_FBTR_Bits',0,4,204,3,3
	.word	6579
	.byte	10
	.byte	'_Ifx_CAN_N_FCR_Bits',0,4,207,3,16,4,11
	.byte	'CFC',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'CFSEL',0,1
	.word	353
	.byte	3,5,2,35,2,11
	.byte	'CFMOD',0,1
	.word	353
	.byte	2,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	353
	.byte	1,2,2,35,2,11
	.byte	'CFCIE',0,1
	.word	353
	.byte	1,1,2,35,2,11
	.byte	'CFCOV',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_N_FCR_Bits',0,4,216,3,3
	.word	6727
	.byte	10
	.byte	'_Ifx_CAN_N_IPR_Bits',0,4,219,3,16,4,11
	.byte	'ALINP',0,1
	.word	353
	.byte	4,4,2,35,0,11
	.byte	'LECINP',0,1
	.word	353
	.byte	4,0,2,35,0,11
	.byte	'TRINP',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'CFCINP',0,1
	.word	353
	.byte	4,0,2,35,1,11
	.byte	'TEINP',0,1
	.word	353
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	384
	.byte	12,0,2,35,2,0,3
	.byte	'Ifx_CAN_N_IPR_Bits',0,4,227,3,3
	.word	6911
	.byte	10
	.byte	'_Ifx_CAN_N_PCR_Bits',0,4,230,3,16,4,11
	.byte	'RXSEL',0,1
	.word	353
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	353
	.byte	5,0,2,35,0,11
	.byte	'LBM',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	268
	.byte	23,0,2,35,0,0,3
	.byte	'Ifx_CAN_N_PCR_Bits',0,4,236,3,3
	.word	7076
	.byte	10
	.byte	'_Ifx_CAN_N_SR_Bits',0,4,239,3,16,4,11
	.byte	'LEC',0,1
	.word	353
	.byte	3,5,2,35,0,11
	.byte	'TXOK',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'RXOK',0,1
	.word	353
	.byte	1,3,2,35,0,11
	.byte	'ALERT',0,1
	.word	353
	.byte	1,2,2,35,0,11
	.byte	'EWRN',0,1
	.word	353
	.byte	1,1,2,35,0,11
	.byte	'BOFF',0,1
	.word	353
	.byte	1,0,2,35,0,11
	.byte	'LLE',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'LOE',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'SUSACK',0,1
	.word	353
	.byte	1,5,2,35,1,11
	.byte	'RESI',0,1
	.word	353
	.byte	1,4,2,35,1,11
	.byte	'FLEC',0,1
	.word	353
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	268
	.byte	17,0,2,35,0,0,3
	.byte	'Ifx_CAN_N_SR_Bits',0,4,253,3,3
	.word	7207
	.byte	10
	.byte	'_Ifx_CAN_N_TCCR_Bits',0,4,128,4,16,4,11
	.byte	'reserved_0',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'TPSC',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	268
	.byte	6,14,2,35,0,11
	.byte	'TRIGSRC',0,1
	.word	353
	.byte	3,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	384
	.byte	11,0,2,35,2,0,3
	.byte	'Ifx_CAN_N_TCCR_Bits',0,4,135,4,3
	.word	7459
	.byte	10
	.byte	'_Ifx_CAN_N_TDCR_Bits',0,4,138,4,16,4,11
	.byte	'TDCV',0,1
	.word	353
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	353
	.byte	3,0,2,35,0,11
	.byte	'TDCO',0,1
	.word	353
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	353
	.byte	3,1,2,35,1,11
	.byte	'TDC',0,1
	.word	353
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	384
	.byte	16,0,2,35,2,0,3
	.byte	'Ifx_CAN_N_TDCR_Bits',0,4,146,4,3
	.word	7619
	.byte	10
	.byte	'_Ifx_CAN_N_TRTR_Bits',0,4,149,4,16,4,11
	.byte	'RELOAD',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,1
	.word	353
	.byte	6,2,2,35,2,11
	.byte	'TEIE',0,1
	.word	353
	.byte	1,1,2,35,2,11
	.byte	'TE',0,1
	.word	353
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_N_TRTR_Bits',0,4,156,4,3
	.word	7791
	.byte	10
	.byte	'_Ifx_CAN_N_TTTR_Bits',0,4,159,4,16,4,11
	.byte	'RELOAD',0,2
	.word	384
	.byte	16,0,2,35,0,11
	.byte	'TXMO',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'STRT',0,1
	.word	353
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	353
	.byte	7,0,2,35,3,0,3
	.byte	'Ifx_CAN_N_TTTR_Bits',0,4,165,4,3
	.word	7942
	.byte	10
	.byte	'_Ifx_CAN_OCS_Bits',0,4,168,4,16,4,11
	.byte	'TGS',0,1
	.word	353
	.byte	2,6,2,35,0,11
	.byte	'TGB',0,1
	.word	353
	.byte	1,5,2,35,0,11
	.byte	'TG_P',0,1
	.word	353
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	268
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,1
	.word	353
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	353
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	353
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	353
	.byte	2,0,2,35,3,0,3
	.byte	'Ifx_CAN_OCS_Bits',0,4,178,4,3
	.word	8072
	.byte	10
	.byte	'_Ifx_CAN_PANCTR_Bits',0,4,181,4,16,4,11
	.byte	'PANCMD',0,1
	.word	353
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	353
	.byte	1,7,2,35,1,11
	.byte	'RBUSY',0,1
	.word	353
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	353
	.byte	6,0,2,35,1,11
	.byte	'PANAR1',0,1
	.word	353
	.byte	8,0,2,35,2,11
	.byte	'PANAR2',0,1
	.word	353
	.byte	8,0,2,35,3,0,3
	.byte	'Ifx_CAN_PANCTR_Bits',0,4,189,4,3
	.word	8264
	.byte	12,4,197,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	681
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_ACCEN0',0,4,202,4,3
	.word	8431
	.byte	12,4,205,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1238
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_ACCEN1',0,4,210,4,3
	.word	8495
	.byte	12,4,213,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1315
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_CLC',0,4,218,4,3
	.word	8559
	.byte	12,4,221,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1456
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_FDR',0,4,226,4,3
	.word	8620
	.byte	12,4,229,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1581
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_ID',0,4,234,4,3
	.word	8681
	.byte	12,4,237,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1686
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_KRST0',0,4,242,4,3
	.word	8741
	.byte	12,4,245,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1795
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_KRST1',0,4,250,4,3
	.word	8804
	.byte	12,4,253,4,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1886
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_KRSTCLR',0,4,130,5,3
	.word	8867
	.byte	12,4,133,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	1982
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_LIST',0,4,138,5,3
	.word	8932
	.byte	12,4,141,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2123
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MCR',0,4,146,5,3
	.word	8994
	.byte	12,4,149,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2254
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MECR',0,4,154,5,3
	.word	9055
	.byte	12,4,157,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2488
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MESTAT',0,4,162,5,3
	.word	9117
	.byte	12,4,165,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2618
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MITR',0,4,170,5,3
	.word	9181
	.byte	12,4,173,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2708
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_AMR',0,4,178,5,3
	.word	9243
	.byte	12,4,181,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2818
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_AR',0,4,186,5,3
	.word	9307
	.byte	12,4,189,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	2917
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_CTR',0,4,194,5,3
	.word	9370
	.byte	12,4,197,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3498
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_DATAH',0,4,202,5,3
	.word	9434
	.byte	12,4,205,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3619
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_DATAL',0,4,210,5,3
	.word	9500
	.byte	12,4,213,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3740
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA0',0,4,218,5,3
	.word	9566
	.byte	12,4,221,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3863
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA1',0,4,226,5,3
	.word	9633
	.byte	12,4,229,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	3986
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA2',0,4,234,5,3
	.word	9700
	.byte	12,4,237,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4109
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA3',0,4,242,5,3
	.word	9767
	.byte	12,4,245,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4232
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA4',0,4,250,5,3
	.word	9834
	.byte	12,4,253,5,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4355
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA5',0,4,130,6,3
	.word	9901
	.byte	12,4,133,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4478
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_EDATA6',0,4,138,6,3
	.word	9968
	.byte	12,4,141,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4601
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_FCR',0,4,146,6,3
	.word	10035
	.byte	12,4,149,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	4999
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_FGPR',0,4,154,6,3
	.word	10099
	.byte	12,4,157,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5118
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_IPR',0,4,162,6,3
	.word	10164
	.byte	12,4,165,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5242
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MO_STAT',0,4,170,6,3
	.word	10228
	.byte	12,4,173,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5554
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MSID',0,4,178,6,3
	.word	10293
	.byte	12,4,181,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5646
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MSIMASK',0,4,186,6,3
	.word	10355
	.byte	12,4,189,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5719
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_MSPND',0,4,194,6,3
	.word	10420
	.byte	12,4,197,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	5789
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_BTEVR',0,4,202,6,3
	.word	10483
	.byte	12,4,205,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6019
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_BTR',0,4,210,6,3
	.word	10548
	.byte	12,4,213,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6177
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_CR',0,4,218,6,3
	.word	10611
	.byte	12,4,221,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6417
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_ECNT',0,4,226,6,3
	.word	10673
	.byte	12,4,229,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6579
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_FBTR',0,4,234,6,3
	.word	10737
	.byte	12,4,237,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6727
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_FCR',0,4,242,6,3
	.word	10801
	.byte	12,4,245,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	6911
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_IPR',0,4,250,6,3
	.word	10864
	.byte	12,4,253,6,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7076
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_PCR',0,4,130,7,3
	.word	10927
	.byte	12,4,133,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7207
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_SR',0,4,138,7,3
	.word	10990
	.byte	12,4,141,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7459
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_TCCR',0,4,146,7,3
	.word	11052
	.byte	12,4,149,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7619
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_TDCR',0,4,154,7,3
	.word	11116
	.byte	12,4,157,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7791
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_TRTR',0,4,162,7,3
	.word	11180
	.byte	12,4,165,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	7942
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_N_TTTR',0,4,170,7,3
	.word	11244
	.byte	12,4,173,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8072
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_OCS',0,4,178,7,3
	.word	11308
	.byte	12,4,181,7,9,4,9
	.byte	'U',0
	.word	268
	.byte	4,2,35,0,9
	.byte	'I',0
	.word	301
	.byte	4,2,35,0,9
	.byte	'B',0
	.word	8264
	.byte	4,2,35,0,0,3
	.byte	'Ifx_CAN_PANCTR',0,4,186,7,3
	.word	11369
	.byte	12,4,199,7,5,4,9
	.byte	'EDATA0',0
	.word	9566
	.byte	4,2,35,0,9
	.byte	'FCR',0
	.word	10035
	.byte	4,2,35,0,0,12,4,205,7,5,4,9
	.byte	'EDATA1',0
	.word	9633
	.byte	4,2,35,0,9
	.byte	'FGPR',0
	.word	10099
	.byte	4,2,35,0,0,12,4,211,7,5,4,9
	.byte	'EDATA2',0
	.word	9700
	.byte	4,2,35,0,9
	.byte	'IPR',0
	.word	10164
	.byte	4,2,35,0,0,12,4,217,7,5,4,9
	.byte	'AMR',0
	.word	9243
	.byte	4,2,35,0,9
	.byte	'EDATA3',0
	.word	9767
	.byte	4,2,35,0,0,12,4,223,7,5,4,9
	.byte	'DATAL',0
	.word	9500
	.byte	4,2,35,0,9
	.byte	'EDATA4',0
	.word	9834
	.byte	4,2,35,0,0,12,4,229,7,5,4,9
	.byte	'DATAH',0
	.word	9434
	.byte	4,2,35,0,9
	.byte	'EDATA5',0
	.word	9901
	.byte	4,2,35,0,0,12,4,235,7,5,4,9
	.byte	'AR',0
	.word	9307
	.byte	4,2,35,0,9
	.byte	'EDATA6',0
	.word	9968
	.byte	4,2,35,0,0,12,4,241,7,5,4,9
	.byte	'CTR',0
	.word	9370
	.byte	4,2,35,0,9
	.byte	'STAT',0
	.word	10228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CAN_MO',0,4,197,7,25,32,13
	.word	11433
	.byte	4,2,35,0,13
	.word	11469
	.byte	4,2,35,4,13
	.word	11506
	.byte	4,2,35,8,13
	.word	11542
	.byte	4,2,35,12,13
	.word	11578
	.byte	4,2,35,16,13
	.word	11616
	.byte	4,2,35,20,13
	.word	11654
	.byte	4,2,35,24,13
	.word	11689
	.byte	4,2,35,28,0,7
	.word	11723
	.byte	3
	.byte	'Ifx_CAN_MO',0,4,247,7,3
	.word	11814
	.byte	12,4,128,8,5,4,9
	.byte	'BTEVR',0
	.word	10483
	.byte	4,2,35,0,9
	.byte	'BTR',0
	.word	10548
	.byte	4,2,35,0,0,14,8
	.word	353
	.byte	15,7,0,14,192,1
	.word	353
	.byte	15,191,1,0,10
	.byte	'_Ifx_CAN_N',0,4,250,7,25,128,2,9
	.byte	'CR',0
	.word	10611
	.byte	4,2,35,0,9
	.byte	'SR',0
	.word	10990
	.byte	4,2,35,4,9
	.byte	'IPR',0
	.word	10864
	.byte	4,2,35,8,9
	.byte	'PCR',0
	.word	10927
	.byte	4,2,35,12,13
	.word	11839
	.byte	4,2,35,16,9
	.byte	'ECNT',0
	.word	10673
	.byte	4,2,35,20,9
	.byte	'FCR',0
	.word	10801
	.byte	4,2,35,24,9
	.byte	'TCCR',0
	.word	11052
	.byte	4,2,35,28,9
	.byte	'TRTR',0
	.word	11180
	.byte	4,2,35,32,9
	.byte	'TATTR',0
	.word	11244
	.byte	4,2,35,36,9
	.byte	'TBTTR',0
	.word	11244
	.byte	4,2,35,40,9
	.byte	'TCTTR',0
	.word	11244
	.byte	4,2,35,44,9
	.byte	'reserved_30',0
	.word	11874
	.byte	8,2,35,48,9
	.byte	'FBTR',0
	.word	10737
	.byte	4,2,35,56,9
	.byte	'TDCR',0
	.word	11116
	.byte	4,2,35,60,9
	.byte	'reserved_40',0
	.word	11883
	.byte	192,1,2,35,64,0,7
	.word	11894
	.byte	3
	.byte	'Ifx_CAN_N',0,4,145,8,3
	.word	12143
	.byte	14,8
	.word	615
	.byte	15,0,0
.L8:
	.byte	16
	.word	12167
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,53,0,73,19,0,0,8,19,1,58,15,59,15,57,15,11
	.byte	15,0,0,9,13,0,3,8,73,19,11,15,56,9,0,0,10,19,1,3,8,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19
	.byte	13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13,0,73,19,11,15,56,9,0,0,14,1,1,11,15,73
	.byte	19,0,0,15,33,0,47,15,0,0,16,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L10-.L9
.L9:
	.half	3
	.word	.L12-.L11
.L11:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCan_regdef.h',0,1,0,0,0
.L12:
.L10:
	.sdecl	'.debug_info',debug,cluster('IfxMultican_cfg_indexMap')
	.sect	'.debug_info'
.L6:
	.word	275
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxMultican_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxMultican_cfg_indexMap',0,1,55,30
	.word	.L8
	.byte	1,5,3
	.word	IfxMultican_cfg_indexMap
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMultican_cfg_indexMap')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
