	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc33324a --dep-file=IfxSent_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT0A_AN24_IN',data,rom,cluster('IfxSent_SENT0A_AN24_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT0A_AN24_IN'
	.global	IfxSent_SENT0A_AN24_IN
	.align	4
IfxSent_SENT0A_AN24_IN:	.type	object
	.size	IfxSent_SENT0A_AN24_IN,20
	.word	-268423168
	.space	8
	.byte	24
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT0A_P40_0_IN',data,rom,cluster('IfxSent_SENT0A_P40_0_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT0A_P40_0_IN'
	.global	IfxSent_SENT0A_P40_0_IN
	.align	4
IfxSent_SENT0A_P40_0_IN:	.type	object
	.size	IfxSent_SENT0A_P40_0_IN,20
	.word	-268423168
	.space	4
	.word	-268181504
	.space	8
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT0B_P00_1_IN',data,rom,cluster('IfxSent_SENT0B_P00_1_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT0B_P00_1_IN'
	.global	IfxSent_SENT0B_P00_1_IN
	.align	4
IfxSent_SENT0B_P00_1_IN:	.type	object
	.size	IfxSent_SENT0B_P00_1_IN,20
	.word	-268423168
	.space	4
	.word	-268197888
	.byte	1
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT0C_P02_8_IN',data,rom,cluster('IfxSent_SENT0C_P02_8_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT0C_P02_8_IN'
	.global	IfxSent_SENT0C_P02_8_IN
	.align	4
IfxSent_SENT0C_P02_8_IN:	.type	object
	.size	IfxSent_SENT0C_P02_8_IN,20
	.word	-268423168
	.space	4
	.word	-268197376
	.byte	8
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT1A_AN25_IN',data,rom,cluster('IfxSent_SENT1A_AN25_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT1A_AN25_IN'
	.global	IfxSent_SENT1A_AN25_IN
	.align	4
IfxSent_SENT1A_AN25_IN:	.type	object
	.size	IfxSent_SENT1A_AN25_IN,20
	.word	-268423168
	.byte	1
	.space	7
	.byte	25
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT1A_P40_1_IN',data,rom,cluster('IfxSent_SENT1A_P40_1_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT1A_P40_1_IN'
	.global	IfxSent_SENT1A_P40_1_IN
	.align	4
IfxSent_SENT1A_P40_1_IN:	.type	object
	.size	IfxSent_SENT1A_P40_1_IN,20
	.word	-268423168
	.byte	1
	.space	3
	.word	-268181504
	.byte	1
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT1B_P00_2_IN',data,rom,cluster('IfxSent_SENT1B_P00_2_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT1B_P00_2_IN'
	.global	IfxSent_SENT1B_P00_2_IN
	.align	4
IfxSent_SENT1B_P00_2_IN:	.type	object
	.size	IfxSent_SENT1B_P00_2_IN,20
	.word	-268423168
	.byte	1
	.space	3
	.word	-268197888
	.byte	2
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT1C_P02_7_IN',data,rom,cluster('IfxSent_SENT1C_P02_7_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT1C_P02_7_IN'
	.global	IfxSent_SENT1C_P02_7_IN
	.align	4
IfxSent_SENT1C_P02_7_IN:	.type	object
	.size	IfxSent_SENT1C_P02_7_IN,20
	.word	-268423168
	.byte	1
	.space	3
	.word	-268197376
	.byte	7
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2A_AN26_IN',data,rom,cluster('IfxSent_SENT2A_AN26_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2A_AN26_IN'
	.global	IfxSent_SENT2A_AN26_IN
	.align	4
IfxSent_SENT2A_AN26_IN:	.type	object
	.size	IfxSent_SENT2A_AN26_IN,20
	.word	-268423168
	.byte	2
	.space	7
	.byte	26
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2A_P40_2_IN',data,rom,cluster('IfxSent_SENT2A_P40_2_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2A_P40_2_IN'
	.global	IfxSent_SENT2A_P40_2_IN
	.align	4
IfxSent_SENT2A_P40_2_IN:	.type	object
	.size	IfxSent_SENT2A_P40_2_IN,20
	.word	-268423168
	.byte	2
	.space	3
	.word	-268181504
	.byte	2
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2B_P00_3_IN',data,rom,cluster('IfxSent_SENT2B_P00_3_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2B_P00_3_IN'
	.global	IfxSent_SENT2B_P00_3_IN
	.align	4
IfxSent_SENT2B_P00_3_IN:	.type	object
	.size	IfxSent_SENT2B_P00_3_IN,20
	.word	-268423168
	.byte	2
	.space	3
	.word	-268197888
	.byte	3
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2C_P02_6_IN',data,rom,cluster('IfxSent_SENT2C_P02_6_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2C_P02_6_IN'
	.global	IfxSent_SENT2C_P02_6_IN
	.align	4
IfxSent_SENT2C_P02_6_IN:	.type	object
	.size	IfxSent_SENT2C_P02_6_IN,20
	.word	-268423168
	.byte	2
	.space	3
	.word	-268197376
	.byte	6
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2D_AN36_IN',data,rom,cluster('IfxSent_SENT2D_AN36_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2D_AN36_IN'
	.global	IfxSent_SENT2D_AN36_IN
	.align	4
IfxSent_SENT2D_AN36_IN:	.type	object
	.size	IfxSent_SENT2D_AN36_IN,20
	.word	-268423168
	.byte	2
	.space	7
	.byte	36
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT2D_P40_6_IN',data,rom,cluster('IfxSent_SENT2D_P40_6_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT2D_P40_6_IN'
	.global	IfxSent_SENT2D_P40_6_IN
	.align	4
IfxSent_SENT2D_P40_6_IN:	.type	object
	.size	IfxSent_SENT2D_P40_6_IN,20
	.word	-268423168
	.byte	2
	.space	3
	.word	-268181504
	.byte	6
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3A_AN27_IN',data,rom,cluster('IfxSent_SENT3A_AN27_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3A_AN27_IN'
	.global	IfxSent_SENT3A_AN27_IN
	.align	4
IfxSent_SENT3A_AN27_IN:	.type	object
	.size	IfxSent_SENT3A_AN27_IN,20
	.word	-268423168
	.byte	3
	.space	7
	.byte	27
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3A_P40_3_IN',data,rom,cluster('IfxSent_SENT3A_P40_3_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3A_P40_3_IN'
	.global	IfxSent_SENT3A_P40_3_IN
	.align	4
IfxSent_SENT3A_P40_3_IN:	.type	object
	.size	IfxSent_SENT3A_P40_3_IN,20
	.word	-268423168
	.byte	3
	.space	3
	.word	-268181504
	.byte	3
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3B_P00_4_IN',data,rom,cluster('IfxSent_SENT3B_P00_4_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3B_P00_4_IN'
	.global	IfxSent_SENT3B_P00_4_IN
	.align	4
IfxSent_SENT3B_P00_4_IN:	.type	object
	.size	IfxSent_SENT3B_P00_4_IN,20
	.word	-268423168
	.byte	3
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3C_P02_5_IN',data,rom,cluster('IfxSent_SENT3C_P02_5_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3C_P02_5_IN'
	.global	IfxSent_SENT3C_P02_5_IN
	.align	4
IfxSent_SENT3C_P02_5_IN:	.type	object
	.size	IfxSent_SENT3C_P02_5_IN,20
	.word	-268423168
	.byte	3
	.space	3
	.word	-268197376
	.byte	5
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3D_AN37_IN',data,rom,cluster('IfxSent_SENT3D_AN37_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3D_AN37_IN'
	.global	IfxSent_SENT3D_AN37_IN
	.align	4
IfxSent_SENT3D_AN37_IN:	.type	object
	.size	IfxSent_SENT3D_AN37_IN,20
	.word	-268423168
	.byte	3
	.space	7
	.byte	37
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT3D_P40_7_IN',data,rom,cluster('IfxSent_SENT3D_P40_7_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT3D_P40_7_IN'
	.global	IfxSent_SENT3D_P40_7_IN
	.align	4
IfxSent_SENT3D_P40_7_IN:	.type	object
	.size	IfxSent_SENT3D_P40_7_IN,20
	.word	-268423168
	.byte	3
	.space	3
	.word	-268181504
	.byte	7
	.space	3
	.byte	3
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT4A_AN38_IN',data,rom,cluster('IfxSent_SENT4A_AN38_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT4A_AN38_IN'
	.global	IfxSent_SENT4A_AN38_IN
	.align	4
IfxSent_SENT4A_AN38_IN:	.type	object
	.size	IfxSent_SENT4A_AN38_IN,20
	.word	-268423168
	.byte	4
	.space	7
	.byte	38
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT4A_P40_8_IN',data,rom,cluster('IfxSent_SENT4A_P40_8_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT4A_P40_8_IN'
	.global	IfxSent_SENT4A_P40_8_IN
	.align	4
IfxSent_SENT4A_P40_8_IN:	.type	object
	.size	IfxSent_SENT4A_P40_8_IN,20
	.word	-268423168
	.byte	4
	.space	3
	.word	-268181504
	.byte	8
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT4B_P00_5_IN',data,rom,cluster('IfxSent_SENT4B_P00_5_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT4B_P00_5_IN'
	.global	IfxSent_SENT4B_P00_5_IN
	.align	4
IfxSent_SENT4B_P00_5_IN:	.type	object
	.size	IfxSent_SENT4B_P00_5_IN,20
	.word	-268423168
	.byte	4
	.space	3
	.word	-268197888
	.byte	5
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT4C_P33_6_IN',data,rom,cluster('IfxSent_SENT4C_P33_6_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT4C_P33_6_IN'
	.global	IfxSent_SENT4C_P33_6_IN
	.align	4
IfxSent_SENT4C_P33_6_IN:	.type	object
	.size	IfxSent_SENT4C_P33_6_IN,20
	.word	-268423168
	.byte	4
	.space	3
	.word	-268184832
	.byte	6
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT5A_AN39_IN',data,rom,cluster('IfxSent_SENT5A_AN39_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT5A_AN39_IN'
	.global	IfxSent_SENT5A_AN39_IN
	.align	4
IfxSent_SENT5A_AN39_IN:	.type	object
	.size	IfxSent_SENT5A_AN39_IN,20
	.word	-268423168
	.byte	5
	.space	7
	.byte	39
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT5A_P40_9_IN',data,rom,cluster('IfxSent_SENT5A_P40_9_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT5A_P40_9_IN'
	.global	IfxSent_SENT5A_P40_9_IN
	.align	4
IfxSent_SENT5A_P40_9_IN:	.type	object
	.size	IfxSent_SENT5A_P40_9_IN,20
	.word	-268423168
	.byte	5
	.space	3
	.word	-268181504
	.byte	9
	.space	7
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT5B_P00_6_IN',data,rom,cluster('IfxSent_SENT5B_P00_6_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT5B_P00_6_IN'
	.global	IfxSent_SENT5B_P00_6_IN
	.align	4
IfxSent_SENT5B_P00_6_IN:	.type	object
	.size	IfxSent_SENT5B_P00_6_IN,20
	.word	-268423168
	.byte	5
	.space	3
	.word	-268197888
	.byte	6
	.space	3
	.byte	1
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SENT5C_P33_5_IN',data,rom,cluster('IfxSent_SENT5C_P33_5_IN')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SENT5C_P33_5_IN'
	.global	IfxSent_SENT5C_P33_5_IN
	.align	4
IfxSent_SENT5C_P33_5_IN:	.type	object
	.size	IfxSent_SENT5C_P33_5_IN,20
	.word	-268423168
	.byte	5
	.space	3
	.word	-268184832
	.byte	5
	.space	3
	.byte	2
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC0_P00_1_OUT',data,rom,cluster('IfxSent_SPC0_P00_1_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC0_P00_1_OUT'
	.global	IfxSent_SPC0_P00_1_OUT
	.align	4
IfxSent_SPC0_P00_1_OUT:	.type	object
	.size	IfxSent_SPC0_P00_1_OUT,20
	.word	-268423168
	.space	4
	.word	-268197888
	.byte	1
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC1_P02_7_OUT',data,rom,cluster('IfxSent_SPC1_P02_7_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC1_P02_7_OUT'
	.global	IfxSent_SPC1_P02_7_OUT
	.align	4
IfxSent_SPC1_P02_7_OUT:	.type	object
	.size	IfxSent_SPC1_P02_7_OUT,20
	.word	-268423168
	.byte	1
	.space	3
	.word	-268197376
	.byte	7
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC2_P00_3_OUT',data,rom,cluster('IfxSent_SPC2_P00_3_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC2_P00_3_OUT'
	.global	IfxSent_SPC2_P00_3_OUT
	.align	4
IfxSent_SPC2_P00_3_OUT:	.type	object
	.size	IfxSent_SPC2_P00_3_OUT,20
	.word	-268423168
	.byte	2
	.space	3
	.word	-268197888
	.byte	3
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC3_P00_4_OUT',data,rom,cluster('IfxSent_SPC3_P00_4_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC3_P00_4_OUT'
	.global	IfxSent_SPC3_P00_4_OUT
	.align	4
IfxSent_SPC3_P00_4_OUT:	.type	object
	.size	IfxSent_SPC3_P00_4_OUT,20
	.word	-268423168
	.byte	3
	.space	3
	.word	-268197888
	.byte	4
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC4_P00_5_OUT',data,rom,cluster('IfxSent_SPC4_P00_5_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC4_P00_5_OUT'
	.global	IfxSent_SPC4_P00_5_OUT
	.align	4
IfxSent_SPC4_P00_5_OUT:	.type	object
	.size	IfxSent_SPC4_P00_5_OUT,20
	.word	-268423168
	.byte	4
	.space	3
	.word	-268197888
	.byte	5
	.space	3
	.byte	176
	.space	3
	.sdecl	'.rodata.IfxSent_PinMap.IfxSent_SPC5_P00_6_OUT',data,rom,cluster('IfxSent_SPC5_P00_6_OUT')
	.sect	'.rodata.IfxSent_PinMap.IfxSent_SPC5_P00_6_OUT'
	.global	IfxSent_SPC5_P00_6_OUT
	.align	4
IfxSent_SPC5_P00_6_OUT:	.type	object
	.size	IfxSent_SPC5_P00_6_OUT,20
	.word	-268423168
	.byte	5
	.space	3
	.word	-268197888
	.byte	6
	.space	3
	.byte	176
	.space	3
	.sdecl	'.data.IfxSent_PinMap.IfxSent_Sent_In_pinTable',data,cluster('IfxSent_Sent_In_pinTable')
	.sect	'.data.IfxSent_PinMap.IfxSent_Sent_In_pinTable'
	.global	IfxSent_Sent_In_pinTable
	.align	4
IfxSent_Sent_In_pinTable:	.type	object
	.size	IfxSent_Sent_In_pinTable,96
	.word	IfxSent_SENT0A_P40_0_IN,IfxSent_SENT0B_P00_1_IN,IfxSent_SENT0C_P02_8_IN
	.space	4
	.word	IfxSent_SENT1A_P40_1_IN,IfxSent_SENT1B_P00_2_IN,IfxSent_SENT1C_P02_7_IN
	.space	4
	.word	IfxSent_SENT2A_P40_2_IN,IfxSent_SENT2B_P00_3_IN,IfxSent_SENT2C_P02_6_IN,IfxSent_SENT2D_P40_6_IN,IfxSent_SENT3A_P40_3_IN,IfxSent_SENT3B_P00_4_IN,IfxSent_SENT3C_P02_5_IN,IfxSent_SENT3D_P40_7_IN
	.word	IfxSent_SENT4A_P40_8_IN,IfxSent_SENT4B_P00_5_IN,IfxSent_SENT4C_P33_6_IN
	.space	4
	.word	IfxSent_SENT5A_P40_9_IN,IfxSent_SENT5B_P00_6_IN,IfxSent_SENT5C_P33_5_IN
	.space	4
	.sdecl	'.data.IfxSent_PinMap.IfxSent_Spc_Out_pinTable',data,cluster('IfxSent_Spc_Out_pinTable')
	.sect	'.data.IfxSent_PinMap.IfxSent_Spc_Out_pinTable'
	.global	IfxSent_Spc_Out_pinTable
	.align	4
IfxSent_Spc_Out_pinTable:	.type	object
	.size	IfxSent_Spc_Out_pinTable,24
	.word	IfxSent_SPC0_P00_1_OUT,IfxSent_SPC1_P02_7_OUT,IfxSent_SPC2_P00_3_OUT,IfxSent_SPC3_P00_4_OUT
	.word	IfxSent_SPC4_P00_5_OUT,IfxSent_SPC5_P00_6_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	44799
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	238
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	241
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	286
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	298
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	378
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	352
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	384
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	384
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	352
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	532
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	848
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1419
	.byte	4,2,35,0,0,14,4
	.word	493
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1547
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1977
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	493
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	493
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2414
	.byte	4,2,35,0,0,14,24
	.word	493
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2737
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3041
	.byte	4,2,35,0,0,14,8
	.word	493
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3366
	.byte	4,2,35,0,0,14,12
	.word	493
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3706
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4072
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4674
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5369
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6034
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6382
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6506
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6590
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6770
	.byte	4,2,35,0,0,14,76
	.word	493
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7023
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7110
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	808
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1379
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1498
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1538
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1722
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1937
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2154
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2374
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1538
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2688
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2728
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3001
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3317
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3357
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3657
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3697
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4032
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4318
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3357
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4465
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4634
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4806
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4981
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5155
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5329
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5505
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5661
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5994
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6342
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3357
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6466
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6715
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6974
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7014
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7070
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7637
	.byte	4,3,35,252,1,0,16
	.word	7677
	.byte	3
	.word	8280
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8285
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	493
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8290
	.byte	6,0,19
	.word	246
	.byte	20
	.word	272
	.byte	6,0,19
	.word	307
	.byte	20
	.word	339
	.byte	6,0,19
	.word	389
	.byte	20
	.word	408
	.byte	6,0,19
	.word	424
	.byte	20
	.word	439
	.byte	20
	.word	453
	.byte	6,0,19
	.word	8393
	.byte	20
	.word	8421
	.byte	20
	.word	8435
	.byte	20
	.word	8453
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8546
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	470
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	486
	.byte	22,1,3
	.word	8614
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8616
	.byte	10
	.byte	'_Ifx_SENT_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SENT_ACCEN0_Bits',0,6,79,3
	.word	8639
	.byte	10
	.byte	'_Ifx_SENT_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SENT_ACCEN1_Bits',0,6,85,3
	.word	9198
	.byte	10
	.byte	'_Ifx_SENT_CH_CFDR_Bits',0,6,88,16,4,11
	.byte	'DIV',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'DIVM',0,2
	.word	510
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SENT_CH_CFDR_Bits',0,6,94,3
	.word	9277
	.byte	10
	.byte	'_Ifx_SENT_CH_CPDR_Bits',0,6,97,16,4,11
	.byte	'PDIV',0,2
	.word	510
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_CPDR_Bits',0,6,101,3
	.word	9413
	.byte	10
	.byte	'_Ifx_SENT_CH_INP_Bits',0,6,104,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'TDI',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TBI',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ERRI',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'SDI',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'WDI',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SENT_CH_INP_Bits',0,6,114,3
	.word	9511
	.byte	10
	.byte	'_Ifx_SENT_CH_INTCLR_Bits',0,6,117,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TDI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FRI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FDI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NNI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NVI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'WSI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SCRI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'WDI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTCLR_Bits',0,6,134,1,3
	.word	9689
	.byte	10
	.byte	'_Ifx_SENT_CH_INTEN_Bits',0,6,137,1,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TDI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FRI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FDI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NNI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NVI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'WSI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SCRI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'WDI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTEN_Bits',0,6,154,1,3
	.word	9988
	.byte	10
	.byte	'_Ifx_SENT_CH_INTSET_Bits',0,6,157,1,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TDI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FRI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FDI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NNI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NVI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'WSI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SCRI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'WDI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTSET_Bits',0,6,174,1,3
	.word	10286
	.byte	10
	.byte	'_Ifx_SENT_CH_INTSTAT_Bits',0,6,177,1,16,4,11
	.byte	'RSI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RDI',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RBI',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'TDI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TBI',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FRI',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FDI',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'NNI',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'NVI',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'CRCI',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'WSI',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SDI',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'SCRI',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'WDI',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,4
	.word	470
	.byte	18,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTSTAT_Bits',0,6,194,1,3
	.word	10586
	.byte	10
	.byte	'_Ifx_SENT_CH_IOCR_Bits',0,6,197,1,16,4,11
	.byte	'ALTI',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'DEPTH',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'OIE',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'IIE',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'CEC',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'REG',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'FEG',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CREG',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'CFEG',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'ETS',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'EC',0,2
	.word	510
	.byte	8,4,2,35,2,11
	.byte	'CTR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'TRM',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SENT_CH_IOCR_Bits',0,6,216,1,3
	.word	10888
	.byte	10
	.byte	'_Ifx_SENT_CH_RCR_Bits',0,6,219,1,16,4,11
	.byte	'CEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IEP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ACE',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SNI',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SDP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'SCDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'CDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'CFC',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FRL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CRZ',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'ESF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IDE',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SUSEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_SENT_CH_RCR_Bits',0,6,235,1,3
	.word	11223
	.byte	10
	.byte	'_Ifx_SENT_CH_RSR_Bits',0,6,238,1,16,4,11
	.byte	'CRC',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'CST',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SCN',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	470
	.byte	20,0,2,35,0,0,21
	.byte	'Ifx_SENT_CH_RSR_Bits',0,6,245,1,3
	.word	11505
	.byte	10
	.byte	'_Ifx_SENT_CH_SCR_Bits',0,6,248,1,16,4,11
	.byte	'PLEN',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'TRIG',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'DEL',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'BASE',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'TRQ',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SENT_CH_SCR_Bits',0,6,128,2,3
	.word	11654
	.byte	10
	.byte	'_Ifx_SENT_CH_SDS_Bits',0,6,131,2,16,4,11
	.byte	'SD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'MID',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SCRC',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'CON',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SENT_CH_SDS_Bits',0,6,138,2,3
	.word	11814
	.byte	10
	.byte	'_Ifx_SENT_CH_VIEW_Bits',0,6,141,2,16,4,11
	.byte	'RDNP0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'RDNP1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'RDNP2',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'RDNP3',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'RDNP4',0,1
	.word	493
	.byte	3,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'RDNP5',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'RDNP6',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RDNP7',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SENT_CH_VIEW_Bits',0,6,159,2,3
	.word	11956
	.byte	10
	.byte	'_Ifx_SENT_CH_WDT_Bits',0,6,162,2,16,4,11
	.byte	'WDLx',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SENT_CH_WDT_Bits',0,6,166,2,3
	.word	12335
	.byte	10
	.byte	'_Ifx_SENT_CLC_Bits',0,6,169,2,16,4,11
	.byte	'DISR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'RMC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SENT_CLC_Bits',0,6,178,2,3
	.word	12433
	.byte	10
	.byte	'_Ifx_SENT_FDR_Bits',0,6,181,2,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SENT_FDR_Bits',0,6,188,2,3
	.word	12616
	.byte	10
	.byte	'_Ifx_SENT_ID_Bits',0,6,191,2,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SENT_ID_Bits',0,6,196,2,3
	.word	12763
	.byte	10
	.byte	'_Ifx_SENT_INTOV_Bits',0,6,199,2,16,4,11
	.byte	'IPC0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPC1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPC2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPC3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPC4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPC5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SENT_INTOV_Bits',0,6,208,2,3
	.word	12872
	.byte	10
	.byte	'_Ifx_SENT_KRST0_Bits',0,6,211,2,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SENT_KRST0_Bits',0,6,216,2,3
	.word	13047
	.byte	10
	.byte	'_Ifx_SENT_KRST1_Bits',0,6,219,2,16,4,11
	.byte	'RST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SENT_KRST1_Bits',0,6,223,2,3
	.word	13160
	.byte	10
	.byte	'_Ifx_SENT_KRSTCLR_Bits',0,6,226,2,16,4,11
	.byte	'CLR',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SENT_KRSTCLR_Bits',0,6,230,2,3
	.word	13254
	.byte	10
	.byte	'_Ifx_SENT_OCS_Bits',0,6,233,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SENT_OCS_Bits',0,6,240,2,3
	.word	13352
	.byte	10
	.byte	'_Ifx_SENT_RDR_Bits',0,6,243,2,16,4,11
	.byte	'RD0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'RD1',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'RD2',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'RD3',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'RD4',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'RD5',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'RD6',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'RD7',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SENT_RDR_Bits',0,6,253,2,3
	.word	13500
	.byte	10
	.byte	'_Ifx_SENT_RTS_Bits',0,6,128,3,16,4,11
	.byte	'LTS',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SENT_RTS_Bits',0,6,131,3,3
	.word	13673
	.byte	10
	.byte	'_Ifx_SENT_TPD_Bits',0,6,134,3,16,4,11
	.byte	'TDIV',0,4
	.word	470
	.byte	20,12,2,35,0,11
	.byte	'reserved_20',0,2
	.word	510
	.byte	12,0,2,35,2,0,21
	.byte	'Ifx_SENT_TPD_Bits',0,6,138,3,3
	.word	13741
	.byte	10
	.byte	'_Ifx_SENT_TSR_Bits',0,6,141,3,16,4,11
	.byte	'CTS',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SENT_TSR_Bits',0,6,144,3,3
	.word	13833
	.byte	12,6,152,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_ACCEN0',0,6,157,3,3
	.word	13901
	.byte	12,6,160,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9198
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_ACCEN1',0,6,165,3,3
	.word	13966
	.byte	12,6,168,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_CFDR',0,6,173,3,3
	.word	14031
	.byte	12,6,176,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_CPDR',0,6,181,3,3
	.word	14097
	.byte	12,6,184,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9511
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INP',0,6,189,3,3
	.word	14163
	.byte	12,6,192,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9689
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTCLR',0,6,197,3,3
	.word	14228
	.byte	12,6,200,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9988
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTEN',0,6,205,3,3
	.word	14296
	.byte	12,6,208,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10286
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTSET',0,6,213,3,3
	.word	14363
	.byte	12,6,216,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10586
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_INTSTAT',0,6,221,3,3
	.word	14431
	.byte	12,6,224,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10888
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_IOCR',0,6,229,3,3
	.word	14500
	.byte	12,6,232,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11223
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_RCR',0,6,237,3,3
	.word	14566
	.byte	12,6,240,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11505
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_RSR',0,6,245,3,3
	.word	14631
	.byte	12,6,248,3,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_SCR',0,6,253,3,3
	.word	14696
	.byte	12,6,128,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11814
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_SDS',0,6,133,4,3
	.word	14761
	.byte	12,6,136,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11956
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_VIEW',0,6,141,4,3
	.word	14826
	.byte	12,6,144,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12335
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CH_WDT',0,6,149,4,3
	.word	14892
	.byte	12,6,152,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12433
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_CLC',0,6,157,4,3
	.word	14957
	.byte	12,6,160,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12616
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_FDR',0,6,165,4,3
	.word	15019
	.byte	12,6,168,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12763
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_ID',0,6,173,4,3
	.word	15081
	.byte	12,6,176,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12872
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_INTOV',0,6,181,4,3
	.word	15142
	.byte	12,6,184,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13047
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_KRST0',0,6,189,4,3
	.word	15206
	.byte	12,6,192,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13160
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_KRST1',0,6,197,4,3
	.word	15270
	.byte	12,6,200,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_KRSTCLR',0,6,205,4,3
	.word	15334
	.byte	12,6,208,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13352
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_OCS',0,6,213,4,3
	.word	15400
	.byte	12,6,216,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13500
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_RDR',0,6,221,4,3
	.word	15462
	.byte	12,6,224,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13673
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_RTS',0,6,229,4,3
	.word	15524
	.byte	12,6,232,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13741
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_TPD',0,6,237,4,3
	.word	15586
	.byte	12,6,240,4,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13833
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SENT_TSR',0,6,245,4,3
	.word	15648
	.byte	10
	.byte	'_Ifx_SENT_CH',0,6,128,5,25,64,13
	.byte	'CPDR',0
	.word	14097
	.byte	4,2,35,0,13
	.byte	'CFDR',0
	.word	14031
	.byte	4,2,35,4,13
	.byte	'RCR',0
	.word	14566
	.byte	4,2,35,8,13
	.byte	'RSR',0
	.word	14631
	.byte	4,2,35,12,13
	.byte	'SDS',0
	.word	14761
	.byte	4,2,35,16,13
	.byte	'IOCR',0
	.word	14500
	.byte	4,2,35,20,13
	.byte	'SCR',0
	.word	14696
	.byte	4,2,35,24,13
	.byte	'VIEW',0
	.word	14826
	.byte	4,2,35,28,13
	.byte	'INTSTAT',0
	.word	14431
	.byte	4,2,35,32,13
	.byte	'INTSET',0
	.word	14363
	.byte	4,2,35,36,13
	.byte	'INTCLR',0
	.word	14228
	.byte	4,2,35,40,13
	.byte	'INTEN',0
	.word	14296
	.byte	4,2,35,44,13
	.byte	'INP',0
	.word	14163
	.byte	4,2,35,48,13
	.byte	'WDT',0
	.word	14892
	.byte	4,2,35,52,13
	.byte	'reserved_38',0
	.word	3357
	.byte	8,2,35,56,0,16
	.word	15710
	.byte	21
	.byte	'Ifx_SENT_CH',0,6,145,5,3
	.word	15949
	.byte	14,96
	.word	493
	.byte	15,95,0,14,24
	.word	15462
	.byte	15,5,0,14,80
	.word	493
	.byte	15,79,0,14,128,3
	.word	15710
	.byte	15,5,0,16
	.word	16002
	.byte	14,128,16
	.word	493
	.byte	15,255,15,0,14,24
	.word	15524
	.byte	15,5,0,14,104
	.word	493
	.byte	15,103,0,10
	.byte	'_Ifx_SENT',0,6,158,5,25,128,22,13
	.byte	'CLC',0
	.word	14957
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1538
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	15081
	.byte	4,2,35,8,13
	.byte	'FDR',0
	.word	15019
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	1538
	.byte	4,2,35,16,13
	.byte	'INTOV',0
	.word	15142
	.byte	4,2,35,20,13
	.byte	'TSR',0
	.word	15648
	.byte	4,2,35,24,13
	.byte	'TPD',0
	.word	15586
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	15975
	.byte	96,2,35,32,13
	.byte	'RDR',0
	.word	15984
	.byte	24,3,35,128,1,13
	.byte	'reserved_98',0
	.word	15993
	.byte	80,3,35,152,1,13
	.byte	'OCS',0
	.word	15400
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	15334
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	15270
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	15206
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	13966
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	13901
	.byte	4,3,35,252,1,13
	.byte	'CH',0
	.word	16012
	.byte	128,3,3,35,128,2,13
	.byte	'reserved_280',0
	.word	16017
	.byte	128,16,3,35,128,5,13
	.byte	'RTS',0
	.word	16028
	.byte	24,3,35,128,21,13
	.byte	'reserved_A98',0
	.word	16037
	.byte	104,3,35,152,21,0,16
	.word	16046
	.byte	21
	.byte	'Ifx_SENT',0,6,181,5,3
	.word	16414
	.byte	17,7,86,9,1,18
	.byte	'IfxSent_ChannelId_none',0,127,18
	.byte	'IfxSent_ChannelId_0',0,0,18
	.byte	'IfxSent_ChannelId_1',0,1,18
	.byte	'IfxSent_ChannelId_2',0,2,18
	.byte	'IfxSent_ChannelId_3',0,3,18
	.byte	'IfxSent_ChannelId_4',0,4,18
	.byte	'IfxSent_ChannelId_5',0,5,0,21
	.byte	'IfxSent_ChannelId',0,7,95,3
	.word	16437
	.byte	21
	.byte	'boolean',0,8,101,29
	.word	493
	.byte	21
	.byte	'uint8',0,8,105,29
	.word	493
	.byte	21
	.byte	'uint16',0,8,109,29
	.word	510
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,8,113,29
	.word	16671
	.byte	21
	.byte	'uint64',0,8,118,29
	.word	352
	.byte	21
	.byte	'sint16',0,8,126,29
	.word	8546
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,8,131,1,29
	.word	16737
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,8,138,1,29
	.word	16765
	.byte	21
	.byte	'float32',0,8,167,1,29
	.word	298
	.byte	21
	.byte	'pvoid',0,9,57,28
	.word	384
	.byte	21
	.byte	'Ifx_TickTime',0,9,79,28
	.word	16765
	.byte	17,9,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,21
	.byte	'Ifx_RxSel',0,9,140,1,3
	.word	16850
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7110
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7023
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3366
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1419
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2414
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1547
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2194
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1762
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1977
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6382
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6506
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6590
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6770
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5021
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5545
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5195
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5369
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6034
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	848
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4358
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4846
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4505
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4674
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5701
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	532
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4072
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3706
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2737
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3041
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7637
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7070
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3657
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1498
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2688
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1722
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2374
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1937
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2154
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6466
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6715
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6974
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6342
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5155
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5661
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5329
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5505
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1379
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5994
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4465
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4981
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4634
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4806
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	808
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4318
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4032
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3001
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3317
	.byte	16
	.word	7677
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	18306
	.byte	17,10,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,10,255,10,3
	.word	18326
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,11,45,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,11,79,3
	.word	18448
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,11,82,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,11,85,3
	.word	19005
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,11,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,11,94,3
	.word	19082
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,11,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,11,111,3
	.word	19218
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,11,114,16,4,11
	.byte	'CANDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	493
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,11,126,3
	.word	19498
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,11,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,11,135,1,3
	.word	19736
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,11,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,11,150,1,3
	.word	19864
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,11,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	493
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	493
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,11,165,1,3
	.word	20107
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,11,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,11,174,1,3
	.word	20342
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,11,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,11,181,1,3
	.word	20470
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,11,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,11,188,1,3
	.word	20570
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,11,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	493
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	493
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,11,202,1,3
	.word	20670
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,11,205,1,16,4,11
	.byte	'PWD',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	470
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,11,213,1,3
	.word	20878
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,11,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,11,225,1,3
	.word	21043
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,11,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,11,235,1,3
	.word	21226
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,11,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	493
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	470
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	493
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,11,129,2,3
	.word	21380
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,11,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,11,143,2,3
	.word	21744
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,11,146,2,16,4,11
	.byte	'POL',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	510
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	493
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,11,159,2,3
	.word	21955
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,11,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,11,167,2,3
	.word	22207
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,11,170,2,16,4,11
	.byte	'ARI',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,11,175,2,3
	.word	22325
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,11,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,11,185,2,3
	.word	22436
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,11,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	470
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,11,195,2,3
	.word	22599
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,11,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,11,205,2,3
	.word	22762
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,11,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,11,215,2,3
	.word	22920
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,11,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	493
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	493
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	510
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,11,232,2,3
	.word	23085
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,11,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	493
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	493
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	510
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,11,245,2,3
	.word	23414
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,11,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,11,255,2,3
	.word	23635
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,11,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,11,142,3,3
	.word	23798
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,11,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,11,152,3,3
	.word	24070
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,11,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,11,162,3,3
	.word	24223
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,11,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,11,172,3,3
	.word	24379
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,11,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,11,181,3,3
	.word	24541
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,11,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,11,191,3,3
	.word	24684
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,11,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,11,200,3,3
	.word	24849
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,11,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,11,211,3,3
	.word	24994
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,11,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	493
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,11,222,3,3
	.word	25175
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,11,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,11,232,3,3
	.word	25349
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,11,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,11,241,3,3
	.word	25509
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,11,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,11,130,4,3
	.word	25653
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,11,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,11,139,4,3
	.word	25927
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,11,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,11,149,4,3
	.word	26066
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,11,152,4,16,4,11
	.byte	'EN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	493
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	510
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	493
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	493
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,11,163,4,3
	.word	26229
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,11,166,4,16,4,11
	.byte	'STEP',0,2
	.word	510
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	493
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	510
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,11,174,4,3
	.word	26447
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,11,177,4,16,4,11
	.byte	'FS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,11,197,4,3
	.word	26610
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,11,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,11,205,4,3
	.word	26946
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,11,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	493
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,11,232,4,3
	.word	27053
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,11,235,4,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,11,240,4,3
	.word	27505
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,11,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	493
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,11,250,4,3
	.word	27604
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,11,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,11,131,5,3
	.word	27754
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,11,134,5,16,4,11
	.byte	'SEED',0,4
	.word	470
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,11,141,5,3
	.word	27903
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,11,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	470
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,11,149,5,3
	.word	28064
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,11,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	510
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,11,158,5,3
	.word	28194
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,11,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,11,166,5,3
	.word	28326
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,11,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	493
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	510
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,11,174,5,3
	.word	28441
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,11,177,5,16,4,11
	.byte	'PS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,11,185,5,3
	.word	28552
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,11,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	493
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	493
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,11,209,5,3
	.word	28710
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,11,212,5,16,4,11
	.byte	'P0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,11,217,5,3
	.word	29122
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,11,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	510
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,11,233,5,3
	.word	29223
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,11,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	470
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,11,242,5,3
	.word	29490
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,11,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,11,250,5,3
	.word	29626
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,11,253,5,16,4,11
	.byte	'PD0',0,1
	.word	493
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	493
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,11,132,6,3
	.word	29737
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,11,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,11,146,6,3
	.word	29870
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,11,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,11,166,6,3
	.word	30073
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,11,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,11,177,6,3
	.word	30429
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,11,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	510
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,11,184,6,3
	.word	30607
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,11,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	510
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	493
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	493
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	493
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,11,204,6,3
	.word	30707
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,11,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	493
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	493
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	493
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,11,215,6,3
	.word	31077
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,11,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	470
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,11,227,6,3
	.word	31263
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,11,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	470
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,11,241,6,3
	.word	31461
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,11,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	493
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	470
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,11,251,6,3
	.word	31694
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,11,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	493
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	493
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	493
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	493
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,11,153,7,3
	.word	31846
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,11,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	493
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	493
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	493
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	493
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,11,170,7,3
	.word	32413
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,11,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	493
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	493
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	493
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	493
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	493
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	493
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	493
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,11,187,7,3
	.word	32707
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,11,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	493
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	493
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	493
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	493
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	493
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	510
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	493
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	493
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,11,214,7,3
	.word	32985
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,11,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	510
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,11,230,7,3
	.word	33481
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,11,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	510
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	493
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	493
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	493
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,11,243,7,3
	.word	33794
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,11,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	493
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	493
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	493
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,11,129,8,3
	.word	34003
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,11,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	493
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	493
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	493
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	493
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	493
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	493
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,11,155,8,3
	.word	34214
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,11,158,8,16,4,11
	.byte	'HBT',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	470
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,11,162,8,3
	.word	34646
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,11,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	493
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	493
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	493
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	493
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	493
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	493
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	493
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	493
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	493
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	493
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	493
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,11,178,8,3
	.word	34742
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,11,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	470
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,11,186,8,3
	.word	35002
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,11,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	493
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	493
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	470
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,11,198,8,3
	.word	35127
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,11,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,11,208,8,3
	.word	35324
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,11,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,11,218,8,3
	.word	35477
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,11,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,11,228,8,3
	.word	35630
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,11,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	470
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,11,238,8,3
	.word	35783
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,11,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	35938
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	35938
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	35938
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	35938
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,11,247,8,3
	.word	35954
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,11,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	493
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,11,134,9,3
	.word	36084
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,11,137,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,11,150,9,3
	.word	36322
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,11,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	35938
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	35938
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	35938
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	35938
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,11,159,9,3
	.word	36545
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,11,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,11,175,9,3
	.word	36671
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,11,178,9,16,4,11
	.byte	'AE',0,1
	.word	493
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	493
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	493
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	493
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	493
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	493
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	493
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	493
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	493
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	493
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	510
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,11,191,9,3
	.word	36923
	.byte	12,11,199,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18448
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,11,204,9,3
	.word	37142
	.byte	12,11,207,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19005
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,11,212,9,3
	.word	37206
	.byte	12,11,215,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19082
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,11,220,9,3
	.word	37270
	.byte	12,11,223,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19218
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,11,228,9,3
	.word	37335
	.byte	12,11,231,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19498
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,11,236,9,3
	.word	37400
	.byte	12,11,239,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19736
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,11,244,9,3
	.word	37465
	.byte	12,11,247,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19864
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,11,252,9,3
	.word	37530
	.byte	12,11,255,9,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20107
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,11,132,10,3
	.word	37595
	.byte	12,11,135,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20342
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,11,140,10,3
	.word	37660
	.byte	12,11,143,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20470
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,11,148,10,3
	.word	37725
	.byte	12,11,151,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20570
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,11,156,10,3
	.word	37790
	.byte	12,11,159,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20670
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,11,164,10,3
	.word	37855
	.byte	12,11,167,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20878
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,11,172,10,3
	.word	37919
	.byte	12,11,175,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21043
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,11,180,10,3
	.word	37983
	.byte	12,11,183,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21226
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,11,188,10,3
	.word	38047
	.byte	12,11,191,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21380
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,11,196,10,3
	.word	38112
	.byte	12,11,199,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21744
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,11,204,10,3
	.word	38174
	.byte	12,11,207,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21955
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,11,212,10,3
	.word	38236
	.byte	12,11,215,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22207
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,11,220,10,3
	.word	38298
	.byte	12,11,223,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22325
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,11,228,10,3
	.word	38362
	.byte	12,11,231,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22436
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,11,236,10,3
	.word	38427
	.byte	12,11,239,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22599
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,11,244,10,3
	.word	38493
	.byte	12,11,247,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22762
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,11,252,10,3
	.word	38559
	.byte	12,11,255,10,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22920
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,11,132,11,3
	.word	38627
	.byte	12,11,135,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23085
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,11,140,11,3
	.word	38694
	.byte	12,11,143,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23414
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,11,148,11,3
	.word	38762
	.byte	12,11,151,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23635
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,11,156,11,3
	.word	38830
	.byte	12,11,159,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23798
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,11,164,11,3
	.word	38896
	.byte	12,11,167,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24070
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,11,172,11,3
	.word	38963
	.byte	12,11,175,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24223
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,11,180,11,3
	.word	39032
	.byte	12,11,183,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24379
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,11,188,11,3
	.word	39101
	.byte	12,11,191,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24541
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,11,196,11,3
	.word	39170
	.byte	12,11,199,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24684
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,11,204,11,3
	.word	39239
	.byte	12,11,207,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24849
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,11,212,11,3
	.word	39308
	.byte	12,11,215,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24994
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,11,220,11,3
	.word	39377
	.byte	12,11,223,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25175
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,11,228,11,3
	.word	39445
	.byte	12,11,231,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25349
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,11,236,11,3
	.word	39513
	.byte	12,11,239,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25509
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,11,244,11,3
	.word	39581
	.byte	12,11,247,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25653
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,11,252,11,3
	.word	39649
	.byte	12,11,255,11,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25927
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,11,132,12,3
	.word	39714
	.byte	12,11,135,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26066
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,11,140,12,3
	.word	39779
	.byte	12,11,143,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26229
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,11,148,12,3
	.word	39845
	.byte	12,11,151,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26447
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,11,156,12,3
	.word	39909
	.byte	12,11,159,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26610
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,11,164,12,3
	.word	39970
	.byte	12,11,167,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26946
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,11,172,12,3
	.word	40031
	.byte	12,11,175,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27053
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,11,180,12,3
	.word	40091
	.byte	12,11,183,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27505
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,11,188,12,3
	.word	40153
	.byte	12,11,191,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27604
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,11,196,12,3
	.word	40213
	.byte	12,11,199,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27754
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,11,204,12,3
	.word	40275
	.byte	12,11,207,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27903
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,11,212,12,3
	.word	40343
	.byte	12,11,215,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28064
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,11,220,12,3
	.word	40411
	.byte	12,11,223,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28194
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,11,228,12,3
	.word	40479
	.byte	12,11,231,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28326
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,11,236,12,3
	.word	40543
	.byte	12,11,239,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28441
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,11,244,12,3
	.word	40608
	.byte	12,11,247,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28552
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,11,252,12,3
	.word	40671
	.byte	12,11,255,12,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28710
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,11,132,13,3
	.word	40732
	.byte	12,11,135,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29122
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,11,140,13,3
	.word	40796
	.byte	12,11,143,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29223
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,11,148,13,3
	.word	40857
	.byte	12,11,151,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29490
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,11,156,13,3
	.word	40921
	.byte	12,11,159,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29626
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,11,164,13,3
	.word	40988
	.byte	12,11,167,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29737
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,11,172,13,3
	.word	41051
	.byte	12,11,175,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29870
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,11,180,13,3
	.word	41112
	.byte	12,11,183,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30073
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,11,188,13,3
	.word	41174
	.byte	12,11,191,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30429
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,11,196,13,3
	.word	41239
	.byte	12,11,199,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30607
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,11,204,13,3
	.word	41304
	.byte	12,11,207,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30707
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,11,212,13,3
	.word	41369
	.byte	12,11,215,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31077
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,11,220,13,3
	.word	41438
	.byte	12,11,223,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31263
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,11,228,13,3
	.word	41507
	.byte	12,11,231,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31461
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,11,236,13,3
	.word	41576
	.byte	12,11,239,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31694
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,11,244,13,3
	.word	41641
	.byte	12,11,247,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31846
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,11,252,13,3
	.word	41704
	.byte	12,11,255,13,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32413
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,11,132,14,3
	.word	41769
	.byte	12,11,135,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32707
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,11,140,14,3
	.word	41834
	.byte	12,11,143,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32985
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,11,148,14,3
	.word	41899
	.byte	12,11,151,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33481
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,11,156,14,3
	.word	41965
	.byte	12,11,159,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34003
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,11,164,14,3
	.word	42034
	.byte	12,11,167,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33794
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,11,172,14,3
	.word	42098
	.byte	12,11,175,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34214
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,11,180,14,3
	.word	42163
	.byte	12,11,183,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34646
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,11,188,14,3
	.word	42228
	.byte	12,11,191,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34742
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,11,196,14,3
	.word	42293
	.byte	12,11,199,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35002
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,11,204,14,3
	.word	42357
	.byte	12,11,207,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35127
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,11,212,14,3
	.word	42423
	.byte	12,11,215,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35324
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,11,220,14,3
	.word	42487
	.byte	12,11,223,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35477
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,11,228,14,3
	.word	42552
	.byte	12,11,231,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35630
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,11,236,14,3
	.word	42617
	.byte	12,11,239,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35783
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,11,244,14,3
	.word	42682
	.byte	12,11,247,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35954
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,11,252,14,3
	.word	42748
	.byte	12,11,255,14,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36084
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,11,132,15,3
	.word	42817
	.byte	12,11,135,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,11,140,15,3
	.word	42886
	.byte	12,11,143,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36545
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,11,148,15,3
	.word	42953
	.byte	12,11,151,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36671
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,11,156,15,3
	.word	43020
	.byte	12,11,159,15,9,4,13
	.byte	'U',0
	.word	470
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	486
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,11,164,15,3
	.word	43087
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,11,175,15,25,12,13
	.byte	'CON0',0
	.word	42748
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	42817
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	42886
	.byte	4,2,35,8,0,16
	.word	43152
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,11,180,15,3
	.word	43215
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,11,183,15,25,12,13
	.byte	'CON0',0
	.word	42953
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43020
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43087
	.byte	4,2,35,8,0,16
	.word	43244
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,11,188,15,3
	.word	43305
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	43332
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	43483
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	43727
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	43825
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8290
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8285
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	493
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	44290
	.byte	16
	.word	16046
	.byte	3
	.word	44350
	.byte	23,12,59,15,20,13
	.byte	'module',0
	.word	44355
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	16437
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	44290
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	16850
	.byte	1,2,35,16,0,24
	.word	44360
	.byte	21
	.byte	'IfxSent_Sent_In',0,12,65,3
	.word	44430
	.byte	23,12,68,15,20,13
	.byte	'module',0
	.word	44355
	.byte	4,2,35,0,13
	.byte	'channelId',0
	.word	16437
	.byte	1,2,35,4,13
	.byte	'pin',0
	.word	44290
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	43483
	.byte	1,2,35,16,0,24
	.word	44459
	.byte	21
	.byte	'IfxSent_Spc_Out',0,12,74,3
	.word	44529
.L78:
	.byte	24
	.word	44360
.L79:
	.byte	24
	.word	44360
.L80:
	.byte	24
	.word	44360
.L81:
	.byte	24
	.word	44360
.L82:
	.byte	24
	.word	44360
.L83:
	.byte	24
	.word	44360
.L84:
	.byte	24
	.word	44360
.L85:
	.byte	24
	.word	44360
.L86:
	.byte	24
	.word	44360
.L87:
	.byte	24
	.word	44360
.L88:
	.byte	24
	.word	44360
.L89:
	.byte	24
	.word	44360
.L90:
	.byte	24
	.word	44360
.L91:
	.byte	24
	.word	44360
.L92:
	.byte	24
	.word	44360
.L93:
	.byte	24
	.word	44360
.L94:
	.byte	24
	.word	44360
.L95:
	.byte	24
	.word	44360
.L96:
	.byte	24
	.word	44360
.L97:
	.byte	24
	.word	44360
.L98:
	.byte	24
	.word	44360
.L99:
	.byte	24
	.word	44360
.L100:
	.byte	24
	.word	44360
.L101:
	.byte	24
	.word	44360
.L102:
	.byte	24
	.word	44360
.L103:
	.byte	24
	.word	44360
.L104:
	.byte	24
	.word	44360
.L105:
	.byte	24
	.word	44360
.L106:
	.byte	24
	.word	44459
.L107:
	.byte	24
	.word	44459
.L108:
	.byte	24
	.word	44459
.L109:
	.byte	24
	.word	44459
.L110:
	.byte	24
	.word	44459
.L111:
	.byte	24
	.word	44459
	.byte	24
	.word	44360
	.byte	3
	.word	44728
	.byte	14,16
	.word	44733
	.byte	15,3,0,14,96
	.word	44738
	.byte	15,5,0
.L112:
	.byte	14,96
	.word	44747
	.byte	15,0,0,24
	.word	44459
	.byte	3
	.word	44765
	.byte	14,4
	.word	44770
	.byte	15,0,0,14,24
	.word	44775
	.byte	15,5,0
.L113:
	.byte	14,24
	.word	44784
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L115-.L114
.L114:
	.half	3
	.word	.L117-.L116
.L116:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0,0,0,0
	.byte	'IfxSent_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSent_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxSent_PinMap.h',0,0,0,0,0
.L117:
.L115:
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT0A_AN24_IN')
	.sect	'.debug_info'
.L6:
	.word	274
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT0A_AN24_IN',0,5,48,17
	.word	.L78
	.byte	1,5,3
	.word	IfxSent_SENT0A_AN24_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT0A_AN24_IN')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT0A_P40_0_IN')
	.sect	'.debug_info'
.L8:
	.word	275
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT0A_P40_0_IN',0,5,49,17
	.word	.L79
	.byte	1,5,3
	.word	IfxSent_SENT0A_P40_0_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT0A_P40_0_IN')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT0B_P00_1_IN')
	.sect	'.debug_info'
.L10:
	.word	275
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT0B_P00_1_IN',0,5,50,17
	.word	.L80
	.byte	1,5,3
	.word	IfxSent_SENT0B_P00_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT0B_P00_1_IN')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT0C_P02_8_IN')
	.sect	'.debug_info'
.L12:
	.word	275
	.half	3
	.word	.L13
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT0C_P02_8_IN',0,5,51,17
	.word	.L81
	.byte	1,5,3
	.word	IfxSent_SENT0C_P02_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT0C_P02_8_IN')
	.sect	'.debug_abbrev'
.L13:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT1A_AN25_IN')
	.sect	'.debug_info'
.L14:
	.word	274
	.half	3
	.word	.L15
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT1A_AN25_IN',0,5,52,17
	.word	.L82
	.byte	1,5,3
	.word	IfxSent_SENT1A_AN25_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT1A_AN25_IN')
	.sect	'.debug_abbrev'
.L15:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT1A_P40_1_IN')
	.sect	'.debug_info'
.L16:
	.word	275
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT1A_P40_1_IN',0,5,53,17
	.word	.L83
	.byte	1,5,3
	.word	IfxSent_SENT1A_P40_1_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT1A_P40_1_IN')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT1B_P00_2_IN')
	.sect	'.debug_info'
.L18:
	.word	275
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT1B_P00_2_IN',0,5,54,17
	.word	.L84
	.byte	1,5,3
	.word	IfxSent_SENT1B_P00_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT1B_P00_2_IN')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT1C_P02_7_IN')
	.sect	'.debug_info'
.L20:
	.word	275
	.half	3
	.word	.L21
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT1C_P02_7_IN',0,5,55,17
	.word	.L85
	.byte	1,5,3
	.word	IfxSent_SENT1C_P02_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT1C_P02_7_IN')
	.sect	'.debug_abbrev'
.L21:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2A_AN26_IN')
	.sect	'.debug_info'
.L22:
	.word	274
	.half	3
	.word	.L23
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2A_AN26_IN',0,5,56,17
	.word	.L86
	.byte	1,5,3
	.word	IfxSent_SENT2A_AN26_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2A_AN26_IN')
	.sect	'.debug_abbrev'
.L23:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2A_P40_2_IN')
	.sect	'.debug_info'
.L24:
	.word	275
	.half	3
	.word	.L25
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2A_P40_2_IN',0,5,57,17
	.word	.L87
	.byte	1,5,3
	.word	IfxSent_SENT2A_P40_2_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2A_P40_2_IN')
	.sect	'.debug_abbrev'
.L25:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2B_P00_3_IN')
	.sect	'.debug_info'
.L26:
	.word	275
	.half	3
	.word	.L27
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2B_P00_3_IN',0,5,58,17
	.word	.L88
	.byte	1,5,3
	.word	IfxSent_SENT2B_P00_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2B_P00_3_IN')
	.sect	'.debug_abbrev'
.L27:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2C_P02_6_IN')
	.sect	'.debug_info'
.L28:
	.word	275
	.half	3
	.word	.L29
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2C_P02_6_IN',0,5,59,17
	.word	.L89
	.byte	1,5,3
	.word	IfxSent_SENT2C_P02_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2C_P02_6_IN')
	.sect	'.debug_abbrev'
.L29:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2D_AN36_IN')
	.sect	'.debug_info'
.L30:
	.word	274
	.half	3
	.word	.L31
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2D_AN36_IN',0,5,60,17
	.word	.L90
	.byte	1,5,3
	.word	IfxSent_SENT2D_AN36_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2D_AN36_IN')
	.sect	'.debug_abbrev'
.L31:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT2D_P40_6_IN')
	.sect	'.debug_info'
.L32:
	.word	275
	.half	3
	.word	.L33
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT2D_P40_6_IN',0,5,61,17
	.word	.L91
	.byte	1,5,3
	.word	IfxSent_SENT2D_P40_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT2D_P40_6_IN')
	.sect	'.debug_abbrev'
.L33:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3A_AN27_IN')
	.sect	'.debug_info'
.L34:
	.word	274
	.half	3
	.word	.L35
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3A_AN27_IN',0,5,62,17
	.word	.L92
	.byte	1,5,3
	.word	IfxSent_SENT3A_AN27_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3A_AN27_IN')
	.sect	'.debug_abbrev'
.L35:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3A_P40_3_IN')
	.sect	'.debug_info'
.L36:
	.word	275
	.half	3
	.word	.L37
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3A_P40_3_IN',0,5,63,17
	.word	.L93
	.byte	1,5,3
	.word	IfxSent_SENT3A_P40_3_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3A_P40_3_IN')
	.sect	'.debug_abbrev'
.L37:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3B_P00_4_IN')
	.sect	'.debug_info'
.L38:
	.word	275
	.half	3
	.word	.L39
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3B_P00_4_IN',0,5,64,17
	.word	.L94
	.byte	1,5,3
	.word	IfxSent_SENT3B_P00_4_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3B_P00_4_IN')
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3C_P02_5_IN')
	.sect	'.debug_info'
.L40:
	.word	275
	.half	3
	.word	.L41
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3C_P02_5_IN',0,5,65,17
	.word	.L95
	.byte	1,5,3
	.word	IfxSent_SENT3C_P02_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3C_P02_5_IN')
	.sect	'.debug_abbrev'
.L41:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3D_AN37_IN')
	.sect	'.debug_info'
.L42:
	.word	274
	.half	3
	.word	.L43
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3D_AN37_IN',0,5,66,17
	.word	.L96
	.byte	1,5,3
	.word	IfxSent_SENT3D_AN37_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3D_AN37_IN')
	.sect	'.debug_abbrev'
.L43:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT3D_P40_7_IN')
	.sect	'.debug_info'
.L44:
	.word	275
	.half	3
	.word	.L45
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT3D_P40_7_IN',0,5,67,17
	.word	.L97
	.byte	1,5,3
	.word	IfxSent_SENT3D_P40_7_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT3D_P40_7_IN')
	.sect	'.debug_abbrev'
.L45:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT4A_AN38_IN')
	.sect	'.debug_info'
.L46:
	.word	274
	.half	3
	.word	.L47
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT4A_AN38_IN',0,5,68,17
	.word	.L98
	.byte	1,5,3
	.word	IfxSent_SENT4A_AN38_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT4A_AN38_IN')
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT4A_P40_8_IN')
	.sect	'.debug_info'
.L48:
	.word	275
	.half	3
	.word	.L49
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT4A_P40_8_IN',0,5,69,17
	.word	.L99
	.byte	1,5,3
	.word	IfxSent_SENT4A_P40_8_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT4A_P40_8_IN')
	.sect	'.debug_abbrev'
.L49:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT4B_P00_5_IN')
	.sect	'.debug_info'
.L50:
	.word	275
	.half	3
	.word	.L51
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT4B_P00_5_IN',0,5,70,17
	.word	.L100
	.byte	1,5,3
	.word	IfxSent_SENT4B_P00_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT4B_P00_5_IN')
	.sect	'.debug_abbrev'
.L51:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT4C_P33_6_IN')
	.sect	'.debug_info'
.L52:
	.word	275
	.half	3
	.word	.L53
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT4C_P33_6_IN',0,5,71,17
	.word	.L101
	.byte	1,5,3
	.word	IfxSent_SENT4C_P33_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT4C_P33_6_IN')
	.sect	'.debug_abbrev'
.L53:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT5A_AN39_IN')
	.sect	'.debug_info'
.L54:
	.word	274
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT5A_AN39_IN',0,5,72,17
	.word	.L102
	.byte	1,5,3
	.word	IfxSent_SENT5A_AN39_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT5A_AN39_IN')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT5A_P40_9_IN')
	.sect	'.debug_info'
.L56:
	.word	275
	.half	3
	.word	.L57
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT5A_P40_9_IN',0,5,73,17
	.word	.L103
	.byte	1,5,3
	.word	IfxSent_SENT5A_P40_9_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT5A_P40_9_IN')
	.sect	'.debug_abbrev'
.L57:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT5B_P00_6_IN')
	.sect	'.debug_info'
.L58:
	.word	275
	.half	3
	.word	.L59
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT5B_P00_6_IN',0,5,74,17
	.word	.L104
	.byte	1,5,3
	.word	IfxSent_SENT5B_P00_6_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT5B_P00_6_IN')
	.sect	'.debug_abbrev'
.L59:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SENT5C_P33_5_IN')
	.sect	'.debug_info'
.L60:
	.word	275
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SENT5C_P33_5_IN',0,5,75,17
	.word	.L105
	.byte	1,5,3
	.word	IfxSent_SENT5C_P33_5_IN
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SENT5C_P33_5_IN')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC0_P00_1_OUT')
	.sect	'.debug_info'
.L62:
	.word	274
	.half	3
	.word	.L63
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC0_P00_1_OUT',0,5,76,17
	.word	.L106
	.byte	1,5,3
	.word	IfxSent_SPC0_P00_1_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC0_P00_1_OUT')
	.sect	'.debug_abbrev'
.L63:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC1_P02_7_OUT')
	.sect	'.debug_info'
.L64:
	.word	274
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC1_P02_7_OUT',0,5,77,17
	.word	.L107
	.byte	1,5,3
	.word	IfxSent_SPC1_P02_7_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC1_P02_7_OUT')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC2_P00_3_OUT')
	.sect	'.debug_info'
.L66:
	.word	274
	.half	3
	.word	.L67
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC2_P00_3_OUT',0,5,78,17
	.word	.L108
	.byte	1,5,3
	.word	IfxSent_SPC2_P00_3_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC2_P00_3_OUT')
	.sect	'.debug_abbrev'
.L67:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC3_P00_4_OUT')
	.sect	'.debug_info'
.L68:
	.word	274
	.half	3
	.word	.L69
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC3_P00_4_OUT',0,5,79,17
	.word	.L109
	.byte	1,5,3
	.word	IfxSent_SPC3_P00_4_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC3_P00_4_OUT')
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC4_P00_5_OUT')
	.sect	'.debug_info'
.L70:
	.word	274
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC4_P00_5_OUT',0,5,80,17
	.word	.L110
	.byte	1,5,3
	.word	IfxSent_SPC4_P00_5_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC4_P00_5_OUT')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_SPC5_P00_6_OUT')
	.sect	'.debug_info'
.L72:
	.word	274
	.half	3
	.word	.L73
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_SPC5_P00_6_OUT',0,5,81,17
	.word	.L111
	.byte	1,5,3
	.word	IfxSent_SPC5_P00_6_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_SPC5_P00_6_OUT')
	.sect	'.debug_abbrev'
.L73:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_Sent_In_pinTable')
	.sect	'.debug_info'
.L74:
	.word	276
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_Sent_In_pinTable',0,5,84,24
	.word	.L112
	.byte	1,5,3
	.word	IfxSent_Sent_In_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_Sent_In_pinTable')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSent_Spc_Out_pinTable')
	.sect	'.debug_info'
.L76:
	.word	276
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSent_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSent_Spc_Out_pinTable',0,5,125,24
	.word	.L113
	.byte	1,5,3
	.word	IfxSent_Spc_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSent_Spc_Out_pinTable')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
