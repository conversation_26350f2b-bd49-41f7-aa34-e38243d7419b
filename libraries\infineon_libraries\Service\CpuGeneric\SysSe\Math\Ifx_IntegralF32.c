/**
 * \file Ifx_IntegralF32.c
 * \brief Discrete integral approximation
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 */
#include "Ifx_IntegralF32.h"

void Ifx_IntegralF32_reset(Ifx_IntegralF32 *ci)
{
    ci->uk = 0;
    ci->ik = 0;
}


void Ifx_IntegralF32_init(Ifx_IntegralF32 *ci, float32 gain, float32 Ts)
{
    ci->delta = gain * Ts / 2;
}


float32 Ifx_IntegralF32_step(Ifx_IntegralF32 *ci, float32 ik)
{
    ci->uk = ci->uk + (ik + ci->ik) * ci->delta;
    ci->ik = ik;

    return ci->uk;
}


void Ifx_ClpxFloat32_Integral_reset(Ifx_ClpxFloat32_Integral *ci)
{
    ci->uk.real = 0;
    ci->uk.imag = 0;
    ci->ik.real = 0;
    ci->ik.imag = 0;
}


void Ifx_ClpxFloat32_Integral_init(Ifx_ClpxFloat32_Integral *ci, float32 gain, float32 Ts)
{
    Ifx_ClpxFloat32_Integral_reset(ci);
    ci->delta = gain * Ts / 2;
}


cfloat32 Ifx_ClpxFloat32_Integral_step(Ifx_ClpxFloat32_Integral *ci, cfloat32 ik)
{
    ci->uk.real = ci->uk.real + (ik.real + ci->ik.real) * ci->delta;
    ci->uk.imag = ci->uk.imag + (ik.imag + ci->ik.imag) * ci->delta;
    ci->ik      = ik;

    return ci->uk;
}
