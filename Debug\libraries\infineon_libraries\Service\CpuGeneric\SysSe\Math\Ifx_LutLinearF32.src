	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc44932a --dep-file=Ifx_LutLinearF32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_LutLinearF32.Ifx_LutLinearF32_searchBin',code,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.text.Ifx_LutLinearF32.Ifx_LutLinearF32_searchBin'
	.align	2
	
	.global	Ifx_LutLinearF32_searchBin
; Function Ifx_LutLinearF32_searchBin
.L14:
Ifx_LutLinearF32_searchBin:	.type	func
	mov	d2,#0
.L33:
	ld.b	d0,[a4]0
.L48:
	add	d3,d0,#-1
.L35:
	ld.a	a15,[a4]4
.L49:
	ld.w	d0,[a15]20
.L50:
	ld.a	a15,[a4]4
.L51:
	ld.w	d15,[a15]8
.L52:
	cmp.f	d0,d0,d15
	extr.u	d15,d0,#2,#1
.L53:
	jeq	d15,#0,.L2
.L54:
	j	.L3
.L4:
	sub	d0,d3,d2
.L55:
	mov	d1,#2
.L56:
	div	e0,d0,d1
.L57:
	add	d0,d2
	extr	d0,d0,#0,#16
.L36:
	mul	d15,d0,#12
.L58:
	ld.a	a15,[a4]4
.L59:
	addsc.a	a15,a15,d15,#0
.L60:
	ld.w	d15,[a15]8
.L61:
	cmp.f	d15,d4,d15
	jnz.t	d15:2,.L5
.L62:
	mov	d3,d0
.L63:
	j	.L6
.L5:
	add	d0,#1
.L37:
	extr	d2,d0,#0,#16
.L6:
.L3:
	jlt	d2,d3,.L4
.L64:
	j	.L7
.L2:
	j	.L8
.L9:
	sub	d15,d3,d2
.L65:
	mov	d0,#2
.L66:
	div	e0,d15,d0
.L67:
	add	d15,d2,d0
	extr	d0,d15,#0,#16
.L38:
	mul	d15,d0,#12
.L68:
	ld.a	a15,[a4]4
.L69:
	addsc.a	a15,a15,d15,#0
.L70:
	ld.w	d15,[a15]8
.L71:
	cmp.f	d15,d4,d15
	jnz.t	d15:0,.L10
.L72:
	mov	d3,d0
.L73:
	j	.L11
.L10:
	add	d0,#1
.L39:
	extr	d2,d0,#0,#16
.L11:
.L8:
	jlt	d2,d3,.L9
.L7:
	mul	d15,d2,#12
.L74:
	ld.a	a15,[a4]4
.L75:
	addsc.a	a15,a15,d15,#0
.L76:
	ld.w	d0,[a15]
.L77:
	mul	d15,d2,#12
.L78:
	ld.a	a15,[a4]4
.L79:
	addsc.a	a15,a15,d15,#0
.L80:
	ld.w	d15,[a15]4
.L81:
	madd.f	d2,d15,d0,d4
.L34:
	j	.L12
.L12:
	ret
.L25:
	
__Ifx_LutLinearF32_searchBin_function_end:
	.size	Ifx_LutLinearF32_searchBin,__Ifx_LutLinearF32_searchBin_function_end-Ifx_LutLinearF32_searchBin
.L23:
	; End of function
	
	.calls	'Ifx_LutLinearF32_searchBin','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L16:
	.word	805
	.half	3
	.word	.L17
	.byte	4
.L15:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L18
.L24:
	.byte	2
	.byte	'float',0,4,4,2
	.byte	'char',0,1,6,3,1,57,9,12,4
	.byte	'gain',0
	.word	243
	.byte	4,2,35,0,4
	.byte	'offset',0
	.word	243
	.byte	4,2,35,4,4
	.byte	'boundary',0
	.word	243
	.byte	4,2,35,8,0,5
	.word	260
	.byte	6
	.word	314
	.byte	3,1,64,9,8,4
	.byte	'segmentCount',0
	.word	252
	.byte	1,2,35,0,4
	.byte	'segments',0
	.word	319
	.byte	4,2,35,4,0,5
	.word	324
.L26:
	.byte	6
	.word	370
.L29:
	.byte	2
	.byte	'short int',0,2,5,7
	.byte	'__wchar_t',0,2,1,1
	.word	380
	.byte	2
	.byte	'unsigned int',0,4,7,7
	.byte	'__size_t',0,2,1,1
	.word	411
	.byte	2
	.byte	'int',0,4,5,7
	.byte	'__ptrdiff_t',0,2,1,1
	.word	444
	.byte	8,1,6
	.word	471
	.byte	7
	.byte	'__codeptr',0,2,1,1
	.word	473
	.byte	2
	.byte	'unsigned char',0,1,8,7
	.byte	'uint8',0,3,105,29
	.word	496
	.byte	2
	.byte	'unsigned short int',0,2,7,7
	.byte	'uint16',0,3,109,29
	.word	527
	.byte	2
	.byte	'unsigned long int',0,4,7,7
	.byte	'uint32',0,3,113,29
	.word	564
	.byte	7
	.byte	'sint8',0,3,122,29
	.word	252
	.byte	7
	.byte	'sint16',0,3,126,29
	.word	380
	.byte	2
	.byte	'long int',0,4,5,7
	.byte	'sint32',0,3,131,1,29
	.word	629
	.byte	2
	.byte	'long long int',0,8,5,7
	.byte	'sint64',0,3,138,1,29
	.word	657
	.byte	7
	.byte	'float32',0,3,167,1,29
	.word	243
	.byte	9
	.byte	'void',0,6
	.word	707
	.byte	7
	.byte	'pvoid',0,4,57,28
	.word	713
	.byte	7
	.byte	'Ifx_TickTime',0,4,79,28
	.word	657
	.byte	7
	.byte	'Ifx_LutLinearF32_Item',0,1,62,3
	.word	260
	.byte	7
	.byte	'Ifx_LutLinearF32',0,1,68,3
	.word	324
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,73,19,11,15,56,9,0,0,5,38,0,73,19,0,0,6,15,0,73,19,0,0,7,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,8,21,0,54,15,0,0,9,59,0,3,8,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L18:
	.word	.L41-.L40
.L40:
	.half	3
	.word	.L43-.L42
.L42:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\SysSe\\Math\\Ifx_LutLinearF32.h',0,0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0,0
.L43:
.L41:
	.sdecl	'.debug_info',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_info'
.L19:
	.word	394
	.half	3
	.word	.L20
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L22,.L21
	.byte	2
	.word	.L15
	.byte	3
	.byte	'Ifx_LutLinearF32_searchBin',0,1,56,9
	.word	.L24
	.byte	1,1,1
	.word	.L14,.L25,.L13
	.byte	4
	.byte	'ml',0,1,56,60
	.word	.L26,.L27
	.byte	4
	.byte	'index',0,1,56,72
	.word	.L24,.L28
	.byte	5
	.word	.L14,.L25
	.byte	6
	.byte	'imin',0,1,58,12
	.word	.L29,.L30
	.byte	6
	.byte	'imax',0,1,59,12
	.word	.L29,.L31
	.byte	6
	.byte	'imid',0,1,60,12
	.word	.L29,.L32
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_abbrev'
.L20:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_line'
.L21:
	.word	.L45-.L44
.L44:
	.half	3
	.word	.L47-.L46
.L46:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LutLinearF32.c',0,0,0,0,0
.L47:
	.byte	5,10,7,0,5,2
	.word	.L14
	.byte	3,61,1,5,14,9
	.half	.L33-.L14
	.byte	3,1,1,5,29,9
	.half	.L48-.L33
	.byte	1,5,11,9
	.half	.L35-.L48
	.byte	3,2,1,5,24,9
	.half	.L49-.L35
	.byte	1,5,38,9
	.half	.L50-.L49
	.byte	1,5,51,9
	.half	.L51-.L50
	.byte	1,5,34,9
	.half	.L52-.L51
	.byte	1,5,5,9
	.half	.L53-.L52
	.byte	1,5,27,7,9
	.half	.L54-.L53
	.byte	3,2,1,5,33,9
	.half	.L4-.L54
	.byte	3,2,1,5,43,9
	.half	.L55-.L4
	.byte	1,5,41,9
	.half	.L56-.L55
	.byte	1,5,25,9
	.half	.L57-.L56
	.byte	1,5,37,9
	.half	.L36-.L57
	.byte	3,2,1,5,27,9
	.half	.L58-.L36
	.byte	1,5,37,9
	.half	.L59-.L58
	.byte	1,5,43,9
	.half	.L60-.L59
	.byte	1,5,13,9
	.half	.L61-.L60
	.byte	1,5,22,9
	.half	.L62-.L61
	.byte	3,6,1,5,32,9
	.half	.L63-.L62
	.byte	3,124,1,5,29,9
	.half	.L5-.L63
	.byte	1,5,27,9
	.half	.L3-.L5
	.byte	3,122,1,7,9
	.half	.L64-.L3
	.byte	1,9
	.half	.L2-.L64
	.byte	3,16,1,5,33,9
	.half	.L9-.L2
	.byte	3,2,1,5,43,9
	.half	.L65-.L9
	.byte	1,5,41,9
	.half	.L66-.L65
	.byte	1,5,25,9
	.half	.L67-.L66
	.byte	1,5,37,9
	.half	.L38-.L67
	.byte	3,2,1,5,27,9
	.half	.L68-.L38
	.byte	1,5,37,9
	.half	.L69-.L68
	.byte	1,5,43,9
	.half	.L70-.L69
	.byte	1,5,13,9
	.half	.L71-.L70
	.byte	1,5,22,9
	.half	.L72-.L71
	.byte	3,6,1,5,32,9
	.half	.L73-.L72
	.byte	3,124,1,5,29,9
	.half	.L10-.L73
	.byte	1,5,27,9
	.half	.L8-.L10
	.byte	3,122,1,5,25,7,9
	.half	.L7-.L8
	.byte	3,15,1,5,15,9
	.half	.L74-.L7
	.byte	1,5,25,9
	.half	.L75-.L74
	.byte	1,5,31,9
	.half	.L76-.L75
	.byte	1,5,60,9
	.half	.L77-.L76
	.byte	1,5,50,9
	.half	.L78-.L77
	.byte	1,5,60,9
	.half	.L79-.L78
	.byte	1,5,66,9
	.half	.L80-.L79
	.byte	1,5,46,9
	.half	.L81-.L80
	.byte	1,5,5,9
	.half	.L34-.L81
	.byte	1,5,1,9
	.half	.L12-.L34
	.byte	3,1,1,7,9
	.half	.L23-.L12
	.byte	0,1,1
.L45:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_ranges'
.L22:
	.word	-1,.L14,0,.L23-.L14,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_loc'
.L13:
	.word	-1,.L14,0,.L25-.L14
	.half	2
	.byte	138,0
	.word	0,0
.L31:
	.word	-1,.L14,.L35-.L14,.L25-.L14
	.half	1
	.byte	83
	.word	0,0
.L32:
	.word	-1,.L14,.L36-.L14,.L37-.L14
	.half	5
	.byte	144,32,157,32,0
	.word	.L38-.L14,.L39-.L14
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L30:
	.word	-1,.L14,.L33-.L14,.L34-.L14
	.half	1
	.byte	82
	.word	0,0
.L28:
	.word	-1,.L14,0,.L25-.L14
	.half	1
	.byte	84
	.word	0,0
.L27:
	.word	-1,.L14,0,.L25-.L14
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L82:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_LutLinearF32_searchBin')
	.sect	'.debug_frame'
	.word	24
	.word	.L82,.L14,.L25-.L14
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
