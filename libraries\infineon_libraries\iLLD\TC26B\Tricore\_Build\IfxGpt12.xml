<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxCcu6" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Gpt12/Std/IfxGpt12.c</iLLD:file>
  <iLLD:file class="mchal">Gpt12/IncrEnc/IfxGpt12_IncrEnc.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxGpt12_PinMap.c</iLLD:file>
</iLLD:filelist>
