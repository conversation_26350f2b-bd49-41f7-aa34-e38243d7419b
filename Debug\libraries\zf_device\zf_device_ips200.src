	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42076a --dep-file=zf_device_ips200.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_device/zf_device_ips200.src ../libraries/zf_device/zf_device_ips200.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_device/zf_device_ips200.c'

	
$TC16X
	
	.sdecl	'.text.zf_device_ips200.ips200_write_data',code,cluster('ips200_write_data')
	.sect	'.text.zf_device_ips200.ips200_write_data'
	.align	2
	
; Function ips200_write_data
.L278:
ips200_write_data:	.type	func
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
	ld.a	a2,[a15]24
.L1662:
	sha	d15,d4,#9
.L1663:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
	ld.a	a15,[a15]24
	ld.w	d0,[a15]
.L1664:
	insert	d0,d0,#0,#9,#4
.L1665:
	or	d15,d0
.L1666:
	st.w	[a2],d15
.L1667:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
	ld.a	a2,[a15]32
.L1668:
	sha	d4,#-4
.L694:
	movh.a	a15,#@his(IfxPort_cfg_indexMap)
	lea	a15,[a15]@los(IfxPort_cfg_indexMap)
	ld.a	a15,[a15]32
	ld.w	d15,[a15]
.L1669:
	insert	d15,d15,#0,#0,#4
.L1670:
	or	d4,d15
.L1671:
	st.w	[a2],d4
.L1672:
	ret
.L668:
	
__ips200_write_data_function_end:
	.size	ips200_write_data,__ips200_write_data_function_end-ips200_write_data
.L427:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_write_command',code,cluster('ips200_write_command')
	.sect	'.text.zf_device_ips200.ips200_write_command'
	.align	2
	
; Function ips200_write_command
.L280:
ips200_write_command:	.type	func
	mov	d8,d4
.L696:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1677:
	jne	d15,#0,.L2
.L1678:
	mov	d15,#0
	jeq	d15,#0,.L3
	mov	d4,#480
.L695:
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L4
.L3:
	mov	d4,#480
.L697:
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L4:
	mov	d4,#2
	mov	d5,d8
.L698:
	call	spi_write_8bit
.L699:
	mov	d15,#1
	jeq	d15,#0,.L5
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	mov	d15,#1
	st.w	[a2],d15
	j	.L6
.L5:
	mov	d4,#480
	call	get_port
	add.a	a2,#4
	movh	d15,#1
	st.w	[a2],d15
.L6:
	j	.L7
.L2:
	mov	d15,#0
	jeq	d15,#0,.L8
	mov	d4,#482
.L700:
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L9
.L8:
	mov	d4,#482
.L701:
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L9:
	mov	d15,#0
	jeq	d15,#0,.L10
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L11
.L10:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L11:
	mov	d15,#1
	jeq	d15,#0,.L12
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	mov	d15,#8
	st.w	[a2],d15
	j	.L13
.L12:
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	movh	d15,#8
	st.w	[a2],d15
.L13:
	mov	d15,#0
	jeq	d15,#0,.L14
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L15
.L14:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L15:
	mov	d4,d8
.L702:
	call	ips200_write_data
.L703:
	mov	d15,#1
	jeq	d15,#0,.L16
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L17
.L16:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L17:
	mov	d15,#1
	jeq	d15,#0,.L18
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L19
.L18:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L19:
	mov	d15,#1
	jeq	d15,#0,.L20
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	mov	d15,#2
	st.w	[a2],d15
	j	.L21
.L20:
	mov	d4,#481
	call	get_port
	add.a	a2,#4
	movh	d15,#2
	st.w	[a2],d15
.L21:
.L7:
	ret
.L670:
	
__ips200_write_command_function_end:
	.size	ips200_write_command,__ips200_write_command_function_end-ips200_write_command
.L432:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_write_8bit_data',code,cluster('ips200_write_8bit_data')
	.sect	'.text.zf_device_ips200.ips200_write_8bit_data'
	.align	2
	
; Function ips200_write_8bit_data
.L282:
ips200_write_8bit_data:	.type	func
	mov	d8,d4
.L705:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1683:
	jne	d15,#0,.L22
.L1684:
	mov	d4,#2
.L704:
	mov	d5,d8
.L706:
	call	spi_write_8bit
.L707:
	j	.L23
.L22:
	mov	d15,#0
	jeq	d15,#0,.L24
	mov	d4,#482
.L708:
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L25
.L24:
	mov	d4,#482
.L709:
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L25:
	mov	d15,#1
	jeq	d15,#0,.L26
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	mov	d15,#8
	st.w	[a2],d15
	j	.L27
.L26:
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	movh	d15,#8
	st.w	[a2],d15
.L27:
	mov	d15,#0
	jeq	d15,#0,.L28
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L29
.L28:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L29:
	mov	d4,d8
.L710:
	call	ips200_write_data
.L711:
	mov	d15,#1
	jeq	d15,#0,.L30
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L31
.L30:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L31:
	mov	d15,#1
	jeq	d15,#0,.L32
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L33
.L32:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L33:
.L23:
	ret
.L673:
	
__ips200_write_8bit_data_function_end:
	.size	ips200_write_8bit_data,__ips200_write_8bit_data_function_end-ips200_write_8bit_data
.L437:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_write_8bit_data_array',code,cluster('ips200_write_8bit_data_array')
	.sect	'.text.zf_device_ips200.ips200_write_8bit_data_array'
	.align	2
	
; Function ips200_write_8bit_data_array
.L284:
ips200_write_8bit_data_array:	.type	func
	mov.aa	a15,a4
.L714:
	mov	d8,d4
.L715:
	movh.a	a2,#@his(ips200_display_type)
	lea	a2,[a2]@los(ips200_display_type)
	ld.bu	d15,[a2]
.L1689:
	jne	d15,#0,.L34
.L1690:
	mov	d4,#2
.L713:
	mov.aa	a4,a15
	mov	d5,d8
.L716:
	call	spi_write_8bit_array
.L712:
	j	.L35
.L34:
	mov	d15,#0
	jeq	d15,#0,.L36
	mov	d4,#482
.L718:
	call	get_port
.L717:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L37
.L36:
	mov	d4,#482
.L720:
	call	get_port
.L719:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L37:
	mov	d15,#1
	jeq	d15,#0,.L38
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	mov	d15,#8
	st.w	[a2],d15
	j	.L39
.L38:
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	movh	d15,#8
	st.w	[a2],d15
.L39:
	j	.L40
.L41:
	mov	d15,#0
	jeq	d15,#0,.L42
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L43
.L42:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L43:
	ld.bu	d4,[a15]
	call	ips200_write_data
.L1691:
	mov	d15,#1
	jeq	d15,#0,.L44
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L45
.L44:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L45:
	add.a	a15,#1
.L40:
	mov	d15,d8
	add	d8,#-1
.L1692:
	jne	d15,#0,.L41
.L1693:
	mov	d15,#1
	jeq	d15,#0,.L46
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L47
.L46:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L47:
.L35:
	ret
.L676:
	
__ips200_write_8bit_data_array_function_end:
	.size	ips200_write_8bit_data_array,__ips200_write_8bit_data_array_function_end-ips200_write_8bit_data_array
.L442:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_write_16bit_data',code,cluster('ips200_write_16bit_data')
	.sect	'.text.zf_device_ips200.ips200_write_16bit_data'
	.align	2
	
	.global	ips200_write_16bit_data
; Function ips200_write_16bit_data
.L286:
ips200_write_16bit_data:	.type	func
	mov	d8,d4
.L722:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1698:
	jne	d15,#0,.L48
.L1699:
	mov	d4,#2
.L721:
	mov	d5,d8
.L723:
	call	spi_write_16bit
.L724:
	j	.L49
.L48:
	mov	d15,#0
	jeq	d15,#0,.L50
	mov	d4,#482
.L725:
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L51
.L50:
	mov	d4,#482
.L726:
	call	get_port
	add.a	a2,#4
	movh	d0,#4
	st.w	[a2],d0
.L51:
	mov	d15,#1
	jeq	d15,#0,.L52
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	mov	d15,#8
	st.w	[a2],d15
	j	.L53
.L52:
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	movh	d0,#8
	st.w	[a2],d0
.L53:
	mov	d15,#0
	jeq	d15,#0,.L54
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L55
.L54:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L55:
	sha	d4,d8,#-8
.L1700:
	call	ips200_write_data
.L1701:
	mov	d15,#1
	jeq	d15,#0,.L56
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L57
.L56:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L57:
	mov	d15,#0
	jeq	d15,#0,.L58
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L59
.L58:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L59:
	and	d4,d8,#255
.L1702:
	call	ips200_write_data
.L1703:
	mov	d15,#1
	jeq	d15,#0,.L60
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L61
.L60:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L61:
	mov	d15,#1
	jeq	d15,#0,.L62
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L63
.L62:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L63:
.L49:
	ret
.L679:
	
__ips200_write_16bit_data_function_end:
	.size	ips200_write_16bit_data,__ips200_write_16bit_data_function_end-ips200_write_16bit_data
.L447:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_write_16bit_data_array',code,cluster('ips200_write_16bit_data_array')
	.sect	'.text.zf_device_ips200.ips200_write_16bit_data_array'
	.align	2
	
; Function ips200_write_16bit_data_array
.L288:
ips200_write_16bit_data_array:	.type	func
	mov.aa	a15,a4
.L729:
	mov	d8,d4
.L730:
	movh.a	a2,#@his(ips200_display_type)
	lea	a2,[a2]@los(ips200_display_type)
	ld.bu	d15,[a2]
.L1708:
	jne	d15,#0,.L64
.L1709:
	mov	d4,#2
.L728:
	mov.aa	a4,a15
	mov	d5,d8
.L731:
	call	spi_write_16bit_array
.L727:
	j	.L65
.L64:
	mov	d15,#0
	jeq	d15,#0,.L66
	mov	d4,#482
.L733:
	call	get_port
.L732:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L67
.L66:
	mov	d4,#482
.L735:
	call	get_port
.L734:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L67:
	mov	d15,#1
	jeq	d15,#0,.L68
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	mov	d15,#8
	st.w	[a2],d15
	j	.L69
.L68:
	mov	d4,#483
	call	get_port
	add.a	a2,#4
	movh	d15,#8
	st.w	[a2],d15
.L69:
	j	.L70
.L71:
	mov	d15,#0
	jeq	d15,#0,.L72
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L73
.L72:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L73:
	ld.hu	d4,[a15]0
.L1710:
	sha	d4,#-8
.L1711:
	call	ips200_write_data
.L1712:
	mov	d15,#1
	jeq	d15,#0,.L74
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L75
.L74:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L75:
	mov	d15,#0
	jeq	d15,#0,.L76
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L77
.L76:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L77:
	ld.hu	d15,[a15]0
.L1713:
	and	d4,d15,#255
.L1714:
	call	ips200_write_data
.L1715:
	mov	d15,#1
	jeq	d15,#0,.L78
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	mov	d15,#32
	st.w	[a2],d15
	j	.L79
.L78:
	mov	d4,#485
	call	get_port
	add.a	a2,#4
	movh	d15,#32
	st.w	[a2],d15
.L79:
	add.a	a15,#2
.L70:
	mov	d15,d8
	add	d8,#-1
.L1716:
	jne	d15,#0,.L71
.L1717:
	mov	d15,#1
	jeq	d15,#0,.L80
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L81
.L80:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L81:
.L65:
	ret
.L682:
	
__ips200_write_16bit_data_array_function_end:
	.size	ips200_write_16bit_data_array,__ips200_write_16bit_data_array_function_end-ips200_write_16bit_data_array
.L452:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_set_region',code,cluster('ips200_set_region')
	.sect	'.text.zf_device_ips200.ips200_set_region'
	.align	2
	
; Function ips200_set_region
.L290:
ips200_set_region:	.type	func
	mov	d15,d4
.L739:
	mov	e8,d6,d5
	mov	d10,d7
.L740:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d15,d0
.L736:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#330
.L738:
	call	debug_assert_handler
.L737:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L741:
	lt.u	d4,d8,d0
.L742:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#331
	call	debug_assert_handler
.L1722:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L743:
	lt.u	d4,d9,d0
.L744:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#332
	call	debug_assert_handler
.L1723:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d10,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#333
	call	debug_assert_handler
.L1724:
	mov	d4,#42
	call	ips200_write_command
.L1725:
	mov	d4,d15
.L745:
	call	ips200_write_16bit_data
.L746:
	mov	d4,d9
.L747:
	call	ips200_write_16bit_data
.L748:
	mov	d4,#43
	call	ips200_write_command
.L749:
	mov	d4,d8
.L750:
	call	ips200_write_16bit_data
.L751:
	mov	d4,d10
.L752:
	call	ips200_write_16bit_data
.L753:
	mov	d4,#44
	call	ips200_write_command
.L1726:
	ret
.L685:
	
__ips200_set_region_function_end:
	.size	ips200_set_region,__ips200_set_region_function_end-ips200_set_region
.L457:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_debug_init',code,cluster('ips200_debug_init')
	.sect	'.text.zf_device_ips200.ips200_debug_init'
	.align	2
	
; Function ips200_debug_init
.L292:
ips200_debug_init:	.type	func
	sub.a	a10,#24
.L754:
	lea	a4,[a10]0
	call	debug_output_struct_init
.L1731:
	mov	d15,#1
.L1732:
	st.h	[a10],d15
.L1733:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1734:
	st.h	[a10]2,d15
.L1735:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L1736:
	st.h	[a10]4,d15
.L1737:
	movh.a	a15,#@his(ips200_display_font)
	lea	a15,[a15]@los(ips200_display_font)
	ld.bu	d15,[a15]
.L1738:
	mov	d0,#0
	jeq	d15,d0,.L82
.L1739:
	mov	d0,#1
	jeq	d15,d0,.L83
.L1740:
	mov	d0,#2
	jeq	d15,d0,.L84
	j	.L85
.L82:
	mov	d15,#6
.L1741:
	st.b	[a10]6,d15
.L1742:
	mov	d15,#8
.L1743:
	st.b	[a10]7,d15
.L1744:
	j	.L86
.L83:
	mov	d15,#8
.L1745:
	st.b	[a10]6,d15
.L1746:
	mov	d15,#16
.L1747:
	st.b	[a10]7,d15
.L1748:
	j	.L87
.L84:
	j	.L88
.L85:
.L88:
.L87:
.L86:
	movh.a	a15,#@his(ips200_show_string)
	lea	a15,[a15]@los(ips200_show_string)
.L1749:
	st.a	[a10]12,a15
.L1750:
	movh.a	a15,#@his(ips200_clear)
	lea	a15,[a15]@los(ips200_clear)
.L1751:
	st.a	[a10]16,a15
.L1752:
	lea	a4,[a10]0
	call	debug_output_init
.L1753:
	ret
.L690:
	
__ips200_debug_init_function_end:
	.size	ips200_debug_init,__ips200_debug_init_function_end-ips200_debug_init
.L462:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_clear',code,cluster('ips200_clear')
	.sect	'.text.zf_device_ips200.ips200_clear'
	.align	2
	
	.global	ips200_clear
; Function ips200_clear
.L294:
ips200_clear:	.type	func
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1136:
	mul	d4,d15,#2
	call	malloc
.L755:
	mov.aa	a12,a2
.L757:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1137:
	jne	d15,#0,.L89
.L1138:
	mov	d15,#0
	jeq	d15,#0,.L90
	mov	d4,#482
	call	get_port
.L756:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L91
.L90:
	mov	d4,#482
	call	get_port
.L758:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L91:
.L89:
	mov	d4,#0
.L1139:
	mov	d5,#0
.L1140:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1141:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L1142:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L1143:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	ips200_set_region
.L1144:
	mov	d0,#0
.L759:
	j	.L92
.L93:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L1145:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d15,[a2]0
.L1146:
	st.h	[a15],d15
.L1147:
	add	d0,#1
.L760:
	extr.u	d0,d0,#0,#16
.L92:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1148:
	jlt.u	d0,d15,.L93
.L1149:
	mov	d15,#0
.L761:
	j	.L94
.L95:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d4,[a15]0
	mov.aa	a4,a12
.L763:
	call	ips200_write_16bit_data_array
.L764:
	add	d15,#1
.L762:
	extr.u	d15,d15,#0,#16
.L94:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L1150:
	jlt.u	d15,d0,.L95
.L1151:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L765:
	jne	d15,#0,.L96
.L1152:
	mov	d15,#1
	jeq	d15,#0,.L97
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L98
.L97:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L98:
.L96:
	mov.aa	a4,a12
.L766:
	call	free
.L767:
	ret
.L483:
	
__ips200_clear_function_end:
	.size	ips200_clear,__ips200_clear_function_end-ips200_clear
.L337:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_full',code,cluster('ips200_full')
	.sect	'.text.zf_device_ips200.ips200_full'
	.align	2
	
	.global	ips200_full
; Function ips200_full
.L296:
ips200_full:	.type	func
	mov	d8,d4
.L769:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1157:
	mul	d4,d15,#2
.L768:
	call	malloc
.L770:
	mov.aa	a12,a2
.L772:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1158:
	jne	d15,#0,.L99
.L1159:
	mov	d15,#0
	jeq	d15,#0,.L100
	mov	d4,#482
	call	get_port
.L771:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L101
.L100:
	mov	d4,#482
	call	get_port
.L773:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L101:
.L99:
	mov	d4,#0
.L1160:
	mov	d5,#0
.L1161:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1162:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L1163:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L1164:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
	call	ips200_set_region
.L1165:
	mov	d0,#0
.L774:
	j	.L102
.L103:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L1166:
	st.h	[a15],d8
.L1167:
	add	d0,#1
.L775:
	extr.u	d0,d0,#0,#16
.L102:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L1168:
	jlt.u	d0,d15,.L103
.L1169:
	mov	d15,#0
.L776:
	j	.L104
.L105:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d4,[a15]0
	mov.aa	a4,a12
.L778:
	call	ips200_write_16bit_data_array
.L779:
	add	d15,#1
.L777:
	extr.u	d15,d15,#0,#16
.L104:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L1170:
	jlt.u	d15,d0,.L105
.L1171:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L780:
	jne	d15,#0,.L106
.L1172:
	mov	d15,#1
	jeq	d15,#0,.L107
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L108
.L107:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L108:
.L106:
	mov.aa	a4,a12
.L781:
	call	free
.L782:
	ret
.L489:
	
__ips200_full_function_end:
	.size	ips200_full,__ips200_full_function_end-ips200_full
.L342:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_set_dir',code,cluster('ips200_set_dir')
	.sect	'.text.zf_device_ips200.ips200_set_dir'
	.align	2
	
	.global	ips200_set_dir
; Function ips200_set_dir
.L298:
ips200_set_dir:	.type	func
	movh.a	a15,#@his(ips200_display_dir)
	lea	a15,[a15]@los(ips200_display_dir)
.L1177:
	st.b	[a15],d4
.L1178:
	mov	d15,#0
	jeq	d15,d4,.L109
.L1179:
	mov	d15,#1
	jeq	d15,d4,.L110
.L1180:
	mov	d15,#2
	jeq	d15,d4,.L111
.L1181:
	mov	d15,#3
	jeq	d15,d4,.L112
	j	.L113
.L109:
.L110:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
.L1182:
	mov	d15,#240
.L1183:
	st.h	[a15],d15
.L1184:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
.L1185:
	mov	d15,#320
.L1186:
	st.h	[a15],d15
.L1187:
	j	.L114
.L111:
.L112:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
.L1188:
	mov	d15,#320
.L1189:
	st.h	[a15],d15
.L1190:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
.L1191:
	mov	d15,#240
.L1192:
	st.h	[a15],d15
.L1193:
	j	.L115
.L113:
.L115:
.L114:
	ret
.L495:
	
__ips200_set_dir_function_end:
	.size	ips200_set_dir,__ips200_set_dir_function_end-ips200_set_dir
.L347:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_set_font',code,cluster('ips200_set_font')
	.sect	'.text.zf_device_ips200.ips200_set_font'
	.align	2
	
	.global	ips200_set_font
; Function ips200_set_font
.L300:
ips200_set_font:	.type	func
	movh.a	a15,#@his(ips200_display_font)
	lea	a15,[a15]@los(ips200_display_font)
.L1198:
	st.b	[a15],d4
.L1199:
	ret
.L498:
	
__ips200_set_font_function_end:
	.size	ips200_set_font,__ips200_set_font_function_end-ips200_set_font
.L352:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_set_color',code,cluster('ips200_set_color')
	.sect	'.text.zf_device_ips200.ips200_set_color'
	.align	2
	
	.global	ips200_set_color
; Function ips200_set_color
.L302:
ips200_set_color:	.type	func
	movh.a	a15,#@his(ips200_pencolor)
	lea	a15,[a15]@los(ips200_pencolor)
.L1204:
	st.h	[a15],d4
.L1205:
	movh.a	a15,#@his(ips200_bgcolor)
	lea	a15,[a15]@los(ips200_bgcolor)
.L1206:
	st.h	[a15],d5
.L1207:
	ret
.L501:
	
__ips200_set_color_function_end:
	.size	ips200_set_color,__ips200_set_color_function_end-ips200_set_color
.L357:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_draw_point',code,cluster('ips200_draw_point')
	.sect	'.text.zf_device_ips200.ips200_draw_point'
	.align	2
	
	.global	ips200_draw_point
; Function ips200_draw_point
.L304:
ips200_draw_point:	.type	func
	mov	e8,d5,d4
	mov	d10,d6
.L786:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L787:
	lt.u	d4,d8,d15
.L784:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#513
.L785:
	call	debug_assert_handler
.L783:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L788:
	lt.u	d4,d9,d15
.L789:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#514
	call	debug_assert_handler
.L1212:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1213:
	jne	d15,#0,.L116
.L1214:
	mov	d15,#0
	jeq	d15,#0,.L117
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L118
.L117:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L118:
.L116:
	mov	e4,d9,d8
	mov	e6,d9,d8
.L790:
	call	ips200_set_region
.L1215:
	mov	d4,d10
.L791:
	call	ips200_write_16bit_data
.L792:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1216:
	jne	d15,#0,.L119
.L1217:
	mov	d15,#1
	jeq	d15,#0,.L120
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L121
.L120:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L121:
.L119:
	ret
.L506:
	
__ips200_draw_point_function_end:
	.size	ips200_draw_point,__ips200_draw_point_function_end-ips200_draw_point
.L362:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_draw_line',code,cluster('ips200_draw_line')
	.sect	'.text.zf_device_ips200.ips200_draw_line'
	.align	2
	
	.global	ips200_draw_line
; Function ips200_draw_line
.L306:
ips200_draw_line:	.type	func
	sub.a	a10,#8
.L793:
	mov	d15,d4
.L797:
	mov	e8,d6,d5
	mov	d10,d7
.L799:
	ld.hu	d11,[a10]8
.L800:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d15,d0
.L795:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#543
.L796:
	call	debug_assert_handler
.L794:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L801:
	lt.u	d4,d8,d0
.L802:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#544
	call	debug_assert_handler
.L1222:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L803:
	lt.u	d4,d9,d0
.L804:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#545
	call	debug_assert_handler
.L1223:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
	lt.u	d4,d10,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#546
	call	debug_assert_handler
.L518:
	jge.u	d15,d9,.L122
.L805:
	mov	d0,#1
	st.w	[a10],d0
.L1224:
	j	.L123
.L122:
	mov	d0,#-1
	st.w	[a10],d0
.L123:
	jge.u	d8,d10,.L124
.L806:
	mov	d14,#1
.L807:
	j	.L125
.L124:
	mov	d14,#-1
.L125:
.L126:
	jeq	d15,d9,.L127
.L808:
	sub	d0,d8,d10
.L809:
	itof	d0,d0
.L810:
	sub	d1,d15,d9
.L811:
	itof	d1,d1
.L1225:
	div.f	d13,d0,d1
.L812:
	utof	d0,d8
.L813:
	utof	d1,d15
.L1226:
	msub.f	d12,d0,d1,d13
.L814:
	j	.L128
.L127:
	j	.L129
.L130:
	mov	e4,d8,d9
.L815:
	mov	d6,d11
.L816:
	call	ips200_draw_point
.L817:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L129:
	jne	d8,d10,.L130
.L818:
	mov	e4,d10,d9
.L819:
	mov	d6,d11
.L820:
	call	ips200_draw_point
.L821:
	j	.L131
.L128:
	sub	d0,d8,d10
.L822:
	jlt	d0,#0,.L132
.L823:
	sub	d0,d8,d10
.L824:
	j	.L133
.L132:
	sub	d0,d8,d10
.L825:
	rsub	d0,#0
.L133:
	sub	d1,d15,d9
.L826:
	jlt	d1,#0,.L134
.L827:
	sub	d1,d15,d9
.L828:
	j	.L135
.L134:
	sub	d1,d15,d9
.L829:
	rsub	d1,#0
.L135:
	jge	d1,d0,.L136
.L1227:
	j	.L137
.L138:
	mov	e4,d8,d15
.L830:
	mov	d6,d11
.L831:
	call	ips200_draw_point
.L832:
	add	d8,d14
	extr.u	d8,d8,#0,#16
.L833:
	utof	d15,d8
.L798:
	sub.f	d15,d15,d12
.L1228:
	div.f	d4,d15,d13
.L1229:
	call	__f_ftos
	extr.u	d15,d2,#0,#16
.L137:
	jne	d8,d10,.L138
.L834:
	mov	e4,d10,d15
	mov	d6,d11
.L836:
	call	ips200_draw_point
.L837:
	j	.L139
.L136:
	j	.L140
.L141:
	mov	e4,d8,d15
.L838:
	mov	d6,d11
.L839:
	call	ips200_draw_point
.L840:
	ld.w	d0,[a10]
.L841:
	add	d15,d0
.L835:
	extr.u	d15,d15,#0,#16
.L843:
	utof	d0,d15
.L842:
	madd.f	d4,d12,d0,d13
.L1230:
	call	__f_ftos
	extr.u	d8,d2,#0,#16
.L140:
	jne	d15,d9,.L141
.L845:
	mov	e4,d8,d9
.L844:
	mov	d6,d11
.L846:
	call	ips200_draw_point
.L139:
.L131:
	ret
.L511:
	
__ips200_draw_line_function_end:
	.size	ips200_draw_line,__ips200_draw_line_function_end-ips200_draw_line
.L367:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_char',code,cluster('ips200_show_char')
	.sect	'.text.zf_device_ips200.ips200_show_char'
	.align	2
	
	.global	ips200_show_char
; Function ips200_show_char
.L308:
ips200_show_char:	.type	func
	lea	a10,[a10]-256
.L847:
	mov	e8,d5,d4
	mov	d10,d6
.L851:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L852:
	lt.u	d4,d8,d0
.L849:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#606
.L850:
	call	debug_assert_handler
.L848:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L853:
	lt.u	d4,d9,d0
.L854:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#607
	call	debug_assert_handler
.L530:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1235:
	jne	d15,#0,.L142
.L1236:
	mov	d15,#0
	jeq	d15,#0,.L143
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d0,#4
	st.w	[a2],d0
	j	.L144
.L143:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L144:
.L142:
	movh.a	a15,#@his(ips200_display_font)
	lea	a15,[a15]@los(ips200_display_font)
	ld.bu	d0,[a15]
.L1237:
	mov	d15,#0
	jeq	d15,d0,.L145
.L1238:
	mov	d15,#1
	jeq	d15,d0,.L146
.L1239:
	mov	d15,#2
	jeq	d15,d0,.L147
	j	.L148
.L145:
	add	d15,d8,#5
.L855:
	extr.u	d6,d15,#0,#16
.L856:
	add	d15,d9,#7
.L857:
	extr.u	d7,d15,#0,#16
.L858:
	mov	e4,d9,d8
.L859:
	call	ips200_set_region
.L1240:
	mov	d0,#0
.L860:
	j	.L149
.L150:
	add	d15,d10,#-32
.L1241:
	mul	d15,d15,#6
.L1242:
	movh.a	a15,#@his(ascii_font_6x8)
	lea	a15,[a15]@los(ascii_font_6x8)
.L1243:
	addsc.a	a15,a15,d15,#0
.L1244:
	addsc.a	a15,a15,d0,#0
	ld.bu	d1,[a15]
.L861:
	mov	d2,#0
.L862:
	j	.L151
.L152:
	jz.t	d1:0,.L153
.L1245:
	mov	d15,#6
.L1246:
	madd	d15,d0,d2,d15
.L1247:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1248:
	movh.a	a2,#@his(ips200_pencolor)
	lea	a2,[a2]@los(ips200_pencolor)
	ld.hu	d15,[a2]0
.L1249:
	st.h	[a15],d15
.L1250:
	j	.L154
.L153:
	mov	d15,#6
.L1251:
	madd	d15,d0,d2,d15
.L1252:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1253:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d15,[a2]0
.L1254:
	st.h	[a15],d15
.L154:
	sha	d1,#-1
.L1255:
	add	d2,#1
.L151:
	jlt.u	d2,#8,.L152
.L537:
	add	d0,#1
.L149:
	jlt.u	d0,#6,.L150
.L1256:
	lea	a4,[a10]0
.L1257:
	mov	d4,#48
	call	ips200_write_16bit_data_array
.L534:
	j	.L155
.L146:
	add	d15,d8,#7
.L863:
	extr.u	d6,d15,#0,#16
.L864:
	add	d15,d9,#15
.L865:
	extr.u	d7,d15,#0,#16
.L866:
	mov	e4,d9,d8
.L867:
	call	ips200_set_region
.L1258:
	mov	d3,#0
.L868:
	j	.L156
.L157:
	add	d0,d10,#-32
.L1259:
	mul	d15,d0,#16
.L1260:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1261:
	addsc.a	a15,a15,d15,#0
.L1262:
	addsc.a	a15,a15,d3,#0
	ld.bu	d0,[a15]
.L869:
	add	d1,d10,#-32
.L1263:
	mul	d15,d1,#16
.L1264:
	movh.a	a15,#@his(ascii_font_8x16)
	lea	a15,[a15]@los(ascii_font_8x16)
.L1265:
	addsc.a	a15,a15,d15,#0
.L1266:
	addsc.a	a15,a15,d3,#0
	ld.bu	d4,[a15]8
.L871:
	mov	d1,#0
.L872:
	j	.L158
.L159:
	jz.t	d0:0,.L160
.L1267:
	mov	d15,#8
.L1268:
	madd	d2,d3,d1,d15
.L1269:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1270:
	movh.a	a2,#@his(ips200_pencolor)
	lea	a2,[a2]@los(ips200_pencolor)
	ld.hu	d2,[a2]0
.L1271:
	st.h	[a15],d2
.L1272:
	j	.L161
.L160:
	mov	d15,#8
.L1273:
	madd	d2,d3,d1,d15
.L1274:
	mul	d15,d2,#2
	addsc.a	a15,a10,d15,#0
.L1275:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d2,[a2]0
.L1276:
	st.h	[a15],d2
.L161:
	sha	d0,#-1
.L1277:
	add	d1,#1
.L158:
	jlt.u	d1,#8,.L159
.L1278:
	mov	d0,#0
.L870:
	j	.L162
.L163:
	jz.t	d4:0,.L164
.L1279:
	mov	d15,#8
.L1280:
	madd	d1,d3,d0,d15
.L1281:
	add	d15,d1,#64
.L1282:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1283:
	movh.a	a2,#@his(ips200_pencolor)
	lea	a2,[a2]@los(ips200_pencolor)
	ld.hu	d15,[a2]0
.L1284:
	st.h	[a15],d15
.L1285:
	j	.L165
.L164:
	mov	d15,#8
.L1286:
	madd	d15,d3,d0,d15
.L1287:
	add	d15,d15,#64
.L1288:
	mul	d15,d15,#2
	addsc.a	a15,a10,d15,#0
.L1289:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d15,[a2]0
.L1290:
	st.h	[a15],d15
.L165:
	sha	d4,#-1
.L1291:
	add	d0,#1
.L162:
	jlt.u	d0,#8,.L163
.L542:
	add	d3,#1
.L156:
	jlt.u	d3,#8,.L157
.L1292:
	lea	a4,[a10]0
.L1293:
	mov	d4,#128
	call	ips200_write_16bit_data_array
.L539:
	j	.L166
.L147:
	j	.L167
.L148:
.L167:
.L166:
.L155:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1294:
	jne	d15,#0,.L168
.L1295:
	mov	d15,#1
	jeq	d15,#0,.L169
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L170
.L169:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L170:
.L168:
	ret
.L525:
	
__ips200_show_char_function_end:
	.size	ips200_show_char,__ips200_show_char_function_end-ips200_show_char
.L372:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_string',code,cluster('ips200_show_string')
	.sect	'.text.zf_device_ips200.ips200_show_string'
	.align	2
	
	.global	ips200_show_string
; Function ips200_show_string
.L310:
ips200_show_string:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L876:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L877:
	lt.u	d4,d8,d15
.L874:
	movh.a	a4,#@his(.1.str)
.L873:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#699
.L875:
	call	debug_assert_handler
.L1300:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L878:
	lt.u	d4,d9,d15
.L879:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#700
	call	debug_assert_handler
.L550:
	mov	d10,#0
.L880:
	j	.L171
.L172:
	movh.a	a15,#@his(ips200_display_font)
	lea	a15,[a15]@los(ips200_display_font)
	ld.bu	d0,[a15]
.L1301:
	mov	d15,#0
	jeq	d15,d0,.L173
.L1302:
	mov	d15,#1
	jeq	d15,d0,.L174
.L1303:
	mov	d1,#2
	jeq	d1,d0,.L175
	j	.L176
.L173:
	mov	d0,#6
.L882:
	madd	d15,d8,d10,d0
.L883:
	extr.u	d4,d15,#0,#16
.L1304:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L884:
	mov	d5,d9
.L885:
	call	ips200_show_char
.L886:
	j	.L177
.L174:
	mov	d15,#8
.L887:
	madd	d15,d8,d10,d15
.L888:
	extr.u	d4,d15,#0,#16
.L1305:
	addsc.a	a15,a12,d10,#0
	ld.b	d6,[a15]0
.L889:
	mov	d5,d9
.L890:
	call	ips200_show_char
.L891:
	j	.L178
.L175:
	j	.L179
.L176:
.L179:
.L178:
.L177:
	add	d10,#1
.L881:
	extr.u	d10,d10,#0,#16
.L171:
	addsc.a	a15,a12,d10,#0
	ld.b	d15,[a15]0
.L1306:
	jne	d15,#0,.L172
.L1307:
	ret
.L545:
	
__ips200_show_string_function_end:
	.size	ips200_show_string,__ips200_show_string_function_end-ips200_show_string
.L377:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_int',code,cluster('ips200_show_int')
	.sect	'.text.zf_device_ips200.ips200_show_int'
	.align	2
	
	.global	ips200_show_int
; Function ips200_show_int
.L312:
ips200_show_int:	.type	func
	sub.a	a10,#16
.L892:
	mov	e10,d5,d4
	mov	d9,d6
.L896:
	mov	d12,d7
.L897:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L898:
	lt.u	d4,d10,d0
.L894:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#729
.L895:
	call	debug_assert_handler
.L893:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L899:
	lt.u	d4,d11,d0
.L900:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#730
	call	debug_assert_handler
.L1312:
	mov	d0,#0
	lt.u	d4,d0,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#731
	call	debug_assert_handler
.L1313:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#732
	call	debug_assert_handler
.L558:
	mov	d15,#1
.L901:
	lea	a4,[a10]0
.L1314:
	mov	d4,#0
.L1315:
	mov	d5,#12
	call	memset
.L1316:
	lea	a4,[a10]0
.L1317:
	mov	d4,#32
.L1318:
	add	d5,d12,#1
	call	memset
.L1319:
	jge.u	d12,#10,.L180
.L1320:
	j	.L181
.L182:
	mul	d15,d15,#10
.L1321:
	add	d12,#-1
.L181:
	jge.u	d12,#1,.L182
.L1322:
	div	e8,d9,d15
.L180:
	lea	a4,[a10]0
.L1323:
	mov	d4,d9
.L902:
	call	func_int_to_str
.L903:
	lea	a4,[a10]0
.L904:
	mov	e4,d11,d10
.L905:
	call	ips200_show_string
.L1324:
	ret
.L552:
	
__ips200_show_int_function_end:
	.size	ips200_show_int,__ips200_show_int_function_end-ips200_show_int
.L382:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_uint',code,cluster('ips200_show_uint')
	.sect	'.text.zf_device_ips200.ips200_show_uint'
	.align	2
	
	.global	ips200_show_uint
; Function ips200_show_uint
.L314:
ips200_show_uint:	.type	func
	sub.a	a10,#16
.L906:
	mov	e10,d5,d4
	mov	d9,d6
.L910:
	mov	d12,d7
.L911:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d15,[a15]0
.L912:
	lt.u	d4,d10,d15
.L908:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#768
.L909:
	call	debug_assert_handler
.L907:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L913:
	lt.u	d4,d11,d15
.L914:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#769
	call	debug_assert_handler
.L1329:
	mov	d15,#0
	lt.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#770
	call	debug_assert_handler
.L1330:
	mov	d15,#10
	ge.u	d4,d15,d12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#771
	call	debug_assert_handler
.L570:
	mov	d15,#1
.L915:
	lea	a4,[a10]0
.L1331:
	mov	d4,#0
.L1332:
	mov	d5,#12
	call	memset
.L1333:
	lea	a4,[a10]0
.L1334:
	mov	d4,#32
.L1335:
	mov	d5,d12
.L916:
	call	memset
.L917:
	jge.u	d12,#10,.L183
.L1336:
	j	.L184
.L185:
	mul	d15,d15,#10
.L1337:
	add	d12,#-1
.L184:
	jge.u	d12,#1,.L185
.L1338:
	div.u	e8,d9,d15
.L183:
	lea	a4,[a10]0
.L1339:
	mov	d4,d9
.L918:
	call	func_uint_to_str
.L919:
	lea	a4,[a10]0
.L920:
	mov	e4,d11,d10
.L921:
	call	ips200_show_string
.L1340:
	ret
.L564:
	
__ips200_show_uint_function_end:
	.size	ips200_show_uint,__ips200_show_uint_function_end-ips200_show_uint
.L387:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_float',code,cluster('ips200_show_float')
	.sect	'.text.zf_device_ips200.ips200_show_float'
	.align	2
	
	.global	ips200_show_float
; Function ips200_show_float
.L316:
ips200_show_float:	.type	func
	sub.a	a10,#24
.L922:
	st.w	[a10]20,d4
.L926:
	mov	d14,d5
.L927:
	mov	e10,d7,d6
	ld.bu	d9,[a10]24
.L928:
	ld.bu	d8,[a10]28
.L929:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
	ld.w	d15,[a10]20
.L924:
	lt.u	d4,d15,d0
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#810
.L925:
	call	debug_assert_handler
.L923:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L930:
	lt.u	d4,d14,d15
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#811
	call	debug_assert_handler
.L1345:
	mov	d15,#0
	lt.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#812
	call	debug_assert_handler
.L1346:
	mov	d15,#8
	ge.u	d4,d15,d9
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#813
	call	debug_assert_handler
.L1347:
	mov	d15,#0
	lt.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#814
	call	debug_assert_handler
.L1348:
	mov	d15,#6
	ge.u	d4,d15,d8
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#815
	call	debug_assert_handler
.L582:
	mov	d12,#0
	mov	d13,#0
.L931:
	addih	d13,d13,#16368
.L1349:
	lea	a4,[a10]0
.L1350:
	mov	d4,#0
.L1351:
	mov	d5,#17
	call	memset
.L1352:
	lea	a4,[a10]0
.L1353:
	mov	d4,#32
.L1354:
	add	d15,d9,d8
.L1355:
	add	d5,d15,#2
	call	memset
.L1356:
	j	.L186
.L187:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16420
.L932:
	mov	e4,d13,d12
.L933:
	call	__d_mul
	mov	e12,d3,d2
.L1357:
	add	d9,#-1
.L186:
	jge.u	d9,#1,.L187
.L934:
	mov	e4,d11,d10
.L935:
	call	__d_dtoi
	mov	d15,d2
.L936:
	mov	e4,d13,d12
.L937:
	call	__d_dtoi
.L1358:
	div	e4,d15,d2
	call	__d_itod
	mov	e4,d3,d2
.L938:
	mov	e6,d13,d12
.L939:
	call	__d_mul
	mov	e6,d3,d2
.L940:
	mov	e4,d11,d10
.L941:
	call	__d_sub
	mov	e4,d3,d2
.L1359:
	lea	a4,[a10]0
.L1360:
	mov	d6,d8
.L942:
	call	func_double_to_str
.L943:
	lea	a4,[a10]0
	ld.w	d4,[a10]20
.L944:
	mov	d5,d14
.L946:
	call	ips200_show_string
.L945:
	ret
.L575:
	
__ips200_show_float_function_end:
	.size	ips200_show_float,__ips200_show_float_function_end-ips200_show_float
.L392:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_binary_image',code,cluster('ips200_show_binary_image')
	.sect	'.text.zf_device_ips200.ips200_show_binary_image'
	.align	2
	
	.global	ips200_show_binary_image
; Function ips200_show_binary_image
.L318:
ips200_show_binary_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L951:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L952:
	ld.hu	d13,[a10]4
.L953:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L954:
	lt.u	d4,d8,d0
.L949:
	movh.a	a4,#@his(.1.str)
.L948:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#853
.L950:
	call	debug_assert_handler
.L947:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L955:
	lt.u	d4,d9,d0
.L956:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#854
	call	debug_assert_handler
.L1365:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#855
	call	debug_assert_handler
.L597:
	mul	d4,d12,#2
	call	malloc
.L957:
	mov.aa	a13,a2
.L959:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1366:
	jne	d15,#0,.L188
.L1367:
	mov	d15,#0
	jeq	d15,#0,.L189
	mov	d4,#482
	call	get_port
.L958:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L190
.L189:
	mov	d4,#482
	call	get_port
.L960:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L190:
.L188:
	add	d15,d8,d12
.L961:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L962:
	add	d15,d9,d13
.L963:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L964:
	mov	e4,d9,d8
.L965:
	call	ips200_set_region
.L1368:
	mov	d8,#0
.L966:
	j	.L191
.L192:
	mul	d15,d8,d11
.L967:
	div.u	e0,d15,d13
.L968:
	mul	d0,d10
.L969:
	mov	d15,#8
.L1369:
	div.u	e0,d0,d15
.L1370:
	addsc.a	a15,a12,d0,#0
.L970:
	mov	d4,#0
.L971:
	j	.L193
.L194:
	mul	d15,d4,d10
.L973:
	div.u	e2,d15,d12
.L974:
	mov	d15,#8
.L1371:
	div.u	e0,d2,d15
.L1372:
	addsc.a	a2,a15,d0,#0
.L1373:
	ld.bu	d0,[a2]
.L976:
	mov	d15,#8
.L1374:
	div.u	e2,d2,d15
.L975:
	sha	d0,d0,d3
.L977:
	jz.t	d0:7,.L195
.L1375:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1376:
	mov.u	d15,#65535
.L1377:
	st.h	[a2],d15
.L1378:
	j	.L196
.L195:
	mul	d15,d4,#2
	addsc.a	a2,a13,d15,#0
.L1379:
	mov	d15,#0
.L1380:
	st.h	[a2],d15
.L196:
	add	d4,#1
.L193:
	jlt.u	d4,d12,.L194
.L1381:
	mov.aa	a4,a13
.L978:
	mov	d4,d12
.L972:
	call	ips200_write_16bit_data_array
.L979:
	add	d8,#1
.L191:
	jlt.u	d8,d13,.L192
.L1382:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1383:
	jne	d15,#0,.L197
.L1384:
	mov	d15,#1
	jeq	d15,#0,.L198
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L199
.L198:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L199:
.L197:
	mov.aa	a4,a13
.L980:
	call	free
.L981:
	ret
.L588:
	
__ips200_show_binary_image_function_end:
	.size	ips200_show_binary_image,__ips200_show_binary_image_function_end-ips200_show_binary_image
.L397:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_gray_image',code,cluster('ips200_show_gray_image')
	.sect	'.text.zf_device_ips200.ips200_show_gray_image'
	.align	2
	
	.global	ips200_show_gray_image
; Function ips200_show_gray_image
.L320:
ips200_show_gray_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L986:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L987:
	ld.hu	d13,[a10]4
.L988:
	ld.bu	d14,[a10]8
.L989:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L990:
	lt.u	d4,d8,d0
.L984:
	movh.a	a4,#@his(.1.str)
.L983:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#914
.L985:
	call	debug_assert_handler
.L982:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L991:
	lt.u	d4,d9,d15
.L992:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#915
	call	debug_assert_handler
.L1389:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#916
	call	debug_assert_handler
.L613:
	mul	d4,d12,#2
	call	malloc
.L993:
	mov.aa	a13,a2
.L995:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1390:
	jne	d15,#0,.L200
.L1391:
	mov	d15,#0
	jeq	d15,#0,.L201
	mov	d4,#482
	call	get_port
.L994:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L202
.L201:
	mov	d4,#482
	call	get_port
.L996:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L202:
.L200:
	add	d15,d8,d12
.L997:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L998:
	add	d15,d9,d13
.L999:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L1000:
	mov	e4,d9,d8
.L1001:
	call	ips200_set_region
.L1392:
	mov	d8,#0
.L1002:
	j	.L203
.L204:
	mul	d15,d8,d11
.L1003:
	div.u	e0,d15,d13
.L1004:
	mul	d15,d0,d10
.L1005:
	addsc.a	a15,a12,d15,#0
.L1006:
	mov	d2,#0
.L1007:
	j	.L205
.L206:
	mul	d15,d2,d10
.L1009:
	div.u	e0,d15,d12
.L1393:
	addsc.a	a2,a15,d0,#0
.L1394:
	ld.bu	d0,[a2]
.L1010:
	jne	d14,#0,.L207
.L1395:
	sha	d15,d0,#-3
.L1396:
	and	d15,#31
.L1397:
	sha	d1,d15,#11
.L1012:
	sha	d15,d0,#-2
.L1398:
	and	d15,#63
.L1399:
	sha	d15,#5
.L1400:
	or	d1,d15
.L1401:
	sha	d0,#-3
.L1011:
	and	d15,d0,#31
.L1402:
	or	d1,d15
.L1403:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1404:
	st.h	[a2],d1
.L1405:
	j	.L208
.L207:
	jge.u	d0,d14,.L209
.L1406:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1407:
	mov	d15,#0
.L1408:
	st.h	[a2],d15
.L1409:
	j	.L210
.L209:
	mul	d15,d2,#2
	addsc.a	a2,a13,d15,#0
.L1410:
	mov.u	d15,#65535
.L1411:
	st.h	[a2],d15
.L210:
.L208:
	add	d2,#1
.L205:
	jlt.u	d2,d12,.L206
.L1412:
	mov.aa	a4,a13
.L1013:
	mov	d4,d12
.L1014:
	call	ips200_write_16bit_data_array
.L1008:
	add	d8,#1
.L203:
	jlt.u	d8,d13,.L204
.L1413:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1414:
	jne	d15,#0,.L211
.L1415:
	mov	d15,#1
	jeq	d15,#0,.L212
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L213
.L212:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L213:
.L211:
	mov.aa	a4,a13
.L1015:
	call	free
.L1016:
	ret
.L604:
	
__ips200_show_gray_image_function_end:
	.size	ips200_show_gray_image,__ips200_show_gray_image_function_end-ips200_show_gray_image
.L402:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_rgb565_image',code,cluster('ips200_show_rgb565_image')
	.sect	'.text.zf_device_ips200.ips200_show_rgb565_image'
	.align	2
	
	.global	ips200_show_rgb565_image
; Function ips200_show_rgb565_image
.L322:
ips200_show_rgb565_image:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L1021:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L1022:
	ld.hu	d13,[a10]4
.L1023:
	ld.bu	d14,[a10]8
.L1024:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L1025:
	lt.u	d4,d8,d0
.L1019:
	movh.a	a4,#@his(.1.str)
.L1018:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#980
.L1020:
	call	debug_assert_handler
.L1017:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d15,[a15]0
.L1026:
	lt.u	d4,d9,d15
.L1027:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#981
	call	debug_assert_handler
.L1420:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#982
	call	debug_assert_handler
.L630:
	mul	d4,d12,#2
	call	malloc
.L1028:
	mov.aa	a13,a2
.L1030:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1421:
	jne	d15,#0,.L214
.L1422:
	mov	d15,#0
	jeq	d15,#0,.L215
	mov	d4,#482
	call	get_port
.L1029:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L216
.L215:
	mov	d4,#482
	call	get_port
.L1031:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L216:
.L214:
	add	d15,d8,d12
.L1032:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L1033:
	add	d15,d9,d13
.L1034:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L1035:
	mov	e4,d9,d8
.L1036:
	call	ips200_set_region
.L1423:
	mov	d8,#0
.L1037:
	j	.L217
.L218:
	mul	d15,d8,d11
.L1038:
	div.u	e0,d15,d13
.L1039:
	mul	d15,d0,d10
.L1040:
	mul	d15,d15,#2
	addsc.a	a15,a12,d15,#0
.L1041:
	mov	d0,#0
.L1042:
	j	.L219
.L220:
	mul	d15,d0,#2
	addsc.a	a2,a13,d15,#0
.L1044:
	mul	d15,d0,d10
.L1045:
	div.u	e2,d15,d12
.L1424:
	mul	d15,d2,#2
	addsc.a	a4,a15,d15,#0
.L1425:
	ld.hu	d15,[a4]0
.L1426:
	st.h	[a2],d15
.L1427:
	add	d0,#1
.L219:
	jlt.u	d0,d12,.L220
.L1428:
	jeq	d14,#0,.L221
.L1429:
	mul	d4,d12,#2
	mov.aa	a4,a13
.L1046:
	call	ips200_write_8bit_data_array
.L1043:
	j	.L222
.L221:
	mov.aa	a4,a13
.L1047:
	mov	d4,d12
.L1048:
	call	ips200_write_16bit_data_array
.L222:
	add	d8,#1
.L217:
	jlt.u	d8,d13,.L218
.L1430:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1431:
	jne	d15,#0,.L223
.L1432:
	mov	d15,#1
	jeq	d15,#0,.L224
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L225
.L224:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L225:
.L223:
	mov.aa	a4,a13
.L1049:
	call	free
.L1050:
	ret
.L620:
	
__ips200_show_rgb565_image_function_end:
	.size	ips200_show_rgb565_image,__ips200_show_rgb565_image_function_end-ips200_show_rgb565_image
.L407:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_wave',code,cluster('ips200_show_wave')
	.sect	'.text.zf_device_ips200.ips200_show_wave'
	.align	2
	
	.global	ips200_show_wave
; Function ips200_show_wave
.L324:
ips200_show_wave:	.type	func
	mov	e8,d5,d4
	mov.aa	a12,a4
.L1055:
	mov	e10,d7,d6
	ld.hu	d12,[a10]0
.L1056:
	ld.hu	d13,[a10]4
.L1057:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L1058:
	lt.u	d4,d8,d0
.L1053:
	movh.a	a4,#@his(.1.str)
.L1052:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1033
.L1054:
	call	debug_assert_handler
.L1051:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L1059:
	lt.u	d4,d9,d0
.L1060:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1034
	call	debug_assert_handler
.L1437:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1035
	call	debug_assert_handler
.L643:
	mul	d4,d12,#2
	call	malloc
.L1061:
	mov.aa	a13,a2
.L1063:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1438:
	jne	d15,#0,.L226
.L1439:
	mov	d15,#0
	jeq	d15,#0,.L227
	mov	d4,#482
	call	get_port
.L1062:
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L228
.L227:
	mov	d4,#482
	call	get_port
.L1064:
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L228:
.L226:
	add	d15,d8,d12
.L1065:
	add	d15,#-1
	extr.u	d6,d15,#0,#16
.L1066:
	add	d15,d9,d13
.L1067:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L1068:
	mov	e4,d9,d8
.L1069:
	call	ips200_set_region
.L1440:
	mov	d14,#0
.L1070:
	j	.L229
.L230:
	mov	d0,#0
.L1072:
	j	.L231
.L232:
	mul	d15,d0,#2
	addsc.a	a15,a13,d15,#0
.L1441:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d15,[a2]0
.L1442:
	st.h	[a15],d15
.L1443:
	add	d0,#1
.L231:
	jlt.u	d0,d12,.L232
.L1444:
	mov.aa	a4,a13
.L1074:
	mov	d4,d12
.L1075:
	call	ips200_write_16bit_data_array
.L1073:
	add	d14,#1
.L229:
	jlt.u	d14,d13,.L230
.L1445:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1446:
	jne	d15,#0,.L233
.L1447:
	mov	d15,#1
	jeq	d15,#0,.L234
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L235
.L234:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L235:
.L233:
	mov	d14,#0
.L1071:
	j	.L236
.L237:
	mul	d15,d14,d10
.L1076:
	div.u	e0,d15,d12
.L1077:
	mul	d15,d0,#2
	addsc.a	a15,a12,d15,#0
.L1448:
	ld.hu	d0,[a15]0
.L1078:
	add	d15,d13,#-1
.L1449:
	mul	d0,d15
.L1079:
	div	e0,d0,d11
.L1080:
	add	d15,d14,d8
.L1082:
	extr.u	d4,d15,#0,#16
.L1450:
	add	d15,d13,#-1
.L1451:
	sub	d15,d0
.L1083:
	add	d15,d9
.L1084:
	extr.u	d5,d15,#0,#16
.L1452:
	movh.a	a15,#@his(ips200_pencolor)
	lea	a15,[a15]@los(ips200_pencolor)
	ld.hu	d6,[a15]0
	call	ips200_draw_point
.L1081:
	add	d14,#1
.L236:
	jlt.u	d14,d12,.L237
.L1453:
	mov.aa	a4,a13
.L1085:
	call	free
.L1086:
	ret
.L635:
	
__ips200_show_wave_function_end:
	.size	ips200_show_wave,__ips200_show_wave_function_end-ips200_show_wave
.L412:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_show_chinese',code,cluster('ips200_show_chinese')
	.sect	'.text.zf_device_ips200.ips200_show_chinese'
	.align	2
	
	.global	ips200_show_chinese
; Function ips200_show_chinese
.L326:
ips200_show_chinese:	.type	func
	sub.a	a10,#8
.L1087:
	mov	e8,d5,d4
	mov	d10,d6
.L1092:
	mov.aa	a12,a4
.L1093:
	st.w	[a10]4,d7
.L1094:
	ld.hu	d11,[a10]8
.L1095:
	movh.a	a15,#@his(ips200_width_max)
	lea	a15,[a15]@los(ips200_width_max)
	ld.hu	d0,[a15]0
.L1096:
	lt.u	d4,d8,d0
.L1090:
	movh.a	a4,#@his(.1.str)
.L1088:
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1083
.L1091:
	call	debug_assert_handler
.L1089:
	movh.a	a15,#@his(ips200_height_max)
	lea	a15,[a15]@los(ips200_height_max)
	ld.hu	d0,[a15]0
.L1097:
	lt.u	d4,d9,d0
.L1098:
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1084
	call	debug_assert_handler
.L1458:
	mov.a	a15,#0
	ne.a	d4,a15,a12
	movh.a	a4,#@his(.1.str)
	lea	a4,[a4]@los(.1.str)
	mov	d5,#1085
	call	debug_assert_handler
.L657:
	mov	d15,#8
.L1459:
	div	e12,d10,d15
.L1099:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1460:
	jne	d15,#0,.L238
.L1461:
	mov	d15,#0
	jeq	d15,#0,.L239
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L240
.L239:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L240:
.L238:
	ld.w	d15,[a10]4
.L1100:
	mul	d15,d10
.L1101:
	add	d15,#-1
.L1102:
	add	d15,d8
.L1103:
	extr.u	d6,d15,#0,#16
.L1104:
	add	d15,d9,d10
.L1105:
	add	d15,#-1
	extr.u	d7,d15,#0,#16
.L1106:
	mov	e4,d9,d8
.L1107:
	call	ips200_set_region
.L1462:
	mov	d8,#0
.L1108:
	j	.L241
.L242:
	ld.w	d15,[a10]4
.L1109:
	st.w	[a10],d15
.L1111:
	mul	d15,d8,d12
.L1110:
	addsc.a	a15,a12,d15,#0
.L1112:
	j	.L243
.L244:
	mov	d9,#0
.L1114:
	j	.L245
.L246:
	mov	d14,#8
.L1115:
	j	.L247
.L248:
	ld.bu	d0,[a15]
.L1463:
	add	d15,d14,#-1
.L1464:
	rsub	d15,#0
	sha	d0,d0,d15
.L1465:
	and	d15,d0,#1
.L1116:
	jeq	d15,#0,.L249
.L1466:
	mov	d4,d11
.L1117:
	call	ips200_write_16bit_data
.L1118:
	j	.L250
.L249:
	movh.a	a2,#@his(ips200_bgcolor)
	lea	a2,[a2]@los(ips200_bgcolor)
	ld.hu	d4,[a2]0
	call	ips200_write_16bit_data
.L250:
	add	d14,#-1
.L247:
	jge	d14,#1,.L248
.L1467:
	add.a	a15,#1
.L1468:
	add	d9,#1
.L245:
	jlt	d9,d12,.L246
.L1469:
	mul	d15,d12,d10
.L1470:
	mov.d	d0,a15
.L1119:
	sub	d0,d12
.L1120:
	add	d15,d0
.L1113:
	mov.a	a15,d15
.L243:
	ld.w	d15,[a10]
.L1121:
	add	d0,d15,#-1
	extr.u	d0,d0,#0,#8
.L1122:
	st.w	[a10],d0
.L1123:
	jne	d15,#0,.L244
.L1124:
	add	d8,#1
.L241:
	jlt	d8,d10,.L242
.L1471:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1472:
	jne	d15,#0,.L251
.L1473:
	mov	d15,#1
	jeq	d15,#0,.L252
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L253
.L252:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L253:
.L251:
	ret
.L649:
	
__ips200_show_chinese_function_end:
	.size	ips200_show_chinese,__ips200_show_chinese_function_end-ips200_show_chinese
.L417:
	; End of function
	
	.sdecl	'.text.zf_device_ips200.ips200_init',code,cluster('ips200_init')
	.sect	'.text.zf_device_ips200.ips200_init'
	.align	2
	
	.global	ips200_init
; Function ips200_init
.L328:
ips200_init:	.type	func
	sub.a	a10,#16
.L1125:
	jne	d4,#0,.L254
.L1478:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
.L1479:
	mov	d15,#0
.L1480:
	st.b	[a15],d15
.L1481:
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
.L1482:
	mov	d15,#481
.L1483:
	st.h	[a15],d15
.L1484:
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
.L1485:
	mov	d15,#484
.L1486:
	st.h	[a15],d15
.L1487:
	movh.a	a15,#@his(ips_cs_pin)
	lea	a15,[a15]@los(ips_cs_pin)
.L1488:
	mov	d15,#482
.L1489:
	st.h	[a15],d15
.L1490:
	mov	d15,#211
	st.h	[a10],d15
.L1491:
	mov	d15,#217
	st.h	[a10]4,d15
.L1492:
	mov	d15,#403
	st.h	[a10]8,d15
.L1493:
	mov	d4,#2
.L1126:
	mov	d5,#0
.L1494:
	mov.u	d6,#34560
	addih	d6,d6,#915
.L1495:
	mov	d7,#206
	call	spi_init
.L1496:
	mov	d4,#480
.L1497:
	mov	d5,#1
.L1498:
	mov	d6,#0
.L1499:
	mov	d7,#3
	call	gpio_init
.L1500:
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d4,[a15]
.L1501:
	mov	d5,#1
.L1502:
	mov	d6,#0
.L1503:
	mov	d7,#3
	call	gpio_init
.L1504:
	movh.a	a15,#@his(ips_cs_pin)
	lea	a15,[a15]@los(ips_cs_pin)
	ld.h	d4,[a15]
.L1505:
	mov	d5,#1
.L1506:
	mov	d6,#1
.L1507:
	mov	d7,#3
	call	gpio_init
.L1508:
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d4,[a15]
.L1509:
	mov	d5,#1
.L1510:
	mov	d6,#1
.L1511:
	mov	d7,#3
	call	gpio_init
.L1512:
	j	.L255
.L254:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
.L1513:
	mov	d15,#1
.L1514:
	st.b	[a15],d15
.L1515:
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
.L1516:
	mov	d15,#480
.L1517:
	st.h	[a15],d15
.L1518:
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
.L1519:
	mov	d15,#484
.L1520:
	st.h	[a15],d15
.L1521:
	movh.a	a15,#@his(ips_cs_pin)
	lea	a15,[a15]@los(ips_cs_pin)
.L1522:
	mov	d15,#482
.L1523:
	st.h	[a15],d15
.L1524:
	mov	d4,#480
.L1127:
	mov	d5,#1
.L1525:
	mov	d6,#0
.L1526:
	mov	d7,#3
	call	gpio_init
.L1527:
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d4,[a15]
.L1528:
	mov	d5,#1
.L1529:
	mov	d6,#0
.L1530:
	mov	d7,#3
	call	gpio_init
.L1531:
	movh.a	a15,#@his(ips_cs_pin)
	lea	a15,[a15]@los(ips_cs_pin)
	ld.h	d4,[a15]
.L1532:
	mov	d5,#1
.L1533:
	mov	d6,#1
.L1534:
	mov	d7,#3
	call	gpio_init
.L1535:
	mov	d4,#483
.L1536:
	mov	d5,#1
.L1537:
	mov	d6,#0
.L1538:
	mov	d7,#3
	call	gpio_init
.L1539:
	mov	d4,#485
.L1540:
	mov	d5,#1
.L1541:
	mov	d6,#0
.L1542:
	mov	d7,#3
	call	gpio_init
.L1543:
	mov	d4,#481
.L1544:
	mov	d5,#1
.L1545:
	mov	d6,#1
.L1546:
	mov	d7,#3
	call	gpio_init
.L1547:
	mov	d4,#361
.L1548:
	mov	d5,#1
.L1549:
	mov	d6,#1
.L1550:
	mov	d7,#3
	call	gpio_init
.L1551:
	mov	d4,#362
.L1552:
	mov	d5,#1
.L1553:
	mov	d6,#1
.L1554:
	mov	d7,#3
	call	gpio_init
.L1555:
	mov	d4,#363
.L1556:
	mov	d5,#1
.L1557:
	mov	d6,#1
.L1558:
	mov	d7,#3
	call	gpio_init
.L1559:
	mov	d4,#364
.L1560:
	mov	d5,#1
.L1561:
	mov	d6,#1
.L1562:
	mov	d7,#3
	call	gpio_init
.L1563:
	mov	d4,#416
.L1564:
	mov	d5,#1
.L1565:
	mov	d6,#1
.L1566:
	mov	d7,#3
	call	gpio_init
.L1567:
	mov	d4,#417
.L1568:
	mov	d5,#1
.L1569:
	mov	d6,#1
.L1570:
	mov	d7,#3
	call	gpio_init
.L1571:
	mov	d4,#418
.L1572:
	mov	d5,#1
.L1573:
	mov	d6,#1
.L1574:
	mov	d7,#3
	call	gpio_init
.L1575:
	mov	d4,#419
.L1576:
	mov	d5,#1
.L1577:
	mov	d6,#1
.L1578:
	mov	d7,#3
	call	gpio_init
.L255:
	movh.a	a15,#@his(ips200_display_dir)
	lea	a15,[a15]@los(ips200_display_dir)
	ld.bu	d4,[a15]
	call	ips200_set_dir
.L1579:
	movh.a	a15,#@his(ips200_pencolor)
	lea	a15,[a15]@los(ips200_pencolor)
	ld.hu	d4,[a15]0
.L1580:
	movh.a	a15,#@his(ips200_bgcolor)
	lea	a15,[a15]@los(ips200_bgcolor)
	ld.hu	d5,[a15]0
	call	ips200_set_color
.L1581:
	mov	d15,#1
	jeq	d15,#0,.L256
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L257
.L256:
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	movh.a	a15,#@his(ips_bl_pin)
	lea	a15,[a15]@los(ips_bl_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L257:
	mov	d15,#0
	jeq	d15,#0,.L258
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L259
.L258:
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L259:
	mov	d4,#5
	call	system_delay_ms
.L1582:
	mov	d15,#1
	jeq	d15,#0,.L260
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	mov	d0,#1
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
	j	.L261
.L260:
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d4,[a15]
	call	get_port
	add.a	a2,#4
	movh	d0,#1
	movh.a	a15,#@his(ips_rst_pin)
	lea	a15,[a15]@los(ips_rst_pin)
	ld.h	d15,[a15]
	and	d15,#31
	sha	d0,d0,d15
	st.w	[a2],d0
.L261:
	mov	d4,#120
	call	system_delay_ms
.L1583:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1584:
	jne	d15,#0,.L262
.L1585:
	mov	d15,#0
	jeq	d15,#0,.L263
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L264
.L263:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L264:
.L262:
	mov	d4,#17
	call	ips200_write_command
.L1586:
	mov	d4,#120
	call	system_delay_ms
.L1587:
	mov	d4,#54
	call	ips200_write_command
.L1588:
	movh.a	a15,#@his(ips200_display_dir)
	lea	a15,[a15]@los(ips200_display_dir)
	ld.bu	d15,[a15]
.L1589:
	mov	d0,#0
	jeq	d15,d0,.L265
.L1590:
	mov	d0,#1
	jeq	d15,d0,.L266
.L1591:
	mov	d0,#2
	jeq	d15,d0,.L267
.L1592:
	mov	d0,#3
	jeq	d15,d0,.L268
	j	.L269
.L265:
	mov	d4,#0
	call	ips200_write_8bit_data
.L1593:
	j	.L270
.L266:
	mov	d4,#192
	call	ips200_write_8bit_data
.L1594:
	j	.L271
.L267:
	mov	d4,#112
	call	ips200_write_8bit_data
.L1595:
	j	.L272
.L268:
	mov	d4,#160
	call	ips200_write_8bit_data
.L1596:
	j	.L273
.L269:
.L273:
.L272:
.L271:
.L270:
	mov	d4,#58
	call	ips200_write_command
.L1597:
	mov	d4,#5
	call	ips200_write_8bit_data
.L1598:
	mov	d4,#178
	call	ips200_write_command
.L1599:
	mov	d4,#12
	call	ips200_write_8bit_data
.L1600:
	mov	d4,#12
	call	ips200_write_8bit_data
.L1601:
	mov	d4,#0
	call	ips200_write_8bit_data
.L1602:
	mov	d4,#51
	call	ips200_write_8bit_data
.L1603:
	mov	d4,#51
	call	ips200_write_8bit_data
.L1604:
	mov	d4,#183
	call	ips200_write_command
.L1605:
	mov	d4,#53
	call	ips200_write_8bit_data
.L1606:
	mov	d4,#187
	call	ips200_write_command
.L1607:
	mov	d4,#41
	call	ips200_write_8bit_data
.L1608:
	mov	d4,#194
	call	ips200_write_command
.L1609:
	mov	d4,#1
	call	ips200_write_8bit_data
.L1610:
	mov	d4,#195
	call	ips200_write_command
.L1611:
	mov	d4,#25
	call	ips200_write_8bit_data
.L1612:
	mov	d4,#196
	call	ips200_write_command
.L1613:
	mov	d4,#32
	call	ips200_write_8bit_data
.L1614:
	mov	d4,#197
	call	ips200_write_command
.L1615:
	mov	d4,#26
	call	ips200_write_8bit_data
.L1616:
	mov	d4,#198
	call	ips200_write_command
.L1617:
	mov	d4,#31
	call	ips200_write_8bit_data
.L1618:
	mov	d4,#208
	call	ips200_write_command
.L1619:
	mov	d4,#164
	call	ips200_write_8bit_data
.L1620:
	mov	d4,#161
	call	ips200_write_8bit_data
.L1621:
	mov	d4,#224
	call	ips200_write_command
.L1622:
	mov	d4,#208
	call	ips200_write_8bit_data
.L1623:
	mov	d4,#8
	call	ips200_write_8bit_data
.L1624:
	mov	d4,#14
	call	ips200_write_8bit_data
.L1625:
	mov	d4,#9
	call	ips200_write_8bit_data
.L1626:
	mov	d4,#9
	call	ips200_write_8bit_data
.L1627:
	mov	d4,#5
	call	ips200_write_8bit_data
.L1628:
	mov	d4,#49
	call	ips200_write_8bit_data
.L1629:
	mov	d4,#51
	call	ips200_write_8bit_data
.L1630:
	mov	d4,#72
	call	ips200_write_8bit_data
.L1631:
	mov	d4,#23
	call	ips200_write_8bit_data
.L1632:
	mov	d4,#20
	call	ips200_write_8bit_data
.L1633:
	mov	d4,#21
	call	ips200_write_8bit_data
.L1634:
	mov	d4,#49
	call	ips200_write_8bit_data
.L1635:
	mov	d4,#52
	call	ips200_write_8bit_data
.L1636:
	mov	d4,#225
	call	ips200_write_command
.L1637:
	mov	d4,#208
	call	ips200_write_8bit_data
.L1638:
	mov	d4,#8
	call	ips200_write_8bit_data
.L1639:
	mov	d4,#14
	call	ips200_write_8bit_data
.L1640:
	mov	d4,#9
	call	ips200_write_8bit_data
.L1641:
	mov	d4,#9
	call	ips200_write_8bit_data
.L1642:
	mov	d4,#21
	call	ips200_write_8bit_data
.L1643:
	mov	d4,#49
	call	ips200_write_8bit_data
.L1644:
	mov	d4,#51
	call	ips200_write_8bit_data
.L1645:
	mov	d4,#72
	call	ips200_write_8bit_data
.L1646:
	mov	d4,#23
	call	ips200_write_8bit_data
.L1647:
	mov	d4,#20
	call	ips200_write_8bit_data
.L1648:
	mov	d4,#21
	call	ips200_write_8bit_data
.L1649:
	mov	d4,#49
	call	ips200_write_8bit_data
.L1650:
	mov	d4,#52
	call	ips200_write_8bit_data
.L1651:
	mov	d4,#33
	call	ips200_write_command
.L1652:
	mov	d4,#41
	call	ips200_write_command
.L1653:
	movh.a	a15,#@his(ips200_display_type)
	lea	a15,[a15]@los(ips200_display_type)
	ld.bu	d15,[a15]
.L1654:
	jne	d15,#0,.L274
.L1655:
	mov	d15,#1
	jeq	d15,#0,.L275
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	mov	d15,#4
	st.w	[a2],d15
	j	.L276
.L275:
	mov	d4,#482
	call	get_port
	add.a	a2,#4
	movh	d15,#4
	st.w	[a2],d15
.L276:
.L274:
	call	ips200_clear
.L1656:
	call	ips200_debug_init
.L1657:
	ret
.L665:
	
__ips200_init_function_end:
	.size	ips200_init,__ips200_init_function_end-ips200_init
.L422:
	; End of function
	
	.sdecl	'.data.zf_device_ips200.ips200_width_max',data,cluster('ips200_width_max')
	.sect	'.data.zf_device_ips200.ips200_width_max'
	.global	ips200_width_max
	.align	2
ips200_width_max:	.type	object
	.size	ips200_width_max,2
	.half	240
	.sdecl	'.data.zf_device_ips200.ips200_height_max',data,cluster('ips200_height_max')
	.sect	'.data.zf_device_ips200.ips200_height_max'
	.global	ips200_height_max
	.align	2
ips200_height_max:	.type	object
	.size	ips200_height_max,2
	.half	320
	.sdecl	'.data.zf_device_ips200.ips200_pencolor',data,cluster('ips200_pencolor')
	.sect	'.data.zf_device_ips200.ips200_pencolor'
	.align	2
ips200_pencolor:	.type	object
	.size	ips200_pencolor,2
	.half	63488
	.sdecl	'.data.zf_device_ips200.ips200_bgcolor',data,cluster('ips200_bgcolor')
	.sect	'.data.zf_device_ips200.ips200_bgcolor'
	.align	2
ips200_bgcolor:	.type	object
	.size	ips200_bgcolor,2
	.half	65535
	.sdecl	'.data.zf_device_ips200.ips200_display_type',data,cluster('ips200_display_type')
	.sect	'.data.zf_device_ips200.ips200_display_type'
ips200_display_type:	.type	object
	.size	ips200_display_type,1
	.space	1
	.sdecl	'.data.zf_device_ips200.ips200_display_dir',data,cluster('ips200_display_dir')
	.sect	'.data.zf_device_ips200.ips200_display_dir'
ips200_display_dir:	.type	object
	.size	ips200_display_dir,1
	.space	1
	.sdecl	'.data.zf_device_ips200.ips200_display_font',data,cluster('ips200_display_font')
	.sect	'.data.zf_device_ips200.ips200_display_font'
ips200_display_font:	.type	object
	.size	ips200_display_font,1
	.byte	1
	.sdecl	'.data.zf_device_ips200.ips_rst_pin',data,cluster('ips_rst_pin')
	.sect	'.data.zf_device_ips200.ips_rst_pin'
	.align	2
ips_rst_pin:	.type	object
	.size	ips_rst_pin,2
	.half	481
	.sdecl	'.data.zf_device_ips200.ips_bl_pin',data,cluster('ips_bl_pin')
	.sect	'.data.zf_device_ips200.ips_bl_pin'
	.align	2
ips_bl_pin:	.type	object
	.size	ips_bl_pin,2
	.half	484
	.sdecl	'.data.zf_device_ips200.ips_cs_pin',data,cluster('ips_cs_pin')
	.sect	'.data.zf_device_ips200.ips_cs_pin'
	.align	2
ips_cs_pin:	.type	object
	.size	ips_cs_pin,2
	.half	482
	.sdecl	'.rodata.zf_device_ips200..1.str',data,rom
	.sect	'.rodata.zf_device_ips200..1.str'
.1.str:	.type	object
	.size	.1.str,42
	.byte	46,46,47,108,105,98,114,97
	.byte	114,105,101,115,47,122,102,95
	.byte	100,101,118,105,99,101,47,122
	.byte	102,95,100,101,118,105,99,101
	.byte	95,105,112,115,50,48,48,46
	.byte	99
	.space	1
	.calls	'ips200_draw_line','__f_ftos'
	.calls	'ips200_show_float','__d_mul'
	.calls	'ips200_show_float','__d_dtoi'
	.calls	'ips200_show_float','__d_itod'
	.calls	'ips200_show_float','__d_sub'
	.calls	'__INDIRECT__','ips200_clear'
	.calls	'__INDIRECT__','ips200_show_string'
	.calls	'ips200_write_command','get_port'
	.calls	'ips200_write_command','spi_write_8bit'
	.calls	'ips200_write_command','ips200_write_data'
	.calls	'ips200_write_8bit_data','spi_write_8bit'
	.calls	'ips200_write_8bit_data','get_port'
	.calls	'ips200_write_8bit_data','ips200_write_data'
	.calls	'ips200_write_8bit_data_array','spi_write_8bit_array'
	.calls	'ips200_write_8bit_data_array','get_port'
	.calls	'ips200_write_8bit_data_array','ips200_write_data'
	.calls	'ips200_write_16bit_data','spi_write_16bit'
	.calls	'ips200_write_16bit_data','get_port'
	.calls	'ips200_write_16bit_data','ips200_write_data'
	.calls	'ips200_write_16bit_data_array','spi_write_16bit_array'
	.calls	'ips200_write_16bit_data_array','get_port'
	.calls	'ips200_write_16bit_data_array','ips200_write_data'
	.calls	'ips200_set_region','debug_assert_handler'
	.calls	'ips200_set_region','ips200_write_command'
	.calls	'ips200_set_region','ips200_write_16bit_data'
	.calls	'ips200_debug_init','debug_output_struct_init'
	.calls	'ips200_debug_init','debug_output_init'
	.calls	'ips200_clear','get_port'
	.calls	'ips200_clear','ips200_set_region'
	.calls	'ips200_clear','ips200_write_16bit_data_array'
	.calls	'ips200_full','get_port'
	.calls	'ips200_full','ips200_set_region'
	.calls	'ips200_full','ips200_write_16bit_data_array'
	.calls	'ips200_draw_point','debug_assert_handler'
	.calls	'ips200_draw_point','get_port'
	.calls	'ips200_draw_point','ips200_set_region'
	.calls	'ips200_draw_point','ips200_write_16bit_data'
	.calls	'ips200_draw_line','debug_assert_handler'
	.calls	'ips200_draw_line','ips200_draw_point'
	.calls	'ips200_show_char','debug_assert_handler'
	.calls	'ips200_show_char','get_port'
	.calls	'ips200_show_char','ips200_set_region'
	.calls	'ips200_show_char','ips200_write_16bit_data_array'
	.calls	'ips200_show_string','debug_assert_handler'
	.calls	'ips200_show_string','ips200_show_char'
	.calls	'ips200_show_int','debug_assert_handler'
	.calls	'ips200_show_int','memset'
	.calls	'ips200_show_int','func_int_to_str'
	.calls	'ips200_show_int','ips200_show_string'
	.calls	'ips200_show_uint','debug_assert_handler'
	.calls	'ips200_show_uint','memset'
	.calls	'ips200_show_uint','func_uint_to_str'
	.calls	'ips200_show_uint','ips200_show_string'
	.calls	'ips200_show_float','debug_assert_handler'
	.calls	'ips200_show_float','memset'
	.calls	'ips200_show_float','func_double_to_str'
	.calls	'ips200_show_float','ips200_show_string'
	.calls	'ips200_show_binary_image','debug_assert_handler'
	.calls	'ips200_show_binary_image','get_port'
	.calls	'ips200_show_binary_image','ips200_set_region'
	.calls	'ips200_show_binary_image','ips200_write_16bit_data_array'
	.calls	'ips200_show_gray_image','debug_assert_handler'
	.calls	'ips200_show_gray_image','get_port'
	.calls	'ips200_show_gray_image','ips200_set_region'
	.calls	'ips200_show_gray_image','ips200_write_16bit_data_array'
	.calls	'ips200_show_rgb565_image','debug_assert_handler'
	.calls	'ips200_show_rgb565_image','get_port'
	.calls	'ips200_show_rgb565_image','ips200_set_region'
	.calls	'ips200_show_rgb565_image','ips200_write_8bit_data_array'
	.calls	'ips200_show_rgb565_image','ips200_write_16bit_data_array'
	.calls	'ips200_show_wave','debug_assert_handler'
	.calls	'ips200_show_wave','get_port'
	.calls	'ips200_show_wave','ips200_set_region'
	.calls	'ips200_show_wave','ips200_write_16bit_data_array'
	.calls	'ips200_show_wave','ips200_draw_point'
	.calls	'ips200_show_chinese','debug_assert_handler'
	.calls	'ips200_show_chinese','get_port'
	.calls	'ips200_show_chinese','ips200_set_region'
	.calls	'ips200_show_chinese','ips200_write_16bit_data'
	.calls	'ips200_init','spi_init'
	.calls	'ips200_init','gpio_init'
	.calls	'ips200_init','ips200_set_dir'
	.calls	'ips200_init','ips200_set_color'
	.calls	'ips200_init','get_port'
	.calls	'ips200_init','system_delay_ms'
	.calls	'ips200_init','ips200_write_command'
	.calls	'ips200_init','ips200_write_8bit_data'
	.calls	'ips200_init','ips200_clear'
	.calls	'ips200_init','ips200_debug_init'
	.calls	'ips200_write_data','',0
	.calls	'ips200_write_command','',0
	.calls	'ips200_write_8bit_data','',0
	.calls	'ips200_write_8bit_data_array','',0
	.calls	'ips200_write_16bit_data','',0
	.calls	'ips200_write_16bit_data_array','',0
	.calls	'ips200_set_region','',0
	.calls	'ips200_debug_init','',24
	.calls	'ips200_clear','',0
	.calls	'ips200_full','',0
	.calls	'ips200_set_dir','',0
	.calls	'ips200_set_font','',0
	.calls	'ips200_set_color','',0
	.calls	'ips200_draw_point','',0
	.calls	'ips200_draw_line','',8
	.calls	'ips200_show_char','',256
	.calls	'ips200_show_string','',0
	.calls	'ips200_show_int','',16
	.calls	'ips200_show_uint','',16
	.calls	'ips200_show_float','',24
	.calls	'ips200_show_binary_image','',0
	.calls	'ips200_show_gray_image','',0
	.calls	'ips200_show_rgb565_image','',0
	.calls	'ips200_show_wave','',0
	.calls	'ips200_show_chinese','',8
	.extern	memset
	.extern	debug_assert_handler
	.extern	debug_output_struct_init
	.extern	debug_output_init
	.extern	ascii_font_8x16
	.extern	ascii_font_6x8
	.extern	func_int_to_str
	.extern	func_uint_to_str
	.extern	func_double_to_str
	.extern	system_delay_ms
	.extern	IfxPort_cfg_indexMap
	.extern	get_port
	.extern	gpio_init
	.extern	spi_write_8bit
	.extern	spi_write_8bit_array
	.extern	spi_write_16bit
	.extern	spi_write_16bit_array
	.extern	spi_init
	.extern	malloc
	.extern	free
	.extern	__f_ftos
	.extern	__d_mul
	.extern	__d_dtoi
	.extern	__d_itod
	.extern	__d_sub
	.extern	__INDIRECT__
	.calls	'ips200_init','',16
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L330:
	.word	42232
	.half	3
	.word	.L331
	.byte	4
.L329:
	.byte	1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L332
	.byte	2,1,1,3
	.word	204
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	207
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L522:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	252
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	264
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	344
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	318
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	350
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	350
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	318
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L559:
	.byte	7
	.byte	'int',0,4,5
.L531:
	.byte	7
	.byte	'unsigned char',0,1,8
.L486:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	498
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	814
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1385
	.byte	4,2,35,0,0,14,4
	.word	459
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1513
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1728
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1943
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	459
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	459
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2160
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2380
	.byte	4,2,35,0,0,14,24
	.word	459
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2703
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	459
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3007
	.byte	4,2,35,0,0,14,8
	.word	459
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3332
	.byte	4,2,35,0,0,14,12
	.word	459
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4038
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4324
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4471
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	436
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4640
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4812
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	476
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5161
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5335
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6000
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6348
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6472
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6556
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6736
	.byte	4,2,35,0,0,14,76
	.word	459
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6989
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7076
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	774
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1345
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1464
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1504
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1688
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1903
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2120
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2340
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1504
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2654
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2694
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2967
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3283
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3323
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3623
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3663
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3998
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4284
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3323
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4431
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4600
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4772
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4947
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5121
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5295
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5471
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5627
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5960
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6308
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3323
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6432
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6681
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6940
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6980
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7036
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7603
	.byte	4,3,35,252,1,0,16
	.word	7643
	.byte	3
	.word	8246
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8251
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	459
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8256
	.byte	6,0,19
	.byte	'__free',0,1,1,1,1,20
	.word	350
	.byte	0,16
	.word	350
	.byte	21
	.byte	'__alloc',0
	.word	8455
	.byte	1,1,1,1,20
	.word	436
	.byte	0,22
	.byte	'memset',0,5,56,17
	.word	350
	.byte	1,1,1,1,23,5,56,33
	.word	350
	.byte	23,5,56,36
	.word	452
	.byte	23,5,56,41
	.word	436
	.byte	0,7
	.byte	'char',0,1,6,3
	.word	8527
	.byte	24
	.byte	'debug_assert_handler',0,6,112,9,1,1,1,1,5
	.byte	'pass',0,6,112,47
	.word	459
	.byte	5
	.byte	'file',0,6,112,59
	.word	8535
	.byte	5
	.byte	'line',0,6,112,69
	.word	452
	.byte	0,25
	.word	8527
.L548:
	.byte	3
	.word	8609
	.byte	26,1,1,20
	.word	8614
	.byte	0,3
	.word	8619
	.byte	26,1,1,20
	.word	476
	.byte	20
	.word	476
	.byte	20
	.word	8614
	.byte	0,3
	.word	8633
.L691:
	.byte	27,6,86,9,20,13
	.byte	'type_index',0
	.word	476
	.byte	2,2,35,0,13
	.byte	'display_x_max',0
	.word	476
	.byte	2,2,35,2,13
	.byte	'display_y_max',0
	.word	476
	.byte	2,2,35,4,13
	.byte	'font_x_size',0
	.word	459
	.byte	1,2,35,6,13
	.byte	'font_y_size',0
	.word	459
	.byte	1,2,35,7,13
	.byte	'output_uart',0
	.word	8628
	.byte	4,2,35,8,13
	.byte	'output_screen',0
	.word	8652
	.byte	4,2,35,12,13
	.byte	'output_screen_clear',0
	.word	207
	.byte	4,2,35,16,0,3
	.word	8657
	.byte	24
	.byte	'debug_output_struct_init',0,6,114,9,1,1,1,1,5
	.byte	'info',0,6,114,62
	.word	8844
	.byte	0,24
	.byte	'debug_output_init',0,6,115,9,1,1,1,1,5
	.byte	'info',0,6,115,62
	.word	8844
	.byte	0,24
	.byte	'func_int_to_str',0,7,80,13,1,1,1,1,5
	.byte	'str',0,7,80,56
	.word	8535
	.byte	5
	.byte	'number',0,7,80,67
	.word	452
	.byte	0
.L571:
	.byte	7
	.byte	'unsigned long int',0,4,7,24
	.byte	'func_uint_to_str',0,7,82,13,1,1,1,1,5
	.byte	'str',0,7,82,56
	.word	8535
	.byte	5
	.byte	'number',0,7,82,68
	.word	8988
	.byte	0
.L583:
	.byte	7
	.byte	'double',0,8,4,24
	.byte	'func_double_to_str',0,7,86,13,1,1,1,1,5
	.byte	'str',0,7,86,56
	.word	8535
	.byte	5
	.byte	'number',0,7,86,68
	.word	9062
	.byte	5
	.byte	'point_bit',0,7,86,82
	.word	459
	.byte	0,24
	.byte	'system_delay_ms',0,8,46,9,1,1,1,1,5
	.byte	'time',0,8,46,45
	.word	8988
	.byte	0,28
	.word	212
	.byte	29
	.word	238
	.byte	6,0,28
	.word	273
	.byte	29
	.word	305
	.byte	6,0,28
	.word	355
	.byte	29
	.word	374
	.byte	6,0,28
	.word	390
	.byte	29
	.word	405
	.byte	29
	.word	419
	.byte	6,0,28
	.word	8359
	.byte	29
	.word	8387
	.byte	29
	.word	8401
	.byte	29
	.word	8419
	.byte	6,0
.L693:
	.byte	17,9,42,9,2,18
	.byte	'P00_0',0,0,18
	.byte	'P00_1',0,1,18
	.byte	'P00_2',0,2,18
	.byte	'P00_3',0,3,18
	.byte	'P00_4',0,4,18
	.byte	'P00_5',0,5,18
	.byte	'P00_6',0,6,18
	.byte	'P00_7',0,7,18
	.byte	'P00_8',0,8,18
	.byte	'P00_9',0,9,18
	.byte	'P00_10',0,10,18
	.byte	'P00_11',0,11,18
	.byte	'P00_12',0,12,18
	.byte	'P00_13',0,13,18
	.byte	'P00_14',0,14,18
	.byte	'P00_15',0,15,18
	.byte	'P02_0',0,192,0,18
	.byte	'P02_1',0,193,0,18
	.byte	'P02_2',0,194,0,18
	.byte	'P02_3',0,195,0,18
	.byte	'P02_4',0,196,0,18
	.byte	'P02_5',0,197,0,18
	.byte	'P02_6',0,198,0,18
	.byte	'P02_7',0,199,0,18
	.byte	'P02_8',0,200,0,18
	.byte	'P02_9',0,201,0,18
	.byte	'P02_10',0,202,0,18
	.byte	'P02_11',0,203,0,18
	.byte	'P02_12',0,204,0,18
	.byte	'P02_13',0,205,0,18
	.byte	'P02_14',0,206,0,18
	.byte	'P02_15',0,207,0,18
	.byte	'P10_0',0,192,2,18
	.byte	'P10_1',0,193,2,18
	.byte	'P10_2',0,194,2,18
	.byte	'P10_3',0,195,2,18
	.byte	'P10_4',0,196,2,18
	.byte	'P10_5',0,197,2,18
	.byte	'P10_6',0,198,2,18
	.byte	'P10_7',0,199,2,18
	.byte	'P10_8',0,200,2,18
	.byte	'P10_9',0,201,2,18
	.byte	'P10_10',0,202,2,18
	.byte	'P10_11',0,203,2,18
	.byte	'P10_12',0,204,2,18
	.byte	'P10_13',0,205,2,18
	.byte	'P10_14',0,206,2,18
	.byte	'P10_15',0,207,2,18
	.byte	'P11_0',0,224,2,18
	.byte	'P11_1',0,225,2,18
	.byte	'P11_2',0,226,2,18
	.byte	'P11_3',0,227,2,18
	.byte	'P11_4',0,228,2,18
	.byte	'P11_5',0,229,2,18
	.byte	'P11_6',0,230,2,18
	.byte	'P11_7',0,231,2,18
	.byte	'P11_8',0,232,2,18
	.byte	'P11_9',0,233,2,18
	.byte	'P11_10',0,234,2,18
	.byte	'P11_11',0,235,2,18
	.byte	'P11_12',0,236,2,18
	.byte	'P11_13',0,237,2,18
	.byte	'P11_14',0,238,2,18
	.byte	'P11_15',0,239,2,18
	.byte	'P13_0',0,160,3,18
	.byte	'P13_1',0,161,3,18
	.byte	'P13_2',0,162,3,18
	.byte	'P13_3',0,163,3,18
	.byte	'P13_4',0,164,3,18
	.byte	'P13_5',0,165,3,18
	.byte	'P13_6',0,166,3,18
	.byte	'P13_7',0,167,3,18
	.byte	'P13_8',0,168,3,18
	.byte	'P13_9',0,169,3,18
	.byte	'P13_10',0,170,3,18
	.byte	'P13_11',0,171,3,18
	.byte	'P13_12',0,172,3,18
	.byte	'P13_13',0,173,3,18
	.byte	'P13_14',0,174,3,18
	.byte	'P13_15',0,175,3,18
	.byte	'P14_0',0,192,3,18
	.byte	'P14_1',0,193,3,18
	.byte	'P14_2',0,194,3,18
	.byte	'P14_3',0,195,3,18
	.byte	'P14_4',0,196,3,18
	.byte	'P14_5',0,197,3,18
	.byte	'P14_6',0,198,3,18
	.byte	'P14_7',0,199,3,18
	.byte	'P14_8',0,200,3,18
	.byte	'P14_9',0,201,3,18
	.byte	'P14_10',0,202,3,18
	.byte	'P14_11',0,203,3,18
	.byte	'P14_12',0,204,3,18
	.byte	'P14_13',0,205,3,18
	.byte	'P14_14',0,206,3,18
	.byte	'P14_15',0,207,3,18
	.byte	'P15_0',0,224,3,18
	.byte	'P15_1',0,225,3,18
	.byte	'P15_2',0,226,3,18
	.byte	'P15_3',0,227,3,18
	.byte	'P15_4',0,228,3,18
	.byte	'P15_5',0,229,3,18
	.byte	'P15_6',0,230,3,18
	.byte	'P15_7',0,231,3,18
	.byte	'P15_8',0,232,3,18
	.byte	'P15_9',0,233,3,18
	.byte	'P15_10',0,234,3,18
	.byte	'P15_11',0,235,3,18
	.byte	'P15_12',0,236,3,18
	.byte	'P15_13',0,237,3,18
	.byte	'P15_14',0,238,3,18
	.byte	'P15_15',0,239,3,18
	.byte	'P20_0',0,128,5,18
	.byte	'P20_1',0,129,5,18
	.byte	'P20_2',0,130,5,18
	.byte	'P20_3',0,131,5,18
	.byte	'P20_4',0,132,5,18
	.byte	'P20_5',0,133,5,18
	.byte	'P20_6',0,134,5,18
	.byte	'P20_7',0,135,5,18
	.byte	'P20_8',0,136,5,18
	.byte	'P20_9',0,137,5,18
	.byte	'P20_10',0,138,5,18
	.byte	'P20_11',0,139,5,18
	.byte	'P20_12',0,140,5,18
	.byte	'P20_13',0,141,5,18
	.byte	'P20_14',0,142,5,18
	.byte	'P20_15',0,143,5,18
	.byte	'P21_0',0,160,5,18
	.byte	'P21_1',0,161,5,18
	.byte	'P21_2',0,162,5,18
	.byte	'P21_3',0,163,5,18
	.byte	'P21_4',0,164,5,18
	.byte	'P21_5',0,165,5,18
	.byte	'P21_6',0,166,5,18
	.byte	'P21_7',0,167,5,18
	.byte	'P21_8',0,168,5,18
	.byte	'P21_9',0,169,5,18
	.byte	'P21_10',0,170,5,18
	.byte	'P21_11',0,171,5,18
	.byte	'P21_12',0,172,5,18
	.byte	'P21_13',0,173,5,18
	.byte	'P21_14',0,174,5,18
	.byte	'P21_15',0,175,5,18
	.byte	'P22_0',0,192,5,18
	.byte	'P22_1',0,193,5,18
	.byte	'P22_2',0,194,5,18
	.byte	'P22_3',0,195,5,18
	.byte	'P22_4',0,196,5,18
	.byte	'P22_5',0,197,5,18
	.byte	'P22_6',0,198,5,18
	.byte	'P22_7',0,199,5,18
	.byte	'P22_8',0,200,5,18
	.byte	'P22_9',0,201,5,18
	.byte	'P22_10',0,202,5,18
	.byte	'P22_11',0,203,5,18
	.byte	'P22_12',0,204,5,18
	.byte	'P22_13',0,205,5,18
	.byte	'P22_14',0,206,5,18
	.byte	'P22_15',0,207,5,18
	.byte	'P23_0',0,224,5,18
	.byte	'P23_1',0,225,5,18
	.byte	'P23_2',0,226,5,18
	.byte	'P23_3',0,227,5,18
	.byte	'P23_4',0,228,5,18
	.byte	'P23_5',0,229,5,18
	.byte	'P23_6',0,230,5,18
	.byte	'P23_7',0,231,5,18
	.byte	'P23_8',0,232,5,18
	.byte	'P23_9',0,233,5,18
	.byte	'P23_10',0,234,5,18
	.byte	'P23_11',0,235,5,18
	.byte	'P23_12',0,236,5,18
	.byte	'P23_13',0,237,5,18
	.byte	'P23_14',0,238,5,18
	.byte	'P23_15',0,239,5,18
	.byte	'P32_0',0,128,8,18
	.byte	'P32_1',0,129,8,18
	.byte	'P32_2',0,130,8,18
	.byte	'P32_3',0,131,8,18
	.byte	'P32_4',0,132,8,18
	.byte	'P32_5',0,133,8,18
	.byte	'P32_6',0,134,8,18
	.byte	'P32_7',0,135,8,18
	.byte	'P32_8',0,136,8,18
	.byte	'P32_9',0,137,8,18
	.byte	'P32_10',0,138,8,18
	.byte	'P32_11',0,139,8,18
	.byte	'P32_12',0,140,8,18
	.byte	'P32_13',0,141,8,18
	.byte	'P32_14',0,142,8,18
	.byte	'P32_15',0,143,8,18
	.byte	'P33_0',0,160,8,18
	.byte	'P33_1',0,161,8,18
	.byte	'P33_2',0,162,8,18
	.byte	'P33_3',0,163,8,18
	.byte	'P33_4',0,164,8,18
	.byte	'P33_5',0,165,8,18
	.byte	'P33_6',0,166,8,18
	.byte	'P33_7',0,167,8,18
	.byte	'P33_8',0,168,8,18
	.byte	'P33_9',0,169,8,18
	.byte	'P33_10',0,170,8,18
	.byte	'P33_11',0,171,8,18
	.byte	'P33_12',0,172,8,18
	.byte	'P33_13',0,173,8,18
	.byte	'P33_14',0,174,8,18
	.byte	'P33_15',0,175,8,0,22
	.byte	'get_port',0,9,114,13
	.word	8251
	.byte	1,1,1,1,5
	.byte	'pin',0,9,114,56
	.word	9258
	.byte	0,17,9,91,9,1,18
	.byte	'GPI',0,0,18
	.byte	'GPO',0,1,0,17,9,103,9,1,18
	.byte	'GPI_FLOATING_IN',0,0,18
	.byte	'GPI_PULL_UP',0,1,18
	.byte	'GPI_PULL_DOWN',0,2,18
	.byte	'GPO_PUSH_PULL',0,3,18
	.byte	'GPO_OPEN_DTAIN',0,4,0,24
	.byte	'gpio_init',0,9,143,1,7,1,1,1,1,5
	.byte	'pin',0,9,143,1,40
	.word	9258
	.byte	5
	.byte	'dir',0,9,143,1,59
	.word	11232
	.byte	5
	.byte	'dat',0,9,143,1,70
	.word	459
	.byte	5
	.byte	'pinconf',0,9,143,1,90
	.word	11250
	.byte	0,17,10,42,9,1,18
	.byte	'SPI_0',0,0,18
	.byte	'SPI_1',0,1,18
	.byte	'SPI_2',0,2,18
	.byte	'SPI_3',0,3,0,25
	.word	459
	.byte	24
	.byte	'spi_write_8bit',0,10,143,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,143,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,143,1,80
	.word	11451
	.byte	0,25
	.word	459
.L591:
	.byte	3
	.word	11510
	.byte	24
	.byte	'spi_write_8bit_array',0,10,144,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,144,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,144,1,81
	.word	11515
	.byte	5
	.byte	'len',0,10,144,1,94
	.word	8988
	.byte	0,25
	.word	476
	.byte	24
	.byte	'spi_write_16bit',0,10,146,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,146,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,146,1,81
	.word	11593
	.byte	0,25
	.word	476
.L623:
	.byte	3
	.word	11653
	.byte	24
	.byte	'spi_write_16bit_array',0,10,147,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,147,1,61
	.word	11413
	.byte	5
	.byte	'data',0,10,147,1,82
	.word	11658
	.byte	5
	.byte	'len',0,10,147,1,95
	.word	8988
	.byte	0,17,10,50,9,1,18
	.byte	'SPI_MODE0',0,0,18
	.byte	'SPI_MODE1',0,1,18
	.byte	'SPI_MODE2',0,2,18
	.byte	'SPI_MODE3',0,3,0,17,10,58,9,2,18
	.byte	'SPI0_SCLK_P20_11',0,0,18
	.byte	'SPI0_SCLK_P20_13',0,1,18
	.byte	'SPI1_SCLK_P10_2',0,230,0,18
	.byte	'SPI1_SCLK_P11_6',0,231,0,18
	.byte	'SPI2_SCLK_P13_0',0,204,1,18
	.byte	'SPI2_SCLK_P13_1',0,205,1,18
	.byte	'SPI2_SCLK_P15_3',0,206,1,18
	.byte	'SPI2_SCLK_P15_6',0,207,1,18
	.byte	'SPI2_SCLK_P15_8',0,208,1,18
	.byte	'SPI3_SCLK_P02_7',0,178,2,18
	.byte	'SPI3_SCLK_P22_0',0,179,2,18
	.byte	'SPI3_SCLK_P22_1',0,180,2,18
	.byte	'SPI3_SCLK_P22_3',0,181,2,18
	.byte	'SPI3_SCLK_P33_11',0,182,2,0,17,10,69,9,2,18
	.byte	'SPI0_MOSI_P20_12',0,6,18
	.byte	'SPI0_MOSI_P20_14',0,7,18
	.byte	'SPI1_MOSI_P10_1',0,236,0,18
	.byte	'SPI1_MOSI_P10_3',0,237,0,18
	.byte	'SPI1_MOSI_P11_9',0,238,0,18
	.byte	'SPI2_MOSI_P13_3',0,210,1,18
	.byte	'SPI2_MOSI_P15_5',0,211,1,18
	.byte	'SPI2_MOSI_P15_6',0,212,1,18
	.byte	'SPI3_MOSI_P02_6',0,184,2,18
	.byte	'SPI3_MOSI_P10_6',0,185,2,18
	.byte	'SPI3_MOSI_P22_0',0,186,2,18
	.byte	'SPI3_MOSI_P22_3',0,187,2,18
	.byte	'SPI3_MOSI_P33_12',0,188,2,0,17,10,80,9,2,18
	.byte	'SPI0_MISO_P20_12',0,12,18
	.byte	'SPI1_MISO_P10_1',0,242,0,18
	.byte	'SPI1_MISO_P11_3',0,243,0,18
	.byte	'SPI2_MISO_P15_2',0,216,1,18
	.byte	'SPI2_MISO_P15_4',0,217,1,18
	.byte	'SPI2_MISO_P15_7',0,218,1,18
	.byte	'SPI2_MISO_P21_2',0,219,1,18
	.byte	'SPI2_MISO_P21_3',0,220,1,18
	.byte	'SPI3_MISO_P02_5',0,190,2,18
	.byte	'SPI3_MISO_P22_1',0,191,2,18
	.byte	'SPI3_MISO_P21_2',0,192,2,18
	.byte	'SPI3_MISO_P21_3',0,193,2,18
	.byte	'SPI3_MISO_P33_13',0,194,2,0,17,10,91,9,2,18
	.byte	'SPI0_CS0_P20_8',0,18,18
	.byte	'SPI0_CS1_P20_9',0,24,18
	.byte	'SPI0_CS2_P20_13',0,30,18
	.byte	'SPI0_CS3_P11_10',0,36,18
	.byte	'SPI0_CS4_P11_11',0,42,18
	.byte	'SPI0_CS5_P11_2',0,48,18
	.byte	'SPI0_CS6_P20_10',0,54,18
	.byte	'SPI0_CS7_P33_5',0,60,18
	.byte	'SPI0_CS8_P20_6',0,194,0,18
	.byte	'SPI0_CS9_P20_3',0,200,0,18
	.byte	'SPI0_CS13_P15_0',0,224,0,18
	.byte	'SPI1_CS0_P20_8',0,248,0,18
	.byte	'SPI1_CS1_P20_9',0,254,0,18
	.byte	'SPI1_CS2_P20_13',0,132,1,18
	.byte	'SPI1_CS3_P11_10',0,138,1,18
	.byte	'SPI1_CS4_P11_11',0,144,1,18
	.byte	'SPI1_CS5_P11_2',0,150,1,18
	.byte	'SPI1_CS6_P33_10',0,156,1,18
	.byte	'SPI1_CS7_P33_5',0,162,1,18
	.byte	'SPI1_CS8_P10_4',0,168,1,18
	.byte	'SPI1_CS9_P10_5',0,174,1,18
	.byte	'SPI2_CS0_P15_2',0,222,1,18
	.byte	'SPI2_CS1_P14_2',0,228,1,18
	.byte	'SPI2_CS2_P14_6',0,234,1,18
	.byte	'SPI2_CS3_P14_3',0,240,1,18
	.byte	'SPI2_CS5_P15_1',0,252,1,18
	.byte	'SPI2_CS6_P33_13',0,130,2,18
	.byte	'SPI2_CS7_P20_10',0,136,2,18
	.byte	'SPI2_CS8_P20_6',0,142,2,18
	.byte	'SPI2_CS9_P20_3',0,148,2,18
	.byte	'SPI3_CS0_P02_4',0,196,2,18
	.byte	'SPI3_CS1_P02_0',0,202,2,18
	.byte	'SPI3_CS1_P33_9',0,203,2,18
	.byte	'SPI3_CS2_P02_1',0,208,2,18
	.byte	'SPI3_CS2_P33_8',0,209,2,18
	.byte	'SPI3_CS3_P02_2',0,214,2,18
	.byte	'SPI3_CS4_P02_3',0,220,2,18
	.byte	'SPI3_CS5_P02_8',0,226,2,18
	.byte	'SPI3_CS6_P00_8',0,232,2,18
	.byte	'SPI3_CS7_P00_9',0,238,2,18
	.byte	'SPI3_CS7_P33_7',0,239,2,18
	.byte	'SPI3_CS8_P10_5',0,244,2,18
	.byte	'SPI3_CS11_P33_10',0,134,3,18
	.byte	'SPI3_CS12_P22_2',0,140,3,18
	.byte	'SPI3_CS13_P23_1',0,146,3,18
	.byte	'SPI_CS_NULL',0,147,3,0,24
	.byte	'spi_init',0,10,170,1,13,1,1,1,1,5
	.byte	'spi_n',0,10,170,1,61
	.word	11413
	.byte	5
	.byte	'mode',0,10,170,1,82
	.word	11737
	.byte	5
	.byte	'baud',0,10,170,1,95
	.word	8988
	.byte	5
	.byte	'sck_pin',0,10,170,1,118
	.word	11791
	.byte	5
	.byte	'mosi_pin',0,10,170,1,145,1
	.word	12064
	.byte	5
	.byte	'miso_pin',0,10,170,1,173,1
	.word	12318
	.byte	5
	.byte	'cs_pin',0,10,170,1,199,1
	.word	12572
	.byte	0
.L484:
	.byte	3
	.word	476
.L490:
	.byte	25
	.word	476
.L496:
	.byte	17,11,146,1,9,1,18
	.byte	'IPS200_PORTAIT',0,0,18
	.byte	'IPS200_PORTAIT_180',0,1,18
	.byte	'IPS200_CROSSWISE',0,2,18
	.byte	'IPS200_CROSSWISE_180',0,3,0
.L499:
	.byte	17,11,154,1,9,1,18
	.byte	'IPS200_6X8_FONT',0,0,18
	.byte	'IPS200_8X16_FONT',0,1,18
	.byte	'IPS200_16X16_FONT',0,2,0
.L502:
	.byte	25
	.word	476
.L504:
	.byte	25
	.word	476
.L509:
	.byte	25
	.word	476
.L516:
	.byte	25
	.word	476
.L519:
	.byte	7
	.byte	'short int',0,2,5
.L528:
	.byte	25
	.word	8527
.L535:
	.byte	14,96
	.word	476
	.byte	15,47,0
.L540:
	.byte	14,128,2
	.word	476
	.byte	15,127,0
.L555:
	.byte	25
	.word	452
.L562:
	.byte	14,12
	.word	8527
	.byte	15,11,0
.L567:
	.byte	25
	.word	8988
.L578:
	.byte	25
	.word	9062
.L586:
	.byte	14,17
	.word	8527
	.byte	15,16,0
.L655:
	.byte	25
	.word	476
.L666:
	.byte	17,11,140,1,9,1,18
	.byte	'IPS200_TYPE_SPI',0,0,18
	.byte	'IPS200_TYPE_PARALLEL8',0,1,0
.L671:
	.byte	25
	.word	459
.L674:
	.byte	25
	.word	459
.L680:
	.byte	25
	.word	476
	.byte	30
	.byte	'__INDIRECT__',0,12,1,1,1,1,1,31
	.byte	'__wchar_t',0,12,1,1
	.word	13725
	.byte	31
	.byte	'__size_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'__ptrdiff_t',0,12,1,1
	.word	452
	.byte	32,1,3
	.word	13939
	.byte	31
	.byte	'__codeptr',0,12,1,1
	.word	13941
	.byte	31
	.byte	'__intptr_t',0,12,1,1
	.word	452
	.byte	31
	.byte	'__uintptr_t',0,12,1,1
	.word	436
	.byte	31
	.byte	'size_t',0,13,31,25
	.word	436
	.byte	31
	.byte	'_iob_flag_t',0,13,82,25
	.word	476
	.byte	31
	.byte	'boolean',0,14,101,29
	.word	459
	.byte	31
	.byte	'uint8',0,14,105,29
	.word	459
	.byte	31
	.byte	'uint16',0,14,109,29
	.word	476
	.byte	31
	.byte	'uint32',0,14,113,29
	.word	8988
	.byte	31
	.byte	'uint64',0,14,118,29
	.word	318
	.byte	31
	.byte	'sint16',0,14,126,29
	.word	13725
	.byte	7
	.byte	'long int',0,4,5,31
	.byte	'sint32',0,14,131,1,29
	.word	14128
	.byte	7
	.byte	'long long int',0,8,5,31
	.byte	'sint64',0,14,138,1,29
	.word	14156
	.byte	31
	.byte	'float32',0,14,167,1,29
	.word	264
	.byte	31
	.byte	'pvoid',0,15,57,28
	.word	350
	.byte	31
	.byte	'Ifx_TickTime',0,15,79,28
	.word	14156
	.byte	16
	.word	344
	.byte	3
	.word	14241
	.byte	27,15,143,1,9,8,13
	.byte	'module',0
	.word	14246
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	14128
	.byte	4,2,35,4,0,31
	.byte	'IfxModule_IndexMap',0,15,147,1,3
	.word	14251
	.byte	7
	.byte	'char',0,1,6,31
	.byte	'int8',0,16,54,29
	.word	14317
	.byte	31
	.byte	'int16',0,16,55,29
	.word	13725
	.byte	31
	.byte	'int32',0,16,56,29
	.word	452
	.byte	31
	.byte	'int64',0,16,57,29
	.word	14156
	.byte	31
	.byte	'debug_output_struct',0,6,99,2
	.word	8657
	.byte	14,16
	.word	459
	.byte	15,15,0,33
	.word	14408
	.byte	34,0,25
	.word	14417
	.byte	35
	.byte	'ascii_font_8x16',0,17,61,25
	.word	14424
	.byte	1,1,14,6
	.word	459
	.byte	15,5,0,33
	.word	14455
	.byte	34,0,25
	.word	14464
	.byte	35
	.byte	'ascii_font_6x8',0,17,62,25
	.word	14471
	.byte	1,1,31
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7076
	.byte	31
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6989
	.byte	31
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3332
	.byte	31
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1385
	.byte	31
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2380
	.byte	31
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1513
	.byte	31
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2160
	.byte	31
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1728
	.byte	31
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1943
	.byte	31
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6348
	.byte	31
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6472
	.byte	31
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6556
	.byte	31
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6736
	.byte	31
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4987
	.byte	31
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5511
	.byte	31
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5161
	.byte	31
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5335
	.byte	31
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6000
	.byte	31
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	814
	.byte	31
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4324
	.byte	31
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4812
	.byte	31
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4471
	.byte	31
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4640
	.byte	31
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5667
	.byte	31
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	498
	.byte	31
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4038
	.byte	31
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3672
	.byte	31
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2703
	.byte	31
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3007
	.byte	31
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7603
	.byte	31
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7036
	.byte	31
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3623
	.byte	31
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1464
	.byte	31
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2654
	.byte	31
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1688
	.byte	31
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2340
	.byte	31
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1903
	.byte	31
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2120
	.byte	31
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6432
	.byte	31
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6681
	.byte	31
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6940
	.byte	31
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6308
	.byte	31
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5121
	.byte	31
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5627
	.byte	31
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5295
	.byte	31
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5471
	.byte	31
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1345
	.byte	31
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5960
	.byte	31
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4431
	.byte	31
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4947
	.byte	31
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4600
	.byte	31
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4772
	.byte	31
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	774
	.byte	31
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4284
	.byte	31
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3998
	.byte	31
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2967
	.byte	31
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3283
	.byte	16
	.word	7643
	.byte	31
	.byte	'Ifx_P',0,4,139,6,3
	.word	15819
	.byte	14,104
	.word	14251
	.byte	15,12,0,25
	.word	15839
	.byte	35
	.byte	'IfxPort_cfg_indexMap',0,18,115,41
	.word	15848
	.byte	1,1,17,19,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,31
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	15884
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_ACCEN0_Bits',0,20,79,3
	.word	16006
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	32,0,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1_Bits',0,20,85,3
	.word	16563
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,20,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,20,94,3
	.word	16640
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,20,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON0_Bits',0,20,111,3
	.word	16776
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,20,114,16,4,11
	.byte	'CANDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	459
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON1_Bits',0,20,126,3
	.word	17056
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,20,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON2_Bits',0,20,135,1,3
	.word	17294
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,20,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON3_Bits',0,20,150,1,3
	.word	17422
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,20,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	459
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	459
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON4_Bits',0,20,165,1,3
	.word	17665
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,20,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CCUCON5_Bits',0,20,174,1,3
	.word	17900
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,20,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6_Bits',0,20,181,1,3
	.word	18028
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,20,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7_Bits',0,20,188,1,3
	.word	18128
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,20,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	459
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_CHIPID_Bits',0,20,202,1,3
	.word	18228
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,20,205,1,16,4,11
	.byte	'PWD',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	436
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSCON_Bits',0,20,213,1,3
	.word	18436
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,20,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_DTSLIM_Bits',0,20,225,1,3
	.word	18601
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,20,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,20,235,1,3
	.word	18784
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,20,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	459
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	436
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	459
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EICR_Bits',0,20,129,2,3
	.word	18938
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,20,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR_Bits',0,20,143,2,3
	.word	19302
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,20,146,2,16,4,11
	.byte	'POL',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	476
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	459
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_EMSR_Bits',0,20,159,2,3
	.word	19513
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,20,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG_Bits',0,20,167,2,3
	.word	19765
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,20,170,2,16,4,11
	.byte	'ARI',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG_Bits',0,20,175,2,3
	.word	19883
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,20,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR13CON_Bits',0,20,185,2,3
	.word	19994
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,20,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	436
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVR33CON_Bits',0,20,195,2,3
	.word	20157
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,20,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,20,205,2,3
	.word	20320
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,20,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,20,215,2,3
	.word	20478
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,20,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	459
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	459
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	476
	.byte	10,0,2,35,2,0,31
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,20,232,2,3
	.word	20643
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,20,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	459
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	459
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	476
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,20,245,2,3
	.word	20972
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,20,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVROVMON_Bits',0,20,255,2,3
	.word	21193
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,20,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,20,142,3,3
	.word	21356
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,20,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,20,152,3,3
	.word	21628
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,20,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,20,162,3,3
	.word	21781
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,20,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,20,172,3,3
	.word	21937
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,20,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,20,181,3,3
	.word	22099
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,20,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,20,191,3,3
	.word	22242
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,20,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,20,200,3,3
	.word	22407
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,20,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,20,211,3,3
	.word	22552
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,20,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	459
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,20,222,3,3
	.word	22733
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,20,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,20,232,3,3
	.word	22907
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,20,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,20,241,3,3
	.word	23067
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,20,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,20,130,4,3
	.word	23211
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,20,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,20,139,4,3
	.word	23485
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,20,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,20,149,4,3
	.word	23624
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,20,152,4,16,4,11
	.byte	'EN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	459
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	476
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	459
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	459
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_EXTCON_Bits',0,20,163,4,3
	.word	23787
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,20,166,4,16,4,11
	.byte	'STEP',0,2
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	459
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	476
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_FDR_Bits',0,20,174,4,3
	.word	24005
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,20,177,4,16,4,11
	.byte	'FS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	8,0,2,35,3,0,31
	.byte	'Ifx_SCU_FMR_Bits',0,20,197,4,3
	.word	24168
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,20,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_ID_Bits',0,20,205,4,3
	.word	24504
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,20,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	459
	.byte	2,0,2,35,3,0,31
	.byte	'Ifx_SCU_IGCR_Bits',0,20,232,4,3
	.word	24611
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,20,235,4,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_IN_Bits',0,20,240,4,3
	.word	25063
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,20,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	459
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_IOCR_Bits',0,20,250,4,3
	.word	25162
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,20,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,20,131,5,3
	.word	25312
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,20,134,5,16,4,11
	.byte	'SEED',0,4
	.word	436
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,20,141,5,3
	.word	25461
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,20,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	436
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,20,149,5,3
	.word	25622
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,20,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	476
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_LCLCON_Bits',0,20,158,5,3
	.word	25752
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,20,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST_Bits',0,20,166,5,3
	.word	25884
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,20,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	459
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	476
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_MANID_Bits',0,20,174,5,3
	.word	25999
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,20,177,5,16,4,11
	.byte	'PS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_OMR_Bits',0,20,185,5,3
	.word	26110
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,20,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	459
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	459
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_OSCCON_Bits',0,20,209,5,3
	.word	26268
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,20,212,5,16,4,11
	.byte	'P0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_OUT_Bits',0,20,217,5,3
	.word	26680
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,20,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	476
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	6,0,2,35,3,0,31
	.byte	'Ifx_SCU_OVCCON_Bits',0,20,233,5,3
	.word	26781
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,20,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	436
	.byte	29,0,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,20,242,5,3
	.word	27048
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,20,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC_Bits',0,20,250,5,3
	.word	27184
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,20,253,5,16,4,11
	.byte	'PD0',0,1
	.word	459
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	459
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDR_Bits',0,20,132,6,3
	.word	27295
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,20,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR_Bits',0,20,146,6,3
	.word	27428
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,20,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLCON0_Bits',0,20,166,6,3
	.word	27631
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,20,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON1_Bits',0,20,177,6,3
	.word	27987
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,20,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	476
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLCON2_Bits',0,20,184,6,3
	.word	28165
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,20,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	476
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	459
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	459
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	459
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,20,204,6,3
	.word	28265
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,20,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	459
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	459
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	459
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	9,0,2,35,2,0,31
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,20,215,6,3
	.word	28635
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,20,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	436
	.byte	26,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,20,227,6,3
	.word	28821
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,20,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	436
	.byte	24,0,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,20,241,6,3
	.word	29019
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,20,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	459
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	436
	.byte	21,0,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR_Bits',0,20,251,6,3
	.word	29252
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,20,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	459
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	459
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	459
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	459
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,20,153,7,3
	.word	29404
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,20,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	459
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	459
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	459
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	459
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,20,170,7,3
	.word	29971
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,20,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	459
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	459
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	459
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	459
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	459
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	459
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	459
	.byte	1,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,20,187,7,3
	.word	30265
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,20,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	459
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	459
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	459
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	459
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	459
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	476
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	459
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	459
	.byte	4,0,2,35,3,0,31
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,20,214,7,3
	.word	30543
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,20,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	476
	.byte	14,0,2,35,2,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,20,230,7,3
	.word	31039
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,20,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	476
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	459
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	459
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	459
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON2_Bits',0,20,243,7,3
	.word	31352
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,20,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	459
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	459
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	459
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_RSTCON_Bits',0,20,129,8,3
	.word	31561
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,20,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	459
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	459
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	459
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	459
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	459
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	459
	.byte	3,0,2,35,3,0,31
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,20,155,8,3
	.word	31772
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,20,158,8,16,4,11
	.byte	'HBT',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	436
	.byte	31,0,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON_Bits',0,20,162,8,3
	.word	32204
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,20,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	459
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	459
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	459
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	459
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	459
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	459
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	459
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	459
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	459
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	459
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	459
	.byte	7,0,2,35,3,0,31
	.byte	'Ifx_SCU_STSTAT_Bits',0,20,178,8,3
	.word	32300
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,20,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	436
	.byte	30,0,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,20,186,8,3
	.word	32560
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,20,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	459
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	459
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	436
	.byte	23,0,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON_Bits',0,20,198,8,3
	.word	32685
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,20,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,20,208,8,3
	.word	32882
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,20,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,20,218,8,3
	.word	33035
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,20,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET_Bits',0,20,228,8,3
	.word	33188
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,20,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	436
	.byte	28,0,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,20,238,8,3
	.word	33341
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,20,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	33496
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33496
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33496
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33496
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,20,247,8,3
	.word	33512
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,20,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	459
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,20,134,9,3
	.word	33642
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,20,137,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,20,150,9,3
	.word	33880
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,20,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	33496
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	33496
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	33496
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	33496
	.byte	16,0,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,20,159,9,3
	.word	34103
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,20,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,20,175,9,3
	.word	34229
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,20,178,9,16,4,11
	.byte	'AE',0,1
	.word	459
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	459
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	459
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	459
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	459
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	459
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	459
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	459
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	459
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	476
	.byte	16,0,2,35,2,0,31
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,20,191,9,3
	.word	34481
	.byte	12,20,199,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16006
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN0',0,20,204,9,3
	.word	34700
	.byte	12,20,207,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16563
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ACCEN1',0,20,212,9,3
	.word	34764
	.byte	12,20,215,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16640
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ARSTDIS',0,20,220,9,3
	.word	34828
	.byte	12,20,223,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16776
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON0',0,20,228,9,3
	.word	34893
	.byte	12,20,231,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17056
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON1',0,20,236,9,3
	.word	34958
	.byte	12,20,239,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17294
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON2',0,20,244,9,3
	.word	35023
	.byte	12,20,247,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17422
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON3',0,20,252,9,3
	.word	35088
	.byte	12,20,255,9,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17665
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON4',0,20,132,10,3
	.word	35153
	.byte	12,20,135,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17900
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON5',0,20,140,10,3
	.word	35218
	.byte	12,20,143,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18028
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON6',0,20,148,10,3
	.word	35283
	.byte	12,20,151,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18128
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CCUCON7',0,20,156,10,3
	.word	35348
	.byte	12,20,159,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18228
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_CHIPID',0,20,164,10,3
	.word	35413
	.byte	12,20,167,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18436
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSCON',0,20,172,10,3
	.word	35477
	.byte	12,20,175,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18601
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSLIM',0,20,180,10,3
	.word	35541
	.byte	12,20,183,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18784
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_DTSSTAT',0,20,188,10,3
	.word	35605
	.byte	12,20,191,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18938
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EICR',0,20,196,10,3
	.word	35670
	.byte	12,20,199,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19302
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EIFR',0,20,204,10,3
	.word	35732
	.byte	12,20,207,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19513
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EMSR',0,20,212,10,3
	.word	35794
	.byte	12,20,215,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19765
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESRCFG',0,20,220,10,3
	.word	35856
	.byte	12,20,223,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19883
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ESROCFG',0,20,228,10,3
	.word	35920
	.byte	12,20,231,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19994
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR13CON',0,20,236,10,3
	.word	35985
	.byte	12,20,239,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20157
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVR33CON',0,20,244,10,3
	.word	36051
	.byte	12,20,247,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20320
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRADCSTAT',0,20,252,10,3
	.word	36117
	.byte	12,20,255,10,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20478
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRDVSTAT',0,20,132,11,3
	.word	36185
	.byte	12,20,135,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20643
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRMONCTRL',0,20,140,11,3
	.word	36252
	.byte	12,20,143,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20972
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROSCCTRL',0,20,148,11,3
	.word	36320
	.byte	12,20,151,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21193
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVROVMON',0,20,156,11,3
	.word	36388
	.byte	12,20,159,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21356
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRRSTCON',0,20,164,11,3
	.word	36454
	.byte	12,20,167,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21628
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,20,172,11,3
	.word	36521
	.byte	12,20,175,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21781
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,20,180,11,3
	.word	36590
	.byte	12,20,183,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21937
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,20,188,11,3
	.word	36659
	.byte	12,20,191,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22099
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,20,196,11,3
	.word	36728
	.byte	12,20,199,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22242
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,20,204,11,3
	.word	36797
	.byte	12,20,207,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22407
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,20,212,11,3
	.word	36866
	.byte	12,20,215,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22552
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL1',0,20,220,11,3
	.word	36935
	.byte	12,20,223,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22733
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL2',0,20,228,11,3
	.word	37003
	.byte	12,20,231,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22907
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL3',0,20,236,11,3
	.word	37071
	.byte	12,20,239,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23067
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSDCTRL4',0,20,244,11,3
	.word	37139
	.byte	12,20,247,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23211
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRSTAT',0,20,252,11,3
	.word	37207
	.byte	12,20,255,11,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23485
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRTRIM',0,20,132,12,3
	.word	37272
	.byte	12,20,135,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23624
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EVRUVMON',0,20,140,12,3
	.word	37337
	.byte	12,20,143,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23787
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_EXTCON',0,20,148,12,3
	.word	37403
	.byte	12,20,151,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24005
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FDR',0,20,156,12,3
	.word	37467
	.byte	12,20,159,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24168
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_FMR',0,20,164,12,3
	.word	37528
	.byte	12,20,167,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24504
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_ID',0,20,172,12,3
	.word	37589
	.byte	12,20,175,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24611
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IGCR',0,20,180,12,3
	.word	37649
	.byte	12,20,183,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25063
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IN',0,20,188,12,3
	.word	37711
	.byte	12,20,191,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25162
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_IOCR',0,20,196,12,3
	.word	37771
	.byte	12,20,199,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25312
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL0',0,20,204,12,3
	.word	37833
	.byte	12,20,207,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25461
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL1',0,20,212,12,3
	.word	37901
	.byte	12,20,215,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25622
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LBISTCTRL2',0,20,220,12,3
	.word	37969
	.byte	12,20,223,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25752
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLCON',0,20,228,12,3
	.word	38037
	.byte	12,20,231,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25884
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_LCLTEST',0,20,236,12,3
	.word	38101
	.byte	12,20,239,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25999
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_MANID',0,20,244,12,3
	.word	38166
	.byte	12,20,247,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26110
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OMR',0,20,252,12,3
	.word	38229
	.byte	12,20,255,12,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26268
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OSCCON',0,20,132,13,3
	.word	38290
	.byte	12,20,135,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26680
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OUT',0,20,140,13,3
	.word	38354
	.byte	12,20,143,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26781
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCCON',0,20,148,13,3
	.word	38415
	.byte	12,20,151,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27048
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_OVCENABLE',0,20,156,13,3
	.word	38479
	.byte	12,20,159,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27184
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDISC',0,20,164,13,3
	.word	38546
	.byte	12,20,167,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27295
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDR',0,20,172,13,3
	.word	38609
	.byte	12,20,175,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27428
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PDRR',0,20,180,13,3
	.word	38670
	.byte	12,20,183,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27631
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON0',0,20,188,13,3
	.word	38732
	.byte	12,20,191,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27987
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON1',0,20,196,13,3
	.word	38797
	.byte	12,20,199,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28165
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLCON2',0,20,204,13,3
	.word	38862
	.byte	12,20,207,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28265
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON0',0,20,212,13,3
	.word	38927
	.byte	12,20,215,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28635
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYCON1',0,20,220,13,3
	.word	38996
	.byte	12,20,223,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28821
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLERAYSTAT',0,20,228,13,3
	.word	39065
	.byte	12,20,231,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29019
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PLLSTAT',0,20,236,13,3
	.word	39134
	.byte	12,20,239,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29252
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMCSR',0,20,244,13,3
	.word	39199
	.byte	12,20,247,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29404
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR0',0,20,252,13,3
	.word	39262
	.byte	12,20,255,13,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29971
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR1',0,20,132,14,3
	.word	39327
	.byte	12,20,135,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30265
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWCR2',0,20,140,14,3
	.word	39392
	.byte	12,20,143,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30543
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTAT',0,20,148,14,3
	.word	39457
	.byte	12,20,151,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31039
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_PMSWSTATCLR',0,20,156,14,3
	.word	39523
	.byte	12,20,159,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31561
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON',0,20,164,14,3
	.word	39592
	.byte	12,20,167,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31352
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTCON2',0,20,172,14,3
	.word	39656
	.byte	12,20,175,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31772
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_RSTSTAT',0,20,180,14,3
	.word	39721
	.byte	12,20,183,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32204
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SAFECON',0,20,188,14,3
	.word	39786
	.byte	12,20,191,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32300
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_STSTAT',0,20,196,14,3
	.word	39851
	.byte	12,20,199,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32560
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SWRSTCON',0,20,204,14,3
	.word	39915
	.byte	12,20,207,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32685
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_SYSCON',0,20,212,14,3
	.word	39981
	.byte	12,20,215,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32882
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPCLR',0,20,220,14,3
	.word	40045
	.byte	12,20,223,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33035
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPDIS',0,20,228,14,3
	.word	40110
	.byte	12,20,231,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33188
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSET',0,20,236,14,3
	.word	40175
	.byte	12,20,239,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33341
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_TRAPSTAT',0,20,244,14,3
	.word	40240
	.byte	12,20,247,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33512
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON0',0,20,252,14,3
	.word	40306
	.byte	12,20,255,14,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33642
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_CON1',0,20,132,15,3
	.word	40375
	.byte	12,20,135,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33880
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTCPU_SR',0,20,140,15,3
	.word	40444
	.byte	12,20,143,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34103
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON0',0,20,148,15,3
	.word	40511
	.byte	12,20,151,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34229
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_CON1',0,20,156,15,3
	.word	40578
	.byte	12,20,159,15,9,4,13
	.byte	'U',0
	.word	436
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	452
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34481
	.byte	4,2,35,0,0,31
	.byte	'Ifx_SCU_WDTS_SR',0,20,164,15,3
	.word	40645
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,20,175,15,25,12,13
	.byte	'CON0',0
	.word	40306
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40375
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40444
	.byte	4,2,35,8,0,16
	.word	40710
	.byte	31
	.byte	'Ifx_SCU_WDTCPU',0,20,180,15,3
	.word	40773
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,20,183,15,25,12,13
	.byte	'CON0',0
	.word	40511
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	40578
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	40645
	.byte	4,2,35,8,0,16
	.word	40802
	.byte	31
	.byte	'Ifx_SCU_WDTS',0,20,188,15,3
	.word	40863
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,31
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	40890
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,31
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	41041
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,31
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	41285
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,31
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	41383
	.byte	31
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8256
	.byte	31
	.byte	'gpio_pin_enum',0,9,89,2
	.word	9258
	.byte	31
	.byte	'gpio_dir_enum',0,9,95,2
	.word	11232
	.byte	31
	.byte	'gpio_mode_enum',0,9,111,2
	.word	11250
	.byte	27,21,45,9,1,11
	.byte	'mode',0,1
	.word	459
	.byte	6,2,2,35,0,11
	.byte	'use_miso',0,1
	.word	459
	.byte	1,1,2,35,0,11
	.byte	'use_cs',0,1
	.word	459
	.byte	1,0,2,35,0,0,31
	.byte	'spi_config_info_struct',0,21,50,2
	.word	41915
	.byte	31
	.byte	'spi_index_enum',0,10,48,2
	.word	11413
	.byte	31
	.byte	'spi_mode_enum',0,10,56,2
	.word	11737
	.byte	31
	.byte	'spi_sck_pin_enum',0,10,67,2
	.word	11791
	.byte	31
	.byte	'spi_mosi_pin_enum',0,10,78,2
	.word	12064
	.byte	31
	.byte	'spi_miso_pin_enum',0,10,89,2
	.word	12318
	.byte	31
	.byte	'spi_cs_pin_enum',0,10,140,1,2
	.word	12572
	.byte	31
	.byte	'ips200_type_enum',0,11,144,1,2
	.word	13800
	.byte	31
	.byte	'ips200_dir_enum',0,11,152,1,2
	.word	13554
	.byte	31
	.byte	'ips200_font_size_enum',0,11,159,1,2
	.word	13641
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L331:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,3,8,54,15,39,12,63,12,60,12,0,0,20,5,0,73,19,0,0,21,46
	.byte	1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,22,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0
	.byte	0,23,5,0,58,15,59,15,57,15,73,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,25,38,0
	.byte	73,19,0,0,26,21,1,54,15,39,12,0,0,27,19,1,58,15,59,15,57,15,11,15,0,0,28,46,1,49,19,0,0,29,5,0,49,19,0
	.byte	0,30,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,31,22,0,3,8,58,15,59,15,57,15,73,19,0,0,32,21,0
	.byte	54,15,0,0,33,1,1,73,19,0,0,34,33,0,0,0,35,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L332:
	.word	.L1129-.L1128
.L1128:
	.half	3
	.word	.L1131-.L1130
.L1130:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'string.h',0,3,0,0
	.byte	'zf_common_debug.h',0,4,0,0
	.byte	'zf_common_function.h',0,4,0,0
	.byte	'zf_driver_delay.h',0,5,0,0
	.byte	'zf_driver_gpio.h',0,5,0,0
	.byte	'zf_driver_spi.h',0,5,0,0
	.byte	'..\\libraries\\zf_device\\zf_device_ips200.h',0,0,0,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'Platform_Types.h',0,6,0,0
	.byte	'ifx_types.h',0,6,0,0
	.byte	'zf_common_typedef.h',0,4,0,0
	.byte	'zf_common_font.h',0,4,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxPort_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'zf_driver_soft_spi.h',0,5,0,0,0
.L1131:
.L1129:
	.sdecl	'.debug_info',debug,cluster('ips200_clear')
	.sect	'.debug_info'
.L333:
	.word	310
	.half	3
	.word	.L334
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L336,.L335
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_clear',0,1,136,3,6,1,1,1
	.word	.L294,.L483,.L293
	.byte	4
	.word	.L294,.L483
	.byte	5
	.byte	'color_buffer',0,1,138,3,12
	.word	.L484,.L485
	.byte	5
	.byte	'i',0,1,139,3,12
	.word	.L486,.L487
	.byte	5
	.byte	'j',0,1,139,3,19
	.word	.L486,.L488
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_clear')
	.sect	'.debug_abbrev'
.L334:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_clear')
	.sect	'.debug_line'
.L335:
	.word	.L1133-.L1132
.L1132:
	.half	3
	.word	.L1135-.L1134
.L1134:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1135:
	.byte	5,25,7,0,5,2
	.word	.L294
	.byte	3,137,3,1,5,41,9
	.half	.L1136-.L294
	.byte	1,5,27,9
	.half	.L757-.L1136
	.byte	3,3,1,5,5,9
	.half	.L1137-.L757
	.byte	1,5,9,7,9
	.half	.L1138-.L1137
	.byte	3,2,1,5,23,9
	.half	.L89-.L1138
	.byte	3,2,1,5,26,9
	.half	.L1139-.L89
	.byte	1,5,29,9
	.half	.L1140-.L1139
	.byte	1,5,46,9
	.half	.L1141-.L1140
	.byte	1,5,51,9
	.half	.L1142-.L1141
	.byte	1,5,69,9
	.half	.L1143-.L1142
	.byte	1,5,11,9
	.half	.L1144-.L1143
	.byte	3,1,1,5,36,9
	.half	.L759-.L1144
	.byte	1,5,21,9
	.half	.L93-.L759
	.byte	3,2,1,5,27,9
	.half	.L1145-.L93
	.byte	1,5,25,9
	.half	.L1146-.L1145
	.byte	1,5,40,9
	.half	.L1147-.L1146
	.byte	3,126,1,5,20,9
	.half	.L92-.L1147
	.byte	1,5,36,9
	.half	.L1148-.L92
	.byte	1,5,12,7,9
	.half	.L1149-.L1148
	.byte	3,4,1,5,38,9
	.half	.L761-.L1149
	.byte	1,5,53,9
	.half	.L95-.L761
	.byte	3,2,1,5,42,9
	.half	.L764-.L95
	.byte	3,126,1,5,21,9
	.half	.L94-.L764
	.byte	1,5,38,9
	.half	.L1150-.L94
	.byte	1,5,27,7,9
	.half	.L1151-.L1150
	.byte	3,4,1,5,5,9
	.half	.L765-.L1151
	.byte	1,5,9,7,9
	.half	.L1152-.L765
	.byte	3,2,1,5,1,9
	.half	.L96-.L1152
	.byte	3,2,1,9
	.half	.L337-.L96
	.byte	0,1,1
.L1133:
	.sdecl	'.debug_ranges',debug,cluster('ips200_clear')
	.sect	'.debug_ranges'
.L336:
	.word	-1,.L294,0,.L337-.L294,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_full')
	.sect	'.debug_info'
.L338:
	.word	328
	.half	3
	.word	.L339
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L341,.L340
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_full',0,1,167,3,6,1,1,1
	.word	.L296,.L489,.L295
	.byte	4
	.byte	'color',0,1,167,3,32
	.word	.L490,.L491
	.byte	5
	.word	.L296,.L489
	.byte	6
	.byte	'color_buffer',0,1,169,3,12
	.word	.L484,.L492
	.byte	6
	.byte	'i',0,1,170,3,12
	.word	.L486,.L493
	.byte	6
	.byte	'j',0,1,170,3,19
	.word	.L486,.L494
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_full')
	.sect	'.debug_abbrev'
.L339:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_full')
	.sect	'.debug_line'
.L340:
	.word	.L1154-.L1153
.L1153:
	.half	3
	.word	.L1156-.L1155
.L1155:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1156:
	.byte	5,6,7,0,5,2
	.word	.L296
	.byte	3,166,3,1,5,25,9
	.half	.L769-.L296
	.byte	3,2,1,5,41,9
	.half	.L1157-.L769
	.byte	1,5,27,9
	.half	.L772-.L1157
	.byte	3,3,1,5,5,9
	.half	.L1158-.L772
	.byte	1,5,9,7,9
	.half	.L1159-.L1158
	.byte	3,2,1,5,23,9
	.half	.L99-.L1159
	.byte	3,2,1,5,26,9
	.half	.L1160-.L99
	.byte	1,5,29,9
	.half	.L1161-.L1160
	.byte	1,5,46,9
	.half	.L1162-.L1161
	.byte	1,5,51,9
	.half	.L1163-.L1162
	.byte	1,5,69,9
	.half	.L1164-.L1163
	.byte	1,5,11,9
	.half	.L1165-.L1164
	.byte	3,1,1,5,36,9
	.half	.L774-.L1165
	.byte	1,5,21,9
	.half	.L103-.L774
	.byte	3,2,1,5,25,9
	.half	.L1166-.L103
	.byte	1,5,40,9
	.half	.L1167-.L1166
	.byte	3,126,1,5,20,9
	.half	.L102-.L1167
	.byte	1,5,36,9
	.half	.L1168-.L102
	.byte	1,5,12,7,9
	.half	.L1169-.L1168
	.byte	3,4,1,5,38,9
	.half	.L776-.L1169
	.byte	1,5,53,9
	.half	.L105-.L776
	.byte	3,2,1,5,42,9
	.half	.L779-.L105
	.byte	3,126,1,5,21,9
	.half	.L104-.L779
	.byte	1,5,38,9
	.half	.L1170-.L104
	.byte	1,5,27,7,9
	.half	.L1171-.L1170
	.byte	3,4,1,5,5,9
	.half	.L780-.L1171
	.byte	1,5,9,7,9
	.half	.L1172-.L780
	.byte	3,2,1,5,1,9
	.half	.L106-.L1172
	.byte	3,2,1,9
	.half	.L342-.L106
	.byte	0,1,1
.L1154:
	.sdecl	'.debug_ranges',debug,cluster('ips200_full')
	.sect	'.debug_ranges'
.L341:
	.word	-1,.L296,0,.L342-.L296,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_set_dir')
	.sect	'.debug_info'
.L343:
	.word	272
	.half	3
	.word	.L344
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L346,.L345
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_set_dir',0,1,198,3,6,1,1,1
	.word	.L298,.L495,.L297
	.byte	4
	.byte	'dir',0,1,198,3,38
	.word	.L496,.L497
	.byte	5
	.word	.L298,.L495
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_set_dir')
	.sect	'.debug_abbrev'
.L344:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_set_dir')
	.sect	'.debug_line'
.L345:
	.word	.L1174-.L1173
.L1173:
	.half	3
	.word	.L1176-.L1175
.L1175:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1176:
	.byte	5,5,7,0,5,2
	.word	.L298
	.byte	3,199,3,1,5,24,9
	.half	.L1177-.L298
	.byte	1,5,14,9
	.half	.L1178-.L1177
	.byte	3,3,1,9
	.half	.L1179-.L1178
	.byte	3,1,1,9
	.half	.L1180-.L1179
	.byte	3,5,1,9
	.half	.L1181-.L1180
	.byte	3,1,1,5,13,9
	.half	.L110-.L1181
	.byte	3,124,1,5,32,9
	.half	.L1182-.L110
	.byte	1,5,30,9
	.half	.L1183-.L1182
	.byte	1,5,13,9
	.half	.L1184-.L1183
	.byte	3,1,1,5,33,9
	.half	.L1185-.L1184
	.byte	1,5,31,9
	.half	.L1186-.L1185
	.byte	1,5,10,9
	.half	.L1187-.L1186
	.byte	3,1,1,5,13,9
	.half	.L112-.L1187
	.byte	3,4,1,5,32,9
	.half	.L1188-.L112
	.byte	1,5,30,9
	.half	.L1189-.L1188
	.byte	1,5,13,9
	.half	.L1190-.L1189
	.byte	3,1,1,5,33,9
	.half	.L1191-.L1190
	.byte	1,5,31,9
	.half	.L1192-.L1191
	.byte	1,5,10,9
	.half	.L1193-.L1192
	.byte	3,1,1,5,1,9
	.half	.L114-.L1193
	.byte	3,2,1,7,9
	.half	.L347-.L114
	.byte	0,1,1
.L1174:
	.sdecl	'.debug_ranges',debug,cluster('ips200_set_dir')
	.sect	'.debug_ranges'
.L346:
	.word	-1,.L298,0,.L347-.L298,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_set_font')
	.sect	'.debug_info'
.L348:
	.word	274
	.half	3
	.word	.L349
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L351,.L350
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_set_font',0,1,225,3,6,1,1,1
	.word	.L300,.L498,.L299
	.byte	4
	.byte	'font',0,1,225,3,45
	.word	.L499,.L500
	.byte	5
	.word	.L300,.L498
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_set_font')
	.sect	'.debug_abbrev'
.L349:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_set_font')
	.sect	'.debug_line'
.L350:
	.word	.L1195-.L1194
.L1194:
	.half	3
	.word	.L1197-.L1196
.L1196:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1197:
	.byte	5,5,7,0,5,2
	.word	.L300
	.byte	3,226,3,1,5,25,9
	.half	.L1198-.L300
	.byte	1,5,1,9
	.half	.L1199-.L1198
	.byte	3,1,1,7,9
	.half	.L352-.L1199
	.byte	0,1,1
.L1195:
	.sdecl	'.debug_ranges',debug,cluster('ips200_set_font')
	.sect	'.debug_ranges'
.L351:
	.word	-1,.L300,0,.L352-.L300,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_set_color')
	.sect	'.debug_info'
.L353:
	.word	295
	.half	3
	.word	.L354
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L356,.L355
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_set_color',0,1,238,3,6,1,1,1
	.word	.L302,.L501,.L301
	.byte	4
	.byte	'pen',0,1,238,3,37
	.word	.L502,.L503
	.byte	4
	.byte	'bgcolor',0,1,238,3,55
	.word	.L504,.L505
	.byte	5
	.word	.L302,.L501
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_set_color')
	.sect	'.debug_abbrev'
.L354:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_set_color')
	.sect	'.debug_line'
.L355:
	.word	.L1201-.L1200
.L1200:
	.half	3
	.word	.L1203-.L1202
.L1202:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1203:
	.byte	5,5,7,0,5,2
	.word	.L302
	.byte	3,239,3,1,5,21,9
	.half	.L1204-.L302
	.byte	1,5,5,9
	.half	.L1205-.L1204
	.byte	3,1,1,5,20,9
	.half	.L1206-.L1205
	.byte	1,5,1,9
	.half	.L1207-.L1206
	.byte	3,1,1,7,9
	.half	.L357-.L1207
	.byte	0,1,1
.L1201:
	.sdecl	'.debug_ranges',debug,cluster('ips200_set_color')
	.sect	'.debug_ranges'
.L356:
	.word	-1,.L302,0,.L357-.L302,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_draw_point')
	.sect	'.debug_info'
.L358:
	.word	307
	.half	3
	.word	.L359
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L361,.L360
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_draw_point',0,1,253,3,6,1,1,1
	.word	.L304,.L506,.L303
	.byte	4
	.byte	'x',0,1,253,3,32
	.word	.L486,.L507
	.byte	4
	.byte	'y',0,1,253,3,42
	.word	.L486,.L508
	.byte	4
	.byte	'color',0,1,253,3,58
	.word	.L509,.L510
	.byte	5
	.word	.L304,.L506
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_draw_point')
	.sect	'.debug_abbrev'
.L359:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_draw_point')
	.sect	'.debug_line'
.L360:
	.word	.L1209-.L1208
.L1208:
	.half	3
	.word	.L1211-.L1210
.L1210:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1211:
	.byte	5,6,7,0,5,2
	.word	.L304
	.byte	3,252,3,1,5,5,9
	.half	.L786-.L304
	.byte	3,4,1,9
	.half	.L783-.L786
	.byte	3,1,1,5,27,9
	.half	.L1212-.L783
	.byte	3,2,1,5,5,9
	.half	.L1213-.L1212
	.byte	1,5,9,7,9
	.half	.L1214-.L1213
	.byte	3,2,1,5,32,9
	.half	.L116-.L1214
	.byte	3,2,1,5,29,9
	.half	.L1215-.L116
	.byte	3,1,1,5,27,9
	.half	.L792-.L1215
	.byte	3,1,1,5,5,9
	.half	.L1216-.L792
	.byte	1,5,9,7,9
	.half	.L1217-.L1216
	.byte	3,2,1,5,1,9
	.half	.L119-.L1217
	.byte	3,2,1,7,9
	.half	.L362-.L119
	.byte	0,1,1
.L1209:
	.sdecl	'.debug_ranges',debug,cluster('ips200_draw_point')
	.sect	'.debug_ranges'
.L361:
	.word	-1,.L304,0,.L362-.L304,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_draw_line')
	.sect	'.debug_info'
.L363:
	.word	448
	.half	3
	.word	.L364
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L366,.L365
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_draw_line',0,1,155,4,6,1,1,1
	.word	.L306,.L511,.L305
	.byte	4
	.byte	'x_start',0,1,155,4,31
	.word	.L486,.L512
	.byte	4
	.byte	'y_start',0,1,155,4,47
	.word	.L486,.L513
	.byte	4
	.byte	'x_end',0,1,155,4,63
	.word	.L486,.L514
	.byte	4
	.byte	'y_end',0,1,155,4,77
	.word	.L486,.L515
	.byte	4
	.byte	'color',0,1,155,4,97
	.word	.L516,.L517
	.byte	5
	.word	.L306,.L511
	.byte	5
	.word	.L518,.L511
	.byte	6
	.byte	'x_dir',0,1,164,4,11
	.word	.L519,.L520
	.byte	6
	.byte	'y_dir',0,1,165,4,11
	.word	.L519,.L521
	.byte	6
	.byte	'temp_rate',0,1,166,4,11
	.word	.L522,.L523
	.byte	6
	.byte	'temp_b',0,1,167,4,11
	.word	.L522,.L524
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_draw_line')
	.sect	'.debug_abbrev'
.L364:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_draw_line')
	.sect	'.debug_line'
.L365:
	.word	.L1219-.L1218
.L1218:
	.half	3
	.word	.L1221-.L1220
.L1220:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1221:
	.byte	5,6,7,0,5,2
	.word	.L306
	.byte	3,154,4,1,5,5,9
	.half	.L800-.L306
	.byte	3,4,1,9
	.half	.L794-.L800
	.byte	3,1,1,9
	.half	.L1222-.L794
	.byte	3,1,1,9
	.half	.L1223-.L1222
	.byte	3,1,1,5,20,9
	.half	.L518-.L1223
	.byte	3,2,1,5,36,7,9
	.half	.L805-.L518
	.byte	1,5,40,9
	.half	.L1224-.L805
	.byte	1,5,36,9
	.half	.L122-.L1224
	.byte	1,5,20,9
	.half	.L123-.L122
	.byte	3,1,1,5,36,7,9
	.half	.L806-.L123
	.byte	1,5,40,9
	.half	.L807-.L806
	.byte	1,5,36,9
	.half	.L124-.L807
	.byte	1,5,9,9
	.half	.L126-.L124
	.byte	3,6,1,5,41,7,9
	.half	.L808-.L126
	.byte	3,2,1,5,25,9
	.half	.L809-.L808
	.byte	1,5,68,9
	.half	.L810-.L809
	.byte	1,5,52,9
	.half	.L811-.L810
	.byte	1,5,50,9
	.half	.L1225-.L811
	.byte	1,5,22,9
	.half	.L812-.L1225
	.byte	3,1,1,5,39,9
	.half	.L813-.L812
	.byte	1,5,37,9
	.half	.L1226-.L813
	.byte	1,5,76,9
	.half	.L814-.L1226
	.byte	3,127,1,5,35,9
	.half	.L127-.L814
	.byte	3,5,1,5,53,9
	.half	.L130-.L127
	.byte	3,2,1,5,25,9
	.half	.L817-.L130
	.byte	3,1,1,5,35,9
	.half	.L129-.L817
	.byte	3,125,1,5,49,7,9
	.half	.L818-.L129
	.byte	3,5,1,5,13,9
	.half	.L821-.L818
	.byte	3,1,1,5,12,9
	.half	.L128-.L821
	.byte	3,2,1,5,40,9
	.half	.L133-.L128
	.byte	1,5,9,9
	.half	.L135-.L133
	.byte	1,5,35,7,9
	.half	.L1227-.L135
	.byte	3,2,1,5,53,9
	.half	.L138-.L1227
	.byte	3,2,1,5,25,9
	.half	.L832-.L138
	.byte	3,1,1,5,36,9
	.half	.L833-.L832
	.byte	3,1,1,5,51,9
	.half	.L798-.L833
	.byte	1,5,61,9
	.half	.L1228-.L798
	.byte	1,5,27,9
	.half	.L1229-.L1228
	.byte	1,5,35,9
	.half	.L137-.L1229
	.byte	3,124,1,5,49,7,9
	.half	.L834-.L137
	.byte	3,6,1,5,55,9
	.half	.L837-.L834
	.byte	1,5,35,9
	.half	.L136-.L837
	.byte	3,4,1,5,53,9
	.half	.L141-.L136
	.byte	3,2,1,5,25,9
	.half	.L840-.L141
	.byte	3,1,1,5,35,9
	.half	.L843-.L840
	.byte	3,1,1,5,62,9
	.half	.L842-.L843
	.byte	1,5,27,9
	.half	.L1230-.L842
	.byte	1,5,35,9
	.half	.L140-.L1230
	.byte	3,124,1,5,49,7,9
	.half	.L845-.L140
	.byte	3,6,1,5,1,9
	.half	.L131-.L845
	.byte	3,3,1,7,9
	.half	.L367-.L131
	.byte	0,1,1
.L1219:
	.sdecl	'.debug_ranges',debug,cluster('ips200_draw_line')
	.sect	'.debug_ranges'
.L366:
	.word	-1,.L306,0,.L367-.L306,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_char')
	.sect	'.debug_info'
.L368:
	.word	510
	.half	3
	.word	.L369
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L371,.L370
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_char',0,1,218,4,6,1,1,1
	.word	.L308,.L525,.L307
	.byte	4
	.byte	'x',0,1,218,4,31
	.word	.L486,.L526
	.byte	4
	.byte	'y',0,1,218,4,41
	.word	.L486,.L527
	.byte	4
	.byte	'dat',0,1,218,4,55
	.word	.L528,.L529
	.byte	5
	.word	.L308,.L525
	.byte	5
	.word	.L530,.L525
	.byte	6
	.byte	'i',0,1,225,4,11
	.word	.L531,.L532
	.byte	6
	.byte	'j',0,1,225,4,18
	.word	.L531,.L533
	.byte	5
	.word	.L145,.L534
	.byte	6
	.byte	'display_buffer',0,1,235,4,20
	.word	.L535,.L536
	.byte	5
	.word	.L150,.L537
	.byte	6
	.byte	'temp_top',0,1,240,4,23
	.word	.L531,.L538
	.byte	0,0,5
	.word	.L146,.L539
	.byte	6
	.byte	'display_buffer',0,1,130,5,20
	.word	.L540,.L541
	.byte	5
	.word	.L157,.L542
	.byte	6
	.byte	'temp_top',0,1,134,5,23
	.word	.L531,.L543
	.byte	6
	.byte	'temp_bottom',0,1,135,5,23
	.word	.L531,.L544
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_char')
	.sect	'.debug_abbrev'
.L369:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_char')
	.sect	'.debug_line'
.L370:
	.word	.L1232-.L1231
.L1231:
	.half	3
	.word	.L1234-.L1233
.L1233:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1234:
	.byte	5,6,7,0,5,2
	.word	.L308
	.byte	3,217,4,1,5,5,9
	.half	.L851-.L308
	.byte	3,4,1,9
	.half	.L848-.L851
	.byte	3,1,1,5,27,9
	.half	.L530-.L848
	.byte	3,4,1,5,5,9
	.half	.L1235-.L530
	.byte	1,5,9,7,9
	.half	.L1236-.L1235
	.byte	3,2,1,5,12,9
	.half	.L142-.L1236
	.byte	3,2,1,5,14,9
	.half	.L1237-.L142
	.byte	3,2,1,9
	.half	.L1238-.L1237
	.byte	3,23,1,9
	.half	.L1239-.L1238
	.byte	3,35,1,5,39,9
	.half	.L145-.L1239
	.byte	3,73,1,5,46,9
	.half	.L856-.L145
	.byte	1,5,19,9
	.half	.L1240-.L856
	.byte	3,1,1,5,29,9
	.half	.L860-.L1240
	.byte	1,5,53,9
	.half	.L150-.L860
	.byte	3,3,1,5,48,9
	.half	.L1241-.L150
	.byte	1,5,34,9
	.half	.L1242-.L1241
	.byte	1,5,48,9
	.half	.L1243-.L1242
	.byte	1,5,58,9
	.half	.L1244-.L1243
	.byte	1,5,23,9
	.half	.L861-.L1244
	.byte	3,1,1,5,33,9
	.half	.L862-.L861
	.byte	1,5,21,9
	.half	.L152-.L862
	.byte	3,2,1,5,48,7,9
	.half	.L1245-.L152
	.byte	3,2,1,5,42,9
	.half	.L1246-.L1245
	.byte	1,5,39,9
	.half	.L1247-.L1246
	.byte	1,5,54,9
	.half	.L1248-.L1247
	.byte	1,5,51,9
	.half	.L1249-.L1248
	.byte	1,5,70,9
	.half	.L1250-.L1249
	.byte	1,5,48,9
	.half	.L153-.L1250
	.byte	3,4,1,5,42,9
	.half	.L1251-.L153
	.byte	1,5,39,9
	.half	.L1252-.L1251
	.byte	1,5,54,9
	.half	.L1253-.L1252
	.byte	1,5,51,9
	.half	.L1254-.L1253
	.byte	1,5,30,9
	.half	.L154-.L1254
	.byte	3,2,1,5,37,9
	.half	.L1255-.L154
	.byte	3,118,1,5,33,9
	.half	.L151-.L1255
	.byte	1,7,9
	.half	.L537-.L151
	.byte	3,124,1,5,29,9
	.half	.L149-.L537
	.byte	1,5,43,7,9
	.half	.L1256-.L149
	.byte	3,17,1,5,60,9
	.half	.L1257-.L1256
	.byte	1,5,10,9
	.half	.L534-.L1257
	.byte	3,1,1,5,39,9
	.half	.L146-.L534
	.byte	3,4,1,5,46,9
	.half	.L864-.L146
	.byte	1,5,19,9
	.half	.L1258-.L864
	.byte	3,1,1,5,29,9
	.half	.L868-.L1258
	.byte	1,5,54,9
	.half	.L157-.L868
	.byte	3,2,1,5,49,9
	.half	.L1259-.L157
	.byte	1,5,34,9
	.half	.L1260-.L1259
	.byte	1,5,49,9
	.half	.L1261-.L1260
	.byte	1,5,59,9
	.half	.L1262-.L1261
	.byte	1,5,57,9
	.half	.L869-.L1262
	.byte	3,1,1,5,52,9
	.half	.L1263-.L869
	.byte	1,5,37,9
	.half	.L1264-.L1263
	.byte	1,5,52,9
	.half	.L1265-.L1264
	.byte	1,5,62,9
	.half	.L1266-.L1265
	.byte	1,5,23,9
	.half	.L871-.L1266
	.byte	3,1,1,5,33,9
	.half	.L872-.L871
	.byte	1,5,21,9
	.half	.L159-.L872
	.byte	3,2,1,5,48,7,9
	.half	.L1267-.L159
	.byte	3,2,1,5,42,9
	.half	.L1268-.L1267
	.byte	1,5,39,9
	.half	.L1269-.L1268
	.byte	1,5,54,9
	.half	.L1270-.L1269
	.byte	1,5,51,9
	.half	.L1271-.L1270
	.byte	1,5,70,9
	.half	.L1272-.L1271
	.byte	1,5,48,9
	.half	.L160-.L1272
	.byte	3,4,1,5,42,9
	.half	.L1273-.L160
	.byte	1,5,39,9
	.half	.L1274-.L1273
	.byte	1,5,54,9
	.half	.L1275-.L1274
	.byte	1,5,51,9
	.half	.L1276-.L1275
	.byte	1,5,30,9
	.half	.L161-.L1276
	.byte	3,2,1,5,37,9
	.half	.L1277-.L161
	.byte	3,118,1,5,33,9
	.half	.L158-.L1277
	.byte	1,5,23,7,9
	.half	.L1278-.L158
	.byte	3,12,1,5,33,9
	.half	.L870-.L1278
	.byte	1,5,21,9
	.half	.L163-.L870
	.byte	3,2,1,5,48,7,9
	.half	.L1279-.L163
	.byte	3,2,1,5,42,9
	.half	.L1280-.L1279
	.byte	1,5,50,9
	.half	.L1281-.L1280
	.byte	1,5,39,9
	.half	.L1282-.L1281
	.byte	1,5,63,9
	.half	.L1283-.L1282
	.byte	1,5,60,9
	.half	.L1284-.L1283
	.byte	1,5,79,9
	.half	.L1285-.L1284
	.byte	1,5,48,9
	.half	.L164-.L1285
	.byte	3,4,1,5,42,9
	.half	.L1286-.L164
	.byte	1,5,50,9
	.half	.L1287-.L1286
	.byte	1,5,39,9
	.half	.L1288-.L1287
	.byte	1,5,63,9
	.half	.L1289-.L1288
	.byte	1,5,60,9
	.half	.L1290-.L1289
	.byte	1,5,33,9
	.half	.L165-.L1290
	.byte	3,2,1,5,37,9
	.half	.L1291-.L165
	.byte	3,118,1,5,33,9
	.half	.L162-.L1291
	.byte	1,7,9
	.half	.L542-.L162
	.byte	3,112,1,5,29,9
	.half	.L156-.L542
	.byte	1,5,43,7,9
	.half	.L1292-.L156
	.byte	3,29,1,5,61,9
	.half	.L1293-.L1292
	.byte	1,5,10,9
	.half	.L539-.L1293
	.byte	3,1,1,9
	.half	.L147-.L539
	.byte	3,4,1,5,27,9
	.half	.L155-.L147
	.byte	3,2,1,5,5,9
	.half	.L1294-.L155
	.byte	1,5,9,7,9
	.half	.L1295-.L1294
	.byte	3,2,1,5,1,9
	.half	.L168-.L1295
	.byte	3,2,1,7,9
	.half	.L372-.L168
	.byte	0,1,1
.L1232:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_char')
	.sect	'.debug_ranges'
.L371:
	.word	-1,.L308,0,.L372-.L308,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_string')
	.sect	'.debug_info'
.L373:
	.word	332
	.half	3
	.word	.L374
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L376,.L375
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_string',0,1,183,5,6,1,1,1
	.word	.L310,.L545,.L309
	.byte	4
	.byte	'x',0,1,183,5,33
	.word	.L486,.L546
	.byte	4
	.byte	'y',0,1,183,5,43
	.word	.L486,.L547
	.byte	4
	.byte	'dat',0,1,183,5,57
	.word	.L548,.L549
	.byte	5
	.word	.L310,.L545
	.byte	5
	.word	.L550,.L545
	.byte	6
	.byte	'j',0,1,190,5,12
	.word	.L486,.L551
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_string')
	.sect	'.debug_abbrev'
.L374:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_string')
	.sect	'.debug_line'
.L375:
	.word	.L1297-.L1296
.L1296:
	.half	3
	.word	.L1299-.L1298
.L1298:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1299:
	.byte	5,6,7,0,5,2
	.word	.L310
	.byte	3,182,5,1,5,5,9
	.half	.L876-.L310
	.byte	3,4,1,9
	.half	.L1300-.L876
	.byte	3,1,1,5,14,9
	.half	.L550-.L1300
	.byte	3,2,1,5,25,9
	.half	.L880-.L550
	.byte	3,1,1,5,16,9
	.half	.L172-.L880
	.byte	3,2,1,5,18,9
	.half	.L1301-.L172
	.byte	3,2,1,9
	.half	.L1302-.L1301
	.byte	3,1,1,9
	.half	.L1303-.L1302
	.byte	3,1,1,5,58,9
	.half	.L173-.L1303
	.byte	3,126,1,5,56,9
	.half	.L882-.L173
	.byte	1,5,71,9
	.half	.L1304-.L882
	.byte	1,5,77,9
	.half	.L886-.L1304
	.byte	1,5,58,9
	.half	.L174-.L886
	.byte	3,1,1,5,56,9
	.half	.L887-.L174
	.byte	1,5,71,9
	.half	.L1305-.L887
	.byte	1,5,77,9
	.half	.L891-.L1305
	.byte	1,5,37,9
	.half	.L175-.L891
	.byte	3,1,1,5,11,9
	.half	.L177-.L175
	.byte	3,2,1,5,22,9
	.half	.L171-.L177
	.byte	3,120,1,5,25,9
	.half	.L1306-.L171
	.byte	1,5,1,7,9
	.half	.L1307-.L1306
	.byte	3,10,1,7,9
	.half	.L377-.L1307
	.byte	0,1,1
.L1297:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_string')
	.sect	'.debug_ranges'
.L376:
	.word	-1,.L310,0,.L377-.L310,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_int')
	.sect	'.debug_info'
.L378:
	.word	398
	.half	3
	.word	.L379
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L381,.L380
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_int',0,1,213,5,6,1,1,1
	.word	.L312,.L552,.L311
	.byte	4
	.byte	'x',0,1,213,5,30
	.word	.L486,.L553
	.byte	4
	.byte	'y',0,1,213,5,40
	.word	.L486,.L554
	.byte	4
	.byte	'dat',0,1,213,5,55
	.word	.L555,.L556
	.byte	4
	.byte	'num',0,1,213,5,66
	.word	.L531,.L557
	.byte	5
	.word	.L312,.L552
	.byte	5
	.word	.L558,.L552
	.byte	6
	.byte	'dat_temp',0,1,222,5,11
	.word	.L559,.L560
	.byte	6
	.byte	'offset',0,1,223,5,11
	.word	.L559,.L561
	.byte	6
	.byte	'data_buffer',0,1,224,5,10
	.word	.L562,.L563
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_int')
	.sect	'.debug_abbrev'
.L379:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_int')
	.sect	'.debug_line'
.L380:
	.word	.L1309-.L1308
.L1308:
	.half	3
	.word	.L1311-.L1310
.L1310:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1311:
	.byte	5,6,7,0,5,2
	.word	.L312
	.byte	3,212,5,1,5,5,9
	.half	.L897-.L312
	.byte	3,4,1,9
	.half	.L893-.L897
	.byte	3,1,1,9
	.half	.L1312-.L893
	.byte	3,1,1,9
	.half	.L1313-.L1312
	.byte	3,1,1,5,18,9
	.half	.L558-.L1313
	.byte	3,3,1,5,12,9
	.half	.L901-.L558
	.byte	3,3,1,5,25,9
	.half	.L1314-.L901
	.byte	1,5,28,9
	.half	.L1315-.L1314
	.byte	1,5,12,9
	.half	.L1316-.L1315
	.byte	3,1,1,5,25,9
	.half	.L1317-.L1316
	.byte	1,5,33,9
	.half	.L1318-.L1317
	.byte	1,5,5,9
	.half	.L1319-.L1318
	.byte	3,3,1,5,22,7,9
	.half	.L1320-.L1319
	.byte	3,2,1,5,20,9
	.half	.L182-.L1320
	.byte	3,2,1,5,28,9
	.half	.L1321-.L182
	.byte	3,126,1,5,22,9
	.half	.L181-.L1321
	.byte	1,5,18,7,9
	.half	.L1322-.L181
	.byte	3,4,1,5,21,9
	.half	.L180-.L1322
	.byte	3,2,1,5,34,9
	.half	.L1323-.L180
	.byte	1,5,45,9
	.half	.L903-.L1323
	.byte	3,1,1,5,30,9
	.half	.L904-.L903
	.byte	1,5,1,9
	.half	.L1324-.L904
	.byte	3,1,1,7,9
	.half	.L382-.L1324
	.byte	0,1,1
.L1309:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_int')
	.sect	'.debug_ranges'
.L381:
	.word	-1,.L312,0,.L382-.L312,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_uint')
	.sect	'.debug_info'
.L383:
	.word	399
	.half	3
	.word	.L384
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L386,.L385
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_uint',0,1,252,5,6,1,1,1
	.word	.L314,.L564,.L313
	.byte	4
	.byte	'x',0,1,252,5,31
	.word	.L486,.L565
	.byte	4
	.byte	'y',0,1,252,5,41
	.word	.L486,.L566
	.byte	4
	.byte	'dat',0,1,252,5,57
	.word	.L567,.L568
	.byte	4
	.byte	'num',0,1,252,5,68
	.word	.L531,.L569
	.byte	5
	.word	.L314,.L564
	.byte	5
	.word	.L570,.L564
	.byte	6
	.byte	'dat_temp',0,1,133,6,12
	.word	.L571,.L572
	.byte	6
	.byte	'offset',0,1,134,6,11
	.word	.L559,.L573
	.byte	6
	.byte	'data_buffer',0,1,135,6,10
	.word	.L562,.L574
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_uint')
	.sect	'.debug_abbrev'
.L384:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_uint')
	.sect	'.debug_line'
.L385:
	.word	.L1326-.L1325
.L1325:
	.half	3
	.word	.L1328-.L1327
.L1327:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1328:
	.byte	5,6,7,0,5,2
	.word	.L314
	.byte	3,251,5,1,5,5,9
	.half	.L911-.L314
	.byte	3,4,1,9
	.half	.L907-.L911
	.byte	3,1,1,9
	.half	.L1329-.L907
	.byte	3,1,1,9
	.half	.L1330-.L1329
	.byte	3,1,1,5,18,9
	.half	.L570-.L1330
	.byte	3,3,1,5,12,9
	.half	.L915-.L570
	.byte	3,2,1,5,25,9
	.half	.L1331-.L915
	.byte	1,5,28,9
	.half	.L1332-.L1331
	.byte	1,5,12,9
	.half	.L1333-.L1332
	.byte	3,1,1,5,25,9
	.half	.L1334-.L1333
	.byte	1,5,30,9
	.half	.L1335-.L1334
	.byte	1,5,5,9
	.half	.L917-.L1335
	.byte	3,3,1,5,22,7,9
	.half	.L1336-.L917
	.byte	3,2,1,5,20,9
	.half	.L185-.L1336
	.byte	3,2,1,5,28,9
	.half	.L1337-.L185
	.byte	3,126,1,5,22,9
	.half	.L184-.L1337
	.byte	1,5,18,7,9
	.half	.L1338-.L184
	.byte	3,4,1,5,22,9
	.half	.L183-.L1338
	.byte	3,2,1,5,35,9
	.half	.L1339-.L183
	.byte	1,5,45,9
	.half	.L919-.L1339
	.byte	3,1,1,5,30,9
	.half	.L920-.L919
	.byte	1,5,1,9
	.half	.L1340-.L920
	.byte	3,1,1,7,9
	.half	.L387-.L1340
	.byte	0,1,1
.L1326:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_uint')
	.sect	'.debug_ranges'
.L386:
	.word	-1,.L314,0,.L387-.L314,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_float')
	.sect	'.debug_info'
.L388:
	.word	422
	.half	3
	.word	.L389
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L391,.L390
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_float',0,1,166,6,6,1,1,1
	.word	.L316,.L575,.L315
	.byte	4
	.byte	'x',0,1,166,6,32
	.word	.L486,.L576
	.byte	4
	.byte	'y',0,1,166,6,42
	.word	.L486,.L577
	.byte	4
	.byte	'dat',0,1,166,6,58
	.word	.L578,.L579
	.byte	4
	.byte	'num',0,1,166,6,69
	.word	.L531,.L580
	.byte	4
	.byte	'pointnum',0,1,166,6,80
	.word	.L531,.L581
	.byte	5
	.word	.L316,.L575
	.byte	5
	.word	.L582,.L575
	.byte	6
	.byte	'dat_temp',0,1,177,6,12
	.word	.L583,.L584
	.byte	6
	.byte	'offset',0,1,178,6,12
	.word	.L583,.L585
	.byte	6
	.byte	'data_buffer',0,1,179,6,10
	.word	.L586,.L587
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_float')
	.sect	'.debug_abbrev'
.L389:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_float')
	.sect	'.debug_line'
.L390:
	.word	.L1342-.L1341
.L1341:
	.half	3
	.word	.L1344-.L1343
.L1343:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1344:
	.byte	5,6,7,0,5,2
	.word	.L316
	.byte	3,165,6,1,5,5,9
	.half	.L929-.L316
	.byte	3,4,1,9
	.half	.L923-.L929
	.byte	3,1,1,9
	.half	.L1345-.L923
	.byte	3,1,1,9
	.half	.L1346-.L1345
	.byte	3,1,1,9
	.half	.L1347-.L1346
	.byte	3,1,1,9
	.half	.L1348-.L1347
	.byte	3,1,1,5,21,9
	.half	.L582-.L1348
	.byte	3,3,1,5,12,9
	.half	.L1349-.L582
	.byte	3,2,1,5,25,9
	.half	.L1350-.L1349
	.byte	1,5,28,9
	.half	.L1351-.L1350
	.byte	1,5,12,9
	.half	.L1352-.L1351
	.byte	3,1,1,5,25,9
	.half	.L1353-.L1352
	.byte	1,5,33,9
	.half	.L1354-.L1353
	.byte	1,5,42,9
	.half	.L1355-.L1354
	.byte	1,5,18,9
	.half	.L1356-.L1355
	.byte	3,3,1,5,19,9
	.half	.L187-.L1356
	.byte	3,2,1,5,16,9
	.half	.L932-.L187
	.byte	1,5,24,9
	.half	.L1357-.L932
	.byte	3,126,1,5,18,9
	.half	.L186-.L1357
	.byte	1,5,28,7,9
	.half	.L934-.L186
	.byte	3,4,1,5,44,9
	.half	.L936-.L934
	.byte	1,5,42,9
	.half	.L1358-.L936
	.byte	1,5,57,9
	.half	.L938-.L1358
	.byte	1,5,25,9
	.half	.L940-.L938
	.byte	1,5,24,9
	.half	.L1359-.L940
	.byte	3,1,1,5,47,9
	.half	.L1360-.L1359
	.byte	1,5,30,9
	.half	.L943-.L1360
	.byte	3,1,1,5,1,9
	.half	.L945-.L943
	.byte	3,1,1,7,9
	.half	.L392-.L945
	.byte	0,1,1
.L1342:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_float')
	.sect	'.debug_ranges'
.L391:
	.word	-1,.L316,0,.L392-.L316,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_info'
.L393:
	.word	533
	.half	3
	.word	.L394
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L396,.L395
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_binary_image',0,1,209,6,6,1,1,1
	.word	.L318,.L588,.L317
	.byte	4
	.byte	'x',0,1,209,6,39
	.word	.L486,.L589
	.byte	4
	.byte	'y',0,1,209,6,49
	.word	.L486,.L590
	.byte	4
	.byte	'image',0,1,209,6,65
	.word	.L591,.L592
	.byte	4
	.byte	'width',0,1,209,6,79
	.word	.L486,.L593
	.byte	4
	.byte	'height',0,1,209,6,93
	.word	.L486,.L594
	.byte	4
	.byte	'dis_width',0,1,209,6,108
	.word	.L486,.L595
	.byte	4
	.byte	'dis_height',0,1,209,6,126
	.word	.L486,.L596
	.byte	5
	.word	.L318,.L588
	.byte	5
	.word	.L597,.L588
	.byte	6
	.byte	'i',0,1,217,6,12
	.word	.L571,.L598
	.byte	6
	.byte	'j',0,1,217,6,19
	.word	.L571,.L599
	.byte	6
	.byte	'temp',0,1,218,6,11
	.word	.L531,.L600
	.byte	6
	.byte	'width_index',0,1,219,6,12
	.word	.L571,.L601
	.byte	6
	.byte	'data_buffer',0,1,220,6,12
	.word	.L484,.L602
	.byte	6
	.byte	'image_temp',0,1,221,6,18
	.word	.L591,.L603
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_abbrev'
.L394:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_line'
.L395:
	.word	.L1362-.L1361
.L1361:
	.half	3
	.word	.L1364-.L1363
.L1363:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1364:
	.byte	5,6,7,0,5,2
	.word	.L318
	.byte	3,208,6,1,5,5,9
	.half	.L953-.L318
	.byte	3,4,1,9
	.half	.L947-.L953
	.byte	3,1,1,9
	.half	.L1365-.L947
	.byte	3,1,1,5,33,9
	.half	.L597-.L1365
	.byte	3,5,1,5,27,9
	.half	.L959-.L597
	.byte	3,3,1,5,5,9
	.half	.L1366-.L959
	.byte	1,5,9,7,9
	.half	.L1367-.L1366
	.byte	3,2,1,5,31,9
	.half	.L188-.L1367
	.byte	3,2,1,5,43,9
	.half	.L961-.L188
	.byte	1,5,50,9
	.half	.L962-.L961
	.byte	1,5,63,9
	.half	.L963-.L962
	.byte	1,5,11,9
	.half	.L1368-.L963
	.byte	3,2,1,5,30,9
	.half	.L966-.L1368
	.byte	1,5,32,9
	.half	.L192-.L966
	.byte	3,2,1,5,41,9
	.half	.L967-.L192
	.byte	1,5,54,9
	.half	.L968-.L967
	.byte	1,5,64,9
	.half	.L969-.L968
	.byte	1,5,62,9
	.half	.L1369-.L969
	.byte	1,5,28,9
	.half	.L1370-.L1369
	.byte	1,5,15,9
	.half	.L970-.L1370
	.byte	3,1,1,5,33,9
	.half	.L971-.L970
	.byte	1,5,29,9
	.half	.L194-.L971
	.byte	3,2,1,5,37,9
	.half	.L973-.L194
	.byte	1,5,49,9
	.half	.L974-.L973
	.byte	3,1,1,5,47,9
	.half	.L1371-.L974
	.byte	1,5,33,9
	.half	.L1372-.L1371
	.byte	1,5,20,9
	.half	.L1373-.L1372
	.byte	1,5,47,9
	.half	.L976-.L1373
	.byte	3,1,1,5,45,9
	.half	.L1374-.L976
	.byte	1,5,29,9
	.half	.L975-.L1374
	.byte	1,5,13,9
	.half	.L977-.L975
	.byte	1,5,28,7,9
	.half	.L1375-.L977
	.byte	3,2,1,5,35,9
	.half	.L1376-.L1375
	.byte	1,5,32,9
	.half	.L1377-.L1376
	.byte	1,5,48,9
	.half	.L1378-.L1377
	.byte	1,5,28,9
	.half	.L195-.L1378
	.byte	3,4,1,5,35,9
	.half	.L1379-.L195
	.byte	1,5,32,9
	.half	.L1380-.L1379
	.byte	1,5,37,9
	.half	.L196-.L1380
	.byte	3,118,1,5,33,9
	.half	.L193-.L196
	.byte	1,5,52,7,9
	.half	.L1381-.L193
	.byte	3,13,1,5,34,9
	.half	.L979-.L1381
	.byte	3,112,1,5,30,9
	.half	.L191-.L979
	.byte	1,5,27,7,9
	.half	.L1382-.L191
	.byte	3,18,1,5,5,9
	.half	.L1383-.L1382
	.byte	1,5,9,7,9
	.half	.L1384-.L1383
	.byte	3,2,1,5,1,9
	.half	.L197-.L1384
	.byte	3,2,1,9
	.half	.L397-.L197
	.byte	0,1,1
.L1362:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_ranges'
.L396:
	.word	-1,.L318,0,.L397-.L318,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_info'
.L398:
	.word	549
	.half	3
	.word	.L399
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L401,.L400
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_gray_image',0,1,142,7,6,1,1,1
	.word	.L320,.L604,.L319
	.byte	4
	.byte	'x',0,1,142,7,37
	.word	.L486,.L605
	.byte	4
	.byte	'y',0,1,142,7,47
	.word	.L486,.L606
	.byte	4
	.byte	'image',0,1,142,7,63
	.word	.L591,.L607
	.byte	4
	.byte	'width',0,1,142,7,77
	.word	.L486,.L608
	.byte	4
	.byte	'height',0,1,142,7,91
	.word	.L486,.L609
	.byte	4
	.byte	'dis_width',0,1,142,7,106
	.word	.L486,.L610
	.byte	4
	.byte	'dis_height',0,1,142,7,124
	.word	.L486,.L611
	.byte	4
	.byte	'threshold',0,1,142,7,142,1
	.word	.L531,.L612
	.byte	5
	.word	.L320,.L604
	.byte	5
	.word	.L613,.L604
	.byte	6
	.byte	'i',0,1,150,7,12
	.word	.L571,.L614
	.byte	6
	.byte	'j',0,1,150,7,19
	.word	.L571,.L615
	.byte	6
	.byte	'color',0,1,151,7,12
	.word	.L486,.L616
	.byte	6
	.byte	'temp',0,1,151,7,22
	.word	.L486,.L617
	.byte	6
	.byte	'data_buffer',0,1,152,7,12
	.word	.L484,.L618
	.byte	6
	.byte	'image_temp',0,1,153,7,18
	.word	.L591,.L619
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_abbrev'
.L399:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_line'
.L400:
	.word	.L1386-.L1385
.L1385:
	.half	3
	.word	.L1388-.L1387
.L1387:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1388:
	.byte	5,6,7,0,5,2
	.word	.L320
	.byte	3,141,7,1,5,5,9
	.half	.L989-.L320
	.byte	3,4,1,9
	.half	.L982-.L989
	.byte	3,1,1,9
	.half	.L1389-.L982
	.byte	3,1,1,5,33,9
	.half	.L613-.L1389
	.byte	3,4,1,5,27,9
	.half	.L995-.L613
	.byte	3,3,1,5,5,9
	.half	.L1390-.L995
	.byte	1,5,9,7,9
	.half	.L1391-.L1390
	.byte	3,2,1,5,31,9
	.half	.L200-.L1391
	.byte	3,2,1,5,43,9
	.half	.L997-.L200
	.byte	1,5,50,9
	.half	.L998-.L997
	.byte	1,5,63,9
	.half	.L999-.L998
	.byte	1,5,11,9
	.half	.L1392-.L999
	.byte	3,2,1,5,30,9
	.half	.L1002-.L1392
	.byte	1,5,32,9
	.half	.L204-.L1002
	.byte	3,2,1,5,41,9
	.half	.L1003-.L204
	.byte	1,5,54,9
	.half	.L1004-.L1003
	.byte	1,5,28,9
	.half	.L1005-.L1004
	.byte	1,5,15,9
	.half	.L1006-.L1005
	.byte	3,1,1,5,33,9
	.half	.L1007-.L1006
	.byte	1,5,37,9
	.half	.L206-.L1007
	.byte	3,2,1,5,45,9
	.half	.L1009-.L206
	.byte	1,5,33,9
	.half	.L1393-.L1009
	.byte	1,5,20,9
	.half	.L1394-.L1393
	.byte	1,5,13,9
	.half	.L1010-.L1394
	.byte	3,1,1,5,43,7,9
	.half	.L1395-.L1010
	.byte	3,2,1,5,33,9
	.half	.L1396-.L1395
	.byte	1,5,50,9
	.half	.L1397-.L1396
	.byte	1,5,54,9
	.half	.L1012-.L1397
	.byte	3,1,1,5,44,9
	.half	.L1398-.L1012
	.byte	1,5,61,9
	.half	.L1399-.L1398
	.byte	1,5,31,9
	.half	.L1400-.L1399
	.byte	1,5,51,9
	.half	.L1401-.L1400
	.byte	3,1,1,5,41,9
	.half	.L1011-.L1401
	.byte	1,5,31,9
	.half	.L1402-.L1011
	.byte	1,5,28,9
	.half	.L1403-.L1402
	.byte	3,1,1,5,32,9
	.half	.L1404-.L1403
	.byte	1,5,55,9
	.half	.L1405-.L1404
	.byte	3,125,1,5,18,9
	.half	.L207-.L1405
	.byte	3,5,1,5,28,7,9
	.half	.L1406-.L207
	.byte	3,2,1,5,35,9
	.half	.L1407-.L1406
	.byte	1,5,32,9
	.half	.L1408-.L1407
	.byte	1,5,48,9
	.half	.L1409-.L1408
	.byte	1,5,28,9
	.half	.L209-.L1409
	.byte	3,4,1,5,35,9
	.half	.L1410-.L209
	.byte	1,5,32,9
	.half	.L1411-.L1410
	.byte	1,5,37,9
	.half	.L208-.L1411
	.byte	3,112,1,5,33,9
	.half	.L205-.L208
	.byte	1,5,52,7,9
	.half	.L1412-.L205
	.byte	3,19,1,5,34,9
	.half	.L1008-.L1412
	.byte	3,106,1,5,30,9
	.half	.L203-.L1008
	.byte	1,5,27,7,9
	.half	.L1413-.L203
	.byte	3,24,1,5,5,9
	.half	.L1414-.L1413
	.byte	1,5,9,7,9
	.half	.L1415-.L1414
	.byte	3,2,1,5,1,9
	.half	.L211-.L1415
	.byte	3,2,1,9
	.half	.L402-.L211
	.byte	0,1,1
.L1386:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_ranges'
.L401:
	.word	-1,.L320,0,.L402-.L320,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_info'
.L403:
	.word	515
	.half	3
	.word	.L404
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L406,.L405
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_rgb565_image',0,1,208,7,6,1,1,1
	.word	.L322,.L620,.L321
	.byte	4
	.byte	'x',0,1,208,7,39
	.word	.L486,.L621
	.byte	4
	.byte	'y',0,1,208,7,49
	.word	.L486,.L622
	.byte	4
	.byte	'image',0,1,208,7,66
	.word	.L623,.L624
	.byte	4
	.byte	'width',0,1,208,7,80
	.word	.L486,.L625
	.byte	4
	.byte	'height',0,1,208,7,94
	.word	.L486,.L626
	.byte	4
	.byte	'dis_width',0,1,208,7,109
	.word	.L486,.L627
	.byte	4
	.byte	'dis_height',0,1,208,7,127
	.word	.L486,.L628
	.byte	4
	.byte	'color_mode',0,1,208,7,145,1
	.word	.L531,.L629
	.byte	5
	.word	.L322,.L620
	.byte	5
	.word	.L630,.L620
	.byte	6
	.byte	'i',0,1,216,7,12
	.word	.L571,.L631
	.byte	6
	.byte	'j',0,1,216,7,19
	.word	.L571,.L632
	.byte	6
	.byte	'data_buffer',0,1,217,7,12
	.word	.L484,.L633
	.byte	6
	.byte	'image_temp',0,1,218,7,19
	.word	.L623,.L634
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_abbrev'
.L404:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_line'
.L405:
	.word	.L1417-.L1416
.L1416:
	.half	3
	.word	.L1419-.L1418
.L1418:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1419:
	.byte	5,6,7,0,5,2
	.word	.L322
	.byte	3,207,7,1,5,5,9
	.half	.L1024-.L322
	.byte	3,4,1,9
	.half	.L1017-.L1024
	.byte	3,1,1,9
	.half	.L1420-.L1017
	.byte	3,1,1,5,33,9
	.half	.L630-.L1420
	.byte	3,3,1,5,27,9
	.half	.L1030-.L630
	.byte	3,3,1,5,5,9
	.half	.L1421-.L1030
	.byte	1,5,9,7,9
	.half	.L1422-.L1421
	.byte	3,2,1,5,31,9
	.half	.L214-.L1422
	.byte	3,2,1,5,43,9
	.half	.L1032-.L214
	.byte	1,5,50,9
	.half	.L1033-.L1032
	.byte	1,5,63,9
	.half	.L1034-.L1033
	.byte	1,5,11,9
	.half	.L1423-.L1034
	.byte	3,2,1,5,30,9
	.half	.L1037-.L1423
	.byte	1,5,32,9
	.half	.L218-.L1037
	.byte	3,2,1,5,41,9
	.half	.L1038-.L218
	.byte	1,5,54,9
	.half	.L1039-.L1038
	.byte	1,5,28,9
	.half	.L1040-.L1039
	.byte	1,5,15,9
	.half	.L1041-.L1040
	.byte	3,1,1,5,33,9
	.half	.L1042-.L1041
	.byte	1,5,24,9
	.half	.L220-.L1042
	.byte	3,2,1,5,47,9
	.half	.L1044-.L220
	.byte	1,5,55,9
	.half	.L1045-.L1044
	.byte	1,5,43,9
	.half	.L1424-.L1045
	.byte	1,5,30,9
	.half	.L1425-.L1424
	.byte	1,5,28,9
	.half	.L1426-.L1425
	.byte	1,5,37,9
	.half	.L1427-.L1426
	.byte	3,126,1,5,33,9
	.half	.L219-.L1427
	.byte	1,5,9,7,9
	.half	.L1428-.L219
	.byte	3,4,1,5,74,7,9
	.half	.L1429-.L1428
	.byte	3,2,1,5,78,9
	.half	.L1043-.L1429
	.byte	1,5,56,9
	.half	.L221-.L1043
	.byte	3,4,1,5,34,9
	.half	.L222-.L221
	.byte	3,115,1,5,30,9
	.half	.L217-.L222
	.byte	1,5,27,7,9
	.half	.L1430-.L217
	.byte	3,16,1,5,5,9
	.half	.L1431-.L1430
	.byte	1,5,9,7,9
	.half	.L1432-.L1431
	.byte	3,2,1,5,1,9
	.half	.L223-.L1432
	.byte	3,2,1,9
	.half	.L407-.L223
	.byte	0,1,1
.L1417:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_ranges'
.L406:
	.word	-1,.L322,0,.L407-.L322,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_wave')
	.sect	'.debug_info'
.L408:
	.word	517
	.half	3
	.word	.L409
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L411,.L410
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_wave',0,1,133,8,6,1,1,1
	.word	.L324,.L635,.L323
	.byte	4
	.byte	'x',0,1,133,8,31
	.word	.L486,.L636
	.byte	4
	.byte	'y',0,1,133,8,41
	.word	.L486,.L637
	.byte	4
	.byte	'wave',0,1,133,8,58
	.word	.L623,.L638
	.byte	4
	.byte	'width',0,1,133,8,71
	.word	.L486,.L639
	.byte	4
	.byte	'value_max',0,1,133,8,85
	.word	.L486,.L640
	.byte	4
	.byte	'dis_width',0,1,133,8,103
	.word	.L486,.L641
	.byte	4
	.byte	'dis_value_max',0,1,133,8,121
	.word	.L486,.L642
	.byte	5
	.word	.L324,.L635
	.byte	5
	.word	.L643,.L635
	.byte	6
	.byte	'i',0,1,141,8,12
	.word	.L571,.L644
	.byte	6
	.byte	'j',0,1,141,8,19
	.word	.L571,.L645
	.byte	6
	.byte	'width_index',0,1,142,8,12
	.word	.L571,.L646
	.byte	6
	.byte	'value_max_index',0,1,142,8,29
	.word	.L571,.L647
	.byte	6
	.byte	'data_buffer',0,1,143,8,12
	.word	.L484,.L648
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_wave')
	.sect	'.debug_abbrev'
.L409:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_wave')
	.sect	'.debug_line'
.L410:
	.word	.L1434-.L1433
.L1433:
	.half	3
	.word	.L1436-.L1435
.L1435:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1436:
	.byte	5,6,7,0,5,2
	.word	.L324
	.byte	3,132,8,1,5,5,9
	.half	.L1057-.L324
	.byte	3,4,1,9
	.half	.L1051-.L1057
	.byte	3,1,1,9
	.half	.L1437-.L1051
	.byte	3,1,1,5,33,9
	.half	.L643-.L1437
	.byte	3,4,1,5,27,9
	.half	.L1063-.L643
	.byte	3,2,1,5,5,9
	.half	.L1438-.L1063
	.byte	1,5,9,7,9
	.half	.L1439-.L1438
	.byte	3,2,1,5,31,9
	.half	.L226-.L1439
	.byte	3,2,1,5,43,9
	.half	.L1065-.L226
	.byte	1,5,50,9
	.half	.L1066-.L1065
	.byte	1,5,66,9
	.half	.L1067-.L1066
	.byte	1,5,11,9
	.half	.L1440-.L1067
	.byte	3,1,1,5,33,9
	.half	.L1070-.L1440
	.byte	1,5,15,9
	.half	.L230-.L1070
	.byte	3,2,1,5,33,9
	.half	.L1072-.L230
	.byte	1,5,24,9
	.half	.L232-.L1072
	.byte	3,2,1,5,31,9
	.half	.L1441-.L232
	.byte	1,5,28,9
	.half	.L1442-.L1441
	.byte	1,5,37,9
	.half	.L1443-.L1442
	.byte	3,126,1,5,33,9
	.half	.L231-.L1443
	.byte	1,5,52,7,9
	.half	.L1444-.L231
	.byte	3,4,1,5,37,9
	.half	.L1073-.L1444
	.byte	3,122,1,5,33,9
	.half	.L229-.L1073
	.byte	1,5,27,7,9
	.half	.L1445-.L229
	.byte	3,8,1,5,5,9
	.half	.L1446-.L1445
	.byte	1,5,9,7,9
	.half	.L1447-.L1446
	.byte	3,2,1,5,11,9
	.half	.L233-.L1447
	.byte	3,3,1,5,29,9
	.half	.L1071-.L233
	.byte	1,5,25,9
	.half	.L237-.L1071
	.byte	3,2,1,5,33,9
	.half	.L1076-.L237
	.byte	1,5,34,9
	.half	.L1077-.L1076
	.byte	3,1,1,5,27,9
	.half	.L1448-.L1077
	.byte	1,5,66,9
	.half	.L1078-.L1448
	.byte	1,5,49,9
	.half	.L1449-.L1078
	.byte	1,5,71,9
	.half	.L1079-.L1449
	.byte	1,5,38,9
	.half	.L1080-.L1079
	.byte	3,1,1,5,27,9
	.half	.L1082-.L1080
	.byte	1,5,68,9
	.half	.L1450-.L1082
	.byte	1,5,73,9
	.half	.L1451-.L1450
	.byte	1,5,91,9
	.half	.L1083-.L1451
	.byte	1,5,44,9
	.half	.L1084-.L1083
	.byte	1,5,97,9
	.half	.L1452-.L1084
	.byte	1,5,33,9
	.half	.L1081-.L1452
	.byte	3,124,1,5,29,9
	.half	.L236-.L1081
	.byte	1,5,1,7,9
	.half	.L1453-.L236
	.byte	3,6,1,9
	.half	.L412-.L1453
	.byte	0,1,1
.L1434:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_wave')
	.sect	'.debug_ranges'
.L411:
	.word	-1,.L324,0,.L412-.L324,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_show_chinese')
	.sect	'.debug_info'
.L413:
	.word	507
	.half	3
	.word	.L414
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L416,.L415
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_show_chinese',0,1,183,8,6,1,1,1
	.word	.L326,.L649,.L325
	.byte	4
	.byte	'x',0,1,183,8,34
	.word	.L486,.L650
	.byte	4
	.byte	'y',0,1,183,8,44
	.word	.L486,.L651
	.byte	4
	.byte	'size',0,1,183,8,53
	.word	.L531,.L652
	.byte	4
	.byte	'chinese_buffer',0,1,183,8,72
	.word	.L591,.L653
	.byte	4
	.byte	'number',0,1,183,8,94
	.word	.L531,.L654
	.byte	4
	.byte	'color',0,1,183,8,115
	.word	.L655,.L656
	.byte	5
	.word	.L326,.L649
	.byte	5
	.word	.L657,.L649
	.byte	6
	.byte	'i',0,1,191,8,9
	.word	.L559,.L658
	.byte	6
	.byte	'j',0,1,191,8,16
	.word	.L559,.L659
	.byte	6
	.byte	'k',0,1,191,8,23
	.word	.L559,.L660
	.byte	6
	.byte	'temp',0,1,192,8,11
	.word	.L531,.L661
	.byte	6
	.byte	'temp1',0,1,192,8,21
	.word	.L531,.L662
	.byte	6
	.byte	'temp2',0,1,192,8,32
	.word	.L531,.L663
	.byte	6
	.byte	'p_data',0,1,193,8,18
	.word	.L591,.L664
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_show_chinese')
	.sect	'.debug_abbrev'
.L414:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_show_chinese')
	.sect	'.debug_line'
.L415:
	.word	.L1455-.L1454
.L1454:
	.half	3
	.word	.L1457-.L1456
.L1456:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1457:
	.byte	5,6,7,0,5,2
	.word	.L326
	.byte	3,182,8,1,5,5,9
	.half	.L1095-.L326
	.byte	3,4,1,9
	.half	.L1089-.L1095
	.byte	3,1,1,9
	.half	.L1458-.L1089
	.byte	3,1,1,5,20,9
	.half	.L657-.L1458
	.byte	3,6,1,5,18,9
	.half	.L1459-.L657
	.byte	1,5,27,9
	.half	.L1099-.L1459
	.byte	3,2,1,5,5,9
	.half	.L1460-.L1099
	.byte	1,5,9,7,9
	.half	.L1461-.L1460
	.byte	3,2,1,5,36,9
	.half	.L238-.L1461
	.byte	3,2,1,5,43,9
	.half	.L1101-.L238
	.byte	1,5,47,9
	.half	.L1102-.L1101
	.byte	1,5,54,9
	.half	.L1104-.L1102
	.byte	1,5,61,9
	.half	.L1105-.L1104
	.byte	1,5,11,9
	.half	.L1462-.L1105
	.byte	3,2,1,5,24,9
	.half	.L1108-.L1462
	.byte	1,5,15,9
	.half	.L242-.L1108
	.byte	3,2,1,5,37,9
	.half	.L1111-.L242
	.byte	3,1,1,5,33,9
	.half	.L1110-.L1111
	.byte	1,5,23,9
	.half	.L1112-.L1110
	.byte	3,1,1,5,19,9
	.half	.L244-.L1112
	.byte	3,2,1,5,33,9
	.half	.L1114-.L244
	.byte	1,5,23,9
	.half	.L246-.L1114
	.byte	3,2,1,5,33,9
	.half	.L1115-.L246
	.byte	1,5,29,9
	.half	.L248-.L1115
	.byte	3,2,1,5,43,9
	.half	.L1463-.L248
	.byte	1,5,37,9
	.half	.L1464-.L1463
	.byte	1,5,49,9
	.half	.L1465-.L1464
	.byte	1,5,21,9
	.half	.L1116-.L1465
	.byte	3,1,1,5,49,7,9
	.half	.L1466-.L1116
	.byte	3,2,1,5,55,9
	.half	.L1118-.L1466
	.byte	1,5,49,9
	.half	.L249-.L1118
	.byte	3,4,1,5,37,9
	.half	.L250-.L249
	.byte	3,119,1,5,33,9
	.half	.L247-.L250
	.byte	1,5,24,7,9
	.half	.L1467-.L247
	.byte	3,12,1,5,37,9
	.half	.L1468-.L1467
	.byte	3,114,1,5,33,9
	.half	.L245-.L1468
	.byte	1,5,45,7,9
	.half	.L1469-.L245
	.byte	3,16,1,5,22,9
	.half	.L1470-.L1469
	.byte	1,5,29,9
	.half	.L1119-.L1470
	.byte	1,5,37,9
	.half	.L1120-.L1119
	.byte	1,5,20,9
	.half	.L1113-.L1120
	.byte	1,5,21,9
	.half	.L243-.L1113
	.byte	3,110,1,5,23,9
	.half	.L1123-.L243
	.byte	1,5,28,7,9
	.half	.L1124-.L1123
	.byte	3,124,1,5,24,9
	.half	.L241-.L1124
	.byte	1,5,27,7,9
	.half	.L1471-.L241
	.byte	3,25,1,5,5,9
	.half	.L1472-.L1471
	.byte	1,5,9,7,9
	.half	.L1473-.L1472
	.byte	3,2,1,5,1,9
	.half	.L251-.L1473
	.byte	3,2,1,7,9
	.half	.L417-.L251
	.byte	0,1,1
.L1455:
	.sdecl	'.debug_ranges',debug,cluster('ips200_show_chinese')
	.sect	'.debug_ranges'
.L416:
	.word	-1,.L326,0,.L417-.L326,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_init')
	.sect	'.debug_info'
.L418:
	.word	277
	.half	3
	.word	.L419
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L421,.L420
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_init',0,1,241,8,6,1,1,1
	.word	.L328,.L665,.L327
	.byte	4
	.byte	'type_select',0,1,241,8,36
	.word	.L666,.L667
	.byte	5
	.word	.L328,.L665
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_init')
	.sect	'.debug_abbrev'
.L419:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_init')
	.sect	'.debug_line'
.L420:
	.word	.L1475-.L1474
.L1474:
	.half	3
	.word	.L1477-.L1476
.L1476:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1477:
	.byte	5,6,7,0,5,2
	.word	.L328
	.byte	3,240,8,1,5,5,9
	.half	.L1125-.L328
	.byte	3,2,1,5,9,7,9
	.half	.L1478-.L1125
	.byte	3,2,1,5,31,9
	.half	.L1479-.L1478
	.byte	1,5,29,9
	.half	.L1480-.L1479
	.byte	1,5,9,9
	.half	.L1481-.L1480
	.byte	3,1,1,5,23,9
	.half	.L1482-.L1481
	.byte	1,5,21,9
	.half	.L1483-.L1482
	.byte	1,5,9,9
	.half	.L1484-.L1483
	.byte	3,1,1,5,23,9
	.half	.L1485-.L1484
	.byte	1,5,20,9
	.half	.L1486-.L1485
	.byte	1,5,9,9
	.half	.L1487-.L1486
	.byte	3,1,1,5,23,9
	.half	.L1488-.L1487
	.byte	1,5,20,9
	.half	.L1489-.L1488
	.byte	1,5,79,9
	.half	.L1490-.L1489
	.byte	3,4,1,5,99,9
	.half	.L1491-.L1490
	.byte	1,5,122,9
	.half	.L1492-.L1491
	.byte	1,5,18,9
	.half	.L1493-.L1492
	.byte	1,5,30,9
	.half	.L1126-.L1493
	.byte	1,5,41,9
	.half	.L1494-.L1126
	.byte	1,5,59,9
	.half	.L1495-.L1494
	.byte	1,5,19,9
	.half	.L1496-.L1495
	.byte	3,3,1,5,38,9
	.half	.L1497-.L1496
	.byte	1,5,43,9
	.half	.L1498-.L1497
	.byte	1,5,53,9
	.half	.L1499-.L1498
	.byte	1,5,19,9
	.half	.L1500-.L1499
	.byte	3,1,1,5,32,9
	.half	.L1501-.L1500
	.byte	1,5,37,9
	.half	.L1502-.L1501
	.byte	1,5,47,9
	.half	.L1503-.L1502
	.byte	1,5,19,9
	.half	.L1504-.L1503
	.byte	3,1,1,5,31,9
	.half	.L1505-.L1504
	.byte	1,5,36,9
	.half	.L1506-.L1505
	.byte	1,5,47,9
	.half	.L1507-.L1506
	.byte	1,5,19,9
	.half	.L1508-.L1507
	.byte	3,1,1,5,31,9
	.half	.L1509-.L1508
	.byte	1,5,36,9
	.half	.L1510-.L1509
	.byte	1,5,47,9
	.half	.L1511-.L1510
	.byte	1,5,46,9
	.half	.L1512-.L1511
	.byte	3,115,1,5,9,9
	.half	.L254-.L1512
	.byte	3,17,1,5,31,9
	.half	.L1513-.L254
	.byte	1,5,29,9
	.half	.L1514-.L1513
	.byte	1,5,9,9
	.half	.L1515-.L1514
	.byte	3,1,1,5,23,9
	.half	.L1516-.L1515
	.byte	1,5,21,9
	.half	.L1517-.L1516
	.byte	1,5,9,9
	.half	.L1518-.L1517
	.byte	3,1,1,5,22,9
	.half	.L1519-.L1518
	.byte	1,5,20,9
	.half	.L1520-.L1519
	.byte	1,5,9,9
	.half	.L1521-.L1520
	.byte	3,1,1,5,23,9
	.half	.L1522-.L1521
	.byte	1,5,20,9
	.half	.L1523-.L1522
	.byte	1,5,19,9
	.half	.L1524-.L1523
	.byte	3,2,1,5,32,9
	.half	.L1127-.L1524
	.byte	1,5,37,9
	.half	.L1525-.L1127
	.byte	1,5,47,9
	.half	.L1526-.L1525
	.byte	1,5,19,9
	.half	.L1527-.L1526
	.byte	3,1,1,5,31,9
	.half	.L1528-.L1527
	.byte	1,5,36,9
	.half	.L1529-.L1528
	.byte	1,5,46,9
	.half	.L1530-.L1529
	.byte	1,5,19,9
	.half	.L1531-.L1530
	.byte	3,1,1,5,31,9
	.half	.L1532-.L1531
	.byte	1,5,36,9
	.half	.L1533-.L1532
	.byte	1,5,47,9
	.half	.L1534-.L1533
	.byte	1,5,19,9
	.half	.L1535-.L1534
	.byte	3,2,1,5,44,9
	.half	.L1536-.L1535
	.byte	1,5,49,9
	.half	.L1537-.L1536
	.byte	1,5,59,9
	.half	.L1538-.L1537
	.byte	1,5,19,9
	.half	.L1539-.L1538
	.byte	3,1,1,5,44,9
	.half	.L1540-.L1539
	.byte	1,5,49,9
	.half	.L1541-.L1540
	.byte	1,5,59,9
	.half	.L1542-.L1541
	.byte	1,5,19,9
	.half	.L1543-.L1542
	.byte	3,1,1,5,44,9
	.half	.L1544-.L1543
	.byte	1,5,49,9
	.half	.L1545-.L1544
	.byte	1,5,60,9
	.half	.L1546-.L1545
	.byte	1,5,19,9
	.half	.L1547-.L1546
	.byte	3,2,1,5,44,9
	.half	.L1548-.L1547
	.byte	1,5,49,9
	.half	.L1549-.L1548
	.byte	1,5,60,9
	.half	.L1550-.L1549
	.byte	1,5,19,9
	.half	.L1551-.L1550
	.byte	3,1,1,5,44,9
	.half	.L1552-.L1551
	.byte	1,5,49,9
	.half	.L1553-.L1552
	.byte	1,5,60,9
	.half	.L1554-.L1553
	.byte	1,5,19,9
	.half	.L1555-.L1554
	.byte	3,2,1,5,44,9
	.half	.L1556-.L1555
	.byte	1,5,49,9
	.half	.L1557-.L1556
	.byte	1,5,60,9
	.half	.L1558-.L1557
	.byte	1,5,19,9
	.half	.L1559-.L1558
	.byte	3,1,1,5,44,9
	.half	.L1560-.L1559
	.byte	1,5,49,9
	.half	.L1561-.L1560
	.byte	1,5,60,9
	.half	.L1562-.L1561
	.byte	1,5,19,9
	.half	.L1563-.L1562
	.byte	3,2,1,5,44,9
	.half	.L1564-.L1563
	.byte	1,5,49,9
	.half	.L1565-.L1564
	.byte	1,5,60,9
	.half	.L1566-.L1565
	.byte	1,5,19,9
	.half	.L1567-.L1566
	.byte	3,1,1,5,44,9
	.half	.L1568-.L1567
	.byte	1,5,49,9
	.half	.L1569-.L1568
	.byte	1,5,60,9
	.half	.L1570-.L1569
	.byte	1,5,19,9
	.half	.L1571-.L1570
	.byte	3,1,1,5,44,9
	.half	.L1572-.L1571
	.byte	1,5,49,9
	.half	.L1573-.L1572
	.byte	1,5,60,9
	.half	.L1574-.L1573
	.byte	1,5,19,9
	.half	.L1575-.L1574
	.byte	3,1,1,5,44,9
	.half	.L1576-.L1575
	.byte	1,5,49,9
	.half	.L1577-.L1576
	.byte	1,5,60,9
	.half	.L1578-.L1577
	.byte	1,5,20,9
	.half	.L255-.L1578
	.byte	3,3,1,5,22,9
	.half	.L1579-.L255
	.byte	3,1,1,5,39,9
	.half	.L1580-.L1579
	.byte	1,5,5,9
	.half	.L1581-.L1580
	.byte	3,2,1,9
	.half	.L257-.L1581
	.byte	3,1,1,5,21,9
	.half	.L259-.L257
	.byte	3,1,1,5,5,9
	.half	.L1582-.L259
	.byte	3,1,1,5,21,9
	.half	.L261-.L1582
	.byte	3,1,1,5,27,9
	.half	.L1583-.L261
	.byte	3,2,1,5,5,9
	.half	.L1584-.L1583
	.byte	1,5,9,7,9
	.half	.L1585-.L1584
	.byte	3,2,1,5,26,9
	.half	.L262-.L1585
	.byte	3,2,1,5,21,9
	.half	.L1586-.L262
	.byte	3,1,1,5,26,9
	.half	.L1587-.L1586
	.byte	3,2,1,5,12,9
	.half	.L1588-.L1587
	.byte	3,1,1,5,14,9
	.half	.L1589-.L1588
	.byte	3,2,1,9
	.half	.L1590-.L1589
	.byte	3,1,1,9
	.half	.L1591-.L1590
	.byte	3,1,1,9
	.half	.L1592-.L1591
	.byte	3,1,1,5,60,9
	.half	.L265-.L1592
	.byte	3,125,1,5,69,9
	.half	.L1593-.L265
	.byte	1,5,60,9
	.half	.L266-.L1593
	.byte	3,1,1,5,69,9
	.half	.L1594-.L266
	.byte	1,5,60,9
	.half	.L267-.L1594
	.byte	3,1,1,5,69,9
	.half	.L1595-.L267
	.byte	1,5,60,9
	.half	.L268-.L1595
	.byte	3,1,1,5,69,9
	.half	.L1596-.L268
	.byte	1,5,26,9
	.half	.L270-.L1596
	.byte	3,3,1,5,28,9
	.half	.L1597-.L270
	.byte	3,1,1,5,26,9
	.half	.L1598-.L1597
	.byte	3,2,1,5,28,9
	.half	.L1599-.L1598
	.byte	3,1,1,9
	.half	.L1600-.L1599
	.byte	3,1,1,9
	.half	.L1601-.L1600
	.byte	3,1,1,9
	.half	.L1602-.L1601
	.byte	3,1,1,9
	.half	.L1603-.L1602
	.byte	3,1,1,5,26,9
	.half	.L1604-.L1603
	.byte	3,2,1,5,28,9
	.half	.L1605-.L1604
	.byte	3,1,1,5,26,9
	.half	.L1606-.L1605
	.byte	3,2,1,5,28,9
	.half	.L1607-.L1606
	.byte	3,1,1,5,26,9
	.half	.L1608-.L1607
	.byte	3,2,1,5,28,9
	.half	.L1609-.L1608
	.byte	3,1,1,5,26,9
	.half	.L1610-.L1609
	.byte	3,2,1,5,28,9
	.half	.L1611-.L1610
	.byte	3,1,1,5,26,9
	.half	.L1612-.L1611
	.byte	3,2,1,5,28,9
	.half	.L1613-.L1612
	.byte	3,1,1,5,26,9
	.half	.L1614-.L1613
	.byte	3,2,1,5,28,9
	.half	.L1615-.L1614
	.byte	3,1,1,5,26,9
	.half	.L1616-.L1615
	.byte	3,2,1,5,28,9
	.half	.L1617-.L1616
	.byte	3,1,1,5,26,9
	.half	.L1618-.L1617
	.byte	3,2,1,5,28,9
	.half	.L1619-.L1618
	.byte	3,1,1,9
	.half	.L1620-.L1619
	.byte	3,1,1,5,26,9
	.half	.L1621-.L1620
	.byte	3,2,1,5,28,9
	.half	.L1622-.L1621
	.byte	3,1,1,9
	.half	.L1623-.L1622
	.byte	3,1,1,9
	.half	.L1624-.L1623
	.byte	3,1,1,9
	.half	.L1625-.L1624
	.byte	3,1,1,9
	.half	.L1626-.L1625
	.byte	3,1,1,9
	.half	.L1627-.L1626
	.byte	3,1,1,9
	.half	.L1628-.L1627
	.byte	3,1,1,9
	.half	.L1629-.L1628
	.byte	3,1,1,9
	.half	.L1630-.L1629
	.byte	3,1,1,9
	.half	.L1631-.L1630
	.byte	3,1,1,9
	.half	.L1632-.L1631
	.byte	3,1,1,9
	.half	.L1633-.L1632
	.byte	3,1,1,9
	.half	.L1634-.L1633
	.byte	3,1,1,9
	.half	.L1635-.L1634
	.byte	3,1,1,5,26,9
	.half	.L1636-.L1635
	.byte	3,2,1,5,28,9
	.half	.L1637-.L1636
	.byte	3,1,1,9
	.half	.L1638-.L1637
	.byte	3,1,1,9
	.half	.L1639-.L1638
	.byte	3,1,1,9
	.half	.L1640-.L1639
	.byte	3,1,1,9
	.half	.L1641-.L1640
	.byte	3,1,1,9
	.half	.L1642-.L1641
	.byte	3,1,1,9
	.half	.L1643-.L1642
	.byte	3,1,1,9
	.half	.L1644-.L1643
	.byte	3,1,1,9
	.half	.L1645-.L1644
	.byte	3,1,1,9
	.half	.L1646-.L1645
	.byte	3,1,1,9
	.half	.L1647-.L1646
	.byte	3,1,1,9
	.half	.L1648-.L1647
	.byte	3,1,1,9
	.half	.L1649-.L1648
	.byte	3,1,1,9
	.half	.L1650-.L1649
	.byte	3,1,1,5,26,9
	.half	.L1651-.L1650
	.byte	3,2,1,9
	.half	.L1652-.L1651
	.byte	3,2,1,5,27,9
	.half	.L1653-.L1652
	.byte	3,1,1,5,5,9
	.half	.L1654-.L1653
	.byte	1,5,9,7,9
	.half	.L1655-.L1654
	.byte	3,2,1,5,17,9
	.half	.L274-.L1655
	.byte	3,3,1,5,22,9
	.half	.L1656-.L274
	.byte	3,1,1,5,1,9
	.half	.L1657-.L1656
	.byte	3,1,1,7,9
	.half	.L422-.L1657
	.byte	0,1,1
.L1475:
	.sdecl	'.debug_ranges',debug,cluster('ips200_init')
	.sect	'.debug_ranges'
.L421:
	.word	-1,.L328,0,.L422-.L328,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_data')
	.sect	'.debug_info'
.L423:
	.word	274
	.half	3
	.word	.L424
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L426,.L425
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_data',0,1,171,1,13,1,1
	.word	.L278,.L668,.L277
	.byte	4
	.byte	'dat',0,1,171,1,37
	.word	.L531,.L669
	.byte	5
	.word	.L278,.L668
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_data')
	.sect	'.debug_abbrev'
.L424:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_data')
	.sect	'.debug_line'
.L425:
	.word	.L1659-.L1658
.L1658:
	.half	3
	.word	.L1661-.L1660
.L1660:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1661:
	.byte	5,5,7,0,5,2
	.word	.L278
	.byte	3,172,1,1,5,29,9
	.half	.L1662-.L278
	.byte	1,5,52,9
	.half	.L1663-.L1662
	.byte	1,5,69,9
	.half	.L1664-.L1663
	.byte	1,5,49,9
	.half	.L1665-.L1664
	.byte	1,5,22,9
	.half	.L1666-.L1665
	.byte	1,5,5,9
	.half	.L1667-.L1666
	.byte	3,1,1,5,29,9
	.half	.L1668-.L1667
	.byte	1,5,57,9
	.half	.L694-.L1668
	.byte	1,5,74,9
	.half	.L1669-.L694
	.byte	1,5,54,9
	.half	.L1670-.L1669
	.byte	1,5,22,9
	.half	.L1671-.L1670
	.byte	1,5,1,9
	.half	.L1672-.L1671
	.byte	3,1,1,7,9
	.half	.L427-.L1672
	.byte	0,1,1
.L1659:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_data')
	.sect	'.debug_ranges'
.L426:
	.word	-1,.L278,0,.L427-.L278,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_command')
	.sect	'.debug_info'
.L428:
	.word	281
	.half	3
	.word	.L429
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L431,.L430
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_command',0,1,185,1,13,1,1
	.word	.L280,.L670,.L279
	.byte	4
	.byte	'command',0,1,185,1,47
	.word	.L671,.L672
	.byte	5
	.word	.L280,.L670
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_command')
	.sect	'.debug_abbrev'
.L429:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_command')
	.sect	'.debug_line'
.L430:
	.word	.L1674-.L1673
.L1673:
	.half	3
	.word	.L1676-.L1675
.L1675:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1676:
	.byte	5,13,7,0,5,2
	.word	.L280
	.byte	3,184,1,1,5,27,9
	.half	.L696-.L280
	.byte	3,2,1,5,5,9
	.half	.L1677-.L696
	.byte	1,5,9,7,9
	.half	.L1678-.L1677
	.byte	3,2,1,9
	.half	.L4-.L1678
	.byte	3,1,1,9
	.half	.L699-.L4
	.byte	3,1,1,9
	.half	.L2-.L699
	.byte	3,4,1,9
	.half	.L9-.L2
	.byte	3,1,1,9
	.half	.L11-.L9
	.byte	3,1,1,9
	.half	.L13-.L11
	.byte	3,1,1,5,27,9
	.half	.L15-.L13
	.byte	3,1,1,5,9,9
	.half	.L703-.L15
	.byte	3,1,1,9
	.half	.L17-.L703
	.byte	3,1,1,9
	.half	.L19-.L17
	.byte	3,1,1,5,1,9
	.half	.L7-.L19
	.byte	3,2,1,7,9
	.half	.L432-.L7
	.byte	0,1,1
.L1674:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_command')
	.sect	'.debug_ranges'
.L431:
	.word	-1,.L280,0,.L432-.L280,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_info'
.L433:
	.word	279
	.half	3
	.word	.L434
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L436,.L435
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_8bit_data',0,1,213,1,13,1,1
	.word	.L282,.L673,.L281
	.byte	4
	.byte	'dat',0,1,213,1,49
	.word	.L674,.L675
	.byte	5
	.word	.L282,.L673
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_abbrev'
.L434:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_line'
.L435:
	.word	.L1680-.L1679
.L1679:
	.half	3
	.word	.L1682-.L1681
.L1681:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1682:
	.byte	5,13,7,0,5,2
	.word	.L282
	.byte	3,212,1,1,5,27,9
	.half	.L705-.L282
	.byte	3,2,1,5,5,9
	.half	.L1683-.L705
	.byte	1,5,9,7,9
	.half	.L1684-.L1683
	.byte	3,2,1,5,40,9
	.half	.L707-.L1684
	.byte	1,5,9,9
	.half	.L22-.L707
	.byte	3,4,1,9
	.half	.L25-.L22
	.byte	3,1,1,9
	.half	.L27-.L25
	.byte	3,1,1,5,27,9
	.half	.L29-.L27
	.byte	3,1,1,5,9,9
	.half	.L711-.L29
	.byte	3,1,1,9
	.half	.L31-.L711
	.byte	3,1,1,5,1,9
	.half	.L23-.L31
	.byte	3,2,1,7,9
	.half	.L437-.L23
	.byte	0,1,1
.L1680:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_ranges'
.L436:
	.word	-1,.L282,0,.L437-.L282,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_info'
.L438:
	.word	302
	.half	3
	.word	.L439
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L441,.L440
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_8bit_data_array',0,1,236,1,13,1,1
	.word	.L284,.L676,.L283
	.byte	4
	.byte	'dat',0,1,236,1,56
	.word	.L591,.L677
	.byte	4
	.byte	'len',0,1,236,1,68
	.word	.L571,.L678
	.byte	5
	.word	.L284,.L676
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_abbrev'
.L439:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_line'
.L440:
	.word	.L1686-.L1685
.L1685:
	.half	3
	.word	.L1688-.L1687
.L1687:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1688:
	.byte	5,13,7,0,5,2
	.word	.L284
	.byte	3,235,1,1,5,27,9
	.half	.L715-.L284
	.byte	3,2,1,5,5,9
	.half	.L1689-.L715
	.byte	1,5,9,7,9
	.half	.L1690-.L1689
	.byte	3,2,1,5,51,9
	.half	.L712-.L1690
	.byte	1,5,9,9
	.half	.L34-.L712
	.byte	3,4,1,9
	.half	.L37-.L34
	.byte	3,1,1,5,21,9
	.half	.L39-.L37
	.byte	3,1,1,5,13,9
	.half	.L41-.L39
	.byte	3,2,1,5,38,9
	.half	.L43-.L41
	.byte	3,1,1,5,13,9
	.half	.L1691-.L43
	.byte	3,1,1,5,17,9
	.half	.L45-.L1691
	.byte	3,1,1,5,19,9
	.half	.L40-.L45
	.byte	3,123,1,5,21,9
	.half	.L1692-.L40
	.byte	1,5,9,7,9
	.half	.L1693-.L1692
	.byte	3,7,1,5,1,9
	.half	.L35-.L1693
	.byte	3,2,1,7,9
	.half	.L442-.L35
	.byte	0,1,1
.L1686:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_ranges'
.L441:
	.word	-1,.L284,0,.L442-.L284,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_info'
.L443:
	.word	281
	.half	3
	.word	.L444
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L446,.L445
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_16bit_data',0,1,137,2,6,1,1,1
	.word	.L286,.L679,.L285
	.byte	4
	.byte	'dat',0,1,137,2,44
	.word	.L680,.L681
	.byte	5
	.word	.L286,.L679
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_abbrev'
.L444:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_line'
.L445:
	.word	.L1695-.L1694
.L1694:
	.half	3
	.word	.L1697-.L1696
.L1696:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1697:
	.byte	5,6,7,0,5,2
	.word	.L286
	.byte	3,136,2,1,5,27,9
	.half	.L722-.L286
	.byte	3,2,1,5,5,9
	.half	.L1698-.L722
	.byte	1,5,9,7,9
	.half	.L1699-.L1698
	.byte	3,2,1,5,41,9
	.half	.L724-.L1699
	.byte	1,5,9,9
	.half	.L48-.L724
	.byte	3,4,1,9
	.half	.L51-.L48
	.byte	3,1,1,9
	.half	.L53-.L51
	.byte	3,1,1,5,39,9
	.half	.L55-.L53
	.byte	3,1,1,5,27,9
	.half	.L1700-.L55
	.byte	1,5,9,9
	.half	.L1701-.L1700
	.byte	3,1,1,9
	.half	.L57-.L1701
	.byte	3,1,1,5,39,9
	.half	.L59-.L57
	.byte	3,1,1,5,27,9
	.half	.L1702-.L59
	.byte	1,5,9,9
	.half	.L1703-.L1702
	.byte	3,1,1,9
	.half	.L61-.L1703
	.byte	3,1,1,5,1,9
	.half	.L49-.L61
	.byte	3,2,1,7,9
	.half	.L447-.L49
	.byte	0,1,1
.L1695:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_ranges'
.L446:
	.word	-1,.L286,0,.L447-.L286,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_info'
.L448:
	.word	303
	.half	3
	.word	.L449
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L451,.L450
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_write_16bit_data_array',0,1,164,2,13,1,1
	.word	.L288,.L682,.L287
	.byte	4
	.byte	'dat',0,1,164,2,58
	.word	.L623,.L683
	.byte	4
	.byte	'len',0,1,164,2,70
	.word	.L571,.L684
	.byte	5
	.word	.L288,.L682
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_abbrev'
.L449:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_line'
.L450:
	.word	.L1705-.L1704
.L1704:
	.half	3
	.word	.L1707-.L1706
.L1706:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1707:
	.byte	5,13,7,0,5,2
	.word	.L288
	.byte	3,163,2,1,5,27,9
	.half	.L730-.L288
	.byte	3,2,1,5,5,9
	.half	.L1708-.L730
	.byte	1,5,9,7,9
	.half	.L1709-.L1708
	.byte	3,2,1,5,52,9
	.half	.L727-.L1709
	.byte	1,5,9,9
	.half	.L64-.L727
	.byte	3,4,1,9
	.half	.L67-.L64
	.byte	3,1,1,5,21,9
	.half	.L69-.L67
	.byte	3,1,1,5,13,9
	.half	.L71-.L69
	.byte	3,2,1,5,39,9
	.half	.L73-.L71
	.byte	3,1,1,5,44,9
	.half	.L1710-.L73
	.byte	1,5,31,9
	.half	.L1711-.L1710
	.byte	1,5,13,9
	.half	.L1712-.L1711
	.byte	3,1,1,9
	.half	.L75-.L1712
	.byte	3,1,1,5,39,9
	.half	.L77-.L75
	.byte	3,1,1,5,44,9
	.half	.L1713-.L77
	.byte	1,5,31,9
	.half	.L1714-.L1713
	.byte	1,5,13,9
	.half	.L1715-.L1714
	.byte	3,1,1,5,17,9
	.half	.L79-.L1715
	.byte	3,1,1,5,19,9
	.half	.L70-.L79
	.byte	3,120,1,5,21,9
	.half	.L1716-.L70
	.byte	1,5,9,7,9
	.half	.L1717-.L1716
	.byte	3,10,1,5,1,9
	.half	.L65-.L1717
	.byte	3,2,1,7,9
	.half	.L452-.L65
	.byte	0,1,1
.L1705:
	.sdecl	'.debug_ranges',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_ranges'
.L451:
	.word	-1,.L288,0,.L452-.L288,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_set_region')
	.sect	'.debug_info'
.L453:
	.word	321
	.half	3
	.word	.L454
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L456,.L455
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_set_region',0,1,197,2,13,1,1
	.word	.L290,.L685,.L289
	.byte	4
	.byte	'x1',0,1,197,2,39
	.word	.L486,.L686
	.byte	4
	.byte	'y1',0,1,197,2,50
	.word	.L486,.L687
	.byte	4
	.byte	'x2',0,1,197,2,61
	.word	.L486,.L688
	.byte	4
	.byte	'y2',0,1,197,2,72
	.word	.L486,.L689
	.byte	5
	.word	.L290,.L685
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_set_region')
	.sect	'.debug_abbrev'
.L454:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_set_region')
	.sect	'.debug_line'
.L455:
	.word	.L1719-.L1718
.L1718:
	.half	3
	.word	.L1721-.L1720
.L1720:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1721:
	.byte	5,13,7,0,5,2
	.word	.L290
	.byte	3,196,2,1,5,5,9
	.half	.L740-.L290
	.byte	3,5,1,9
	.half	.L737-.L740
	.byte	3,1,1,9
	.half	.L1722-.L737
	.byte	3,1,1,9
	.half	.L1723-.L1722
	.byte	3,1,1,5,26,9
	.half	.L1724-.L1723
	.byte	3,2,1,5,29,9
	.half	.L1725-.L1724
	.byte	3,1,1,9
	.half	.L746-.L1725
	.byte	3,1,1,5,26,9
	.half	.L748-.L746
	.byte	3,2,1,5,29,9
	.half	.L749-.L748
	.byte	3,1,1,9
	.half	.L751-.L749
	.byte	3,1,1,5,26,9
	.half	.L753-.L751
	.byte	3,2,1,5,1,9
	.half	.L1726-.L753
	.byte	3,1,1,7,9
	.half	.L457-.L1726
	.byte	0,1,1
.L1719:
	.sdecl	'.debug_ranges',debug,cluster('ips200_set_region')
	.sect	'.debug_ranges'
.L456:
	.word	-1,.L290,0,.L457-.L290,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_debug_init')
	.sect	'.debug_info'
.L458:
	.word	276
	.half	3
	.word	.L459
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L461,.L460
	.byte	2
	.word	.L329
	.byte	3
	.byte	'ips200_debug_init',0,1,225,2,13,1,1
	.word	.L292,.L690,.L291
	.byte	4
	.word	.L292,.L690
	.byte	5
	.byte	'info',0,1,227,2,25
	.word	.L691,.L692
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_debug_init')
	.sect	'.debug_abbrev'
.L459:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('ips200_debug_init')
	.sect	'.debug_line'
.L460:
	.word	.L1728-.L1727
.L1727:
	.half	3
	.word	.L1730-.L1729
.L1729:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_device/zf_device_ips200.c',0,0,0,0,0
.L1730:
	.byte	5,13,7,0,5,2
	.word	.L292
	.byte	3,224,2,1,5,31,9
	.half	.L754-.L292
	.byte	3,3,1,5,23,9
	.half	.L1731-.L754
	.byte	3,2,1,5,21,9
	.half	.L1732-.L1731
	.byte	1,5,26,9
	.half	.L1733-.L1732
	.byte	3,1,1,5,24,9
	.half	.L1734-.L1733
	.byte	1,5,26,9
	.half	.L1735-.L1734
	.byte	3,1,1,5,24,9
	.half	.L1736-.L1735
	.byte	1,5,12,9
	.half	.L1737-.L1736
	.byte	3,2,1,5,14,9
	.half	.L1738-.L1737
	.byte	3,2,1,9
	.half	.L1739-.L1738
	.byte	3,5,1,9
	.half	.L1740-.L1739
	.byte	3,5,1,5,32,9
	.half	.L82-.L1740
	.byte	3,120,1,5,30,9
	.half	.L1741-.L82
	.byte	1,5,32,9
	.half	.L1742-.L1741
	.byte	3,1,1,5,30,9
	.half	.L1743-.L1742
	.byte	1,5,10,9
	.half	.L1744-.L1743
	.byte	3,1,1,5,32,9
	.half	.L83-.L1744
	.byte	3,3,1,5,30,9
	.half	.L1745-.L83
	.byte	1,5,32,9
	.half	.L1746-.L1745
	.byte	3,1,1,5,30,9
	.half	.L1747-.L1746
	.byte	1,5,10,9
	.half	.L1748-.L1747
	.byte	3,1,1,9
	.half	.L84-.L1748
	.byte	3,4,1,5,26,9
	.half	.L86-.L84
	.byte	3,2,1,5,24,9
	.half	.L1749-.L86
	.byte	1,5,32,9
	.half	.L1750-.L1749
	.byte	3,1,1,5,30,9
	.half	.L1751-.L1750
	.byte	1,5,24,9
	.half	.L1752-.L1751
	.byte	3,2,1,5,1,9
	.half	.L1753-.L1752
	.byte	3,1,1,7,9
	.half	.L462-.L1753
	.byte	0,1,1
.L1728:
	.sdecl	'.debug_ranges',debug,cluster('ips200_debug_init')
	.sect	'.debug_ranges'
.L461:
	.word	-1,.L292,0,.L462-.L292,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_width_max')
	.sect	'.debug_info'
.L463:
	.word	234
	.half	3
	.word	.L464
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_width_max',0,12,74,33
	.word	.L486
	.byte	1,5,3
	.word	ips200_width_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_width_max')
	.sect	'.debug_abbrev'
.L464:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_height_max')
	.sect	'.debug_info'
.L465:
	.word	235
	.half	3
	.word	.L466
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_height_max',0,12,75,33
	.word	.L486
	.byte	1,5,3
	.word	ips200_height_max
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_height_max')
	.sect	'.debug_abbrev'
.L466:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_pencolor')
	.sect	'.debug_info'
.L467:
	.word	232
	.half	3
	.word	.L468
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_pencolor',0,12,76,33
	.word	.L486
	.byte	5,3
	.word	ips200_pencolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_pencolor')
	.sect	'.debug_abbrev'
.L468:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_bgcolor')
	.sect	'.debug_info'
.L469:
	.word	231
	.half	3
	.word	.L470
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_bgcolor',0,12,77,33
	.word	.L486
	.byte	5,3
	.word	ips200_bgcolor
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_bgcolor')
	.sect	'.debug_abbrev'
.L470:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_display_type')
	.sect	'.debug_info'
.L471:
	.word	236
	.half	3
	.word	.L472
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_display_type',0,12,78,33
	.word	.L666
	.byte	5,3
	.word	ips200_display_type
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_display_type')
	.sect	'.debug_abbrev'
.L472:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_display_dir')
	.sect	'.debug_info'
.L473:
	.word	235
	.half	3
	.word	.L474
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_display_dir',0,12,79,33
	.word	.L496
	.byte	5,3
	.word	ips200_display_dir
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_display_dir')
	.sect	'.debug_abbrev'
.L474:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips200_display_font')
	.sect	'.debug_info'
.L475:
	.word	236
	.half	3
	.word	.L476
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips200_display_font',0,12,80,33
	.word	.L499
	.byte	5,3
	.word	ips200_display_font
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips200_display_font')
	.sect	'.debug_abbrev'
.L476:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips_rst_pin')
	.sect	'.debug_info'
.L477:
	.word	228
	.half	3
	.word	.L478
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips_rst_pin',0,12,82,33
	.word	.L693
	.byte	5,3
	.word	ips_rst_pin
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips_rst_pin')
	.sect	'.debug_abbrev'
.L478:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips_bl_pin')
	.sect	'.debug_info'
.L479:
	.word	227
	.half	3
	.word	.L480
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips_bl_pin',0,12,83,33
	.word	.L693
	.byte	5,3
	.word	ips_bl_pin
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips_bl_pin')
	.sect	'.debug_abbrev'
.L480:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('ips_cs_pin')
	.sect	'.debug_info'
.L481:
	.word	227
	.half	3
	.word	.L482
	.byte	4,1
	.byte	'../libraries/zf_device/zf_device_ips200.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L329
	.byte	3
	.byte	'ips_cs_pin',0,12,84,33
	.word	.L693
	.byte	5,3
	.word	ips_cs_pin
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('ips_cs_pin')
	.sect	'.debug_abbrev'
.L482:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_clear')
	.sect	'.debug_loc'
.L485:
	.word	-1,.L294,.L755-.L294,.L756-.L294
	.half	1
	.byte	98
	.word	.L757-.L294,.L483-.L294
	.half	1
	.byte	108
	.word	.L90-.L294,.L758-.L294
	.half	1
	.byte	98
	.word	.L763-.L294,.L764-.L294
	.half	1
	.byte	100
	.word	.L766-.L294,.L767-.L294
	.half	1
	.byte	100
	.word	0,0
.L487:
	.word	-1,.L294,.L759-.L294,.L760-.L294
	.half	5
	.byte	144,32,157,32,0
	.word	.L92-.L294,.L95-.L294
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L293:
	.word	-1,.L294,0,.L483-.L294
	.half	2
	.byte	138,0
	.word	0,0
.L488:
	.word	-1,.L294,.L761-.L294,.L762-.L294
	.half	1
	.byte	95
	.word	.L94-.L294,.L765-.L294
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_debug_init')
	.sect	'.debug_loc'
.L692:
	.word	-1,.L292,0,.L690-.L292
	.half	2
	.byte	145,104
	.word	0,0
.L291:
	.word	-1,.L292,0,.L754-.L292
	.half	2
	.byte	138,0
	.word	.L754-.L292,.L690-.L292
	.half	2
	.byte	138,24
	.word	.L690-.L292,.L690-.L292
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_draw_line')
	.sect	'.debug_loc'
.L517:
	.word	-1,.L306,0,.L511-.L306
	.half	2
	.byte	145,0
	.word	.L800-.L306,.L511-.L306
	.half	1
	.byte	91
	.word	.L816-.L306,.L817-.L306
	.half	1
	.byte	86
	.word	.L820-.L306,.L821-.L306
	.half	1
	.byte	86
	.word	.L831-.L306,.L832-.L306
	.half	1
	.byte	86
	.word	.L836-.L306,.L837-.L306
	.half	1
	.byte	86
	.word	.L839-.L306,.L840-.L306
	.half	1
	.byte	86
	.word	.L846-.L306,.L131-.L306
	.half	1
	.byte	86
	.word	0,0
.L305:
	.word	-1,.L306,0,.L793-.L306
	.half	2
	.byte	138,0
	.word	.L793-.L306,.L511-.L306
	.half	2
	.byte	138,8
	.word	.L511-.L306,.L511-.L306
	.half	2
	.byte	138,0
	.word	0,0
.L524:
	.word	-1,.L306,.L814-.L306,.L127-.L306
	.half	1
	.byte	92
	.word	.L128-.L306,.L131-.L306
	.half	1
	.byte	92
	.word	0,0
.L523:
	.word	-1,.L306,.L812-.L306,.L127-.L306
	.half	1
	.byte	93
	.word	.L128-.L306,.L131-.L306
	.half	1
	.byte	93
	.word	0,0
.L520:
	.word	-1,.L306,.L841-.L306,.L842-.L306
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L514:
	.word	-1,.L306,0,.L794-.L306
	.half	1
	.byte	86
	.word	.L803-.L306,.L804-.L306
	.half	1
	.byte	89
	.word	.L518-.L306,.L805-.L306
	.half	1
	.byte	89
	.word	.L126-.L306,.L808-.L306
	.half	1
	.byte	89
	.word	.L810-.L306,.L811-.L306
	.half	1
	.byte	89
	.word	.L130-.L306,.L815-.L306
	.half	1
	.byte	89
	.word	.L818-.L306,.L819-.L306
	.half	1
	.byte	89
	.word	.L133-.L306,.L826-.L306
	.half	1
	.byte	89
	.word	.L827-.L306,.L828-.L306
	.half	1
	.byte	89
	.word	.L134-.L306,.L829-.L306
	.half	1
	.byte	89
	.word	.L140-.L306,.L844-.L306
	.half	1
	.byte	89
	.word	0,0
.L512:
	.word	-1,.L306,0,.L795-.L306
	.half	1
	.byte	84
	.word	.L797-.L306,.L798-.L306
	.half	1
	.byte	95
	.word	.L137-.L306,.L835-.L306
	.half	1
	.byte	95
	.word	.L843-.L306,.L511-.L306
	.half	1
	.byte	95
	.word	0,0
.L521:
	.word	-1,.L306,.L807-.L306,.L124-.L306
	.half	1
	.byte	94
	.word	.L126-.L306,.L511-.L306
	.half	1
	.byte	94
	.word	0,0
.L515:
	.word	-1,.L306,0,.L794-.L306
	.half	1
	.byte	87
	.word	.L799-.L306,.L511-.L306
	.half	1
	.byte	90
	.word	0,0
.L513:
	.word	-1,.L306,0,.L796-.L306
	.half	1
	.byte	85
	.word	.L801-.L306,.L802-.L306
	.half	1
	.byte	88
	.word	.L123-.L306,.L806-.L306
	.half	1
	.byte	88
	.word	.L808-.L306,.L809-.L306
	.half	1
	.byte	88
	.word	.L812-.L306,.L813-.L306
	.half	1
	.byte	88
	.word	.L130-.L306,.L815-.L306
	.half	1
	.byte	88
	.word	.L129-.L306,.L818-.L306
	.half	1
	.byte	88
	.word	.L128-.L306,.L822-.L306
	.half	1
	.byte	88
	.word	.L823-.L306,.L824-.L306
	.half	1
	.byte	88
	.word	.L132-.L306,.L825-.L306
	.half	1
	.byte	88
	.word	.L138-.L306,.L830-.L306
	.half	1
	.byte	88
	.word	.L833-.L306,.L834-.L306
	.half	1
	.byte	88
	.word	.L141-.L306,.L838-.L306
	.half	1
	.byte	88
	.word	.L845-.L306,.L844-.L306
	.half	1
	.byte	88
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_draw_point')
	.sect	'.debug_loc'
.L510:
	.word	-1,.L304,0,.L783-.L304
	.half	1
	.byte	86
	.word	.L786-.L304,.L506-.L304
	.half	1
	.byte	90
	.word	.L791-.L304,.L792-.L304
	.half	1
	.byte	84
	.word	0,0
.L303:
	.word	-1,.L304,0,.L506-.L304
	.half	2
	.byte	138,0
	.word	0,0
.L507:
	.word	-1,.L304,0,.L784-.L304
	.half	1
	.byte	84
	.word	.L787-.L304,.L784-.L304
	.half	1
	.byte	88
	.word	.L116-.L304,.L790-.L304
	.half	1
	.byte	88
	.word	0,0
.L508:
	.word	-1,.L304,0,.L785-.L304
	.half	1
	.byte	85
	.word	.L788-.L304,.L789-.L304
	.half	1
	.byte	89
	.word	.L116-.L304,.L790-.L304
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_full')
	.sect	'.debug_loc'
.L491:
	.word	-1,.L296,0,.L768-.L296
	.half	1
	.byte	84
	.word	.L769-.L296,.L489-.L296
	.half	1
	.byte	88
	.word	0,0
.L492:
	.word	-1,.L296,.L770-.L296,.L771-.L296
	.half	1
	.byte	98
	.word	.L772-.L296,.L489-.L296
	.half	1
	.byte	108
	.word	.L100-.L296,.L773-.L296
	.half	1
	.byte	98
	.word	.L778-.L296,.L779-.L296
	.half	1
	.byte	100
	.word	.L781-.L296,.L782-.L296
	.half	1
	.byte	100
	.word	0,0
.L493:
	.word	-1,.L296,.L774-.L296,.L775-.L296
	.half	5
	.byte	144,32,157,32,0
	.word	.L102-.L296,.L105-.L296
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L295:
	.word	-1,.L296,0,.L489-.L296
	.half	2
	.byte	138,0
	.word	0,0
.L494:
	.word	-1,.L296,.L776-.L296,.L777-.L296
	.half	1
	.byte	95
	.word	.L104-.L296,.L780-.L296
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_init')
	.sect	'.debug_loc'
.L327:
	.word	-1,.L328,0,.L1125-.L328
	.half	2
	.byte	138,0
	.word	.L1125-.L328,.L665-.L328
	.half	2
	.byte	138,16
	.word	.L665-.L328,.L665-.L328
	.half	2
	.byte	138,0
	.word	0,0
.L667:
	.word	-1,.L328,0,.L1126-.L328
	.half	1
	.byte	84
	.word	.L254-.L328,.L1127-.L328
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_set_color')
	.sect	'.debug_loc'
.L505:
	.word	-1,.L302,0,.L501-.L302
	.half	1
	.byte	85
	.word	0,0
.L301:
	.word	-1,.L302,0,.L501-.L302
	.half	2
	.byte	138,0
	.word	0,0
.L503:
	.word	-1,.L302,0,.L501-.L302
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_set_dir')
	.sect	'.debug_loc'
.L497:
	.word	-1,.L298,0,.L495-.L298
	.half	1
	.byte	84
	.word	0,0
.L297:
	.word	-1,.L298,0,.L495-.L298
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_set_font')
	.sect	'.debug_loc'
.L500:
	.word	-1,.L300,0,.L498-.L300
	.half	1
	.byte	84
	.word	0,0
.L299:
	.word	-1,.L300,0,.L498-.L300
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_set_region')
	.sect	'.debug_loc'
.L289:
	.word	-1,.L290,0,.L685-.L290
	.half	2
	.byte	138,0
	.word	0,0
.L686:
	.word	-1,.L290,0,.L736-.L290
	.half	1
	.byte	84
	.word	.L739-.L290,.L685-.L290
	.half	1
	.byte	95
	.word	.L745-.L290,.L746-.L290
	.half	1
	.byte	84
	.word	0,0
.L688:
	.word	-1,.L290,0,.L737-.L290
	.half	1
	.byte	86
	.word	.L743-.L290,.L744-.L290
	.half	1
	.byte	89
	.word	.L746-.L290,.L747-.L290
	.half	1
	.byte	89
	.word	.L747-.L290,.L748-.L290
	.half	1
	.byte	84
	.word	0,0
.L687:
	.word	-1,.L290,0,.L738-.L290
	.half	1
	.byte	85
	.word	.L741-.L290,.L742-.L290
	.half	1
	.byte	88
	.word	.L749-.L290,.L750-.L290
	.half	1
	.byte	88
	.word	.L750-.L290,.L751-.L290
	.half	1
	.byte	84
	.word	0,0
.L689:
	.word	-1,.L290,0,.L737-.L290
	.half	1
	.byte	87
	.word	.L740-.L290,.L685-.L290
	.half	1
	.byte	90
	.word	.L752-.L290,.L753-.L290
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_loc'
.L602:
	.word	-1,.L318,.L957-.L318,.L958-.L318
	.half	1
	.byte	98
	.word	.L959-.L318,.L588-.L318
	.half	1
	.byte	109
	.word	.L189-.L318,.L960-.L318
	.half	1
	.byte	98
	.word	.L978-.L318,.L979-.L318
	.half	1
	.byte	100
	.word	.L980-.L318,.L981-.L318
	.half	1
	.byte	100
	.word	0,0
.L596:
	.word	-1,.L318,0,.L588-.L318
	.half	2
	.byte	145,4
	.word	.L953-.L318,.L588-.L318
	.half	1
	.byte	93
	.word	0,0
.L595:
	.word	-1,.L318,0,.L588-.L318
	.half	2
	.byte	145,0
	.word	.L952-.L318,.L588-.L318
	.half	1
	.byte	92
	.word	.L972-.L318,.L979-.L318
	.half	1
	.byte	84
	.word	0,0
.L594:
	.word	-1,.L318,0,.L947-.L318
	.half	1
	.byte	87
	.word	.L192-.L318,.L967-.L318
	.half	1
	.byte	91
	.word	0,0
.L598:
	.word	-1,.L318,.L971-.L318,.L972-.L318
	.half	1
	.byte	84
	.word	0,0
.L592:
	.word	-1,.L318,0,.L948-.L318
	.half	1
	.byte	100
	.word	.L951-.L318,.L588-.L318
	.half	1
	.byte	108
	.word	0,0
.L603:
	.word	-1,.L318,.L970-.L318,.L191-.L318
	.half	1
	.byte	111
	.word	0,0
.L317:
	.word	-1,.L318,0,.L588-.L318
	.half	2
	.byte	138,0
	.word	0,0
.L599:
	.word	-1,.L318,.L966-.L318,.L588-.L318
	.half	1
	.byte	88
	.word	0,0
.L600:
	.word	-1,.L318,.L976-.L318,.L977-.L318
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L593:
	.word	-1,.L318,0,.L947-.L318
	.half	1
	.byte	86
	.word	.L968-.L318,.L969-.L318
	.half	1
	.byte	90
	.word	.L194-.L318,.L973-.L318
	.half	1
	.byte	90
	.word	0,0
.L601:
	.word	-1,.L318,.L974-.L318,.L975-.L318
	.half	1
	.byte	82
	.word	0,0
.L589:
	.word	-1,.L318,0,.L949-.L318
	.half	1
	.byte	84
	.word	.L954-.L318,.L949-.L318
	.half	1
	.byte	88
	.word	.L188-.L318,.L961-.L318
	.half	1
	.byte	88
	.word	.L964-.L318,.L965-.L318
	.half	1
	.byte	88
	.word	0,0
.L590:
	.word	-1,.L318,0,.L950-.L318
	.half	1
	.byte	85
	.word	.L955-.L318,.L956-.L318
	.half	1
	.byte	89
	.word	.L962-.L318,.L963-.L318
	.half	1
	.byte	89
	.word	.L964-.L318,.L965-.L318
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_char')
	.sect	'.debug_loc'
.L529:
	.word	-1,.L308,0,.L848-.L308
	.half	1
	.byte	86
	.word	.L851-.L308,.L525-.L308
	.half	1
	.byte	90
	.word	0,0
.L536:
	.word	-1,.L308,0,.L525-.L308
	.half	3
	.byte	145,128,126
	.word	0,0
.L541:
	.word	-1,.L308,0,.L525-.L308
	.half	3
	.byte	145,128,126
	.word	0,0
.L532:
	.word	-1,.L308,.L860-.L308,.L534-.L308
	.half	5
	.byte	144,32,157,32,0
	.word	.L868-.L308,.L539-.L308
	.half	1
	.byte	83
	.word	0,0
.L307:
	.word	-1,.L308,0,.L847-.L308
	.half	2
	.byte	138,0
	.word	.L847-.L308,.L525-.L308
	.half	3
	.byte	138,128,2
	.word	.L525-.L308,.L525-.L308
	.half	2
	.byte	138,0
	.word	0,0
.L533:
	.word	-1,.L308,.L862-.L308,.L149-.L308
	.half	1
	.byte	82
	.word	.L872-.L308,.L870-.L308
	.half	1
	.byte	81
	.word	.L870-.L308,.L156-.L308
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L544:
	.word	-1,.L308,.L871-.L308,.L156-.L308
	.half	1
	.byte	84
	.word	0,0
.L538:
	.word	-1,.L308,.L861-.L308,.L149-.L308
	.half	1
	.byte	81
	.word	0,0
.L543:
	.word	-1,.L308,.L869-.L308,.L870-.L308
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L526:
	.word	-1,.L308,0,.L849-.L308
	.half	1
	.byte	84
	.word	.L852-.L308,.L849-.L308
	.half	1
	.byte	88
	.word	.L145-.L308,.L855-.L308
	.half	1
	.byte	88
	.word	.L858-.L308,.L859-.L308
	.half	1
	.byte	88
	.word	.L146-.L308,.L863-.L308
	.half	1
	.byte	88
	.word	.L866-.L308,.L867-.L308
	.half	1
	.byte	88
	.word	0,0
.L527:
	.word	-1,.L308,0,.L850-.L308
	.half	1
	.byte	85
	.word	.L853-.L308,.L854-.L308
	.half	1
	.byte	89
	.word	.L856-.L308,.L857-.L308
	.half	1
	.byte	89
	.word	.L858-.L308,.L859-.L308
	.half	1
	.byte	89
	.word	.L864-.L308,.L865-.L308
	.half	1
	.byte	89
	.word	.L866-.L308,.L867-.L308
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_chinese')
	.sect	'.debug_loc'
.L653:
	.word	-1,.L326,0,.L1088-.L326
	.half	1
	.byte	100
	.word	.L1093-.L326,.L649-.L326
	.half	1
	.byte	108
	.word	0,0
.L656:
	.word	-1,.L326,0,.L649-.L326
	.half	2
	.byte	145,0
	.word	.L1095-.L326,.L649-.L326
	.half	1
	.byte	91
	.word	.L1117-.L326,.L1118-.L326
	.half	1
	.byte	84
	.word	0,0
.L658:
	.word	-1,.L326,.L1108-.L326,.L649-.L326
	.half	1
	.byte	88
	.word	0,0
.L325:
	.word	-1,.L326,0,.L1087-.L326
	.half	2
	.byte	138,0
	.word	.L1087-.L326,.L649-.L326
	.half	2
	.byte	138,8
	.word	.L649-.L326,.L649-.L326
	.half	2
	.byte	138,0
	.word	0,0
.L659:
	.word	-1,.L326,.L1115-.L326,.L245-.L326
	.half	1
	.byte	94
	.word	0,0
.L660:
	.word	-1,.L326,.L1114-.L326,.L243-.L326
	.half	1
	.byte	89
	.word	0,0
.L654:
	.word	-1,.L326,0,.L1089-.L326
	.half	1
	.byte	87
	.word	.L1094-.L326,.L649-.L326
	.half	2
	.byte	145,124
	.word	.L1100-.L326,.L1101-.L326
	.half	1
	.byte	95
	.word	.L1109-.L326,.L1110-.L326
	.half	1
	.byte	95
	.word	0,0
.L664:
	.word	-1,.L326,.L1112-.L326,.L1113-.L326
	.half	1
	.byte	111
	.word	.L1119-.L326,.L1120-.L326
	.half	5
	.byte	144,32,157,32,0
	.word	.L1120-.L326,.L243-.L326
	.half	1
	.byte	95
	.word	.L243-.L326,.L241-.L326
	.half	1
	.byte	111
	.word	0,0
.L652:
	.word	-1,.L326,0,.L1089-.L326
	.half	1
	.byte	86
	.word	.L1092-.L326,.L649-.L326
	.half	1
	.byte	90
	.word	0,0
.L661:
	.word	-1,.L326,.L1116-.L326,.L247-.L326
	.half	1
	.byte	95
	.word	0,0
.L662:
	.word	-1,.L326,.L1111-.L326,.L241-.L326
	.half	2
	.byte	145,120
	.word	.L244-.L326,.L246-.L326
	.half	5
	.byte	144,32,157,32,0
	.word	.L1121-.L326,.L1122-.L326
	.half	1
	.byte	95
	.word	.L1122-.L326,.L241-.L326
	.half	5
	.byte	144,32,157,32,0
	.word	.L1123-.L326,.L1124-.L326
	.half	1
	.byte	95
	.word	0,0
.L663:
	.word	-1,.L326,.L1099-.L326,.L649-.L326
	.half	1
	.byte	92
	.word	0,0
.L650:
	.word	-1,.L326,0,.L1090-.L326
	.half	1
	.byte	84
	.word	.L1096-.L326,.L1090-.L326
	.half	1
	.byte	88
	.word	.L1102-.L326,.L1103-.L326
	.half	1
	.byte	88
	.word	.L1106-.L326,.L1107-.L326
	.half	1
	.byte	88
	.word	0,0
.L651:
	.word	-1,.L326,0,.L1091-.L326
	.half	1
	.byte	85
	.word	.L1097-.L326,.L1098-.L326
	.half	1
	.byte	89
	.word	.L1104-.L326,.L1105-.L326
	.half	1
	.byte	89
	.word	.L1106-.L326,.L1107-.L326
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_float')
	.sect	'.debug_loc'
.L579:
	.word	-1,.L316,0,.L923-.L316
	.half	2
	.byte	144,35
	.word	.L934-.L316,.L935-.L316
	.half	2
	.byte	144,37
	.word	.L940-.L316,.L941-.L316
	.half	2
	.byte	144,37
	.word	0,0
.L584:
	.word	-1,.L316,.L942-.L316,.L943-.L316
	.half	2
	.byte	144,34
	.word	0,0
.L587:
	.word	-1,.L316,0,.L575-.L316
	.half	2
	.byte	145,104
	.word	0,0
.L315:
	.word	-1,.L316,0,.L922-.L316
	.half	2
	.byte	138,0
	.word	.L922-.L316,.L575-.L316
	.half	2
	.byte	138,24
	.word	.L575-.L316,.L575-.L316
	.half	2
	.byte	138,0
	.word	0,0
.L580:
	.word	-1,.L316,0,.L575-.L316
	.half	2
	.byte	145,0
	.word	.L928-.L316,.L575-.L316
	.half	1
	.byte	89
	.word	0,0
.L585:
	.word	-1,.L316,.L931-.L316,.L187-.L316
	.half	2
	.byte	144,38
	.word	.L932-.L316,.L933-.L316
	.half	2
	.byte	144,38
	.word	.L936-.L316,.L937-.L316
	.half	2
	.byte	144,38
	.word	.L938-.L316,.L939-.L316
	.half	2
	.byte	144,38
	.word	0,0
.L581:
	.word	-1,.L316,0,.L575-.L316
	.half	2
	.byte	145,4
	.word	.L929-.L316,.L575-.L316
	.half	1
	.byte	88
	.word	.L942-.L316,.L943-.L316
	.half	1
	.byte	86
	.word	0,0
.L576:
	.word	-1,.L316,0,.L924-.L316
	.half	1
	.byte	84
	.word	.L926-.L316,.L575-.L316
	.half	2
	.byte	145,124
	.word	.L924-.L316,.L930-.L316
	.half	1
	.byte	95
	.word	.L944-.L316,.L945-.L316
	.half	1
	.byte	84
	.word	0,0
.L577:
	.word	-1,.L316,0,.L925-.L316
	.half	1
	.byte	85
	.word	.L927-.L316,.L575-.L316
	.half	1
	.byte	94
	.word	.L946-.L316,.L945-.L316
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_loc'
.L616:
	.word	-1,.L320,.L1012-.L320,.L207-.L320
	.half	1
	.byte	81
	.word	0,0
.L618:
	.word	-1,.L320,.L993-.L320,.L994-.L320
	.half	1
	.byte	98
	.word	.L995-.L320,.L604-.L320
	.half	1
	.byte	109
	.word	.L201-.L320,.L996-.L320
	.half	1
	.byte	98
	.word	.L1013-.L320,.L1008-.L320
	.half	1
	.byte	100
	.word	.L1015-.L320,.L1016-.L320
	.half	1
	.byte	100
	.word	0,0
.L611:
	.word	-1,.L320,0,.L604-.L320
	.half	2
	.byte	145,4
	.word	.L988-.L320,.L604-.L320
	.half	1
	.byte	93
	.word	0,0
.L610:
	.word	-1,.L320,0,.L604-.L320
	.half	2
	.byte	145,0
	.word	.L987-.L320,.L604-.L320
	.half	1
	.byte	92
	.word	.L1014-.L320,.L1008-.L320
	.half	1
	.byte	84
	.word	0,0
.L609:
	.word	-1,.L320,0,.L982-.L320
	.half	1
	.byte	87
	.word	.L204-.L320,.L1003-.L320
	.half	1
	.byte	91
	.word	0,0
.L614:
	.word	-1,.L320,.L1007-.L320,.L1008-.L320
	.half	1
	.byte	82
	.word	0,0
.L607:
	.word	-1,.L320,0,.L983-.L320
	.half	1
	.byte	100
	.word	.L986-.L320,.L604-.L320
	.half	1
	.byte	108
	.word	0,0
.L619:
	.word	-1,.L320,.L1006-.L320,.L203-.L320
	.half	1
	.byte	111
	.word	0,0
.L319:
	.word	-1,.L320,0,.L604-.L320
	.half	2
	.byte	138,0
	.word	0,0
.L615:
	.word	-1,.L320,.L1002-.L320,.L604-.L320
	.half	1
	.byte	88
	.word	0,0
.L617:
	.word	-1,.L320,.L1010-.L320,.L1011-.L320
	.half	5
	.byte	144,32,157,32,0
	.word	.L207-.L320,.L208-.L320
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L612:
	.word	-1,.L320,0,.L604-.L320
	.half	2
	.byte	145,8
	.word	.L989-.L320,.L604-.L320
	.half	1
	.byte	94
	.word	0,0
.L608:
	.word	-1,.L320,0,.L982-.L320
	.half	1
	.byte	86
	.word	.L1004-.L320,.L1005-.L320
	.half	1
	.byte	90
	.word	.L206-.L320,.L1009-.L320
	.half	1
	.byte	90
	.word	0,0
.L605:
	.word	-1,.L320,0,.L984-.L320
	.half	1
	.byte	84
	.word	.L990-.L320,.L984-.L320
	.half	1
	.byte	88
	.word	.L200-.L320,.L997-.L320
	.half	1
	.byte	88
	.word	.L1000-.L320,.L1001-.L320
	.half	1
	.byte	88
	.word	0,0
.L606:
	.word	-1,.L320,0,.L985-.L320
	.half	1
	.byte	85
	.word	.L991-.L320,.L992-.L320
	.half	1
	.byte	89
	.word	.L998-.L320,.L999-.L320
	.half	1
	.byte	89
	.word	.L1000-.L320,.L1001-.L320
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_int')
	.sect	'.debug_loc'
.L556:
	.word	-1,.L312,0,.L893-.L312
	.half	1
	.byte	86
	.word	.L896-.L312,.L552-.L312
	.half	1
	.byte	89
	.word	.L902-.L312,.L903-.L312
	.half	1
	.byte	84
	.word	0,0
.L560:
	.word	0,0
.L563:
	.word	-1,.L312,0,.L552-.L312
	.half	2
	.byte	145,112
	.word	0,0
.L311:
	.word	-1,.L312,0,.L892-.L312
	.half	2
	.byte	138,0
	.word	.L892-.L312,.L552-.L312
	.half	2
	.byte	138,16
	.word	.L552-.L312,.L552-.L312
	.half	2
	.byte	138,0
	.word	0,0
.L557:
	.word	-1,.L312,0,.L893-.L312
	.half	1
	.byte	87
	.word	.L897-.L312,.L552-.L312
	.half	1
	.byte	92
	.word	0,0
.L561:
	.word	-1,.L312,.L901-.L312,.L552-.L312
	.half	1
	.byte	95
	.word	0,0
.L553:
	.word	-1,.L312,0,.L894-.L312
	.half	1
	.byte	84
	.word	.L898-.L312,.L894-.L312
	.half	1
	.byte	90
	.word	.L904-.L312,.L905-.L312
	.half	1
	.byte	90
	.word	0,0
.L554:
	.word	-1,.L312,0,.L895-.L312
	.half	1
	.byte	85
	.word	.L899-.L312,.L900-.L312
	.half	1
	.byte	91
	.word	.L904-.L312,.L905-.L312
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_loc'
.L629:
	.word	-1,.L322,0,.L620-.L322
	.half	2
	.byte	145,8
	.word	.L1024-.L322,.L620-.L322
	.half	1
	.byte	94
	.word	0,0
.L633:
	.word	-1,.L322,.L1028-.L322,.L1029-.L322
	.half	1
	.byte	98
	.word	.L1030-.L322,.L620-.L322
	.half	1
	.byte	109
	.word	.L215-.L322,.L1031-.L322
	.half	1
	.byte	98
	.word	.L1046-.L322,.L1043-.L322
	.half	1
	.byte	100
	.word	.L1047-.L322,.L222-.L322
	.half	1
	.byte	100
	.word	.L1049-.L322,.L1050-.L322
	.half	1
	.byte	100
	.word	0,0
.L628:
	.word	-1,.L322,0,.L620-.L322
	.half	2
	.byte	145,4
	.word	.L1023-.L322,.L620-.L322
	.half	1
	.byte	93
	.word	0,0
.L627:
	.word	-1,.L322,0,.L620-.L322
	.half	2
	.byte	145,0
	.word	.L1022-.L322,.L620-.L322
	.half	1
	.byte	92
	.word	.L1048-.L322,.L222-.L322
	.half	1
	.byte	84
	.word	0,0
.L626:
	.word	-1,.L322,0,.L1017-.L322
	.half	1
	.byte	87
	.word	.L218-.L322,.L1038-.L322
	.half	1
	.byte	91
	.word	0,0
.L631:
	.word	-1,.L322,.L1042-.L322,.L1043-.L322
	.half	5
	.byte	144,32,157,32,0
	.word	.L221-.L322,.L222-.L322
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L624:
	.word	-1,.L322,0,.L1018-.L322
	.half	1
	.byte	100
	.word	.L1021-.L322,.L620-.L322
	.half	1
	.byte	108
	.word	0,0
.L634:
	.word	-1,.L322,.L1041-.L322,.L217-.L322
	.half	1
	.byte	111
	.word	0,0
.L321:
	.word	-1,.L322,0,.L620-.L322
	.half	2
	.byte	138,0
	.word	0,0
.L632:
	.word	-1,.L322,.L1037-.L322,.L620-.L322
	.half	1
	.byte	88
	.word	0,0
.L625:
	.word	-1,.L322,0,.L1017-.L322
	.half	1
	.byte	86
	.word	.L1039-.L322,.L1040-.L322
	.half	1
	.byte	90
	.word	.L1044-.L322,.L1045-.L322
	.half	1
	.byte	90
	.word	0,0
.L621:
	.word	-1,.L322,0,.L1019-.L322
	.half	1
	.byte	84
	.word	.L1025-.L322,.L1019-.L322
	.half	1
	.byte	88
	.word	.L214-.L322,.L1032-.L322
	.half	1
	.byte	88
	.word	.L1035-.L322,.L1036-.L322
	.half	1
	.byte	88
	.word	0,0
.L622:
	.word	-1,.L322,0,.L1020-.L322
	.half	1
	.byte	85
	.word	.L1026-.L322,.L1027-.L322
	.half	1
	.byte	89
	.word	.L1033-.L322,.L1034-.L322
	.half	1
	.byte	89
	.word	.L1035-.L322,.L1036-.L322
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_string')
	.sect	'.debug_loc'
.L549:
	.word	-1,.L310,0,.L873-.L310
	.half	1
	.byte	100
	.word	.L876-.L310,.L545-.L310
	.half	1
	.byte	108
	.word	0,0
.L309:
	.word	-1,.L310,0,.L545-.L310
	.half	2
	.byte	138,0
	.word	0,0
.L551:
	.word	-1,.L310,.L880-.L310,.L881-.L310
	.half	1
	.byte	90
	.word	.L171-.L310,.L545-.L310
	.half	1
	.byte	90
	.word	0,0
.L546:
	.word	-1,.L310,0,.L874-.L310
	.half	1
	.byte	84
	.word	.L877-.L310,.L874-.L310
	.half	1
	.byte	88
	.word	.L882-.L310,.L883-.L310
	.half	1
	.byte	88
	.word	.L887-.L310,.L888-.L310
	.half	1
	.byte	88
	.word	0,0
.L547:
	.word	-1,.L310,0,.L875-.L310
	.half	1
	.byte	85
	.word	.L878-.L310,.L879-.L310
	.half	1
	.byte	89
	.word	.L884-.L310,.L885-.L310
	.half	1
	.byte	89
	.word	.L885-.L310,.L886-.L310
	.half	1
	.byte	85
	.word	.L889-.L310,.L890-.L310
	.half	1
	.byte	89
	.word	.L890-.L310,.L891-.L310
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_uint')
	.sect	'.debug_loc'
.L568:
	.word	-1,.L314,0,.L907-.L314
	.half	1
	.byte	86
	.word	.L910-.L314,.L564-.L314
	.half	1
	.byte	89
	.word	.L918-.L314,.L919-.L314
	.half	1
	.byte	84
	.word	0,0
.L572:
	.word	0,0
.L574:
	.word	-1,.L314,0,.L564-.L314
	.half	2
	.byte	145,112
	.word	0,0
.L313:
	.word	-1,.L314,0,.L906-.L314
	.half	2
	.byte	138,0
	.word	.L906-.L314,.L564-.L314
	.half	2
	.byte	138,16
	.word	.L564-.L314,.L564-.L314
	.half	2
	.byte	138,0
	.word	0,0
.L569:
	.word	-1,.L314,0,.L907-.L314
	.half	1
	.byte	87
	.word	.L911-.L314,.L564-.L314
	.half	1
	.byte	92
	.word	.L916-.L314,.L917-.L314
	.half	1
	.byte	85
	.word	0,0
.L573:
	.word	-1,.L314,.L915-.L314,.L564-.L314
	.half	1
	.byte	95
	.word	0,0
.L565:
	.word	-1,.L314,0,.L908-.L314
	.half	1
	.byte	84
	.word	.L912-.L314,.L908-.L314
	.half	1
	.byte	90
	.word	.L920-.L314,.L921-.L314
	.half	1
	.byte	90
	.word	0,0
.L566:
	.word	-1,.L314,0,.L909-.L314
	.half	1
	.byte	85
	.word	.L913-.L314,.L914-.L314
	.half	1
	.byte	91
	.word	.L920-.L314,.L921-.L314
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_show_wave')
	.sect	'.debug_loc'
.L648:
	.word	-1,.L324,.L1061-.L324,.L1062-.L324
	.half	1
	.byte	98
	.word	.L1063-.L324,.L635-.L324
	.half	1
	.byte	109
	.word	.L227-.L324,.L1064-.L324
	.half	1
	.byte	98
	.word	.L1074-.L324,.L1073-.L324
	.half	1
	.byte	100
	.word	.L1085-.L324,.L1086-.L324
	.half	1
	.byte	100
	.word	0,0
.L642:
	.word	-1,.L324,0,.L635-.L324
	.half	2
	.byte	145,4
	.word	.L1057-.L324,.L635-.L324
	.half	1
	.byte	93
	.word	0,0
.L641:
	.word	-1,.L324,0,.L635-.L324
	.half	2
	.byte	145,0
	.word	.L1056-.L324,.L635-.L324
	.half	1
	.byte	92
	.word	.L1075-.L324,.L1073-.L324
	.half	1
	.byte	84
	.word	0,0
.L644:
	.word	-1,.L324,.L1072-.L324,.L1073-.L324
	.half	5
	.byte	144,32,157,32,0
	.word	.L1071-.L324,.L635-.L324
	.half	1
	.byte	94
	.word	0,0
.L323:
	.word	-1,.L324,0,.L635-.L324
	.half	2
	.byte	138,0
	.word	0,0
.L645:
	.word	-1,.L324,.L1070-.L324,.L1071-.L324
	.half	1
	.byte	94
	.word	0,0
.L640:
	.word	-1,.L324,0,.L1051-.L324
	.half	1
	.byte	87
	.word	.L1079-.L324,.L1080-.L324
	.half	1
	.byte	91
	.word	0,0
.L647:
	.word	-1,.L324,.L1080-.L324,.L1081-.L324
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L638:
	.word	-1,.L324,0,.L1052-.L324
	.half	1
	.byte	100
	.word	.L1055-.L324,.L635-.L324
	.half	1
	.byte	108
	.word	0,0
.L639:
	.word	-1,.L324,0,.L1051-.L324
	.half	1
	.byte	86
	.word	.L237-.L324,.L1076-.L324
	.half	1
	.byte	90
	.word	0,0
.L646:
	.word	-1,.L324,.L1077-.L324,.L1078-.L324
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L636:
	.word	-1,.L324,0,.L1053-.L324
	.half	1
	.byte	84
	.word	.L1058-.L324,.L1053-.L324
	.half	1
	.byte	88
	.word	.L226-.L324,.L1065-.L324
	.half	1
	.byte	88
	.word	.L1068-.L324,.L1069-.L324
	.half	1
	.byte	88
	.word	.L1080-.L324,.L1082-.L324
	.half	1
	.byte	88
	.word	0,0
.L637:
	.word	-1,.L324,0,.L1054-.L324
	.half	1
	.byte	85
	.word	.L1059-.L324,.L1060-.L324
	.half	1
	.byte	89
	.word	.L1066-.L324,.L1067-.L324
	.half	1
	.byte	89
	.word	.L1068-.L324,.L1069-.L324
	.half	1
	.byte	89
	.word	.L1083-.L324,.L1084-.L324
	.half	1
	.byte	89
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_loc'
.L681:
	.word	-1,.L286,0,.L721-.L286
	.half	1
	.byte	84
	.word	.L722-.L286,.L679-.L286
	.half	1
	.byte	88
	.word	.L723-.L286,.L724-.L286
	.half	1
	.byte	85
	.word	.L48-.L286,.L725-.L286
	.half	1
	.byte	84
	.word	.L50-.L286,.L726-.L286
	.half	1
	.byte	84
	.word	0,0
.L285:
	.word	-1,.L286,0,.L679-.L286
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_loc'
.L683:
	.word	-1,.L288,0,.L727-.L288
	.half	1
	.byte	100
	.word	.L729-.L288,.L682-.L288
	.half	1
	.byte	111
	.word	.L64-.L288,.L732-.L288
	.half	1
	.byte	100
	.word	.L66-.L288,.L734-.L288
	.half	1
	.byte	100
	.word	0,0
.L287:
	.word	-1,.L288,0,.L682-.L288
	.half	2
	.byte	138,0
	.word	0,0
.L684:
	.word	-1,.L288,0,.L728-.L288
	.half	1
	.byte	84
	.word	.L730-.L288,.L682-.L288
	.half	1
	.byte	88
	.word	.L731-.L288,.L727-.L288
	.half	1
	.byte	85
	.word	.L64-.L288,.L733-.L288
	.half	1
	.byte	84
	.word	.L66-.L288,.L735-.L288
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_loc'
.L675:
	.word	-1,.L282,0,.L704-.L282
	.half	1
	.byte	84
	.word	.L705-.L282,.L673-.L282
	.half	1
	.byte	88
	.word	.L706-.L282,.L707-.L282
	.half	1
	.byte	85
	.word	.L22-.L282,.L708-.L282
	.half	1
	.byte	84
	.word	.L24-.L282,.L709-.L282
	.half	1
	.byte	84
	.word	.L710-.L282,.L711-.L282
	.half	1
	.byte	84
	.word	0,0
.L281:
	.word	-1,.L282,0,.L673-.L282
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_loc'
.L677:
	.word	-1,.L284,0,.L712-.L284
	.half	1
	.byte	100
	.word	.L714-.L284,.L676-.L284
	.half	1
	.byte	111
	.word	.L34-.L284,.L717-.L284
	.half	1
	.byte	100
	.word	.L36-.L284,.L719-.L284
	.half	1
	.byte	100
	.word	0,0
.L283:
	.word	-1,.L284,0,.L676-.L284
	.half	2
	.byte	138,0
	.word	0,0
.L678:
	.word	-1,.L284,0,.L713-.L284
	.half	1
	.byte	84
	.word	.L715-.L284,.L676-.L284
	.half	1
	.byte	88
	.word	.L716-.L284,.L712-.L284
	.half	1
	.byte	85
	.word	.L34-.L284,.L718-.L284
	.half	1
	.byte	84
	.word	.L36-.L284,.L720-.L284
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_command')
	.sect	'.debug_loc'
.L672:
	.word	-1,.L280,0,.L695-.L280
	.half	1
	.byte	84
	.word	.L696-.L280,.L670-.L280
	.half	1
	.byte	88
	.word	.L3-.L280,.L697-.L280
	.half	1
	.byte	84
	.word	.L698-.L280,.L699-.L280
	.half	1
	.byte	85
	.word	.L2-.L280,.L700-.L280
	.half	1
	.byte	84
	.word	.L8-.L280,.L701-.L280
	.half	1
	.byte	84
	.word	.L702-.L280,.L703-.L280
	.half	1
	.byte	84
	.word	0,0
.L279:
	.word	-1,.L280,0,.L670-.L280
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('ips200_write_data')
	.sect	'.debug_loc'
.L669:
	.word	-1,.L278,0,.L694-.L278
	.half	1
	.byte	84
	.word	0,0
.L277:
	.word	-1,.L278,0,.L668-.L278
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1754:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('ips200_write_data')
	.sect	'.debug_frame'
	.word	24
	.word	.L1754,.L278,.L668-.L278
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_write_command')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L280,.L670-.L280
	.sdecl	'.debug_frame',debug,cluster('ips200_write_8bit_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L282,.L673-.L282
	.sdecl	'.debug_frame',debug,cluster('ips200_write_8bit_data_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L284,.L676-.L284
	.sdecl	'.debug_frame',debug,cluster('ips200_write_16bit_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L286,.L679-.L286
	.sdecl	'.debug_frame',debug,cluster('ips200_write_16bit_data_array')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L288,.L682-.L288
	.sdecl	'.debug_frame',debug,cluster('ips200_set_region')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L290,.L685-.L290
	.sdecl	'.debug_frame',debug,cluster('ips200_debug_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L292,.L690-.L292
	.byte	4
	.word	(.L754-.L292)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L690-.L754)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_clear')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L294,.L483-.L294
	.sdecl	'.debug_frame',debug,cluster('ips200_full')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L296,.L489-.L296
	.sdecl	'.debug_frame',debug,cluster('ips200_set_dir')
	.sect	'.debug_frame'
	.word	24
	.word	.L1754,.L298,.L495-.L298
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips200_set_font')
	.sect	'.debug_frame'
	.word	24
	.word	.L1754,.L300,.L498-.L300
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips200_set_color')
	.sect	'.debug_frame'
	.word	24
	.word	.L1754,.L302,.L501-.L302
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('ips200_draw_point')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L304,.L506-.L304
	.sdecl	'.debug_frame',debug,cluster('ips200_draw_line')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L306,.L511-.L306
	.byte	4
	.word	(.L793-.L306)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L511-.L793)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_show_char')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L308,.L525-.L308
	.byte	4
	.word	(.L847-.L308)/2
	.byte	19,128,2,22,26,4,19,138,128,2,4
	.word	(.L525-.L847)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug,cluster('ips200_show_string')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L310,.L545-.L310
	.sdecl	'.debug_frame',debug,cluster('ips200_show_int')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L312,.L552-.L312
	.byte	4
	.word	(.L892-.L312)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L552-.L892)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_show_uint')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L314,.L564-.L314
	.byte	4
	.word	(.L906-.L314)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L564-.L906)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_show_float')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L316,.L575-.L316
	.byte	4
	.word	(.L922-.L316)/2
	.byte	19,24,22,26,3,19,138,24,4
	.word	(.L575-.L922)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_show_binary_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L318,.L588-.L318
	.sdecl	'.debug_frame',debug,cluster('ips200_show_gray_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L320,.L604-.L320
	.sdecl	'.debug_frame',debug,cluster('ips200_show_rgb565_image')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L322,.L620-.L322
	.sdecl	'.debug_frame',debug,cluster('ips200_show_wave')
	.sect	'.debug_frame'
	.word	12
	.word	.L1754,.L324,.L635-.L324
	.sdecl	'.debug_frame',debug,cluster('ips200_show_chinese')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L326,.L649-.L326
	.byte	4
	.word	(.L1087-.L326)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L649-.L1087)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('ips200_init')
	.sect	'.debug_frame'
	.word	36
	.word	.L1754,.L328,.L665-.L328
	.byte	4
	.word	(.L1125-.L328)/2
	.byte	19,16,22,26,3,19,138,16,4
	.word	(.L665-.L1125)/2
	.byte	19,0,8,26,0,0
	; Module end
