	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc8428a --dep-file=IfxSrc.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std/IfxSrc.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std/IfxSrc.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std/IfxSrc.c'

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	7679
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std/IfxSrc.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	230
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	233
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	278
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	290
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	370
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	344
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	376
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	376
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	344
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	485
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	485
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	485
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	485
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	485
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	485
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	485
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	485
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	485
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	485
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	485
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	485
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	485
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	485
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	485
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	462
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	478
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	502
	.byte	4,2,35,0,0,14
	.word	792
	.byte	3
	.word	831
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	836
	.byte	6,0,15
	.word	238
	.byte	16
	.word	264
	.byte	6,0,15
	.word	299
	.byte	16
	.word	331
	.byte	6,0,15
	.word	381
	.byte	16
	.word	400
	.byte	6,0,15
	.word	416
	.byte	16
	.word	431
	.byte	16
	.word	445
	.byte	6,0,15
	.word	841
	.byte	16
	.word	869
	.byte	6,0,7
	.byte	'short int',0,2,5,17
	.byte	'__wchar_t',0,5,1,1
	.word	949
	.byte	17
	.byte	'__size_t',0,5,1,1
	.word	462
	.byte	17
	.byte	'__ptrdiff_t',0,5,1,1
	.word	478
	.byte	18,1,3
	.word	1017
	.byte	17
	.byte	'__codeptr',0,5,1,1
	.word	1019
	.byte	19,6,69,9,1,20
	.byte	'IfxSrc_Tos_cpu0',0,0,20
	.byte	'IfxSrc_Tos_cpu1',0,1,20
	.byte	'IfxSrc_Tos_dma',0,3,0,17
	.byte	'IfxSrc_Tos',0,6,74,3
	.word	1042
	.byte	17
	.byte	'boolean',0,7,101,29
	.word	485
	.byte	17
	.byte	'uint8',0,7,105,29
	.word	485
	.byte	7
	.byte	'unsigned short int',0,2,7,17
	.byte	'uint16',0,7,109,29
	.word	1150
	.byte	7
	.byte	'unsigned long int',0,4,7,17
	.byte	'uint32',0,7,113,29
	.word	1187
	.byte	17
	.byte	'uint64',0,7,118,29
	.word	344
	.byte	17
	.byte	'sint16',0,7,126,29
	.word	949
	.byte	7
	.byte	'long int',0,4,5,17
	.byte	'sint32',0,7,131,1,29
	.word	1253
	.byte	7
	.byte	'long long int',0,8,5,17
	.byte	'sint64',0,7,138,1,29
	.word	1281
	.byte	17
	.byte	'float32',0,7,167,1,29
	.word	290
	.byte	17
	.byte	'pvoid',0,8,57,28
	.word	376
	.byte	17
	.byte	'Ifx_TickTime',0,8,79,28
	.word	1281
	.byte	17
	.byte	'Ifx_Priority',0,8,103,16
	.word	1150
	.byte	17
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	502
	.byte	17
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	792
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	1434
	.byte	17
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	1466
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,8,0,14
	.word	1492
	.byte	17
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	1551
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	1579
	.byte	17
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	1616
	.byte	21,64
	.word	792
	.byte	22,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	1644
	.byte	64,2,35,0,0,14
	.word	1653
	.byte	17
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	1685
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	792
	.byte	4,2,35,12,0,14
	.word	1710
	.byte	17
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	1782
	.byte	21,8
	.word	792
	.byte	22,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	1808
	.byte	8,2,35,0,0,14
	.word	1817
	.byte	17
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	1853
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	792
	.byte	4,2,35,12,0,14
	.word	1883
	.byte	17
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	1956
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	1982
	.byte	17
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	2017
	.byte	21,12
	.word	485
	.byte	22,11,0,21,192,1
	.word	792
	.byte	22,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2043
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	2052
	.byte	192,1,2,35,16,0,14
	.word	2062
	.byte	17
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	2129
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	792
	.byte	4,2,35,4,0,14
	.word	2155
	.byte	17
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	2203
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	2231
	.byte	17
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	2264
	.byte	21,40
	.word	485
	.byte	22,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	1808
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	1808
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	1808
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	1808
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	792
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	792
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2291
	.byte	40,2,35,40,0,14
	.word	2300
	.byte	17
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	2427
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	2454
	.byte	17
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	2486
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	2512
	.byte	17
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	2544
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	792
	.byte	4,2,35,8,0,14
	.word	2570
	.byte	17
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	2630
	.byte	21,16
	.word	485
	.byte	22,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	792
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	2656
	.byte	16,2,35,16,0,14
	.word	2665
	.byte	17
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	2759
	.byte	21,24
	.word	485
	.byte	22,23,0,10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	792
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	792
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	792
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	2786
	.byte	24,2,35,24,0,14
	.word	2795
	.byte	17
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	2912
	.byte	21,12
	.word	792
	.byte	22,2,0,21,4
	.word	485
	.byte	22,3,0,21,8
	.word	485
	.byte	22,7,0,21,32
	.word	792
	.byte	22,7,0,21,32
	.word	2967
	.byte	22,0,0,21,88
	.word	485
	.byte	22,87,0,21,108
	.word	792
	.byte	22,26,0,21,96
	.word	485
	.byte	22,95,0,21,96
	.word	2967
	.byte	22,2,0,21,160,3
	.word	485
	.byte	22,159,3,0,21,64
	.word	2967
	.byte	22,1,0,21,192,3
	.word	485
	.byte	22,191,3,0,21,16
	.word	792
	.byte	22,3,0,21,64
	.word	3052
	.byte	22,3,0,21,192,2
	.word	485
	.byte	22,191,2,0,21,52
	.word	485
	.byte	22,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	2940
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2949
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	792
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	792
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	1808
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	2958
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	2976
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	2985
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	2994
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	3003
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	792
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	2043
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	3012
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	3021
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	3012
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	3021
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	3032
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	3041
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	3061
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	3070
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	2940
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	3081
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	2940
	.byte	12,3,35,192,18,0,14
	.word	3090
	.byte	17
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	3550
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	3576
	.byte	17
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	3609
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	792
	.byte	4,2,35,12,0,14
	.word	3636
	.byte	17
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	3709
	.byte	21,56
	.word	485
	.byte	22,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	792
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	792
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3736
	.byte	56,2,35,24,0,14
	.word	3745
	.byte	17
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	3868
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	3894
	.byte	17
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	3926
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	792
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	792
	.byte	4,2,35,16,0,14
	.word	3952
	.byte	17
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	4037
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	4063
	.byte	17
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	4095
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	2967
	.byte	32,2,35,0,0,14
	.word	4121
	.byte	17
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	4154
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	2967
	.byte	32,2,35,0,0,14
	.word	4181
	.byte	17
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	4215
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	792
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	792
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	792
	.byte	4,2,35,20,0,14
	.word	4243
	.byte	17
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	4336
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	4363
	.byte	17
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	4395
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	3052
	.byte	16,2,35,4,0,14
	.word	4421
	.byte	17
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	4467
	.byte	21,24
	.word	792
	.byte	22,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	4493
	.byte	24,2,35,0,0,14
	.word	4502
	.byte	17
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	4535
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	2940
	.byte	12,2,35,0,0,14
	.word	4562
	.byte	17
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	4594
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,0,14
	.word	4620
	.byte	17
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	4666
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	792
	.byte	4,2,35,12,0,14
	.word	4692
	.byte	17
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	4767
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	792
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	792
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	792
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	792
	.byte	4,2,35,12,0,14
	.word	4796
	.byte	17
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	4870
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	792
	.byte	4,2,35,0,0,14
	.word	4898
	.byte	17
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	4932
	.byte	21,4
	.word	1434
	.byte	22,0,0,14
	.word	4959
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	4968
	.byte	4,2,35,0,0,14
	.word	4973
	.byte	17
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	5009
	.byte	21,48
	.word	1492
	.byte	22,3,0,14
	.word	5037
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	5046
	.byte	48,2,35,0,0,14
	.word	5051
	.byte	17
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	5091
	.byte	14
	.word	1579
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	5121
	.byte	4,2,35,0,0,14
	.word	5126
	.byte	17
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	5160
	.byte	21,64
	.word	1653
	.byte	22,0,0,14
	.word	5187
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	5196
	.byte	64,2,35,0,0,14
	.word	5201
	.byte	17
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	5235
	.byte	21,32
	.word	1710
	.byte	22,1,0,14
	.word	5262
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	5271
	.byte	32,2,35,0,0,14
	.word	5276
	.byte	17
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	5312
	.byte	14
	.word	1817
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	5340
	.byte	8,2,35,0,0,14
	.word	5345
	.byte	17
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	5389
	.byte	21,16
	.word	1883
	.byte	22,0,0,14
	.word	5421
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	5430
	.byte	16,2,35,0,0,14
	.word	5435
	.byte	17
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	5469
	.byte	21,8
	.word	1982
	.byte	22,1,0,14
	.word	5496
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	5505
	.byte	8,2,35,0,0,14
	.word	5510
	.byte	17
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	5544
	.byte	21,208,1
	.word	2062
	.byte	22,0,0,14
	.word	5571
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	5581
	.byte	208,1,2,35,0,0,14
	.word	5586
	.byte	17
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	5622
	.byte	14
	.word	2155
	.byte	14
	.word	2155
	.byte	14
	.word	2155
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	5649
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	2958
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	5654
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	5659
	.byte	8,2,35,24,0,14
	.word	5664
	.byte	17
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	5755
	.byte	21,4
	.word	2231
	.byte	22,0,0,14
	.word	5784
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	5793
	.byte	4,2,35,0,0,14
	.word	5798
	.byte	17
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	5834
	.byte	21,80
	.word	2300
	.byte	22,0,0,14
	.word	5862
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	5871
	.byte	80,2,35,0,0,14
	.word	5876
	.byte	17
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	5912
	.byte	21,4
	.word	2454
	.byte	22,0,0,14
	.word	5940
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	5949
	.byte	4,2,35,0,0,14
	.word	5954
	.byte	17
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	5988
	.byte	21,4
	.word	2512
	.byte	22,0,0,14
	.word	6015
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	6024
	.byte	4,2,35,0,0,14
	.word	6029
	.byte	17
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	6063
	.byte	21,12
	.word	2570
	.byte	22,0,0,14
	.word	6090
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	6099
	.byte	12,2,35,0,0,14
	.word	6104
	.byte	17
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	6138
	.byte	21,64
	.word	2665
	.byte	22,1,0,14
	.word	6165
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	6174
	.byte	64,2,35,0,0,14
	.word	6179
	.byte	17
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	6215
	.byte	21,48
	.word	2795
	.byte	22,0,0,14
	.word	6243
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	6252
	.byte	48,2,35,0,0,14
	.word	6257
	.byte	17
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	6295
	.byte	21,204,18
	.word	3090
	.byte	22,0,0,14
	.word	6324
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	6334
	.byte	204,18,2,35,0,0,14
	.word	6339
	.byte	17
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	6375
	.byte	21,4
	.word	3576
	.byte	22,0,0,14
	.word	6402
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	6411
	.byte	4,2,35,0,0,14
	.word	6416
	.byte	17
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	6452
	.byte	21,64
	.word	3636
	.byte	22,3,0,14
	.word	6480
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	6489
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	792
	.byte	4,2,35,64,0,14
	.word	6494
	.byte	17
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	6543
	.byte	21,80
	.word	3745
	.byte	22,0,0,14
	.word	6571
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	6580
	.byte	80,2,35,0,0,14
	.word	6585
	.byte	17
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	6619
	.byte	21,4
	.word	3894
	.byte	22,0,0,14
	.word	6646
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	6655
	.byte	4,2,35,0,0,14
	.word	6660
	.byte	17
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	6694
	.byte	21,40
	.word	3952
	.byte	22,1,0,14
	.word	6721
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	6730
	.byte	40,2,35,0,0,14
	.word	6735
	.byte	17
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	6769
	.byte	21,8
	.word	4063
	.byte	22,1,0,14
	.word	6796
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	6805
	.byte	8,2,35,0,0,14
	.word	6810
	.byte	17
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	6844
	.byte	21,32
	.word	4121
	.byte	22,0,0,14
	.word	6871
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	6880
	.byte	32,2,35,0,0,14
	.word	6885
	.byte	17
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	6921
	.byte	21,32
	.word	4181
	.byte	22,0,0,14
	.word	6949
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	6958
	.byte	32,2,35,0,0,14
	.word	6963
	.byte	17
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	7001
	.byte	21,96
	.word	4243
	.byte	22,3,0,14
	.word	7030
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	7039
	.byte	96,2,35,0,0,14
	.word	7044
	.byte	17
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	7080
	.byte	21,4
	.word	4363
	.byte	22,0,0,14
	.word	7108
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	7117
	.byte	4,2,35,0,0,14
	.word	7122
	.byte	17
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	7156
	.byte	14
	.word	4421
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	7183
	.byte	20,2,35,0,0,14
	.word	7188
	.byte	17
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	7222
	.byte	21,24
	.word	4502
	.byte	22,0,0,14
	.word	7249
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	7258
	.byte	24,2,35,0,0,14
	.word	7263
	.byte	17
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	7299
	.byte	21,12
	.word	4562
	.byte	22,0,0,14
	.word	7327
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	7336
	.byte	12,2,35,0,0,14
	.word	7341
	.byte	17
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	7375
	.byte	21,16
	.word	4620
	.byte	22,1,0,14
	.word	7402
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	7411
	.byte	16,2,35,0,0,14
	.word	7416
	.byte	17
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	7450
	.byte	21,64
	.word	4796
	.byte	22,3,0,14
	.word	7477
	.byte	21,224,1
	.word	485
	.byte	22,223,1,0,21,32
	.word	4692
	.byte	22,1,0,14
	.word	7502
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	7486
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	7491
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	7511
	.byte	32,3,35,160,2,0,14
	.word	7516
	.byte	17
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	7585
	.byte	14
	.word	4898
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	7613
	.byte	4,2,35,0,0,14
	.word	7618
	.byte	17
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	7654
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,46,1,49,19,0,0,16,5,0,49,19,0,0,17,22,0,3,8,58,15,59,15
	.byte	57,15,73,19,0,0,18,21,0,54,15,0,0,19,4,1,58,15,59,15,57,15,11,15,0,0,20,40,0,3,8,28,13,0,0,21,1,1,11,15
	.byte	73,19,0,0,22,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Src\\Std\\IfxSrc.h',0,0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Src/Std/IfxSrc.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0,0
.L9:
.L7:
	; Module end
