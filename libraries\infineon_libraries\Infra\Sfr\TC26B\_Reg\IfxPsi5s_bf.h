/**
 * \file IfxPsi5s_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Psi5s_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Psi5s
 * 
 */
#ifndef IFXPSI5S_BF_H
#define IFXPSI5S_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Psi5s_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN0 */
#define IFX_PSI5S_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN0 */
#define IFX_PSI5S_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN0 */
#define IFX_PSI5S_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN10 */
#define IFX_PSI5S_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN10 */
#define IFX_PSI5S_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN10 */
#define IFX_PSI5S_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN11 */
#define IFX_PSI5S_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN11 */
#define IFX_PSI5S_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN11 */
#define IFX_PSI5S_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN12 */
#define IFX_PSI5S_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN12 */
#define IFX_PSI5S_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN12 */
#define IFX_PSI5S_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN13 */
#define IFX_PSI5S_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN13 */
#define IFX_PSI5S_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN13 */
#define IFX_PSI5S_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN14 */
#define IFX_PSI5S_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN14 */
#define IFX_PSI5S_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN14 */
#define IFX_PSI5S_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN15 */
#define IFX_PSI5S_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN15 */
#define IFX_PSI5S_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN15 */
#define IFX_PSI5S_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN16 */
#define IFX_PSI5S_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN16 */
#define IFX_PSI5S_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN16 */
#define IFX_PSI5S_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN17 */
#define IFX_PSI5S_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN17 */
#define IFX_PSI5S_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN17 */
#define IFX_PSI5S_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN18 */
#define IFX_PSI5S_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN18 */
#define IFX_PSI5S_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN18 */
#define IFX_PSI5S_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN19 */
#define IFX_PSI5S_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN19 */
#define IFX_PSI5S_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN19 */
#define IFX_PSI5S_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN1 */
#define IFX_PSI5S_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN1 */
#define IFX_PSI5S_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN1 */
#define IFX_PSI5S_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN20 */
#define IFX_PSI5S_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN20 */
#define IFX_PSI5S_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN20 */
#define IFX_PSI5S_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN21 */
#define IFX_PSI5S_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN21 */
#define IFX_PSI5S_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN21 */
#define IFX_PSI5S_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN22 */
#define IFX_PSI5S_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN22 */
#define IFX_PSI5S_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN22 */
#define IFX_PSI5S_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN23 */
#define IFX_PSI5S_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN23 */
#define IFX_PSI5S_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN23 */
#define IFX_PSI5S_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN24 */
#define IFX_PSI5S_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN24 */
#define IFX_PSI5S_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN24 */
#define IFX_PSI5S_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN25 */
#define IFX_PSI5S_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN25 */
#define IFX_PSI5S_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN25 */
#define IFX_PSI5S_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN26 */
#define IFX_PSI5S_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN26 */
#define IFX_PSI5S_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN26 */
#define IFX_PSI5S_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN27 */
#define IFX_PSI5S_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN27 */
#define IFX_PSI5S_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN27 */
#define IFX_PSI5S_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN28 */
#define IFX_PSI5S_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN28 */
#define IFX_PSI5S_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN28 */
#define IFX_PSI5S_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN29 */
#define IFX_PSI5S_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN29 */
#define IFX_PSI5S_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN29 */
#define IFX_PSI5S_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN2 */
#define IFX_PSI5S_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN2 */
#define IFX_PSI5S_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN2 */
#define IFX_PSI5S_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN30 */
#define IFX_PSI5S_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN30 */
#define IFX_PSI5S_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN30 */
#define IFX_PSI5S_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN31 */
#define IFX_PSI5S_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN31 */
#define IFX_PSI5S_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN31 */
#define IFX_PSI5S_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN3 */
#define IFX_PSI5S_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN3 */
#define IFX_PSI5S_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN3 */
#define IFX_PSI5S_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN4 */
#define IFX_PSI5S_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN4 */
#define IFX_PSI5S_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN4 */
#define IFX_PSI5S_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN5 */
#define IFX_PSI5S_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN5 */
#define IFX_PSI5S_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN5 */
#define IFX_PSI5S_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN6 */
#define IFX_PSI5S_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN6 */
#define IFX_PSI5S_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN6 */
#define IFX_PSI5S_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN7 */
#define IFX_PSI5S_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN7 */
#define IFX_PSI5S_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN7 */
#define IFX_PSI5S_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN8 */
#define IFX_PSI5S_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN8 */
#define IFX_PSI5S_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN8 */
#define IFX_PSI5S_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_PSI5S_ACCEN0_Bits.EN9 */
#define IFX_PSI5S_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_ACCEN0_Bits.EN9 */
#define IFX_PSI5S_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_ACCEN0_Bits.EN9 */
#define IFX_PSI5S_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_PSI5S_BAR_Bits.BA */
#define IFX_PSI5S_BAR_BA_LEN (30u)

/** \brief  Mask for Ifx_PSI5S_BAR_Bits.BA */
#define IFX_PSI5S_BAR_BA_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_PSI5S_BAR_Bits.BA */
#define IFX_PSI5S_BAR_BA_OFF (2u)

/** \brief  Length for Ifx_PSI5S_BG_Bits.BR_VALUE */
#define IFX_PSI5S_BG_BR_VALUE_LEN (13u)

/** \brief  Mask for Ifx_PSI5S_BG_Bits.BR_VALUE */
#define IFX_PSI5S_BG_BR_VALUE_MSK (0x1fffu)

/** \brief  Offset for Ifx_PSI5S_BG_Bits.BR_VALUE */
#define IFX_PSI5S_BG_BR_VALUE_OFF (0u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD0 */
#define IFX_PSI5S_CDW_SD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD0 */
#define IFX_PSI5S_CDW_SD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD0 */
#define IFX_PSI5S_CDW_SD0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD1 */
#define IFX_PSI5S_CDW_SD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD1 */
#define IFX_PSI5S_CDW_SD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD1 */
#define IFX_PSI5S_CDW_SD1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD2 */
#define IFX_PSI5S_CDW_SD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD2 */
#define IFX_PSI5S_CDW_SD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD2 */
#define IFX_PSI5S_CDW_SD2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD3 */
#define IFX_PSI5S_CDW_SD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD3 */
#define IFX_PSI5S_CDW_SD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD3 */
#define IFX_PSI5S_CDW_SD3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD4 */
#define IFX_PSI5S_CDW_SD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD4 */
#define IFX_PSI5S_CDW_SD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD4 */
#define IFX_PSI5S_CDW_SD4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD5 */
#define IFX_PSI5S_CDW_SD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD5 */
#define IFX_PSI5S_CDW_SD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD5 */
#define IFX_PSI5S_CDW_SD5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD6 */
#define IFX_PSI5S_CDW_SD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD6 */
#define IFX_PSI5S_CDW_SD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD6 */
#define IFX_PSI5S_CDW_SD6_OFF (6u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.SD7 */
#define IFX_PSI5S_CDW_SD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.SD7 */
#define IFX_PSI5S_CDW_SD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.SD7 */
#define IFX_PSI5S_CDW_SD7_OFF (7u)

/** \brief  Length for Ifx_PSI5S_CDW_Bits.TSI */
#define IFX_PSI5S_CDW_TSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CDW_Bits.TSI */
#define IFX_PSI5S_CDW_TSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CDW_Bits.TSI */
#define IFX_PSI5S_CDW_TSI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_CLC_Bits.DISR */
#define IFX_PSI5S_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CLC_Bits.DISR */
#define IFX_PSI5S_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CLC_Bits.DISR */
#define IFX_PSI5S_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_CLC_Bits.DISS */
#define IFX_PSI5S_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CLC_Bits.DISS */
#define IFX_PSI5S_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CLC_Bits.DISS */
#define IFX_PSI5S_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_PSI5S_CLC_Bits.EDIS */
#define IFX_PSI5S_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CLC_Bits.EDIS */
#define IFX_PSI5S_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CLC_Bits.EDIS */
#define IFX_PSI5S_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.BRS */
#define IFX_PSI5S_CON_BRS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.BRS */
#define IFX_PSI5S_CON_BRS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.BRS */
#define IFX_PSI5S_CON_BRS_OFF (13u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.FDE */
#define IFX_PSI5S_CON_FDE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.FDE */
#define IFX_PSI5S_CON_FDE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.FDE */
#define IFX_PSI5S_CON_FDE_OFF (11u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.FE */
#define IFX_PSI5S_CON_FE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.FE */
#define IFX_PSI5S_CON_FE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.FE */
#define IFX_PSI5S_CON_FE_OFF (9u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.FEN */
#define IFX_PSI5S_CON_FEN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.FEN */
#define IFX_PSI5S_CON_FEN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.FEN */
#define IFX_PSI5S_CON_FEN_OFF (6u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.LB */
#define IFX_PSI5S_CON_LB_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.LB */
#define IFX_PSI5S_CON_LB_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.LB */
#define IFX_PSI5S_CON_LB_OFF (14u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.M */
#define IFX_PSI5S_CON_M_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.M */
#define IFX_PSI5S_CON_M_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.M */
#define IFX_PSI5S_CON_M_OFF (0u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.MTX */
#define IFX_PSI5S_CON_MTX_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.MTX */
#define IFX_PSI5S_CON_MTX_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.MTX */
#define IFX_PSI5S_CON_MTX_OFF (16u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.ODD */
#define IFX_PSI5S_CON_ODD_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.ODD */
#define IFX_PSI5S_CON_ODD_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.ODD */
#define IFX_PSI5S_CON_ODD_OFF (12u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.ODDTX */
#define IFX_PSI5S_CON_ODDTX_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.ODDTX */
#define IFX_PSI5S_CON_ODDTX_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.ODDTX */
#define IFX_PSI5S_CON_ODDTX_OFF (28u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.OE */
#define IFX_PSI5S_CON_OE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.OE */
#define IFX_PSI5S_CON_OE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.OE */
#define IFX_PSI5S_CON_OE_OFF (10u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.OEN */
#define IFX_PSI5S_CON_OEN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.OEN */
#define IFX_PSI5S_CON_OEN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.OEN */
#define IFX_PSI5S_CON_OEN_OFF (7u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.PE */
#define IFX_PSI5S_CON_PE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.PE */
#define IFX_PSI5S_CON_PE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.PE */
#define IFX_PSI5S_CON_PE_OFF (8u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.PEN */
#define IFX_PSI5S_CON_PEN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.PEN */
#define IFX_PSI5S_CON_PEN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.PEN */
#define IFX_PSI5S_CON_PEN_OFF (5u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.R */
#define IFX_PSI5S_CON_R_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.R */
#define IFX_PSI5S_CON_R_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.R */
#define IFX_PSI5S_CON_R_OFF (15u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.REN */
#define IFX_PSI5S_CON_REN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.REN */
#define IFX_PSI5S_CON_REN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.REN */
#define IFX_PSI5S_CON_REN_OFF (4u)

/** \brief  Length for Ifx_PSI5S_CON_Bits.STP */
#define IFX_PSI5S_CON_STP_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_CON_Bits.STP */
#define IFX_PSI5S_CON_STP_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_CON_Bits.STP */
#define IFX_PSI5S_CON_STP_OFF (3u)

/** \brief  Length for Ifx_PSI5S_CTV_Bits.CTC */
#define IFX_PSI5S_CTV_CTC_LEN (16u)

/** \brief  Mask for Ifx_PSI5S_CTV_Bits.CTC */
#define IFX_PSI5S_CTV_CTC_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5S_CTV_Bits.CTC */
#define IFX_PSI5S_CTV_CTC_OFF (16u)

/** \brief  Length for Ifx_PSI5S_CTV_Bits.CTV */
#define IFX_PSI5S_CTV_CTV_LEN (16u)

/** \brief  Mask for Ifx_PSI5S_CTV_Bits.CTV */
#define IFX_PSI5S_CTV_CTV_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5S_CTV_Bits.CTV */
#define IFX_PSI5S_CTV_CTV_OFF (0u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC0 */
#define IFX_PSI5S_FCNT_FC0_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC0 */
#define IFX_PSI5S_FCNT_FC0_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC0 */
#define IFX_PSI5S_FCNT_FC0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC1 */
#define IFX_PSI5S_FCNT_FC1_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC1 */
#define IFX_PSI5S_FCNT_FC1_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC1 */
#define IFX_PSI5S_FCNT_FC1_OFF (3u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC2 */
#define IFX_PSI5S_FCNT_FC2_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC2 */
#define IFX_PSI5S_FCNT_FC2_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC2 */
#define IFX_PSI5S_FCNT_FC2_OFF (6u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC3 */
#define IFX_PSI5S_FCNT_FC3_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC3 */
#define IFX_PSI5S_FCNT_FC3_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC3 */
#define IFX_PSI5S_FCNT_FC3_OFF (9u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC4 */
#define IFX_PSI5S_FCNT_FC4_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC4 */
#define IFX_PSI5S_FCNT_FC4_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC4 */
#define IFX_PSI5S_FCNT_FC4_OFF (12u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC5 */
#define IFX_PSI5S_FCNT_FC5_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC5 */
#define IFX_PSI5S_FCNT_FC5_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC5 */
#define IFX_PSI5S_FCNT_FC5_OFF (15u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC6 */
#define IFX_PSI5S_FCNT_FC6_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC6 */
#define IFX_PSI5S_FCNT_FC6_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC6 */
#define IFX_PSI5S_FCNT_FC6_OFF (18u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.FC7 */
#define IFX_PSI5S_FCNT_FC7_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.FC7 */
#define IFX_PSI5S_FCNT_FC7_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.FC7 */
#define IFX_PSI5S_FCNT_FC7_OFF (21u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR0 */
#define IFX_PSI5S_FCNT_NFCLR0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR0 */
#define IFX_PSI5S_FCNT_NFCLR0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR0 */
#define IFX_PSI5S_FCNT_NFCLR0_OFF (24u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR1 */
#define IFX_PSI5S_FCNT_NFCLR1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR1 */
#define IFX_PSI5S_FCNT_NFCLR1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR1 */
#define IFX_PSI5S_FCNT_NFCLR1_OFF (25u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR2 */
#define IFX_PSI5S_FCNT_NFCLR2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR2 */
#define IFX_PSI5S_FCNT_NFCLR2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR2 */
#define IFX_PSI5S_FCNT_NFCLR2_OFF (26u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR3 */
#define IFX_PSI5S_FCNT_NFCLR3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR3 */
#define IFX_PSI5S_FCNT_NFCLR3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR3 */
#define IFX_PSI5S_FCNT_NFCLR3_OFF (27u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR4 */
#define IFX_PSI5S_FCNT_NFCLR4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR4 */
#define IFX_PSI5S_FCNT_NFCLR4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR4 */
#define IFX_PSI5S_FCNT_NFCLR4_OFF (28u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR5 */
#define IFX_PSI5S_FCNT_NFCLR5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR5 */
#define IFX_PSI5S_FCNT_NFCLR5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR5 */
#define IFX_PSI5S_FCNT_NFCLR5_OFF (29u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR6 */
#define IFX_PSI5S_FCNT_NFCLR6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR6 */
#define IFX_PSI5S_FCNT_NFCLR6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR6 */
#define IFX_PSI5S_FCNT_NFCLR6_OFF (30u)

/** \brief  Length for Ifx_PSI5S_FCNT_Bits.NFCLR7 */
#define IFX_PSI5S_FCNT_NFCLR7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FCNT_Bits.NFCLR7 */
#define IFX_PSI5S_FCNT_NFCLR7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FCNT_Bits.NFCLR7 */
#define IFX_PSI5S_FCNT_NFCLR7_OFF (31u)

/** \brief  Length for Ifx_PSI5S_FDO_Bits.DM */
#define IFX_PSI5S_FDO_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_FDO_Bits.DM */
#define IFX_PSI5S_FDO_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_FDO_Bits.DM */
#define IFX_PSI5S_FDO_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5S_FDO_Bits.STEP */
#define IFX_PSI5S_FDO_STEP_LEN (11u)

/** \brief  Mask for Ifx_PSI5S_FDO_Bits.STEP */
#define IFX_PSI5S_FDO_STEP_MSK (0x7ffu)

/** \brief  Offset for Ifx_PSI5S_FDO_Bits.STEP */
#define IFX_PSI5S_FDO_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5S_FDR_Bits.DM */
#define IFX_PSI5S_FDR_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_FDR_Bits.DM */
#define IFX_PSI5S_FDR_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_FDR_Bits.DM */
#define IFX_PSI5S_FDR_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5S_FDR_Bits.RESULT */
#define IFX_PSI5S_FDR_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5S_FDR_Bits.RESULT */
#define IFX_PSI5S_FDR_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5S_FDR_Bits.RESULT */
#define IFX_PSI5S_FDR_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5S_FDR_Bits.STEP */
#define IFX_PSI5S_FDR_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5S_FDR_Bits.STEP */
#define IFX_PSI5S_FDR_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5S_FDR_Bits.STEP */
#define IFX_PSI5S_FDR_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.DM */
#define IFX_PSI5S_FDRT_DM_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.DM */
#define IFX_PSI5S_FDRT_DM_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.DM */
#define IFX_PSI5S_FDRT_DM_OFF (14u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.ECEA */
#define IFX_PSI5S_FDRT_ECEA_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.ECEA */
#define IFX_PSI5S_FDRT_ECEA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.ECEA */
#define IFX_PSI5S_FDRT_ECEA_OFF (29u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.ECEB */
#define IFX_PSI5S_FDRT_ECEB_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.ECEB */
#define IFX_PSI5S_FDRT_ECEB_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.ECEB */
#define IFX_PSI5S_FDRT_ECEB_OFF (30u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.ECS */
#define IFX_PSI5S_FDRT_ECS_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.ECS */
#define IFX_PSI5S_FDRT_ECS_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.ECS */
#define IFX_PSI5S_FDRT_ECS_OFF (26u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.RESULT */
#define IFX_PSI5S_FDRT_RESULT_LEN (10u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.RESULT */
#define IFX_PSI5S_FDRT_RESULT_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.RESULT */
#define IFX_PSI5S_FDRT_RESULT_OFF (16u)

/** \brief  Length for Ifx_PSI5S_FDRT_Bits.STEP */
#define IFX_PSI5S_FDRT_STEP_LEN (10u)

/** \brief  Mask for Ifx_PSI5S_FDRT_Bits.STEP */
#define IFX_PSI5S_FDRT_STEP_MSK (0x3ffu)

/** \brief  Offset for Ifx_PSI5S_FDRT_Bits.STEP */
#define IFX_PSI5S_FDRT_STEP_OFF (0u)

/** \brief  Length for Ifx_PSI5S_FDV_Bits.FD_VALUE */
#define IFX_PSI5S_FDV_FD_VALUE_LEN (11u)

/** \brief  Mask for Ifx_PSI5S_FDV_Bits.FD_VALUE */
#define IFX_PSI5S_FDV_FD_VALUE_MSK (0x7ffu)

/** \brief  Offset for Ifx_PSI5S_FDV_Bits.FD_VALUE */
#define IFX_PSI5S_FDV_FD_VALUE_OFF (0u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ASC */
#define IFX_PSI5S_GCR_ASC_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ASC */
#define IFX_PSI5S_GCR_ASC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ASC */
#define IFX_PSI5S_GCR_ASC_OFF (31u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN0 */
#define IFX_PSI5S_GCR_CEN0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN0 */
#define IFX_PSI5S_GCR_CEN0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN0 */
#define IFX_PSI5S_GCR_CEN0_OFF (16u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN1 */
#define IFX_PSI5S_GCR_CEN1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN1 */
#define IFX_PSI5S_GCR_CEN1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN1 */
#define IFX_PSI5S_GCR_CEN1_OFF (17u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN2 */
#define IFX_PSI5S_GCR_CEN2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN2 */
#define IFX_PSI5S_GCR_CEN2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN2 */
#define IFX_PSI5S_GCR_CEN2_OFF (18u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN3 */
#define IFX_PSI5S_GCR_CEN3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN3 */
#define IFX_PSI5S_GCR_CEN3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN3 */
#define IFX_PSI5S_GCR_CEN3_OFF (19u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN4 */
#define IFX_PSI5S_GCR_CEN4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN4 */
#define IFX_PSI5S_GCR_CEN4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN4 */
#define IFX_PSI5S_GCR_CEN4_OFF (20u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN5 */
#define IFX_PSI5S_GCR_CEN5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN5 */
#define IFX_PSI5S_GCR_CEN5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN5 */
#define IFX_PSI5S_GCR_CEN5_OFF (21u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN6 */
#define IFX_PSI5S_GCR_CEN6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN6 */
#define IFX_PSI5S_GCR_CEN6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN6 */
#define IFX_PSI5S_GCR_CEN6_OFF (22u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CEN7 */
#define IFX_PSI5S_GCR_CEN7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CEN7 */
#define IFX_PSI5S_GCR_CEN7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CEN7 */
#define IFX_PSI5S_GCR_CEN7_OFF (23u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.CRCI */
#define IFX_PSI5S_GCR_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.CRCI */
#define IFX_PSI5S_GCR_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.CRCI */
#define IFX_PSI5S_GCR_CRCI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC0 */
#define IFX_PSI5S_GCR_ETC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC0 */
#define IFX_PSI5S_GCR_ETC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC0 */
#define IFX_PSI5S_GCR_ETC0_OFF (8u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC1 */
#define IFX_PSI5S_GCR_ETC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC1 */
#define IFX_PSI5S_GCR_ETC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC1 */
#define IFX_PSI5S_GCR_ETC1_OFF (9u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC2 */
#define IFX_PSI5S_GCR_ETC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC2 */
#define IFX_PSI5S_GCR_ETC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC2 */
#define IFX_PSI5S_GCR_ETC2_OFF (10u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC3 */
#define IFX_PSI5S_GCR_ETC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC3 */
#define IFX_PSI5S_GCR_ETC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC3 */
#define IFX_PSI5S_GCR_ETC3_OFF (11u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC4 */
#define IFX_PSI5S_GCR_ETC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC4 */
#define IFX_PSI5S_GCR_ETC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC4 */
#define IFX_PSI5S_GCR_ETC4_OFF (12u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC5 */
#define IFX_PSI5S_GCR_ETC5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC5 */
#define IFX_PSI5S_GCR_ETC5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC5 */
#define IFX_PSI5S_GCR_ETC5_OFF (13u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC6 */
#define IFX_PSI5S_GCR_ETC6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC6 */
#define IFX_PSI5S_GCR_ETC6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC6 */
#define IFX_PSI5S_GCR_ETC6_OFF (14u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.ETC7 */
#define IFX_PSI5S_GCR_ETC7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.ETC7 */
#define IFX_PSI5S_GCR_ETC7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.ETC7 */
#define IFX_PSI5S_GCR_ETC7_OFF (15u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.FE */
#define IFX_PSI5S_GCR_FE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.FE */
#define IFX_PSI5S_GCR_FE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.FE */
#define IFX_PSI5S_GCR_FE_OFF (4u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.HDI */
#define IFX_PSI5S_GCR_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.HDI */
#define IFX_PSI5S_GCR_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.HDI */
#define IFX_PSI5S_GCR_HDI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.IDT */
#define IFX_PSI5S_GCR_IDT_LEN (4u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.IDT */
#define IFX_PSI5S_GCR_IDT_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.IDT */
#define IFX_PSI5S_GCR_IDT_OFF (24u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.OE */
#define IFX_PSI5S_GCR_OE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.OE */
#define IFX_PSI5S_GCR_OE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.OE */
#define IFX_PSI5S_GCR_OE_OFF (5u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.PE */
#define IFX_PSI5S_GCR_PE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.PE */
#define IFX_PSI5S_GCR_PE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.PE */
#define IFX_PSI5S_GCR_PE_OFF (3u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.RBI */
#define IFX_PSI5S_GCR_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.RBI */
#define IFX_PSI5S_GCR_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.RBI */
#define IFX_PSI5S_GCR_RBI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.TEI */
#define IFX_PSI5S_GCR_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.TEI */
#define IFX_PSI5S_GCR_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.TEI */
#define IFX_PSI5S_GCR_TEI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_GCR_Bits.XCRCI */
#define IFX_PSI5S_GCR_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_GCR_Bits.XCRCI */
#define IFX_PSI5S_GCR_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_GCR_Bits.XCRCI */
#define IFX_PSI5S_GCR_XCRCI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_ID_Bits.MODNUMBER */
#define IFX_PSI5S_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_PSI5S_ID_Bits.MODNUMBER */
#define IFX_PSI5S_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_PSI5S_ID_Bits.MODNUMBER */
#define IFX_PSI5S_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_PSI5S_ID_Bits.MODREV */
#define IFX_PSI5S_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_PSI5S_ID_Bits.MODREV */
#define IFX_PSI5S_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_PSI5S_ID_Bits.MODREV */
#define IFX_PSI5S_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_PSI5S_ID_Bits.MODTYPE */
#define IFX_PSI5S_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_PSI5S_ID_Bits.MODTYPE */
#define IFX_PSI5S_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_PSI5S_ID_Bits.MODTYPE */
#define IFX_PSI5S_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.CHCI */
#define IFX_PSI5S_INP_CHCI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.CHCI */
#define IFX_PSI5S_INP_CHCI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.CHCI */
#define IFX_PSI5S_INP_CHCI_OFF (12u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.CRCI */
#define IFX_PSI5S_INP_CRCI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.CRCI */
#define IFX_PSI5S_INP_CRCI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.CRCI */
#define IFX_PSI5S_INP_CRCI_OFF (15u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.HDI */
#define IFX_PSI5S_INP_HDI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.HDI */
#define IFX_PSI5S_INP_HDI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.HDI */
#define IFX_PSI5S_INP_HDI_OFF (24u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.RBI */
#define IFX_PSI5S_INP_RBI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.RBI */
#define IFX_PSI5S_INP_RBI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.RBI */
#define IFX_PSI5S_INP_RBI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.RDI */
#define IFX_PSI5S_INP_RDI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.RDI */
#define IFX_PSI5S_INP_RDI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.RDI */
#define IFX_PSI5S_INP_RDI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.RSI */
#define IFX_PSI5S_INP_RSI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.RSI */
#define IFX_PSI5S_INP_RSI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.RSI */
#define IFX_PSI5S_INP_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.TEI */
#define IFX_PSI5S_INP_TEI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.TEI */
#define IFX_PSI5S_INP_TEI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.TEI */
#define IFX_PSI5S_INP_TEI_OFF (9u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.TPI */
#define IFX_PSI5S_INP_TPI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.TPI */
#define IFX_PSI5S_INP_TPI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.TPI */
#define IFX_PSI5S_INP_TPI_OFF (18u)

/** \brief  Length for Ifx_PSI5S_INP_Bits.TPOI */
#define IFX_PSI5S_INP_TPOI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INP_Bits.TPOI */
#define IFX_PSI5S_INP_TPOI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INP_Bits.TPOI */
#define IFX_PSI5S_INP_TPOI_OFF (21u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.EIR */
#define IFX_PSI5S_INPG_EIR_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.EIR */
#define IFX_PSI5S_INPG_EIR_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.EIR */
#define IFX_PSI5S_INPG_EIR_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.FOI */
#define IFX_PSI5S_INPG_FOI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.FOI */
#define IFX_PSI5S_INPG_FOI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.FOI */
#define IFX_PSI5S_INPG_FOI_OFF (15u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.RIR */
#define IFX_PSI5S_INPG_RIR_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.RIR */
#define IFX_PSI5S_INPG_RIR_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.RIR */
#define IFX_PSI5S_INPG_RIR_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.TBIR */
#define IFX_PSI5S_INPG_TBIR_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.TBIR */
#define IFX_PSI5S_INPG_TBIR_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.TBIR */
#define IFX_PSI5S_INPG_TBIR_OFF (9u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.TIR */
#define IFX_PSI5S_INPG_TIR_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.TIR */
#define IFX_PSI5S_INPG_TIR_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.TIR */
#define IFX_PSI5S_INPG_TIR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INPG_Bits.XCRCI */
#define IFX_PSI5S_INPG_XCRCI_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_INPG_Bits.XCRCI */
#define IFX_PSI5S_INPG_XCRCI_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_INPG_Bits.XCRCI */
#define IFX_PSI5S_INPG_XCRCI_OFF (12u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.CHCI */
#define IFX_PSI5S_INTCLR_CHCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.CHCI */
#define IFX_PSI5S_INTCLR_CHCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.CHCI */
#define IFX_PSI5S_INTCLR_CHCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.CRCI */
#define IFX_PSI5S_INTCLR_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.CRCI */
#define IFX_PSI5S_INTCLR_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.CRCI */
#define IFX_PSI5S_INTCLR_CRCI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.HDI */
#define IFX_PSI5S_INTCLR_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.HDI */
#define IFX_PSI5S_INTCLR_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.HDI */
#define IFX_PSI5S_INTCLR_HDI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.RBI */
#define IFX_PSI5S_INTCLR_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.RBI */
#define IFX_PSI5S_INTCLR_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.RBI */
#define IFX_PSI5S_INTCLR_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.RDI */
#define IFX_PSI5S_INTCLR_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.RDI */
#define IFX_PSI5S_INTCLR_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.RDI */
#define IFX_PSI5S_INTCLR_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.RSI */
#define IFX_PSI5S_INTCLR_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.RSI */
#define IFX_PSI5S_INTCLR_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.RSI */
#define IFX_PSI5S_INTCLR_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.TEI */
#define IFX_PSI5S_INTCLR_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.TEI */
#define IFX_PSI5S_INTCLR_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.TEI */
#define IFX_PSI5S_INTCLR_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.TPI */
#define IFX_PSI5S_INTCLR_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.TPI */
#define IFX_PSI5S_INTCLR_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.TPI */
#define IFX_PSI5S_INTCLR_TPI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INTCLR_Bits.TPOI */
#define IFX_PSI5S_INTCLR_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLR_Bits.TPOI */
#define IFX_PSI5S_INTCLR_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLR_Bits.TPOI */
#define IFX_PSI5S_INTCLR_TPOI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.EIR */
#define IFX_PSI5S_INTCLRG_EIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.EIR */
#define IFX_PSI5S_INTCLRG_EIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.EIR */
#define IFX_PSI5S_INTCLRG_EIR_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.FOI */
#define IFX_PSI5S_INTCLRG_FOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.FOI */
#define IFX_PSI5S_INTCLRG_FOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.FOI */
#define IFX_PSI5S_INTCLRG_FOI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.RIR */
#define IFX_PSI5S_INTCLRG_RIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.RIR */
#define IFX_PSI5S_INTCLRG_RIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.RIR */
#define IFX_PSI5S_INTCLRG_RIR_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.TBIR */
#define IFX_PSI5S_INTCLRG_TBIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.TBIR */
#define IFX_PSI5S_INTCLRG_TBIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.TBIR */
#define IFX_PSI5S_INTCLRG_TBIR_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.TIR */
#define IFX_PSI5S_INTCLRG_TIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.TIR */
#define IFX_PSI5S_INTCLRG_TIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.TIR */
#define IFX_PSI5S_INTCLRG_TIR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTCLRG_Bits.XCRCI */
#define IFX_PSI5S_INTCLRG_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTCLRG_Bits.XCRCI */
#define IFX_PSI5S_INTCLRG_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTCLRG_Bits.XCRCI */
#define IFX_PSI5S_INTCLRG_XCRCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.CHCI */
#define IFX_PSI5S_INTEN_CHCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.CHCI */
#define IFX_PSI5S_INTEN_CHCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.CHCI */
#define IFX_PSI5S_INTEN_CHCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.CRCI */
#define IFX_PSI5S_INTEN_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.CRCI */
#define IFX_PSI5S_INTEN_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.CRCI */
#define IFX_PSI5S_INTEN_CRCI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.HDI */
#define IFX_PSI5S_INTEN_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.HDI */
#define IFX_PSI5S_INTEN_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.HDI */
#define IFX_PSI5S_INTEN_HDI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.RBI */
#define IFX_PSI5S_INTEN_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.RBI */
#define IFX_PSI5S_INTEN_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.RBI */
#define IFX_PSI5S_INTEN_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.RDI */
#define IFX_PSI5S_INTEN_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.RDI */
#define IFX_PSI5S_INTEN_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.RDI */
#define IFX_PSI5S_INTEN_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.RSI */
#define IFX_PSI5S_INTEN_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.RSI */
#define IFX_PSI5S_INTEN_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.RSI */
#define IFX_PSI5S_INTEN_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.TEI */
#define IFX_PSI5S_INTEN_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.TEI */
#define IFX_PSI5S_INTEN_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.TEI */
#define IFX_PSI5S_INTEN_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.TPI */
#define IFX_PSI5S_INTEN_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.TPI */
#define IFX_PSI5S_INTEN_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.TPI */
#define IFX_PSI5S_INTEN_TPI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INTEN_Bits.TPOI */
#define IFX_PSI5S_INTEN_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTEN_Bits.TPOI */
#define IFX_PSI5S_INTEN_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTEN_Bits.TPOI */
#define IFX_PSI5S_INTEN_TPOI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.EIR */
#define IFX_PSI5S_INTENG_EIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.EIR */
#define IFX_PSI5S_INTENG_EIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.EIR */
#define IFX_PSI5S_INTENG_EIR_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.FOI */
#define IFX_PSI5S_INTENG_FOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.FOI */
#define IFX_PSI5S_INTENG_FOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.FOI */
#define IFX_PSI5S_INTENG_FOI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.RIR */
#define IFX_PSI5S_INTENG_RIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.RIR */
#define IFX_PSI5S_INTENG_RIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.RIR */
#define IFX_PSI5S_INTENG_RIR_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.TBIR */
#define IFX_PSI5S_INTENG_TBIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.TBIR */
#define IFX_PSI5S_INTENG_TBIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.TBIR */
#define IFX_PSI5S_INTENG_TBIR_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.TIR */
#define IFX_PSI5S_INTENG_TIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.TIR */
#define IFX_PSI5S_INTENG_TIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.TIR */
#define IFX_PSI5S_INTENG_TIR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTENG_Bits.XCRCI */
#define IFX_PSI5S_INTENG_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTENG_Bits.XCRCI */
#define IFX_PSI5S_INTENG_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTENG_Bits.XCRCI */
#define IFX_PSI5S_INTENG_XCRCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.CHCI */
#define IFX_PSI5S_INTOV_CHCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.CHCI */
#define IFX_PSI5S_INTOV_CHCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.CHCI */
#define IFX_PSI5S_INTOV_CHCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.CRCI */
#define IFX_PSI5S_INTOV_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.CRCI */
#define IFX_PSI5S_INTOV_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.CRCI */
#define IFX_PSI5S_INTOV_CRCI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.EIR */
#define IFX_PSI5S_INTOV_EIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.EIR */
#define IFX_PSI5S_INTOV_EIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.EIR */
#define IFX_PSI5S_INTOV_EIR_OFF (11u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.FOI */
#define IFX_PSI5S_INTOV_FOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.FOI */
#define IFX_PSI5S_INTOV_FOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.FOI */
#define IFX_PSI5S_INTOV_FOI_OFF (14u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.HDI */
#define IFX_PSI5S_INTOV_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.HDI */
#define IFX_PSI5S_INTOV_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.HDI */
#define IFX_PSI5S_INTOV_HDI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.RBI */
#define IFX_PSI5S_INTOV_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.RBI */
#define IFX_PSI5S_INTOV_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.RBI */
#define IFX_PSI5S_INTOV_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.RDI */
#define IFX_PSI5S_INTOV_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.RDI */
#define IFX_PSI5S_INTOV_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.RDI */
#define IFX_PSI5S_INTOV_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.RIR */
#define IFX_PSI5S_INTOV_RIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.RIR */
#define IFX_PSI5S_INTOV_RIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.RIR */
#define IFX_PSI5S_INTOV_RIR_OFF (10u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.RSI */
#define IFX_PSI5S_INTOV_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.RSI */
#define IFX_PSI5S_INTOV_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.RSI */
#define IFX_PSI5S_INTOV_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.TBIR */
#define IFX_PSI5S_INTOV_TBIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.TBIR */
#define IFX_PSI5S_INTOV_TBIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.TBIR */
#define IFX_PSI5S_INTOV_TBIR_OFF (12u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.TEI */
#define IFX_PSI5S_INTOV_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.TEI */
#define IFX_PSI5S_INTOV_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.TEI */
#define IFX_PSI5S_INTOV_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.TIR */
#define IFX_PSI5S_INTOV_TIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.TIR */
#define IFX_PSI5S_INTOV_TIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.TIR */
#define IFX_PSI5S_INTOV_TIR_OFF (9u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.TPI */
#define IFX_PSI5S_INTOV_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.TPI */
#define IFX_PSI5S_INTOV_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.TPI */
#define IFX_PSI5S_INTOV_TPI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.TPOI */
#define IFX_PSI5S_INTOV_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.TPOI */
#define IFX_PSI5S_INTOV_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.TPOI */
#define IFX_PSI5S_INTOV_TPOI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_INTOV_Bits.XCRCI */
#define IFX_PSI5S_INTOV_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTOV_Bits.XCRCI */
#define IFX_PSI5S_INTOV_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTOV_Bits.XCRCI */
#define IFX_PSI5S_INTOV_XCRCI_OFF (13u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.CHCI */
#define IFX_PSI5S_INTSET_CHCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.CHCI */
#define IFX_PSI5S_INTSET_CHCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.CHCI */
#define IFX_PSI5S_INTSET_CHCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.CRCI */
#define IFX_PSI5S_INTSET_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.CRCI */
#define IFX_PSI5S_INTSET_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.CRCI */
#define IFX_PSI5S_INTSET_CRCI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.HDI */
#define IFX_PSI5S_INTSET_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.HDI */
#define IFX_PSI5S_INTSET_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.HDI */
#define IFX_PSI5S_INTSET_HDI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.RBI */
#define IFX_PSI5S_INTSET_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.RBI */
#define IFX_PSI5S_INTSET_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.RBI */
#define IFX_PSI5S_INTSET_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.RDI */
#define IFX_PSI5S_INTSET_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.RDI */
#define IFX_PSI5S_INTSET_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.RDI */
#define IFX_PSI5S_INTSET_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.RSI */
#define IFX_PSI5S_INTSET_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.RSI */
#define IFX_PSI5S_INTSET_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.RSI */
#define IFX_PSI5S_INTSET_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.TEI */
#define IFX_PSI5S_INTSET_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.TEI */
#define IFX_PSI5S_INTSET_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.TEI */
#define IFX_PSI5S_INTSET_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.TPI */
#define IFX_PSI5S_INTSET_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.TPI */
#define IFX_PSI5S_INTSET_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.TPI */
#define IFX_PSI5S_INTSET_TPI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INTSET_Bits.TPOI */
#define IFX_PSI5S_INTSET_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSET_Bits.TPOI */
#define IFX_PSI5S_INTSET_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSET_Bits.TPOI */
#define IFX_PSI5S_INTSET_TPOI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.EIR */
#define IFX_PSI5S_INTSETG_EIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.EIR */
#define IFX_PSI5S_INTSETG_EIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.EIR */
#define IFX_PSI5S_INTSETG_EIR_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.FOI */
#define IFX_PSI5S_INTSETG_FOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.FOI */
#define IFX_PSI5S_INTSETG_FOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.FOI */
#define IFX_PSI5S_INTSETG_FOI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.RIR */
#define IFX_PSI5S_INTSETG_RIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.RIR */
#define IFX_PSI5S_INTSETG_RIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.RIR */
#define IFX_PSI5S_INTSETG_RIR_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.TBIR */
#define IFX_PSI5S_INTSETG_TBIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.TBIR */
#define IFX_PSI5S_INTSETG_TBIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.TBIR */
#define IFX_PSI5S_INTSETG_TBIR_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.TIR */
#define IFX_PSI5S_INTSETG_TIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.TIR */
#define IFX_PSI5S_INTSETG_TIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.TIR */
#define IFX_PSI5S_INTSETG_TIR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTSETG_Bits.XCRCI */
#define IFX_PSI5S_INTSETG_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSETG_Bits.XCRCI */
#define IFX_PSI5S_INTSETG_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSETG_Bits.XCRCI */
#define IFX_PSI5S_INTSETG_XCRCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.CHCI */
#define IFX_PSI5S_INTSTAT_CHCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.CHCI */
#define IFX_PSI5S_INTSTAT_CHCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.CHCI */
#define IFX_PSI5S_INTSTAT_CHCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.CRCI */
#define IFX_PSI5S_INTSTAT_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.CRCI */
#define IFX_PSI5S_INTSTAT_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.CRCI */
#define IFX_PSI5S_INTSTAT_CRCI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.HDI */
#define IFX_PSI5S_INTSTAT_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.HDI */
#define IFX_PSI5S_INTSTAT_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.HDI */
#define IFX_PSI5S_INTSTAT_HDI_OFF (8u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.RBI */
#define IFX_PSI5S_INTSTAT_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.RBI */
#define IFX_PSI5S_INTSTAT_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.RBI */
#define IFX_PSI5S_INTSTAT_RBI_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.RDI */
#define IFX_PSI5S_INTSTAT_RDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.RDI */
#define IFX_PSI5S_INTSTAT_RDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.RDI */
#define IFX_PSI5S_INTSTAT_RDI_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.RSI */
#define IFX_PSI5S_INTSTAT_RSI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.RSI */
#define IFX_PSI5S_INTSTAT_RSI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.RSI */
#define IFX_PSI5S_INTSTAT_RSI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.TEI */
#define IFX_PSI5S_INTSTAT_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.TEI */
#define IFX_PSI5S_INTSTAT_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.TEI */
#define IFX_PSI5S_INTSTAT_TEI_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.TPI */
#define IFX_PSI5S_INTSTAT_TPI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.TPI */
#define IFX_PSI5S_INTSTAT_TPI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.TPI */
#define IFX_PSI5S_INTSTAT_TPI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_INTSTAT_Bits.TPOI */
#define IFX_PSI5S_INTSTAT_TPOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTAT_Bits.TPOI */
#define IFX_PSI5S_INTSTAT_TPOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTAT_Bits.TPOI */
#define IFX_PSI5S_INTSTAT_TPOI_OFF (7u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.EIR */
#define IFX_PSI5S_INTSTATG_EIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.EIR */
#define IFX_PSI5S_INTSTATG_EIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.EIR */
#define IFX_PSI5S_INTSTATG_EIR_OFF (2u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.FOI */
#define IFX_PSI5S_INTSTATG_FOI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.FOI */
#define IFX_PSI5S_INTSTATG_FOI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.FOI */
#define IFX_PSI5S_INTSTATG_FOI_OFF (5u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.RIR */
#define IFX_PSI5S_INTSTATG_RIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.RIR */
#define IFX_PSI5S_INTSTATG_RIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.RIR */
#define IFX_PSI5S_INTSTATG_RIR_OFF (1u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.TBIR */
#define IFX_PSI5S_INTSTATG_TBIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.TBIR */
#define IFX_PSI5S_INTSTATG_TBIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.TBIR */
#define IFX_PSI5S_INTSTATG_TBIR_OFF (3u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.TIR */
#define IFX_PSI5S_INTSTATG_TIR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.TIR */
#define IFX_PSI5S_INTSTATG_TIR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.TIR */
#define IFX_PSI5S_INTSTATG_TIR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_INTSTATG_Bits.XCRCI */
#define IFX_PSI5S_INTSTATG_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_INTSTATG_Bits.XCRCI */
#define IFX_PSI5S_INTSTATG_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_INTSTATG_Bits.XCRCI */
#define IFX_PSI5S_INTSTATG_XCRCI_OFF (4u)

/** \brief  Length for Ifx_PSI5S_IOCR_Bits.ALTI */
#define IFX_PSI5S_IOCR_ALTI_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_IOCR_Bits.ALTI */
#define IFX_PSI5S_IOCR_ALTI_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_IOCR_Bits.ALTI */
#define IFX_PSI5S_IOCR_ALTI_OFF (0u)

/** \brief  Length for Ifx_PSI5S_KRST0_Bits.RST */
#define IFX_PSI5S_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_KRST0_Bits.RST */
#define IFX_PSI5S_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_KRST0_Bits.RST */
#define IFX_PSI5S_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_PSI5S_KRST0_Bits.RSTSTAT */
#define IFX_PSI5S_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_KRST0_Bits.RSTSTAT */
#define IFX_PSI5S_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_KRST0_Bits.RSTSTAT */
#define IFX_PSI5S_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_PSI5S_KRST1_Bits.RST */
#define IFX_PSI5S_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_KRST1_Bits.RST */
#define IFX_PSI5S_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_KRST1_Bits.RST */
#define IFX_PSI5S_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_PSI5S_KRSTCLR_Bits.CLR */
#define IFX_PSI5S_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_KRSTCLR_Bits.CLR */
#define IFX_PSI5S_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_KRSTCLR_Bits.CLR */
#define IFX_PSI5S_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF0 */
#define IFX_PSI5S_NFC_NF0_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF0 */
#define IFX_PSI5S_NFC_NF0_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF0 */
#define IFX_PSI5S_NFC_NF0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF1 */
#define IFX_PSI5S_NFC_NF1_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF1 */
#define IFX_PSI5S_NFC_NF1_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF1 */
#define IFX_PSI5S_NFC_NF1_OFF (3u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF2 */
#define IFX_PSI5S_NFC_NF2_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF2 */
#define IFX_PSI5S_NFC_NF2_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF2 */
#define IFX_PSI5S_NFC_NF2_OFF (6u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF3 */
#define IFX_PSI5S_NFC_NF3_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF3 */
#define IFX_PSI5S_NFC_NF3_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF3 */
#define IFX_PSI5S_NFC_NF3_OFF (9u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF4 */
#define IFX_PSI5S_NFC_NF4_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF4 */
#define IFX_PSI5S_NFC_NF4_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF4 */
#define IFX_PSI5S_NFC_NF4_OFF (12u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF5 */
#define IFX_PSI5S_NFC_NF5_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF5 */
#define IFX_PSI5S_NFC_NF5_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF5 */
#define IFX_PSI5S_NFC_NF5_OFF (15u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF6 */
#define IFX_PSI5S_NFC_NF6_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF6 */
#define IFX_PSI5S_NFC_NF6_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF6 */
#define IFX_PSI5S_NFC_NF6_OFF (18u)

/** \brief  Length for Ifx_PSI5S_NFC_Bits.NF7 */
#define IFX_PSI5S_NFC_NF7_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_NFC_Bits.NF7 */
#define IFX_PSI5S_NFC_NF7_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_NFC_Bits.NF7 */
#define IFX_PSI5S_NFC_NF7_OFF (21u)

/** \brief  Length for Ifx_PSI5S_OCS_Bits.SUS */
#define IFX_PSI5S_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_PSI5S_OCS_Bits.SUS */
#define IFX_PSI5S_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5S_OCS_Bits.SUS */
#define IFX_PSI5S_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_PSI5S_OCS_Bits.SUS_P */
#define IFX_PSI5S_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_OCS_Bits.SUS_P */
#define IFX_PSI5S_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_OCS_Bits.SUS_P */
#define IFX_PSI5S_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_PSI5S_OCS_Bits.SUSSTA */
#define IFX_PSI5S_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_OCS_Bits.SUSSTA */
#define IFX_PSI5S_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_OCS_Bits.SUSSTA */
#define IFX_PSI5S_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.ATXCMD */
#define IFX_PSI5S_PGC_ATXCMD_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.ATXCMD */
#define IFX_PSI5S_PGC_ATXCMD_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.ATXCMD */
#define IFX_PSI5S_PGC_ATXCMD_OFF (8u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.ETB */
#define IFX_PSI5S_PGC_ETB_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.ETB */
#define IFX_PSI5S_PGC_ETB_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.ETB */
#define IFX_PSI5S_PGC_ETB_OFF (16u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.ETE */
#define IFX_PSI5S_PGC_ETE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.ETE */
#define IFX_PSI5S_PGC_ETE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.ETE */
#define IFX_PSI5S_PGC_ETE_OFF (23u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.ETS */
#define IFX_PSI5S_PGC_ETS_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.ETS */
#define IFX_PSI5S_PGC_ETS_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.ETS */
#define IFX_PSI5S_PGC_ETS_OFF (20u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.PTE */
#define IFX_PSI5S_PGC_PTE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.PTE */
#define IFX_PSI5S_PGC_PTE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.PTE */
#define IFX_PSI5S_PGC_PTE_OFF (19u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.TBS */
#define IFX_PSI5S_PGC_TBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.TBS */
#define IFX_PSI5S_PGC_TBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.TBS */
#define IFX_PSI5S_PGC_TBS_OFF (15u)

/** \brief  Length for Ifx_PSI5S_PGC_Bits.TXCMD */
#define IFX_PSI5S_PGC_TXCMD_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_PGC_Bits.TXCMD */
#define IFX_PSI5S_PGC_TXCMD_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_PGC_Bits.TXCMD */
#define IFX_PSI5S_PGC_TXCMD_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RBUF_Bits.RD_VALUE */
#define IFX_PSI5S_RBUF_RD_VALUE_LEN (9u)

/** \brief  Mask for Ifx_PSI5S_RBUF_Bits.RD_VALUE */
#define IFX_PSI5S_RBUF_RD_VALUE_MSK (0x1ffu)

/** \brief  Offset for Ifx_PSI5S_RBUF_Bits.RD_VALUE */
#define IFX_PSI5S_RBUF_RD_VALUE_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC0 */
#define IFX_PSI5S_RCRA_CRC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC0 */
#define IFX_PSI5S_RCRA_CRC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC0 */
#define IFX_PSI5S_RCRA_CRC0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC1 */
#define IFX_PSI5S_RCRA_CRC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC1 */
#define IFX_PSI5S_RCRA_CRC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC1 */
#define IFX_PSI5S_RCRA_CRC1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC2 */
#define IFX_PSI5S_RCRA_CRC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC2 */
#define IFX_PSI5S_RCRA_CRC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC2 */
#define IFX_PSI5S_RCRA_CRC2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC3 */
#define IFX_PSI5S_RCRA_CRC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC3 */
#define IFX_PSI5S_RCRA_CRC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC3 */
#define IFX_PSI5S_RCRA_CRC3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC4 */
#define IFX_PSI5S_RCRA_CRC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC4 */
#define IFX_PSI5S_RCRA_CRC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC4 */
#define IFX_PSI5S_RCRA_CRC4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.CRC5 */
#define IFX_PSI5S_RCRA_CRC5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.CRC5 */
#define IFX_PSI5S_RCRA_CRC5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.CRC5 */
#define IFX_PSI5S_RCRA_CRC5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.FIDS */
#define IFX_PSI5S_RCRA_FIDS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.FIDS */
#define IFX_PSI5S_RCRA_FIDS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.FIDS */
#define IFX_PSI5S_RCRA_FIDS_OFF (9u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.TSEN */
#define IFX_PSI5S_RCRA_TSEN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.TSEN */
#define IFX_PSI5S_RCRA_TSEN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.TSEN */
#define IFX_PSI5S_RCRA_TSEN_OFF (6u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.TSP */
#define IFX_PSI5S_RCRA_TSP_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.TSP */
#define IFX_PSI5S_RCRA_TSP_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.TSP */
#define IFX_PSI5S_RCRA_TSP_OFF (7u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.TSTS */
#define IFX_PSI5S_RCRA_TSTS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.TSTS */
#define IFX_PSI5S_RCRA_TSTS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.TSTS */
#define IFX_PSI5S_RCRA_TSTS_OFF (8u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC0 */
#define IFX_PSI5S_RCRA_UFC0_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC0 */
#define IFX_PSI5S_RCRA_UFC0_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC0 */
#define IFX_PSI5S_RCRA_UFC0_OFF (16u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC1 */
#define IFX_PSI5S_RCRA_UFC1_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC1 */
#define IFX_PSI5S_RCRA_UFC1_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC1 */
#define IFX_PSI5S_RCRA_UFC1_OFF (18u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC2 */
#define IFX_PSI5S_RCRA_UFC2_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC2 */
#define IFX_PSI5S_RCRA_UFC2_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC2 */
#define IFX_PSI5S_RCRA_UFC2_OFF (20u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC3 */
#define IFX_PSI5S_RCRA_UFC3_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC3 */
#define IFX_PSI5S_RCRA_UFC3_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC3 */
#define IFX_PSI5S_RCRA_UFC3_OFF (22u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC4 */
#define IFX_PSI5S_RCRA_UFC4_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC4 */
#define IFX_PSI5S_RCRA_UFC4_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC4 */
#define IFX_PSI5S_RCRA_UFC4_OFF (24u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.UFC5 */
#define IFX_PSI5S_RCRA_UFC5_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.UFC5 */
#define IFX_PSI5S_RCRA_UFC5_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.UFC5 */
#define IFX_PSI5S_RCRA_UFC5_OFF (26u)

/** \brief  Length for Ifx_PSI5S_RCRA_Bits.WDMS */
#define IFX_PSI5S_RCRA_WDMS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RCRA_Bits.WDMS */
#define IFX_PSI5S_RCRA_WDMS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RCRA_Bits.WDMS */
#define IFX_PSI5S_RCRA_WDMS_OFF (10u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL0 */
#define IFX_PSI5S_RCRB_PDL0_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL0 */
#define IFX_PSI5S_RCRB_PDL0_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL0 */
#define IFX_PSI5S_RCRB_PDL0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL1 */
#define IFX_PSI5S_RCRB_PDL1_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL1 */
#define IFX_PSI5S_RCRB_PDL1_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL1 */
#define IFX_PSI5S_RCRB_PDL1_OFF (5u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL2 */
#define IFX_PSI5S_RCRB_PDL2_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL2 */
#define IFX_PSI5S_RCRB_PDL2_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL2 */
#define IFX_PSI5S_RCRB_PDL2_OFF (10u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL3 */
#define IFX_PSI5S_RCRB_PDL3_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL3 */
#define IFX_PSI5S_RCRB_PDL3_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL3 */
#define IFX_PSI5S_RCRB_PDL3_OFF (15u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL4 */
#define IFX_PSI5S_RCRB_PDL4_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL4 */
#define IFX_PSI5S_RCRB_PDL4_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL4 */
#define IFX_PSI5S_RCRB_PDL4_OFF (20u)

/** \brief  Length for Ifx_PSI5S_RCRB_Bits.PDL5 */
#define IFX_PSI5S_RCRB_PDL5_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_RCRB_Bits.PDL5 */
#define IFX_PSI5S_RCRB_PDL5_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_RCRB_Bits.PDL5 */
#define IFX_PSI5S_RCRB_PDL5_OFF (25u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.PFC */
#define IFX_PSI5S_RDR_PFC_LEN (4u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.PFC */
#define IFX_PSI5S_RDR_PFC_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.PFC */
#define IFX_PSI5S_RDR_PFC_OFF (28u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD0 */
#define IFX_PSI5S_RDR_RD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD0 */
#define IFX_PSI5S_RDR_RD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD0 */
#define IFX_PSI5S_RDR_RD0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD10 */
#define IFX_PSI5S_RDR_RD10_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD10 */
#define IFX_PSI5S_RDR_RD10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD10 */
#define IFX_PSI5S_RDR_RD10_OFF (10u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD11 */
#define IFX_PSI5S_RDR_RD11_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD11 */
#define IFX_PSI5S_RDR_RD11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD11 */
#define IFX_PSI5S_RDR_RD11_OFF (11u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD12 */
#define IFX_PSI5S_RDR_RD12_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD12 */
#define IFX_PSI5S_RDR_RD12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD12 */
#define IFX_PSI5S_RDR_RD12_OFF (12u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD13 */
#define IFX_PSI5S_RDR_RD13_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD13 */
#define IFX_PSI5S_RDR_RD13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD13 */
#define IFX_PSI5S_RDR_RD13_OFF (13u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD14 */
#define IFX_PSI5S_RDR_RD14_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD14 */
#define IFX_PSI5S_RDR_RD14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD14 */
#define IFX_PSI5S_RDR_RD14_OFF (14u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD15 */
#define IFX_PSI5S_RDR_RD15_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD15 */
#define IFX_PSI5S_RDR_RD15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD15 */
#define IFX_PSI5S_RDR_RD15_OFF (15u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD16 */
#define IFX_PSI5S_RDR_RD16_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD16 */
#define IFX_PSI5S_RDR_RD16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD16 */
#define IFX_PSI5S_RDR_RD16_OFF (16u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD17 */
#define IFX_PSI5S_RDR_RD17_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD17 */
#define IFX_PSI5S_RDR_RD17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD17 */
#define IFX_PSI5S_RDR_RD17_OFF (17u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD18 */
#define IFX_PSI5S_RDR_RD18_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD18 */
#define IFX_PSI5S_RDR_RD18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD18 */
#define IFX_PSI5S_RDR_RD18_OFF (18u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD19 */
#define IFX_PSI5S_RDR_RD19_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD19 */
#define IFX_PSI5S_RDR_RD19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD19 */
#define IFX_PSI5S_RDR_RD19_OFF (19u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD1 */
#define IFX_PSI5S_RDR_RD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD1 */
#define IFX_PSI5S_RDR_RD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD1 */
#define IFX_PSI5S_RDR_RD1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD20 */
#define IFX_PSI5S_RDR_RD20_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD20 */
#define IFX_PSI5S_RDR_RD20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD20 */
#define IFX_PSI5S_RDR_RD20_OFF (20u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD21 */
#define IFX_PSI5S_RDR_RD21_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD21 */
#define IFX_PSI5S_RDR_RD21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD21 */
#define IFX_PSI5S_RDR_RD21_OFF (21u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD22 */
#define IFX_PSI5S_RDR_RD22_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD22 */
#define IFX_PSI5S_RDR_RD22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD22 */
#define IFX_PSI5S_RDR_RD22_OFF (22u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD23 */
#define IFX_PSI5S_RDR_RD23_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD23 */
#define IFX_PSI5S_RDR_RD23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD23 */
#define IFX_PSI5S_RDR_RD23_OFF (23u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD24 */
#define IFX_PSI5S_RDR_RD24_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD24 */
#define IFX_PSI5S_RDR_RD24_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD24 */
#define IFX_PSI5S_RDR_RD24_OFF (24u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD25 */
#define IFX_PSI5S_RDR_RD25_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD25 */
#define IFX_PSI5S_RDR_RD25_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD25 */
#define IFX_PSI5S_RDR_RD25_OFF (25u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD26 */
#define IFX_PSI5S_RDR_RD26_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD26 */
#define IFX_PSI5S_RDR_RD26_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD26 */
#define IFX_PSI5S_RDR_RD26_OFF (26u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD27 */
#define IFX_PSI5S_RDR_RD27_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD27 */
#define IFX_PSI5S_RDR_RD27_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD27 */
#define IFX_PSI5S_RDR_RD27_OFF (27u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD2 */
#define IFX_PSI5S_RDR_RD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD2 */
#define IFX_PSI5S_RDR_RD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD2 */
#define IFX_PSI5S_RDR_RD2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD3 */
#define IFX_PSI5S_RDR_RD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD3 */
#define IFX_PSI5S_RDR_RD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD3 */
#define IFX_PSI5S_RDR_RD3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD4 */
#define IFX_PSI5S_RDR_RD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD4 */
#define IFX_PSI5S_RDR_RD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD4 */
#define IFX_PSI5S_RDR_RD4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD5 */
#define IFX_PSI5S_RDR_RD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD5 */
#define IFX_PSI5S_RDR_RD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD5 */
#define IFX_PSI5S_RDR_RD5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD6 */
#define IFX_PSI5S_RDR_RD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD6 */
#define IFX_PSI5S_RDR_RD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD6 */
#define IFX_PSI5S_RDR_RD6_OFF (6u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD7 */
#define IFX_PSI5S_RDR_RD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD7 */
#define IFX_PSI5S_RDR_RD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD7 */
#define IFX_PSI5S_RDR_RD7_OFF (7u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD8 */
#define IFX_PSI5S_RDR_RD8_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD8 */
#define IFX_PSI5S_RDR_RD8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD8 */
#define IFX_PSI5S_RDR_RD8_OFF (8u)

/** \brief  Length for Ifx_PSI5S_RDR_Bits.RD9 */
#define IFX_PSI5S_RDR_RD9_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDR_Bits.RD9 */
#define IFX_PSI5S_RDR_RD9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDR_Bits.RD9 */
#define IFX_PSI5S_RDR_RD9_OFF (9u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.AFC */
#define IFX_PSI5S_RDS_AFC_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.AFC */
#define IFX_PSI5S_RDS_AFC_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.AFC */
#define IFX_PSI5S_RDS_AFC_OFF (25u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.CID */
#define IFX_PSI5S_RDS_CID_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.CID */
#define IFX_PSI5S_RDS_CID_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.CID */
#define IFX_PSI5S_RDS_CID_OFF (22u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.CRC0 */
#define IFX_PSI5S_RDS_CRC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.CRC0 */
#define IFX_PSI5S_RDS_CRC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.CRC0 */
#define IFX_PSI5S_RDS_CRC0_OFF (7u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.CRC1 */
#define IFX_PSI5S_RDS_CRC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.CRC1 */
#define IFX_PSI5S_RDS_CRC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.CRC1 */
#define IFX_PSI5S_RDS_CRC1_OFF (8u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.CRC2 */
#define IFX_PSI5S_RDS_CRC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.CRC2 */
#define IFX_PSI5S_RDS_CRC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.CRC2 */
#define IFX_PSI5S_RDS_CRC2_OFF (9u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.CRCI */
#define IFX_PSI5S_RDS_CRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.CRCI */
#define IFX_PSI5S_RDS_CRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.CRCI */
#define IFX_PSI5S_RDS_CRCI_OFF (10u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.ERR0 */
#define IFX_PSI5S_RDS_ERR0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.ERR0 */
#define IFX_PSI5S_RDS_ERR0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.ERR0 */
#define IFX_PSI5S_RDS_ERR0_OFF (11u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.ERR1 */
#define IFX_PSI5S_RDS_ERR1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.ERR1 */
#define IFX_PSI5S_RDS_ERR1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.ERR1 */
#define IFX_PSI5S_RDS_ERR1_OFF (12u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.FE */
#define IFX_PSI5S_RDS_FE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.FE */
#define IFX_PSI5S_RDS_FE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.FE */
#define IFX_PSI5S_RDS_FE_OFF (15u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.FID */
#define IFX_PSI5S_RDS_FID_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.FID */
#define IFX_PSI5S_RDS_FID_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.FID */
#define IFX_PSI5S_RDS_FID_OFF (19u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.HDI */
#define IFX_PSI5S_RDS_HDI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.HDI */
#define IFX_PSI5S_RDS_HDI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.HDI */
#define IFX_PSI5S_RDS_HDI_OFF (13u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.OE */
#define IFX_PSI5S_RDS_OE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.OE */
#define IFX_PSI5S_RDS_OE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.OE */
#define IFX_PSI5S_RDS_OE_OFF (16u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.PE */
#define IFX_PSI5S_RDS_PE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.PE */
#define IFX_PSI5S_RDS_PE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.PE */
#define IFX_PSI5S_RDS_PE_OFF (14u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.PFC */
#define IFX_PSI5S_RDS_PFC_LEN (4u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.PFC */
#define IFX_PSI5S_RDS_PFC_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.PFC */
#define IFX_PSI5S_RDS_PFC_OFF (28u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.RBI */
#define IFX_PSI5S_RDS_RBI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.RBI */
#define IFX_PSI5S_RDS_RBI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.RBI */
#define IFX_PSI5S_RDS_RBI_OFF (18u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.TEI */
#define IFX_PSI5S_RDS_TEI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.TEI */
#define IFX_PSI5S_RDS_TEI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.TEI */
#define IFX_PSI5S_RDS_TEI_OFF (17u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC0 */
#define IFX_PSI5S_RDS_XCRC0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC0 */
#define IFX_PSI5S_RDS_XCRC0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC0 */
#define IFX_PSI5S_RDS_XCRC0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC1 */
#define IFX_PSI5S_RDS_XCRC1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC1 */
#define IFX_PSI5S_RDS_XCRC1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC1 */
#define IFX_PSI5S_RDS_XCRC1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC2 */
#define IFX_PSI5S_RDS_XCRC2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC2 */
#define IFX_PSI5S_RDS_XCRC2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC2 */
#define IFX_PSI5S_RDS_XCRC2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC3 */
#define IFX_PSI5S_RDS_XCRC3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC3 */
#define IFX_PSI5S_RDS_XCRC3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC3 */
#define IFX_PSI5S_RDS_XCRC3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC4 */
#define IFX_PSI5S_RDS_XCRC4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC4 */
#define IFX_PSI5S_RDS_XCRC4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC4 */
#define IFX_PSI5S_RDS_XCRC4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRC5 */
#define IFX_PSI5S_RDS_XCRC5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRC5 */
#define IFX_PSI5S_RDS_XCRC5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRC5 */
#define IFX_PSI5S_RDS_XCRC5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_RDS_Bits.XCRCI */
#define IFX_PSI5S_RDS_XCRCI_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_RDS_Bits.XCRCI */
#define IFX_PSI5S_RDS_XCRCI_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_RDS_Bits.XCRCI */
#define IFX_PSI5S_RDS_XCRCI_OFF (6u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.BSC */
#define IFX_PSI5S_SCR_BSC_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.BSC */
#define IFX_PSI5S_SCR_BSC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.BSC */
#define IFX_PSI5S_SCR_BSC_OFF (8u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.CRC */
#define IFX_PSI5S_SCR_CRC_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.CRC */
#define IFX_PSI5S_SCR_CRC_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.CRC */
#define IFX_PSI5S_SCR_CRC_OFF (22u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.EPS */
#define IFX_PSI5S_SCR_EPS_LEN (2u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.EPS */
#define IFX_PSI5S_SCR_EPS_MSK (0x3u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.EPS */
#define IFX_PSI5S_SCR_EPS_OFF (6u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.FLUS */
#define IFX_PSI5S_SCR_FLUS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.FLUS */
#define IFX_PSI5S_SCR_FLUS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.FLUS */
#define IFX_PSI5S_SCR_FLUS_OFF (14u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.PLL */
#define IFX_PSI5S_SCR_PLL_LEN (5u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.PLL */
#define IFX_PSI5S_SCR_PLL_MSK (0x1fu)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.PLL */
#define IFX_PSI5S_SCR_PLL_OFF (0u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.STA */
#define IFX_PSI5S_SCR_STA_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.STA */
#define IFX_PSI5S_SCR_STA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.STA */
#define IFX_PSI5S_SCR_STA_OFF (23u)

/** \brief  Length for Ifx_PSI5S_SCR_Bits.TPF */
#define IFX_PSI5S_SCR_TPF_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SCR_Bits.TPF */
#define IFX_PSI5S_SCR_TPF_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SCR_Bits.TPF */
#define IFX_PSI5S_SCR_TPF_OFF (26u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD0 */
#define IFX_PSI5S_SDR_SD0_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD0 */
#define IFX_PSI5S_SDR_SD0_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD0 */
#define IFX_PSI5S_SDR_SD0_OFF (0u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD10 */
#define IFX_PSI5S_SDR_SD10_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD10 */
#define IFX_PSI5S_SDR_SD10_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD10 */
#define IFX_PSI5S_SDR_SD10_OFF (10u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD11 */
#define IFX_PSI5S_SDR_SD11_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD11 */
#define IFX_PSI5S_SDR_SD11_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD11 */
#define IFX_PSI5S_SDR_SD11_OFF (11u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD12 */
#define IFX_PSI5S_SDR_SD12_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD12 */
#define IFX_PSI5S_SDR_SD12_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD12 */
#define IFX_PSI5S_SDR_SD12_OFF (12u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD13 */
#define IFX_PSI5S_SDR_SD13_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD13 */
#define IFX_PSI5S_SDR_SD13_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD13 */
#define IFX_PSI5S_SDR_SD13_OFF (13u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD14 */
#define IFX_PSI5S_SDR_SD14_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD14 */
#define IFX_PSI5S_SDR_SD14_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD14 */
#define IFX_PSI5S_SDR_SD14_OFF (14u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD15 */
#define IFX_PSI5S_SDR_SD15_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD15 */
#define IFX_PSI5S_SDR_SD15_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD15 */
#define IFX_PSI5S_SDR_SD15_OFF (15u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD16 */
#define IFX_PSI5S_SDR_SD16_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD16 */
#define IFX_PSI5S_SDR_SD16_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD16 */
#define IFX_PSI5S_SDR_SD16_OFF (16u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD17 */
#define IFX_PSI5S_SDR_SD17_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD17 */
#define IFX_PSI5S_SDR_SD17_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD17 */
#define IFX_PSI5S_SDR_SD17_OFF (17u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD18 */
#define IFX_PSI5S_SDR_SD18_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD18 */
#define IFX_PSI5S_SDR_SD18_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD18 */
#define IFX_PSI5S_SDR_SD18_OFF (18u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD19 */
#define IFX_PSI5S_SDR_SD19_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD19 */
#define IFX_PSI5S_SDR_SD19_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD19 */
#define IFX_PSI5S_SDR_SD19_OFF (19u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD1 */
#define IFX_PSI5S_SDR_SD1_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD1 */
#define IFX_PSI5S_SDR_SD1_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD1 */
#define IFX_PSI5S_SDR_SD1_OFF (1u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD20 */
#define IFX_PSI5S_SDR_SD20_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD20 */
#define IFX_PSI5S_SDR_SD20_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD20 */
#define IFX_PSI5S_SDR_SD20_OFF (20u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD21 */
#define IFX_PSI5S_SDR_SD21_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD21 */
#define IFX_PSI5S_SDR_SD21_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD21 */
#define IFX_PSI5S_SDR_SD21_OFF (21u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD22 */
#define IFX_PSI5S_SDR_SD22_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD22 */
#define IFX_PSI5S_SDR_SD22_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD22 */
#define IFX_PSI5S_SDR_SD22_OFF (22u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD23 */
#define IFX_PSI5S_SDR_SD23_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD23 */
#define IFX_PSI5S_SDR_SD23_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD23 */
#define IFX_PSI5S_SDR_SD23_OFF (23u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD2 */
#define IFX_PSI5S_SDR_SD2_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD2 */
#define IFX_PSI5S_SDR_SD2_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD2 */
#define IFX_PSI5S_SDR_SD2_OFF (2u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD3 */
#define IFX_PSI5S_SDR_SD3_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD3 */
#define IFX_PSI5S_SDR_SD3_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD3 */
#define IFX_PSI5S_SDR_SD3_OFF (3u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD4 */
#define IFX_PSI5S_SDR_SD4_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD4 */
#define IFX_PSI5S_SDR_SD4_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD4 */
#define IFX_PSI5S_SDR_SD4_OFF (4u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD5 */
#define IFX_PSI5S_SDR_SD5_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD5 */
#define IFX_PSI5S_SDR_SD5_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD5 */
#define IFX_PSI5S_SDR_SD5_OFF (5u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD6 */
#define IFX_PSI5S_SDR_SD6_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD6 */
#define IFX_PSI5S_SDR_SD6_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD6 */
#define IFX_PSI5S_SDR_SD6_OFF (6u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD7 */
#define IFX_PSI5S_SDR_SD7_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD7 */
#define IFX_PSI5S_SDR_SD7_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD7 */
#define IFX_PSI5S_SDR_SD7_OFF (7u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD8 */
#define IFX_PSI5S_SDR_SD8_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD8 */
#define IFX_PSI5S_SDR_SD8_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD8 */
#define IFX_PSI5S_SDR_SD8_OFF (8u)

/** \brief  Length for Ifx_PSI5S_SDR_Bits.SD9 */
#define IFX_PSI5S_SDR_SD9_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_SDR_Bits.SD9 */
#define IFX_PSI5S_SDR_SD9_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_SDR_Bits.SD9 */
#define IFX_PSI5S_SDR_SD9_OFF (9u)

/** \brief  Length for Ifx_PSI5S_TAR_Bits.TA */
#define IFX_PSI5S_TAR_TA_LEN (30u)

/** \brief  Mask for Ifx_PSI5S_TAR_Bits.TA */
#define IFX_PSI5S_TAR_TA_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_PSI5S_TAR_Bits.TA */
#define IFX_PSI5S_TAR_TA_OFF (2u)

/** \brief  Length for Ifx_PSI5S_TBUF_Bits.TD_VALUE */
#define IFX_PSI5S_TBUF_TD_VALUE_LEN (9u)

/** \brief  Mask for Ifx_PSI5S_TBUF_Bits.TD_VALUE */
#define IFX_PSI5S_TBUF_TD_VALUE_MSK (0x1ffu)

/** \brief  Offset for Ifx_PSI5S_TBUF_Bits.TD_VALUE */
#define IFX_PSI5S_TBUF_TD_VALUE_OFF (0u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.CLRA */
#define IFX_PSI5S_TSCNTA_CLRA_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.CLRA */
#define IFX_PSI5S_TSCNTA_CLRA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.CLRA */
#define IFX_PSI5S_TSCNTA_CLRA_OFF (30u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.CLRB */
#define IFX_PSI5S_TSCNTA_CLRB_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.CLRB */
#define IFX_PSI5S_TSCNTA_CLRB_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.CLRB */
#define IFX_PSI5S_TSCNTA_CLRB_OFF (31u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.CTS */
#define IFX_PSI5S_TSCNTA_CTS_LEN (24u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.CTS */
#define IFX_PSI5S_TSCNTA_CTS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.CTS */
#define IFX_PSI5S_TSCNTA_CTS_OFF (0u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.ETB */
#define IFX_PSI5S_TSCNTA_ETB_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.ETB */
#define IFX_PSI5S_TSCNTA_ETB_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.ETB */
#define IFX_PSI5S_TSCNTA_ETB_OFF (24u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.TBEA */
#define IFX_PSI5S_TSCNTA_TBEA_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.TBEA */
#define IFX_PSI5S_TSCNTA_TBEA_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.TBEA */
#define IFX_PSI5S_TSCNTA_TBEA_OFF (28u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.TBEB */
#define IFX_PSI5S_TSCNTA_TBEB_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.TBEB */
#define IFX_PSI5S_TSCNTA_TBEB_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.TBEB */
#define IFX_PSI5S_TSCNTA_TBEB_OFF (29u)

/** \brief  Length for Ifx_PSI5S_TSCNTA_Bits.TBS */
#define IFX_PSI5S_TSCNTA_TBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTA_Bits.TBS */
#define IFX_PSI5S_TSCNTA_TBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTA_Bits.TBS */
#define IFX_PSI5S_TSCNTA_TBS_OFF (27u)

/** \brief  Length for Ifx_PSI5S_TSCNTB_Bits.CTS */
#define IFX_PSI5S_TSCNTB_CTS_LEN (24u)

/** \brief  Mask for Ifx_PSI5S_TSCNTB_Bits.CTS */
#define IFX_PSI5S_TSCNTB_CTS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5S_TSCNTB_Bits.CTS */
#define IFX_PSI5S_TSCNTB_CTS_OFF (0u)

/** \brief  Length for Ifx_PSI5S_TSCNTB_Bits.ETB */
#define IFX_PSI5S_TSCNTB_ETB_LEN (3u)

/** \brief  Mask for Ifx_PSI5S_TSCNTB_Bits.ETB */
#define IFX_PSI5S_TSCNTB_ETB_MSK (0x7u)

/** \brief  Offset for Ifx_PSI5S_TSCNTB_Bits.ETB */
#define IFX_PSI5S_TSCNTB_ETB_OFF (24u)

/** \brief  Length for Ifx_PSI5S_TSCNTB_Bits.TBS */
#define IFX_PSI5S_TSCNTB_TBS_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_TSCNTB_Bits.TBS */
#define IFX_PSI5S_TSCNTB_TBS_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_TSCNTB_Bits.TBS */
#define IFX_PSI5S_TSCNTB_TBS_OFF (27u)

/** \brief  Length for Ifx_PSI5S_TSCR_Bits.TS */
#define IFX_PSI5S_TSCR_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5S_TSCR_Bits.TS */
#define IFX_PSI5S_TSCR_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5S_TSCR_Bits.TS */
#define IFX_PSI5S_TSCR_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5S_TSM_Bits.PFC */
#define IFX_PSI5S_TSM_PFC_LEN (4u)

/** \brief  Mask for Ifx_PSI5S_TSM_Bits.PFC */
#define IFX_PSI5S_TSM_PFC_MSK (0xfu)

/** \brief  Offset for Ifx_PSI5S_TSM_Bits.PFC */
#define IFX_PSI5S_TSM_PFC_OFF (28u)

/** \brief  Length for Ifx_PSI5S_TSM_Bits.TS */
#define IFX_PSI5S_TSM_TS_LEN (24u)

/** \brief  Mask for Ifx_PSI5S_TSM_Bits.TS */
#define IFX_PSI5S_TSM_TS_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5S_TSM_Bits.TS */
#define IFX_PSI5S_TSM_TS_OFF (0u)

/** \brief  Length for Ifx_PSI5S_WDT_Bits.WDL */
#define IFX_PSI5S_WDT_WDL_LEN (24u)

/** \brief  Mask for Ifx_PSI5S_WDT_Bits.WDL */
#define IFX_PSI5S_WDT_WDL_MSK (0xffffffu)

/** \brief  Offset for Ifx_PSI5S_WDT_Bits.WDL */
#define IFX_PSI5S_WDT_WDL_OFF (0u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.CLRFE */
#define IFX_PSI5S_WHBCON_CLRFE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.CLRFE */
#define IFX_PSI5S_WHBCON_CLRFE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.CLRFE */
#define IFX_PSI5S_WHBCON_CLRFE_OFF (9u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.CLROE */
#define IFX_PSI5S_WHBCON_CLROE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.CLROE */
#define IFX_PSI5S_WHBCON_CLROE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.CLROE */
#define IFX_PSI5S_WHBCON_CLROE_OFF (10u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.CLRPE */
#define IFX_PSI5S_WHBCON_CLRPE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.CLRPE */
#define IFX_PSI5S_WHBCON_CLRPE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.CLRPE */
#define IFX_PSI5S_WHBCON_CLRPE_OFF (8u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.CLRREN */
#define IFX_PSI5S_WHBCON_CLRREN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.CLRREN */
#define IFX_PSI5S_WHBCON_CLRREN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.CLRREN */
#define IFX_PSI5S_WHBCON_CLRREN_OFF (4u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.SETFE */
#define IFX_PSI5S_WHBCON_SETFE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.SETFE */
#define IFX_PSI5S_WHBCON_SETFE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.SETFE */
#define IFX_PSI5S_WHBCON_SETFE_OFF (12u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.SETOE */
#define IFX_PSI5S_WHBCON_SETOE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.SETOE */
#define IFX_PSI5S_WHBCON_SETOE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.SETOE */
#define IFX_PSI5S_WHBCON_SETOE_OFF (13u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.SETPE */
#define IFX_PSI5S_WHBCON_SETPE_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.SETPE */
#define IFX_PSI5S_WHBCON_SETPE_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.SETPE */
#define IFX_PSI5S_WHBCON_SETPE_OFF (11u)

/** \brief  Length for Ifx_PSI5S_WHBCON_Bits.SETREN */
#define IFX_PSI5S_WHBCON_SETREN_LEN (1u)

/** \brief  Mask for Ifx_PSI5S_WHBCON_Bits.SETREN */
#define IFX_PSI5S_WHBCON_SETREN_MSK (0x1u)

/** \brief  Offset for Ifx_PSI5S_WHBCON_Bits.SETREN */
#define IFX_PSI5S_WHBCON_SETREN_OFF (5u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXPSI5S_BF_H */
