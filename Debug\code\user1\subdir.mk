################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../code/user1/u1_adapter.c \
../code/user1/u1_core.c 

COMPILED_SRCS += \
code/user1/u1_adapter.src \
code/user1/u1_core.src 

C_DEPS += \
code/user1/u1_adapter.d \
code/user1/u1_core.d 

OBJS += \
code/user1/u1_adapter.o \
code/user1/u1_core.o 


# Each subdirectory must supply rules for building sources it contributes
code/user1/u1_adapter.src: ../code/user1/u1_adapter.c code/user1/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user1/u1_adapter.o: code/user1/u1_adapter.src code/user1/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user1/u1_core.src: ../code/user1/u1_core.c code/user1/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user1/u1_core.o: code/user1/u1_core.src code/user1/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"

clean: clean-code-2f-user1

clean-code-2f-user1:
	-$(RM) code/user1/u1_adapter.d code/user1/u1_adapter.o code/user1/u1_adapter.src code/user1/u1_core.d code/user1/u1_core.o code/user1/u1_core.src

.PHONY: clean-code-2f-user1

