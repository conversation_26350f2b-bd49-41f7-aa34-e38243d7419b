/**
 * \file Ifx_FftF32.h
 * \brief Floating-point Fast Fourier Transform
 * \ingroup library_srvsw_sysse_math_f32_fft
 *
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_sysse_math_f32_fft Floating-point FFT
 * This module implements the Fast Fourier Transform in single precision floating-point
 * \ingroup library_srvsw_sysse_math_f32
 *
 */

#ifndef IFX_FFTF32_H
#define IFX_FFTF32_H

#include "Ifx_Cf32.h"

/** \brief Maximum FFT resolution (bits).
 * If redefined, then \ref Ifx_g_FftF32_bitReverseTable and \ref Ifx_g_FftF32_twiddleTable shall be regenerated. */
#define IFX_FFTF32_MAX_RESOLUTION (14)

/** \brief Maximum FFT length.
 * If redefined, then \ref Ifx_g_FftF32_bitReverseTable and \ref Ifx_g_FftF32_twiddleTable shall be regenerated. */
#define IFX_FFTF32_MAX_LENGTH     (1U << IFX_FFTF32_MAX_RESOLUTION)

/** \brief Bit reversal table */
IFX_EXTERN IFX_CONST uint16   Ifx_g_FftF32_bitReverseTable[IFX_FFTF32_MAX_LENGTH];

/** \brief Twiddle factor table */
IFX_EXTERN IFX_CONST cfloat32 Ifx_g_FftF32_twiddleTable[IFX_FFTF32_MAX_LENGTH / 2];

//----------------------------------------------------------------------------------------
/** \addtogroup library_srvsw_sysse_math_f32_fft
 * \{ */

/** \name Transform functions
 * \{ */

/** \brief Twiddle factor generator */
IFX_EXTERN cfloat32 *Ifx_FftF32_generateTwiddleFactor(cfloat32 *TF, sint16 nX);

/** \brief Radix-2 Fast-Fourier Transform */
IFX_EXTERN cfloat32 *Ifx_FftF32_radix2(cfloat32 *R, const cfloat32 *X, uint16 nX);

/** \brief Radix-2 Inverse Fast-Fourier Transform */
IFX_EXTERN cfloat32 *Ifx_FftF32_radix2I(cfloat32 *R, const cfloat32 *X, uint16 nX);

/** \} */
//----------------------------------------------------------------------------------------
/** \name Utility functions
 * \{ */

/** \brief Lookup from \ref Ifx_g_FftF32_bitReverseTable the bit-reversed \<n\> with \<bits\> as number of bits */
IFX_INLINE uint16 Ifx_FftF32_lookUpReversedBits(uint16 n, unsigned bits)
{
    unsigned shift = IFX_FFTF32_MAX_RESOLUTION - bits;
    uint16   index = Ifx_g_FftF32_bitReverseTable[n];
    return index >> shift;
}


/** \brief Lookup from \ref Ifx_g_FftF32_twiddleTable the twiddle factor for N, k */
IFX_INLINE cfloat32 Ifx_FftF32_lookUpTwiddleFactor(sint32 N, sint32 k)
{
    return Ifx_g_FftF32_twiddleTable[k * IFX_FFTF32_MAX_LENGTH / N];
}


/** \brief Calculate the bit-reversed \<n\> with \<bits\> as number of bits */
IFX_EXTERN uint16 Ifx_FftF32_reverseBits(uint16 n, unsigned bits);

/** \} */
//----------------------------------------------------------------------------------------
/** \} */

#endif /* IFX_FFTF32_H */
