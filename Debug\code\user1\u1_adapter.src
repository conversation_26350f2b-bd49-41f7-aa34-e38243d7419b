	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc14424a --dep-file=u1_adapter.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o code/user1/u1_adapter.src ../code/user1/u1_adapter.c"
	.compiler_name		"ctc"
	;source	'../code/user1/u1_adapter.c'

	
$TC16X
	
	.sdecl	'.text.u1_adapter.u1_adapter_uart_rx_handler',code,cluster('u1_adapter_uart_rx_handler')
	.sect	'.text.u1_adapter.u1_adapter_uart_rx_handler'
	.align	2
	
	.global	u1_adapter_uart_rx_handler
; Function u1_adapter_uart_rx_handler
.L37:
u1_adapter_uart_rx_handler:	.type	func
	sub.a	a10,#8
.L153:
	j	.L2
.L3:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d15,[a15]
.L178:
	add	d15,#1
.L179:
	mov	d0,#1024
.L180:
	div.u	e0,d15,d0
.L154:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
	ld.w	d15,[a15]
.L181:
	jeq	d15,d1,.L4
.L182:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d15,[a15]
.L183:
	movh.a	a15,#@his(rx_buffer)
	lea	a15,[a15]@los(rx_buffer)
.L184:
	addsc.a	a15,a15,d15,#0
.L185:
	ld.bu	d15,[a10]
.L186:
	st.b	[a15],d15
.L187:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
.L188:
	st.w	[a15],d1
.L189:
	j	.L5
.L4:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
.L190:
	movh.a	a2,#@his(rx_tail)
	lea	a2,[a2]@los(rx_tail)
	ld.w	d15,[a2]
.L191:
	addi	d15,d15,#256
.L192:
	mov	d0,#1024
.L193:
	div.u	e0,d15,d0
.L155:
	st.w	[a15],d1
.L194:
	movh.a	a15,#@his(rx_overflow)
	lea	a15,[a15]@los(rx_overflow)
.L195:
	mov	d15,#1
.L196:
	st.b	[a15],d15
.L197:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L198:
	mov	d15,#3
.L199:
	st.b	[a15],d15
.L5:
.L2:
	mov	d4,#3
.L200:
	lea	a4,[a10]0
	call	uart_query_byte
.L201:
	jne	d2,#0,.L3
.L202:
	ret
.L122:
	
__u1_adapter_uart_rx_handler_function_end:
	.size	u1_adapter_uart_rx_handler,__u1_adapter_uart_rx_handler_function_end-u1_adapter_uart_rx_handler
.L64:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_init',code,cluster('u1_adapter_init')
	.sect	'.text.u1_adapter.u1_adapter_init'
	.align	2
	
	.global	u1_adapter_init
; Function u1_adapter_init
.L39:
u1_adapter_init:	.type	func
	mov	d4,#3
.L207:
	mov.u	d5,#49664
	addih	d5,d5,#1
.L208:
	mov	d6,#21
.L209:
	mov	d7,#14
	call	uart_init
.L210:
	mov	d4,#3
.L211:
	mov	d5,#1
	call	uart_rx_interrupt
.L212:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
.L213:
	mov	d15,#0
.L214:
	st.w	[a15],d15
.L215:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
.L216:
	mov	d15,#0
.L217:
	st.w	[a15],d15
.L218:
	movh.a	a15,#@his(rx_overflow)
	lea	a15,[a15]@los(rx_overflow)
.L219:
	mov	d15,#0
.L220:
	st.b	[a15],d15
.L221:
	movh.a	a15,#@his(is_initialized)
	lea	a15,[a15]@los(is_initialized)
.L222:
	mov	d15,#1
.L223:
	st.b	[a15],d15
.L224:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L225:
	mov	d15,#0
.L226:
	st.b	[a15],d15
.L227:
	mov	d2,#1
.L228:
	j	.L6
.L6:
	ret
.L127:
	
__u1_adapter_init_function_end:
	.size	u1_adapter_init,__u1_adapter_init_function_end-u1_adapter_init
.L69:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_is_ready',code,cluster('u1_adapter_is_ready')
	.sect	'.text.u1_adapter.u1_adapter_is_ready'
	.align	2
	
	.global	u1_adapter_is_ready
; Function u1_adapter_is_ready
.L41:
u1_adapter_is_ready:	.type	func
	movh.a	a15,#@his(is_initialized)
	lea	a15,[a15]@los(is_initialized)
	ld.bu	d2,[a15]
.L233:
	j	.L7
.L7:
	ret
.L128:
	
__u1_adapter_is_ready_function_end:
	.size	u1_adapter_is_ready,__u1_adapter_is_ready_function_end-u1_adapter_is_ready
.L74:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_send_data',code,cluster('u1_adapter_send_data')
	.sect	'.text.u1_adapter.u1_adapter_send_data'
	.align	2
	
	.global	u1_adapter_send_data
; Function u1_adapter_send_data
.L43:
u1_adapter_send_data:	.type	func
	mov	d5,d4
.L158:
	jz.a	a4,.L8
.L238:
	jne	d5,#0,.L9
.L8:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L239:
	mov	d15,#4
.L240:
	st.b	[a15],d15
.L241:
	mov	d2,#0
.L242:
	j	.L10
.L9:
	movh.a	a15,#@his(is_initialized)
	lea	a15,[a15]@los(is_initialized)
	ld.bu	d15,[a15]
	jne	d15,#0,.L11
.L243:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L244:
	mov	d15,#1
.L245:
	st.b	[a15],d15
.L246:
	mov	d2,#0
.L247:
	j	.L12
.L11:
	mov	d4,#3
.L157:
	call	uart_write_buffer
.L156:
	mov	d2,#1
.L248:
	j	.L13
.L13:
.L12:
.L10:
	ret
.L129:
	
__u1_adapter_send_data_function_end:
	.size	u1_adapter_send_data,__u1_adapter_send_data_function_end-u1_adapter_send_data
.L79:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_receive_data',code,cluster('u1_adapter_receive_data')
	.sect	'.text.u1_adapter.u1_adapter_receive_data'
	.align	2
	
	.global	u1_adapter_receive_data
; Function u1_adapter_receive_data
.L45:
u1_adapter_receive_data:	.type	func
	mov.aa	a12,a4
.L160:
	mov	e8,d5,d4
.L253:
	mov	d12,#0
.L161:
	call	system_getval
.L159:
	mov	d0,#3125
	sh	d0,#5
	div.u	e10,d2,d0
.L162:
	jz.a	a12,.L14
.L163:
	jne	d8,#0,.L15
.L14:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L254:
	mov	d15,#4
.L255:
	st.b	[a15],d15
.L256:
	mov	d2,#0
.L257:
	j	.L16
.L15:
	movh.a	a15,#@his(is_initialized)
	lea	a15,[a15]@los(is_initialized)
	ld.bu	d15,[a15]
	jne	d15,#0,.L17
.L258:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L259:
	mov	d15,#1
.L260:
	st.b	[a15],d15
.L261:
	mov	d2,#0
.L262:
	j	.L18
.L17:
	j	.L19
.L20:
	jlt.u	d9,#1,.L21
.L164:
	call	system_getval
	mov	d15,#3125
	sh	d15,#5
	div.u	e0,d2,d15
.L263:
	sub	d15,d0,d10
.L165:
	jge.u	d9,d15,.L22
.L166:
	j	.L23
.L22:
.L21:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d15,[a15]
.L264:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
	ld.w	d0,[a15]
.L265:
	jeq	d15,d0,.L24
.L266:
	addsc.a	a15,a12,d12,#0
.L267:
	movh.a	a2,#@his(rx_tail)
	lea	a2,[a2]@los(rx_tail)
	ld.w	d15,[a2]
.L268:
	movh.a	a2,#@his(rx_buffer)
	lea	a2,[a2]@los(rx_buffer)
.L269:
	addsc.a	a2,a2,d15,#0
	ld.bu	d15,[a2]
.L270:
	st.b	[a15],d15
.L271:
	add	d12,#1
.L272:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
.L273:
	movh.a	a2,#@his(rx_tail)
	lea	a2,[a2]@los(rx_tail)
	ld.w	d15,[a2]
.L274:
	add	d15,#1
.L275:
	mov	d0,#1024
.L276:
	div.u	e0,d15,d0
.L277:
	st.w	[a15],d1
.L278:
	j	.L25
.L24:
	jlt.u	d9,#1,.L26
.L167:
	mov	d4,#1
	call	system_delay_ms
.L279:
	j	.L27
.L26:
	j	.L28
.L27:
.L25:
.L19:
	jlt.u	d12,d8,.L20
.L28:
.L23:
	mov	d2,d12
.L168:
	j	.L29
.L29:
.L18:
.L16:
	ret
.L133:
	
__u1_adapter_receive_data_function_end:
	.size	u1_adapter_receive_data,__u1_adapter_receive_data_function_end-u1_adapter_receive_data
.L84:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_get_rx_count',code,cluster('u1_adapter_get_rx_count')
	.sect	'.text.u1_adapter.u1_adapter_get_rx_count'
	.align	2
	
	.global	u1_adapter_get_rx_count
; Function u1_adapter_get_rx_count
.L47:
u1_adapter_get_rx_count:	.type	func
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d15,[a15]
.L284:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
	ld.w	d0,[a15]
.L285:
	jlt.u	d15,d0,.L30
.L286:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d2,[a15]
.L287:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
	ld.w	d15,[a15]
.L288:
	sub	d2,d15
.L289:
	j	.L31
.L30:
	mov	d15,#1024
.L290:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
	ld.w	d0,[a15]
.L291:
	sub	d2,d15,d0
.L292:
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
	ld.w	d15,[a15]
.L293:
	add	d2,d15
.L294:
	j	.L32
.L32:
.L31:
	ret
.L140:
	
__u1_adapter_get_rx_count_function_end:
	.size	u1_adapter_get_rx_count,__u1_adapter_get_rx_count_function_end-u1_adapter_get_rx_count
.L89:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_clear_rx_buffer',code,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.text.u1_adapter.u1_adapter_clear_rx_buffer'
	.align	2
	
	.global	u1_adapter_clear_rx_buffer
; Function u1_adapter_clear_rx_buffer
.L49:
u1_adapter_clear_rx_buffer:	.type	func
	movh.a	a15,#@his(rx_head)
	lea	a15,[a15]@los(rx_head)
.L299:
	mov	d15,#0
.L300:
	st.w	[a15],d15
.L301:
	movh.a	a15,#@his(rx_tail)
	lea	a15,[a15]@los(rx_tail)
.L302:
	mov	d15,#0
.L303:
	st.w	[a15],d15
.L304:
	movh.a	a15,#@his(rx_overflow)
	lea	a15,[a15]@los(rx_overflow)
.L305:
	mov	d15,#0
.L306:
	st.b	[a15],d15
.L307:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
	ld.bu	d15,[a15]
.L308:
	jne	d15,#3,.L33
.L309:
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
.L310:
	mov	d15,#0
.L311:
	st.b	[a15],d15
.L33:
	ret
.L141:
	
__u1_adapter_clear_rx_buffer_function_end:
	.size	u1_adapter_clear_rx_buffer,__u1_adapter_clear_rx_buffer_function_end-u1_adapter_clear_rx_buffer
.L94:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_check_rx_overflow',code,cluster('u1_adapter_check_rx_overflow')
	.sect	'.text.u1_adapter.u1_adapter_check_rx_overflow'
	.align	2
	
	.global	u1_adapter_check_rx_overflow
; Function u1_adapter_check_rx_overflow
.L51:
u1_adapter_check_rx_overflow:	.type	func
	movh.a	a15,#@his(rx_overflow)
	lea	a15,[a15]@los(rx_overflow)
	ld.bu	d2,[a15]
.L169:
	movh.a	a15,#@his(rx_overflow)
	lea	a15,[a15]@los(rx_overflow)
.L316:
	mov	d15,#0
.L317:
	st.b	[a15],d15
.L318:
	j	.L34
.L34:
	ret
.L142:
	
__u1_adapter_check_rx_overflow_function_end:
	.size	u1_adapter_check_rx_overflow,__u1_adapter_check_rx_overflow_function_end-u1_adapter_check_rx_overflow
.L99:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_get_status',code,cluster('u1_adapter_get_status')
	.sect	'.text.u1_adapter.u1_adapter_get_status'
	.align	2
	
	.global	u1_adapter_get_status
; Function u1_adapter_get_status
.L53:
u1_adapter_get_status:	.type	func
	movh.a	a15,#@his(adapter_status)
	lea	a15,[a15]@los(adapter_status)
	ld.bu	d2,[a15]
.L323:
	j	.L35
.L35:
	ret
.L145:
	
__u1_adapter_get_status_function_end:
	.size	u1_adapter_get_status,__u1_adapter_get_status_function_end-u1_adapter_get_status
.L104:
	; End of function
	
	.sdecl	'.text.u1_adapter.u1_adapter_debug_output',code,cluster('u1_adapter_debug_output')
	.sect	'.text.u1_adapter.u1_adapter_debug_output'
	.align	2
	
	.global	u1_adapter_debug_output
; Function u1_adapter_debug_output
.L55:
u1_adapter_debug_output:	.type	func
	ret
.L146:
	
__u1_adapter_debug_output_function_end:
	.size	u1_adapter_debug_output,__u1_adapter_debug_output_function_end-u1_adapter_debug_output
.L109:
	; End of function
	
	.sdecl	'.bss.u1_adapter.rx_buffer',data,cluster('rx_buffer')
	.sect	'.bss.u1_adapter.rx_buffer'
rx_buffer:	.type	object
	.size	rx_buffer,1024
	.space	1024
	.sdecl	'.data.u1_adapter.rx_head',data,cluster('rx_head')
	.sect	'.data.u1_adapter.rx_head'
	.align	2
rx_head:	.type	object
	.size	rx_head,4
	.space	4
	.sdecl	'.data.u1_adapter.rx_tail',data,cluster('rx_tail')
	.sect	'.data.u1_adapter.rx_tail'
	.align	2
rx_tail:	.type	object
	.size	rx_tail,4
	.space	4
	.sdecl	'.data.u1_adapter.rx_overflow',data,cluster('rx_overflow')
	.sect	'.data.u1_adapter.rx_overflow'
rx_overflow:	.type	object
	.size	rx_overflow,1
	.space	1
	.sdecl	'.data.u1_adapter.is_initialized',data,cluster('is_initialized')
	.sect	'.data.u1_adapter.is_initialized'
is_initialized:	.type	object
	.size	is_initialized,1
	.space	1
	.sdecl	'.data.u1_adapter.adapter_status',data,cluster('adapter_status')
	.sect	'.data.u1_adapter.adapter_status'
adapter_status:	.type	object
	.size	adapter_status,1
	.space	1
	.calls	'u1_adapter_uart_rx_handler','uart_query_byte'
	.calls	'u1_adapter_init','uart_init'
	.calls	'u1_adapter_init','uart_rx_interrupt'
	.calls	'u1_adapter_send_data','uart_write_buffer'
	.calls	'u1_adapter_receive_data','system_getval'
	.calls	'u1_adapter_receive_data','system_delay_ms'
	.calls	'u1_adapter_uart_rx_handler','',8
	.calls	'u1_adapter_init','',0
	.calls	'u1_adapter_is_ready','',0
	.calls	'u1_adapter_send_data','',0
	.calls	'u1_adapter_receive_data','',0
	.calls	'u1_adapter_get_rx_count','',0
	.calls	'u1_adapter_clear_rx_buffer','',0
	.calls	'u1_adapter_check_rx_overflow','',0
	.calls	'u1_adapter_get_status','',0
	.extern	uart_write_buffer
	.extern	uart_query_byte
	.extern	uart_rx_interrupt
	.extern	uart_init
	.extern	system_delay_ms
	.extern	system_getval
	.calls	'u1_adapter_debug_output','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L57:
	.word	101246
	.half	3
	.word	.L58
	.byte	4
.L56:
	.byte	1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L59
	.byte	2,1,1,3
	.word	189
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	192
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	237
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	249
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	361
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	335
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	367
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	367
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	335
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	476
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	476
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	492
	.byte	4,2,35,0,0
.L123:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	667
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	911
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	588
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	871
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1102
	.byte	4,2,35,8,0,14
	.word	1142
	.byte	3
	.word	1205
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1210
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	645
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1210
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	645
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	645
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1210
	.byte	6,0,15,6,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,5,141,6,31
	.word	1440
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,5,139,5,20
	.word	628
	.byte	1,1,6,0,8
	.byte	'IfxCpu_disableInterrupts',0,3,5,147,5,20
	.word	628
	.byte	1,1,17,6,0,0,4
	.byte	'IfxCpu_enableInterrupts',0,3,5,157,5,17,1,1,6,0,4
	.byte	'IfxCpu_forceDisableInterrupts',0,3,5,225,5,17,1,1,6,0,4
	.byte	'IfxCpu_restoreInterrupts',0,3,5,168,7,17,1,1,5
	.byte	'enabled',0,5,168,7,50
	.word	628
	.byte	6,0
.L125:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,5,161,6,19
	.word	1762
	.byte	1,1,5
	.byte	'address',0,5,161,6,55
	.word	645
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,5,190,6,20
	.word	628
	.byte	1,1,5
	.byte	'address',0,5,190,6,70
	.word	645
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,5,172,8,17,1,1,5
	.byte	'address',0,5,172,8,56
	.word	1762
	.byte	5
	.byte	'count',0,5,172,8,72
	.word	1762
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,8,143,3,16,4,11
	.byte	'P0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,181,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1993
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,8,169,2,16,4,11
	.byte	'PS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,133,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2309
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,8,110,16,4,11
	.byte	'MODREV',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,148,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2880
	.byte	4,2,35,0,0,18,4
	.word	628
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,8,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	628
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	628
	.byte	5,0,2,35,3,0,12,8,164,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3008
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,8,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	628
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	628
	.byte	5,0,2,35,3,0,12,8,180,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3223
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,8,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	628
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	628
	.byte	5,0,2,35,3,0,12,8,188,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3438
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,8,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	628
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	628
	.byte	5,0,2,35,3,0,12,8,172,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3655
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,8,118,16,4,11
	.byte	'P0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,156,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3875
	.byte	4,2,35,0,0,18,24
	.word	628
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,8,205,3,16,4,11
	.byte	'PD0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	628
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	628
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	628
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	628
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,205,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4198
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,8,226,3,16,4,11
	.byte	'PD8',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	628
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	628
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	628
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	628
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	628
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,213,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4502
	.byte	4,2,35,0,0,18,8
	.word	628
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,8,88,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,140,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4827
	.byte	4,2,35,0,0,18,12
	.word	628
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,8,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,197,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5167
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,8,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	453
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,189,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5533
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,8,206,2,16,4,11
	.byte	'PS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,12,8,149,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5819
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,8,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,12,8,165,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5966
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,8,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	453
	.byte	20,0,2,35,0,0,12,8,173,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6135
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,8,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	645
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,157,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6307
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,8,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	645
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	645
	.byte	12,0,2,35,2,0,12,8,229,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6482
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,8,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	8,0,2,35,3,0,12,8,245,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6656
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,8,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,12,8,253,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6830
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,8,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,237,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7006
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,8,249,2,16,4,11
	.byte	'PS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,141,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7162
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,8,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	645
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,221,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7495
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,8,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,12,8,196,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7843
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,8,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,8,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,12,8,204,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7967
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8051
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,8,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,8,213,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8231
	.byte	4,2,35,0,0,18,76
	.word	628
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,8,82,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,8,132,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8484
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,8,45,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,8,252,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,8,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2269
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2840
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2959
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2999
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3183
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3398
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3615
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3835
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2999
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4149
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4189
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4462
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4778
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4818
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5118
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5158
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5493
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5779
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4818
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5926
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6095
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6267
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6442
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6616
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6790
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6966
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7122
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7455
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7803
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4818
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7927
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8176
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8435
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8475
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8531
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9098
	.byte	4,3,35,252,1,0,14
	.word	9138
	.byte	3
	.word	9741
	.byte	15,7,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,7,196,4,17,1,1,5
	.byte	'port',0,7,196,4,48
	.word	9746
	.byte	5
	.byte	'pinIndex',0,7,196,4,60
	.word	628
	.byte	5
	.byte	'mode',0,7,196,4,88
	.word	9751
	.byte	6,0,15,7,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,7,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,7,202,4,17,1,1,5
	.byte	'port',0,7,202,4,49
	.word	9746
	.byte	5
	.byte	'pinIndex',0,7,202,4,61
	.word	628
	.byte	5
	.byte	'mode',0,7,202,4,90
	.word	9956
	.byte	5
	.byte	'index',0,7,202,4,114
	.word	10026
	.byte	6,0,15,7,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,7,208,4,17,1,1,5
	.byte	'port',0,7,208,4,44
	.word	9746
	.byte	5
	.byte	'pinIndex',0,7,208,4,56
	.word	628
	.byte	5
	.byte	'action',0,7,208,4,80
	.word	10339
	.byte	6,0,8
	.byte	'IfxScuCcu_getStmFrequency',0,3,9,226,8,20
	.word	249
	.byte	1,1,6,0,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,11,45,16,4,11
	.byte	'SRPN',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	628
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	628
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	628
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,11,70,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10560
	.byte	4,2,35,0,0,14
	.word	10850
	.byte	3
	.word	10889
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,10,250,1,17,1,1,5
	.byte	'src',0,10,250,1,60
	.word	10894
	.byte	6,0,10
	.byte	'_Ifx_ASCLIN_CLC_Bits',0,13,118,16,4,11
	.byte	'DISR',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,12,13,207,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10942
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_IOCR_Bits',0,13,169,2,16,4,11
	.byte	'ALTI',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'DEPTH',0,2
	.word	645
	.byte	6,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	6,0,2,35,1,11
	.byte	'CTS',0,1
	.word	628
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	645
	.byte	7,7,2,35,2,11
	.byte	'RCPOL',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'CPOL',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'SPOL',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'LB',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'CTSEN',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'RXM',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'TXM',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,151,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11101
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ID_Bits',0,13,161,2,16,4,11
	.byte	'MODREV',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,13,143,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11396
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXFIFOCON_Bits',0,13,149,3,16,4,11
	.byte	'FLUSH',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ENO',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	4,2,2,35,0,11
	.byte	'INW',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	628
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	645
	.byte	11,0,2,35,2,0,12,13,247,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11521
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXFIFOCON_Bits',0,13,129,3,16,4,11
	.byte	'FLUSH',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ENI',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	4,2,2,35,0,11
	.byte	'OUTW',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'INTLEVEL',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'FILL',0,1
	.word	628
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	645
	.byte	10,1,2,35,2,11
	.byte	'BUF',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,231,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11746
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BITCON_Bits',0,13,88,16,4,11
	.byte	'PRESCALER',0,2
	.word	645
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'OVERSAMPLING',0,1
	.word	628
	.byte	4,4,2,35,2,11
	.byte	'reserved_20',0,1
	.word	628
	.byte	4,0,2,35,2,11
	.byte	'SAMPLEPOINT',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	3,1,2,35,3,11
	.byte	'SM',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,183,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FRAMECON_Bits',0,13,145,2,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'IDLE',0,2
	.word	645
	.byte	3,7,2,35,0,11
	.byte	'STOP',0,1
	.word	628
	.byte	3,4,2,35,1,11
	.byte	'LEAD',0,1
	.word	628
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'MODE',0,1
	.word	628
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	645
	.byte	10,4,2,35,2,11
	.byte	'MSB',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'CEN',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'PEN',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'ODD',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,135,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12208
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_DATCON_Bits',0,13,136,1,16,4,11
	.byte	'DATLEN',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	645
	.byte	9,3,2,35,0,11
	.byte	'HO',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'RM',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'CSM',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'RESPONSE',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	8,0,2,35,3,0,12,13,223,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12473
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRG_Bits',0,13,109,16,4,11
	.byte	'DENOMINATOR',0,2
	.word	645
	.byte	12,4,2,35,0,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'NUMERATOR',0,2
	.word	645
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,12,13,199,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12670
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_BRD_Bits',0,13,100,16,4,11
	.byte	'LOWERLIMIT',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'UPPERLIMIT',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MEASURED',0,2
	.word	645
	.byte	12,4,2,35,2,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,12,13,191,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12827
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_CON_Bits',0,13,216,2,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	23,9,2,35,0,11
	.byte	'CSI',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CSEN',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'MS',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'ABD',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,12,13,191,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,209,2,16,4,11
	.byte	'BREAK',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,12,13,183,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13181
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,228,2,16,4,11
	.byte	'HEADER',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,12,13,199,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13295
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_LIN',0,13,135,5,25,12,13
	.byte	'CON',0
	.word	13141
	.byte	4,2,35,0,13
	.byte	'BTIMER',0
	.word	13255
	.byte	4,2,35,4,13
	.byte	'HTIMER',0
	.word	13370
	.byte	4,2,35,8,0,14
	.word	13410
	.byte	10
	.byte	'_Ifx_ASCLIN_FLAGS_Bits',0,13,148,1,16,4,11
	.byte	'TH',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'TR',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RH',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'RR',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'FED',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'RED',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	6,3,2,35,0,11
	.byte	'TWRQ',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'THRQ',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'TRRQ',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PE',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'TC',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'FE',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'HT',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'RT',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'BD',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'LP',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'LA',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'LC',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CE',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RFO',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'RFU',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'RFL',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'TFO',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'TFL',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,231,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13483
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSSET_Bits',0,13,241,1,16,4,11
	.byte	'THS',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'TRS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RHS',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'RRS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'FEDS',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'REDS',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	6,3,2,35,0,11
	.byte	'TWRQS',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'THRQS',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'TRRQS',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PES',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'TCS',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'FES',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'HTS',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'RTS',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'BDS',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'LPS',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'LAS',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'LCS',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CES',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RFOS',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'RFUS',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'RFLS',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'TFOS',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'TFLS',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,255,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13969
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,180,1,16,4,11
	.byte	'THC',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'TRC',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RHC',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'RRC',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'FEDC',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'REDC',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	6,3,2,35,0,11
	.byte	'TWRQC',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'THRQC',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'TRRQC',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PEC',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'TCC',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'FEC',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'HTC',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'RTC',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'BDC',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'LPC',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'LAC',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'LCC',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CEC',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RFOC',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'RFUC',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'RFLC',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'TFOC',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'TFLC',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,239,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14482
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,212,1,16,4,11
	.byte	'THE',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'TRE',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RHE',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'RRE',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'FEDE',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'REDE',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	9,0,2,35,0,11
	.byte	'PEE',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'TCE',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'FEE',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'HTE',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'RTE',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'BDE',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'LPE',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'ABE',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'LCE',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CEE',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RFOE',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'RFUE',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'RFLE',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'TFOE',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'TFLE',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,247,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14997
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_TXDATA_Bits',0,13,143,3,16,4,11
	.byte	'DATA',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,13,239,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15462
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATA_Bits',0,13,245,2,16,4,11
	.byte	'DATA',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,13,215,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15549
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_CSR_Bits',0,13,128,1,16,4,11
	.byte	'CLKSEL',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,4
	.word	453
	.byte	26,1,2,35,0,11
	.byte	'CON',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,215,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15636
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_RXDATAD_Bits',0,13,251,2,16,4,11
	.byte	'DATA',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,13,223,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15759
	.byte	4,2,35,0,0,18,148,1
	.word	628
	.byte	19,147,1,0,10
	.byte	'_Ifx_ASCLIN_OCS_Bits',0,13,235,2,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	2,0,2,35,3,0,12,13,207,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15858
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRSTCLR_Bits',0,13,202,2,16,4,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,12,13,175,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST1_Bits',0,13,195,2,16,4,11
	.byte	'RST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,12,13,167,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16130
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_KRST0_Bits',0,13,187,2,16,4,11
	.byte	'RST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,12,13,159,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16237
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN1_Bits',0,13,82,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,13,175,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16363
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN_ACCEN0_Bits',0,13,45,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,13,167,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16455
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_ASCLIN',0,13,153,5,25,128,2,13
	.byte	'CLC',0
	.word	11061
	.byte	4,2,35,0,13
	.byte	'IOCR',0
	.word	11356
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11481
	.byte	4,2,35,8,13
	.byte	'TXFIFOCON',0
	.word	11706
	.byte	4,2,35,12,13
	.byte	'RXFIFOCON',0
	.word	11947
	.byte	4,2,35,16,13
	.byte	'BITCON',0
	.word	12168
	.byte	4,2,35,20,13
	.byte	'FRAMECON',0
	.word	12433
	.byte	4,2,35,24,13
	.byte	'DATCON',0
	.word	12630
	.byte	4,2,35,28,13
	.byte	'BRG',0
	.word	12787
	.byte	4,2,35,32,13
	.byte	'BRD',0
	.word	12941
	.byte	4,2,35,36,13
	.byte	'LIN',0
	.word	13478
	.byte	12,2,35,40,13
	.byte	'FLAGS',0
	.word	13929
	.byte	4,2,35,52,13
	.byte	'FLAGSSET',0
	.word	14442
	.byte	4,2,35,56,13
	.byte	'FLAGSCLEAR',0
	.word	14957
	.byte	4,2,35,60,13
	.byte	'FLAGSENABLE',0
	.word	15422
	.byte	4,2,35,64,13
	.byte	'TXDATA',0
	.word	15509
	.byte	4,2,35,68,13
	.byte	'RXDATA',0
	.word	15596
	.byte	4,2,35,72,13
	.byte	'CSR',0
	.word	15719
	.byte	4,2,35,76,13
	.byte	'RXDATAD',0
	.word	15807
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	15847
	.byte	148,1,2,35,84,13
	.byte	'OCS',0
	.word	15981
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	16090
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	16197
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	16323
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	16415
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	16987
	.byte	4,3,35,252,1,0,14
	.word	17027
	.byte	3
	.word	17469
	.byte	4
	.byte	'IfxAsclin_enableCts',0,3,12,228,13,17,1,1,5
	.byte	'asclin',0,12,228,13,49
	.word	17474
	.byte	5
	.byte	'enable',0,12,228,13,65
	.word	628
	.byte	6,0,15,12,123,9,1,16
	.byte	'IfxAsclin_CtsInputSelect_0',0,0,16
	.byte	'IfxAsclin_CtsInputSelect_1',0,1,16
	.byte	'IfxAsclin_CtsInputSelect_2',0,2,16
	.byte	'IfxAsclin_CtsInputSelect_3',0,3,0,4
	.byte	'IfxAsclin_setCtsInput',0,3,12,169,17,17,1,1,5
	.byte	'asclin',0,12,169,17,51
	.word	17474
	.byte	5
	.byte	'ctsi',0,12,169,17,84
	.word	17541
	.byte	6,0,15,12,181,2,9,1,16
	.byte	'IfxAsclin_RxInputSelect_0',0,0,16
	.byte	'IfxAsclin_RxInputSelect_1',0,1,16
	.byte	'IfxAsclin_RxInputSelect_2',0,2,16
	.byte	'IfxAsclin_RxInputSelect_3',0,3,16
	.byte	'IfxAsclin_RxInputSelect_4',0,4,16
	.byte	'IfxAsclin_RxInputSelect_5',0,5,16
	.byte	'IfxAsclin_RxInputSelect_6',0,6,16
	.byte	'IfxAsclin_RxInputSelect_7',0,7,0,4
	.byte	'IfxAsclin_setRxInput',0,3,12,191,18,17,1,1,5
	.byte	'asclin',0,12,191,18,50
	.word	17474
	.byte	5
	.byte	'alti',0,12,191,18,82
	.word	17725
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'long int',0,4,5,20,14,60,9,12,13
	.byte	'count',0
	.word	18017
	.byte	2,2,35,0,13
	.byte	'readerWaitx',0
	.word	18030
	.byte	4,2,35,2,13
	.byte	'writerWaitx',0
	.word	18030
	.byte	4,2,35,6,13
	.byte	'maxcount',0
	.word	18017
	.byte	2,2,35,10,0,14
	.word	628
	.byte	14
	.word	628
	.byte	10
	.byte	'_Fifo',0,14,73,16,28,13
	.byte	'buffer',0
	.word	367
	.byte	4,2,35,0,13
	.byte	'shared',0
	.word	18042
	.byte	12,2,35,4,13
	.byte	'startIndex',0
	.word	18017
	.byte	2,2,35,16,13
	.byte	'endIndex',0
	.word	18017
	.byte	2,2,35,18,13
	.byte	'size',0
	.word	18017
	.byte	2,2,35,20,13
	.byte	'elementSize',0
	.word	18017
	.byte	2,2,35,22,13
	.byte	'eventReader',0
	.word	18123
	.byte	1,2,35,24,13
	.byte	'eventWriter',0
	.word	18128
	.byte	1,2,35,25,0,3
	.word	18133
	.byte	8
	.byte	'Ifx_Fifo_readCount',0,3,14,206,1,22
	.word	18017
	.byte	1,1,5
	.byte	'fifo',0,14,206,1,51
	.word	18292
	.byte	6,0,10
	.byte	'_Ifx_STM_CLC_Bits',0,16,100,16,4,11
	.byte	'DISR',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,12,16,149,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18344
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ID_Bits',0,16,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	645
	.byte	16,0,2,35,2,0,12,16,181,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18500
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0_Bits',0,16,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,229,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18622
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM1_Bits',0,16,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,245,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM2_Bits',0,16,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,253,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18792
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM3_Bits',0,16,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,133,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18877
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM4_Bits',0,16,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,141,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18963
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM5_Bits',0,16,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,149,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19049
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM6_Bits',0,16,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,157,3,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19135
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAP_Bits',0,16,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,133,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19221
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CMP_Bits',0,16,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,165,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19308
	.byte	4,2,35,0,0,18,8
	.word	19350
	.byte	19,1,0,10
	.byte	'_Ifx_STM_CMCON_Bits',0,16,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	628
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	628
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	628
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	628
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	628
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	628
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	3,0,2,35,3,0,12,16,157,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19399
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ICR_Bits',0,16,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	453
	.byte	25,0,2,35,0,0,12,16,173,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19630
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ISCR_Bits',0,16,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,12,16,189,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,16,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,237,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20011
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_CAPSV_Bits',0,16,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,141,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20098
	.byte	4,2,35,0,0,18,144,1
	.word	628
	.byte	19,143,1,0,10
	.byte	'_Ifx_STM_OCS_Bits',0,16,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	2,0,2,35,3,0,12,16,221,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20198
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,16,175,1,16,4,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,12,16,213,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20358
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST1_Bits',0,16,168,1,16,4,11
	.byte	'RST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,12,16,205,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20464
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_KRST0_Bits',0,16,160,1,16,4,11
	.byte	'RST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,12,16,197,2,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20568
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,12,16,253,1,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20691
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	628
	.byte	1,0,2,35,3,0,12,16,245,1,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20780
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_STM',0,16,173,3,25,128,2,13
	.byte	'CLC',0
	.word	18460
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	2999
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	18582
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2999
	.byte	4,2,35,12,13
	.byte	'TIM0',0
	.word	18667
	.byte	4,2,35,16,13
	.byte	'TIM1',0
	.word	18752
	.byte	4,2,35,20,13
	.byte	'TIM2',0
	.word	18837
	.byte	4,2,35,24,13
	.byte	'TIM3',0
	.word	18923
	.byte	4,2,35,28,13
	.byte	'TIM4',0
	.word	19009
	.byte	4,2,35,32,13
	.byte	'TIM5',0
	.word	19095
	.byte	4,2,35,36,13
	.byte	'TIM6',0
	.word	19181
	.byte	4,2,35,40,13
	.byte	'CAP',0
	.word	19268
	.byte	4,2,35,44,13
	.byte	'CMP',0
	.word	19390
	.byte	8,2,35,48,13
	.byte	'CMCON',0
	.word	19590
	.byte	4,2,35,56,13
	.byte	'ICR',0
	.word	19807
	.byte	4,2,35,60,13
	.byte	'ISCR',0
	.word	19971
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	5158
	.byte	12,2,35,68,13
	.byte	'TIM0SV',0
	.word	20058
	.byte	4,2,35,80,13
	.byte	'CAPSV',0
	.word	20147
	.byte	4,2,35,84,13
	.byte	'reserved_58',0
	.word	20187
	.byte	144,1,2,35,88,13
	.byte	'OCS',0
	.word	20318
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	20424
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	20528
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	20651
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	20740
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	21309
	.byte	4,3,35,252,1,0,14
	.word	21349
	.byte	3
	.word	21769
	.byte	8
	.byte	'IfxStm_get',0,3,15,162,4,19
	.word	335
	.byte	1,1,5
	.byte	'stm',0,15,162,4,39
	.word	21774
	.byte	6,0,8
	.byte	'IfxStm_getFrequency',0,3,15,179,4,20
	.word	249
	.byte	1,1,5
	.byte	'stm',0,15,179,4,49
	.word	21774
	.byte	17,6,6,0,0,8
	.byte	'IfxStm_getLower',0,3,15,190,4,19
	.word	1762
	.byte	1,1,5
	.byte	'stm',0,15,190,4,44
	.word	21774
	.byte	6,0,8
	.byte	'disableInterrupts',0,3,17,108,20
	.word	628
	.byte	1,1,17,6,0,0,4
	.byte	'restoreInterrupts',0,3,17,142,1,17,1,1,5
	.byte	'enabled',0,17,142,1,43
	.word	628
	.byte	17,6,0,0,7
	.byte	'long long int',0,8,5,8
	.byte	'getDeadLine',0,3,17,164,2,25
	.word	21990
	.byte	1,1,5
	.byte	'timeout',0,17,164,2,50
	.word	21990
	.byte	17,6,0,0,8
	.byte	'isDeadLine',0,3,17,211,2,20
	.word	628
	.byte	1,1,5
	.byte	'deadLine',0,17,211,2,44
	.word	21990
	.byte	17,6,0,0,8
	.byte	'now',0,3,17,221,1,25
	.word	21990
	.byte	1,1,17,6,6,6,0,0,8
	.byte	'nowWithoutCriticalSection',0,3,17,240,1,25
	.word	21990
	.byte	1,1,17,6,0,0,21
	.word	197
	.byte	22
	.word	223
	.byte	6,0,21
	.word	258
	.byte	22
	.word	290
	.byte	6,0,21
	.word	303
	.byte	6,0,21
	.word	372
	.byte	22
	.word	391
	.byte	6,0,21
	.word	407
	.byte	22
	.word	422
	.byte	22
	.word	436
	.byte	6,0,21
	.word	1215
	.byte	22
	.word	1255
	.byte	22
	.word	1273
	.byte	6,0,21
	.word	1293
	.byte	22
	.word	1331
	.byte	22
	.word	1349
	.byte	6,0,21
	.word	1369
	.byte	22
	.word	1420
	.byte	6,0,21
	.word	1519
	.byte	6,0,21
	.word	1553
	.byte	6,0,21
	.word	1595
	.byte	17,23
	.word	1553
	.byte	24
	.word	1593
	.byte	0,6,0,0,21
	.word	1636
	.byte	6,0,21
	.word	1670
	.byte	6,0,21
	.word	1710
	.byte	22
	.word	1743
	.byte	6,0,21
	.word	1783
	.byte	22
	.word	1824
	.byte	6,0,21
	.word	1843
	.byte	22
	.word	1898
	.byte	6,0,21
	.word	1917
	.byte	22
	.word	1957
	.byte	22
	.word	1974
	.byte	17,6,0,0,21
	.word	9876
	.byte	22
	.word	9908
	.byte	22
	.word	9922
	.byte	22
	.word	9940
	.byte	6,0,21
	.word	10243
	.byte	22
	.word	10276
	.byte	22
	.word	10290
	.byte	22
	.word	10308
	.byte	22
	.word	10322
	.byte	6,0,21
	.word	10442
	.byte	22
	.word	10470
	.byte	22
	.word	10484
	.byte	22
	.word	10502
	.byte	6,0,21
	.word	10520
	.byte	6,0,21
	.word	10899
	.byte	22
	.word	10927
	.byte	6,0,21
	.word	17479
	.byte	22
	.word	17507
	.byte	22
	.word	17523
	.byte	6,0,21
	.word	17663
	.byte	22
	.word	17693
	.byte	22
	.word	17709
	.byte	6,0,21
	.word	17956
	.byte	22
	.word	17985
	.byte	22
	.word	18001
	.byte	6,0,21
	.word	18297
	.byte	22
	.word	18328
	.byte	6,0,21
	.word	21779
	.byte	22
	.word	21802
	.byte	6,0,21
	.word	21817
	.byte	22
	.word	21849
	.byte	17,17,23
	.word	10520
	.byte	24
	.word	10558
	.byte	0,0,6,0,0,21
	.word	21867
	.byte	22
	.word	21895
	.byte	6,0,21
	.word	21910
	.byte	17,23
	.word	1595
	.byte	25
	.word	1632
	.byte	23
	.word	1553
	.byte	24
	.word	1593
	.byte	0,24
	.word	1633
	.byte	0,0,6,0,0,21
	.word	21943
	.byte	22
	.word	21969
	.byte	17,23
	.word	1710
	.byte	22
	.word	1743
	.byte	24
	.word	1760
	.byte	0,6,0,0,21
	.word	22007
	.byte	22
	.word	22031
	.byte	17,23
	.word	22097
	.byte	25
	.word	22113
	.byte	23
	.word	21910
	.byte	25
	.word	21939
	.byte	23
	.word	1595
	.byte	25
	.word	1632
	.byte	23
	.word	1553
	.byte	24
	.word	1593
	.byte	0,24
	.word	1633
	.byte	0,0,24
	.word	21940
	.byte	0,0,24
	.word	22114
	.byte	23
	.word	21943
	.byte	22
	.word	21969
	.byte	25
	.word	21986
	.byte	23
	.word	1710
	.byte	22
	.word	1743
	.byte	24
	.word	1760
	.byte	0,24
	.word	21987
	.byte	0,0,24
	.word	22115
	.byte	23
	.word	21779
	.byte	22
	.word	21802
	.byte	24
	.word	21815
	.byte	0,24
	.word	22116
	.byte	0,0,6,0,0,21
	.word	22052
	.byte	22
	.word	22075
	.byte	17,23
	.word	22097
	.byte	25
	.word	22113
	.byte	23
	.word	21910
	.byte	25
	.word	21939
	.byte	23
	.word	1595
	.byte	25
	.word	1632
	.byte	23
	.word	1553
	.byte	24
	.word	1593
	.byte	0,24
	.word	1633
	.byte	0,0,24
	.word	21940
	.byte	0,0,24
	.word	22114
	.byte	23
	.word	21943
	.byte	22
	.word	21969
	.byte	25
	.word	21986
	.byte	23
	.word	1710
	.byte	22
	.word	1743
	.byte	24
	.word	1760
	.byte	0,24
	.word	21987
	.byte	0,0,24
	.word	22115
	.byte	23
	.word	21779
	.byte	22
	.word	21802
	.byte	24
	.word	21815
	.byte	0,24
	.word	22116
	.byte	0,0,6,0,0,21
	.word	22097
	.byte	17,23
	.word	21910
	.byte	25
	.word	21939
	.byte	23
	.word	1595
	.byte	25
	.word	1632
	.byte	23
	.word	1553
	.byte	24
	.word	1593
	.byte	0,24
	.word	1633
	.byte	0,0,24
	.word	21940
	.byte	0,0,6,23
	.word	21943
	.byte	22
	.word	21969
	.byte	25
	.word	21986
	.byte	23
	.word	1710
	.byte	22
	.word	1743
	.byte	24
	.word	1760
	.byte	0,24
	.word	21987
	.byte	0,0,6,23
	.word	21779
	.byte	22
	.word	21802
	.byte	24
	.word	21815
	.byte	0,6,0,0,21
	.word	22119
	.byte	17,23
	.word	21779
	.byte	22
	.word	21802
	.byte	24
	.word	21815
	.byte	0,6,0,0,15,18,103,9,1,16
	.byte	'UART_0',0,0,16
	.byte	'UART_1',0,1,16
	.byte	'UART_2',0,2,16
	.byte	'UART_3',0,3,0,26
	.word	628
.L130:
	.byte	3
	.word	23098
	.byte	27
	.byte	'uart_write_buffer',0,18,119,9,1,1,1,1,5
	.byte	'uartn',0,18,119,62
	.word	23056
	.byte	5
	.byte	'buff',0,18,119,82
	.word	23103
	.byte	5
	.byte	'len',0,18,119,95
	.word	1762
	.byte	0
.L134:
	.byte	3
	.word	628
	.byte	28
	.byte	'uart_query_byte',0,18,123,9
	.word	628
	.byte	1,1,1,1,5
	.byte	'uartn',0,18,123,62
	.word	23056
	.byte	5
	.byte	'dat',0,18,123,76
	.word	23174
	.byte	0,27
	.byte	'uart_rx_interrupt',0,18,126,9,1,1,1,1,5
	.byte	'uartn',0,18,126,62
	.word	23056
	.byte	5
	.byte	'status',0,18,126,76
	.word	1762
	.byte	0,15,18,43,9,1,16
	.byte	'UART0_TX_P14_0',0,0,16
	.byte	'UART0_TX_P14_1',0,1,16
	.byte	'UART0_TX_P15_2',0,2,16
	.byte	'UART0_TX_P15_3',0,3,16
	.byte	'UART1_TX_P02_2',0,4,16
	.byte	'UART1_TX_P11_12',0,5,16
	.byte	'UART1_TX_P15_0',0,6,16
	.byte	'UART1_TX_P15_1',0,7,16
	.byte	'UART1_TX_P15_4',0,8,16
	.byte	'UART1_TX_P15_5',0,9,16
	.byte	'UART1_TX_P20_10',0,10,16
	.byte	'UART1_TX_P33_12',0,11,16
	.byte	'UART1_TX_P33_13',0,12,16
	.byte	'UART2_TX_P02_0',0,13,16
	.byte	'UART2_TX_P10_5',0,14,16
	.byte	'UART2_TX_P14_2',0,15,16
	.byte	'UART2_TX_P14_3',0,16,16
	.byte	'UART2_TX_P33_8',0,17,16
	.byte	'UART2_TX_P33_9',0,18,16
	.byte	'UART3_TX_P00_0',0,19,16
	.byte	'UART3_TX_P00_1',0,20,16
	.byte	'UART3_TX_P15_6',0,21,16
	.byte	'UART3_TX_P15_7',0,22,16
	.byte	'UART3_TX_P20_0',0,23,16
	.byte	'UART3_TX_P20_3',0,24,16
	.byte	'UART3_TX_P21_7',0,25,0,15,18,77,9,1,16
	.byte	'UART0_RX_P14_1',0,0,16
	.byte	'UART0_RX_P15_3',0,1,16
	.byte	'UART1_RX_P02_3',0,2,16
	.byte	'UART1_RX_P11_10',0,3,16
	.byte	'UART1_RX_P15_1',0,4,16
	.byte	'UART1_RX_P15_5',0,5,16
	.byte	'UART1_RX_P20_9',0,6,16
	.byte	'UART1_RX_P33_13',0,7,16
	.byte	'UART2_RX_P02_0',0,8,16
	.byte	'UART2_RX_P02_1',0,9,16
	.byte	'UART2_RX_P10_6',0,10,16
	.byte	'UART2_RX_P14_3',0,11,16
	.byte	'UART2_RX_P33_8',0,12,16
	.byte	'UART3_RX_P00_1',0,13,16
	.byte	'UART3_RX_P15_7',0,14,16
	.byte	'UART3_RX_P20_3',0,15,16
	.byte	'UART3_RX_P21_6',0,16,0,27
	.byte	'uart_init',0,18,129,1,9,1,1,1,1,5
	.byte	'uartn',0,18,129,1,62
	.word	23056
	.byte	5
	.byte	'baud',0,18,129,1,76
	.word	1762
	.byte	5
	.byte	'tx_pin',0,18,129,1,99
	.word	23290
	.byte	5
	.byte	'rx_pin',0,18,129,1,124
	.word	23742
	.byte	0,27
	.byte	'system_delay_ms',0,19,46,9,1,1,1,1,5
	.byte	'time',0,19,46,45
	.word	1762
	.byte	0
.L144:
	.byte	15,20,41,9,1,16
	.byte	'U1_ADAPTER_OK',0,0,16
	.byte	'U1_ADAPTER_ERROR_INIT_FAILED',0,1,16
	.byte	'U1_ADAPTER_ERROR_SEND_FAILED',0,2,16
	.byte	'U1_ADAPTER_ERROR_RX_OVERFLOW',0,3,16
	.byte	'U1_ADAPTER_ERROR_INVALID_PARAM',0,4,0,7
	.byte	'char',0,1,6,26
	.word	24306
.L147:
	.byte	3
	.word	24314
	.byte	29
	.byte	'system_getval',0,21,43,9
	.word	1762
	.byte	1,1,1,1,30
	.byte	'__wchar_t',0,22,1,1
	.word	18017
	.byte	30
	.byte	'__size_t',0,22,1,1
	.word	453
	.byte	30
	.byte	'__ptrdiff_t',0,22,1,1
	.word	469
	.byte	31,1,3
	.word	24405
	.byte	30
	.byte	'__codeptr',0,22,1,1
	.word	24407
	.byte	30
	.byte	'__intptr_t',0,22,1,1
	.word	469
	.byte	30
	.byte	'__uintptr_t',0,22,1,1
	.word	453
	.byte	30
	.byte	'boolean',0,23,101,29
	.word	628
	.byte	30
	.byte	'uint8',0,23,105,29
	.word	628
	.byte	30
	.byte	'uint16',0,23,109,29
	.word	645
	.byte	30
	.byte	'uint32',0,23,113,29
	.word	1762
	.byte	30
	.byte	'uint64',0,23,118,29
	.word	335
	.byte	30
	.byte	'sint16',0,23,126,29
	.word	18017
	.byte	30
	.byte	'sint32',0,23,131,1,29
	.word	18030
	.byte	30
	.byte	'sint64',0,23,138,1,29
	.word	21990
	.byte	30
	.byte	'float32',0,23,167,1,29
	.word	249
	.byte	30
	.byte	'pvoid',0,24,57,28
	.word	367
	.byte	30
	.byte	'Ifx_TickTime',0,24,79,28
	.word	21990
	.byte	30
	.byte	'Ifx_SizeT',0,24,92,16
	.word	18017
	.byte	30
	.byte	'Ifx_Priority',0,24,103,16
	.word	645
	.byte	15,24,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,30
	.byte	'Ifx_RxSel',0,24,140,1,3
	.word	24682
	.byte	15,24,164,1,9,1,16
	.byte	'Ifx_DataBufferMode_normal',0,0,16
	.byte	'Ifx_DataBufferMode_timeStampSingle',0,1,0,30
	.byte	'Ifx_DataBufferMode',0,24,169,1,2
	.word	24820
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN0_Bits',0,13,79,3
	.word	16455
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN1_Bits',0,13,85,3
	.word	16363
	.byte	30
	.byte	'Ifx_ASCLIN_BITCON_Bits',0,13,97,3
	.word	11987
	.byte	30
	.byte	'Ifx_ASCLIN_BRD_Bits',0,13,106,3
	.word	12827
	.byte	30
	.byte	'Ifx_ASCLIN_BRG_Bits',0,13,115,3
	.word	12670
	.byte	30
	.byte	'Ifx_ASCLIN_CLC_Bits',0,13,125,3
	.word	10942
	.byte	30
	.byte	'Ifx_ASCLIN_CSR_Bits',0,13,133,1,3
	.word	15636
	.byte	30
	.byte	'Ifx_ASCLIN_DATCON_Bits',0,13,145,1,3
	.word	12473
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGS_Bits',0,13,177,1,3
	.word	13483
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSCLEAR_Bits',0,13,209,1,3
	.word	14482
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSENABLE_Bits',0,13,238,1,3
	.word	14997
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSSET_Bits',0,13,142,2,3
	.word	13969
	.byte	30
	.byte	'Ifx_ASCLIN_FRAMECON_Bits',0,13,158,2,3
	.word	12208
	.byte	30
	.byte	'Ifx_ASCLIN_ID_Bits',0,13,166,2,3
	.word	11396
	.byte	30
	.byte	'Ifx_ASCLIN_IOCR_Bits',0,13,184,2,3
	.word	11101
	.byte	30
	.byte	'Ifx_ASCLIN_KRST0_Bits',0,13,192,2,3
	.word	16237
	.byte	30
	.byte	'Ifx_ASCLIN_KRST1_Bits',0,13,199,2,3
	.word	16130
	.byte	30
	.byte	'Ifx_ASCLIN_KRSTCLR_Bits',0,13,206,2,3
	.word	16021
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_BTIMER_Bits',0,13,213,2,3
	.word	13181
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_CON_Bits',0,13,225,2,3
	.word	12981
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_HTIMER_Bits',0,13,232,2,3
	.word	13295
	.byte	30
	.byte	'Ifx_ASCLIN_OCS_Bits',0,13,242,2,3
	.word	15858
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATA_Bits',0,13,248,2,3
	.word	15549
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATAD_Bits',0,13,254,2,3
	.word	15759
	.byte	30
	.byte	'Ifx_ASCLIN_RXFIFOCON_Bits',0,13,140,3,3
	.word	11746
	.byte	30
	.byte	'Ifx_ASCLIN_TXDATA_Bits',0,13,146,3,3
	.word	15462
	.byte	30
	.byte	'Ifx_ASCLIN_TXFIFOCON_Bits',0,13,159,3,3
	.word	11521
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN0',0,13,172,3,3
	.word	16987
	.byte	30
	.byte	'Ifx_ASCLIN_ACCEN1',0,13,180,3,3
	.word	16415
	.byte	30
	.byte	'Ifx_ASCLIN_BITCON',0,13,188,3,3
	.word	12168
	.byte	30
	.byte	'Ifx_ASCLIN_BRD',0,13,196,3,3
	.word	12941
	.byte	30
	.byte	'Ifx_ASCLIN_BRG',0,13,204,3,3
	.word	12787
	.byte	30
	.byte	'Ifx_ASCLIN_CLC',0,13,212,3,3
	.word	11061
	.byte	30
	.byte	'Ifx_ASCLIN_CSR',0,13,220,3,3
	.word	15719
	.byte	30
	.byte	'Ifx_ASCLIN_DATCON',0,13,228,3,3
	.word	12630
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGS',0,13,236,3,3
	.word	13929
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSCLEAR',0,13,244,3,3
	.word	14957
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSENABLE',0,13,252,3,3
	.word	15422
	.byte	30
	.byte	'Ifx_ASCLIN_FLAGSSET',0,13,132,4,3
	.word	14442
	.byte	30
	.byte	'Ifx_ASCLIN_FRAMECON',0,13,140,4,3
	.word	12433
	.byte	30
	.byte	'Ifx_ASCLIN_ID',0,13,148,4,3
	.word	11481
	.byte	30
	.byte	'Ifx_ASCLIN_IOCR',0,13,156,4,3
	.word	11356
	.byte	30
	.byte	'Ifx_ASCLIN_KRST0',0,13,164,4,3
	.word	16323
	.byte	30
	.byte	'Ifx_ASCLIN_KRST1',0,13,172,4,3
	.word	16197
	.byte	30
	.byte	'Ifx_ASCLIN_KRSTCLR',0,13,180,4,3
	.word	16090
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_BTIMER',0,13,188,4,3
	.word	13255
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_CON',0,13,196,4,3
	.word	13141
	.byte	30
	.byte	'Ifx_ASCLIN_LIN_HTIMER',0,13,204,4,3
	.word	13370
	.byte	30
	.byte	'Ifx_ASCLIN_OCS',0,13,212,4,3
	.word	15981
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATA',0,13,220,4,3
	.word	15596
	.byte	30
	.byte	'Ifx_ASCLIN_RXDATAD',0,13,228,4,3
	.word	15807
	.byte	30
	.byte	'Ifx_ASCLIN_RXFIFOCON',0,13,236,4,3
	.word	11947
	.byte	30
	.byte	'Ifx_ASCLIN_TXDATA',0,13,244,4,3
	.word	15509
	.byte	30
	.byte	'Ifx_ASCLIN_TXFIFOCON',0,13,252,4,3
	.word	11706
	.byte	14
	.word	13410
	.byte	30
	.byte	'Ifx_ASCLIN_LIN',0,13,140,5,3
	.word	26519
	.byte	14
	.word	17027
	.byte	30
	.byte	'Ifx_ASCLIN',0,13,181,5,3
	.word	26548
	.byte	15,25,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,30
	.byte	'IfxScu_CCUCON0_CLKSEL',0,25,240,10,3
	.word	26573
	.byte	15,25,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,30
	.byte	'IfxScu_WDTCON1_IR',0,25,255,10,3
	.word	26670
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	26792
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	27349
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	453
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	27426
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	628
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	628
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	628
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	628
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	628
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	27562
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	628
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	628
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	628
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	628
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	27842
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	28080
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	628
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	628
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	28208
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	628
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	628
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	28451
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	28686
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	28814
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	28914
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	628
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	628
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	29014
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	453
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	29222
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	645
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	645
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	29387
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	645
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	29570
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	628
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	628
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	453
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	628
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	628
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	29724
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	30088
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	645
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	628
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	628
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	628
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	30299
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	645
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	453
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	30551
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	30669
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	30780
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	30943
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	31106
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	31264
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	628
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	628
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	628
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	628
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	628
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	628
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	645
	.byte	10,0,2,35,2,0,30
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	31429
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	645
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	628
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	628
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	645
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	31758
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	31979
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	32142
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	32414
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	32567
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	32723
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	32885
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	33028
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	33193
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	645
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	33338
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	628
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	33519
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	33693
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	453
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	33853
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	453
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	33997
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	34271
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	34410
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	628
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	645
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	628
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	628
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	34573
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	645
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	628
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	645
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	34791
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	34954
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	35290
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	628
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	35397
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	35849
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	628
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	35948
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	645
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	36098
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	453
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	36247
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	453
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	36408
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	645
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	645
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	36538
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	36670
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	645
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	36785
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	645
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	645
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	36896
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	628
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	628
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	628
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	628
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	37054
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	37466
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	645
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	6,0,2,35,3,0,30
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	37567
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	453
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	37834
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	37970
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	628
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	38081
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	38214
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	38417
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	628
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	628
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	628
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	645
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	38773
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	645
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	38951
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	645
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	628
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	628
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	628
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	39051
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	628
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	628
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	628
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	645
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	39421
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	39607
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	39805
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	628
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	453
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	40038
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	628
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	628
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	628
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	628
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	40190
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	628
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	628
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	628
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	628
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	40757
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	628
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	628
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	628
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	41051
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	628
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	628
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	645
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	628
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	41329
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	645
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	41825
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	645
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	42138
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	628
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	628
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	628
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	628
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	42347
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	628
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	628
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	42558
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	42990
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	628
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	628
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	628
	.byte	7,0,2,35,3,0,30
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	43086
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	453
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	43346
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	628
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	453
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	43471
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	43668
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	43821
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	43974
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	44127
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	492
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	667
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	911
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	476
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	44382
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	44508
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	44760
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26792
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	44979
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27349
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	45043
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27426
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	45107
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27562
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	45172
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27842
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	45237
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28080
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	45302
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28208
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	45367
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28451
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	45432
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28686
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	45497
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28814
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	45562
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28914
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	45627
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29014
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	45692
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29222
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	45756
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29387
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	45820
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29570
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	45884
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29724
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	45949
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30088
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	46011
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30299
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	46073
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30551
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	46135
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30669
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	46199
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30780
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	46264
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30943
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	46330
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31106
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	46396
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31264
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	46464
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31429
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	46531
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31758
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	46599
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31979
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	46667
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32142
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	46733
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32414
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	46800
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32567
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	46869
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32723
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	46938
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32885
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	47007
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33028
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	47076
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33193
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	47145
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33338
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	47214
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33519
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	47282
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33693
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	47350
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33853
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	47418
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33997
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	47486
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34271
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	47551
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34410
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	47616
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34573
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	47682
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34791
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	47746
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34954
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	47807
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35290
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	47868
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35397
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	47928
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35849
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	47990
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35948
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	48050
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36098
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	48112
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36247
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	48180
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36408
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	48248
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36538
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	48316
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36670
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	48380
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36785
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	48445
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36896
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	48508
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37054
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	48569
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37466
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	48633
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37567
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	48694
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37834
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	48758
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37970
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	48825
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38081
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	48888
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38214
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	48949
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38417
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	49011
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38773
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	49076
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38951
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	49141
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39051
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	49206
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39421
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	49275
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39607
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	49344
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39805
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	49413
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40038
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	49478
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40190
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	49541
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40757
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	49606
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41051
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	49671
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41329
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	49736
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41825
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	49802
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42347
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	49871
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42138
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	49935
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42558
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	50000
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42990
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	50065
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43086
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	50130
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43346
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	50194
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43471
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	50260
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43668
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	50324
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43821
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	50389
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43974
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	50454
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44127
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	50519
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	588
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	871
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1102
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44382
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	50670
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44508
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	50737
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44760
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	50804
	.byte	14
	.word	1142
	.byte	30
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	50869
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	50670
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	50737
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	50804
	.byte	4,2,35,8,0,14
	.word	50898
	.byte	30
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	50959
	.byte	18,8
	.word	46135
	.byte	19,1,0,18,20
	.word	628
	.byte	19,19,0,18,8
	.word	49478
	.byte	19,1,0,14
	.word	50898
	.byte	18,24
	.word	1142
	.byte	19,1,0,14
	.word	51018
	.byte	18,16
	.word	628
	.byte	19,15,0,18,28
	.word	628
	.byte	19,27,0,18,40
	.word	628
	.byte	19,39,0,18,16
	.word	45949
	.byte	19,3,0,18,16
	.word	47928
	.byte	19,3,0,18,180,3
	.word	628
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4818
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	47868
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2999
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	48569
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	49413
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	49011
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	49076
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	49141
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	49344
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	49206
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	49275
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	45172
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	45237
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	47746
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	47682
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	45302
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	45367
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	45432
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	45497
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	50000
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2999
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	49871
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	45107
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	50194
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	49935
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2999
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	46733
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	50986
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	46199
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	50260
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	45562
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	45627
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	50995
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	48888
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	48050
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	48633
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	48508
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	47990
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	47486
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	46464
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	46264
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	46330
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	50130
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2999
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	49541
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	49736
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	49802
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	51004
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2999
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	45884
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	45756
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	49606
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	49671
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	51013
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	46073
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	51027
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5158
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	50519
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	50454
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	50324
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	50389
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2999
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	48316
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	48380
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	45692
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	48445
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4818
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	50065
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	51032
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	48112
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	48180
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	48248
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	51041
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	48825
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4818
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	47551
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	46396
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	47616
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	46667
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	46531
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2999
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	47214
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	47282
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	47350
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	47418
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	46800
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	46869
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	46938
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	47007
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	47076
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	47145
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	46599
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2999
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	48758
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	48694
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	51050
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	51059
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	46011
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	47807
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	48949
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	51068
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2999
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	45820
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	51077
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	45043
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	44979
	.byte	4,3,35,252,7,0,14
	.word	51088
	.byte	30
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	53078
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,26,45,16,4,11
	.byte	'ADDR',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_A_Bits',0,26,48,3
	.word	53100
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,26,51,16,4,11
	.byte	'VSS',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	476
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BIV_Bits',0,26,55,3
	.word	53161
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,26,58,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	476
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BTV_Bits',0,26,62,3
	.word	53240
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,26,65,16,4,11
	.byte	'CountValue',0,4
	.word	476
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT_Bits',0,26,69,3
	.word	53326
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,26,72,16,4,11
	.byte	'CM',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	476
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	476
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	476
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	476
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL_Bits',0,26,80,3
	.word	53415
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,26,83,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	476
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT_Bits',0,26,89,3
	.word	53561
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,26,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID_Bits',0,26,96,3
	.word	53688
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,26,99,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L_Bits',0,26,103,3
	.word	53786
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,26,106,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U_Bits',0,26,110,3
	.word	53879
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,26,113,16,4,11
	.byte	'MODREV',0,4
	.word	476
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	476
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID_Bits',0,26,118,3
	.word	53972
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,26,121,16,4,11
	.byte	'XE',0,4
	.word	476
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE_Bits',0,26,125,3
	.word	54079
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,26,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	476
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT_Bits',0,26,136,1,3
	.word	54166
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,26,139,1,16,4,11
	.byte	'CID',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID_Bits',0,26,143,1,3
	.word	54320
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,26,146,1,16,4,11
	.byte	'DATA',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_D_Bits',0,26,149,1,3
	.word	54414
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,26,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	476
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	476
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	476
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	476
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	476
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	476
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DATR_Bits',0,26,163,1,3
	.word	54477
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,26,166,1,16,4,11
	.byte	'DE',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	476
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	476
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	476
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	476
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	476
	.byte	19,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR_Bits',0,26,177,1,3
	.word	54695
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,26,180,1,16,4,11
	.byte	'DTA',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	476
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR_Bits',0,26,184,1,3
	.word	54910
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,26,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	476
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0_Bits',0,26,192,1,3
	.word	55004
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,26,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2_Bits',0,26,199,1,3
	.word	55120
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,26,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	476
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCX_Bits',0,26,206,1,3
	.word	55221
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,26,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD_Bits',0,26,212,1,3
	.word	55314
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,26,215,1,16,4,11
	.byte	'TA',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR_Bits',0,26,218,1,3
	.word	55394
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,26,221,1,16,4,11
	.byte	'IED',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	476
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	476
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	476
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	476
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	476
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR_Bits',0,26,233,1,3
	.word	55463
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,26,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	476
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DMS_Bits',0,26,240,1,3
	.word	55692
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,26,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L_Bits',0,26,247,1,3
	.word	55785
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,26,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	476
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U_Bits',0,26,254,1,3
	.word	55880
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,26,129,2,16,4,11
	.byte	'RE',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE_Bits',0,26,133,2,3
	.word	55975
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,26,136,2,16,4,11
	.byte	'WE',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE_Bits',0,26,140,2,3
	.word	56065
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,26,143,2,16,4,11
	.byte	'SRE',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	476
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	476
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	476
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	476
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	476
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	476
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	476
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	476
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	476
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	476
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	476
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	476
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR_Bits',0,26,161,2,3
	.word	56155
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,26,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	476
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT_Bits',0,26,172,2,3
	.word	56479
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,26,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	476
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	476
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FCX_Bits',0,26,180,2,3
	.word	56633
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,26,183,2,16,4,11
	.byte	'TST',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	476
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	476
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	476
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	476
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	476
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	476
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	476
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	476
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	476
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	476
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	476
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	476
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	476
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	476
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,26,202,2,3
	.word	56739
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,205,2,16,4,11
	.byte	'OPC',0,4
	.word	476
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	476
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	476
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	476
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	476
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,26,212,2,3
	.word	57088
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,26,215,2,16,4,11
	.byte	'PC',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,26,218,2,3
	.word	57248
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,26,224,2,3
	.word	57329
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,26,230,2,3
	.word	57416
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,26,236,2,3
	.word	57503
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,26,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	476
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT_Bits',0,26,243,2,3
	.word	57590
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,26,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	476
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	476
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	476
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	476
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	476
	.byte	6,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICR_Bits',0,26,253,2,3
	.word	57681
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,26,128,3,16,4,11
	.byte	'ISP',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_ISP_Bits',0,26,131,3,3
	.word	57824
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,26,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	476
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	476
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_LCX_Bits',0,26,139,3,3
	.word	57890
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,26,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	476
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT_Bits',0,26,146,3,3
	.word	57996
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,26,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	476
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT_Bits',0,26,153,3,3
	.word	58089
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,26,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	476
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT_Bits',0,26,160,3,3
	.word	58182
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,26,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	476
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_PC_Bits',0,26,167,3,3
	.word	58275
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,26,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	476
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0_Bits',0,26,175,3,3
	.word	58360
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,26,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	476
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1_Bits',0,26,183,3,3
	.word	58476
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,26,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2_Bits',0,26,190,3,3
	.word	58587
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,26,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	476
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	476
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	476
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	476
	.byte	10,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI_Bits',0,26,200,3,3
	.word	58688
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,26,203,3,16,4,11
	.byte	'TA',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR_Bits',0,26,206,3,3
	.word	58818
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,26,209,3,16,4,11
	.byte	'IED',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	476
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	476
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	476
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	476
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	476
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR_Bits',0,26,221,3,3
	.word	58887
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,26,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	476
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0_Bits',0,26,229,3,3
	.word	59116
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,26,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	476
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	476
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1_Bits',0,26,237,3,3
	.word	59229
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,26,240,3,16,4,11
	.byte	'PSI',0,4
	.word	476
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	476
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2_Bits',0,26,244,3,3
	.word	59342
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,26,247,3,16,4,11
	.byte	'FRE',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	476
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	476
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	476
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	476
	.byte	17,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR_Bits',0,26,129,4,3
	.word	59433
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,26,132,4,16,4,11
	.byte	'CDC',0,4
	.word	476
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	476
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	476
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	476
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	476
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	476
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	476
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	476
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	476
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	476
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	476
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	476
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSW_Bits',0,26,147,4,3
	.word	59636
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,26,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	476
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	476
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	476
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	476
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN_Bits',0,26,156,4,3
	.word	59879
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,26,159,4,16,4,11
	.byte	'PC',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	476
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	476
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	476
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	476
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	476
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	476
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON_Bits',0,26,171,4,3
	.word	60007
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,26,174,4,16,4,11
	.byte	'EN',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,26,177,4,3
	.word	60248
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,26,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,26,183,4,3
	.word	60331
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,186,4,16,4,11
	.byte	'EN',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,26,189,4,3
	.word	60422
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,26,195,4,3
	.word	60513
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,26,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	453
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,26,202,4,3
	.word	60612
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,26,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	453
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,26,209,4,3
	.word	60719
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,26,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	476
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT_Bits',0,26,220,4,3
	.word	60826
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,26,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	476
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON_Bits',0,26,231,4,3
	.word	60980
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,26,234,4,16,4,11
	.byte	'ASI',0,4
	.word	476
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	476
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,26,238,4,3
	.word	61141
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,26,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	476
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	476
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	476
	.byte	15,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON_Bits',0,26,249,4,3
	.word	61239
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,26,252,4,16,4,11
	.byte	'Timer',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,26,255,4,3
	.word	61411
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,26,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	476
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR_Bits',0,26,133,5,3
	.word	61491
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,26,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	476
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	476
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	476
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	476
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	476
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	476
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	476
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	476
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	476
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	476
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	476
	.byte	3,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT_Bits',0,26,153,5,3
	.word	61564
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,26,156,5,16,4,11
	.byte	'T0',0,4
	.word	476
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	476
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	476
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	476
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	476
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	476
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	476
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	476
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	476
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,26,167,5,3
	.word	61882
	.byte	12,26,175,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53100
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_A',0,26,180,5,3
	.word	62077
	.byte	12,26,183,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53161
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BIV',0,26,188,5,3
	.word	62136
	.byte	12,26,191,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53240
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BTV',0,26,196,5,3
	.word	62197
	.byte	12,26,199,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53326
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT',0,26,204,5,3
	.word	62258
	.byte	12,26,207,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53415
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL',0,26,212,5,3
	.word	62320
	.byte	12,26,215,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53561
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT',0,26,220,5,3
	.word	62383
	.byte	12,26,223,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53688
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID',0,26,228,5,3
	.word	62447
	.byte	12,26,231,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53786
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L',0,26,236,5,3
	.word	62512
	.byte	12,26,239,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53879
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U',0,26,244,5,3
	.word	62575
	.byte	12,26,247,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53972
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID',0,26,252,5,3
	.word	62638
	.byte	12,26,255,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54079
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE',0,26,132,6,3
	.word	62702
	.byte	12,26,135,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54166
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT',0,26,140,6,3
	.word	62764
	.byte	12,26,143,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54320
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID',0,26,148,6,3
	.word	62827
	.byte	12,26,151,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54414
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_D',0,26,156,6,3
	.word	62891
	.byte	12,26,159,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54477
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DATR',0,26,164,6,3
	.word	62950
	.byte	12,26,167,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54695
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR',0,26,172,6,3
	.word	63012
	.byte	12,26,175,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54910
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR',0,26,180,6,3
	.word	63075
	.byte	12,26,183,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55004
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0',0,26,188,6,3
	.word	63139
	.byte	12,26,191,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55120
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2',0,26,196,6,3
	.word	63202
	.byte	12,26,199,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55221
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCX',0,26,204,6,3
	.word	63265
	.byte	12,26,207,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55314
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD',0,26,212,6,3
	.word	63326
	.byte	12,26,215,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55394
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR',0,26,220,6,3
	.word	63389
	.byte	12,26,223,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55463
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR',0,26,228,6,3
	.word	63452
	.byte	12,26,231,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55692
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DMS',0,26,236,6,3
	.word	63515
	.byte	12,26,239,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55785
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L',0,26,244,6,3
	.word	63576
	.byte	12,26,247,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55880
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U',0,26,252,6,3
	.word	63639
	.byte	12,26,255,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55975
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE',0,26,132,7,3
	.word	63702
	.byte	12,26,135,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56065
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE',0,26,140,7,3
	.word	63764
	.byte	12,26,143,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56155
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR',0,26,148,7,3
	.word	63826
	.byte	12,26,151,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56479
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT',0,26,156,7,3
	.word	63888
	.byte	12,26,159,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56633
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FCX',0,26,164,7,3
	.word	63951
	.byte	12,26,167,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56739
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,26,172,7,3
	.word	64012
	.byte	12,26,175,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57088
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,26,180,7,3
	.word	64082
	.byte	12,26,183,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57248
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,26,188,7,3
	.word	64152
	.byte	12,26,191,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57329
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,26,196,7,3
	.word	64221
	.byte	12,26,199,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57416
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,26,204,7,3
	.word	64292
	.byte	12,26,207,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57503
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,26,212,7,3
	.word	64363
	.byte	12,26,215,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57590
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT',0,26,220,7,3
	.word	64434
	.byte	12,26,223,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57681
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICR',0,26,228,7,3
	.word	64496
	.byte	12,26,231,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57824
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ISP',0,26,236,7,3
	.word	64557
	.byte	12,26,239,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57890
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_LCX',0,26,244,7,3
	.word	64618
	.byte	12,26,247,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57996
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT',0,26,252,7,3
	.word	64679
	.byte	12,26,255,7,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58089
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT',0,26,132,8,3
	.word	64742
	.byte	12,26,135,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58182
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT',0,26,140,8,3
	.word	64805
	.byte	12,26,143,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58275
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PC',0,26,148,8,3
	.word	64868
	.byte	12,26,151,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58360
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0',0,26,156,8,3
	.word	64928
	.byte	12,26,159,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58476
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1',0,26,164,8,3
	.word	64991
	.byte	12,26,167,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58587
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2',0,26,172,8,3
	.word	65054
	.byte	12,26,175,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58688
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI',0,26,180,8,3
	.word	65117
	.byte	12,26,183,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58818
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR',0,26,188,8,3
	.word	65179
	.byte	12,26,191,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58887
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR',0,26,196,8,3
	.word	65242
	.byte	12,26,199,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59116
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0',0,26,204,8,3
	.word	65305
	.byte	12,26,207,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59229
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1',0,26,212,8,3
	.word	65367
	.byte	12,26,215,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59342
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2',0,26,220,8,3
	.word	65429
	.byte	12,26,223,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59433
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR',0,26,228,8,3
	.word	65491
	.byte	12,26,231,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59636
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSW',0,26,236,8,3
	.word	65553
	.byte	12,26,239,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59879
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN',0,26,244,8,3
	.word	65614
	.byte	12,26,247,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60007
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON',0,26,252,8,3
	.word	65677
	.byte	12,26,255,8,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60248
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA',0,26,132,9,3
	.word	65741
	.byte	12,26,135,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60331
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB',0,26,140,9,3
	.word	65811
	.byte	12,26,143,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60422
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,26,148,9,3
	.word	65881
	.byte	12,26,151,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60513
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,26,156,9,3
	.word	65955
	.byte	12,26,159,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60612
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,26,164,9,3
	.word	66029
	.byte	12,26,167,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60719
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,26,172,9,3
	.word	66099
	.byte	12,26,175,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60826
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT',0,26,180,9,3
	.word	66169
	.byte	12,26,183,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60980
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON',0,26,188,9,3
	.word	66232
	.byte	12,26,191,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61141
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI',0,26,196,9,3
	.word	66296
	.byte	12,26,199,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61239
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON',0,26,204,9,3
	.word	66362
	.byte	12,26,207,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61411
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER',0,26,212,9,3
	.word	66427
	.byte	12,26,215,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61491
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR',0,26,220,9,3
	.word	66494
	.byte	12,26,223,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61564
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT',0,26,228,9,3
	.word	66558
	.byte	12,26,231,9,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61882
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC',0,26,236,9,3
	.word	66622
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,26,247,9,25,8,13
	.byte	'L',0
	.word	62512
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	62575
	.byte	4,2,35,4,0,14
	.word	66688
	.byte	30
	.byte	'Ifx_CPU_CPR',0,26,251,9,3
	.word	66730
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,26,254,9,25,8,13
	.byte	'L',0
	.word	63576
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	63639
	.byte	4,2,35,4,0,14
	.word	66756
	.byte	30
	.byte	'Ifx_CPU_DPR',0,26,130,10,3
	.word	66798
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,26,133,10,25,16,13
	.byte	'LA',0
	.word	66029
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	66099
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	65881
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	65955
	.byte	4,2,35,12,0,14
	.word	66824
	.byte	30
	.byte	'Ifx_CPU_SPROT_RGN',0,26,139,10,3
	.word	66906
	.byte	18,12
	.word	66427
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,26,142,10,25,16,13
	.byte	'CON',0
	.word	66362
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	66938
	.byte	12,2,35,4,0,14
	.word	66947
	.byte	30
	.byte	'Ifx_CPU_TPS',0,26,146,10,3
	.word	66995
	.byte	10
	.byte	'_Ifx_CPU_TR',0,26,149,10,25,8,13
	.byte	'EVT',0
	.word	66558
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	66494
	.byte	4,2,35,4,0,14
	.word	67021
	.byte	30
	.byte	'Ifx_CPU_TR',0,26,153,10,3
	.word	67066
	.byte	18,176,32
	.word	628
	.byte	19,175,32,0,18,208,223,1
	.word	628
	.byte	19,207,223,1,0,18,248,1
	.word	628
	.byte	19,247,1,0,18,244,29
	.word	628
	.byte	19,243,29,0,18,188,3
	.word	628
	.byte	19,187,3,0,18,232,3
	.word	628
	.byte	19,231,3,0,18,252,23
	.word	628
	.byte	19,251,23,0,18,228,63
	.word	628
	.byte	19,227,63,0,18,128,1
	.word	66756
	.byte	19,15,0,14
	.word	67181
	.byte	18,128,31
	.word	628
	.byte	19,255,30,0,18,64
	.word	66688
	.byte	19,7,0,14
	.word	67207
	.byte	18,192,31
	.word	628
	.byte	19,191,31,0,18,16
	.word	62702
	.byte	19,3,0,18,16
	.word	63702
	.byte	19,3,0,18,16
	.word	63764
	.byte	19,3,0,18,208,7
	.word	628
	.byte	19,207,7,0,14
	.word	66947
	.byte	18,240,23
	.word	628
	.byte	19,239,23,0,18,64
	.word	67021
	.byte	19,7,0,14
	.word	67286
	.byte	18,192,23
	.word	628
	.byte	19,191,23,0,18,232,1
	.word	628
	.byte	19,231,1,0,18,180,1
	.word	628
	.byte	19,179,1,0,18,172,1
	.word	628
	.byte	19,171,1,0,18,64
	.word	62891
	.byte	19,15,0,18,64
	.word	628
	.byte	19,63,0,18,64
	.word	62077
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,26,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	67091
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	65614
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	67102
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	66296
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	67115
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	65305
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	65367
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	65429
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	67126
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	63202
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4818
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	65677
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	63826
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2999
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	62950
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	63326
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	63389
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	63452
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4189
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	63139
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	67137
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	65491
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	64991
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	65054
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	64928
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	65179
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	65242
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	67148
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	62383
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	67159
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	64012
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	64152
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	64082
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2999
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	64221
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	64292
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	64363
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	67170
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	67191
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	67196
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	67216
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	67221
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	67232
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	67241
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	67250
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	67259
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	67270
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	67275
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	67295
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	67300
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	62320
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	62258
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	64434
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	64679
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	64742
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	64805
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	67311
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	63012
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2999
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	63888
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	62764
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	66169
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	51041
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	66622
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5158
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	63515
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	63265
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	63075
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	67322
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	65117
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	65553
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	64868
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4818
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	66232
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	62638
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	62447
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	62136
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	62197
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	64557
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	64496
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4818
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	63951
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	64618
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	51032
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	62827
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	67333
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	67344
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	67353
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	67362
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	67353
	.byte	64,4,35,192,255,3,0,14
	.word	67371
	.byte	30
	.byte	'Ifx_CPU',0,26,130,11,3
	.word	69162
	.byte	15,6,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,30
	.byte	'IfxCpu_Id',0,6,132,1,3
	.word	69184
	.byte	30
	.byte	'IfxCpu_ResourceCpu',0,6,161,1,3
	.word	1440
	.byte	30
	.byte	'Ifx_SRC_SRCR_Bits',0,11,62,3
	.word	10560
	.byte	30
	.byte	'Ifx_SRC_SRCR',0,11,75,3
	.word	10850
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,11,86,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	69329
	.byte	30
	.byte	'Ifx_SRC_AGBT',0,11,89,3
	.word	69361
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,11,92,25,12,13
	.byte	'TX',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,8,0,14
	.word	69387
	.byte	30
	.byte	'Ifx_SRC_ASCLIN',0,11,97,3
	.word	69446
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,11,100,25,4,13
	.byte	'SBSRC',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	69474
	.byte	30
	.byte	'Ifx_SRC_BCUSPB',0,11,103,3
	.word	69511
	.byte	18,64
	.word	10850
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,11,106,25,64,13
	.byte	'INT',0
	.word	69539
	.byte	64,2,35,0,0,14
	.word	69548
	.byte	30
	.byte	'Ifx_SRC_CAN',0,11,109,3
	.word	69580
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,11,112,25,16,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10850
	.byte	4,2,35,12,0,14
	.word	69605
	.byte	30
	.byte	'Ifx_SRC_CCU6',0,11,118,3
	.word	69677
	.byte	18,8
	.word	10850
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,11,121,25,8,13
	.byte	'SR',0
	.word	69703
	.byte	8,2,35,0,0,14
	.word	69712
	.byte	30
	.byte	'Ifx_SRC_CERBERUS',0,11,124,3
	.word	69748
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,11,127,25,16,13
	.byte	'MI',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	10850
	.byte	4,2,35,12,0,14
	.word	69778
	.byte	30
	.byte	'Ifx_SRC_CIF',0,11,133,1,3
	.word	69851
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,11,136,1,25,4,13
	.byte	'SBSRC',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	69877
	.byte	30
	.byte	'Ifx_SRC_CPU',0,11,139,1,3
	.word	69912
	.byte	18,192,1
	.word	10850
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,11,142,1,25,208,1,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5158
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	69938
	.byte	192,1,2,35,16,0,14
	.word	69948
	.byte	30
	.byte	'Ifx_SRC_DMA',0,11,147,1,3
	.word	70015
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,11,150,1,25,8,13
	.byte	'SRM',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	10850
	.byte	4,2,35,4,0,14
	.word	70041
	.byte	30
	.byte	'Ifx_SRC_DSADC',0,11,154,1,3
	.word	70089
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,11,157,1,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	70117
	.byte	30
	.byte	'Ifx_SRC_EMEM',0,11,160,1,3
	.word	70150
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,11,163,1,25,80,13
	.byte	'INT',0
	.word	69703
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	69703
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	69703
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	69703
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	10850
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	10850
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	51050
	.byte	40,2,35,40,0,14
	.word	70177
	.byte	30
	.byte	'Ifx_SRC_ERAY',0,11,172,1,3
	.word	70304
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,11,175,1,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	70331
	.byte	30
	.byte	'Ifx_SRC_ETH',0,11,178,1,3
	.word	70363
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,11,181,1,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	70389
	.byte	30
	.byte	'Ifx_SRC_FCE',0,11,184,1,3
	.word	70421
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,11,187,1,25,12,13
	.byte	'DONE',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	10850
	.byte	4,2,35,8,0,14
	.word	70447
	.byte	30
	.byte	'Ifx_SRC_FFT',0,11,192,1,3
	.word	70507
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,11,195,1,25,32,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10850
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	51032
	.byte	16,2,35,16,0,14
	.word	70533
	.byte	30
	.byte	'Ifx_SRC_GPSR',0,11,202,1,3
	.word	70627
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,11,205,1,25,48,13
	.byte	'CIRQ',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	10850
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	10850
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	10850
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4189
	.byte	24,2,35,24,0,14
	.word	70654
	.byte	30
	.byte	'Ifx_SRC_GPT12',0,11,214,1,3
	.word	70771
	.byte	18,12
	.word	10850
	.byte	19,2,0,18,32
	.word	10850
	.byte	19,7,0,18,32
	.word	70808
	.byte	19,0,0,18,88
	.word	628
	.byte	19,87,0,18,108
	.word	10850
	.byte	19,26,0,18,96
	.word	628
	.byte	19,95,0,18,96
	.word	70808
	.byte	19,2,0,18,160,3
	.word	628
	.byte	19,159,3,0,18,64
	.word	70808
	.byte	19,1,0,18,192,3
	.word	628
	.byte	19,191,3,0,18,16
	.word	10850
	.byte	19,3,0,18,64
	.word	70893
	.byte	19,3,0,18,192,2
	.word	628
	.byte	19,191,2,0,18,52
	.word	628
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,11,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	70799
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2999
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	10850
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	10850
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	69703
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4818
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	70817
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	70826
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	70835
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	70844
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	10850
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5158
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	70853
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	70862
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	70853
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	70862
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	70873
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	70882
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	70902
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	70911
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	70799
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	70922
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	70799
	.byte	12,3,35,192,18,0,14
	.word	70931
	.byte	30
	.byte	'Ifx_SRC_GTM',0,11,243,1,3
	.word	71391
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,11,246,1,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	71417
	.byte	30
	.byte	'Ifx_SRC_HSCT',0,11,249,1,3
	.word	71450
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,11,252,1,25,16,13
	.byte	'COK',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	10850
	.byte	4,2,35,12,0,14
	.word	71477
	.byte	30
	.byte	'Ifx_SRC_HSSL',0,11,130,2,3
	.word	71550
	.byte	18,56
	.word	628
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,11,133,2,25,80,13
	.byte	'BREQ',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	10850
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	10850
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	71577
	.byte	56,2,35,24,0,14
	.word	71586
	.byte	30
	.byte	'Ifx_SRC_I2C',0,11,142,2,3
	.word	71709
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,11,145,2,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	71735
	.byte	30
	.byte	'Ifx_SRC_LMU',0,11,148,2,3
	.word	71767
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,11,151,2,25,20,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10850
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	10850
	.byte	4,2,35,16,0,14
	.word	71793
	.byte	30
	.byte	'Ifx_SRC_MSC',0,11,158,2,3
	.word	71878
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,11,161,2,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	71904
	.byte	30
	.byte	'Ifx_SRC_PMU',0,11,164,2,3
	.word	71936
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,11,167,2,25,32,13
	.byte	'SR',0
	.word	70808
	.byte	32,2,35,0,0,14
	.word	71962
	.byte	30
	.byte	'Ifx_SRC_PSI5',0,11,170,2,3
	.word	71995
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,11,173,2,25,32,13
	.byte	'SR',0
	.word	70808
	.byte	32,2,35,0,0,14
	.word	72022
	.byte	30
	.byte	'Ifx_SRC_PSI5S',0,11,176,2,3
	.word	72056
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,11,179,2,25,24,13
	.byte	'TX',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	10850
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	10850
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	10850
	.byte	4,2,35,20,0,14
	.word	72084
	.byte	30
	.byte	'Ifx_SRC_QSPI',0,11,187,2,3
	.word	72177
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,11,190,2,25,4,13
	.byte	'SR',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	72204
	.byte	30
	.byte	'Ifx_SRC_SCR',0,11,193,2,3
	.word	72236
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,11,196,2,25,20,13
	.byte	'DTS',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	70893
	.byte	16,2,35,4,0,14
	.word	72262
	.byte	30
	.byte	'Ifx_SRC_SCU',0,11,200,2,3
	.word	72308
	.byte	18,24
	.word	10850
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,11,203,2,25,24,13
	.byte	'SR',0
	.word	72334
	.byte	24,2,35,0,0,14
	.word	72343
	.byte	30
	.byte	'Ifx_SRC_SENT',0,11,206,2,3
	.word	72376
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,11,209,2,25,12,13
	.byte	'SR',0
	.word	70799
	.byte	12,2,35,0,0,14
	.word	72403
	.byte	30
	.byte	'Ifx_SRC_SMU',0,11,212,2,3
	.word	72435
	.byte	10
	.byte	'_Ifx_SRC_STM',0,11,215,2,25,8,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,0,14
	.word	72461
	.byte	30
	.byte	'Ifx_SRC_STM',0,11,219,2,3
	.word	72507
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,11,222,2,25,16,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10850
	.byte	4,2,35,12,0,14
	.word	72533
	.byte	30
	.byte	'Ifx_SRC_VADCCG',0,11,228,2,3
	.word	72608
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,11,231,2,25,16,13
	.byte	'SR0',0
	.word	10850
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	10850
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	10850
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	10850
	.byte	4,2,35,12,0,14
	.word	72637
	.byte	30
	.byte	'Ifx_SRC_VADCG',0,11,237,2,3
	.word	72711
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,11,240,2,25,4,13
	.byte	'SRC',0
	.word	10850
	.byte	4,2,35,0,0,14
	.word	72739
	.byte	30
	.byte	'Ifx_SRC_XBAR',0,11,243,2,3
	.word	72773
	.byte	18,4
	.word	69329
	.byte	19,0,0,14
	.word	72800
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,11,128,3,25,4,13
	.byte	'AGBT',0
	.word	72809
	.byte	4,2,35,0,0,14
	.word	72814
	.byte	30
	.byte	'Ifx_SRC_GAGBT',0,11,131,3,3
	.word	72850
	.byte	18,48
	.word	69387
	.byte	19,3,0,14
	.word	72878
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,11,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	72887
	.byte	48,2,35,0,0,14
	.word	72892
	.byte	30
	.byte	'Ifx_SRC_GASCLIN',0,11,137,3,3
	.word	72932
	.byte	14
	.word	69474
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,11,140,3,25,4,13
	.byte	'SPB',0
	.word	72962
	.byte	4,2,35,0,0,14
	.word	72967
	.byte	30
	.byte	'Ifx_SRC_GBCU',0,11,143,3,3
	.word	73001
	.byte	18,64
	.word	69548
	.byte	19,0,0,14
	.word	73028
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,11,146,3,25,64,13
	.byte	'CAN',0
	.word	73037
	.byte	64,2,35,0,0,14
	.word	73042
	.byte	30
	.byte	'Ifx_SRC_GCAN',0,11,149,3,3
	.word	73076
	.byte	18,32
	.word	69605
	.byte	19,1,0,14
	.word	73103
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,11,152,3,25,32,13
	.byte	'CCU6',0
	.word	73112
	.byte	32,2,35,0,0,14
	.word	73117
	.byte	30
	.byte	'Ifx_SRC_GCCU6',0,11,155,3,3
	.word	73153
	.byte	14
	.word	69712
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,11,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	73181
	.byte	8,2,35,0,0,14
	.word	73186
	.byte	30
	.byte	'Ifx_SRC_GCERBERUS',0,11,161,3,3
	.word	73230
	.byte	18,16
	.word	69778
	.byte	19,0,0,14
	.word	73262
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,11,164,3,25,16,13
	.byte	'CIF',0
	.word	73271
	.byte	16,2,35,0,0,14
	.word	73276
	.byte	30
	.byte	'Ifx_SRC_GCIF',0,11,167,3,3
	.word	73310
	.byte	18,8
	.word	69877
	.byte	19,1,0,14
	.word	73337
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,11,170,3,25,8,13
	.byte	'CPU',0
	.word	73346
	.byte	8,2,35,0,0,14
	.word	73351
	.byte	30
	.byte	'Ifx_SRC_GCPU',0,11,173,3,3
	.word	73385
	.byte	18,208,1
	.word	69948
	.byte	19,0,0,14
	.word	73412
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,11,176,3,25,208,1,13
	.byte	'DMA',0
	.word	73422
	.byte	208,1,2,35,0,0,14
	.word	73427
	.byte	30
	.byte	'Ifx_SRC_GDMA',0,11,179,3,3
	.word	73463
	.byte	14
	.word	70041
	.byte	14
	.word	70041
	.byte	14
	.word	70041
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,11,182,3,25,32,13
	.byte	'DSADC0',0
	.word	73490
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4818
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	73495
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	73500
	.byte	8,2,35,24,0,14
	.word	73505
	.byte	30
	.byte	'Ifx_SRC_GDSADC',0,11,188,3,3
	.word	73596
	.byte	18,4
	.word	70117
	.byte	19,0,0,14
	.word	73625
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,11,191,3,25,4,13
	.byte	'EMEM',0
	.word	73634
	.byte	4,2,35,0,0,14
	.word	73639
	.byte	30
	.byte	'Ifx_SRC_GEMEM',0,11,194,3,3
	.word	73675
	.byte	18,80
	.word	70177
	.byte	19,0,0,14
	.word	73703
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,11,197,3,25,80,13
	.byte	'ERAY',0
	.word	73712
	.byte	80,2,35,0,0,14
	.word	73717
	.byte	30
	.byte	'Ifx_SRC_GERAY',0,11,200,3,3
	.word	73753
	.byte	18,4
	.word	70331
	.byte	19,0,0,14
	.word	73781
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,11,203,3,25,4,13
	.byte	'ETH',0
	.word	73790
	.byte	4,2,35,0,0,14
	.word	73795
	.byte	30
	.byte	'Ifx_SRC_GETH',0,11,206,3,3
	.word	73829
	.byte	18,4
	.word	70389
	.byte	19,0,0,14
	.word	73856
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,11,209,3,25,4,13
	.byte	'FCE',0
	.word	73865
	.byte	4,2,35,0,0,14
	.word	73870
	.byte	30
	.byte	'Ifx_SRC_GFCE',0,11,212,3,3
	.word	73904
	.byte	18,12
	.word	70447
	.byte	19,0,0,14
	.word	73931
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,11,215,3,25,12,13
	.byte	'FFT',0
	.word	73940
	.byte	12,2,35,0,0,14
	.word	73945
	.byte	30
	.byte	'Ifx_SRC_GFFT',0,11,218,3,3
	.word	73979
	.byte	18,64
	.word	70533
	.byte	19,1,0,14
	.word	74006
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,11,221,3,25,64,13
	.byte	'GPSR',0
	.word	74015
	.byte	64,2,35,0,0,14
	.word	74020
	.byte	30
	.byte	'Ifx_SRC_GGPSR',0,11,224,3,3
	.word	74056
	.byte	18,48
	.word	70654
	.byte	19,0,0,14
	.word	74084
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,11,227,3,25,48,13
	.byte	'GPT12',0
	.word	74093
	.byte	48,2,35,0,0,14
	.word	74098
	.byte	30
	.byte	'Ifx_SRC_GGPT12',0,11,230,3,3
	.word	74136
	.byte	18,204,18
	.word	70931
	.byte	19,0,0,14
	.word	74165
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,11,233,3,25,204,18,13
	.byte	'GTM',0
	.word	74175
	.byte	204,18,2,35,0,0,14
	.word	74180
	.byte	30
	.byte	'Ifx_SRC_GGTM',0,11,236,3,3
	.word	74216
	.byte	18,4
	.word	71417
	.byte	19,0,0,14
	.word	74243
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,11,239,3,25,4,13
	.byte	'HSCT',0
	.word	74252
	.byte	4,2,35,0,0,14
	.word	74257
	.byte	30
	.byte	'Ifx_SRC_GHSCT',0,11,242,3,3
	.word	74293
	.byte	18,64
	.word	71477
	.byte	19,3,0,14
	.word	74321
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,11,245,3,25,68,13
	.byte	'HSSL',0
	.word	74330
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	10850
	.byte	4,2,35,64,0,14
	.word	74335
	.byte	30
	.byte	'Ifx_SRC_GHSSL',0,11,249,3,3
	.word	74384
	.byte	18,80
	.word	71586
	.byte	19,0,0,14
	.word	74412
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,11,252,3,25,80,13
	.byte	'I2C',0
	.word	74421
	.byte	80,2,35,0,0,14
	.word	74426
	.byte	30
	.byte	'Ifx_SRC_GI2C',0,11,255,3,3
	.word	74460
	.byte	18,4
	.word	71735
	.byte	19,0,0,14
	.word	74487
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,11,130,4,25,4,13
	.byte	'LMU',0
	.word	74496
	.byte	4,2,35,0,0,14
	.word	74501
	.byte	30
	.byte	'Ifx_SRC_GLMU',0,11,133,4,3
	.word	74535
	.byte	18,40
	.word	71793
	.byte	19,1,0,14
	.word	74562
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,11,136,4,25,40,13
	.byte	'MSC',0
	.word	74571
	.byte	40,2,35,0,0,14
	.word	74576
	.byte	30
	.byte	'Ifx_SRC_GMSC',0,11,139,4,3
	.word	74610
	.byte	18,8
	.word	71904
	.byte	19,1,0,14
	.word	74637
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,11,142,4,25,8,13
	.byte	'PMU',0
	.word	74646
	.byte	8,2,35,0,0,14
	.word	74651
	.byte	30
	.byte	'Ifx_SRC_GPMU',0,11,145,4,3
	.word	74685
	.byte	18,32
	.word	71962
	.byte	19,0,0,14
	.word	74712
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,11,148,4,25,32,13
	.byte	'PSI5',0
	.word	74721
	.byte	32,2,35,0,0,14
	.word	74726
	.byte	30
	.byte	'Ifx_SRC_GPSI5',0,11,151,4,3
	.word	74762
	.byte	18,32
	.word	72022
	.byte	19,0,0,14
	.word	74790
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,11,154,4,25,32,13
	.byte	'PSI5S',0
	.word	74799
	.byte	32,2,35,0,0,14
	.word	74804
	.byte	30
	.byte	'Ifx_SRC_GPSI5S',0,11,157,4,3
	.word	74842
	.byte	18,96
	.word	72084
	.byte	19,3,0,14
	.word	74871
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,11,160,4,25,96,13
	.byte	'QSPI',0
	.word	74880
	.byte	96,2,35,0,0,14
	.word	74885
	.byte	30
	.byte	'Ifx_SRC_GQSPI',0,11,163,4,3
	.word	74921
	.byte	18,4
	.word	72204
	.byte	19,0,0,14
	.word	74949
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,11,166,4,25,4,13
	.byte	'SCR',0
	.word	74958
	.byte	4,2,35,0,0,14
	.word	74963
	.byte	30
	.byte	'Ifx_SRC_GSCR',0,11,169,4,3
	.word	74997
	.byte	14
	.word	72262
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,11,172,4,25,20,13
	.byte	'SCU',0
	.word	75024
	.byte	20,2,35,0,0,14
	.word	75029
	.byte	30
	.byte	'Ifx_SRC_GSCU',0,11,175,4,3
	.word	75063
	.byte	18,24
	.word	72343
	.byte	19,0,0,14
	.word	75090
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,11,178,4,25,24,13
	.byte	'SENT',0
	.word	75099
	.byte	24,2,35,0,0,14
	.word	75104
	.byte	30
	.byte	'Ifx_SRC_GSENT',0,11,181,4,3
	.word	75140
	.byte	18,12
	.word	72403
	.byte	19,0,0,14
	.word	75168
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,11,184,4,25,12,13
	.byte	'SMU',0
	.word	75177
	.byte	12,2,35,0,0,14
	.word	75182
	.byte	30
	.byte	'Ifx_SRC_GSMU',0,11,187,4,3
	.word	75216
	.byte	18,16
	.word	72461
	.byte	19,1,0,14
	.word	75243
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,11,190,4,25,16,13
	.byte	'STM',0
	.word	75252
	.byte	16,2,35,0,0,14
	.word	75257
	.byte	30
	.byte	'Ifx_SRC_GSTM',0,11,193,4,3
	.word	75291
	.byte	18,64
	.word	72637
	.byte	19,3,0,14
	.word	75318
	.byte	18,224,1
	.word	628
	.byte	19,223,1,0,18,32
	.word	72533
	.byte	19,1,0,14
	.word	75343
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,11,196,4,25,192,2,13
	.byte	'G',0
	.word	75327
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	75332
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	75352
	.byte	32,3,35,160,2,0,14
	.word	75357
	.byte	30
	.byte	'Ifx_SRC_GVADC',0,11,201,4,3
	.word	75426
	.byte	14
	.word	72739
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,11,204,4,25,4,13
	.byte	'XBAR',0
	.word	75454
	.byte	4,2,35,0,0,14
	.word	75459
	.byte	30
	.byte	'Ifx_SRC_GXBAR',0,11,207,4,3
	.word	75495
	.byte	30
	.byte	'Ifx_STM_ACCEN0_Bits',0,16,79,3
	.word	20780
	.byte	30
	.byte	'Ifx_STM_ACCEN1_Bits',0,16,85,3
	.word	20691
	.byte	30
	.byte	'Ifx_STM_CAP_Bits',0,16,91,3
	.word	19221
	.byte	30
	.byte	'Ifx_STM_CAPSV_Bits',0,16,97,3
	.word	20098
	.byte	30
	.byte	'Ifx_STM_CLC_Bits',0,16,107,3
	.word	18344
	.byte	30
	.byte	'Ifx_STM_CMCON_Bits',0,16,120,3
	.word	19399
	.byte	30
	.byte	'Ifx_STM_CMP_Bits',0,16,126,3
	.word	19308
	.byte	30
	.byte	'Ifx_STM_ICR_Bits',0,16,139,1,3
	.word	19630
	.byte	30
	.byte	'Ifx_STM_ID_Bits',0,16,147,1,3
	.word	18500
	.byte	30
	.byte	'Ifx_STM_ISCR_Bits',0,16,157,1,3
	.word	19847
	.byte	30
	.byte	'Ifx_STM_KRST0_Bits',0,16,165,1,3
	.word	20568
	.byte	30
	.byte	'Ifx_STM_KRST1_Bits',0,16,172,1,3
	.word	20464
	.byte	30
	.byte	'Ifx_STM_KRSTCLR_Bits',0,16,179,1,3
	.word	20358
	.byte	30
	.byte	'Ifx_STM_OCS_Bits',0,16,189,1,3
	.word	20198
	.byte	30
	.byte	'Ifx_STM_TIM0_Bits',0,16,195,1,3
	.word	18622
	.byte	30
	.byte	'Ifx_STM_TIM0SV_Bits',0,16,201,1,3
	.word	20011
	.byte	30
	.byte	'Ifx_STM_TIM1_Bits',0,16,207,1,3
	.word	18707
	.byte	30
	.byte	'Ifx_STM_TIM2_Bits',0,16,213,1,3
	.word	18792
	.byte	30
	.byte	'Ifx_STM_TIM3_Bits',0,16,219,1,3
	.word	18877
	.byte	30
	.byte	'Ifx_STM_TIM4_Bits',0,16,225,1,3
	.word	18963
	.byte	30
	.byte	'Ifx_STM_TIM5_Bits',0,16,231,1,3
	.word	19049
	.byte	30
	.byte	'Ifx_STM_TIM6_Bits',0,16,237,1,3
	.word	19135
	.byte	30
	.byte	'Ifx_STM_ACCEN0',0,16,250,1,3
	.word	21309
	.byte	30
	.byte	'Ifx_STM_ACCEN1',0,16,130,2,3
	.word	20740
	.byte	30
	.byte	'Ifx_STM_CAP',0,16,138,2,3
	.word	19268
	.byte	30
	.byte	'Ifx_STM_CAPSV',0,16,146,2,3
	.word	20147
	.byte	30
	.byte	'Ifx_STM_CLC',0,16,154,2,3
	.word	18460
	.byte	30
	.byte	'Ifx_STM_CMCON',0,16,162,2,3
	.word	19590
	.byte	30
	.byte	'Ifx_STM_CMP',0,16,170,2,3
	.word	19350
	.byte	30
	.byte	'Ifx_STM_ICR',0,16,178,2,3
	.word	19807
	.byte	30
	.byte	'Ifx_STM_ID',0,16,186,2,3
	.word	18582
	.byte	30
	.byte	'Ifx_STM_ISCR',0,16,194,2,3
	.word	19971
	.byte	30
	.byte	'Ifx_STM_KRST0',0,16,202,2,3
	.word	20651
	.byte	30
	.byte	'Ifx_STM_KRST1',0,16,210,2,3
	.word	20528
	.byte	30
	.byte	'Ifx_STM_KRSTCLR',0,16,218,2,3
	.word	20424
	.byte	30
	.byte	'Ifx_STM_OCS',0,16,226,2,3
	.word	20318
	.byte	30
	.byte	'Ifx_STM_TIM0',0,16,234,2,3
	.word	18667
	.byte	30
	.byte	'Ifx_STM_TIM0SV',0,16,242,2,3
	.word	20058
	.byte	30
	.byte	'Ifx_STM_TIM1',0,16,250,2,3
	.word	18752
	.byte	30
	.byte	'Ifx_STM_TIM2',0,16,130,3,3
	.word	18837
	.byte	30
	.byte	'Ifx_STM_TIM3',0,16,138,3,3
	.word	18923
	.byte	30
	.byte	'Ifx_STM_TIM4',0,16,146,3,3
	.word	19009
	.byte	30
	.byte	'Ifx_STM_TIM5',0,16,154,3,3
	.word	19095
	.byte	30
	.byte	'Ifx_STM_TIM6',0,16,162,3,3
	.word	19181
	.byte	14
	.word	21349
	.byte	30
	.byte	'Ifx_STM',0,16,201,3,3
	.word	76606
	.byte	15,5,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,30
	.byte	'IfxCpu_CounterMode',0,5,148,1,3
	.word	76628
	.byte	20,5,160,1,9,6,13
	.byte	'counter',0
	.word	1762
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	628
	.byte	1,2,35,4,0,30
	.byte	'IfxCpu_Counter',0,5,164,1,3
	.word	76717
	.byte	20,5,172,1,9,32,13
	.byte	'instruction',0
	.word	76717
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	76717
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	76717
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	76717
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	76717
	.byte	6,2,35,24,0,30
	.byte	'IfxCpu_Perf',0,5,179,1,3
	.word	76783
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,27,45,16,4,11
	.byte	'EN0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,27,79,3
	.word	76901
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,27,82,16,4,11
	.byte	'reserved_0',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,27,85,3
	.word	77462
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,27,88,16,4,11
	.byte	'SEL',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	453
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,27,95,3
	.word	77543
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,27,98,16,4,11
	.byte	'VLD0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	453
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,27,111,3
	.word	77696
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,27,114,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	453
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	628
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,27,121,3
	.word	77944
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,27,124,16,4,11
	.byte	'STATUS',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	453
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0_Bits',0,27,128,1,3
	.word	78090
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,27,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM1_Bits',0,27,136,1,3
	.word	78188
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,27,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM2_Bits',0,27,144,1,3
	.word	78304
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,27,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	453
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	645
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRD_Bits',0,27,153,1,3
	.word	78420
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,27,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	453
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	645
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRP_Bits',0,27,162,1,3
	.word	78560
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,27,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	453
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	645
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCW_Bits',0,27,171,1,3
	.word	78700
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,27,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	628
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	628
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	645
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	628
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	628
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	628
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FCON_Bits',0,27,193,1,3
	.word	78839
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,27,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	628
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	628
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	628
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FPRO_Bits',0,27,218,1,3
	.word	79201
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,27,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	645
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	628
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	628
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	628
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FSR_Bits',0,27,254,1,3
	.word	79642
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,27,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	628
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	628
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_ID_Bits',0,27,134,2,3
	.word	80248
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,27,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	645
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARD_Bits',0,27,147,2,3
	.word	80359
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,27,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	645
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARP_Bits',0,27,159,2,3
	.word	80573
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,27,162,2,16,4,11
	.byte	'L',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	628
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	628
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	645
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	628
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCOND_Bits',0,27,179,2,3
	.word	80760
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,27,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	628
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	453
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,27,188,2,3
	.word	81084
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,27,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	645
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,27,199,2,3
	.word	81227
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,27,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	645
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	628
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	628
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	628
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	645
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,27,219,2,3
	.word	81416
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,27,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	628
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	628
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,27,254,2,3
	.word	81779
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,27,129,3,16,4,11
	.byte	'S0L',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	628
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONP_Bits',0,27,160,3,3
	.word	82374
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,27,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	628
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	628
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	628
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	628
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	628
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	628
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	628
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	628
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	628
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	628
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	628
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	628
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	628
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	628
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	628
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	628
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	628
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	628
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	628
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	628
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	628
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,27,194,3,3
	.word	82898
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,27,197,3,16,4,11
	.byte	'TAG',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,27,201,3,3
	.word	83480
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,27,204,3,16,4,11
	.byte	'TAG',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,27,208,3,3
	.word	83582
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,27,211,3,16,4,11
	.byte	'TAG',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	453
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,27,215,3,3
	.word	83684
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,27,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	453
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD_Bits',0,27,222,3,3
	.word	83786
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,27,225,3,16,4,11
	.byte	'STRT',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	628
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	628
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	628
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	645
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_RRCT_Bits',0,27,236,3,3
	.word	83880
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,27,239,3,16,4,11
	.byte	'DATA',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0_Bits',0,27,242,3,3
	.word	84090
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,27,245,3,16,4,11
	.byte	'DATA',0,4
	.word	453
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1_Bits',0,27,248,3,3
	.word	84163
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,27,251,3,16,4,11
	.byte	'SEL',0,1
	.word	628
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	628
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	628
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	453
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,27,130,4,3
	.word	84236
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,27,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	453
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,27,137,4,3
	.word	84391
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,27,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	628
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	453
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	628
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	628
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	628
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,27,147,4,3
	.word	84496
	.byte	12,27,155,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76901
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN0',0,27,160,4,3
	.word	84644
	.byte	12,27,163,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77462
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1',0,27,168,4,3
	.word	84710
	.byte	12,27,171,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77543
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG',0,27,176,4,3
	.word	84776
	.byte	12,27,179,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77696
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT',0,27,184,4,3
	.word	84844
	.byte	12,27,187,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77944
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_TOP',0,27,192,4,3
	.word	84913
	.byte	12,27,195,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78090
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0',0,27,200,4,3
	.word	84981
	.byte	12,27,203,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78188
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM1',0,27,208,4,3
	.word	85046
	.byte	12,27,211,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78304
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM2',0,27,216,4,3
	.word	85111
	.byte	12,27,219,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78420
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRD',0,27,224,4,3
	.word	85176
	.byte	12,27,227,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78560
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRP',0,27,232,4,3
	.word	85241
	.byte	12,27,235,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78700
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCW',0,27,240,4,3
	.word	85306
	.byte	12,27,243,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78839
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FCON',0,27,248,4,3
	.word	85370
	.byte	12,27,251,4,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79201
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FPRO',0,27,128,5,3
	.word	85434
	.byte	12,27,131,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79642
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FSR',0,27,136,5,3
	.word	85498
	.byte	12,27,139,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80248
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ID',0,27,144,5,3
	.word	85561
	.byte	12,27,147,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80359
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARD',0,27,152,5,3
	.word	85623
	.byte	12,27,155,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80573
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARP',0,27,160,5,3
	.word	85687
	.byte	12,27,163,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80760
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCOND',0,27,168,5,3
	.word	85751
	.byte	12,27,171,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81084
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG',0,27,176,5,3
	.word	85818
	.byte	12,27,179,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81227
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSM',0,27,184,5,3
	.word	85887
	.byte	12,27,187,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81416
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,27,192,5,3
	.word	85956
	.byte	12,27,195,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81779
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONOTP',0,27,200,5,3
	.word	86029
	.byte	12,27,203,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82374
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONP',0,27,208,5,3
	.word	86098
	.byte	12,27,211,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82898
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONWOP',0,27,216,5,3
	.word	86165
	.byte	12,27,219,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83480
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0',0,27,224,5,3
	.word	86234
	.byte	12,27,227,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83582
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1',0,27,232,5,3
	.word	86302
	.byte	12,27,235,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83684
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2',0,27,240,5,3
	.word	86370
	.byte	12,27,243,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83786
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD',0,27,248,5,3
	.word	86438
	.byte	12,27,251,5,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83880
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRCT',0,27,128,6,3
	.word	86502
	.byte	12,27,131,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84090
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0',0,27,136,6,3
	.word	86566
	.byte	12,27,139,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84163
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1',0,27,144,6,3
	.word	86630
	.byte	12,27,147,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84236
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG',0,27,152,6,3
	.word	86694
	.byte	12,27,155,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84391
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT',0,27,160,6,3
	.word	86762
	.byte	12,27,163,6,9,4,13
	.byte	'U',0
	.word	453
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84496
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_TOP',0,27,168,6,3
	.word	86831
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,27,179,6,25,12,13
	.byte	'CFG',0
	.word	84776
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	84844
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	84913
	.byte	4,2,35,8,0,14
	.word	86899
	.byte	30
	.byte	'Ifx_FLASH_CBAB',0,27,184,6,3
	.word	86962
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,27,187,6,25,12,13
	.byte	'CFG0',0
	.word	86234
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	86302
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	86370
	.byte	4,2,35,8,0,14
	.word	86991
	.byte	30
	.byte	'Ifx_FLASH_RDB',0,27,192,6,3
	.word	87055
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,27,195,6,25,12,13
	.byte	'CFG',0
	.word	86694
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	86762
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	86831
	.byte	4,2,35,8,0,14
	.word	87083
	.byte	30
	.byte	'Ifx_FLASH_UBAB',0,27,200,6,3
	.word	87146
	.byte	30
	.byte	'Ifx_P_ACCEN0_Bits',0,8,79,3
	.word	8571
	.byte	30
	.byte	'Ifx_P_ACCEN1_Bits',0,8,85,3
	.word	8484
	.byte	30
	.byte	'Ifx_P_ESR_Bits',0,8,107,3
	.word	4827
	.byte	30
	.byte	'Ifx_P_ID_Bits',0,8,115,3
	.word	2880
	.byte	30
	.byte	'Ifx_P_IN_Bits',0,8,137,1,3
	.word	3875
	.byte	30
	.byte	'Ifx_P_IOCR0_Bits',0,8,150,1,3
	.word	3008
	.byte	30
	.byte	'Ifx_P_IOCR12_Bits',0,8,163,1,3
	.word	3655
	.byte	30
	.byte	'Ifx_P_IOCR4_Bits',0,8,176,1,3
	.word	3223
	.byte	30
	.byte	'Ifx_P_IOCR8_Bits',0,8,189,1,3
	.word	3438
	.byte	30
	.byte	'Ifx_P_LPCR0_Bits',0,8,197,1,3
	.word	7843
	.byte	30
	.byte	'Ifx_P_LPCR1_Bits',0,8,205,1,3
	.word	7967
	.byte	30
	.byte	'Ifx_P_LPCR1_P21_Bits',0,8,215,1,3
	.word	8051
	.byte	30
	.byte	'Ifx_P_LPCR2_Bits',0,8,229,1,3
	.word	8231
	.byte	30
	.byte	'Ifx_P_OMCR0_Bits',0,8,240,1,3
	.word	6482
	.byte	30
	.byte	'Ifx_P_OMCR12_Bits',0,8,250,1,3
	.word	7006
	.byte	30
	.byte	'Ifx_P_OMCR4_Bits',0,8,133,2,3
	.word	6656
	.byte	30
	.byte	'Ifx_P_OMCR8_Bits',0,8,144,2,3
	.word	6830
	.byte	30
	.byte	'Ifx_P_OMCR_Bits',0,8,166,2,3
	.word	7495
	.byte	30
	.byte	'Ifx_P_OMR_Bits',0,8,203,2,3
	.word	2309
	.byte	30
	.byte	'Ifx_P_OMSR0_Bits',0,8,213,2,3
	.word	5819
	.byte	30
	.byte	'Ifx_P_OMSR12_Bits',0,8,224,2,3
	.word	6307
	.byte	30
	.byte	'Ifx_P_OMSR4_Bits',0,8,235,2,3
	.word	5966
	.byte	30
	.byte	'Ifx_P_OMSR8_Bits',0,8,246,2,3
	.word	6135
	.byte	30
	.byte	'Ifx_P_OMSR_Bits',0,8,140,3,3
	.word	7162
	.byte	30
	.byte	'Ifx_P_OUT_Bits',0,8,162,3,3
	.word	1993
	.byte	30
	.byte	'Ifx_P_PCSR_Bits',0,8,180,3,3
	.word	5533
	.byte	30
	.byte	'Ifx_P_PDISC_Bits',0,8,202,3,3
	.word	5167
	.byte	30
	.byte	'Ifx_P_PDR0_Bits',0,8,223,3,3
	.word	4198
	.byte	30
	.byte	'Ifx_P_PDR1_Bits',0,8,244,3,3
	.word	4502
	.byte	30
	.byte	'Ifx_P_ACCEN0',0,8,129,4,3
	.word	9098
	.byte	30
	.byte	'Ifx_P_ACCEN1',0,8,137,4,3
	.word	8531
	.byte	30
	.byte	'Ifx_P_ESR',0,8,145,4,3
	.word	5118
	.byte	30
	.byte	'Ifx_P_ID',0,8,153,4,3
	.word	2959
	.byte	30
	.byte	'Ifx_P_IN',0,8,161,4,3
	.word	4149
	.byte	30
	.byte	'Ifx_P_IOCR0',0,8,169,4,3
	.word	3183
	.byte	30
	.byte	'Ifx_P_IOCR12',0,8,177,4,3
	.word	3835
	.byte	30
	.byte	'Ifx_P_IOCR4',0,8,185,4,3
	.word	3398
	.byte	30
	.byte	'Ifx_P_IOCR8',0,8,193,4,3
	.word	3615
	.byte	30
	.byte	'Ifx_P_LPCR0',0,8,201,4,3
	.word	7927
	.byte	30
	.byte	'Ifx_P_LPCR1',0,8,210,4,3
	.word	8176
	.byte	30
	.byte	'Ifx_P_LPCR2',0,8,218,4,3
	.word	8435
	.byte	30
	.byte	'Ifx_P_OMCR',0,8,226,4,3
	.word	7803
	.byte	30
	.byte	'Ifx_P_OMCR0',0,8,234,4,3
	.word	6616
	.byte	30
	.byte	'Ifx_P_OMCR12',0,8,242,4,3
	.word	7122
	.byte	30
	.byte	'Ifx_P_OMCR4',0,8,250,4,3
	.word	6790
	.byte	30
	.byte	'Ifx_P_OMCR8',0,8,130,5,3
	.word	6966
	.byte	30
	.byte	'Ifx_P_OMR',0,8,138,5,3
	.word	2840
	.byte	30
	.byte	'Ifx_P_OMSR',0,8,146,5,3
	.word	7455
	.byte	30
	.byte	'Ifx_P_OMSR0',0,8,154,5,3
	.word	5926
	.byte	30
	.byte	'Ifx_P_OMSR12',0,8,162,5,3
	.word	6442
	.byte	30
	.byte	'Ifx_P_OMSR4',0,8,170,5,3
	.word	6095
	.byte	30
	.byte	'Ifx_P_OMSR8',0,8,178,5,3
	.word	6267
	.byte	30
	.byte	'Ifx_P_OUT',0,8,186,5,3
	.word	2269
	.byte	30
	.byte	'Ifx_P_PCSR',0,8,194,5,3
	.word	5779
	.byte	30
	.byte	'Ifx_P_PDISC',0,8,202,5,3
	.word	5493
	.byte	30
	.byte	'Ifx_P_PDR0',0,8,210,5,3
	.word	4462
	.byte	30
	.byte	'Ifx_P_PDR1',0,8,218,5,3
	.word	4778
	.byte	14
	.word	9138
	.byte	30
	.byte	'Ifx_P',0,8,139,6,3
	.word	88493
	.byte	30
	.byte	'IfxPort_InputMode',0,7,89,3
	.word	9751
	.byte	30
	.byte	'IfxPort_OutputIdx',0,7,130,1,3
	.word	10026
	.byte	30
	.byte	'IfxPort_OutputMode',0,7,138,1,3
	.word	9956
	.byte	15,7,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,30
	.byte	'IfxPort_PadDriver',0,7,158,1,3
	.word	88594
	.byte	30
	.byte	'IfxPort_State',0,7,178,1,3
	.word	10339
	.byte	20,7,190,1,9,8,13
	.byte	'port',0
	.word	9746
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	628
	.byte	1,2,35,4,0,30
	.byte	'IfxPort_Pin',0,7,194,1,3
	.word	89059
	.byte	30
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,9,148,1,16
	.word	192
	.byte	20,9,212,5,9,8,13
	.byte	'value',0
	.word	1762
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1762
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_CcuconRegConfig',0,9,216,5,3
	.word	89159
	.byte	20,9,221,5,9,8,13
	.byte	'pDivider',0
	.word	628
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	628
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	628
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	249
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_InitialStepConfig',0,9,227,5,3
	.word	89230
	.byte	20,9,231,5,9,12,13
	.byte	'k2Step',0
	.word	628
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	249
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	89119
	.byte	4,2,35,8,0,30
	.byte	'IfxScuCcu_PllStepsConfig',0,9,236,5,3
	.word	89347
	.byte	3
	.word	189
	.byte	20,9,244,5,9,48,13
	.byte	'ccucon0',0
	.word	89159
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	89159
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	89159
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	89159
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	89159
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	89159
	.byte	8,2,35,40,0,30
	.byte	'IfxScuCcu_ClockDistributionConfig',0,9,252,5,3
	.word	89449
	.byte	20,9,128,6,9,8,13
	.byte	'value',0
	.word	1762
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	1762
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,9,132,6,3
	.word	89601
	.byte	3
	.word	89347
	.byte	20,9,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	628
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	89677
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	89230
	.byte	8,2,35,8,0,30
	.byte	'IfxScuCcu_SysPllConfig',0,9,142,6,3
	.word	89682
	.byte	15,28,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,30
	.byte	'IfxSrc_Tos',0,28,74,3
	.word	89799
	.byte	20,29,59,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24682
	.byte	1,2,35,12,0,26
	.word	89877
	.byte	30
	.byte	'IfxAsclin_Cts_In',0,29,64,3
	.word	89928
	.byte	20,29,67,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	24682
	.byte	1,2,35,12,0,26
	.word	89958
	.byte	30
	.byte	'IfxAsclin_Rx_In',0,29,72,3
	.word	90009
	.byte	20,29,75,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10026
	.byte	1,2,35,12,0,26
	.word	90038
	.byte	30
	.byte	'IfxAsclin_Rts_Out',0,29,80,3
	.word	90089
	.byte	20,29,83,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10026
	.byte	1,2,35,12,0,26
	.word	90120
	.byte	30
	.byte	'IfxAsclin_Sclk_Out',0,29,88,3
	.word	90171
	.byte	20,29,91,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10026
	.byte	1,2,35,12,0,26
	.word	90203
	.byte	30
	.byte	'IfxAsclin_Slso_Out',0,29,96,3
	.word	90254
	.byte	20,29,99,15,16,13
	.byte	'module',0
	.word	17474
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	89059
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10026
	.byte	1,2,35,12,0,26
	.word	90286
	.byte	30
	.byte	'IfxAsclin_Tx_Out',0,29,104,3
	.word	90337
	.byte	15,12,82,9,1,16
	.byte	'IfxAsclin_Checksum_classic',0,0,16
	.byte	'IfxAsclin_Checksum_enhanced',0,1,0,30
	.byte	'IfxAsclin_Checksum',0,12,86,3
	.word	90367
	.byte	15,12,91,9,1,16
	.byte	'IfxAsclin_ChecksumInjection_notWritten',0,0,16
	.byte	'IfxAsclin_ChecksumInjection_written',0,1,0,30
	.byte	'IfxAsclin_ChecksumInjection',0,12,95,3
	.word	90459
	.byte	15,12,101,9,1,16
	.byte	'IfxAsclin_ClockPolarity_idleLow',0,0,16
	.byte	'IfxAsclin_ClockPolarity_idleHigh',0,1,0,30
	.byte	'IfxAsclin_ClockPolarity',0,12,105,3
	.word	90580
	.byte	15,12,110,9,1,16
	.byte	'IfxAsclin_ClockSource_noClock',0,0,16
	.byte	'IfxAsclin_ClockSource_kernelClock',0,1,16
	.byte	'IfxAsclin_ClockSource_oscillatorClock',0,2,16
	.byte	'IfxAsclin_ClockSource_flexRayClock',0,4,16
	.byte	'IfxAsclin_ClockSource_ascFastClock',0,8,16
	.byte	'IfxAsclin_ClockSource_ascSlowClock',0,16,0,30
	.byte	'IfxAsclin_ClockSource',0,12,118,3
	.word	90687
	.byte	30
	.byte	'IfxAsclin_CtsInputSelect',0,12,129,1,3
	.word	17541
	.byte	15,12,134,1,9,1,16
	.byte	'IfxAsclin_DataLength_1',0,0,16
	.byte	'IfxAsclin_DataLength_2',0,1,16
	.byte	'IfxAsclin_DataLength_3',0,2,16
	.byte	'IfxAsclin_DataLength_4',0,3,16
	.byte	'IfxAsclin_DataLength_5',0,4,16
	.byte	'IfxAsclin_DataLength_6',0,5,16
	.byte	'IfxAsclin_DataLength_7',0,6,16
	.byte	'IfxAsclin_DataLength_8',0,7,16
	.byte	'IfxAsclin_DataLength_9',0,8,16
	.byte	'IfxAsclin_DataLength_10',0,9,16
	.byte	'IfxAsclin_DataLength_11',0,10,16
	.byte	'IfxAsclin_DataLength_12',0,11,16
	.byte	'IfxAsclin_DataLength_13',0,12,16
	.byte	'IfxAsclin_DataLength_14',0,13,16
	.byte	'IfxAsclin_DataLength_15',0,14,16
	.byte	'IfxAsclin_DataLength_16',0,15,0,30
	.byte	'IfxAsclin_DataLength',0,12,152,1,3
	.word	90976
	.byte	15,12,157,1,9,1,16
	.byte	'IfxAsclin_FrameMode_initialise',0,0,16
	.byte	'IfxAsclin_FrameMode_asc',0,1,16
	.byte	'IfxAsclin_FrameMode_spi',0,2,16
	.byte	'IfxAsclin_FrameMode_lin',0,3,0,30
	.byte	'IfxAsclin_FrameMode',0,12,163,1,3
	.word	91420
	.byte	15,12,168,1,9,1,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerAndResponse',0,0,16
	.byte	'IfxAsclin_HeaderResponseSelect_headerOnly',0,1,0,30
	.byte	'IfxAsclin_HeaderResponseSelect',0,12,172,1,3
	.word	91567
	.byte	15,12,179,1,9,1,16
	.byte	'IfxAsclin_IdleDelay_0',0,0,16
	.byte	'IfxAsclin_IdleDelay_1',0,1,16
	.byte	'IfxAsclin_IdleDelay_2',0,2,16
	.byte	'IfxAsclin_IdleDelay_3',0,3,16
	.byte	'IfxAsclin_IdleDelay_4',0,4,16
	.byte	'IfxAsclin_IdleDelay_5',0,5,16
	.byte	'IfxAsclin_IdleDelay_6',0,6,16
	.byte	'IfxAsclin_IdleDelay_7',0,7,0,30
	.byte	'IfxAsclin_IdleDelay',0,12,189,1,3
	.word	91709
	.byte	15,12,195,1,9,1,16
	.byte	'IfxAsclin_LeadDelay_0',0,0,16
	.byte	'IfxAsclin_LeadDelay_1',0,1,16
	.byte	'IfxAsclin_LeadDelay_2',0,2,16
	.byte	'IfxAsclin_LeadDelay_3',0,3,16
	.byte	'IfxAsclin_LeadDelay_4',0,4,16
	.byte	'IfxAsclin_LeadDelay_5',0,5,16
	.byte	'IfxAsclin_LeadDelay_6',0,6,16
	.byte	'IfxAsclin_LeadDelay_7',0,7,0,30
	.byte	'IfxAsclin_LeadDelay',0,12,205,1,3
	.word	91937
	.byte	15,12,210,1,9,1,16
	.byte	'IfxAsclin_LinMode_slave',0,0,16
	.byte	'IfxAsclin_LinMode_master',0,1,0,30
	.byte	'IfxAsclin_LinMode',0,12,214,1,3
	.word	92165
	.byte	15,12,219,1,9,1,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_frameTimeout',0,0,16
	.byte	'IfxAsclin_LinResponseTimeoutMode_responseTimeout',0,1,0,30
	.byte	'IfxAsclin_LinResponseTimeoutMode',0,12,223,1,3
	.word	92252
	.byte	15,12,228,1,9,1,16
	.byte	'IfxAsclin_OversamplingFactor_4',0,3,16
	.byte	'IfxAsclin_OversamplingFactor_5',0,4,16
	.byte	'IfxAsclin_OversamplingFactor_6',0,5,16
	.byte	'IfxAsclin_OversamplingFactor_7',0,6,16
	.byte	'IfxAsclin_OversamplingFactor_8',0,7,16
	.byte	'IfxAsclin_OversamplingFactor_9',0,8,16
	.byte	'IfxAsclin_OversamplingFactor_10',0,9,16
	.byte	'IfxAsclin_OversamplingFactor_11',0,10,16
	.byte	'IfxAsclin_OversamplingFactor_12',0,11,16
	.byte	'IfxAsclin_OversamplingFactor_13',0,12,16
	.byte	'IfxAsclin_OversamplingFactor_14',0,13,16
	.byte	'IfxAsclin_OversamplingFactor_15',0,14,16
	.byte	'IfxAsclin_OversamplingFactor_16',0,15,0,30
	.byte	'IfxAsclin_OversamplingFactor',0,12,243,1,3
	.word	92400
	.byte	15,12,248,1,9,1,16
	.byte	'IfxAsclin_ParityType_even',0,0,16
	.byte	'IfxAsclin_ParityType_odd',0,1,0,30
	.byte	'IfxAsclin_ParityType',0,12,252,1,3
	.word	92881
	.byte	15,12,129,2,9,1,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxFifo',0,0,16
	.byte	'IfxAsclin_ReceiveBufferMode_rxBuffer',0,1,0,30
	.byte	'IfxAsclin_ReceiveBufferMode',0,12,133,2,3
	.word	92973
	.byte	15,12,138,2,9,1,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeHigh',0,0,16
	.byte	'IfxAsclin_RtsCtsPolarity_activeLow',0,1,0,30
	.byte	'IfxAsclin_RtsCtsPolarity',0,12,142,2,3
	.word	93093
	.byte	15,12,147,2,9,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_1',0,0,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_2',0,1,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_3',0,2,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_4',0,3,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_5',0,4,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_6',0,5,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_7',0,6,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_8',0,7,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_9',0,8,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_10',0,9,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_11',0,10,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_12',0,11,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_13',0,12,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_14',0,13,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_15',0,14,16
	.byte	'IfxAsclin_RxFifoInterruptLevel_16',0,15,0,30
	.byte	'IfxAsclin_RxFifoInterruptLevel',0,12,165,2,3
	.word	93209
	.byte	15,12,170,2,9,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_0',0,0,16
	.byte	'IfxAsclin_RxFifoOutletWidth_1',0,1,16
	.byte	'IfxAsclin_RxFifoOutletWidth_2',0,2,16
	.byte	'IfxAsclin_RxFifoOutletWidth_3',0,3,0,30
	.byte	'IfxAsclin_RxFifoOutletWidth',0,12,176,2,3
	.word	93823
	.byte	30
	.byte	'IfxAsclin_RxInputSelect',0,12,191,2,3
	.word	17725
	.byte	15,12,196,2,9,1,16
	.byte	'IfxAsclin_SamplePointPosition_1',0,1,16
	.byte	'IfxAsclin_SamplePointPosition_2',0,2,16
	.byte	'IfxAsclin_SamplePointPosition_3',0,3,16
	.byte	'IfxAsclin_SamplePointPosition_4',0,4,16
	.byte	'IfxAsclin_SamplePointPosition_5',0,5,16
	.byte	'IfxAsclin_SamplePointPosition_6',0,6,16
	.byte	'IfxAsclin_SamplePointPosition_7',0,7,16
	.byte	'IfxAsclin_SamplePointPosition_8',0,8,16
	.byte	'IfxAsclin_SamplePointPosition_9',0,9,16
	.byte	'IfxAsclin_SamplePointPosition_10',0,10,16
	.byte	'IfxAsclin_SamplePointPosition_11',0,11,16
	.byte	'IfxAsclin_SamplePointPosition_12',0,12,16
	.byte	'IfxAsclin_SamplePointPosition_13',0,13,16
	.byte	'IfxAsclin_SamplePointPosition_14',0,14,16
	.byte	'IfxAsclin_SamplePointPosition_15',0,15,0,30
	.byte	'IfxAsclin_SamplePointPosition',0,12,213,2,3
	.word	94028
	.byte	15,12,218,2,9,1,16
	.byte	'IfxAsclin_SamplesPerBit_one',0,0,16
	.byte	'IfxAsclin_SamplesPerBit_three',0,1,0,30
	.byte	'IfxAsclin_SamplesPerBit',0,12,222,2,3
	.word	94590
	.byte	15,12,228,2,9,1,16
	.byte	'IfxAsclin_ShiftDirection_lsbFirst',0,0,16
	.byte	'IfxAsclin_ShiftDirection_msbFirst',0,1,0,30
	.byte	'IfxAsclin_ShiftDirection',0,12,232,2,3
	.word	94692
	.byte	15,12,238,2,9,1,16
	.byte	'IfxAsclin_SlavePolarity_idleLow',0,0,16
	.byte	'IfxAsclin_SlavePolarity_idlehigh',0,1,0,30
	.byte	'IfxAsclin_SlavePolarity',0,12,242,2,3
	.word	94805
	.byte	15,12,247,2,9,1,16
	.byte	'IfxAsclin_SleepMode_enable',0,0,16
	.byte	'IfxAsclin_SleepMode_disable',0,1,0,30
	.byte	'IfxAsclin_SleepMode',0,12,251,2,3
	.word	94914
	.byte	15,12,136,3,9,1,16
	.byte	'IfxAsclin_StopBit_0',0,0,16
	.byte	'IfxAsclin_StopBit_1',0,1,16
	.byte	'IfxAsclin_StopBit_2',0,2,16
	.byte	'IfxAsclin_StopBit_3',0,3,16
	.byte	'IfxAsclin_StopBit_4',0,4,16
	.byte	'IfxAsclin_StopBit_5',0,5,16
	.byte	'IfxAsclin_StopBit_6',0,6,16
	.byte	'IfxAsclin_StopBit_7',0,7,0,30
	.byte	'IfxAsclin_StopBit',0,12,146,3,3
	.word	95009
	.byte	15,12,150,3,9,1,16
	.byte	'IfxAsclin_SuspendMode_none',0,0,16
	.byte	'IfxAsclin_SuspendMode_hard',0,1,16
	.byte	'IfxAsclin_SuspendMode_soft',0,2,0,30
	.byte	'IfxAsclin_SuspendMode',0,12,155,3,3
	.word	95219
	.byte	15,12,160,3,9,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_0',0,0,16
	.byte	'IfxAsclin_TxFifoInletWidth_1',0,1,16
	.byte	'IfxAsclin_TxFifoInletWidth_2',0,2,16
	.byte	'IfxAsclin_TxFifoInletWidth_3',0,3,0,30
	.byte	'IfxAsclin_TxFifoInletWidth',0,12,166,3,3
	.word	95344
	.byte	15,12,171,3,9,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_0',0,0,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_1',0,1,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_2',0,2,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_3',0,3,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_4',0,4,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_5',0,5,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_6',0,6,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_7',0,7,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_8',0,8,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_9',0,9,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_10',0,10,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_11',0,11,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_12',0,12,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_13',0,13,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_14',0,14,16
	.byte	'IfxAsclin_TxFifoInterruptLevel_15',0,15,0,30
	.byte	'IfxAsclin_TxFifoInterruptLevel',0,12,189,3,3
	.word	95511
	.byte	30
	.byte	'Ifx_Fifo_Shared',0,14,66,3
	.word	18042
	.byte	30
	.byte	'Ifx_Fifo',0,14,83,3
	.word	18133
	.byte	15,15,151,1,9,1,16
	.byte	'IfxStm_Comparator_0',0,0,16
	.byte	'IfxStm_Comparator_1',0,1,0,30
	.byte	'IfxStm_Comparator',0,15,155,1,3
	.word	96165
	.byte	15,15,159,1,9,1,16
	.byte	'IfxStm_ComparatorInterrupt_ir0',0,0,16
	.byte	'IfxStm_ComparatorInterrupt_ir1',0,1,0,30
	.byte	'IfxStm_ComparatorInterrupt',0,15,163,1,3
	.word	96243
	.byte	15,15,167,1,9,1,16
	.byte	'IfxStm_ComparatorOffset_0',0,0,16
	.byte	'IfxStm_ComparatorOffset_1',0,1,16
	.byte	'IfxStm_ComparatorOffset_2',0,2,16
	.byte	'IfxStm_ComparatorOffset_3',0,3,16
	.byte	'IfxStm_ComparatorOffset_4',0,4,16
	.byte	'IfxStm_ComparatorOffset_5',0,5,16
	.byte	'IfxStm_ComparatorOffset_6',0,6,16
	.byte	'IfxStm_ComparatorOffset_7',0,7,16
	.byte	'IfxStm_ComparatorOffset_8',0,8,16
	.byte	'IfxStm_ComparatorOffset_9',0,9,16
	.byte	'IfxStm_ComparatorOffset_10',0,10,16
	.byte	'IfxStm_ComparatorOffset_11',0,11,16
	.byte	'IfxStm_ComparatorOffset_12',0,12,16
	.byte	'IfxStm_ComparatorOffset_13',0,13,16
	.byte	'IfxStm_ComparatorOffset_14',0,14,16
	.byte	'IfxStm_ComparatorOffset_15',0,15,16
	.byte	'IfxStm_ComparatorOffset_16',0,16,16
	.byte	'IfxStm_ComparatorOffset_17',0,17,16
	.byte	'IfxStm_ComparatorOffset_18',0,18,16
	.byte	'IfxStm_ComparatorOffset_19',0,19,16
	.byte	'IfxStm_ComparatorOffset_20',0,20,16
	.byte	'IfxStm_ComparatorOffset_21',0,21,16
	.byte	'IfxStm_ComparatorOffset_22',0,22,16
	.byte	'IfxStm_ComparatorOffset_23',0,23,16
	.byte	'IfxStm_ComparatorOffset_24',0,24,16
	.byte	'IfxStm_ComparatorOffset_25',0,25,16
	.byte	'IfxStm_ComparatorOffset_26',0,26,16
	.byte	'IfxStm_ComparatorOffset_27',0,27,16
	.byte	'IfxStm_ComparatorOffset_28',0,28,16
	.byte	'IfxStm_ComparatorOffset_29',0,29,16
	.byte	'IfxStm_ComparatorOffset_30',0,30,16
	.byte	'IfxStm_ComparatorOffset_31',0,31,0,30
	.byte	'IfxStm_ComparatorOffset',0,15,201,1,3
	.word	96352
	.byte	15,15,205,1,9,1,16
	.byte	'IfxStm_ComparatorSize_1Bit',0,0,16
	.byte	'IfxStm_ComparatorSize_2Bits',0,1,16
	.byte	'IfxStm_ComparatorSize_3Bits',0,2,16
	.byte	'IfxStm_ComparatorSize_4Bits',0,3,16
	.byte	'IfxStm_ComparatorSize_5Bits',0,4,16
	.byte	'IfxStm_ComparatorSize_6Bits',0,5,16
	.byte	'IfxStm_ComparatorSize_7Bits',0,6,16
	.byte	'IfxStm_ComparatorSize_8Bits',0,7,16
	.byte	'IfxStm_ComparatorSize_9Bits',0,8,16
	.byte	'IfxStm_ComparatorSize_10Bits',0,9,16
	.byte	'IfxStm_ComparatorSize_11Bits',0,10,16
	.byte	'IfxStm_ComparatorSize_12Bits',0,11,16
	.byte	'IfxStm_ComparatorSize_13Bits',0,12,16
	.byte	'IfxStm_ComparatorSize_14Bits',0,13,16
	.byte	'IfxStm_ComparatorSize_15Bits',0,14,16
	.byte	'IfxStm_ComparatorSize_16Bits',0,15,16
	.byte	'IfxStm_ComparatorSize_17Bits',0,16,16
	.byte	'IfxStm_ComparatorSize_18Bits',0,17,16
	.byte	'IfxStm_ComparatorSize_19Bits',0,18,16
	.byte	'IfxStm_ComparatorSize_20Bits',0,19,16
	.byte	'IfxStm_ComparatorSize_21Bits',0,20,16
	.byte	'IfxStm_ComparatorSize_22Bits',0,21,16
	.byte	'IfxStm_ComparatorSize_23Bits',0,22,16
	.byte	'IfxStm_ComparatorSize_24Bits',0,23,16
	.byte	'IfxStm_ComparatorSize_25Bits',0,24,16
	.byte	'IfxStm_ComparatorSize_26Bits',0,25,16
	.byte	'IfxStm_ComparatorSize_27Bits',0,26,16
	.byte	'IfxStm_ComparatorSize_28Bits',0,27,16
	.byte	'IfxStm_ComparatorSize_29Bits',0,28,16
	.byte	'IfxStm_ComparatorSize_30Bits',0,29,16
	.byte	'IfxStm_ComparatorSize_31Bits',0,30,16
	.byte	'IfxStm_ComparatorSize_32Bits',0,31,0,30
	.byte	'IfxStm_ComparatorSize',0,15,239,1,3
	.word	97310
	.byte	15,15,244,1,9,1,16
	.byte	'IfxStm_SleepMode_enable',0,0,16
	.byte	'IfxStm_SleepMode_disable',0,1,0,30
	.byte	'IfxStm_SleepMode',0,15,248,1,3
	.word	98330
	.byte	15,15,252,1,9,1,16
	.byte	'IfxStm_SuspendMode_none',0,0,16
	.byte	'IfxStm_SuspendMode_hard',0,1,16
	.byte	'IfxStm_SuspendMode_soft',0,2,0,30
	.byte	'IfxStm_SuspendMode',0,15,129,2,3
	.word	98416
	.byte	30
	.byte	'IfxStdIf_InterfaceDriver',0,30,118,15
	.word	367
	.byte	3
	.word	18017
	.byte	32
	.word	628
	.byte	1,1,33
	.word	367
	.byte	33
	.word	367
	.byte	33
	.word	98562
	.byte	33
	.word	21990
	.byte	0,3
	.word	98567
	.byte	30
	.byte	'IfxStdIf_DPipe_Write',0,31,92,19
	.word	98595
	.byte	30
	.byte	'IfxStdIf_DPipe_Read',0,31,107,19
	.word	98595
	.byte	32
	.word	18030
	.byte	1,1,33
	.word	367
	.byte	0,3
	.word	98657
	.byte	30
	.byte	'IfxStdIf_DPipe_GetReadCount',0,31,115,18
	.word	98670
	.byte	14
	.word	628
	.byte	3
	.word	98711
	.byte	32
	.word	98716
	.byte	1,1,33
	.word	367
	.byte	0,3
	.word	98721
	.byte	30
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,31,123,36
	.word	98734
	.byte	30
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,31,147,1,18
	.word	98670
	.byte	3
	.word	98721
	.byte	30
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,31,155,1,37
	.word	98813
	.byte	32
	.word	628
	.byte	1,1,33
	.word	367
	.byte	33
	.word	18017
	.byte	33
	.word	21990
	.byte	0,3
	.word	98856
	.byte	30
	.byte	'IfxStdIf_DPipe_CanReadCount',0,31,166,1,19
	.word	98879
	.byte	30
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,31,177,1,19
	.word	98879
	.byte	32
	.word	628
	.byte	1,1,33
	.word	367
	.byte	33
	.word	21990
	.byte	0,3
	.word	98959
	.byte	30
	.byte	'IfxStdIf_DPipe_FlushTx',0,31,186,1,19
	.word	98977
	.byte	34,1,1,33
	.word	367
	.byte	0,3
	.word	99014
	.byte	30
	.byte	'IfxStdIf_DPipe_ClearTx',0,31,200,1,16
	.word	99023
	.byte	30
	.byte	'IfxStdIf_DPipe_ClearRx',0,31,193,1,16
	.word	99023
	.byte	30
	.byte	'IfxStdIf_DPipe_OnReceive',0,31,208,1,16
	.word	99023
	.byte	30
	.byte	'IfxStdIf_DPipe_OnTransmit',0,31,215,1,16
	.word	99023
	.byte	30
	.byte	'IfxStdIf_DPipe_OnError',0,31,222,1,16
	.word	99023
	.byte	32
	.word	1762
	.byte	1,1,33
	.word	367
	.byte	0,3
	.word	99193
	.byte	30
	.byte	'IfxStdIf_DPipe_GetSendCount',0,31,131,1,18
	.word	99206
	.byte	32
	.word	21990
	.byte	1,1,33
	.word	367
	.byte	0,3
	.word	99248
	.byte	30
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,31,139,1,24
	.word	99261
	.byte	30
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,31,229,1,16
	.word	99023
	.byte	10
	.byte	'IfxStdIf_DPipe_',0,31,233,1,8,76,13
	.byte	'driver',0
	.word	98529
	.byte	4,2,35,0,13
	.byte	'txDisabled',0
	.word	628
	.byte	1,2,35,4,13
	.byte	'write',0
	.word	98600
	.byte	4,2,35,8,13
	.byte	'read',0
	.word	98629
	.byte	4,2,35,12,13
	.byte	'getReadCount',0
	.word	98675
	.byte	4,2,35,16,13
	.byte	'getReadEvent',0
	.word	98739
	.byte	4,2,35,20,13
	.byte	'getWriteCount',0
	.word	98775
	.byte	4,2,35,24,13
	.byte	'getWriteEvent',0
	.word	98818
	.byte	4,2,35,28,13
	.byte	'canReadCount',0
	.word	98884
	.byte	4,2,35,32,13
	.byte	'canWriteCount',0
	.word	98921
	.byte	4,2,35,36,13
	.byte	'flushTx',0
	.word	98982
	.byte	4,2,35,40,13
	.byte	'clearTx',0
	.word	99028
	.byte	4,2,35,44,13
	.byte	'clearRx',0
	.word	99060
	.byte	4,2,35,48,13
	.byte	'onReceive',0
	.word	99092
	.byte	4,2,35,52,13
	.byte	'onTransmit',0
	.word	99126
	.byte	4,2,35,56,13
	.byte	'onError',0
	.word	99161
	.byte	4,2,35,60,13
	.byte	'getSendCount',0
	.word	99211
	.byte	4,2,35,64,13
	.byte	'getTxTimeStamp',0
	.word	99266
	.byte	4,2,35,68,13
	.byte	'resetSendCount',0
	.word	99305
	.byte	4,2,35,72,0,30
	.byte	'IfxStdIf_DPipe',0,31,71,32
	.word	99344
	.byte	3
	.word	361
	.byte	3
	.word	98567
	.byte	3
	.word	98567
	.byte	3
	.word	98657
	.byte	3
	.word	98721
	.byte	3
	.word	98657
	.byte	3
	.word	98721
	.byte	3
	.word	98856
	.byte	3
	.word	98856
	.byte	3
	.word	98959
	.byte	3
	.word	99014
	.byte	3
	.word	99014
	.byte	3
	.word	99014
	.byte	3
	.word	99014
	.byte	3
	.word	99014
	.byte	3
	.word	99193
	.byte	3
	.word	99248
	.byte	3
	.word	99014
	.byte	14
	.word	628
	.byte	3
	.word	99857
	.byte	30
	.byte	'IfxStdIf_DPipe_WriteEvent',0,31,73,32
	.word	99862
	.byte	30
	.byte	'IfxStdIf_DPipe_ReadEvent',0,31,74,32
	.word	99862
	.byte	20,32,252,1,9,1,11
	.byte	'parityError',0,1
	.word	628
	.byte	1,7,2,35,0,11
	.byte	'frameError',0,1
	.word	628
	.byte	1,6,2,35,0,11
	.byte	'rxFifoOverflow',0,1
	.word	628
	.byte	1,5,2,35,0,11
	.byte	'rxFifoUnderflow',0,1
	.word	628
	.byte	1,4,2,35,0,11
	.byte	'txFifoOverflow',0,1
	.word	628
	.byte	1,3,2,35,0,0,30
	.byte	'IfxAsclin_Asc_ErrorFlags',0,32,131,2,3
	.word	99934
	.byte	20,32,137,2,9,8,13
	.byte	'baudrate',0
	.word	249
	.byte	4,2,35,0,13
	.byte	'prescaler',0
	.word	645
	.byte	2,2,35,4,13
	.byte	'oversampling',0
	.word	92400
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_BaudRate',0,32,142,2,3
	.word	100099
	.byte	20,32,146,2,9,2,13
	.byte	'medianFilter',0
	.word	94590
	.byte	1,2,35,0,13
	.byte	'samplePointPosition',0
	.word	94028
	.byte	1,2,35,1,0,30
	.byte	'IfxAsclin_Asc_BitTimingControl',0,32,150,2,3
	.word	100197
	.byte	20,32,154,2,9,6,13
	.byte	'inWidth',0
	.word	95344
	.byte	1,2,35,0,13
	.byte	'outWidth',0
	.word	93823
	.byte	1,2,35,1,13
	.byte	'txFifoInterruptLevel',0
	.word	95511
	.byte	1,2,35,2,13
	.byte	'rxFifoInterruptLevel',0
	.word	93209
	.byte	1,2,35,3,13
	.byte	'buffMode',0
	.word	92973
	.byte	1,2,35,4,0,30
	.byte	'IfxAsclin_Asc_FifoControl',0,32,161,2,3
	.word	100295
	.byte	20,32,165,2,9,8,13
	.byte	'idleDelay',0
	.word	91709
	.byte	1,2,35,0,13
	.byte	'stopBit',0
	.word	95009
	.byte	1,2,35,1,13
	.byte	'frameMode',0
	.word	91420
	.byte	1,2,35,2,13
	.byte	'shiftDir',0
	.word	94692
	.byte	1,2,35,3,13
	.byte	'parityType',0
	.word	92881
	.byte	1,2,35,4,13
	.byte	'dataLength',0
	.word	90976
	.byte	1,2,35,5,13
	.byte	'parityBit',0
	.word	628
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_FrameControl',0,32,174,2,3
	.word	100450
	.byte	20,32,178,2,9,8,13
	.byte	'txPriority',0
	.word	645
	.byte	2,2,35,0,13
	.byte	'rxPriority',0
	.word	645
	.byte	2,2,35,2,13
	.byte	'erPriority',0
	.word	645
	.byte	2,2,35,4,13
	.byte	'typeOfService',0
	.word	89799
	.byte	1,2,35,6,0,30
	.byte	'IfxAsclin_Asc_InterruptConfig',0,32,184,2,3
	.word	100625
	.byte	26
	.word	89877
	.byte	3
	.word	100754
	.byte	26
	.word	89958
	.byte	3
	.word	100764
	.byte	26
	.word	90038
	.byte	3
	.word	100774
	.byte	26
	.word	90286
	.byte	3
	.word	100784
	.byte	20,32,188,2,9,32,13
	.byte	'cts',0
	.word	100759
	.byte	4,2,35,0,13
	.byte	'ctsMode',0
	.word	9751
	.byte	1,2,35,4,13
	.byte	'rx',0
	.word	100769
	.byte	4,2,35,8,13
	.byte	'rxMode',0
	.word	9751
	.byte	1,2,35,12,13
	.byte	'rts',0
	.word	100779
	.byte	4,2,35,16,13
	.byte	'rtsMode',0
	.word	9956
	.byte	1,2,35,20,13
	.byte	'tx',0
	.word	100789
	.byte	4,2,35,24,13
	.byte	'txMode',0
	.word	9956
	.byte	1,2,35,28,13
	.byte	'pinDriver',0
	.word	88594
	.byte	1,2,35,29,0,30
	.byte	'IfxAsclin_Asc_Pins',0,32,199,2,3
	.word	100794
	.byte	12,32,205,2,9,1,13
	.byte	'ALL',0
	.word	628
	.byte	1,2,35,0,13
	.byte	'flags',0
	.word	99934
	.byte	1,2,35,0,0,30
	.byte	'IfxAsclin_Asc_ErrorFlagsUnion',0,32,209,2,3
	.word	100964
	.byte	30
	.byte	'_iob_flag_t',0,33,82,25
	.word	645
	.byte	7
	.byte	'char',0,1,6,30
	.byte	'int8',0,34,54,29
	.word	101058
	.byte	30
	.byte	'int16',0,34,55,29
	.word	18017
	.byte	30
	.byte	'int32',0,34,56,29
	.word	469
	.byte	30
	.byte	'int64',0,34,57,29
	.word	21990
	.byte	30
	.byte	'uart_tx_pin_enum',0,18,74,2
	.word	23290
	.byte	30
	.byte	'uart_rx_pin_enum',0,18,100,2
	.word	23742
	.byte	30
	.byte	'uart_index_enum',0,18,109,2
	.word	23056
	.byte	30
	.byte	'u1_adapter_status_t',0,20,47,3
	.word	24158
.L149:
	.byte	18,128,8
	.word	628
	.byte	19,255,7,0
.L150:
	.byte	14
	.word	1762
.L151:
	.byte	14
	.word	1762
.L152:
	.byte	14
	.word	628
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L58:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,19,1,58,15,59,15,57,15,11,15,0,0,21,46,1,49,19
	.byte	0,0,22,5,0,49,19,0,0,23,29,1,49,19,0,0,24,11,0,49,19,0,0,25,11,1,49,19,0,0,26,38,0,73,19,0,0,27,46,1,3
	.byte	8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,28,46,1,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12
	.byte	60,12,0,0,29,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,30,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,31,21,0,54,15,0,0,32,21,1,73,19,54,15,39,12,0,0,33,5,0,73,19,0,0,34,21,1,54,15,39,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L59:
	.word	.L171-.L170
.L170:
	.half	3
	.word	.L173-.L172
.L172:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Asclin\\Std\\IfxAsclin.h',0
	.byte	0,0,0
	.byte	'IfxAsclin_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Lib\\DataHandling\\Ifx_Fifo.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Stm\\Std\\IfxStm.h',0
	.byte	0,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Bsp\\Bsp.h',0
	.byte	0,0,0
	.byte	'zf_driver_uart.h',0,2,0,0
	.byte	'zf_driver_delay.h',0,2,0,0
	.byte	'..\\code\\user1\\u1_adapter.h',0,0,0,0
	.byte	'zf_driver_timer.h',0,2,0,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxAsclin_PinMap.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0
	.byte	0,0,0
	.byte	'ifxAsclin_Asc.h',0,3,0,0
	.byte	'stdio.h',0,4,0,0
	.byte	'zf_common_typedef.h',0,5,0,0,0
.L173:
.L171:
	.sdecl	'.debug_info',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_info'
.L60:
	.word	301
	.half	3
	.word	.L61
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L63,.L62
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_uart_rx_handler',0,1,21,6,1,1,1
	.word	.L37,.L122,.L36
	.byte	4
	.word	.L37,.L122
	.byte	5
	.byte	'data',0,1,23,11
	.word	.L123,.L124
	.byte	4
	.word	.L3,.L2
	.byte	5
	.byte	'next_head',0,1,29,16
	.word	.L125,.L126
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_abbrev'
.L61:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_line'
.L62:
	.word	.L175-.L174
.L174:
	.half	3
	.word	.L177-.L176
.L176:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L177:
	.byte	5,6,7,0,5,2
	.word	.L37
	.byte	3,20,1,5,48,9
	.half	.L153-.L37
	.byte	3,5,1,5,29,9
	.half	.L3-.L153
	.byte	3,3,1,5,37,9
	.half	.L178-.L3
	.byte	1,5,44,9
	.half	.L179-.L178
	.byte	1,5,42,9
	.half	.L180-.L179
	.byte	1,5,25,9
	.half	.L154-.L180
	.byte	3,3,1,5,9,9
	.half	.L181-.L154
	.byte	1,5,23,7,9
	.half	.L182-.L181
	.byte	3,3,1,5,13,9
	.half	.L183-.L182
	.byte	1,5,22,9
	.half	.L184-.L183
	.byte	1,5,34,9
	.half	.L185-.L184
	.byte	1,5,32,9
	.half	.L186-.L185
	.byte	1,5,13,9
	.half	.L187-.L186
	.byte	3,1,1,5,21,9
	.half	.L188-.L187
	.byte	1,5,32,9
	.half	.L189-.L188
	.byte	3,127,1,5,13,9
	.half	.L4-.L189
	.byte	3,6,1,5,24,9
	.half	.L190-.L4
	.byte	1,5,32,9
	.half	.L191-.L190
	.byte	1,5,59,9
	.half	.L192-.L191
	.byte	1,5,57,9
	.half	.L193-.L192
	.byte	1,5,21,9
	.half	.L155-.L193
	.byte	1,5,13,9
	.half	.L194-.L155
	.byte	3,1,1,5,27,9
	.half	.L195-.L194
	.byte	1,5,25,9
	.half	.L196-.L195
	.byte	1,5,13,9
	.half	.L197-.L196
	.byte	3,1,1,5,30,9
	.half	.L198-.L197
	.byte	1,5,28,9
	.half	.L199-.L198
	.byte	1,5,27,9
	.half	.L2-.L199
	.byte	3,111,1,5,43,9
	.half	.L200-.L2
	.byte	1,5,48,9
	.half	.L201-.L200
	.byte	1,5,1,7,9
	.half	.L202-.L201
	.byte	3,20,1,7,9
	.half	.L64-.L202
	.byte	0,1,1
.L175:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_ranges'
.L63:
	.word	-1,.L37,0,.L64-.L37,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_init')
	.sect	'.debug_info'
.L65:
	.word	244
	.half	3
	.word	.L66
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L68,.L67
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_init',0,1,49,9
	.word	.L123
	.byte	1,1,1
	.word	.L39,.L127,.L38
	.byte	4
	.word	.L39,.L127
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_init')
	.sect	'.debug_abbrev'
.L66:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_init')
	.sect	'.debug_line'
.L67:
	.word	.L204-.L203
.L203:
	.half	3
	.word	.L206-.L205
.L205:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L206:
	.byte	5,15,7,0,5,2
	.word	.L39
	.byte	3,51,1,5,30,9
	.half	.L207-.L39
	.byte	1,5,48,9
	.half	.L208-.L207
	.byte	1,5,64,9
	.half	.L209-.L208
	.byte	1,5,23,9
	.half	.L210-.L209
	.byte	3,3,1,5,38,9
	.half	.L211-.L210
	.byte	1,5,5,9
	.half	.L212-.L211
	.byte	3,3,1,5,15,9
	.half	.L213-.L212
	.byte	1,5,13,9
	.half	.L214-.L213
	.byte	1,5,5,9
	.half	.L215-.L214
	.byte	3,1,1,5,15,9
	.half	.L216-.L215
	.byte	1,5,13,9
	.half	.L217-.L216
	.byte	1,5,5,9
	.half	.L218-.L217
	.byte	3,1,1,5,19,9
	.half	.L219-.L218
	.byte	1,5,17,9
	.half	.L220-.L219
	.byte	1,5,5,9
	.half	.L221-.L220
	.byte	3,3,1,5,22,9
	.half	.L222-.L221
	.byte	1,5,20,9
	.half	.L223-.L222
	.byte	1,5,5,9
	.half	.L224-.L223
	.byte	3,1,1,5,22,9
	.half	.L225-.L224
	.byte	1,5,20,9
	.half	.L226-.L225
	.byte	1,5,12,9
	.half	.L227-.L226
	.byte	3,6,1,5,5,9
	.half	.L228-.L227
	.byte	1,5,1,9
	.half	.L6-.L228
	.byte	3,1,1,7,9
	.half	.L69-.L6
	.byte	0,1,1
.L204:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_init')
	.sect	'.debug_ranges'
.L68:
	.word	-1,.L39,0,.L69-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_info'
.L70:
	.word	248
	.half	3
	.word	.L71
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L73,.L72
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_is_ready',0,1,74,9
	.word	.L123
	.byte	1,1,1
	.word	.L41,.L128,.L40
	.byte	4
	.word	.L41,.L128
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_abbrev'
.L71:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_line'
.L72:
	.word	.L230-.L229
.L229:
	.half	3
	.word	.L232-.L231
.L231:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L232:
	.byte	5,12,7,0,5,2
	.word	.L41
	.byte	3,203,0,1,5,5,9
	.half	.L233-.L41
	.byte	1,5,1,9
	.half	.L7-.L233
	.byte	3,1,1,7,9
	.half	.L74-.L7
	.byte	0,1,1
.L230:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_ranges'
.L73:
	.word	-1,.L41,0,.L74-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_info'
.L75:
	.word	282
	.half	3
	.word	.L76
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L78,.L77
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_send_data',0,1,80,9
	.word	.L123
	.byte	1,1,1
	.word	.L43,.L129,.L42
	.byte	4
	.byte	'data',0,1,80,43
	.word	.L130,.L131
	.byte	4
	.byte	'len',0,1,80,56
	.word	.L125,.L132
	.byte	5
	.word	.L43,.L129
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_abbrev'
.L76:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_line'
.L77:
	.word	.L235-.L234
.L234:
	.half	3
	.word	.L237-.L236
.L236:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L237:
	.byte	5,9,7,0,5,2
	.word	.L43
	.byte	3,207,0,1,9
	.half	.L158-.L43
	.byte	3,3,1,5,21,7,9
	.half	.L238-.L158
	.byte	1,5,9,7,9
	.half	.L8-.L238
	.byte	3,2,1,5,26,9
	.half	.L239-.L8
	.byte	1,5,24,9
	.half	.L240-.L239
	.byte	1,5,16,9
	.half	.L241-.L240
	.byte	3,1,1,5,9,9
	.half	.L242-.L241
	.byte	1,9
	.half	.L9-.L242
	.byte	3,4,1,9
	.half	.L243-.L9
	.byte	3,2,1,5,26,9
	.half	.L244-.L243
	.byte	1,5,24,9
	.half	.L245-.L244
	.byte	1,5,16,9
	.half	.L246-.L245
	.byte	3,1,1,5,9,9
	.half	.L247-.L246
	.byte	1,5,23,9
	.half	.L11-.L247
	.byte	3,4,1,5,44,9
	.half	.L157-.L11
	.byte	1,5,12,9
	.half	.L156-.L157
	.byte	3,2,1,5,5,9
	.half	.L248-.L156
	.byte	1,5,1,9
	.half	.L10-.L248
	.byte	3,1,1,7,9
	.half	.L79-.L10
	.byte	0,1,1
.L235:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_ranges'
.L78:
	.word	-1,.L43,0,.L79-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_info'
.L80:
	.word	361
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L83,.L82
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_receive_data',0,1,103,8
	.word	.L125
	.byte	1,1,1
	.word	.L45,.L133,.L44
	.byte	4
	.byte	'buffer',0,1,103,39
	.word	.L134,.L135
	.byte	4
	.byte	'max_len',0,1,103,54
	.word	.L125,.L136
	.byte	4
	.byte	'timeout_ms',0,1,103,70
	.word	.L125,.L137
	.byte	5
	.word	.L45,.L133
	.byte	6
	.byte	'recv_count',0,1,105,12
	.word	.L125,.L138
	.byte	6
	.byte	'start_time',0,1,106,12
	.word	.L125,.L139
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_line'
.L82:
	.word	.L250-.L249
.L249:
	.half	3
	.word	.L252-.L251
.L251:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L252:
	.byte	5,8,7,0,5,2
	.word	.L45
	.byte	3,230,0,1,5,23,9
	.half	.L253-.L45
	.byte	3,2,1,5,25,9
	.half	.L161-.L253
	.byte	3,1,1,5,9,9
	.half	.L162-.L161
	.byte	3,3,1,5,27,7,9
	.half	.L163-.L162
	.byte	1,5,9,7,9
	.half	.L14-.L163
	.byte	3,2,1,5,26,9
	.half	.L254-.L14
	.byte	1,5,24,9
	.half	.L255-.L254
	.byte	1,5,16,9
	.half	.L256-.L255
	.byte	3,1,1,5,9,9
	.half	.L257-.L256
	.byte	1,9
	.half	.L15-.L257
	.byte	3,4,1,9
	.half	.L258-.L15
	.byte	3,2,1,5,26,9
	.half	.L259-.L258
	.byte	1,5,24,9
	.half	.L260-.L259
	.byte	1,5,16,9
	.half	.L261-.L260
	.byte	3,1,1,5,9,9
	.half	.L262-.L261
	.byte	1,5,31,9
	.half	.L17-.L262
	.byte	3,3,1,5,12,9
	.half	.L20-.L17
	.byte	3,3,1,5,31,7,9
	.half	.L164-.L20
	.byte	1,5,50,9
	.half	.L263-.L164
	.byte	1,5,64,9
	.half	.L165-.L263
	.byte	1,5,13,7,9
	.half	.L166-.L165
	.byte	3,2,1,5,12,9
	.half	.L21-.L166
	.byte	3,4,1,5,23,9
	.half	.L264-.L21
	.byte	1,5,9,9
	.half	.L265-.L264
	.byte	1,5,19,7,9
	.half	.L266-.L265
	.byte	3,3,1,5,46,9
	.half	.L267-.L266
	.byte	1,5,36,9
	.half	.L268-.L267
	.byte	1,5,45,9
	.half	.L269-.L268
	.byte	1,5,34,9
	.half	.L270-.L269
	.byte	1,5,30,9
	.half	.L271-.L270
	.byte	1,5,13,9
	.half	.L272-.L271
	.byte	3,1,1,5,24,9
	.half	.L273-.L272
	.byte	1,5,32,9
	.half	.L274-.L273
	.byte	1,5,39,9
	.half	.L275-.L274
	.byte	1,5,37,9
	.half	.L276-.L275
	.byte	1,5,21,9
	.half	.L277-.L276
	.byte	1,5,54,9
	.half	.L278-.L277
	.byte	3,127,1,5,13,9
	.half	.L24-.L278
	.byte	3,6,1,5,33,7,9
	.half	.L167-.L24
	.byte	3,2,1,5,35,9
	.half	.L279-.L167
	.byte	1,5,17,9
	.half	.L26-.L279
	.byte	3,4,1,5,31,9
	.half	.L19-.L26
	.byte	3,104,1,5,5,7,9
	.half	.L23-.L19
	.byte	3,29,1,5,1,9
	.half	.L16-.L23
	.byte	3,1,1,7,9
	.half	.L84-.L16
	.byte	0,1,1
.L250:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L45,0,.L84-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_info'
.L85:
	.word	253
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L88,.L87
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_get_rx_count',0,1,155,1,8
	.word	.L125
	.byte	1,1,1
	.word	.L47,.L140,.L46
	.byte	4
	.word	.L47,.L140
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_line'
.L87:
	.word	.L281-.L280
.L280:
	.half	3
	.word	.L283-.L282
.L282:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L283:
	.byte	5,8,7,0,5,2
	.word	.L47
	.byte	3,156,1,1,5,19,9
	.half	.L284-.L47
	.byte	1,5,5,9
	.half	.L285-.L284
	.byte	1,5,16,7,9
	.half	.L286-.L285
	.byte	3,2,1,5,26,9
	.half	.L287-.L286
	.byte	1,5,24,9
	.half	.L288-.L287
	.byte	1,5,9,9
	.half	.L289-.L288
	.byte	1,5,16,9
	.half	.L30-.L289
	.byte	3,4,1,5,36,9
	.half	.L290-.L30
	.byte	1,5,34,9
	.half	.L291-.L290
	.byte	1,5,46,9
	.half	.L292-.L291
	.byte	1,5,44,9
	.half	.L293-.L292
	.byte	1,5,9,9
	.half	.L294-.L293
	.byte	1,5,1,9
	.half	.L31-.L294
	.byte	3,2,1,7,9
	.half	.L89-.L31
	.byte	0,1,1
.L281:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L47,0,.L89-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_info'
.L90:
	.word	252
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L93,.L92
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_clear_rx_buffer',0,1,168,1,6,1,1,1
	.word	.L49,.L141,.L48
	.byte	4
	.word	.L49,.L141
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_line'
.L92:
	.word	.L296-.L295
.L295:
	.half	3
	.word	.L298-.L297
.L297:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L298:
	.byte	5,5,7,0,5,2
	.word	.L49
	.byte	3,169,1,1,5,15,9
	.half	.L299-.L49
	.byte	1,5,13,9
	.half	.L300-.L299
	.byte	1,5,5,9
	.half	.L301-.L300
	.byte	3,1,1,5,15,9
	.half	.L302-.L301
	.byte	1,5,13,9
	.half	.L303-.L302
	.byte	1,5,5,9
	.half	.L304-.L303
	.byte	3,1,1,5,19,9
	.half	.L305-.L304
	.byte	1,5,17,9
	.half	.L306-.L305
	.byte	1,5,8,9
	.half	.L307-.L306
	.byte	3,2,1,5,5,9
	.half	.L308-.L307
	.byte	1,5,9,7,9
	.half	.L309-.L308
	.byte	3,2,1,5,26,9
	.half	.L310-.L309
	.byte	1,5,24,9
	.half	.L311-.L310
	.byte	1,5,1,9
	.half	.L33-.L311
	.byte	3,2,1,7,9
	.half	.L94-.L33
	.byte	0,1,1
.L296:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_ranges'
.L93:
	.word	-1,.L49,0,.L94-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_info'
.L95:
	.word	281
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L98,.L97
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_check_rx_overflow',0,1,181,1,9
	.word	.L123
	.byte	1,1,1
	.word	.L51,.L142,.L50
	.byte	4
	.word	.L51,.L142
	.byte	5
	.byte	'overflow',0,1,183,1,13
	.word	.L123,.L143
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_line'
.L97:
	.word	.L313-.L312
.L312:
	.half	3
	.word	.L315-.L314
.L314:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L315:
	.byte	5,24,7,0,5,2
	.word	.L51
	.byte	3,182,1,1,5,5,9
	.half	.L169-.L51
	.byte	3,1,1,5,19,9
	.half	.L316-.L169
	.byte	1,5,17,9
	.half	.L317-.L316
	.byte	1,5,5,9
	.half	.L318-.L317
	.byte	3,1,1,5,1,9
	.half	.L34-.L318
	.byte	3,1,1,7,9
	.half	.L99-.L34
	.byte	0,1,1
.L313:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_ranges'
.L98:
	.word	-1,.L51,0,.L99-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_info'
.L100:
	.word	251
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L103,.L102
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_get_status',0,1,189,1,21
	.word	.L144
	.byte	1,1,1
	.word	.L53,.L145,.L52
	.byte	4
	.word	.L53,.L145
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_line'
.L102:
	.word	.L320-.L319
.L319:
	.half	3
	.word	.L322-.L321
.L321:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L322:
	.byte	5,12,7,0,5,2
	.word	.L53
	.byte	3,190,1,1,5,5,9
	.half	.L323-.L53
	.byte	1,5,1,9
	.half	.L35-.L323
	.byte	3,1,1,7,9
	.half	.L104-.L35
	.byte	0,1,1
.L320:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_ranges'
.L103:
	.word	-1,.L53,0,.L104-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_info'
.L105:
	.word	266
	.half	3
	.word	.L106
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L108,.L107
	.byte	2
	.word	.L56
	.byte	3
	.byte	'u1_adapter_debug_output',0,1,195,1,6,1,1,1
	.word	.L55,.L146,.L54
	.byte	4
	.byte	'msg',0,1,195,1,42
	.word	.L147,.L148
	.byte	5
	.word	.L55,.L146
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_line'
.L107:
	.word	.L325-.L324
.L324:
	.half	3
	.word	.L327-.L326
.L326:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../code/user1/u1_adapter.c',0,0,0,0,0
.L327:
	.byte	5,1,7,0,5,2
	.word	.L55
	.byte	3,205,1,1,7,9
	.half	.L109-.L55
	.byte	0,1,1
.L325:
	.sdecl	'.debug_ranges',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_ranges'
.L108:
	.word	-1,.L55,0,.L109-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('rx_buffer')
	.sect	'.debug_info'
.L110:
	.word	211
	.half	3
	.word	.L111
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'rx_buffer',0,22,13,14
	.word	.L149
	.byte	5,3
	.word	rx_buffer
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('rx_buffer')
	.sect	'.debug_abbrev'
.L111:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('rx_head')
	.sect	'.debug_info'
.L112:
	.word	209
	.half	3
	.word	.L113
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'rx_head',0,22,14,24
	.word	.L150
	.byte	5,3
	.word	rx_head
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('rx_head')
	.sect	'.debug_abbrev'
.L113:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('rx_tail')
	.sect	'.debug_info'
.L114:
	.word	209
	.half	3
	.word	.L115
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'rx_tail',0,22,15,24
	.word	.L151
	.byte	5,3
	.word	rx_tail
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('rx_tail')
	.sect	'.debug_abbrev'
.L115:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('rx_overflow')
	.sect	'.debug_info'
.L116:
	.word	213
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'rx_overflow',0,22,16,25
	.word	.L152
	.byte	5,3
	.word	rx_overflow
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('rx_overflow')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('is_initialized')
	.sect	'.debug_info'
.L118:
	.word	216
	.half	3
	.word	.L119
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'is_initialized',0,22,17,16
	.word	.L123
	.byte	5,3
	.word	is_initialized
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('is_initialized')
	.sect	'.debug_abbrev'
.L119:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_info',debug,cluster('adapter_status')
	.sect	'.debug_info'
.L120:
	.word	216
	.half	3
	.word	.L121
	.byte	4,1
	.byte	'../code/user1/u1_adapter.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L56
	.byte	3
	.byte	'adapter_status',0,22,18,28
	.word	.L144
	.byte	5,3
	.word	adapter_status
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('adapter_status')
	.sect	'.debug_abbrev'
.L121:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,2,9,0,0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_loc'
.L143:
	.word	-1,.L51,.L169-.L51,.L142-.L51
	.half	1
	.byte	82
	.word	0,0
.L50:
	.word	-1,.L51,0,.L142-.L51
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L141-.L49
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_loc'
.L148:
	.word	-1,.L55,0,.L146-.L55
	.half	1
	.byte	100
	.word	0,0
.L54:
	.word	-1,.L55,0,.L146-.L55
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L140-.L47
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L145-.L53
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_init')
	.sect	'.debug_loc'
.L38:
	.word	-1,.L39,0,.L127-.L39
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L128-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_loc'
.L135:
	.word	-1,.L45,0,.L159-.L45
	.half	1
	.byte	100
	.word	.L160-.L45,.L133-.L45
	.half	1
	.byte	108
	.word	0,0
.L136:
	.word	-1,.L45,0,.L159-.L45
	.half	1
	.byte	84
	.word	.L163-.L45,.L14-.L45
	.half	1
	.byte	88
	.word	.L19-.L45,.L23-.L45
	.half	1
	.byte	88
	.word	0,0
.L138:
	.word	-1,.L45,.L161-.L45,.L133-.L45
	.half	1
	.byte	92
	.word	.L168-.L45,.L16-.L45
	.half	1
	.byte	82
	.word	0,0
.L139:
	.word	-1,.L45,.L162-.L45,.L133-.L45
	.half	1
	.byte	90
	.word	0,0
.L137:
	.word	-1,.L45,0,.L159-.L45
	.half	1
	.byte	85
	.word	.L20-.L45,.L164-.L45
	.half	1
	.byte	89
	.word	.L165-.L45,.L166-.L45
	.half	1
	.byte	89
	.word	.L24-.L45,.L167-.L45
	.half	1
	.byte	89
	.word	0,0
.L44:
	.word	-1,.L45,0,.L133-.L45
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L43,0,.L156-.L43
	.half	1
	.byte	100
	.word	0,0
.L132:
	.word	-1,.L43,0,.L157-.L43
	.half	1
	.byte	84
	.word	.L158-.L43,.L156-.L43
	.half	1
	.byte	85
	.word	0,0
.L42:
	.word	-1,.L43,0,.L129-.L43
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L37,0,.L122-.L37
	.half	2
	.byte	145,120
	.word	0,0
.L126:
	.word	-1,.L37,.L154-.L37,.L155-.L37
	.half	1
	.byte	81
	.word	0,0
.L36:
	.word	-1,.L37,0,.L153-.L37
	.half	2
	.byte	138,0
	.word	.L153-.L37,.L122-.L37
	.half	2
	.byte	138,8
	.word	.L122-.L37,.L122-.L37
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L328:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_uart_rx_handler')
	.sect	'.debug_frame'
	.word	36
	.word	.L328,.L37,.L122-.L37
	.byte	4
	.word	(.L153-.L37)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L122-.L153)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L328,.L39,.L127-.L39
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_is_ready')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L41,.L128-.L41
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_send_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L328,.L43,.L129-.L43
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_receive_data')
	.sect	'.debug_frame'
	.word	12
	.word	.L328,.L45,.L133-.L45
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_get_rx_count')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L47,.L140-.L47
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_clear_rx_buffer')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L49,.L141-.L49
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_check_rx_overflow')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L51,.L142-.L51
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_get_status')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L53,.L145-.L53
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('u1_adapter_debug_output')
	.sect	'.debug_frame'
	.word	24
	.word	.L328,.L55,.L146-.L55
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	; Module end
