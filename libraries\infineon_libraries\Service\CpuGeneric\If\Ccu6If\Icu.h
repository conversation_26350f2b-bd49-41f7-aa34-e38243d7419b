/**
 * \file Icu.h
 * \brief ICU interface
 *
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 * $Date: 2014-02-27 20:08:18 GMT$
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup library_srvsw_if_icu ICU interface
 * \ingroup library_srvsw_if
 *
 */

#ifndef ICU_H_
#define ICU_H_ 1

#include "Cpu/Std/Ifx_Types.h"

typedef struct Icu_s Icu;
typedef void       (*Icu_StartCapture)(Icu *handle);
typedef void       (*Icu_StopCapture)(Icu *handle);
typedef void       (*Icu_GetTimeStamp)(Icu *handle);

typedef struct
{
    Icu_StartCapture startCapture;
    Icu_StopCapture  stopCapture;
    Icu_GetTimeStamp getTimeStamp;
} Icu_Functions;

/** \brief Structure of the Icu interface */
struct Icu_s
{
    float32       frequency;      /**< \brief dummy member to avoid error */
#if IFX_CFG_USE_STANDARD_INTERFACE
    Icu_Functions functions;      /**< \brief Actual timer period */
#endif
};

/** \brief Configuration structure of the Icu interface */
typedef struct
{
    float32 frequency;             /**< \brief dummy member to avoid error */
//	  IfxCcu6_Timer	*timer;
} Icu_Config;

#if IFX_CFG_USE_STANDARD_INTERFACE
IFX_INLINE void Icu_startCapture(Icu *handle) {handle->functions.startCapture(handle); }
IFX_INLINE void Icu_stopCapture(Icu *handle)  {handle->functions.stopCapture(handle); }
IFX_INLINE void Icu_getTimeStamp(Icu *handle) {handle->functions.getTimeStamp(handle); }
#endif

#endif /* ICU_H_ */
