	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc42132a --dep-file=IfxCpu_CStart1.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c'

	
$TC16X
	
		 .extern _SMALL_DATA_, _LITERAL_DATA_, _A8_DATA_, _A9_DATA_
		 .extern __USTACK1
		 .extern core1_main
	.sdecl	'.text.IfxCpu_CStart1._Core1_start',code,cluster('_Core1_start')
	.sect	'.text.IfxCpu_CStart1._Core1_start'
	.align	2
	
	.global	_Core1_start
; Function _Core1_start
.L36:
_Core1_start:	.type	func
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003610c)
.L52:
	ld.w	d15,[a15]
	extr.u	d15,d15,#2,#14
.L131:
	xor	d1,d15,#63
.L132:
	j	.L2
.L2:
	 movh.a	 sp,#@his(__USTACK1)
 lea	 sp,[sp]@los(__USTACK1)
.L164:
	dsync
.L165:
	mov	d15,#2432
.L166:
	mtcr	#65028,d15
	isync
.L167:
	mfcr	d15,#65024
.L134:
	insert	d15,d15,#0,#0,#20
.L168:
	mtcr	#65024,d15
	isync
.L169:
	mov	d2,#1
.L58:
	jeq	d2,#0,.L3
.L64:
	mov	d15,#0
.L135:
	or	d15,#1
.L170:
	mtcr	#37380,d15
	isync
.L3:
	mfcr	d15,#65052
.L136:
	and	d3,d15,#7
.L171:
	j	.L4
.L4:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L172:
	mul	d15,d3,#12
.L137:
	addsc.a	a15,a15,d15,#0
.L74:
	ld.w	d0,[a15]
	extr.u	d0,d0,#2,#14
.L138:
	xor	d4,d0,#63
.L139:
	j	.L5
.L5:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L173:
	mul	d15,d3,#12
	addsc.a	a15,a15,d15,#0
.L77:
	ld.w	d15,[a15]
.L174:
	jz.t	d15:1,.L6
.L175:
	sha	d15,d4,#2
	or	d15,#1
.L176:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L177:
	sha	d0,d0,#16
.L178:
	or	d0,d15
.L179:
	st.w	[a15],d0
.L6:
	sha	d15,d4,#2
	or	d15,#2
.L180:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L181:
	sha	d0,d0,#16
.L182:
	or	d15,d0
.L183:
	st.w	[a15],d15
.L184:
	j	.L7
.L8:
.L7:
	ld.w	d15,[a15]
	and	d15,#1
.L185:
	jeq	d15,#1,.L8
.L78:
	mov	d15,#0
.L141:
	jeq	d2,#0,.L9
.L186:
	mov	d0,#0
.L187:
	j	.L10
.L9:
	mov	d0,#1
.L10:
	insert	d15,d15,d0,#1,#1
.L188:
	mtcr	#37388,d15
	isync
.L189:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L190:
	mul	d15,d3,#12
.L142:
	addsc.a	a15,a15,d15,#0
.L89:
	ld.w	d15,[a15]
.L191:
	jz.t	d15:1,.L11
.L192:
	sha	d15,d4,#2
	or	d15,#1
.L193:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L194:
	sha	d0,d0,#16
.L195:
	or	d15,d0
.L196:
	st.w	[a15],d15
.L11:
	sha	d4,#2
.L140:
	or	d15,d4,#3
.L197:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L198:
	sha	d0,d0,#16
.L199:
	or	d15,d0
.L200:
	st.w	[a15],d15
.L201:
	j	.L12
.L13:
.L12:
	ld.w	d15,[a15]
.L202:
	jz.t	d15:0,.L13
.L85:
	isync
.L59:
	mov	d2,#1
.L96:
	mfcr	d15,#65052
.L143:
	and	d3,d15,#7
.L203:
	j	.L14
.L14:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L204:
	mul	d15,d3,#12
.L144:
	addsc.a	a15,a15,d15,#0
.L104:
	ld.w	d0,[a15]
	extr.u	d0,d0,#2,#14
.L145:
	xor	d4,d0,#63
.L146:
	j	.L15
.L15:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L205:
	mul	d15,d3,#12
	addsc.a	a15,a15,d15,#0
.L106:
	ld.w	d15,[a15]
.L206:
	jz.t	d15:1,.L16
.L207:
	sha	d15,d4,#2
	or	d15,#1
.L208:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L209:
	sha	d0,d0,#16
.L210:
	or	d0,d15
.L211:
	st.w	[a15],d0
.L16:
	sha	d15,d4,#2
	or	d15,#2
.L212:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L213:
	sha	d0,d0,#16
.L214:
	or	d15,d0
.L215:
	st.w	[a15],d15
.L216:
	j	.L17
.L18:
.L17:
	ld.w	d15,[a15]
	and	d15,#1
.L217:
	jeq	d15,#1,.L18
.L107:
	mov	d15,#0
.L148:
	jeq	d2,#0,.L19
.L218:
	mov	d0,#0
.L219:
	j	.L20
.L19:
	mov	d0,#1
.L20:
	insert	d15,d15,d0,#1,#1
.L220:
	mtcr	#36928,d15
	isync
.L221:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L222:
	mul	d15,d3,#12
.L149:
	addsc.a	a15,a15,d15,#0
.L112:
	ld.w	d15,[a15]
.L223:
	jz.t	d15:1,.L21
.L224:
	sha	d15,d4,#2
	or	d15,#1
.L225:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L226:
	sha	d0,d0,#16
.L227:
	or	d15,d0
.L228:
	st.w	[a15],d15
.L21:
	sha	d4,#2
.L147:
	or	d15,d4,#3
.L229:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L230:
	sha	d0,d0,#16
.L231:
	or	d15,d0
.L232:
	st.w	[a15],d15
.L233:
	j	.L22
.L23:
.L22:
	ld.w	d15,[a15]
.L234:
	jz.t	d15:0,.L23
.L109:
	isync
.L97:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003610c)
.L113:
	ld.w	d15,[a15]
.L235:
	jz.t	d15:1,.L24
.L236:
	sha	d15,d1,#2
	or	d15,#1
.L237:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L238:
	sha	d0,d0,#16
.L239:
	or	d15,d0
.L240:
	st.w	[a15],d15
.L24:
	sha	d15,d1,#2
	or	d15,#2
.L241:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L242:
	sha	d0,d0,#16
.L243:
	or	d15,d0
.L244:
	st.w	[a15],d15
.L245:
	j	.L25
.L26:
.L25:
	ld.w	d15,[a15]
	and	d15,#1
.L246:
	jeq	d15,#1,.L26
.L114:
	movh.a	a15,#@his(__TRAPTAB_CPU1)
	lea	a15,[a15]@los(__TRAPTAB_CPU1)
	mov.d	d15,a15
.L247:
	mtcr	#65060,d15
	isync
.L248:
	movh.a	a15,#@his(__INTTAB_CPU1)
	lea	a15,[a15]@los(__INTTAB_CPU1)
	mov.d	d15,a15
.L249:
	mtcr	#65056,d15
	isync
.L250:
	movh.a	a15,#@his(__ISTACK1)
	lea	a15,[a15]@los(__ISTACK1)
	mov.d	d15,a15
.L251:
	mtcr	#65064,d15
	isync
.L252:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf003610c)
.L115:
	ld.w	d15,[a15]
.L253:
	jz.t	d15:1,.L27
.L254:
	sha	d15,d1,#2
	or	d15,#1
.L255:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L256:
	sha	d0,d0,#16
.L257:
	or	d15,d0
.L258:
	st.w	[a15],d15
.L27:
	sha	d1,#2
.L133:
	or	d15,d1,#3
.L259:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L260:
	sha	d0,d0,#16
.L261:
	or	d15,d0
.L262:
	st.w	[a15],d15
.L263:
	j	.L28
.L29:
.L28:
	ld.w	d15,[a15]
.L264:
	jz.t	d15:0,.L29
.L116:
	 movh.a	 a0,#@his(_SMALL_DATA_)
 lea	 a0,[a0]@los(_SMALL_DATA_)
.L265:
	 movh.a	 a1,#@his(_LITERAL_DATA_)
 lea	 a1,[a1]@los(_LITERAL_DATA_)
.L266:
	 movh.a	 a8,#@his(_A8_DATA_)
 lea	 a8,[a8]@los(_A8_DATA_)
.L267:
	 movh.a	 a9,#@his(_A9_DATA_)
 lea	 a9,[a9]@los(_A9_DATA_)
.L268:
	movh.a	a15,#@his(__CSA1)
.L150:
	lea	a15,[a15]@los(__CSA1)
.L269:
	movh.a	a2,#@his(__CSA1_END)
	lea	a2,[a2]@los(__CSA1_END)
.L118:
	mov.a	a4,#0
.L152:
	mov.d	d0,a2
.L270:
	mov.d	d15,a15
.L271:
	sub	d0,d15
.L272:
	mov	d15,#64
.L273:
	div.u	e4,d0,d15
.L153:
	mov	d0,#0
.L154:
	j	.L30
.L31:
	mov.d	d15,a15
.L274:
	insert	d15,d15,#0,#0,#28
.L275:
	sh	d1,d15,#-12
.L276:
	mov.d	d15,a15
.L277:
	mov.u	d2,#65535
	sh	d2,#6
.L278:
	and	d15,d2
.L279:
	sh	d15,#-6
.L155:
	or	d1,d15
.L280:
	jne	d0,#0,.L32
.L281:
	mtcr	#65080,d1
	isync
.L282:
	j	.L33
.L32:
	st.w	[a4],d1
.L33:
	add	d15,d4,#-3
.L283:
	jne	d15,d0,.L34
.L284:
	mtcr	#65084,d1
	isync
.L34:
	mov.aa	a4,a15
.L285:
	lea	a15,[a15]64
.L286:
	add	d0,#1
.L30:
	jlt.u	d0,d4,.L31
.L287:
	mov	d15,#0
.L288:
	st.w	[a4],d15
.L119:
	movh.a	a15,#@his(core1_main)
.L151:
	lea	a15,[a15]@los(core1_main)
		ji a15
.L289:
	ret
.L46:
	
___Core1_start_function_end:
	.size	_Core1_start,___Core1_start_function_end-_Core1_start
.L45:
	; End of function
	
	.calls	'__INDIRECT__','core1_main'
	.extern	__ISTACK1
	.extern	__INTTAB_CPU1
	.extern	__TRAPTAB_CPU1
	.extern	__CSA1
	.extern	__CSA1_END
	.extern	core1_main
	.extern	__INDIRECT__
	.calls	'_Core1_start','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L38:
	.word	77962
	.half	3
	.word	.L39
	.byte	4
.L37:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L40
	.byte	2,1,1,3
	.word	241
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	244
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	289
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	301
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	413
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	387
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	419
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	419
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	387
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	528
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	528
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	544
	.byte	4,2,35,0,0,7
	.byte	'unsigned char',0,1,8
.L49:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	719
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	963
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	640
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	923
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1154
	.byte	4,2,35,8,0,14
	.word	1194
	.byte	3
	.word	1257
.L76:
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1
.L79:
	.byte	5
	.byte	'watchdog',0,3,181,3,65
	.word	1262
.L81:
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	697
.L83:
	.byte	6,0
.L88:
	.byte	4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1
.L90:
	.byte	5
	.byte	'watchdog',0,3,140,4,63
	.word	1262
.L92:
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	697
.L94:
	.byte	6,0
.L51:
	.byte	8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	697
	.byte	1,1
.L53:
	.byte	5
	.byte	'watchdog',0,3,227,3,74
	.word	1262
.L55:
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1492
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1808
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2379
	.byte	4,2,35,0,0,15,4
	.word	680
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2507
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2722
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2937
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	680
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3154
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3374
	.byte	4,2,35,0,0,15,24
	.word	680
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3697
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	680
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4001
	.byte	4,2,35,0,0,15,8
	.word	680
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4326
	.byte	4,2,35,0,0,15,12
	.word	680
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4666
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	505
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5032
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5318
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5465
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	505
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5634
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5806
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	697
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5981
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6155
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6329
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6661
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6994
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7342
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7466
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7550
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7730
	.byte	4,2,35,0,0,15,76
	.word	680
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7983
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8070
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1768
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2339
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2458
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2498
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2682
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2897
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3114
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3334
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2498
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3648
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3688
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3961
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4277
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4317
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4617
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4657
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4992
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5278
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4317
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5425
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5594
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5766
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5941
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6115
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6289
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6465
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6621
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6954
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7302
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4317
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7426
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7675
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7934
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7974
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8030
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8597
	.byte	4,3,35,252,1,0,14
	.word	8637
	.byte	3
	.word	9240
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9245
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	680
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9250
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0
.L70:
	.byte	8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9431
	.byte	1,1
.L71:
	.byte	6,0
.L47:
	.byte	7
	.byte	'unsigned long int',0,4,7
.L127:
	.byte	3
	.word	9544
.L117:
	.byte	4
	.byte	'IfxCpu_initCSA',0,3,7,219,6,17,1,1
.L120:
	.byte	5
	.byte	'csaBegin',0,7,219,6,40
	.word	9565
.L122:
	.byte	5
	.byte	'csaEnd',0,7,219,6,58
	.word	9565
.L124:
	.byte	6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	680
	.byte	1,1,6,0
.L95:
	.byte	4
	.byte	'IfxCpu_setDataCache',0,3,7,177,7,17,1,1
.L98:
	.byte	5
	.byte	'enable',0,7,177,7,45
	.word	680
.L100:
	.byte	19
.L108:
	.byte	6,6,6,6,6,0,0
.L57:
	.byte	4
	.byte	'IfxCpu_setProgramCache',0,3,7,204,7,17,1,1
.L60:
	.byte	5
	.byte	'enable',0,7,204,7,48
	.word	680
.L62:
	.byte	19
.L63:
	.byte	6
.L67:
	.byte	19
.L84:
	.byte	6,0,6,6,6,6,0,0,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9544
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	697
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	680
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	697
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9544
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9544
	.byte	19,6,0,0,20
	.byte	'__mtcr',0,1,1,1,1,21
	.word	521
	.byte	21
	.word	521
	.byte	0,14
	.word	521
	.byte	22
	.byte	'__mfcr',0
	.word	10014
	.byte	1,1,1,1,21
	.word	521
	.byte	0,23
	.byte	'__isync',0,1,1,1,1,23
	.byte	'__dsync',0,1,1,1,1,24
	.word	249
	.byte	25
	.word	275
	.byte	6,0,24
	.word	310
	.byte	25
	.word	342
	.byte	6,0,24
	.word	355
	.byte	6,0,24
	.word	424
	.byte	25
	.word	443
	.byte	6,0,24
	.word	459
	.byte	25
	.word	474
	.byte	25
	.word	488
	.byte	6,0,24
	.word	1267
	.byte	25
	.word	1307
	.byte	25
	.word	1325
	.byte	6,0,24
	.word	1345
	.byte	25
	.word	1383
	.byte	25
	.word	1401
	.byte	6,0,24
	.word	1421
	.byte	25
	.word	1472
	.byte	6,0,24
	.word	9353
	.byte	25
	.word	9381
	.byte	25
	.word	9395
	.byte	25
	.word	9413
	.byte	6,0,24
	.word	9510
	.byte	6,0,24
	.word	9570
	.byte	25
	.word	9593
	.byte	25
	.word	9611
	.byte	6,0,24
	.word	9629
	.byte	6,0,24
	.word	9671
	.byte	25
	.word	9699
	.byte	19,19,26
	.word	1345
	.byte	25
	.word	1383
	.byte	25
	.word	1401
	.byte	27
	.word	1419
	.byte	0,0,26
	.word	9510
	.byte	27
	.word	9542
	.byte	0,6,26
	.word	1421
	.byte	25
	.word	1472
	.byte	27
	.word	1490
	.byte	0,6,26
	.word	1267
	.byte	25
	.word	1307
	.byte	25
	.word	1325
	.byte	27
	.word	1343
	.byte	0,6,6,0,0,24
	.word	9723
	.byte	25
	.word	9754
	.byte	19,6,19,19,26
	.word	1345
	.byte	25
	.word	1383
	.byte	25
	.word	1401
	.byte	27
	.word	1419
	.byte	0,0,26
	.word	9510
	.byte	27
	.word	9542
	.byte	0,26
	.word	1421
	.byte	25
	.word	1472
	.byte	27
	.word	1490
	.byte	0,26
	.word	1267
	.byte	25
	.word	1307
	.byte	25
	.word	1325
	.byte	27
	.word	1343
	.byte	0,0,6,6,6,6,0,0,24
	.word	9781
	.byte	25
	.word	9822
	.byte	6,0,24
	.word	9841
	.byte	25
	.word	9896
	.byte	6,0,24
	.word	9915
	.byte	25
	.word	9955
	.byte	25
	.word	9972
	.byte	19,6,0,0,10
	.byte	'_Ifx_CPU_PCON1_Bits',0,9,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L65:
	.byte	12,9,159,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10448
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,9,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	29,0,2,35,0,0
.L72:
	.byte	12,9,223,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_PCON0_Bits',0,9,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L86:
	.byte	12,9,151,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10680
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_CPU_DCON0_Bits',0,9,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	30,0,2,35,0,0
.L110:
	.byte	12,9,183,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10808
	.byte	4,2,35,0,0,28
	.byte	'core1_main',0,10,74,17,1,1,1,1,29
	.byte	'__INDIRECT__',0,10,1,1,1,1,1,7
	.byte	'short int',0,2,5,30
	.byte	'__wchar_t',0,10,1,1
	.word	10975
	.byte	30
	.byte	'__size_t',0,10,1,1
	.word	505
	.byte	30
	.byte	'__ptrdiff_t',0,10,1,1
	.word	521
	.byte	31,1,3
	.word	11043
	.byte	30
	.byte	'__codeptr',0,10,1,1
	.word	11045
	.byte	17,11,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,30
	.byte	'IfxScu_CCUCON0_CLKSEL',0,11,240,10,3
	.word	11068
	.byte	17,11,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,30
	.byte	'IfxScu_WDTCON1_IR',0,11,255,10,3
	.word	11165
	.byte	30
	.byte	'boolean',0,12,101,29
	.word	680
	.byte	30
	.byte	'uint8',0,12,105,29
	.word	680
	.byte	30
	.byte	'uint16',0,12,109,29
	.word	697
	.byte	30
	.byte	'uint32',0,12,113,29
	.word	9544
	.byte	30
	.byte	'uint64',0,12,118,29
	.word	387
	.byte	30
	.byte	'sint16',0,12,126,29
	.word	10975
	.byte	7
	.byte	'long int',0,4,5,30
	.byte	'sint32',0,12,131,1,29
	.word	11377
	.byte	7
	.byte	'long long int',0,8,5,30
	.byte	'sint64',0,12,138,1,29
	.word	11405
	.byte	30
	.byte	'float32',0,12,167,1,29
	.word	301
	.byte	30
	.byte	'pvoid',0,13,57,28
	.word	419
	.byte	30
	.byte	'Ifx_TickTime',0,13,79,28
	.word	11405
	.byte	17,13,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,30
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	11490
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	11628
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	12185
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	505
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	12262
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	680
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	12398
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	680
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	12678
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	12916
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	680
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	13044
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	680
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	13287
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	13522
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	13650
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	13750
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	680
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	13850
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	505
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	14058
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	697
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	14223
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	14406
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	505
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	680
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	14560
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	14924
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	697
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	680
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	15135
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	505
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	15387
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	15505
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	15616
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	15779
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	15942
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	16100
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	680
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	680
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	10,0,2,35,2,0,30
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	16265
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	680
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	680
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	697
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	16594
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	16815
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	16978
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	17250
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	17403
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	17559
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	17721
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	17864
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	18029
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	18174
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	18355
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	18529
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	18689
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	18833
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	19107
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	19246
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	680
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	697
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	680
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	680
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	19409
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	697
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	680
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	697
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	19627
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	19790
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	20126
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	680
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	20233
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	20685
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	20784
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	697
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	20934
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	505
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	21083
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	21244
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	697
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	21374
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	21506
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	697
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	21621
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	697
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	21732
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	680
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	680
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	680
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	21890
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	22302
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	697
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	6,0,2,35,3,0,30
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	22403
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	505
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	22670
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	22806
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	680
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	22917
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	23050
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	23253
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	680
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	680
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	23609
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	697
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	23787
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	680
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	23887
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	680
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	680
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	680
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	24257
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	24443
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	24641
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	505
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	24874
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	680
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	680
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	25026
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	680
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	680
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	680
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	25593
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	680
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	680
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	25887
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	680
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	680
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	680
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	26165
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	26661
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	697
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	26974
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	680
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	680
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	680
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	27183
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	680
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	27394
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	27826
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	680
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	680
	.byte	7,0,2,35,3,0,30
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	27922
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	28182
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	505
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	28307
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	28504
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	28657
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	28810
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	28963
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	544
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	719
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	963
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	528
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	29218
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	29344
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	29596
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11628
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	29815
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12185
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	29879
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12262
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	29943
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12398
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	30008
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12678
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	30073
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12916
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	30138
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13044
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	30203
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13287
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	30268
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13522
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	30333
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13650
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	30398
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13750
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	30463
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13850
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	30528
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14058
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	30592
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14223
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	30656
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14406
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	30720
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14560
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	30785
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14924
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	30847
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15135
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	30909
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15387
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	30971
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15505
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	31035
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15616
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	31100
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15779
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	31166
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15942
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	31232
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16100
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	31300
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16265
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	31367
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16594
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	31435
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16815
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	31503
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16978
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	31569
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17250
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	31636
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17403
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	31705
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17559
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	31774
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17721
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	31843
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17864
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	31912
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18029
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	31981
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18174
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	32050
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18355
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	32118
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18529
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	32186
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18689
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	32254
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18833
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	32322
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19107
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	32387
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19246
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	32452
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19409
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	32518
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19627
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	32582
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19790
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	32643
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20126
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	32704
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20233
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	32764
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20685
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	32826
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20784
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	32886
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20934
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	32948
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21083
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	33016
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21244
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	33084
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21374
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	33152
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21506
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	33216
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21621
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	33281
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21732
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	33344
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21890
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	33405
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22302
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	33469
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22403
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	33530
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22670
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	33594
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22806
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	33661
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22917
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	33724
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23050
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	33785
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23253
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	33847
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23609
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	33912
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23787
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	33977
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23887
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	34042
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24257
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	34111
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24443
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	34180
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24641
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	34249
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24874
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	34314
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25026
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	34377
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25593
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	34442
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25887
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	34507
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26165
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	34572
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26661
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	34638
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27183
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	34707
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26974
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	34771
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27394
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	34836
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27826
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	34901
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27922
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	34966
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28182
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	35030
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28307
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	35096
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28504
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	35160
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28657
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	35225
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28810
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	35290
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28963
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	35355
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	640
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	923
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1154
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29218
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	35506
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29344
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	35573
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29596
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	35640
	.byte	14
	.word	1194
	.byte	30
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	35705
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	35506
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	35573
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	35640
	.byte	4,2,35,8,0,14
	.word	35734
	.byte	30
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	35795
	.byte	15,8
	.word	30971
	.byte	16,1,0,15,20
	.word	680
	.byte	16,19,0,15,8
	.word	34314
	.byte	16,1,0,14
	.word	35734
	.byte	15,24
	.word	1194
	.byte	16,1,0,14
	.word	35854
	.byte	15,16
	.word	680
	.byte	16,15,0,15,28
	.word	680
	.byte	16,27,0,15,40
	.word	680
	.byte	16,39,0,15,16
	.word	30785
	.byte	16,3,0,15,16
	.word	32764
	.byte	16,3,0,15,180,3
	.word	680
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4317
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	32704
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2498
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	33405
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	34249
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	33847
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	33912
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	33977
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	34180
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	34042
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	34111
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	30008
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	30073
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	32582
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	32518
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	30138
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	30203
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	30268
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	30333
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	34836
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2498
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	34707
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	29943
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	35030
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	34771
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2498
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	31569
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	35822
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	31035
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	35096
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	30398
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	30463
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	35831
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	33724
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	32886
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	33469
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	33344
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	32826
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	32322
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	31300
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	31100
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	31166
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	34966
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2498
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	34377
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	34572
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	34638
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	35840
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2498
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	30720
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	30592
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	34442
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	34507
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	35849
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	30909
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	35863
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4657
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	35355
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	35290
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	35160
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	35225
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2498
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	33152
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	33216
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	30528
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	33281
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4317
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	34901
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	35868
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	32948
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	33016
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	33084
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	35877
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	33661
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4317
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	32387
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	31232
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	32452
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	31503
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	31367
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2498
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	32050
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	32118
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	32186
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	32254
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	31636
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	31705
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	31774
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	31843
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	31912
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	31981
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	31435
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2498
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	33594
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	33530
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	35886
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	35895
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	30847
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	32643
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	33785
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	35904
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2498
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	30656
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	35913
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	29879
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	29815
	.byte	4,3,35,252,7,0,14
	.word	35924
	.byte	30
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	37914
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,9,45,16,4,11
	.byte	'ADDR',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_A_Bits',0,9,48,3
	.word	37936
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,9,51,16,4,11
	.byte	'VSS',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	528
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BIV_Bits',0,9,55,3
	.word	37997
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,9,58,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	528
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BTV_Bits',0,9,62,3
	.word	38076
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,9,65,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT_Bits',0,9,69,3
	.word	38162
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,9,72,16,4,11
	.byte	'CM',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	528
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	528
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	528
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL_Bits',0,9,80,3
	.word	38251
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,9,83,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT_Bits',0,9,89,3
	.word	38397
	.byte	30
	.byte	'Ifx_CPU_CORE_ID_Bits',0,9,96,3
	.word	10571
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,9,99,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L_Bits',0,9,103,3
	.word	38553
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,9,106,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U_Bits',0,9,110,3
	.word	38646
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,9,113,16,4,11
	.byte	'MODREV',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	528
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID_Bits',0,9,118,3
	.word	38739
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,9,121,16,4,11
	.byte	'XE',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE_Bits',0,9,125,3
	.word	38846
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,9,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT_Bits',0,9,136,1,3
	.word	38933
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,9,139,1,16,4,11
	.byte	'CID',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID_Bits',0,9,143,1,3
	.word	39087
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,9,146,1,16,4,11
	.byte	'DATA',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_D_Bits',0,9,149,1,3
	.word	39181
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,9,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	528
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	528
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DATR_Bits',0,9,163,1,3
	.word	39244
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,9,166,1,16,4,11
	.byte	'DE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	528
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	528
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	528
	.byte	19,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR_Bits',0,9,177,1,3
	.word	39462
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,9,180,1,16,4,11
	.byte	'DTA',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR_Bits',0,9,184,1,3
	.word	39677
	.byte	30
	.byte	'Ifx_CPU_DCON0_Bits',0,9,192,1,3
	.word	10808
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,9,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2_Bits',0,9,199,1,3
	.word	39799
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,9,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	528
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCX_Bits',0,9,206,1,3
	.word	39900
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,9,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD_Bits',0,9,212,1,3
	.word	39993
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,9,215,1,16,4,11
	.byte	'TA',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR_Bits',0,9,218,1,3
	.word	40073
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,9,221,1,16,4,11
	.byte	'IED',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	528
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	528
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR_Bits',0,9,233,1,3
	.word	40142
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,9,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	528
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DMS_Bits',0,9,240,1,3
	.word	40371
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,9,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L_Bits',0,9,247,1,3
	.word	40464
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,9,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	528
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U_Bits',0,9,254,1,3
	.word	40559
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,9,129,2,16,4,11
	.byte	'RE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE_Bits',0,9,133,2,3
	.word	40654
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,9,136,2,16,4,11
	.byte	'WE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE_Bits',0,9,140,2,3
	.word	40744
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,9,143,2,16,4,11
	.byte	'SRE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	528
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	528
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	528
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	528
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	528
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	528
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	528
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	528
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR_Bits',0,9,161,2,3
	.word	40834
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,9,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT_Bits',0,9,172,2,3
	.word	41158
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,9,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FCX_Bits',0,9,180,2,3
	.word	41312
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,9,183,2,16,4,11
	.byte	'TST',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	528
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	528
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	528
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	528
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	528
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	528
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	528
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	528
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	528
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	528
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,9,202,2,3
	.word	41418
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,9,205,2,16,4,11
	.byte	'OPC',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	528
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,9,212,2,3
	.word	41767
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,9,215,2,16,4,11
	.byte	'PC',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,9,218,2,3
	.word	41927
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,9,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,9,224,2,3
	.word	42008
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,9,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,9,230,2,3
	.word	42095
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,9,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,9,236,2,3
	.word	42182
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,9,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT_Bits',0,9,243,2,3
	.word	42269
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,9,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	528
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	528
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	528
	.byte	6,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICR_Bits',0,9,253,2,3
	.word	42360
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,9,128,3,16,4,11
	.byte	'ISP',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_ISP_Bits',0,9,131,3,3
	.word	42503
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,9,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	528
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_LCX_Bits',0,9,139,3,3
	.word	42569
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,9,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT_Bits',0,9,146,3,3
	.word	42675
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,9,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT_Bits',0,9,153,3,3
	.word	42768
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,9,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	528
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT_Bits',0,9,160,3,3
	.word	42861
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,9,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	528
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_PC_Bits',0,9,167,3,3
	.word	42954
	.byte	30
	.byte	'Ifx_CPU_PCON0_Bits',0,9,175,3,3
	.word	10680
	.byte	30
	.byte	'Ifx_CPU_PCON1_Bits',0,9,183,3,3
	.word	10448
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,9,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2_Bits',0,9,190,3,3
	.word	43095
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,9,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	528
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	528
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	528
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	528
	.byte	10,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI_Bits',0,9,200,3,3
	.word	43196
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,9,203,3,16,4,11
	.byte	'TA',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR_Bits',0,9,206,3,3
	.word	43326
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,9,209,3,16,4,11
	.byte	'IED',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	528
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	528
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR_Bits',0,9,221,3,3
	.word	43395
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,9,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	528
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0_Bits',0,9,229,3,3
	.word	43624
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,9,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	528
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	528
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1_Bits',0,9,237,3,3
	.word	43737
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,9,240,3,16,4,11
	.byte	'PSI',0,4
	.word	528
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	528
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2_Bits',0,9,244,3,3
	.word	43850
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,9,247,3,16,4,11
	.byte	'FRE',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	528
	.byte	17,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR_Bits',0,9,129,4,3
	.word	43941
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,9,132,4,16,4,11
	.byte	'CDC',0,4
	.word	528
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	528
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	528
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	528
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	528
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	528
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSW_Bits',0,9,147,4,3
	.word	44144
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,9,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	528
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	528
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	528
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	528
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN_Bits',0,9,156,4,3
	.word	44387
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,9,159,4,16,4,11
	.byte	'PC',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	528
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	528
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	528
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	528
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	528
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	528
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON_Bits',0,9,171,4,3
	.word	44515
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,9,174,4,16,4,11
	.byte	'EN',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,9,177,4,3
	.word	44756
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,9,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,9,183,4,3
	.word	44839
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,9,186,4,16,4,11
	.byte	'EN',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,9,189,4,3
	.word	44930
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,9,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,9,195,4,3
	.word	45021
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,9,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,9,202,4,3
	.word	45120
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,9,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,9,209,4,3
	.word	45227
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,9,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT_Bits',0,9,220,4,3
	.word	45334
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,9,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON_Bits',0,9,231,4,3
	.word	45488
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,9,234,4,16,4,11
	.byte	'ASI',0,4
	.word	528
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	528
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,9,238,4,3
	.word	45649
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,9,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	528
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	528
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	528
	.byte	15,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON_Bits',0,9,249,4,3
	.word	45747
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,9,252,4,16,4,11
	.byte	'Timer',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,9,255,4,3
	.word	45919
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,9,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	528
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR_Bits',0,9,133,5,3
	.word	45999
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,9,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	528
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	528
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	528
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	528
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	528
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	528
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	528
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	528
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	528
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	528
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	528
	.byte	3,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT_Bits',0,9,153,5,3
	.word	46072
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,9,156,5,16,4,11
	.byte	'T0',0,4
	.word	528
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	528
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	528
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	528
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	528
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	528
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	528
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	528
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	528
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,9,167,5,3
	.word	46390
	.byte	12,9,175,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37936
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_A',0,9,180,5,3
	.word	46585
	.byte	12,9,183,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37997
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BIV',0,9,188,5,3
	.word	46644
	.byte	12,9,191,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38076
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BTV',0,9,196,5,3
	.word	46705
	.byte	12,9,199,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38162
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT',0,9,204,5,3
	.word	46766
	.byte	12,9,207,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38251
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL',0,9,212,5,3
	.word	46828
	.byte	12,9,215,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38397
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT',0,9,220,5,3
	.word	46891
	.byte	30
	.byte	'Ifx_CPU_CORE_ID',0,9,228,5,3
	.word	10640
	.byte	12,9,231,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38553
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L',0,9,236,5,3
	.word	46980
	.byte	12,9,239,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38646
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U',0,9,244,5,3
	.word	47043
	.byte	12,9,247,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38739
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID',0,9,252,5,3
	.word	47106
	.byte	12,9,255,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38846
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE',0,9,132,6,3
	.word	47170
	.byte	12,9,135,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38933
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT',0,9,140,6,3
	.word	47232
	.byte	12,9,143,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39087
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID',0,9,148,6,3
	.word	47295
	.byte	12,9,151,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39181
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_D',0,9,156,6,3
	.word	47359
	.byte	12,9,159,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39244
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DATR',0,9,164,6,3
	.word	47418
	.byte	12,9,167,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39462
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR',0,9,172,6,3
	.word	47480
	.byte	12,9,175,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39677
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR',0,9,180,6,3
	.word	47543
	.byte	30
	.byte	'Ifx_CPU_DCON0',0,9,188,6,3
	.word	10896
	.byte	12,9,191,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39799
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2',0,9,196,6,3
	.word	47630
	.byte	12,9,199,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39900
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCX',0,9,204,6,3
	.word	47693
	.byte	12,9,207,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39993
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD',0,9,212,6,3
	.word	47754
	.byte	12,9,215,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40073
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR',0,9,220,6,3
	.word	47817
	.byte	12,9,223,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40142
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR',0,9,228,6,3
	.word	47880
	.byte	12,9,231,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40371
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DMS',0,9,236,6,3
	.word	47943
	.byte	12,9,239,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40464
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L',0,9,244,6,3
	.word	48004
	.byte	12,9,247,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40559
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U',0,9,252,6,3
	.word	48067
	.byte	12,9,255,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40654
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE',0,9,132,7,3
	.word	48130
	.byte	12,9,135,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40744
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE',0,9,140,7,3
	.word	48192
	.byte	12,9,143,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40834
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR',0,9,148,7,3
	.word	48254
	.byte	12,9,151,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41158
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT',0,9,156,7,3
	.word	48316
	.byte	12,9,159,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41312
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FCX',0,9,164,7,3
	.word	48379
	.byte	12,9,167,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41418
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,9,172,7,3
	.word	48440
	.byte	12,9,175,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41767
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,9,180,7,3
	.word	48510
	.byte	12,9,183,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41927
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,9,188,7,3
	.word	48580
	.byte	12,9,191,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42008
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,9,196,7,3
	.word	48649
	.byte	12,9,199,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42095
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,9,204,7,3
	.word	48720
	.byte	12,9,207,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42182
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,9,212,7,3
	.word	48791
	.byte	12,9,215,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42269
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT',0,9,220,7,3
	.word	48862
	.byte	12,9,223,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42360
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICR',0,9,228,7,3
	.word	48924
	.byte	12,9,231,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42503
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ISP',0,9,236,7,3
	.word	48985
	.byte	12,9,239,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42569
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_LCX',0,9,244,7,3
	.word	49046
	.byte	12,9,247,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42675
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT',0,9,252,7,3
	.word	49107
	.byte	12,9,255,7,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42768
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT',0,9,132,8,3
	.word	49170
	.byte	12,9,135,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42861
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT',0,9,140,8,3
	.word	49233
	.byte	12,9,143,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42954
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PC',0,9,148,8,3
	.word	49296
	.byte	30
	.byte	'Ifx_CPU_PCON0',0,9,156,8,3
	.word	10768
	.byte	30
	.byte	'Ifx_CPU_PCON1',0,9,164,8,3
	.word	10531
	.byte	12,9,167,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43095
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2',0,9,172,8,3
	.word	49402
	.byte	12,9,175,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43196
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI',0,9,180,8,3
	.word	49465
	.byte	12,9,183,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43326
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR',0,9,188,8,3
	.word	49527
	.byte	12,9,191,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43395
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR',0,9,196,8,3
	.word	49590
	.byte	12,9,199,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43624
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0',0,9,204,8,3
	.word	49653
	.byte	12,9,207,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43737
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1',0,9,212,8,3
	.word	49715
	.byte	12,9,215,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43850
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2',0,9,220,8,3
	.word	49777
	.byte	12,9,223,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43941
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR',0,9,228,8,3
	.word	49839
	.byte	12,9,231,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44144
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSW',0,9,236,8,3
	.word	49901
	.byte	12,9,239,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44387
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN',0,9,244,8,3
	.word	49962
	.byte	12,9,247,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44515
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON',0,9,252,8,3
	.word	50025
	.byte	12,9,255,8,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44756
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA',0,9,132,9,3
	.word	50089
	.byte	12,9,135,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44839
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB',0,9,140,9,3
	.word	50159
	.byte	12,9,143,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44930
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,9,148,9,3
	.word	50229
	.byte	12,9,151,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45021
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,9,156,9,3
	.word	50303
	.byte	12,9,159,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45120
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,9,164,9,3
	.word	50377
	.byte	12,9,167,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45227
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,9,172,9,3
	.word	50447
	.byte	12,9,175,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45334
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT',0,9,180,9,3
	.word	50517
	.byte	12,9,183,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45488
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON',0,9,188,9,3
	.word	50580
	.byte	12,9,191,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45649
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI',0,9,196,9,3
	.word	50644
	.byte	12,9,199,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45747
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON',0,9,204,9,3
	.word	50710
	.byte	12,9,207,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45919
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER',0,9,212,9,3
	.word	50775
	.byte	12,9,215,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45999
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR',0,9,220,9,3
	.word	50842
	.byte	12,9,223,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46072
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT',0,9,228,9,3
	.word	50906
	.byte	12,9,231,9,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46390
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC',0,9,236,9,3
	.word	50970
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,9,247,9,25,8,13
	.byte	'L',0
	.word	46980
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	47043
	.byte	4,2,35,4,0,14
	.word	51036
	.byte	30
	.byte	'Ifx_CPU_CPR',0,9,251,9,3
	.word	51078
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,9,254,9,25,8,13
	.byte	'L',0
	.word	48004
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	48067
	.byte	4,2,35,4,0,14
	.word	51104
	.byte	30
	.byte	'Ifx_CPU_DPR',0,9,130,10,3
	.word	51146
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,9,133,10,25,16,13
	.byte	'LA',0
	.word	50377
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	50447
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	50229
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	50303
	.byte	4,2,35,12,0,14
	.word	51172
	.byte	30
	.byte	'Ifx_CPU_SPROT_RGN',0,9,139,10,3
	.word	51254
	.byte	15,12
	.word	50775
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,9,142,10,25,16,13
	.byte	'CON',0
	.word	50710
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	51286
	.byte	12,2,35,4,0,14
	.word	51295
	.byte	30
	.byte	'Ifx_CPU_TPS',0,9,146,10,3
	.word	51343
	.byte	10
	.byte	'_Ifx_CPU_TR',0,9,149,10,25,8,13
	.byte	'EVT',0
	.word	50906
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	50842
	.byte	4,2,35,4,0,14
	.word	51369
	.byte	30
	.byte	'Ifx_CPU_TR',0,9,153,10,3
	.word	51414
	.byte	15,176,32
	.word	680
	.byte	16,175,32,0,15,208,223,1
	.word	680
	.byte	16,207,223,1,0,15,248,1
	.word	680
	.byte	16,247,1,0,15,244,29
	.word	680
	.byte	16,243,29,0,15,188,3
	.word	680
	.byte	16,187,3,0,15,232,3
	.word	680
	.byte	16,231,3,0,15,252,23
	.word	680
	.byte	16,251,23,0,15,228,63
	.word	680
	.byte	16,227,63,0,15,128,1
	.word	51104
	.byte	16,15,0,14
	.word	51529
	.byte	15,128,31
	.word	680
	.byte	16,255,30,0,15,64
	.word	51036
	.byte	16,7,0,14
	.word	51555
	.byte	15,192,31
	.word	680
	.byte	16,191,31,0,15,16
	.word	47170
	.byte	16,3,0,15,16
	.word	48130
	.byte	16,3,0,15,16
	.word	48192
	.byte	16,3,0,15,208,7
	.word	680
	.byte	16,207,7,0,14
	.word	51295
	.byte	15,240,23
	.word	680
	.byte	16,239,23,0,15,64
	.word	51369
	.byte	16,7,0,14
	.word	51634
	.byte	15,192,23
	.word	680
	.byte	16,191,23,0,15,232,1
	.word	680
	.byte	16,231,1,0,15,180,1
	.word	680
	.byte	16,179,1,0,15,172,1
	.word	680
	.byte	16,171,1,0,15,64
	.word	47359
	.byte	16,15,0,15,64
	.word	680
	.byte	16,63,0,15,64
	.word	46585
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,9,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	51439
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	49962
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	51450
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	50644
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	51463
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	49653
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	49715
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	49777
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	51474
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	47630
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4317
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	50025
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	48254
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2498
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	47418
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	47754
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	47817
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	47880
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3688
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	10896
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	51485
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	49839
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	10531
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	49402
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	10768
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	49527
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	49590
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	51496
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	46891
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	51507
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	48440
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	48580
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	48510
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2498
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	48649
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	48720
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	48791
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	51518
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	51539
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	51544
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	51564
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	51569
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	51580
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	51589
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	51598
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	51607
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	51618
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	51623
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	51643
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	51648
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	46828
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	46766
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	48862
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	49107
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	49170
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	49233
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	51659
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	47480
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2498
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	48316
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	47232
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	50517
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	35877
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	50970
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4657
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	47943
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	47693
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	47543
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	51670
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	49465
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	49901
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	49296
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4317
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	50580
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	47106
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	10640
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	46644
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	46705
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	48985
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	48924
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4317
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	48379
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	49046
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	35868
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	47295
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	51681
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	51692
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	51701
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	51710
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	51701
	.byte	64,4,35,192,255,3,0,14
	.word	51719
	.byte	30
	.byte	'Ifx_CPU',0,9,130,11,3
	.word	53510
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,30
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	53532
	.byte	30
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9431
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,14,45,16,4,11
	.byte	'SRPN',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	680
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	680
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SRC_SRCR_Bits',0,14,62,3
	.word	53630
	.byte	12,14,70,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53630
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SRC_SRCR',0,14,75,3
	.word	53946
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,14,86,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	54006
	.byte	30
	.byte	'Ifx_SRC_AGBT',0,14,89,3
	.word	54038
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,14,92,25,12,13
	.byte	'TX',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,8,0,14
	.word	54064
	.byte	30
	.byte	'Ifx_SRC_ASCLIN',0,14,97,3
	.word	54123
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,14,100,25,4,13
	.byte	'SBSRC',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	54151
	.byte	30
	.byte	'Ifx_SRC_BCUSPB',0,14,103,3
	.word	54188
	.byte	15,64
	.word	53946
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,14,106,25,64,13
	.byte	'INT',0
	.word	54216
	.byte	64,2,35,0,0,14
	.word	54225
	.byte	30
	.byte	'Ifx_SRC_CAN',0,14,109,3
	.word	54257
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,14,112,25,16,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	53946
	.byte	4,2,35,12,0,14
	.word	54282
	.byte	30
	.byte	'Ifx_SRC_CCU6',0,14,118,3
	.word	54354
	.byte	15,8
	.word	53946
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,14,121,25,8,13
	.byte	'SR',0
	.word	54380
	.byte	8,2,35,0,0,14
	.word	54389
	.byte	30
	.byte	'Ifx_SRC_CERBERUS',0,14,124,3
	.word	54425
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,14,127,25,16,13
	.byte	'MI',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	53946
	.byte	4,2,35,12,0,14
	.word	54455
	.byte	30
	.byte	'Ifx_SRC_CIF',0,14,133,1,3
	.word	54528
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,14,136,1,25,4,13
	.byte	'SBSRC',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	54554
	.byte	30
	.byte	'Ifx_SRC_CPU',0,14,139,1,3
	.word	54589
	.byte	15,192,1
	.word	53946
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,14,142,1,25,208,1,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4657
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	54615
	.byte	192,1,2,35,16,0,14
	.word	54625
	.byte	30
	.byte	'Ifx_SRC_DMA',0,14,147,1,3
	.word	54692
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,14,150,1,25,8,13
	.byte	'SRM',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	53946
	.byte	4,2,35,4,0,14
	.word	54718
	.byte	30
	.byte	'Ifx_SRC_DSADC',0,14,154,1,3
	.word	54766
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,14,157,1,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	54794
	.byte	30
	.byte	'Ifx_SRC_EMEM',0,14,160,1,3
	.word	54827
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,14,163,1,25,80,13
	.byte	'INT',0
	.word	54380
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	54380
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	54380
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	54380
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	53946
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	53946
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	35886
	.byte	40,2,35,40,0,14
	.word	54854
	.byte	30
	.byte	'Ifx_SRC_ERAY',0,14,172,1,3
	.word	54981
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,14,175,1,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	55008
	.byte	30
	.byte	'Ifx_SRC_ETH',0,14,178,1,3
	.word	55040
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,14,181,1,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	55066
	.byte	30
	.byte	'Ifx_SRC_FCE',0,14,184,1,3
	.word	55098
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,14,187,1,25,12,13
	.byte	'DONE',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	53946
	.byte	4,2,35,8,0,14
	.word	55124
	.byte	30
	.byte	'Ifx_SRC_FFT',0,14,192,1,3
	.word	55184
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,14,195,1,25,32,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	53946
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	35868
	.byte	16,2,35,16,0,14
	.word	55210
	.byte	30
	.byte	'Ifx_SRC_GPSR',0,14,202,1,3
	.word	55304
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,14,205,1,25,48,13
	.byte	'CIRQ',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	53946
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	53946
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	53946
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3688
	.byte	24,2,35,24,0,14
	.word	55331
	.byte	30
	.byte	'Ifx_SRC_GPT12',0,14,214,1,3
	.word	55448
	.byte	15,12
	.word	53946
	.byte	16,2,0,15,32
	.word	53946
	.byte	16,7,0,15,32
	.word	55485
	.byte	16,0,0,15,88
	.word	680
	.byte	16,87,0,15,108
	.word	53946
	.byte	16,26,0,15,96
	.word	680
	.byte	16,95,0,15,96
	.word	55485
	.byte	16,2,0,15,160,3
	.word	680
	.byte	16,159,3,0,15,64
	.word	55485
	.byte	16,1,0,15,192,3
	.word	680
	.byte	16,191,3,0,15,16
	.word	53946
	.byte	16,3,0,15,64
	.word	55570
	.byte	16,3,0,15,192,2
	.word	680
	.byte	16,191,2,0,15,52
	.word	680
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,14,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	55476
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2498
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	53946
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	53946
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	54380
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4317
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	55494
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	55503
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	55512
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	55521
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	53946
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4657
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	55530
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	55539
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	55530
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	55539
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	55550
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	55559
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	55579
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	55588
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	55476
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	55599
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	55476
	.byte	12,3,35,192,18,0,14
	.word	55608
	.byte	30
	.byte	'Ifx_SRC_GTM',0,14,243,1,3
	.word	56068
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,14,246,1,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	56094
	.byte	30
	.byte	'Ifx_SRC_HSCT',0,14,249,1,3
	.word	56127
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,14,252,1,25,16,13
	.byte	'COK',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	53946
	.byte	4,2,35,12,0,14
	.word	56154
	.byte	30
	.byte	'Ifx_SRC_HSSL',0,14,130,2,3
	.word	56227
	.byte	15,56
	.word	680
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,14,133,2,25,80,13
	.byte	'BREQ',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	53946
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	53946
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	56254
	.byte	56,2,35,24,0,14
	.word	56263
	.byte	30
	.byte	'Ifx_SRC_I2C',0,14,142,2,3
	.word	56386
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,14,145,2,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	56412
	.byte	30
	.byte	'Ifx_SRC_LMU',0,14,148,2,3
	.word	56444
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,14,151,2,25,20,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	53946
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	53946
	.byte	4,2,35,16,0,14
	.word	56470
	.byte	30
	.byte	'Ifx_SRC_MSC',0,14,158,2,3
	.word	56555
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,14,161,2,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	56581
	.byte	30
	.byte	'Ifx_SRC_PMU',0,14,164,2,3
	.word	56613
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,14,167,2,25,32,13
	.byte	'SR',0
	.word	55485
	.byte	32,2,35,0,0,14
	.word	56639
	.byte	30
	.byte	'Ifx_SRC_PSI5',0,14,170,2,3
	.word	56672
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,14,173,2,25,32,13
	.byte	'SR',0
	.word	55485
	.byte	32,2,35,0,0,14
	.word	56699
	.byte	30
	.byte	'Ifx_SRC_PSI5S',0,14,176,2,3
	.word	56733
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,14,179,2,25,24,13
	.byte	'TX',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	53946
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	53946
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	53946
	.byte	4,2,35,20,0,14
	.word	56761
	.byte	30
	.byte	'Ifx_SRC_QSPI',0,14,187,2,3
	.word	56854
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,14,190,2,25,4,13
	.byte	'SR',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	56881
	.byte	30
	.byte	'Ifx_SRC_SCR',0,14,193,2,3
	.word	56913
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,14,196,2,25,20,13
	.byte	'DTS',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	55570
	.byte	16,2,35,4,0,14
	.word	56939
	.byte	30
	.byte	'Ifx_SRC_SCU',0,14,200,2,3
	.word	56985
	.byte	15,24
	.word	53946
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,14,203,2,25,24,13
	.byte	'SR',0
	.word	57011
	.byte	24,2,35,0,0,14
	.word	57020
	.byte	30
	.byte	'Ifx_SRC_SENT',0,14,206,2,3
	.word	57053
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,14,209,2,25,12,13
	.byte	'SR',0
	.word	55476
	.byte	12,2,35,0,0,14
	.word	57080
	.byte	30
	.byte	'Ifx_SRC_SMU',0,14,212,2,3
	.word	57112
	.byte	10
	.byte	'_Ifx_SRC_STM',0,14,215,2,25,8,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,0,14
	.word	57138
	.byte	30
	.byte	'Ifx_SRC_STM',0,14,219,2,3
	.word	57184
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,14,222,2,25,16,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	53946
	.byte	4,2,35,12,0,14
	.word	57210
	.byte	30
	.byte	'Ifx_SRC_VADCCG',0,14,228,2,3
	.word	57285
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,14,231,2,25,16,13
	.byte	'SR0',0
	.word	53946
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	53946
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	53946
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	53946
	.byte	4,2,35,12,0,14
	.word	57314
	.byte	30
	.byte	'Ifx_SRC_VADCG',0,14,237,2,3
	.word	57388
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,14,240,2,25,4,13
	.byte	'SRC',0
	.word	53946
	.byte	4,2,35,0,0,14
	.word	57416
	.byte	30
	.byte	'Ifx_SRC_XBAR',0,14,243,2,3
	.word	57450
	.byte	15,4
	.word	54006
	.byte	16,0,0,14
	.word	57477
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,14,128,3,25,4,13
	.byte	'AGBT',0
	.word	57486
	.byte	4,2,35,0,0,14
	.word	57491
	.byte	30
	.byte	'Ifx_SRC_GAGBT',0,14,131,3,3
	.word	57527
	.byte	15,48
	.word	54064
	.byte	16,3,0,14
	.word	57555
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,14,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	57564
	.byte	48,2,35,0,0,14
	.word	57569
	.byte	30
	.byte	'Ifx_SRC_GASCLIN',0,14,137,3,3
	.word	57609
	.byte	14
	.word	54151
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,14,140,3,25,4,13
	.byte	'SPB',0
	.word	57639
	.byte	4,2,35,0,0,14
	.word	57644
	.byte	30
	.byte	'Ifx_SRC_GBCU',0,14,143,3,3
	.word	57678
	.byte	15,64
	.word	54225
	.byte	16,0,0,14
	.word	57705
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,14,146,3,25,64,13
	.byte	'CAN',0
	.word	57714
	.byte	64,2,35,0,0,14
	.word	57719
	.byte	30
	.byte	'Ifx_SRC_GCAN',0,14,149,3,3
	.word	57753
	.byte	15,32
	.word	54282
	.byte	16,1,0,14
	.word	57780
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,14,152,3,25,32,13
	.byte	'CCU6',0
	.word	57789
	.byte	32,2,35,0,0,14
	.word	57794
	.byte	30
	.byte	'Ifx_SRC_GCCU6',0,14,155,3,3
	.word	57830
	.byte	14
	.word	54389
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,14,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	57858
	.byte	8,2,35,0,0,14
	.word	57863
	.byte	30
	.byte	'Ifx_SRC_GCERBERUS',0,14,161,3,3
	.word	57907
	.byte	15,16
	.word	54455
	.byte	16,0,0,14
	.word	57939
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,14,164,3,25,16,13
	.byte	'CIF',0
	.word	57948
	.byte	16,2,35,0,0,14
	.word	57953
	.byte	30
	.byte	'Ifx_SRC_GCIF',0,14,167,3,3
	.word	57987
	.byte	15,8
	.word	54554
	.byte	16,1,0,14
	.word	58014
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,14,170,3,25,8,13
	.byte	'CPU',0
	.word	58023
	.byte	8,2,35,0,0,14
	.word	58028
	.byte	30
	.byte	'Ifx_SRC_GCPU',0,14,173,3,3
	.word	58062
	.byte	15,208,1
	.word	54625
	.byte	16,0,0,14
	.word	58089
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,14,176,3,25,208,1,13
	.byte	'DMA',0
	.word	58099
	.byte	208,1,2,35,0,0,14
	.word	58104
	.byte	30
	.byte	'Ifx_SRC_GDMA',0,14,179,3,3
	.word	58140
	.byte	14
	.word	54718
	.byte	14
	.word	54718
	.byte	14
	.word	54718
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,14,182,3,25,32,13
	.byte	'DSADC0',0
	.word	58167
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4317
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	58172
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	58177
	.byte	8,2,35,24,0,14
	.word	58182
	.byte	30
	.byte	'Ifx_SRC_GDSADC',0,14,188,3,3
	.word	58273
	.byte	15,4
	.word	54794
	.byte	16,0,0,14
	.word	58302
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,14,191,3,25,4,13
	.byte	'EMEM',0
	.word	58311
	.byte	4,2,35,0,0,14
	.word	58316
	.byte	30
	.byte	'Ifx_SRC_GEMEM',0,14,194,3,3
	.word	58352
	.byte	15,80
	.word	54854
	.byte	16,0,0,14
	.word	58380
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,14,197,3,25,80,13
	.byte	'ERAY',0
	.word	58389
	.byte	80,2,35,0,0,14
	.word	58394
	.byte	30
	.byte	'Ifx_SRC_GERAY',0,14,200,3,3
	.word	58430
	.byte	15,4
	.word	55008
	.byte	16,0,0,14
	.word	58458
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,14,203,3,25,4,13
	.byte	'ETH',0
	.word	58467
	.byte	4,2,35,0,0,14
	.word	58472
	.byte	30
	.byte	'Ifx_SRC_GETH',0,14,206,3,3
	.word	58506
	.byte	15,4
	.word	55066
	.byte	16,0,0,14
	.word	58533
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,14,209,3,25,4,13
	.byte	'FCE',0
	.word	58542
	.byte	4,2,35,0,0,14
	.word	58547
	.byte	30
	.byte	'Ifx_SRC_GFCE',0,14,212,3,3
	.word	58581
	.byte	15,12
	.word	55124
	.byte	16,0,0,14
	.word	58608
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,14,215,3,25,12,13
	.byte	'FFT',0
	.word	58617
	.byte	12,2,35,0,0,14
	.word	58622
	.byte	30
	.byte	'Ifx_SRC_GFFT',0,14,218,3,3
	.word	58656
	.byte	15,64
	.word	55210
	.byte	16,1,0,14
	.word	58683
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,14,221,3,25,64,13
	.byte	'GPSR',0
	.word	58692
	.byte	64,2,35,0,0,14
	.word	58697
	.byte	30
	.byte	'Ifx_SRC_GGPSR',0,14,224,3,3
	.word	58733
	.byte	15,48
	.word	55331
	.byte	16,0,0,14
	.word	58761
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,14,227,3,25,48,13
	.byte	'GPT12',0
	.word	58770
	.byte	48,2,35,0,0,14
	.word	58775
	.byte	30
	.byte	'Ifx_SRC_GGPT12',0,14,230,3,3
	.word	58813
	.byte	15,204,18
	.word	55608
	.byte	16,0,0,14
	.word	58842
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,14,233,3,25,204,18,13
	.byte	'GTM',0
	.word	58852
	.byte	204,18,2,35,0,0,14
	.word	58857
	.byte	30
	.byte	'Ifx_SRC_GGTM',0,14,236,3,3
	.word	58893
	.byte	15,4
	.word	56094
	.byte	16,0,0,14
	.word	58920
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,14,239,3,25,4,13
	.byte	'HSCT',0
	.word	58929
	.byte	4,2,35,0,0,14
	.word	58934
	.byte	30
	.byte	'Ifx_SRC_GHSCT',0,14,242,3,3
	.word	58970
	.byte	15,64
	.word	56154
	.byte	16,3,0,14
	.word	58998
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,14,245,3,25,68,13
	.byte	'HSSL',0
	.word	59007
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	53946
	.byte	4,2,35,64,0,14
	.word	59012
	.byte	30
	.byte	'Ifx_SRC_GHSSL',0,14,249,3,3
	.word	59061
	.byte	15,80
	.word	56263
	.byte	16,0,0,14
	.word	59089
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,14,252,3,25,80,13
	.byte	'I2C',0
	.word	59098
	.byte	80,2,35,0,0,14
	.word	59103
	.byte	30
	.byte	'Ifx_SRC_GI2C',0,14,255,3,3
	.word	59137
	.byte	15,4
	.word	56412
	.byte	16,0,0,14
	.word	59164
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,14,130,4,25,4,13
	.byte	'LMU',0
	.word	59173
	.byte	4,2,35,0,0,14
	.word	59178
	.byte	30
	.byte	'Ifx_SRC_GLMU',0,14,133,4,3
	.word	59212
	.byte	15,40
	.word	56470
	.byte	16,1,0,14
	.word	59239
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,14,136,4,25,40,13
	.byte	'MSC',0
	.word	59248
	.byte	40,2,35,0,0,14
	.word	59253
	.byte	30
	.byte	'Ifx_SRC_GMSC',0,14,139,4,3
	.word	59287
	.byte	15,8
	.word	56581
	.byte	16,1,0,14
	.word	59314
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,14,142,4,25,8,13
	.byte	'PMU',0
	.word	59323
	.byte	8,2,35,0,0,14
	.word	59328
	.byte	30
	.byte	'Ifx_SRC_GPMU',0,14,145,4,3
	.word	59362
	.byte	15,32
	.word	56639
	.byte	16,0,0,14
	.word	59389
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,14,148,4,25,32,13
	.byte	'PSI5',0
	.word	59398
	.byte	32,2,35,0,0,14
	.word	59403
	.byte	30
	.byte	'Ifx_SRC_GPSI5',0,14,151,4,3
	.word	59439
	.byte	15,32
	.word	56699
	.byte	16,0,0,14
	.word	59467
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,14,154,4,25,32,13
	.byte	'PSI5S',0
	.word	59476
	.byte	32,2,35,0,0,14
	.word	59481
	.byte	30
	.byte	'Ifx_SRC_GPSI5S',0,14,157,4,3
	.word	59519
	.byte	15,96
	.word	56761
	.byte	16,3,0,14
	.word	59548
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,14,160,4,25,96,13
	.byte	'QSPI',0
	.word	59557
	.byte	96,2,35,0,0,14
	.word	59562
	.byte	30
	.byte	'Ifx_SRC_GQSPI',0,14,163,4,3
	.word	59598
	.byte	15,4
	.word	56881
	.byte	16,0,0,14
	.word	59626
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,14,166,4,25,4,13
	.byte	'SCR',0
	.word	59635
	.byte	4,2,35,0,0,14
	.word	59640
	.byte	30
	.byte	'Ifx_SRC_GSCR',0,14,169,4,3
	.word	59674
	.byte	14
	.word	56939
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,14,172,4,25,20,13
	.byte	'SCU',0
	.word	59701
	.byte	20,2,35,0,0,14
	.word	59706
	.byte	30
	.byte	'Ifx_SRC_GSCU',0,14,175,4,3
	.word	59740
	.byte	15,24
	.word	57020
	.byte	16,0,0,14
	.word	59767
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,14,178,4,25,24,13
	.byte	'SENT',0
	.word	59776
	.byte	24,2,35,0,0,14
	.word	59781
	.byte	30
	.byte	'Ifx_SRC_GSENT',0,14,181,4,3
	.word	59817
	.byte	15,12
	.word	57080
	.byte	16,0,0,14
	.word	59845
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,14,184,4,25,12,13
	.byte	'SMU',0
	.word	59854
	.byte	12,2,35,0,0,14
	.word	59859
	.byte	30
	.byte	'Ifx_SRC_GSMU',0,14,187,4,3
	.word	59893
	.byte	15,16
	.word	57138
	.byte	16,1,0,14
	.word	59920
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,14,190,4,25,16,13
	.byte	'STM',0
	.word	59929
	.byte	16,2,35,0,0,14
	.word	59934
	.byte	30
	.byte	'Ifx_SRC_GSTM',0,14,193,4,3
	.word	59968
	.byte	15,64
	.word	57314
	.byte	16,3,0,14
	.word	59995
	.byte	15,224,1
	.word	680
	.byte	16,223,1,0,15,32
	.word	57210
	.byte	16,1,0,14
	.word	60020
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,14,196,4,25,192,2,13
	.byte	'G',0
	.word	60004
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	60009
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	60029
	.byte	32,3,35,160,2,0,14
	.word	60034
	.byte	30
	.byte	'Ifx_SRC_GVADC',0,14,201,4,3
	.word	60103
	.byte	14
	.word	57416
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,14,204,4,25,4,13
	.byte	'XBAR',0
	.word	60131
	.byte	4,2,35,0,0,14
	.word	60136
	.byte	30
	.byte	'Ifx_SRC_GXBAR',0,14,207,4,3
	.word	60172
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_STM_ACCEN0_Bits',0,15,79,3
	.word	60200
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN1_Bits',0,15,85,3
	.word	60757
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,15,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CAP_Bits',0,15,91,3
	.word	60834
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,15,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CAPSV_Bits',0,15,97,3
	.word	60906
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,15,100,16,4,11
	.byte	'DISR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_STM_CLC_Bits',0,15,107,3
	.word	60982
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,15,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	680
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	680
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	680
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	680
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_STM_CMCON_Bits',0,15,120,3
	.word	61123
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,15,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CMP_Bits',0,15,126,3
	.word	61341
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,15,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	505
	.byte	25,0,2,35,0,0,30
	.byte	'Ifx_STM_ICR_Bits',0,15,139,1,3
	.word	61408
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,15,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_STM_ID_Bits',0,15,147,1,3
	.word	61611
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,15,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_STM_ISCR_Bits',0,15,157,1,3
	.word	61718
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,15,160,1,16,4,11
	.byte	'RST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	505
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_STM_KRST0_Bits',0,15,165,1,3
	.word	61869
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,15,168,1,16,4,11
	.byte	'RST',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_STM_KRST1_Bits',0,15,172,1,3
	.word	61980
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,15,175,1,16,4,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_STM_KRSTCLR_Bits',0,15,179,1,3
	.word	62072
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,15,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	680
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	680
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_STM_OCS_Bits',0,15,189,1,3
	.word	62168
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,15,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM0_Bits',0,15,195,1,3
	.word	62314
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,15,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM0SV_Bits',0,15,201,1,3
	.word	62386
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,15,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM1_Bits',0,15,207,1,3
	.word	62462
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,15,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM2_Bits',0,15,213,1,3
	.word	62534
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,15,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM3_Bits',0,15,219,1,3
	.word	62606
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,15,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM4_Bits',0,15,225,1,3
	.word	62679
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,15,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM5_Bits',0,15,231,1,3
	.word	62752
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,15,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM6_Bits',0,15,237,1,3
	.word	62825
	.byte	12,15,245,1,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60200
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN0',0,15,250,1,3
	.word	62898
	.byte	12,15,253,1,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60757
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN1',0,15,130,2,3
	.word	62962
	.byte	12,15,133,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60834
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CAP',0,15,138,2,3
	.word	63026
	.byte	12,15,141,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60906
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CAPSV',0,15,146,2,3
	.word	63087
	.byte	12,15,149,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60982
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CLC',0,15,154,2,3
	.word	63150
	.byte	12,15,157,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61123
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CMCON',0,15,162,2,3
	.word	63211
	.byte	12,15,165,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61341
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CMP',0,15,170,2,3
	.word	63274
	.byte	12,15,173,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61408
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ICR',0,15,178,2,3
	.word	63335
	.byte	12,15,181,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61611
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ID',0,15,186,2,3
	.word	63396
	.byte	12,15,189,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61718
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ISCR',0,15,194,2,3
	.word	63456
	.byte	12,15,197,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61869
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRST0',0,15,202,2,3
	.word	63518
	.byte	12,15,205,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61980
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRST1',0,15,210,2,3
	.word	63581
	.byte	12,15,213,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62072
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRSTCLR',0,15,218,2,3
	.word	63644
	.byte	12,15,221,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62168
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_OCS',0,15,226,2,3
	.word	63709
	.byte	12,15,229,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62314
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM0',0,15,234,2,3
	.word	63770
	.byte	12,15,237,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62386
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM0SV',0,15,242,2,3
	.word	63832
	.byte	12,15,245,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62462
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM1',0,15,250,2,3
	.word	63896
	.byte	12,15,253,2,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62534
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM2',0,15,130,3,3
	.word	63958
	.byte	12,15,133,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62606
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM3',0,15,138,3,3
	.word	64020
	.byte	12,15,141,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62679
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM4',0,15,146,3,3
	.word	64082
	.byte	12,15,149,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62752
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM5',0,15,154,3,3
	.word	64144
	.byte	12,15,157,3,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62825
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM6',0,15,162,3,3
	.word	64206
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,16,79,3
	.word	64268
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,16,85,3
	.word	64829
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,16,88,16,4,11
	.byte	'SEL',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,16,95,3
	.word	64910
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,16,98,16,4,11
	.byte	'VLD0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,16,111,3
	.word	65063
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,16,114,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,16,121,3
	.word	65311
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,16,124,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	505
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0_Bits',0,16,128,1,3
	.word	65457
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,16,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM1_Bits',0,16,136,1,3
	.word	65555
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,16,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM2_Bits',0,16,144,1,3
	.word	65671
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,16,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRD_Bits',0,16,153,1,3
	.word	65787
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,16,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRP_Bits',0,16,162,1,3
	.word	65927
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,16,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	505
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	697
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCW_Bits',0,16,171,1,3
	.word	66067
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,16,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	680
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	680
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	697
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	680
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	680
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FCON_Bits',0,16,193,1,3
	.word	66206
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,16,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	680
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	680
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	680
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FPRO_Bits',0,16,218,1,3
	.word	66568
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,16,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	697
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	680
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	680
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	680
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FSR_Bits',0,16,254,1,3
	.word	67009
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,16,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	680
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	680
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_ID_Bits',0,16,134,2,3
	.word	67615
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,16,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	697
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARD_Bits',0,16,147,2,3
	.word	67726
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,16,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	697
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARP_Bits',0,16,159,2,3
	.word	67940
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,16,162,2,16,4,11
	.byte	'L',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	680
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	680
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	697
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	680
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCOND_Bits',0,16,179,2,3
	.word	68127
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,16,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	680
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	505
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,16,188,2,3
	.word	68451
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,16,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	697
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,16,199,2,3
	.word	68594
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	697
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	680
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	680
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	680
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	697
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,219,2,3
	.word	68783
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,16,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	680
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,16,254,2,3
	.word	69146
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,16,129,3,16,4,11
	.byte	'S0L',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONP_Bits',0,16,160,3,3
	.word	69741
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,16,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	680
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	680
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	680
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	680
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	680
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	680
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	680
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	680
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	680
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	680
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	680
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	680
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	680
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	680
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	680
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	680
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	680
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	680
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	680
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	680
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	680
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,16,194,3,3
	.word	70265
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,16,197,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,16,201,3,3
	.word	70847
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,16,204,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,16,208,3,3
	.word	70949
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,16,211,3,16,4,11
	.byte	'TAG',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	505
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,16,215,3,3
	.word	71051
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,16,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	505
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD_Bits',0,16,222,3,3
	.word	71153
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,16,225,3,16,4,11
	.byte	'STRT',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	680
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	680
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	680
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	680
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	680
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	697
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_RRCT_Bits',0,16,236,3,3
	.word	71247
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,16,239,3,16,4,11
	.byte	'DATA',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0_Bits',0,16,242,3,3
	.word	71457
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,16,245,3,16,4,11
	.byte	'DATA',0,4
	.word	505
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1_Bits',0,16,248,3,3
	.word	71530
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,16,251,3,16,4,11
	.byte	'SEL',0,1
	.word	680
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	680
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	680
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	505
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,16,130,4,3
	.word	71603
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,16,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	680
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	505
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,16,137,4,3
	.word	71758
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,16,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	680
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	505
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	680
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	680
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	680
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,16,147,4,3
	.word	71863
	.byte	12,16,155,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64268
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN0',0,16,160,4,3
	.word	72011
	.byte	12,16,163,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64829
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1',0,16,168,4,3
	.word	72077
	.byte	12,16,171,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64910
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG',0,16,176,4,3
	.word	72143
	.byte	12,16,179,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65063
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT',0,16,184,4,3
	.word	72211
	.byte	12,16,187,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65311
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_TOP',0,16,192,4,3
	.word	72280
	.byte	12,16,195,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65457
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0',0,16,200,4,3
	.word	72348
	.byte	12,16,203,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65555
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM1',0,16,208,4,3
	.word	72413
	.byte	12,16,211,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65671
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM2',0,16,216,4,3
	.word	72478
	.byte	12,16,219,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65787
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRD',0,16,224,4,3
	.word	72543
	.byte	12,16,227,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65927
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRP',0,16,232,4,3
	.word	72608
	.byte	12,16,235,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66067
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCW',0,16,240,4,3
	.word	72673
	.byte	12,16,243,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66206
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FCON',0,16,248,4,3
	.word	72737
	.byte	12,16,251,4,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66568
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FPRO',0,16,128,5,3
	.word	72801
	.byte	12,16,131,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67009
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FSR',0,16,136,5,3
	.word	72865
	.byte	12,16,139,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67615
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ID',0,16,144,5,3
	.word	72928
	.byte	12,16,147,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67726
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARD',0,16,152,5,3
	.word	72990
	.byte	12,16,155,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67940
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARP',0,16,160,5,3
	.word	73054
	.byte	12,16,163,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68127
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCOND',0,16,168,5,3
	.word	73118
	.byte	12,16,171,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68451
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG',0,16,176,5,3
	.word	73185
	.byte	12,16,179,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68594
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSM',0,16,184,5,3
	.word	73254
	.byte	12,16,187,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68783
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,16,192,5,3
	.word	73323
	.byte	12,16,195,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69146
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONOTP',0,16,200,5,3
	.word	73396
	.byte	12,16,203,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69741
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONP',0,16,208,5,3
	.word	73465
	.byte	12,16,211,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70265
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONWOP',0,16,216,5,3
	.word	73532
	.byte	12,16,219,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70847
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0',0,16,224,5,3
	.word	73601
	.byte	12,16,227,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70949
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1',0,16,232,5,3
	.word	73669
	.byte	12,16,235,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71051
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2',0,16,240,5,3
	.word	73737
	.byte	12,16,243,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71153
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD',0,16,248,5,3
	.word	73805
	.byte	12,16,251,5,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71247
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRCT',0,16,128,6,3
	.word	73869
	.byte	12,16,131,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71457
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0',0,16,136,6,3
	.word	73933
	.byte	12,16,139,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71530
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1',0,16,144,6,3
	.word	73997
	.byte	12,16,147,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71603
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG',0,16,152,6,3
	.word	74061
	.byte	12,16,155,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71758
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT',0,16,160,6,3
	.word	74129
	.byte	12,16,163,6,9,4,13
	.byte	'U',0
	.word	505
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	521
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71863
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_TOP',0,16,168,6,3
	.word	74198
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,16,179,6,25,12,13
	.byte	'CFG',0
	.word	72143
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	72211
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	72280
	.byte	4,2,35,8,0,14
	.word	74266
	.byte	30
	.byte	'Ifx_FLASH_CBAB',0,16,184,6,3
	.word	74329
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,16,187,6,25,12,13
	.byte	'CFG0',0
	.word	73601
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	73669
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	73737
	.byte	4,2,35,8,0,14
	.word	74358
	.byte	30
	.byte	'Ifx_FLASH_RDB',0,16,192,6,3
	.word	74422
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,16,195,6,25,12,13
	.byte	'CFG',0
	.word	74061
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74129
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	74198
	.byte	4,2,35,8,0,14
	.word	74450
	.byte	30
	.byte	'Ifx_FLASH_UBAB',0,16,200,6,3
	.word	74513
	.byte	30
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8070
	.byte	30
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7983
	.byte	30
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4326
	.byte	30
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2379
	.byte	30
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3374
	.byte	30
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2507
	.byte	30
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3154
	.byte	30
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2722
	.byte	30
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2937
	.byte	30
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7342
	.byte	30
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7466
	.byte	30
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7550
	.byte	30
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7730
	.byte	30
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5981
	.byte	30
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6505
	.byte	30
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6155
	.byte	30
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6329
	.byte	30
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6994
	.byte	30
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1808
	.byte	30
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5318
	.byte	30
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5806
	.byte	30
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5465
	.byte	30
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5634
	.byte	30
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6661
	.byte	30
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1492
	.byte	30
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5032
	.byte	30
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4666
	.byte	30
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3697
	.byte	30
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4001
	.byte	30
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8597
	.byte	30
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8030
	.byte	30
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4617
	.byte	30
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2458
	.byte	30
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3648
	.byte	30
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2682
	.byte	30
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3334
	.byte	30
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2897
	.byte	30
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3114
	.byte	30
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7426
	.byte	30
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7675
	.byte	30
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7934
	.byte	30
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7302
	.byte	30
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6115
	.byte	30
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6621
	.byte	30
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6289
	.byte	30
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6465
	.byte	30
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2339
	.byte	30
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6954
	.byte	30
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5425
	.byte	30
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5941
	.byte	30
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5594
	.byte	30
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5766
	.byte	30
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1768
	.byte	30
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5278
	.byte	30
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4992
	.byte	30
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3961
	.byte	30
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4277
	.byte	14
	.word	8637
	.byte	30
	.byte	'Ifx_P',0,6,139,6,3
	.word	75860
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,30
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	75880
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,30
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	76031
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,30
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	76275
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,30
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	76373
	.byte	30
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9250
	.byte	32,5,190,1,9,8,13
	.byte	'port',0
	.word	9245
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	680
	.byte	1,2,35,4,0,30
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	76838
	.byte	30
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,17,148,1,16
	.word	244
	.byte	32,17,212,5,9,8,13
	.byte	'value',0
	.word	9544
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9544
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_CcuconRegConfig',0,17,216,5,3
	.word	76938
	.byte	32,17,221,5,9,8,13
	.byte	'pDivider',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	680
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	680
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	301
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_InitialStepConfig',0,17,227,5,3
	.word	77009
	.byte	32,17,231,5,9,12,13
	.byte	'k2Step',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	301
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	76898
	.byte	4,2,35,8,0,30
	.byte	'IfxScuCcu_PllStepsConfig',0,17,236,5,3
	.word	77126
	.byte	3
	.word	241
	.byte	32,17,244,5,9,48,13
	.byte	'ccucon0',0
	.word	76938
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	76938
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	76938
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	76938
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	76938
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	76938
	.byte	8,2,35,40,0,30
	.byte	'IfxScuCcu_ClockDistributionConfig',0,17,252,5,3
	.word	77228
	.byte	32,17,128,6,9,8,13
	.byte	'value',0
	.word	9544
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9544
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,17,132,6,3
	.word	77380
	.byte	3
	.word	77126
	.byte	32,17,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	680
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	77456
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	77009
	.byte	8,2,35,8,0,30
	.byte	'IfxScuCcu_SysPllConfig',0,17,142,6,3
	.word	77461
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,30
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	77578
	.byte	32,7,160,1,9,6,13
	.byte	'counter',0
	.word	9544
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	680
	.byte	1,2,35,4,0,30
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	77667
	.byte	32,7,172,1,9,32,13
	.byte	'instruction',0
	.word	77667
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	77667
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	77667
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	77667
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	77667
	.byte	6,2,35,24,0,30
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	77733
	.byte	33
	.word	505
	.byte	34,0,35
	.byte	'__ISTACK1',0,10,72,1
	.word	77851
	.byte	1,1,35
	.byte	'__INTTAB_CPU1',0,10,72,1
	.word	77851
	.byte	1,1,35
	.byte	'__TRAPTAB_CPU1',0,10,72,1
	.word	77851
	.byte	1,1,35
	.byte	'__CSA1',0,10,72,1
	.word	77851
	.byte	1,1,35
	.byte	'__CSA1_END',0,10,72,1
	.word	77851
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L39:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,54,15,39,12,63,12,60,12,0,0,21,5,0,73
	.byte	19,0,0,22,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,23,46,0,3,8,54,15,39,12,63,12,60,12,0,0,24,46,1,49
	.byte	19,0,0,25,5,0,49,19,0,0,26,29,1,49,19,0,0,27,11,0,49,19,0,0,28,46,0,3,8,58,15,59,15,57,15,54,15,39,12
	.byte	63,12,60,12,0,0,29,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,30,22,0,3,8,58,15,59,15,57,15,73,19
	.byte	0,0,31,21,0,54,15,0,0,32,19,1,58,15,59,15,57,15,11,15,0,0,33,1,1,73,19,0,0,34,33,0,0,0,35,52,0,3,8,58
	.byte	15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L40:
	.word	.L157-.L156
.L156:
	.half	3
	.word	.L159-.L158
.L158:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L159:
.L157:
	.sdecl	'.debug_info',debug,cluster('_Core1_start')
	.sect	'.debug_info'
.L41:
	.word	1299
	.half	3
	.word	.L42
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L44,.L43
	.byte	2
	.word	.L37
	.byte	3
	.byte	'_Core1_start',0,1,92,6,1,1,1
	.word	.L36,.L46,.L35
	.byte	4
	.word	.L36,.L46
	.byte	5
	.byte	'pcxi',0,1,94,12
	.word	.L47,.L48
	.byte	5
	.byte	'wdtPassword',0,1,95,12
	.word	.L49,.L50
	.byte	6
	.word	.L51,.L52,.L2
	.byte	7
	.word	.L53,.L54
	.byte	8
	.word	.L55,.L52,.L2
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L49,.L56
	.byte	0,0,6
	.word	.L57,.L58,.L59
	.byte	7
	.word	.L60,.L61
	.byte	8
	.word	.L62,.L58,.L59
	.byte	8
	.word	.L63,.L64,.L3
	.byte	5
	.byte	'pcon1',0,3,208,7,23
	.word	.L65,.L66
	.byte	0,8
	.word	.L67,.L3,.L59
	.byte	5
	.byte	'coreIndex',0,3,214,7,12
	.word	.L47,.L68
	.byte	5
	.byte	'wdtPassword',0,3,215,7,12
	.word	.L49,.L69
	.byte	6
	.word	.L70,.L3,.L4
	.byte	8
	.word	.L71,.L3,.L4
	.byte	5
	.byte	'reg',0,3,143,6,21
	.word	.L72,.L73
	.byte	0,0,6
	.word	.L51,.L74,.L5
	.byte	7
	.word	.L53,.L54
	.byte	8
	.word	.L55,.L74,.L5
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L49,.L75
	.byte	0,0,6
	.word	.L76,.L77,.L78
	.byte	7
	.word	.L79,.L80
	.byte	7
	.word	.L81,.L82
	.byte	9
	.word	.L83,.L77,.L78
	.byte	0,8
	.word	.L84,.L78,.L85
	.byte	5
	.byte	'pcon0',0,3,219,7,23
	.word	.L86,.L87
	.byte	6
	.word	.L88,.L89,.L85
	.byte	7
	.word	.L90,.L91
	.byte	7
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L89,.L85
	.byte	0,0,0,0,0,6
	.word	.L95,.L96,.L97
	.byte	7
	.word	.L98,.L99
	.byte	8
	.word	.L100,.L96,.L97
	.byte	5
	.byte	'coreIndex',0,3,179,7,12
	.word	.L47,.L101
	.byte	5
	.byte	'wdtPassword',0,3,180,7,12
	.word	.L49,.L102
	.byte	6
	.word	.L70,.L96,.L14
	.byte	8
	.word	.L71,.L96,.L14
	.byte	5
	.byte	'reg',0,3,143,6,21
	.word	.L72,.L103
	.byte	0,0,6
	.word	.L51,.L104,.L15
	.byte	7
	.word	.L53,.L54
	.byte	8
	.word	.L55,.L104,.L15
	.byte	5
	.byte	'password',0,2,229,3,12
	.word	.L49,.L105
	.byte	0,0,6
	.word	.L76,.L106,.L107
	.byte	7
	.word	.L79,.L80
	.byte	7
	.word	.L81,.L82
	.byte	9
	.word	.L83,.L106,.L107
	.byte	0,8
	.word	.L108,.L107,.L109
	.byte	5
	.byte	'dcon0',0,3,184,7,23
	.word	.L110,.L111
	.byte	6
	.word	.L88,.L112,.L109
	.byte	7
	.word	.L90,.L91
	.byte	7
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L112,.L109
	.byte	0,0,0,0,6
	.word	.L76,.L113,.L114
	.byte	7
	.word	.L79,.L80
	.byte	7
	.word	.L81,.L82
	.byte	9
	.word	.L83,.L113,.L114
	.byte	0,6
	.word	.L88,.L115,.L116
	.byte	7
	.word	.L90,.L91
	.byte	7
	.word	.L92,.L93
	.byte	9
	.word	.L94,.L115,.L116
	.byte	0,6
	.word	.L117,.L118,.L119
	.byte	7
	.word	.L120,.L121
	.byte	7
	.word	.L122,.L123
	.byte	8
	.word	.L124,.L118,.L119
	.byte	5
	.byte	'k',0,3,221,6,13
	.word	.L47,.L125
	.byte	5
	.byte	'nxt_cxi_val',0,3,222,6,13
	.word	.L47,.L126
	.byte	5
	.byte	'prvCsa',0,3,223,6,13
	.word	.L127,.L128
	.byte	5
	.byte	'nxtCsa',0,3,224,6,13
	.word	.L127,.L129
	.byte	5
	.byte	'numOfCsa',0,3,225,6,13
	.word	.L47,.L130
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('_Core1_start')
	.sect	'.debug_abbrev'
.L42:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,29,1
	.byte	49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('_Core1_start')
	.sect	'.debug_line'
.L43:
	.word	.L161-.L160
.L160:
	.half	3
	.word	.L163-.L162
.L162:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Cpu/CStart/IfxCpu_CStart1.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L163:
	.byte	5,83,7,0,5,2
	.word	.L36
	.byte	3,222,0,1,4,2,5,33,9
	.half	.L52-.L36
	.byte	3,139,3,1,5,14,9
	.half	.L131-.L52
	.byte	3,1,1,5,5,9
	.half	.L132-.L131
	.byte	3,2,1,4,1,9
	.half	.L2-.L132
	.byte	3,247,124,1,5,12,9
	.half	.L164-.L2
	.byte	3,1,1,5,21,9
	.half	.L165-.L164
	.byte	3,3,1,5,11,9
	.half	.L166-.L165
	.byte	1,5,19,9
	.half	.L167-.L166
	.byte	3,3,1,5,10,9
	.half	.L134-.L167
	.byte	3,1,1,5,11,9
	.half	.L168-.L134
	.byte	3,1,1,5,28,9
	.half	.L169-.L168
	.byte	3,3,1,4,3,5,5,9
	.half	.L58-.L169
	.byte	3,222,6,1,5,23,7,9
	.half	.L64-.L58
	.byte	3,3,1,9
	.half	.L135-.L64
	.byte	3,1,1,5,15,9
	.half	.L170-.L135
	.byte	3,1,1,5,19,9
	.half	.L3-.L170
	.byte	3,189,126,1,5,37,9
	.half	.L136-.L3
	.byte	3,1,1,5,5,9
	.half	.L171-.L136
	.byte	1,5,76,9
	.half	.L4-.L171
	.byte	3,198,1,1,5,83,9
	.half	.L172-.L4
	.byte	1,4,2,5,33,9
	.half	.L74-.L172
	.byte	3,147,124,1,5,14,9
	.half	.L138-.L74
	.byte	3,1,1,5,5,9
	.half	.L139-.L138
	.byte	3,2,1,4,3,5,52,9
	.half	.L5-.L139
	.byte	3,237,3,1,5,59,9
	.half	.L173-.L5
	.byte	1,4,2,5,25,9
	.half	.L77-.L173
	.byte	3,221,123,1,5,5,9
	.half	.L174-.L77
	.byte	1,5,38,7,9
	.half	.L175-.L174
	.byte	3,5,1,5,45,9
	.half	.L176-.L175
	.byte	3,1,1,5,50,9
	.half	.L177-.L176
	.byte	1,5,69,9
	.half	.L178-.L177
	.byte	3,127,1,5,26,9
	.half	.L179-.L178
	.byte	3,126,1,5,34,9
	.half	.L6-.L179
	.byte	3,9,1,5,41,9
	.half	.L180-.L6
	.byte	3,1,1,5,46,9
	.half	.L181-.L180
	.byte	1,5,65,9
	.half	.L182-.L181
	.byte	3,127,1,5,22,9
	.half	.L183-.L182
	.byte	3,126,1,5,41,9
	.half	.L184-.L183
	.byte	3,6,1,5,28,9
	.half	.L7-.L184
	.byte	1,5,41,9
	.half	.L185-.L7
	.byte	1,4,3,5,23,7,9
	.half	.L78-.L185
	.byte	3,149,4,1,5,25,9
	.half	.L141-.L78
	.byte	3,1,1,5,32,7,9
	.half	.L186-.L141
	.byte	1,5,36,9
	.half	.L187-.L186
	.byte	1,5,32,9
	.half	.L9-.L187
	.byte	1,5,23,9
	.half	.L10-.L9
	.byte	1,5,15,9
	.half	.L188-.L10
	.byte	3,1,1,5,50,9
	.half	.L189-.L188
	.byte	3,1,1,5,57,9
	.half	.L190-.L189
	.byte	1,4,2,5,25,9
	.half	.L89-.L190
	.byte	3,175,124,1,5,5,9
	.half	.L191-.L89
	.byte	1,5,38,7,9
	.half	.L192-.L191
	.byte	3,5,1,5,45,9
	.half	.L193-.L192
	.byte	3,1,1,5,50,9
	.half	.L194-.L193
	.byte	1,5,69,9
	.half	.L195-.L194
	.byte	3,127,1,5,26,9
	.half	.L196-.L195
	.byte	3,126,1,5,34,9
	.half	.L11-.L196
	.byte	3,9,1,5,41,9
	.half	.L197-.L11
	.byte	3,1,1,5,46,9
	.half	.L198-.L197
	.byte	1,5,65,9
	.half	.L199-.L198
	.byte	3,127,1,5,22,9
	.half	.L200-.L199
	.byte	3,126,1,5,41,9
	.half	.L201-.L200
	.byte	3,6,1,5,28,9
	.half	.L12-.L201
	.byte	1,5,41,9
	.half	.L202-.L12
	.byte	1,4,3,5,12,7,9
	.half	.L85-.L202
	.byte	3,196,3,1,4,1,5,25,9
	.half	.L59-.L85
	.byte	3,145,121,1,4,3,5,19,9
	.half	.L96-.L59
	.byte	3,157,5,1,5,37,9
	.half	.L143-.L96
	.byte	3,1,1,5,5,9
	.half	.L203-.L143
	.byte	1,5,76,9
	.half	.L14-.L203
	.byte	3,163,1,1,5,83,9
	.half	.L204-.L14
	.byte	1,4,2,5,33,9
	.half	.L104-.L204
	.byte	3,182,124,1,5,14,9
	.half	.L145-.L104
	.byte	3,1,1,5,5,9
	.half	.L146-.L145
	.byte	3,2,1,4,3,5,52,9
	.half	.L15-.L146
	.byte	3,202,3,1,5,59,9
	.half	.L205-.L15
	.byte	1,4,2,5,25,9
	.half	.L106-.L205
	.byte	3,128,124,1,5,5,9
	.half	.L206-.L106
	.byte	1,5,38,7,9
	.half	.L207-.L206
	.byte	3,5,1,5,45,9
	.half	.L208-.L207
	.byte	3,1,1,5,50,9
	.half	.L209-.L208
	.byte	1,5,69,9
	.half	.L210-.L209
	.byte	3,127,1,5,26,9
	.half	.L211-.L210
	.byte	3,126,1,5,34,9
	.half	.L16-.L211
	.byte	3,9,1,5,41,9
	.half	.L212-.L16
	.byte	3,1,1,5,46,9
	.half	.L213-.L212
	.byte	1,5,65,9
	.half	.L214-.L213
	.byte	3,127,1,5,22,9
	.half	.L215-.L214
	.byte	3,126,1,5,41,9
	.half	.L216-.L215
	.byte	3,6,1,5,28,9
	.half	.L17-.L216
	.byte	1,5,41,9
	.half	.L217-.L17
	.byte	1,4,3,5,23,7,9
	.half	.L107-.L217
	.byte	3,242,3,1,5,25,9
	.half	.L148-.L107
	.byte	3,1,1,5,32,7,9
	.half	.L218-.L148
	.byte	1,5,36,9
	.half	.L219-.L218
	.byte	1,5,32,9
	.half	.L19-.L219
	.byte	1,5,23,9
	.half	.L20-.L19
	.byte	1,5,15,9
	.half	.L220-.L20
	.byte	3,1,1,5,50,9
	.half	.L221-.L220
	.byte	3,1,1,5,57,9
	.half	.L222-.L221
	.byte	1,4,2,5,25,9
	.half	.L112-.L222
	.byte	3,210,124,1,5,5,9
	.half	.L223-.L112
	.byte	1,5,38,7,9
	.half	.L224-.L223
	.byte	3,5,1,5,45,9
	.half	.L225-.L224
	.byte	3,1,1,5,50,9
	.half	.L226-.L225
	.byte	1,5,69,9
	.half	.L227-.L226
	.byte	3,127,1,5,26,9
	.half	.L228-.L227
	.byte	3,126,1,5,34,9
	.half	.L21-.L228
	.byte	3,9,1,5,41,9
	.half	.L229-.L21
	.byte	3,1,1,5,46,9
	.half	.L230-.L229
	.byte	1,5,65,9
	.half	.L231-.L230
	.byte	3,127,1,5,22,9
	.half	.L232-.L231
	.byte	3,126,1,5,41,9
	.half	.L233-.L232
	.byte	3,6,1,5,28,9
	.half	.L22-.L233
	.byte	1,5,41,9
	.half	.L234-.L22
	.byte	1,4,3,5,12,7,9
	.half	.L109-.L234
	.byte	3,161,3,1,4,1,5,55,9
	.half	.L97-.L109
	.byte	3,183,121,1,4,2,5,25,9
	.half	.L113-.L97
	.byte	3,193,2,1,5,5,9
	.half	.L235-.L113
	.byte	1,5,38,7,9
	.half	.L236-.L235
	.byte	3,5,1,5,45,9
	.half	.L237-.L236
	.byte	3,1,1,5,50,9
	.half	.L238-.L237
	.byte	1,5,69,9
	.half	.L239-.L238
	.byte	3,127,1,5,26,9
	.half	.L240-.L239
	.byte	3,126,1,5,34,9
	.half	.L24-.L240
	.byte	3,9,1,5,41,9
	.half	.L241-.L24
	.byte	3,1,1,5,46,9
	.half	.L242-.L241
	.byte	1,5,65,9
	.half	.L243-.L242
	.byte	3,127,1,5,22,9
	.half	.L244-.L243
	.byte	3,126,1,5,41,9
	.half	.L245-.L244
	.byte	3,6,1,5,28,9
	.half	.L25-.L245
	.byte	1,5,41,9
	.half	.L246-.L25
	.byte	1,4,1,5,29,7,9
	.half	.L114-.L246
	.byte	3,178,125,1,5,11,9
	.half	.L247-.L114
	.byte	1,5,29,9
	.half	.L248-.L247
	.byte	3,3,1,5,11,9
	.half	.L249-.L248
	.byte	1,5,29,9
	.half	.L250-.L249
	.byte	3,3,1,5,11,9
	.half	.L251-.L250
	.byte	1,5,53,9
	.half	.L252-.L251
	.byte	3,2,1,4,2,5,25,9
	.half	.L115-.L252
	.byte	3,141,3,1,5,5,9
	.half	.L253-.L115
	.byte	1,5,38,7,9
	.half	.L254-.L253
	.byte	3,5,1,5,45,9
	.half	.L255-.L254
	.byte	3,1,1,5,50,9
	.half	.L256-.L255
	.byte	1,5,69,9
	.half	.L257-.L256
	.byte	3,127,1,5,26,9
	.half	.L258-.L257
	.byte	3,126,1,5,34,9
	.half	.L27-.L258
	.byte	3,9,1,5,41,9
	.half	.L259-.L27
	.byte	3,1,1,5,46,9
	.half	.L260-.L259
	.byte	1,5,65,9
	.half	.L261-.L260
	.byte	3,127,1,5,22,9
	.half	.L262-.L261
	.byte	3,126,1,5,41,9
	.half	.L263-.L262
	.byte	3,6,1,5,28,9
	.half	.L28-.L263
	.byte	1,5,41,9
	.half	.L264-.L28
	.byte	1,4,1,5,5,7,9
	.half	.L116-.L264
	.byte	3,230,124,1,9
	.half	.L265-.L116
	.byte	3,1,1,9
	.half	.L266-.L265
	.byte	3,3,1,9
	.half	.L267-.L266
	.byte	3,1,1,5,30,9
	.half	.L268-.L267
	.byte	3,2,1,5,50,9
	.half	.L269-.L268
	.byte	1,4,3,5,25,9
	.half	.L118-.L269
	.byte	3,212,5,1,5,37,9
	.half	.L152-.L118
	.byte	3,2,1,5,54,9
	.half	.L270-.L152
	.byte	1,5,44,9
	.half	.L271-.L270
	.byte	1,5,66,9
	.half	.L272-.L271
	.byte	1,5,64,9
	.half	.L273-.L272
	.byte	1,5,12,9
	.half	.L153-.L273
	.byte	3,2,1,5,29,9
	.half	.L154-.L153
	.byte	1,5,32,9
	.half	.L31-.L154
	.byte	3,2,1,5,39,9
	.half	.L274-.L31
	.byte	1,5,55,9
	.half	.L275-.L274
	.byte	1,5,72,9
	.half	.L276-.L275
	.byte	1,5,90,9
	.half	.L277-.L276
	.byte	1,5,79,9
	.half	.L278-.L277
	.byte	1,5,97,9
	.half	.L279-.L278
	.byte	1,5,61,9
	.half	.L155-.L279
	.byte	1,5,9,9
	.half	.L280-.L155
	.byte	3,2,1,5,19,7,9
	.half	.L281-.L280
	.byte	3,2,1,5,41,9
	.half	.L282-.L281
	.byte	1,5,21,9
	.half	.L32-.L282
	.byte	3,4,1,5,28,9
	.half	.L33-.L32
	.byte	3,3,1,5,9,9
	.half	.L283-.L33
	.byte	1,5,19,7,9
	.half	.L284-.L283
	.byte	3,2,1,5,17,9
	.half	.L34-.L284
	.byte	3,3,1,5,16,9
	.half	.L285-.L34
	.byte	3,1,1,5,32,9
	.half	.L286-.L285
	.byte	3,109,1,5,29,9
	.half	.L30-.L286
	.byte	1,5,15,7,9
	.half	.L287-.L30
	.byte	3,22,1,5,13,9
	.half	.L288-.L287
	.byte	1,4,1,5,5,9
	.half	.L119-.L288
	.byte	3,149,122,1,5,1,9
	.half	.L289-.L119
	.byte	3,1,1,7,9
	.half	.L45-.L289
	.byte	0,1,1
.L161:
	.sdecl	'.debug_ranges',debug,cluster('_Core1_start')
	.sect	'.debug_ranges'
.L44:
	.word	-1,.L36,0,.L45-.L36,0,0
	.sdecl	'.debug_loc',debug,cluster('_Core1_start')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L46-.L36
	.half	2
	.byte	138,0
	.word	0,0
.L101:
	.word	0,0
.L68:
	.word	0,0
.L121:
	.word	0,0
.L123:
	.word	0,0
.L111:
	.word	-1,.L36,.L148-.L36,.L149-.L36
	.half	1
	.byte	95
	.word	0,0
.L99:
	.word	0,0
.L61:
	.word	0,0
.L125:
	.word	-1,.L36,.L154-.L36,.L46-.L36
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L130:
	.word	-1,.L36,.L153-.L36,.L46-.L36
	.half	1
	.byte	84
	.word	0,0
.L129:
	.word	-1,.L36,.L150-.L36,.L151-.L36
	.half	1
	.byte	111
	.word	0,0
.L126:
	.word	-1,.L36,.L155-.L36,.L30-.L36
	.half	1
	.byte	81
	.word	0,0
.L82:
	.word	0,0
.L75:
	.word	-1,.L36,.L138-.L36,.L139-.L36
	.half	5
	.byte	144,32,157,32,0
	.word	.L139-.L36,.L140-.L36
	.half	1
	.byte	84
	.word	0,0
.L56:
	.word	-1,.L36,.L131-.L36,.L132-.L36
	.half	1
	.byte	95
	.word	.L132-.L36,.L133-.L36
	.half	1
	.byte	81
	.word	0,0
.L105:
	.word	-1,.L36,.L145-.L36,.L146-.L36
	.half	5
	.byte	144,32,157,32,0
	.word	.L146-.L36,.L147-.L36
	.half	1
	.byte	84
	.word	0,0
.L93:
	.word	0,0
.L87:
	.word	-1,.L36,.L141-.L36,.L142-.L36
	.half	1
	.byte	95
	.word	0,0
.L66:
	.word	-1,.L36,.L135-.L36,.L3-.L36
	.half	1
	.byte	95
	.word	0,0
.L48:
	.word	-1,.L36,.L134-.L36,.L135-.L36
	.half	1
	.byte	95
	.word	0,0
.L128:
	.word	-1,.L36,.L152-.L36,.L46-.L36
	.half	1
	.byte	100
	.word	0,0
.L73:
	.word	-1,.L36,.L136-.L36,.L137-.L36
	.half	1
	.byte	95
	.word	0,0
.L103:
	.word	-1,.L36,.L143-.L36,.L144-.L36
	.half	1
	.byte	95
	.word	0,0
.L80:
	.word	0,0
.L54:
	.word	0,0
.L91:
	.word	0,0
.L50:
	.word	0,0
.L102:
	.word	0,0
.L69:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L290:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('_Core1_start')
	.sect	'.debug_frame'
	.word	20
	.word	.L290,.L36,.L46-.L36
	.byte	8,19,8,21,8,22,8,23
	; Module end
