<?xml version="1.0" encoding="UTF-8"?>
<iLLD:filelist driver="IfxDsadc" xmlns:iLLD="http://www.infineon.com/cms/xml/iLLD/1.0/EN">
  <iLLD:class name="mchal"/> <!-- Derivative specific library -->
  <iLLD:class name="srvsw"/> <!-- Service software library -->
  <iLLD:file class="mchal">Scu/Std/IfxScuCcu.c</iLLD:file>
  <iLLD:file class="mchal">Scu/Std/IfxScuWdt.c</iLLD:file>
  <iLLD:file class="mchal">Port/Std/IfxPort.c</iLLD:file>
  <iLLD:file class="mchal">_Impl/IfxPort_cfg.c</iLLD:file>
  <iLLD:file class="mchal">Dsadc/Std/IfxDsadc.c</iLLD:file>
  <iLLD:file class="mchal">Dsadc/Dsadc/IfxDsadc_Dsadc.c</iLLD:file>
  <iLLD:file class="mchal">Dsadc/Rdc/IfxDsadc_Rdc.c</iLLD:file>
  <iLLD:file class="mchal">_PinMap/IfxDsadc_PinMap.c</iLLD:file>
</iLLD:filelist>
