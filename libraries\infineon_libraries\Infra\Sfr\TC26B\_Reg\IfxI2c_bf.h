/**
 * \file IfxI2c_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_I2c_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_I2c
 * 
 */
#ifndef IFXI2C_BF_H
#define IFXI2C_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_I2c_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN0 */
#define IFX_I2C_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN0 */
#define IFX_I2C_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN0 */
#define IFX_I2C_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN10 */
#define IFX_I2C_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN10 */
#define IFX_I2C_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN10 */
#define IFX_I2C_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN11 */
#define IFX_I2C_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN11 */
#define IFX_I2C_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN11 */
#define IFX_I2C_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN12 */
#define IFX_I2C_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN12 */
#define IFX_I2C_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN12 */
#define IFX_I2C_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN13 */
#define IFX_I2C_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN13 */
#define IFX_I2C_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN13 */
#define IFX_I2C_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN14 */
#define IFX_I2C_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN14 */
#define IFX_I2C_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN14 */
#define IFX_I2C_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN15 */
#define IFX_I2C_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN15 */
#define IFX_I2C_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN15 */
#define IFX_I2C_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN16 */
#define IFX_I2C_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN16 */
#define IFX_I2C_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN16 */
#define IFX_I2C_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN17 */
#define IFX_I2C_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN17 */
#define IFX_I2C_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN17 */
#define IFX_I2C_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN18 */
#define IFX_I2C_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN18 */
#define IFX_I2C_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN18 */
#define IFX_I2C_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN19 */
#define IFX_I2C_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN19 */
#define IFX_I2C_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN19 */
#define IFX_I2C_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN1 */
#define IFX_I2C_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN1 */
#define IFX_I2C_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN1 */
#define IFX_I2C_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN20 */
#define IFX_I2C_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN20 */
#define IFX_I2C_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN20 */
#define IFX_I2C_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN21 */
#define IFX_I2C_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN21 */
#define IFX_I2C_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN21 */
#define IFX_I2C_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN22 */
#define IFX_I2C_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN22 */
#define IFX_I2C_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN22 */
#define IFX_I2C_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN23 */
#define IFX_I2C_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN23 */
#define IFX_I2C_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN23 */
#define IFX_I2C_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN24 */
#define IFX_I2C_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN24 */
#define IFX_I2C_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN24 */
#define IFX_I2C_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN25 */
#define IFX_I2C_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN25 */
#define IFX_I2C_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN25 */
#define IFX_I2C_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN26 */
#define IFX_I2C_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN26 */
#define IFX_I2C_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN26 */
#define IFX_I2C_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN27 */
#define IFX_I2C_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN27 */
#define IFX_I2C_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN27 */
#define IFX_I2C_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN28 */
#define IFX_I2C_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN28 */
#define IFX_I2C_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN28 */
#define IFX_I2C_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN29 */
#define IFX_I2C_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN29 */
#define IFX_I2C_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN29 */
#define IFX_I2C_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN2 */
#define IFX_I2C_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN2 */
#define IFX_I2C_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN2 */
#define IFX_I2C_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN30 */
#define IFX_I2C_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN30 */
#define IFX_I2C_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN30 */
#define IFX_I2C_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN31 */
#define IFX_I2C_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN31 */
#define IFX_I2C_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN31 */
#define IFX_I2C_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN3 */
#define IFX_I2C_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN3 */
#define IFX_I2C_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN3 */
#define IFX_I2C_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN4 */
#define IFX_I2C_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN4 */
#define IFX_I2C_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN4 */
#define IFX_I2C_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN5 */
#define IFX_I2C_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN5 */
#define IFX_I2C_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN5 */
#define IFX_I2C_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN6 */
#define IFX_I2C_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN6 */
#define IFX_I2C_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN6 */
#define IFX_I2C_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN7 */
#define IFX_I2C_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN7 */
#define IFX_I2C_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN7 */
#define IFX_I2C_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN8 */
#define IFX_I2C_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN8 */
#define IFX_I2C_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN8 */
#define IFX_I2C_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_I2C_ACCEN0_Bits.EN9 */
#define IFX_I2C_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_I2C_ACCEN0_Bits.EN9 */
#define IFX_I2C_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ACCEN0_Bits.EN9 */
#define IFX_I2C_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.ADR */
#define IFX_I2C_ADDRCFG_ADR_LEN (10u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.ADR */
#define IFX_I2C_ADDRCFG_ADR_MSK (0x3ffu)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.ADR */
#define IFX_I2C_ADDRCFG_ADR_OFF (0u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.GCE */
#define IFX_I2C_ADDRCFG_GCE_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.GCE */
#define IFX_I2C_ADDRCFG_GCE_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.GCE */
#define IFX_I2C_ADDRCFG_GCE_OFF (17u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.MCE */
#define IFX_I2C_ADDRCFG_MCE_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.MCE */
#define IFX_I2C_ADDRCFG_MCE_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.MCE */
#define IFX_I2C_ADDRCFG_MCE_OFF (18u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.MnS */
#define IFX_I2C_ADDRCFG_MNS_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.MnS */
#define IFX_I2C_ADDRCFG_MNS_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.MnS */
#define IFX_I2C_ADDRCFG_MNS_OFF (19u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.SONA */
#define IFX_I2C_ADDRCFG_SONA_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.SONA */
#define IFX_I2C_ADDRCFG_SONA_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.SONA */
#define IFX_I2C_ADDRCFG_SONA_OFF (20u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.SOPE */
#define IFX_I2C_ADDRCFG_SOPE_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.SOPE */
#define IFX_I2C_ADDRCFG_SOPE_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.SOPE */
#define IFX_I2C_ADDRCFG_SOPE_OFF (21u)

/** \brief  Length for Ifx_I2C_ADDRCFG_Bits.TBAM */
#define IFX_I2C_ADDRCFG_TBAM_LEN (1u)

/** \brief  Mask for Ifx_I2C_ADDRCFG_Bits.TBAM */
#define IFX_I2C_ADDRCFG_TBAM_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ADDRCFG_Bits.TBAM */
#define IFX_I2C_ADDRCFG_TBAM_OFF (16u)

/** \brief  Length for Ifx_I2C_BUSSTAT_Bits.BS */
#define IFX_I2C_BUSSTAT_BS_LEN (2u)

/** \brief  Mask for Ifx_I2C_BUSSTAT_Bits.BS */
#define IFX_I2C_BUSSTAT_BS_MSK (0x3u)

/** \brief  Offset for Ifx_I2C_BUSSTAT_Bits.BS */
#define IFX_I2C_BUSSTAT_BS_OFF (0u)

/** \brief  Length for Ifx_I2C_BUSSTAT_Bits.RnW */
#define IFX_I2C_BUSSTAT_RNW_LEN (1u)

/** \brief  Mask for Ifx_I2C_BUSSTAT_Bits.RnW */
#define IFX_I2C_BUSSTAT_RNW_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_BUSSTAT_Bits.RnW */
#define IFX_I2C_BUSSTAT_RNW_OFF (2u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.DISR */
#define IFX_I2C_CLC1_DISR_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.DISR */
#define IFX_I2C_CLC1_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.DISR */
#define IFX_I2C_CLC1_DISR_OFF (0u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.DISS */
#define IFX_I2C_CLC1_DISS_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.DISS */
#define IFX_I2C_CLC1_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.DISS */
#define IFX_I2C_CLC1_DISS_OFF (1u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.EDIS */
#define IFX_I2C_CLC1_EDIS_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.EDIS */
#define IFX_I2C_CLC1_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.EDIS */
#define IFX_I2C_CLC1_EDIS_OFF (3u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.FSOE */
#define IFX_I2C_CLC1_FSOE_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.FSOE */
#define IFX_I2C_CLC1_FSOE_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.FSOE */
#define IFX_I2C_CLC1_FSOE_OFF (5u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.RMC */
#define IFX_I2C_CLC1_RMC_LEN (8u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.RMC */
#define IFX_I2C_CLC1_RMC_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.RMC */
#define IFX_I2C_CLC1_RMC_OFF (8u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.SBWE */
#define IFX_I2C_CLC1_SBWE_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.SBWE */
#define IFX_I2C_CLC1_SBWE_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.SBWE */
#define IFX_I2C_CLC1_SBWE_OFF (4u)

/** \brief  Length for Ifx_I2C_CLC1_Bits.SPEN */
#define IFX_I2C_CLC1_SPEN_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC1_Bits.SPEN */
#define IFX_I2C_CLC1_SPEN_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC1_Bits.SPEN */
#define IFX_I2C_CLC1_SPEN_OFF (2u)

/** \brief  Length for Ifx_I2C_CLC_Bits.DISR */
#define IFX_I2C_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC_Bits.DISR */
#define IFX_I2C_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC_Bits.DISR */
#define IFX_I2C_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_I2C_CLC_Bits.DISS */
#define IFX_I2C_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_I2C_CLC_Bits.DISS */
#define IFX_I2C_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_CLC_Bits.DISS */
#define IFX_I2C_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_I2C_ENDDCTRL_Bits.SETEND */
#define IFX_I2C_ENDDCTRL_SETEND_LEN (1u)

/** \brief  Mask for Ifx_I2C_ENDDCTRL_Bits.SETEND */
#define IFX_I2C_ENDDCTRL_SETEND_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ENDDCTRL_Bits.SETEND */
#define IFX_I2C_ENDDCTRL_SETEND_OFF (1u)

/** \brief  Length for Ifx_I2C_ENDDCTRL_Bits.SETRSC */
#define IFX_I2C_ENDDCTRL_SETRSC_LEN (1u)

/** \brief  Mask for Ifx_I2C_ENDDCTRL_Bits.SETRSC */
#define IFX_I2C_ENDDCTRL_SETRSC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ENDDCTRL_Bits.SETRSC */
#define IFX_I2C_ENDDCTRL_SETRSC_OFF (0u)

/** \brief  Length for Ifx_I2C_ERRIRQSC_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSC_RXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSC_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSC_RXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSC_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSC_RXF_OFL_OFF (1u)

/** \brief  Length for Ifx_I2C_ERRIRQSC_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSC_RXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSC_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSC_RXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSC_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSC_RXF_UFL_OFF (0u)

/** \brief  Length for Ifx_I2C_ERRIRQSC_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSC_TXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSC_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSC_TXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSC_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSC_TXF_OFL_OFF (3u)

/** \brief  Length for Ifx_I2C_ERRIRQSC_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSC_TXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSC_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSC_TXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSC_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSC_TXF_UFL_OFF (2u)

/** \brief  Length for Ifx_I2C_ERRIRQSM_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSM_RXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSM_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSM_RXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSM_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSM_RXF_OFL_OFF (1u)

/** \brief  Length for Ifx_I2C_ERRIRQSM_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSM_RXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSM_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSM_RXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSM_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSM_RXF_UFL_OFF (0u)

/** \brief  Length for Ifx_I2C_ERRIRQSM_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSM_TXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSM_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSM_TXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSM_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSM_TXF_OFL_OFF (3u)

/** \brief  Length for Ifx_I2C_ERRIRQSM_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSM_TXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSM_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSM_TXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSM_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSM_TXF_UFL_OFF (2u)

/** \brief  Length for Ifx_I2C_ERRIRQSS_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSS_RXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSS_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSS_RXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSS_Bits.RXF_OFL */
#define IFX_I2C_ERRIRQSS_RXF_OFL_OFF (1u)

/** \brief  Length for Ifx_I2C_ERRIRQSS_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSS_RXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSS_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSS_RXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSS_Bits.RXF_UFL */
#define IFX_I2C_ERRIRQSS_RXF_UFL_OFF (0u)

/** \brief  Length for Ifx_I2C_ERRIRQSS_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSS_TXF_OFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSS_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSS_TXF_OFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSS_Bits.TXF_OFL */
#define IFX_I2C_ERRIRQSS_TXF_OFL_OFF (3u)

/** \brief  Length for Ifx_I2C_ERRIRQSS_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSS_TXF_UFL_LEN (1u)

/** \brief  Mask for Ifx_I2C_ERRIRQSS_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSS_TXF_UFL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ERRIRQSS_Bits.TXF_UFL */
#define IFX_I2C_ERRIRQSS_TXF_UFL_OFF (2u)

/** \brief  Length for Ifx_I2C_FDIVCFG_Bits.DEC */
#define IFX_I2C_FDIVCFG_DEC_LEN (11u)

/** \brief  Mask for Ifx_I2C_FDIVCFG_Bits.DEC */
#define IFX_I2C_FDIVCFG_DEC_MSK (0x7ffu)

/** \brief  Offset for Ifx_I2C_FDIVCFG_Bits.DEC */
#define IFX_I2C_FDIVCFG_DEC_OFF (0u)

/** \brief  Length for Ifx_I2C_FDIVCFG_Bits.INC */
#define IFX_I2C_FDIVCFG_INC_LEN (8u)

/** \brief  Mask for Ifx_I2C_FDIVCFG_Bits.INC */
#define IFX_I2C_FDIVCFG_INC_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_FDIVCFG_Bits.INC */
#define IFX_I2C_FDIVCFG_INC_OFF (16u)

/** \brief  Length for Ifx_I2C_FDIVHIGHCFG_Bits.DEC */
#define IFX_I2C_FDIVHIGHCFG_DEC_LEN (11u)

/** \brief  Mask for Ifx_I2C_FDIVHIGHCFG_Bits.DEC */
#define IFX_I2C_FDIVHIGHCFG_DEC_MSK (0x7ffu)

/** \brief  Offset for Ifx_I2C_FDIVHIGHCFG_Bits.DEC */
#define IFX_I2C_FDIVHIGHCFG_DEC_OFF (0u)

/** \brief  Length for Ifx_I2C_FDIVHIGHCFG_Bits.INC */
#define IFX_I2C_FDIVHIGHCFG_INC_LEN (8u)

/** \brief  Mask for Ifx_I2C_FDIVHIGHCFG_Bits.INC */
#define IFX_I2C_FDIVHIGHCFG_INC_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_FDIVHIGHCFG_Bits.INC */
#define IFX_I2C_FDIVHIGHCFG_INC_OFF (16u)

/** \brief  Length for Ifx_I2C_FFSSTAT_Bits.FFS */
#define IFX_I2C_FFSSTAT_FFS_LEN (6u)

/** \brief  Mask for Ifx_I2C_FFSSTAT_Bits.FFS */
#define IFX_I2C_FFSSTAT_FFS_MSK (0x3fu)

/** \brief  Offset for Ifx_I2C_FFSSTAT_Bits.FFS */
#define IFX_I2C_FFSSTAT_FFS_OFF (0u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.RXBS */
#define IFX_I2C_FIFOCFG_RXBS_LEN (2u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.RXBS */
#define IFX_I2C_FIFOCFG_RXBS_MSK (0x3u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.RXBS */
#define IFX_I2C_FIFOCFG_RXBS_OFF (0u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.RXFA */
#define IFX_I2C_FIFOCFG_RXFA_LEN (2u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.RXFA */
#define IFX_I2C_FIFOCFG_RXFA_MSK (0x3u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.RXFA */
#define IFX_I2C_FIFOCFG_RXFA_OFF (8u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.RXFC */
#define IFX_I2C_FIFOCFG_RXFC_LEN (1u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.RXFC */
#define IFX_I2C_FIFOCFG_RXFC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.RXFC */
#define IFX_I2C_FIFOCFG_RXFC_OFF (16u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.TXBS */
#define IFX_I2C_FIFOCFG_TXBS_LEN (2u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.TXBS */
#define IFX_I2C_FIFOCFG_TXBS_MSK (0x3u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.TXBS */
#define IFX_I2C_FIFOCFG_TXBS_OFF (4u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.TXFA */
#define IFX_I2C_FIFOCFG_TXFA_LEN (2u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.TXFA */
#define IFX_I2C_FIFOCFG_TXFA_MSK (0x3u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.TXFA */
#define IFX_I2C_FIFOCFG_TXFA_OFF (12u)

/** \brief  Length for Ifx_I2C_FIFOCFG_Bits.TXFC */
#define IFX_I2C_FIFOCFG_TXFC_LEN (1u)

/** \brief  Mask for Ifx_I2C_FIFOCFG_Bits.TXFC */
#define IFX_I2C_FIFOCFG_TXFC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_FIFOCFG_Bits.TXFC */
#define IFX_I2C_FIFOCFG_TXFC_OFF (17u)

/** \brief  Length for Ifx_I2C_GPCTL_Bits.PISEL */
#define IFX_I2C_GPCTL_PISEL_LEN (3u)

/** \brief  Mask for Ifx_I2C_GPCTL_Bits.PISEL */
#define IFX_I2C_GPCTL_PISEL_MSK (0x7u)

/** \brief  Offset for Ifx_I2C_GPCTL_Bits.PISEL */
#define IFX_I2C_GPCTL_PISEL_OFF (0u)

/** \brief  Length for Ifx_I2C_ICR_Bits.BREQ_INT */
#define IFX_I2C_ICR_BREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ICR_Bits.BREQ_INT */
#define IFX_I2C_ICR_BREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ICR_Bits.BREQ_INT */
#define IFX_I2C_ICR_BREQ_INT_OFF (3u)

/** \brief  Length for Ifx_I2C_ICR_Bits.LBREQ_INT */
#define IFX_I2C_ICR_LBREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ICR_Bits.LBREQ_INT */
#define IFX_I2C_ICR_LBREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ICR_Bits.LBREQ_INT */
#define IFX_I2C_ICR_LBREQ_INT_OFF (2u)

/** \brief  Length for Ifx_I2C_ICR_Bits.LSREQ_INT */
#define IFX_I2C_ICR_LSREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ICR_Bits.LSREQ_INT */
#define IFX_I2C_ICR_LSREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ICR_Bits.LSREQ_INT */
#define IFX_I2C_ICR_LSREQ_INT_OFF (0u)

/** \brief  Length for Ifx_I2C_ICR_Bits.SREQ_INT */
#define IFX_I2C_ICR_SREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ICR_Bits.SREQ_INT */
#define IFX_I2C_ICR_SREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ICR_Bits.SREQ_INT */
#define IFX_I2C_ICR_SREQ_INT_OFF (1u)

/** \brief  Length for Ifx_I2C_ID_Bits.MODNUMBER */
#define IFX_I2C_ID_MODNUMBER_LEN (8u)

/** \brief  Mask for Ifx_I2C_ID_Bits.MODNUMBER */
#define IFX_I2C_ID_MODNUMBER_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_ID_Bits.MODNUMBER */
#define IFX_I2C_ID_MODNUMBER_OFF (8u)

/** \brief  Length for Ifx_I2C_ID_Bits.MODREV */
#define IFX_I2C_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_I2C_ID_Bits.MODREV */
#define IFX_I2C_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_ID_Bits.MODREV */
#define IFX_I2C_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.BREQ_INT */
#define IFX_I2C_IMSC_BREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.BREQ_INT */
#define IFX_I2C_IMSC_BREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.BREQ_INT */
#define IFX_I2C_IMSC_BREQ_INT_OFF (3u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.I2C_ERR_INT */
#define IFX_I2C_IMSC_I2C_ERR_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.I2C_ERR_INT */
#define IFX_I2C_IMSC_I2C_ERR_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.I2C_ERR_INT */
#define IFX_I2C_IMSC_I2C_ERR_INT_OFF (4u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.I2C_P_INT */
#define IFX_I2C_IMSC_I2C_P_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.I2C_P_INT */
#define IFX_I2C_IMSC_I2C_P_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.I2C_P_INT */
#define IFX_I2C_IMSC_I2C_P_INT_OFF (5u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.LBREQ_INT */
#define IFX_I2C_IMSC_LBREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.LBREQ_INT */
#define IFX_I2C_IMSC_LBREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.LBREQ_INT */
#define IFX_I2C_IMSC_LBREQ_INT_OFF (2u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.LSREQ_INT */
#define IFX_I2C_IMSC_LSREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.LSREQ_INT */
#define IFX_I2C_IMSC_LSREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.LSREQ_INT */
#define IFX_I2C_IMSC_LSREQ_INT_OFF (0u)

/** \brief  Length for Ifx_I2C_IMSC_Bits.SREQ_INT */
#define IFX_I2C_IMSC_SREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_IMSC_Bits.SREQ_INT */
#define IFX_I2C_IMSC_SREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_IMSC_Bits.SREQ_INT */
#define IFX_I2C_IMSC_SREQ_INT_OFF (1u)

/** \brief  Length for Ifx_I2C_ISR_Bits.BREQ_INT */
#define IFX_I2C_ISR_BREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.BREQ_INT */
#define IFX_I2C_ISR_BREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.BREQ_INT */
#define IFX_I2C_ISR_BREQ_INT_OFF (3u)

/** \brief  Length for Ifx_I2C_ISR_Bits.I2C_ERR_INT */
#define IFX_I2C_ISR_I2C_ERR_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.I2C_ERR_INT */
#define IFX_I2C_ISR_I2C_ERR_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.I2C_ERR_INT */
#define IFX_I2C_ISR_I2C_ERR_INT_OFF (4u)

/** \brief  Length for Ifx_I2C_ISR_Bits.I2C_P_INT */
#define IFX_I2C_ISR_I2C_P_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.I2C_P_INT */
#define IFX_I2C_ISR_I2C_P_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.I2C_P_INT */
#define IFX_I2C_ISR_I2C_P_INT_OFF (5u)

/** \brief  Length for Ifx_I2C_ISR_Bits.LBREQ_INT */
#define IFX_I2C_ISR_LBREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.LBREQ_INT */
#define IFX_I2C_ISR_LBREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.LBREQ_INT */
#define IFX_I2C_ISR_LBREQ_INT_OFF (2u)

/** \brief  Length for Ifx_I2C_ISR_Bits.LSREQ_INT */
#define IFX_I2C_ISR_LSREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.LSREQ_INT */
#define IFX_I2C_ISR_LSREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.LSREQ_INT */
#define IFX_I2C_ISR_LSREQ_INT_OFF (0u)

/** \brief  Length for Ifx_I2C_ISR_Bits.SREQ_INT */
#define IFX_I2C_ISR_SREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_ISR_Bits.SREQ_INT */
#define IFX_I2C_ISR_SREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_ISR_Bits.SREQ_INT */
#define IFX_I2C_ISR_SREQ_INT_OFF (1u)

/** \brief  Length for Ifx_I2C_KRST0_Bits.RST */
#define IFX_I2C_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_I2C_KRST0_Bits.RST */
#define IFX_I2C_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_KRST0_Bits.RST */
#define IFX_I2C_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_I2C_KRST0_Bits.RSTSTAT */
#define IFX_I2C_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_I2C_KRST0_Bits.RSTSTAT */
#define IFX_I2C_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_KRST0_Bits.RSTSTAT */
#define IFX_I2C_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_I2C_KRST1_Bits.RST */
#define IFX_I2C_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_I2C_KRST1_Bits.RST */
#define IFX_I2C_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_KRST1_Bits.RST */
#define IFX_I2C_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_I2C_KRSTCLR_Bits.CLR */
#define IFX_I2C_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_I2C_KRSTCLR_Bits.CLR */
#define IFX_I2C_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_KRSTCLR_Bits.CLR */
#define IFX_I2C_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_I2C_MIS_Bits.BREQ_INT */
#define IFX_I2C_MIS_BREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.BREQ_INT */
#define IFX_I2C_MIS_BREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.BREQ_INT */
#define IFX_I2C_MIS_BREQ_INT_OFF (3u)

/** \brief  Length for Ifx_I2C_MIS_Bits.I2C_ERR_INT */
#define IFX_I2C_MIS_I2C_ERR_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.I2C_ERR_INT */
#define IFX_I2C_MIS_I2C_ERR_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.I2C_ERR_INT */
#define IFX_I2C_MIS_I2C_ERR_INT_OFF (4u)

/** \brief  Length for Ifx_I2C_MIS_Bits.I2C_P_INT */
#define IFX_I2C_MIS_I2C_P_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.I2C_P_INT */
#define IFX_I2C_MIS_I2C_P_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.I2C_P_INT */
#define IFX_I2C_MIS_I2C_P_INT_OFF (5u)

/** \brief  Length for Ifx_I2C_MIS_Bits.LBREQ_INT */
#define IFX_I2C_MIS_LBREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.LBREQ_INT */
#define IFX_I2C_MIS_LBREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.LBREQ_INT */
#define IFX_I2C_MIS_LBREQ_INT_OFF (2u)

/** \brief  Length for Ifx_I2C_MIS_Bits.LSREQ_INT */
#define IFX_I2C_MIS_LSREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.LSREQ_INT */
#define IFX_I2C_MIS_LSREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.LSREQ_INT */
#define IFX_I2C_MIS_LSREQ_INT_OFF (0u)

/** \brief  Length for Ifx_I2C_MIS_Bits.SREQ_INT */
#define IFX_I2C_MIS_SREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_MIS_Bits.SREQ_INT */
#define IFX_I2C_MIS_SREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_MIS_Bits.SREQ_INT */
#define IFX_I2C_MIS_SREQ_INT_OFF (1u)

/** \brief  Length for Ifx_I2C_MODID_Bits.MODNUMBER */
#define IFX_I2C_MODID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_I2C_MODID_Bits.MODNUMBER */
#define IFX_I2C_MODID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_I2C_MODID_Bits.MODNUMBER */
#define IFX_I2C_MODID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_I2C_MODID_Bits.MODREV */
#define IFX_I2C_MODID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_I2C_MODID_Bits.MODREV */
#define IFX_I2C_MODID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_MODID_Bits.MODREV */
#define IFX_I2C_MODID_MODREV_OFF (0u)

/** \brief  Length for Ifx_I2C_MODID_Bits.MODTYPE */
#define IFX_I2C_MODID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_I2C_MODID_Bits.MODTYPE */
#define IFX_I2C_MODID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_MODID_Bits.MODTYPE */
#define IFX_I2C_MODID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_I2C_MRPSCTRL_Bits.MRPS */
#define IFX_I2C_MRPSCTRL_MRPS_LEN (14u)

/** \brief  Mask for Ifx_I2C_MRPSCTRL_Bits.MRPS */
#define IFX_I2C_MRPSCTRL_MRPS_MSK (0x3fffu)

/** \brief  Offset for Ifx_I2C_MRPSCTRL_Bits.MRPS */
#define IFX_I2C_MRPSCTRL_MRPS_OFF (0u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.AL */
#define IFX_I2C_PIRQSC_AL_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.AL */
#define IFX_I2C_PIRQSC_AL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.AL */
#define IFX_I2C_PIRQSC_AL_OFF (3u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.AM */
#define IFX_I2C_PIRQSC_AM_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.AM */
#define IFX_I2C_PIRQSC_AM_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.AM */
#define IFX_I2C_PIRQSC_AM_OFF (0u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.GC */
#define IFX_I2C_PIRQSC_GC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.GC */
#define IFX_I2C_PIRQSC_GC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.GC */
#define IFX_I2C_PIRQSC_GC_OFF (1u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.MC */
#define IFX_I2C_PIRQSC_MC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.MC */
#define IFX_I2C_PIRQSC_MC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.MC */
#define IFX_I2C_PIRQSC_MC_OFF (2u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.NACK */
#define IFX_I2C_PIRQSC_NACK_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.NACK */
#define IFX_I2C_PIRQSC_NACK_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.NACK */
#define IFX_I2C_PIRQSC_NACK_OFF (4u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.RX */
#define IFX_I2C_PIRQSC_RX_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.RX */
#define IFX_I2C_PIRQSC_RX_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.RX */
#define IFX_I2C_PIRQSC_RX_OFF (6u)

/** \brief  Length for Ifx_I2C_PIRQSC_Bits.TX_END */
#define IFX_I2C_PIRQSC_TX_END_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSC_Bits.TX_END */
#define IFX_I2C_PIRQSC_TX_END_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSC_Bits.TX_END */
#define IFX_I2C_PIRQSC_TX_END_OFF (5u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.AL */
#define IFX_I2C_PIRQSM_AL_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.AL */
#define IFX_I2C_PIRQSM_AL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.AL */
#define IFX_I2C_PIRQSM_AL_OFF (3u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.AM */
#define IFX_I2C_PIRQSM_AM_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.AM */
#define IFX_I2C_PIRQSM_AM_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.AM */
#define IFX_I2C_PIRQSM_AM_OFF (0u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.GC */
#define IFX_I2C_PIRQSM_GC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.GC */
#define IFX_I2C_PIRQSM_GC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.GC */
#define IFX_I2C_PIRQSM_GC_OFF (1u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.MC */
#define IFX_I2C_PIRQSM_MC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.MC */
#define IFX_I2C_PIRQSM_MC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.MC */
#define IFX_I2C_PIRQSM_MC_OFF (2u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.NACK */
#define IFX_I2C_PIRQSM_NACK_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.NACK */
#define IFX_I2C_PIRQSM_NACK_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.NACK */
#define IFX_I2C_PIRQSM_NACK_OFF (4u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.RX */
#define IFX_I2C_PIRQSM_RX_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.RX */
#define IFX_I2C_PIRQSM_RX_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.RX */
#define IFX_I2C_PIRQSM_RX_OFF (6u)

/** \brief  Length for Ifx_I2C_PIRQSM_Bits.TX_END */
#define IFX_I2C_PIRQSM_TX_END_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSM_Bits.TX_END */
#define IFX_I2C_PIRQSM_TX_END_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSM_Bits.TX_END */
#define IFX_I2C_PIRQSM_TX_END_OFF (5u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.AL */
#define IFX_I2C_PIRQSS_AL_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.AL */
#define IFX_I2C_PIRQSS_AL_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.AL */
#define IFX_I2C_PIRQSS_AL_OFF (3u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.AM */
#define IFX_I2C_PIRQSS_AM_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.AM */
#define IFX_I2C_PIRQSS_AM_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.AM */
#define IFX_I2C_PIRQSS_AM_OFF (0u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.GC */
#define IFX_I2C_PIRQSS_GC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.GC */
#define IFX_I2C_PIRQSS_GC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.GC */
#define IFX_I2C_PIRQSS_GC_OFF (1u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.MC */
#define IFX_I2C_PIRQSS_MC_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.MC */
#define IFX_I2C_PIRQSS_MC_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.MC */
#define IFX_I2C_PIRQSS_MC_OFF (2u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.NACK */
#define IFX_I2C_PIRQSS_NACK_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.NACK */
#define IFX_I2C_PIRQSS_NACK_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.NACK */
#define IFX_I2C_PIRQSS_NACK_OFF (4u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.RX */
#define IFX_I2C_PIRQSS_RX_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.RX */
#define IFX_I2C_PIRQSS_RX_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.RX */
#define IFX_I2C_PIRQSS_RX_OFF (6u)

/** \brief  Length for Ifx_I2C_PIRQSS_Bits.TX_END */
#define IFX_I2C_PIRQSS_TX_END_LEN (1u)

/** \brief  Mask for Ifx_I2C_PIRQSS_Bits.TX_END */
#define IFX_I2C_PIRQSS_TX_END_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_PIRQSS_Bits.TX_END */
#define IFX_I2C_PIRQSS_TX_END_OFF (5u)

/** \brief  Length for Ifx_I2C_RIS_Bits.BREQ_INT */
#define IFX_I2C_RIS_BREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.BREQ_INT */
#define IFX_I2C_RIS_BREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.BREQ_INT */
#define IFX_I2C_RIS_BREQ_INT_OFF (3u)

/** \brief  Length for Ifx_I2C_RIS_Bits.I2C_ERR_INT */
#define IFX_I2C_RIS_I2C_ERR_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.I2C_ERR_INT */
#define IFX_I2C_RIS_I2C_ERR_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.I2C_ERR_INT */
#define IFX_I2C_RIS_I2C_ERR_INT_OFF (4u)

/** \brief  Length for Ifx_I2C_RIS_Bits.I2C_P_INT */
#define IFX_I2C_RIS_I2C_P_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.I2C_P_INT */
#define IFX_I2C_RIS_I2C_P_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.I2C_P_INT */
#define IFX_I2C_RIS_I2C_P_INT_OFF (5u)

/** \brief  Length for Ifx_I2C_RIS_Bits.LBREQ_INT */
#define IFX_I2C_RIS_LBREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.LBREQ_INT */
#define IFX_I2C_RIS_LBREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.LBREQ_INT */
#define IFX_I2C_RIS_LBREQ_INT_OFF (2u)

/** \brief  Length for Ifx_I2C_RIS_Bits.LSREQ_INT */
#define IFX_I2C_RIS_LSREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.LSREQ_INT */
#define IFX_I2C_RIS_LSREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.LSREQ_INT */
#define IFX_I2C_RIS_LSREQ_INT_OFF (0u)

/** \brief  Length for Ifx_I2C_RIS_Bits.SREQ_INT */
#define IFX_I2C_RIS_SREQ_INT_LEN (1u)

/** \brief  Mask for Ifx_I2C_RIS_Bits.SREQ_INT */
#define IFX_I2C_RIS_SREQ_INT_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RIS_Bits.SREQ_INT */
#define IFX_I2C_RIS_SREQ_INT_OFF (1u)

/** \brief  Length for Ifx_I2C_RPSSTAT_Bits.RPS */
#define IFX_I2C_RPSSTAT_RPS_LEN (14u)

/** \brief  Mask for Ifx_I2C_RPSSTAT_Bits.RPS */
#define IFX_I2C_RPSSTAT_RPS_MSK (0x3fffu)

/** \brief  Offset for Ifx_I2C_RPSSTAT_Bits.RPS */
#define IFX_I2C_RPSSTAT_RPS_OFF (0u)

/** \brief  Length for Ifx_I2C_RUNCTRL_Bits.RUN */
#define IFX_I2C_RUNCTRL_RUN_LEN (1u)

/** \brief  Mask for Ifx_I2C_RUNCTRL_Bits.RUN */
#define IFX_I2C_RUNCTRL_RUN_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_RUNCTRL_Bits.RUN */
#define IFX_I2C_RUNCTRL_RUN_OFF (0u)

/** \brief  Length for Ifx_I2C_RXD_Bits.RXD */
#define IFX_I2C_RXD_RXD_LEN (32u)

/** \brief  Mask for Ifx_I2C_RXD_Bits.RXD */
#define IFX_I2C_RXD_RXD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_I2C_RXD_Bits.RXD */
#define IFX_I2C_RXD_RXD_OFF (0u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.EN_SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_EN_SCL_LOW_LEN_LEN (1u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.EN_SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_EN_SCL_LOW_LEN_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.EN_SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_EN_SCL_LOW_LEN_OFF (14u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.FS_SCL_LOW */
#define IFX_I2C_TIMCFG_FS_SCL_LOW_LEN (1u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.FS_SCL_LOW */
#define IFX_I2C_TIMCFG_FS_SCL_LOW_MSK (0x1u)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.FS_SCL_LOW */
#define IFX_I2C_TIMCFG_FS_SCL_LOW_OFF (15u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_HD_DAT_LEN (3u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_HD_DAT_MSK (0x7u)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_HD_DAT_OFF (6u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_LEN (3u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_MSK (0x7u)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.HS_SDA_DEL */
#define IFX_I2C_TIMCFG_HS_SDA_DEL_OFF (16u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.SCL_DEL_HD_STA */
#define IFX_I2C_TIMCFG_SCL_DEL_HD_STA_LEN (3u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.SCL_DEL_HD_STA */
#define IFX_I2C_TIMCFG_SCL_DEL_HD_STA_MSK (0x7u)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.SCL_DEL_HD_STA */
#define IFX_I2C_TIMCFG_SCL_DEL_HD_STA_OFF (9u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_SCL_LOW_LEN_LEN (8u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_SCL_LOW_LEN_MSK (0xffu)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.SCL_LOW_LEN */
#define IFX_I2C_TIMCFG_SCL_LOW_LEN_OFF (24u)

/** \brief  Length for Ifx_I2C_TIMCFG_Bits.SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_SDA_DEL_HD_DAT_LEN (6u)

/** \brief  Mask for Ifx_I2C_TIMCFG_Bits.SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_SDA_DEL_HD_DAT_MSK (0x3fu)

/** \brief  Offset for Ifx_I2C_TIMCFG_Bits.SDA_DEL_HD_DAT */
#define IFX_I2C_TIMCFG_SDA_DEL_HD_DAT_OFF (0u)

/** \brief  Length for Ifx_I2C_TPSCTRL_Bits.TPS */
#define IFX_I2C_TPSCTRL_TPS_LEN (14u)

/** \brief  Mask for Ifx_I2C_TPSCTRL_Bits.TPS */
#define IFX_I2C_TPSCTRL_TPS_MSK (0x3fffu)

/** \brief  Offset for Ifx_I2C_TPSCTRL_Bits.TPS */
#define IFX_I2C_TPSCTRL_TPS_OFF (0u)

/** \brief  Length for Ifx_I2C_TXD_Bits.TXD */
#define IFX_I2C_TXD_TXD_LEN (32u)

/** \brief  Mask for Ifx_I2C_TXD_Bits.TXD */
#define IFX_I2C_TXD_TXD_MSK (0xffffffffu)

/** \brief  Offset for Ifx_I2C_TXD_Bits.TXD */
#define IFX_I2C_TXD_TXD_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXI2C_BF_H */
