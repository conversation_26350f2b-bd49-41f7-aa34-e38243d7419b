/**
 * \file IfxVadc_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Vadc_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Vadc
 * 
 */
#ifndef IFXVADC_BF_H
#define IFXVADC_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Vadc_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN0 */
#define IFX_VADC_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN0 */
#define IFX_VADC_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN0 */
#define IFX_VADC_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN10 */
#define IFX_VADC_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN10 */
#define IFX_VADC_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN10 */
#define IFX_VADC_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN11 */
#define IFX_VADC_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN11 */
#define IFX_VADC_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN11 */
#define IFX_VADC_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN12 */
#define IFX_VADC_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN12 */
#define IFX_VADC_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN12 */
#define IFX_VADC_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN13 */
#define IFX_VADC_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN13 */
#define IFX_VADC_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN13 */
#define IFX_VADC_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN14 */
#define IFX_VADC_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN14 */
#define IFX_VADC_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN14 */
#define IFX_VADC_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN15 */
#define IFX_VADC_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN15 */
#define IFX_VADC_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN15 */
#define IFX_VADC_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN16 */
#define IFX_VADC_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN16 */
#define IFX_VADC_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN16 */
#define IFX_VADC_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN17 */
#define IFX_VADC_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN17 */
#define IFX_VADC_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN17 */
#define IFX_VADC_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN18 */
#define IFX_VADC_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN18 */
#define IFX_VADC_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN18 */
#define IFX_VADC_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN19 */
#define IFX_VADC_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN19 */
#define IFX_VADC_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN19 */
#define IFX_VADC_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN1 */
#define IFX_VADC_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN1 */
#define IFX_VADC_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN1 */
#define IFX_VADC_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN20 */
#define IFX_VADC_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN20 */
#define IFX_VADC_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN20 */
#define IFX_VADC_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN21 */
#define IFX_VADC_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN21 */
#define IFX_VADC_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN21 */
#define IFX_VADC_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN22 */
#define IFX_VADC_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN22 */
#define IFX_VADC_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN22 */
#define IFX_VADC_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN23 */
#define IFX_VADC_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN23 */
#define IFX_VADC_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN23 */
#define IFX_VADC_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN24 */
#define IFX_VADC_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN24 */
#define IFX_VADC_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN24 */
#define IFX_VADC_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN25 */
#define IFX_VADC_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN25 */
#define IFX_VADC_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN25 */
#define IFX_VADC_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN26 */
#define IFX_VADC_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN26 */
#define IFX_VADC_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN26 */
#define IFX_VADC_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN27 */
#define IFX_VADC_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN27 */
#define IFX_VADC_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN27 */
#define IFX_VADC_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN28 */
#define IFX_VADC_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN28 */
#define IFX_VADC_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN28 */
#define IFX_VADC_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN29 */
#define IFX_VADC_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN29 */
#define IFX_VADC_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN29 */
#define IFX_VADC_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN2 */
#define IFX_VADC_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN2 */
#define IFX_VADC_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN2 */
#define IFX_VADC_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN30 */
#define IFX_VADC_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN30 */
#define IFX_VADC_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN30 */
#define IFX_VADC_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN31 */
#define IFX_VADC_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN31 */
#define IFX_VADC_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN31 */
#define IFX_VADC_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN3 */
#define IFX_VADC_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN3 */
#define IFX_VADC_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN3 */
#define IFX_VADC_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN4 */
#define IFX_VADC_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN4 */
#define IFX_VADC_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN4 */
#define IFX_VADC_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN5 */
#define IFX_VADC_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN5 */
#define IFX_VADC_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN5 */
#define IFX_VADC_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN6 */
#define IFX_VADC_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN6 */
#define IFX_VADC_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN6 */
#define IFX_VADC_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN7 */
#define IFX_VADC_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN7 */
#define IFX_VADC_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN7 */
#define IFX_VADC_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN8 */
#define IFX_VADC_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN8 */
#define IFX_VADC_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN8 */
#define IFX_VADC_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_VADC_ACCEN0_Bits.EN9 */
#define IFX_VADC_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCEN0_Bits.EN9 */
#define IFX_VADC_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCEN0_Bits.EN9 */
#define IFX_VADC_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APC0 */
#define IFX_VADC_ACCPROT0_APC0_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APC0 */
#define IFX_VADC_ACCPROT0_APC0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APC0 */
#define IFX_VADC_ACCPROT0_APC0_OFF (0u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APC1 */
#define IFX_VADC_ACCPROT0_APC1_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APC1 */
#define IFX_VADC_ACCPROT0_APC1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APC1 */
#define IFX_VADC_ACCPROT0_APC1_OFF (1u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APC2 */
#define IFX_VADC_ACCPROT0_APC2_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APC2 */
#define IFX_VADC_ACCPROT0_APC2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APC2 */
#define IFX_VADC_ACCPROT0_APC2_OFF (2u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APC3 */
#define IFX_VADC_ACCPROT0_APC3_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APC3 */
#define IFX_VADC_ACCPROT0_APC3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APC3 */
#define IFX_VADC_ACCPROT0_APC3_OFF (3u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APEM */
#define IFX_VADC_ACCPROT0_APEM_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APEM */
#define IFX_VADC_ACCPROT0_APEM_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APEM */
#define IFX_VADC_ACCPROT0_APEM_OFF (15u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.APGC */
#define IFX_VADC_ACCPROT0_APGC_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.APGC */
#define IFX_VADC_ACCPROT0_APGC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.APGC */
#define IFX_VADC_ACCPROT0_APGC_OFF (31u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.API0 */
#define IFX_VADC_ACCPROT0_API0_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.API0 */
#define IFX_VADC_ACCPROT0_API0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.API0 */
#define IFX_VADC_ACCPROT0_API0_OFF (16u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.API1 */
#define IFX_VADC_ACCPROT0_API1_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.API1 */
#define IFX_VADC_ACCPROT0_API1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.API1 */
#define IFX_VADC_ACCPROT0_API1_OFF (17u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.API2 */
#define IFX_VADC_ACCPROT0_API2_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.API2 */
#define IFX_VADC_ACCPROT0_API2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.API2 */
#define IFX_VADC_ACCPROT0_API2_OFF (18u)

/** \brief  Length for Ifx_VADC_ACCPROT0_Bits.API3 */
#define IFX_VADC_ACCPROT0_API3_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT0_Bits.API3 */
#define IFX_VADC_ACCPROT0_API3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT0_Bits.API3 */
#define IFX_VADC_ACCPROT0_API3_OFF (19u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APR0 */
#define IFX_VADC_ACCPROT1_APR0_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APR0 */
#define IFX_VADC_ACCPROT1_APR0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APR0 */
#define IFX_VADC_ACCPROT1_APR0_OFF (16u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APR1 */
#define IFX_VADC_ACCPROT1_APR1_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APR1 */
#define IFX_VADC_ACCPROT1_APR1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APR1 */
#define IFX_VADC_ACCPROT1_APR1_OFF (17u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APR2 */
#define IFX_VADC_ACCPROT1_APR2_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APR2 */
#define IFX_VADC_ACCPROT1_APR2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APR2 */
#define IFX_VADC_ACCPROT1_APR2_OFF (18u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APR3 */
#define IFX_VADC_ACCPROT1_APR3_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APR3 */
#define IFX_VADC_ACCPROT1_APR3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APR3 */
#define IFX_VADC_ACCPROT1_APR3_OFF (19u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APS0 */
#define IFX_VADC_ACCPROT1_APS0_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APS0 */
#define IFX_VADC_ACCPROT1_APS0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APS0 */
#define IFX_VADC_ACCPROT1_APS0_OFF (0u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APS1 */
#define IFX_VADC_ACCPROT1_APS1_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APS1 */
#define IFX_VADC_ACCPROT1_APS1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APS1 */
#define IFX_VADC_ACCPROT1_APS1_OFF (1u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APS2 */
#define IFX_VADC_ACCPROT1_APS2_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APS2 */
#define IFX_VADC_ACCPROT1_APS2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APS2 */
#define IFX_VADC_ACCPROT1_APS2_OFF (2u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APS3 */
#define IFX_VADC_ACCPROT1_APS3_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APS3 */
#define IFX_VADC_ACCPROT1_APS3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APS3 */
#define IFX_VADC_ACCPROT1_APS3_OFF (3u)

/** \brief  Length for Ifx_VADC_ACCPROT1_Bits.APTF */
#define IFX_VADC_ACCPROT1_APTF_LEN (1u)

/** \brief  Mask for Ifx_VADC_ACCPROT1_Bits.APTF */
#define IFX_VADC_ACCPROT1_APTF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_ACCPROT1_Bits.APTF */
#define IFX_VADC_ACCPROT1_APTF_OFF (15u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.GTLVL */
#define IFX_VADC_BRSCTRL_GTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.GTLVL */
#define IFX_VADC_BRSCTRL_GTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.GTLVL */
#define IFX_VADC_BRSCTRL_GTLVL_OFF (20u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.GTSEL */
#define IFX_VADC_BRSCTRL_GTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.GTSEL */
#define IFX_VADC_BRSCTRL_GTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.GTSEL */
#define IFX_VADC_BRSCTRL_GTSEL_OFF (16u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.GTWC */
#define IFX_VADC_BRSCTRL_GTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.GTWC */
#define IFX_VADC_BRSCTRL_GTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.GTWC */
#define IFX_VADC_BRSCTRL_GTWC_OFF (23u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.SRCRESREG */
#define IFX_VADC_BRSCTRL_SRCRESREG_LEN (4u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.SRCRESREG */
#define IFX_VADC_BRSCTRL_SRCRESREG_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.SRCRESREG */
#define IFX_VADC_BRSCTRL_SRCRESREG_OFF (0u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.XTLVL */
#define IFX_VADC_BRSCTRL_XTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.XTLVL */
#define IFX_VADC_BRSCTRL_XTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.XTLVL */
#define IFX_VADC_BRSCTRL_XTLVL_OFF (12u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.XTMODE */
#define IFX_VADC_BRSCTRL_XTMODE_LEN (2u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.XTMODE */
#define IFX_VADC_BRSCTRL_XTMODE_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.XTMODE */
#define IFX_VADC_BRSCTRL_XTMODE_OFF (13u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.XTSEL */
#define IFX_VADC_BRSCTRL_XTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.XTSEL */
#define IFX_VADC_BRSCTRL_XTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.XTSEL */
#define IFX_VADC_BRSCTRL_XTSEL_OFF (8u)

/** \brief  Length for Ifx_VADC_BRSCTRL_Bits.XTWC */
#define IFX_VADC_BRSCTRL_XTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSCTRL_Bits.XTWC */
#define IFX_VADC_BRSCTRL_XTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSCTRL_Bits.XTWC */
#define IFX_VADC_BRSCTRL_XTWC_OFF (15u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.CLRPND */
#define IFX_VADC_BRSMR_CLRPND_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.CLRPND */
#define IFX_VADC_BRSMR_CLRPND_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.CLRPND */
#define IFX_VADC_BRSMR_CLRPND_OFF (8u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.ENGT */
#define IFX_VADC_BRSMR_ENGT_LEN (2u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.ENGT */
#define IFX_VADC_BRSMR_ENGT_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.ENGT */
#define IFX_VADC_BRSMR_ENGT_OFF (0u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.ENSI */
#define IFX_VADC_BRSMR_ENSI_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.ENSI */
#define IFX_VADC_BRSMR_ENSI_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.ENSI */
#define IFX_VADC_BRSMR_ENSI_OFF (3u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.ENTR */
#define IFX_VADC_BRSMR_ENTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.ENTR */
#define IFX_VADC_BRSMR_ENTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.ENTR */
#define IFX_VADC_BRSMR_ENTR_OFF (2u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.LDEV */
#define IFX_VADC_BRSMR_LDEV_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.LDEV */
#define IFX_VADC_BRSMR_LDEV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.LDEV */
#define IFX_VADC_BRSMR_LDEV_OFF (9u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.LDM */
#define IFX_VADC_BRSMR_LDM_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.LDM */
#define IFX_VADC_BRSMR_LDM_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.LDM */
#define IFX_VADC_BRSMR_LDM_OFF (5u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.REQGT */
#define IFX_VADC_BRSMR_REQGT_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.REQGT */
#define IFX_VADC_BRSMR_REQGT_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.REQGT */
#define IFX_VADC_BRSMR_REQGT_OFF (7u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.RPTDIS */
#define IFX_VADC_BRSMR_RPTDIS_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.RPTDIS */
#define IFX_VADC_BRSMR_RPTDIS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.RPTDIS */
#define IFX_VADC_BRSMR_RPTDIS_OFF (16u)

/** \brief  Length for Ifx_VADC_BRSMR_Bits.SCAN */
#define IFX_VADC_BRSMR_SCAN_LEN (1u)

/** \brief  Mask for Ifx_VADC_BRSMR_Bits.SCAN */
#define IFX_VADC_BRSMR_SCAN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_BRSMR_Bits.SCAN */
#define IFX_VADC_BRSMR_SCAN_OFF (4u)

/** \brief  Length for Ifx_VADC_BRSPND_Bits.CHPNDGy */
#define IFX_VADC_BRSPND_CHPNDGY_LEN (32u)

/** \brief  Mask for Ifx_VADC_BRSPND_Bits.CHPNDGy */
#define IFX_VADC_BRSPND_CHPNDGY_MSK (0xffffffffu)

/** \brief  Offset for Ifx_VADC_BRSPND_Bits.CHPNDGy */
#define IFX_VADC_BRSPND_CHPNDGY_OFF (0u)

/** \brief  Length for Ifx_VADC_BRSSEL_Bits.CHSELGy */
#define IFX_VADC_BRSSEL_CHSELGY_LEN (32u)

/** \brief  Mask for Ifx_VADC_BRSSEL_Bits.CHSELGy */
#define IFX_VADC_BRSSEL_CHSELGY_MSK (0xffffffffu)

/** \brief  Offset for Ifx_VADC_BRSSEL_Bits.CHSELGy */
#define IFX_VADC_BRSSEL_CHSELGY_OFF (0u)

/** \brief  Length for Ifx_VADC_CLC_Bits.DISR */
#define IFX_VADC_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_VADC_CLC_Bits.DISR */
#define IFX_VADC_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_CLC_Bits.DISR */
#define IFX_VADC_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_VADC_CLC_Bits.DISS */
#define IFX_VADC_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_VADC_CLC_Bits.DISS */
#define IFX_VADC_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_CLC_Bits.DISS */
#define IFX_VADC_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_VADC_CLC_Bits.EDIS */
#define IFX_VADC_CLC_EDIS_LEN (1u)

/** \brief  Mask for Ifx_VADC_CLC_Bits.EDIS */
#define IFX_VADC_CLC_EDIS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_CLC_Bits.EDIS */
#define IFX_VADC_CLC_EDIS_OFF (3u)

/** \brief  Length for Ifx_VADC_EMUXSEL_Bits.EMUXGRP0 */
#define IFX_VADC_EMUXSEL_EMUXGRP0_LEN (4u)

/** \brief  Mask for Ifx_VADC_EMUXSEL_Bits.EMUXGRP0 */
#define IFX_VADC_EMUXSEL_EMUXGRP0_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_EMUXSEL_Bits.EMUXGRP0 */
#define IFX_VADC_EMUXSEL_EMUXGRP0_OFF (0u)

/** \brief  Length for Ifx_VADC_EMUXSEL_Bits.EMUXGRP1 */
#define IFX_VADC_EMUXSEL_EMUXGRP1_LEN (4u)

/** \brief  Mask for Ifx_VADC_EMUXSEL_Bits.EMUXGRP1 */
#define IFX_VADC_EMUXSEL_EMUXGRP1_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_EMUXSEL_Bits.EMUXGRP1 */
#define IFX_VADC_EMUXSEL_EMUXGRP1_OFF (4u)

/** \brief  Length for Ifx_VADC_G_ALIAS_Bits.ALIAS0 */
#define IFX_VADC_G_ALIAS_ALIAS0_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_ALIAS_Bits.ALIAS0 */
#define IFX_VADC_G_ALIAS_ALIAS0_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_ALIAS_Bits.ALIAS0 */
#define IFX_VADC_G_ALIAS_ALIAS0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ALIAS_Bits.ALIAS1 */
#define IFX_VADC_G_ALIAS_ALIAS1_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_ALIAS_Bits.ALIAS1 */
#define IFX_VADC_G_ALIAS_ALIAS1_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_ALIAS_Bits.ALIAS1 */
#define IFX_VADC_G_ALIAS_ALIAS1_OFF (8u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.ANONC */
#define IFX_VADC_G_ARBCFG_ANONC_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.ANONC */
#define IFX_VADC_G_ARBCFG_ANONC_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.ANONC */
#define IFX_VADC_G_ARBCFG_ANONC_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.ANONS */
#define IFX_VADC_G_ARBCFG_ANONS_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.ANONS */
#define IFX_VADC_G_ARBCFG_ANONS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.ANONS */
#define IFX_VADC_G_ARBCFG_ANONS_OFF (16u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.ARBM */
#define IFX_VADC_G_ARBCFG_ARBM_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.ARBM */
#define IFX_VADC_G_ARBCFG_ARBM_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.ARBM */
#define IFX_VADC_G_ARBCFG_ARBM_OFF (7u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.ARBRND */
#define IFX_VADC_G_ARBCFG_ARBRND_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.ARBRND */
#define IFX_VADC_G_ARBCFG_ARBRND_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.ARBRND */
#define IFX_VADC_G_ARBCFG_ARBRND_OFF (4u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.BUSY */
#define IFX_VADC_G_ARBCFG_BUSY_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.BUSY */
#define IFX_VADC_G_ARBCFG_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.BUSY */
#define IFX_VADC_G_ARBCFG_BUSY_OFF (30u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.CAL */
#define IFX_VADC_G_ARBCFG_CAL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.CAL */
#define IFX_VADC_G_ARBCFG_CAL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.CAL */
#define IFX_VADC_G_ARBCFG_CAL_OFF (28u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.CALS */
#define IFX_VADC_G_ARBCFG_CALS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.CALS */
#define IFX_VADC_G_ARBCFG_CALS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.CALS */
#define IFX_VADC_G_ARBCFG_CALS_OFF (29u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.CHNR */
#define IFX_VADC_G_ARBCFG_CHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.CHNR */
#define IFX_VADC_G_ARBCFG_CHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.CHNR */
#define IFX_VADC_G_ARBCFG_CHNR_OFF (20u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.CSRC */
#define IFX_VADC_G_ARBCFG_CSRC_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.CSRC */
#define IFX_VADC_G_ARBCFG_CSRC_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.CSRC */
#define IFX_VADC_G_ARBCFG_CSRC_OFF (18u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.SAMPLE */
#define IFX_VADC_G_ARBCFG_SAMPLE_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.SAMPLE */
#define IFX_VADC_G_ARBCFG_SAMPLE_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.SAMPLE */
#define IFX_VADC_G_ARBCFG_SAMPLE_OFF (31u)

/** \brief  Length for Ifx_VADC_G_ARBCFG_Bits.SYNRUN */
#define IFX_VADC_G_ARBCFG_SYNRUN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBCFG_Bits.SYNRUN */
#define IFX_VADC_G_ARBCFG_SYNRUN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBCFG_Bits.SYNRUN */
#define IFX_VADC_G_ARBCFG_SYNRUN_OFF (25u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.ASEN0 */
#define IFX_VADC_G_ARBPR_ASEN0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.ASEN0 */
#define IFX_VADC_G_ARBPR_ASEN0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.ASEN0 */
#define IFX_VADC_G_ARBPR_ASEN0_OFF (24u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.ASEN1 */
#define IFX_VADC_G_ARBPR_ASEN1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.ASEN1 */
#define IFX_VADC_G_ARBPR_ASEN1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.ASEN1 */
#define IFX_VADC_G_ARBPR_ASEN1_OFF (25u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.ASEN2 */
#define IFX_VADC_G_ARBPR_ASEN2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.ASEN2 */
#define IFX_VADC_G_ARBPR_ASEN2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.ASEN2 */
#define IFX_VADC_G_ARBPR_ASEN2_OFF (26u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.CSM0 */
#define IFX_VADC_G_ARBPR_CSM0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.CSM0 */
#define IFX_VADC_G_ARBPR_CSM0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.CSM0 */
#define IFX_VADC_G_ARBPR_CSM0_OFF (3u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.CSM1 */
#define IFX_VADC_G_ARBPR_CSM1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.CSM1 */
#define IFX_VADC_G_ARBPR_CSM1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.CSM1 */
#define IFX_VADC_G_ARBPR_CSM1_OFF (7u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.CSM2 */
#define IFX_VADC_G_ARBPR_CSM2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.CSM2 */
#define IFX_VADC_G_ARBPR_CSM2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.CSM2 */
#define IFX_VADC_G_ARBPR_CSM2_OFF (11u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.PRIO0 */
#define IFX_VADC_G_ARBPR_PRIO0_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.PRIO0 */
#define IFX_VADC_G_ARBPR_PRIO0_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.PRIO0 */
#define IFX_VADC_G_ARBPR_PRIO0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.PRIO1 */
#define IFX_VADC_G_ARBPR_PRIO1_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.PRIO1 */
#define IFX_VADC_G_ARBPR_PRIO1_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.PRIO1 */
#define IFX_VADC_G_ARBPR_PRIO1_OFF (4u)

/** \brief  Length for Ifx_VADC_G_ARBPR_Bits.PRIO2 */
#define IFX_VADC_G_ARBPR_PRIO2_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ARBPR_Bits.PRIO2 */
#define IFX_VADC_G_ARBPR_PRIO2_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ARBPR_Bits.PRIO2 */
#define IFX_VADC_G_ARBPR_PRIO2_OFF (8u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.GTLVL */
#define IFX_VADC_G_ASCTRL_GTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.GTLVL */
#define IFX_VADC_G_ASCTRL_GTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.GTLVL */
#define IFX_VADC_G_ASCTRL_GTLVL_OFF (20u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.GTSEL */
#define IFX_VADC_G_ASCTRL_GTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.GTSEL */
#define IFX_VADC_G_ASCTRL_GTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.GTSEL */
#define IFX_VADC_G_ASCTRL_GTSEL_OFF (16u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.GTWC */
#define IFX_VADC_G_ASCTRL_GTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.GTWC */
#define IFX_VADC_G_ASCTRL_GTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.GTWC */
#define IFX_VADC_G_ASCTRL_GTWC_OFF (23u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.SRCRESREG */
#define IFX_VADC_G_ASCTRL_SRCRESREG_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.SRCRESREG */
#define IFX_VADC_G_ASCTRL_SRCRESREG_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.SRCRESREG */
#define IFX_VADC_G_ASCTRL_SRCRESREG_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.TMEN */
#define IFX_VADC_G_ASCTRL_TMEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.TMEN */
#define IFX_VADC_G_ASCTRL_TMEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.TMEN */
#define IFX_VADC_G_ASCTRL_TMEN_OFF (28u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.TMWC */
#define IFX_VADC_G_ASCTRL_TMWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.TMWC */
#define IFX_VADC_G_ASCTRL_TMWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.TMWC */
#define IFX_VADC_G_ASCTRL_TMWC_OFF (31u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.XTLVL */
#define IFX_VADC_G_ASCTRL_XTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.XTLVL */
#define IFX_VADC_G_ASCTRL_XTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.XTLVL */
#define IFX_VADC_G_ASCTRL_XTLVL_OFF (12u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.XTMODE */
#define IFX_VADC_G_ASCTRL_XTMODE_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.XTMODE */
#define IFX_VADC_G_ASCTRL_XTMODE_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.XTMODE */
#define IFX_VADC_G_ASCTRL_XTMODE_OFF (13u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.XTSEL */
#define IFX_VADC_G_ASCTRL_XTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.XTSEL */
#define IFX_VADC_G_ASCTRL_XTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.XTSEL */
#define IFX_VADC_G_ASCTRL_XTSEL_OFF (8u)

/** \brief  Length for Ifx_VADC_G_ASCTRL_Bits.XTWC */
#define IFX_VADC_G_ASCTRL_XTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASCTRL_Bits.XTWC */
#define IFX_VADC_G_ASCTRL_XTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASCTRL_Bits.XTWC */
#define IFX_VADC_G_ASCTRL_XTWC_OFF (15u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.CLRPND */
#define IFX_VADC_G_ASMR_CLRPND_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.CLRPND */
#define IFX_VADC_G_ASMR_CLRPND_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.CLRPND */
#define IFX_VADC_G_ASMR_CLRPND_OFF (8u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.ENGT */
#define IFX_VADC_G_ASMR_ENGT_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.ENGT */
#define IFX_VADC_G_ASMR_ENGT_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.ENGT */
#define IFX_VADC_G_ASMR_ENGT_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.ENSI */
#define IFX_VADC_G_ASMR_ENSI_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.ENSI */
#define IFX_VADC_G_ASMR_ENSI_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.ENSI */
#define IFX_VADC_G_ASMR_ENSI_OFF (3u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.ENTR */
#define IFX_VADC_G_ASMR_ENTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.ENTR */
#define IFX_VADC_G_ASMR_ENTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.ENTR */
#define IFX_VADC_G_ASMR_ENTR_OFF (2u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.LDEV */
#define IFX_VADC_G_ASMR_LDEV_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.LDEV */
#define IFX_VADC_G_ASMR_LDEV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.LDEV */
#define IFX_VADC_G_ASMR_LDEV_OFF (9u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.LDM */
#define IFX_VADC_G_ASMR_LDM_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.LDM */
#define IFX_VADC_G_ASMR_LDM_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.LDM */
#define IFX_VADC_G_ASMR_LDM_OFF (5u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.REQGT */
#define IFX_VADC_G_ASMR_REQGT_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.REQGT */
#define IFX_VADC_G_ASMR_REQGT_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.REQGT */
#define IFX_VADC_G_ASMR_REQGT_OFF (7u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.RPTDIS */
#define IFX_VADC_G_ASMR_RPTDIS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.RPTDIS */
#define IFX_VADC_G_ASMR_RPTDIS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.RPTDIS */
#define IFX_VADC_G_ASMR_RPTDIS_OFF (16u)

/** \brief  Length for Ifx_VADC_G_ASMR_Bits.SCAN */
#define IFX_VADC_G_ASMR_SCAN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_ASMR_Bits.SCAN */
#define IFX_VADC_G_ASMR_SCAN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_ASMR_Bits.SCAN */
#define IFX_VADC_G_ASMR_SCAN_OFF (4u)

/** \brief  Length for Ifx_VADC_G_ASPND_Bits.CHPND */
#define IFX_VADC_G_ASPND_CHPND_LEN (32u)

/** \brief  Mask for Ifx_VADC_G_ASPND_Bits.CHPND */
#define IFX_VADC_G_ASPND_CHPND_MSK (0xffffffffu)

/** \brief  Offset for Ifx_VADC_G_ASPND_Bits.CHPND */
#define IFX_VADC_G_ASPND_CHPND_OFF (0u)

/** \brief  Length for Ifx_VADC_G_ASSEL_Bits.CHSEL */
#define IFX_VADC_G_ASSEL_CHSEL_LEN (32u)

/** \brief  Mask for Ifx_VADC_G_ASSEL_Bits.CHSEL */
#define IFX_VADC_G_ASSEL_CHSEL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_VADC_G_ASSEL_Bits.CHSEL */
#define IFX_VADC_G_ASSEL_CHSEL_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFA0 */
#define IFX_VADC_G_BFL_BFA0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFA0 */
#define IFX_VADC_G_BFL_BFA0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFA0 */
#define IFX_VADC_G_BFL_BFA0_OFF (8u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFA1 */
#define IFX_VADC_G_BFL_BFA1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFA1 */
#define IFX_VADC_G_BFL_BFA1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFA1 */
#define IFX_VADC_G_BFL_BFA1_OFF (9u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFA2 */
#define IFX_VADC_G_BFL_BFA2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFA2 */
#define IFX_VADC_G_BFL_BFA2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFA2 */
#define IFX_VADC_G_BFL_BFA2_OFF (10u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFA3 */
#define IFX_VADC_G_BFL_BFA3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFA3 */
#define IFX_VADC_G_BFL_BFA3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFA3 */
#define IFX_VADC_G_BFL_BFA3_OFF (11u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFI0 */
#define IFX_VADC_G_BFL_BFI0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFI0 */
#define IFX_VADC_G_BFL_BFI0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFI0 */
#define IFX_VADC_G_BFL_BFI0_OFF (16u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFI1 */
#define IFX_VADC_G_BFL_BFI1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFI1 */
#define IFX_VADC_G_BFL_BFI1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFI1 */
#define IFX_VADC_G_BFL_BFI1_OFF (17u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFI2 */
#define IFX_VADC_G_BFL_BFI2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFI2 */
#define IFX_VADC_G_BFL_BFI2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFI2 */
#define IFX_VADC_G_BFL_BFI2_OFF (18u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFI3 */
#define IFX_VADC_G_BFL_BFI3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFI3 */
#define IFX_VADC_G_BFL_BFI3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFI3 */
#define IFX_VADC_G_BFL_BFI3_OFF (19u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFL0 */
#define IFX_VADC_G_BFL_BFL0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFL0 */
#define IFX_VADC_G_BFL_BFL0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFL0 */
#define IFX_VADC_G_BFL_BFL0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFL1 */
#define IFX_VADC_G_BFL_BFL1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFL1 */
#define IFX_VADC_G_BFL_BFL1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFL1 */
#define IFX_VADC_G_BFL_BFL1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFL2 */
#define IFX_VADC_G_BFL_BFL2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFL2 */
#define IFX_VADC_G_BFL_BFL2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFL2 */
#define IFX_VADC_G_BFL_BFL2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_BFL_Bits.BFL3 */
#define IFX_VADC_G_BFL_BFL3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFL_Bits.BFL3 */
#define IFX_VADC_G_BFL_BFL3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFL_Bits.BFL3 */
#define IFX_VADC_G_BFL_BFL3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_BFLC_Bits.BFM0 */
#define IFX_VADC_G_BFLC_BFM0_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLC_Bits.BFM0 */
#define IFX_VADC_G_BFLC_BFM0_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLC_Bits.BFM0 */
#define IFX_VADC_G_BFLC_BFM0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BFLC_Bits.BFM1 */
#define IFX_VADC_G_BFLC_BFM1_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLC_Bits.BFM1 */
#define IFX_VADC_G_BFLC_BFM1_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLC_Bits.BFM1 */
#define IFX_VADC_G_BFLC_BFM1_OFF (4u)

/** \brief  Length for Ifx_VADC_G_BFLC_Bits.BFM2 */
#define IFX_VADC_G_BFLC_BFM2_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLC_Bits.BFM2 */
#define IFX_VADC_G_BFLC_BFM2_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLC_Bits.BFM2 */
#define IFX_VADC_G_BFLC_BFM2_OFF (8u)

/** \brief  Length for Ifx_VADC_G_BFLC_Bits.BFM3 */
#define IFX_VADC_G_BFLC_BFM3_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLC_Bits.BFM3 */
#define IFX_VADC_G_BFLC_BFM3_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLC_Bits.BFM3 */
#define IFX_VADC_G_BFLC_BFM3_OFF (12u)

/** \brief  Length for Ifx_VADC_G_BFLNP_Bits.BFL0NP */
#define IFX_VADC_G_BFLNP_BFL0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLNP_Bits.BFL0NP */
#define IFX_VADC_G_BFLNP_BFL0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLNP_Bits.BFL0NP */
#define IFX_VADC_G_BFLNP_BFL0NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BFLNP_Bits.BFL1NP */
#define IFX_VADC_G_BFLNP_BFL1NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLNP_Bits.BFL1NP */
#define IFX_VADC_G_BFLNP_BFL1NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLNP_Bits.BFL1NP */
#define IFX_VADC_G_BFLNP_BFL1NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_BFLNP_Bits.BFL2NP */
#define IFX_VADC_G_BFLNP_BFL2NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLNP_Bits.BFL2NP */
#define IFX_VADC_G_BFLNP_BFL2NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLNP_Bits.BFL2NP */
#define IFX_VADC_G_BFLNP_BFL2NP_OFF (8u)

/** \brief  Length for Ifx_VADC_G_BFLNP_Bits.BFL3NP */
#define IFX_VADC_G_BFLNP_BFL3NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_BFLNP_Bits.BFL3NP */
#define IFX_VADC_G_BFLNP_BFL3NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_BFLNP_Bits.BFL3NP */
#define IFX_VADC_G_BFLNP_BFL3NP_OFF (12u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFC0 */
#define IFX_VADC_G_BFLS_BFC0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFC0 */
#define IFX_VADC_G_BFLS_BFC0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFC0 */
#define IFX_VADC_G_BFLS_BFC0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFC1 */
#define IFX_VADC_G_BFLS_BFC1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFC1 */
#define IFX_VADC_G_BFLS_BFC1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFC1 */
#define IFX_VADC_G_BFLS_BFC1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFC2 */
#define IFX_VADC_G_BFLS_BFC2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFC2 */
#define IFX_VADC_G_BFLS_BFC2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFC2 */
#define IFX_VADC_G_BFLS_BFC2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFC3 */
#define IFX_VADC_G_BFLS_BFC3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFC3 */
#define IFX_VADC_G_BFLS_BFC3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFC3 */
#define IFX_VADC_G_BFLS_BFC3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFS0 */
#define IFX_VADC_G_BFLS_BFS0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFS0 */
#define IFX_VADC_G_BFLS_BFS0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFS0 */
#define IFX_VADC_G_BFLS_BFS0_OFF (16u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFS1 */
#define IFX_VADC_G_BFLS_BFS1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFS1 */
#define IFX_VADC_G_BFLS_BFS1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFS1 */
#define IFX_VADC_G_BFLS_BFS1_OFF (17u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFS2 */
#define IFX_VADC_G_BFLS_BFS2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFS2 */
#define IFX_VADC_G_BFLS_BFS2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFS2 */
#define IFX_VADC_G_BFLS_BFS2_OFF (18u)

/** \brief  Length for Ifx_VADC_G_BFLS_Bits.BFS3 */
#define IFX_VADC_G_BFLS_BFS3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_BFLS_Bits.BFS3 */
#define IFX_VADC_G_BFLS_BFS3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_BFLS_Bits.BFS3 */
#define IFX_VADC_G_BFLS_BFS3_OFF (19u)

/** \brief  Length for Ifx_VADC_G_BOUND_Bits.BOUNDARY0 */
#define IFX_VADC_G_BOUND_BOUNDARY0_LEN (12u)

/** \brief  Mask for Ifx_VADC_G_BOUND_Bits.BOUNDARY0 */
#define IFX_VADC_G_BOUND_BOUNDARY0_MSK (0xfffu)

/** \brief  Offset for Ifx_VADC_G_BOUND_Bits.BOUNDARY0 */
#define IFX_VADC_G_BOUND_BOUNDARY0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_BOUND_Bits.BOUNDARY1 */
#define IFX_VADC_G_BOUND_BOUNDARY1_LEN (12u)

/** \brief  Mask for Ifx_VADC_G_BOUND_Bits.BOUNDARY1 */
#define IFX_VADC_G_BOUND_BOUNDARY1_MSK (0xfffu)

/** \brief  Offset for Ifx_VADC_G_BOUND_Bits.BOUNDARY1 */
#define IFX_VADC_G_BOUND_BOUNDARY1_OFF (16u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV0 */
#define IFX_VADC_G_CEFCLR_CEV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV0 */
#define IFX_VADC_G_CEFCLR_CEV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV0 */
#define IFX_VADC_G_CEFCLR_CEV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV10 */
#define IFX_VADC_G_CEFCLR_CEV10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV10 */
#define IFX_VADC_G_CEFCLR_CEV10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV10 */
#define IFX_VADC_G_CEFCLR_CEV10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV11 */
#define IFX_VADC_G_CEFCLR_CEV11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV11 */
#define IFX_VADC_G_CEFCLR_CEV11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV11 */
#define IFX_VADC_G_CEFCLR_CEV11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV12 */
#define IFX_VADC_G_CEFCLR_CEV12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV12 */
#define IFX_VADC_G_CEFCLR_CEV12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV12 */
#define IFX_VADC_G_CEFCLR_CEV12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV13 */
#define IFX_VADC_G_CEFCLR_CEV13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV13 */
#define IFX_VADC_G_CEFCLR_CEV13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV13 */
#define IFX_VADC_G_CEFCLR_CEV13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV14 */
#define IFX_VADC_G_CEFCLR_CEV14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV14 */
#define IFX_VADC_G_CEFCLR_CEV14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV14 */
#define IFX_VADC_G_CEFCLR_CEV14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV15 */
#define IFX_VADC_G_CEFCLR_CEV15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV15 */
#define IFX_VADC_G_CEFCLR_CEV15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV15 */
#define IFX_VADC_G_CEFCLR_CEV15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV1 */
#define IFX_VADC_G_CEFCLR_CEV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV1 */
#define IFX_VADC_G_CEFCLR_CEV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV1 */
#define IFX_VADC_G_CEFCLR_CEV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV2 */
#define IFX_VADC_G_CEFCLR_CEV2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV2 */
#define IFX_VADC_G_CEFCLR_CEV2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV2 */
#define IFX_VADC_G_CEFCLR_CEV2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV3 */
#define IFX_VADC_G_CEFCLR_CEV3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV3 */
#define IFX_VADC_G_CEFCLR_CEV3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV3 */
#define IFX_VADC_G_CEFCLR_CEV3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV4 */
#define IFX_VADC_G_CEFCLR_CEV4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV4 */
#define IFX_VADC_G_CEFCLR_CEV4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV4 */
#define IFX_VADC_G_CEFCLR_CEV4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV5 */
#define IFX_VADC_G_CEFCLR_CEV5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV5 */
#define IFX_VADC_G_CEFCLR_CEV5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV5 */
#define IFX_VADC_G_CEFCLR_CEV5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV6 */
#define IFX_VADC_G_CEFCLR_CEV6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV6 */
#define IFX_VADC_G_CEFCLR_CEV6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV6 */
#define IFX_VADC_G_CEFCLR_CEV6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV7 */
#define IFX_VADC_G_CEFCLR_CEV7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV7 */
#define IFX_VADC_G_CEFCLR_CEV7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV7 */
#define IFX_VADC_G_CEFCLR_CEV7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV8 */
#define IFX_VADC_G_CEFCLR_CEV8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV8 */
#define IFX_VADC_G_CEFCLR_CEV8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV8 */
#define IFX_VADC_G_CEFCLR_CEV8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CEFCLR_Bits.CEV9 */
#define IFX_VADC_G_CEFCLR_CEV9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFCLR_Bits.CEV9 */
#define IFX_VADC_G_CEFCLR_CEV9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFCLR_Bits.CEV9 */
#define IFX_VADC_G_CEFCLR_CEV9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV0 */
#define IFX_VADC_G_CEFLAG_CEV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV0 */
#define IFX_VADC_G_CEFLAG_CEV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV0 */
#define IFX_VADC_G_CEFLAG_CEV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV10 */
#define IFX_VADC_G_CEFLAG_CEV10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV10 */
#define IFX_VADC_G_CEFLAG_CEV10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV10 */
#define IFX_VADC_G_CEFLAG_CEV10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV11 */
#define IFX_VADC_G_CEFLAG_CEV11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV11 */
#define IFX_VADC_G_CEFLAG_CEV11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV11 */
#define IFX_VADC_G_CEFLAG_CEV11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV12 */
#define IFX_VADC_G_CEFLAG_CEV12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV12 */
#define IFX_VADC_G_CEFLAG_CEV12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV12 */
#define IFX_VADC_G_CEFLAG_CEV12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV13 */
#define IFX_VADC_G_CEFLAG_CEV13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV13 */
#define IFX_VADC_G_CEFLAG_CEV13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV13 */
#define IFX_VADC_G_CEFLAG_CEV13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV14 */
#define IFX_VADC_G_CEFLAG_CEV14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV14 */
#define IFX_VADC_G_CEFLAG_CEV14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV14 */
#define IFX_VADC_G_CEFLAG_CEV14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV15 */
#define IFX_VADC_G_CEFLAG_CEV15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV15 */
#define IFX_VADC_G_CEFLAG_CEV15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV15 */
#define IFX_VADC_G_CEFLAG_CEV15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV1 */
#define IFX_VADC_G_CEFLAG_CEV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV1 */
#define IFX_VADC_G_CEFLAG_CEV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV1 */
#define IFX_VADC_G_CEFLAG_CEV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV2 */
#define IFX_VADC_G_CEFLAG_CEV2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV2 */
#define IFX_VADC_G_CEFLAG_CEV2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV2 */
#define IFX_VADC_G_CEFLAG_CEV2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV3 */
#define IFX_VADC_G_CEFLAG_CEV3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV3 */
#define IFX_VADC_G_CEFLAG_CEV3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV3 */
#define IFX_VADC_G_CEFLAG_CEV3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV4 */
#define IFX_VADC_G_CEFLAG_CEV4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV4 */
#define IFX_VADC_G_CEFLAG_CEV4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV4 */
#define IFX_VADC_G_CEFLAG_CEV4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV5 */
#define IFX_VADC_G_CEFLAG_CEV5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV5 */
#define IFX_VADC_G_CEFLAG_CEV5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV5 */
#define IFX_VADC_G_CEFLAG_CEV5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV6 */
#define IFX_VADC_G_CEFLAG_CEV6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV6 */
#define IFX_VADC_G_CEFLAG_CEV6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV6 */
#define IFX_VADC_G_CEFLAG_CEV6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV7 */
#define IFX_VADC_G_CEFLAG_CEV7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV7 */
#define IFX_VADC_G_CEFLAG_CEV7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV7 */
#define IFX_VADC_G_CEFLAG_CEV7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV8 */
#define IFX_VADC_G_CEFLAG_CEV8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV8 */
#define IFX_VADC_G_CEFLAG_CEV8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV8 */
#define IFX_VADC_G_CEFLAG_CEV8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CEFLAG_Bits.CEV9 */
#define IFX_VADC_G_CEFLAG_CEV9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CEFLAG_Bits.CEV9 */
#define IFX_VADC_G_CEFLAG_CEV9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CEFLAG_Bits.CEV9 */
#define IFX_VADC_G_CEFLAG_CEV9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV0NP */
#define IFX_VADC_G_CEVNP0_CEV0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV0NP */
#define IFX_VADC_G_CEVNP0_CEV0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV0NP */
#define IFX_VADC_G_CEVNP0_CEV0NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV1NP */
#define IFX_VADC_G_CEVNP0_CEV1NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV1NP */
#define IFX_VADC_G_CEVNP0_CEV1NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV1NP */
#define IFX_VADC_G_CEVNP0_CEV1NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV2NP */
#define IFX_VADC_G_CEVNP0_CEV2NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV2NP */
#define IFX_VADC_G_CEVNP0_CEV2NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV2NP */
#define IFX_VADC_G_CEVNP0_CEV2NP_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV3NP */
#define IFX_VADC_G_CEVNP0_CEV3NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV3NP */
#define IFX_VADC_G_CEVNP0_CEV3NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV3NP */
#define IFX_VADC_G_CEVNP0_CEV3NP_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV4NP */
#define IFX_VADC_G_CEVNP0_CEV4NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV4NP */
#define IFX_VADC_G_CEVNP0_CEV4NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV4NP */
#define IFX_VADC_G_CEVNP0_CEV4NP_OFF (16u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV5NP */
#define IFX_VADC_G_CEVNP0_CEV5NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV5NP */
#define IFX_VADC_G_CEVNP0_CEV5NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV5NP */
#define IFX_VADC_G_CEVNP0_CEV5NP_OFF (20u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV6NP */
#define IFX_VADC_G_CEVNP0_CEV6NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV6NP */
#define IFX_VADC_G_CEVNP0_CEV6NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV6NP */
#define IFX_VADC_G_CEVNP0_CEV6NP_OFF (24u)

/** \brief  Length for Ifx_VADC_G_CEVNP0_Bits.CEV7NP */
#define IFX_VADC_G_CEVNP0_CEV7NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP0_Bits.CEV7NP */
#define IFX_VADC_G_CEVNP0_CEV7NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP0_Bits.CEV7NP */
#define IFX_VADC_G_CEVNP0_CEV7NP_OFF (28u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV10NP */
#define IFX_VADC_G_CEVNP1_CEV10NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV10NP */
#define IFX_VADC_G_CEVNP1_CEV10NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV10NP */
#define IFX_VADC_G_CEVNP1_CEV10NP_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV11NP */
#define IFX_VADC_G_CEVNP1_CEV11NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV11NP */
#define IFX_VADC_G_CEVNP1_CEV11NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV11NP */
#define IFX_VADC_G_CEVNP1_CEV11NP_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV12NP */
#define IFX_VADC_G_CEVNP1_CEV12NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV12NP */
#define IFX_VADC_G_CEVNP1_CEV12NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV12NP */
#define IFX_VADC_G_CEVNP1_CEV12NP_OFF (16u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV13NP */
#define IFX_VADC_G_CEVNP1_CEV13NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV13NP */
#define IFX_VADC_G_CEVNP1_CEV13NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV13NP */
#define IFX_VADC_G_CEVNP1_CEV13NP_OFF (20u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV14NP */
#define IFX_VADC_G_CEVNP1_CEV14NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV14NP */
#define IFX_VADC_G_CEVNP1_CEV14NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV14NP */
#define IFX_VADC_G_CEVNP1_CEV14NP_OFF (24u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV15NP */
#define IFX_VADC_G_CEVNP1_CEV15NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV15NP */
#define IFX_VADC_G_CEVNP1_CEV15NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV15NP */
#define IFX_VADC_G_CEVNP1_CEV15NP_OFF (28u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV8NP */
#define IFX_VADC_G_CEVNP1_CEV8NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV8NP */
#define IFX_VADC_G_CEVNP1_CEV8NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV8NP */
#define IFX_VADC_G_CEVNP1_CEV8NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CEVNP1_Bits.CEV9NP */
#define IFX_VADC_G_CEVNP1_CEV9NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CEVNP1_Bits.CEV9NP */
#define IFX_VADC_G_CEVNP1_CEV9NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CEVNP1_Bits.CEV9NP */
#define IFX_VADC_G_CEVNP1_CEV9NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH0 */
#define IFX_VADC_G_CHASS_ASSCH0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH0 */
#define IFX_VADC_G_CHASS_ASSCH0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH0 */
#define IFX_VADC_G_CHASS_ASSCH0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH10 */
#define IFX_VADC_G_CHASS_ASSCH10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH10 */
#define IFX_VADC_G_CHASS_ASSCH10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH10 */
#define IFX_VADC_G_CHASS_ASSCH10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH11 */
#define IFX_VADC_G_CHASS_ASSCH11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH11 */
#define IFX_VADC_G_CHASS_ASSCH11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH11 */
#define IFX_VADC_G_CHASS_ASSCH11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH12 */
#define IFX_VADC_G_CHASS_ASSCH12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH12 */
#define IFX_VADC_G_CHASS_ASSCH12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH12 */
#define IFX_VADC_G_CHASS_ASSCH12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH13 */
#define IFX_VADC_G_CHASS_ASSCH13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH13 */
#define IFX_VADC_G_CHASS_ASSCH13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH13 */
#define IFX_VADC_G_CHASS_ASSCH13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH14 */
#define IFX_VADC_G_CHASS_ASSCH14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH14 */
#define IFX_VADC_G_CHASS_ASSCH14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH14 */
#define IFX_VADC_G_CHASS_ASSCH14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH15 */
#define IFX_VADC_G_CHASS_ASSCH15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH15 */
#define IFX_VADC_G_CHASS_ASSCH15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH15 */
#define IFX_VADC_G_CHASS_ASSCH15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH16 */
#define IFX_VADC_G_CHASS_ASSCH16_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH16 */
#define IFX_VADC_G_CHASS_ASSCH16_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH16 */
#define IFX_VADC_G_CHASS_ASSCH16_OFF (16u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH17 */
#define IFX_VADC_G_CHASS_ASSCH17_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH17 */
#define IFX_VADC_G_CHASS_ASSCH17_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH17 */
#define IFX_VADC_G_CHASS_ASSCH17_OFF (17u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH18 */
#define IFX_VADC_G_CHASS_ASSCH18_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH18 */
#define IFX_VADC_G_CHASS_ASSCH18_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH18 */
#define IFX_VADC_G_CHASS_ASSCH18_OFF (18u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH19 */
#define IFX_VADC_G_CHASS_ASSCH19_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH19 */
#define IFX_VADC_G_CHASS_ASSCH19_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH19 */
#define IFX_VADC_G_CHASS_ASSCH19_OFF (19u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH1 */
#define IFX_VADC_G_CHASS_ASSCH1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH1 */
#define IFX_VADC_G_CHASS_ASSCH1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH1 */
#define IFX_VADC_G_CHASS_ASSCH1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH20 */
#define IFX_VADC_G_CHASS_ASSCH20_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH20 */
#define IFX_VADC_G_CHASS_ASSCH20_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH20 */
#define IFX_VADC_G_CHASS_ASSCH20_OFF (20u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH21 */
#define IFX_VADC_G_CHASS_ASSCH21_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH21 */
#define IFX_VADC_G_CHASS_ASSCH21_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH21 */
#define IFX_VADC_G_CHASS_ASSCH21_OFF (21u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH22 */
#define IFX_VADC_G_CHASS_ASSCH22_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH22 */
#define IFX_VADC_G_CHASS_ASSCH22_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH22 */
#define IFX_VADC_G_CHASS_ASSCH22_OFF (22u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH23 */
#define IFX_VADC_G_CHASS_ASSCH23_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH23 */
#define IFX_VADC_G_CHASS_ASSCH23_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH23 */
#define IFX_VADC_G_CHASS_ASSCH23_OFF (23u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH24 */
#define IFX_VADC_G_CHASS_ASSCH24_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH24 */
#define IFX_VADC_G_CHASS_ASSCH24_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH24 */
#define IFX_VADC_G_CHASS_ASSCH24_OFF (24u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH25 */
#define IFX_VADC_G_CHASS_ASSCH25_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH25 */
#define IFX_VADC_G_CHASS_ASSCH25_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH25 */
#define IFX_VADC_G_CHASS_ASSCH25_OFF (25u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH26 */
#define IFX_VADC_G_CHASS_ASSCH26_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH26 */
#define IFX_VADC_G_CHASS_ASSCH26_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH26 */
#define IFX_VADC_G_CHASS_ASSCH26_OFF (26u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH27 */
#define IFX_VADC_G_CHASS_ASSCH27_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH27 */
#define IFX_VADC_G_CHASS_ASSCH27_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH27 */
#define IFX_VADC_G_CHASS_ASSCH27_OFF (27u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH28 */
#define IFX_VADC_G_CHASS_ASSCH28_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH28 */
#define IFX_VADC_G_CHASS_ASSCH28_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH28 */
#define IFX_VADC_G_CHASS_ASSCH28_OFF (28u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH29 */
#define IFX_VADC_G_CHASS_ASSCH29_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH29 */
#define IFX_VADC_G_CHASS_ASSCH29_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH29 */
#define IFX_VADC_G_CHASS_ASSCH29_OFF (29u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH2 */
#define IFX_VADC_G_CHASS_ASSCH2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH2 */
#define IFX_VADC_G_CHASS_ASSCH2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH2 */
#define IFX_VADC_G_CHASS_ASSCH2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH30 */
#define IFX_VADC_G_CHASS_ASSCH30_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH30 */
#define IFX_VADC_G_CHASS_ASSCH30_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH30 */
#define IFX_VADC_G_CHASS_ASSCH30_OFF (30u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH31 */
#define IFX_VADC_G_CHASS_ASSCH31_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH31 */
#define IFX_VADC_G_CHASS_ASSCH31_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH31 */
#define IFX_VADC_G_CHASS_ASSCH31_OFF (31u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH3 */
#define IFX_VADC_G_CHASS_ASSCH3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH3 */
#define IFX_VADC_G_CHASS_ASSCH3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH3 */
#define IFX_VADC_G_CHASS_ASSCH3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH4 */
#define IFX_VADC_G_CHASS_ASSCH4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH4 */
#define IFX_VADC_G_CHASS_ASSCH4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH4 */
#define IFX_VADC_G_CHASS_ASSCH4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH5 */
#define IFX_VADC_G_CHASS_ASSCH5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH5 */
#define IFX_VADC_G_CHASS_ASSCH5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH5 */
#define IFX_VADC_G_CHASS_ASSCH5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH6 */
#define IFX_VADC_G_CHASS_ASSCH6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH6 */
#define IFX_VADC_G_CHASS_ASSCH6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH6 */
#define IFX_VADC_G_CHASS_ASSCH6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH7 */
#define IFX_VADC_G_CHASS_ASSCH7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH7 */
#define IFX_VADC_G_CHASS_ASSCH7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH7 */
#define IFX_VADC_G_CHASS_ASSCH7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH8 */
#define IFX_VADC_G_CHASS_ASSCH8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH8 */
#define IFX_VADC_G_CHASS_ASSCH8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH8 */
#define IFX_VADC_G_CHASS_ASSCH8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CHASS_Bits.ASSCH9 */
#define IFX_VADC_G_CHASS_ASSCH9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHASS_Bits.ASSCH9 */
#define IFX_VADC_G_CHASS_ASSCH9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHASS_Bits.ASSCH9 */
#define IFX_VADC_G_CHASS_ASSCH9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.BNDSELL */
#define IFX_VADC_G_CHCTR_BNDSELL_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.BNDSELL */
#define IFX_VADC_G_CHCTR_BNDSELL_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.BNDSELL */
#define IFX_VADC_G_CHCTR_BNDSELL_OFF (4u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.BNDSELU */
#define IFX_VADC_G_CHCTR_BNDSELU_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.BNDSELU */
#define IFX_VADC_G_CHCTR_BNDSELU_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.BNDSELU */
#define IFX_VADC_G_CHCTR_BNDSELU_OFF (6u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.BNDSELX */
#define IFX_VADC_G_CHCTR_BNDSELX_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.BNDSELX */
#define IFX_VADC_G_CHCTR_BNDSELX_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.BNDSELX */
#define IFX_VADC_G_CHCTR_BNDSELX_OFF (12u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.BWDCH */
#define IFX_VADC_G_CHCTR_BWDCH_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.BWDCH */
#define IFX_VADC_G_CHCTR_BWDCH_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.BWDCH */
#define IFX_VADC_G_CHCTR_BWDCH_OFF (28u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.BWDEN */
#define IFX_VADC_G_CHCTR_BWDEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.BWDEN */
#define IFX_VADC_G_CHCTR_BWDEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.BWDEN */
#define IFX_VADC_G_CHCTR_BWDEN_OFF (30u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.CHEVMODE */
#define IFX_VADC_G_CHCTR_CHEVMODE_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.CHEVMODE */
#define IFX_VADC_G_CHCTR_CHEVMODE_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.CHEVMODE */
#define IFX_VADC_G_CHCTR_CHEVMODE_OFF (8u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.ICLSEL */
#define IFX_VADC_G_CHCTR_ICLSEL_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.ICLSEL */
#define IFX_VADC_G_CHCTR_ICLSEL_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.ICLSEL */
#define IFX_VADC_G_CHCTR_ICLSEL_OFF (0u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.REFSEL */
#define IFX_VADC_G_CHCTR_REFSEL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.REFSEL */
#define IFX_VADC_G_CHCTR_REFSEL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.REFSEL */
#define IFX_VADC_G_CHCTR_REFSEL_OFF (11u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.RESPOS */
#define IFX_VADC_G_CHCTR_RESPOS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.RESPOS */
#define IFX_VADC_G_CHCTR_RESPOS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.RESPOS */
#define IFX_VADC_G_CHCTR_RESPOS_OFF (21u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.RESREG */
#define IFX_VADC_G_CHCTR_RESREG_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.RESREG */
#define IFX_VADC_G_CHCTR_RESREG_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.RESREG */
#define IFX_VADC_G_CHCTR_RESREG_OFF (16u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.RESTBS */
#define IFX_VADC_G_CHCTR_RESTBS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.RESTBS */
#define IFX_VADC_G_CHCTR_RESTBS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.RESTBS */
#define IFX_VADC_G_CHCTR_RESTBS_OFF (20u)

/** \brief  Length for Ifx_VADC_G_CHCTR_Bits.SYNC */
#define IFX_VADC_G_CHCTR_SYNC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_CHCTR_Bits.SYNC */
#define IFX_VADC_G_CHCTR_SYNC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_CHCTR_Bits.SYNC */
#define IFX_VADC_G_CHCTR_SYNC_OFF (10u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMUXACT */
#define IFX_VADC_G_EMUXCTR_EMUXACT_LEN (3u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMUXACT */
#define IFX_VADC_G_EMUXCTR_EMUXACT_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMUXACT */
#define IFX_VADC_G_EMUXCTR_EMUXACT_OFF (8u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMUXCH */
#define IFX_VADC_G_EMUXCTR_EMUXCH_LEN (10u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMUXCH */
#define IFX_VADC_G_EMUXCTR_EMUXCH_MSK (0x3ffu)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMUXCH */
#define IFX_VADC_G_EMUXCTR_EMUXCH_OFF (16u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMUXMODE */
#define IFX_VADC_G_EMUXCTR_EMUXMODE_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMUXMODE */
#define IFX_VADC_G_EMUXCTR_EMUXMODE_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMUXMODE */
#define IFX_VADC_G_EMUXCTR_EMUXMODE_OFF (26u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMUXSET */
#define IFX_VADC_G_EMUXCTR_EMUXSET_LEN (3u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMUXSET */
#define IFX_VADC_G_EMUXCTR_EMUXSET_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMUXSET */
#define IFX_VADC_G_EMUXCTR_EMUXSET_OFF (0u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMXCOD */
#define IFX_VADC_G_EMUXCTR_EMXCOD_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMXCOD */
#define IFX_VADC_G_EMUXCTR_EMXCOD_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMXCOD */
#define IFX_VADC_G_EMUXCTR_EMXCOD_OFF (28u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMXCSS */
#define IFX_VADC_G_EMUXCTR_EMXCSS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMXCSS */
#define IFX_VADC_G_EMUXCTR_EMXCSS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMXCSS */
#define IFX_VADC_G_EMUXCTR_EMXCSS_OFF (30u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMXST */
#define IFX_VADC_G_EMUXCTR_EMXST_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMXST */
#define IFX_VADC_G_EMUXCTR_EMXST_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMXST */
#define IFX_VADC_G_EMUXCTR_EMXST_OFF (29u)

/** \brief  Length for Ifx_VADC_G_EMUXCTR_Bits.EMXWC */
#define IFX_VADC_G_EMUXCTR_EMXWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_EMUXCTR_Bits.EMXWC */
#define IFX_VADC_G_EMUXCTR_EMXWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_EMUXCTR_Bits.EMXWC */
#define IFX_VADC_G_EMUXCTR_EMXWC_OFF (31u)

/** \brief  Length for Ifx_VADC_G_Q0R0_Bits.ENSI */
#define IFX_VADC_G_Q0R0_ENSI_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_Q0R0_Bits.ENSI */
#define IFX_VADC_G_Q0R0_ENSI_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_Q0R0_Bits.ENSI */
#define IFX_VADC_G_Q0R0_ENSI_OFF (6u)

/** \brief  Length for Ifx_VADC_G_Q0R0_Bits.EXTR */
#define IFX_VADC_G_Q0R0_EXTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_Q0R0_Bits.EXTR */
#define IFX_VADC_G_Q0R0_EXTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_Q0R0_Bits.EXTR */
#define IFX_VADC_G_Q0R0_EXTR_OFF (7u)

/** \brief  Length for Ifx_VADC_G_Q0R0_Bits.REQCHNR */
#define IFX_VADC_G_Q0R0_REQCHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_Q0R0_Bits.REQCHNR */
#define IFX_VADC_G_Q0R0_REQCHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_Q0R0_Bits.REQCHNR */
#define IFX_VADC_G_Q0R0_REQCHNR_OFF (0u)

/** \brief  Length for Ifx_VADC_G_Q0R0_Bits.RF */
#define IFX_VADC_G_Q0R0_RF_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_Q0R0_Bits.RF */
#define IFX_VADC_G_Q0R0_RF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_Q0R0_Bits.RF */
#define IFX_VADC_G_Q0R0_RF_OFF (5u)

/** \brief  Length for Ifx_VADC_G_Q0R0_Bits.V */
#define IFX_VADC_G_Q0R0_V_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_Q0R0_Bits.V */
#define IFX_VADC_G_Q0R0_V_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_Q0R0_Bits.V */
#define IFX_VADC_G_Q0R0_V_OFF (8u)

/** \brief  Length for Ifx_VADC_G_QBUR0_Bits.ENSI */
#define IFX_VADC_G_QBUR0_ENSI_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QBUR0_Bits.ENSI */
#define IFX_VADC_G_QBUR0_ENSI_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QBUR0_Bits.ENSI */
#define IFX_VADC_G_QBUR0_ENSI_OFF (6u)

/** \brief  Length for Ifx_VADC_G_QBUR0_Bits.EXTR */
#define IFX_VADC_G_QBUR0_EXTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QBUR0_Bits.EXTR */
#define IFX_VADC_G_QBUR0_EXTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QBUR0_Bits.EXTR */
#define IFX_VADC_G_QBUR0_EXTR_OFF (7u)

/** \brief  Length for Ifx_VADC_G_QBUR0_Bits.REQCHNR */
#define IFX_VADC_G_QBUR0_REQCHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_QBUR0_Bits.REQCHNR */
#define IFX_VADC_G_QBUR0_REQCHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_QBUR0_Bits.REQCHNR */
#define IFX_VADC_G_QBUR0_REQCHNR_OFF (0u)

/** \brief  Length for Ifx_VADC_G_QBUR0_Bits.RF */
#define IFX_VADC_G_QBUR0_RF_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QBUR0_Bits.RF */
#define IFX_VADC_G_QBUR0_RF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QBUR0_Bits.RF */
#define IFX_VADC_G_QBUR0_RF_OFF (5u)

/** \brief  Length for Ifx_VADC_G_QBUR0_Bits.V */
#define IFX_VADC_G_QBUR0_V_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QBUR0_Bits.V */
#define IFX_VADC_G_QBUR0_V_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QBUR0_Bits.V */
#define IFX_VADC_G_QBUR0_V_OFF (8u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.GTLVL */
#define IFX_VADC_G_QCTRL0_GTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.GTLVL */
#define IFX_VADC_G_QCTRL0_GTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.GTLVL */
#define IFX_VADC_G_QCTRL0_GTLVL_OFF (20u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.GTSEL */
#define IFX_VADC_G_QCTRL0_GTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.GTSEL */
#define IFX_VADC_G_QCTRL0_GTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.GTSEL */
#define IFX_VADC_G_QCTRL0_GTSEL_OFF (16u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.GTWC */
#define IFX_VADC_G_QCTRL0_GTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.GTWC */
#define IFX_VADC_G_QCTRL0_GTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.GTWC */
#define IFX_VADC_G_QCTRL0_GTWC_OFF (23u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.SRCRESREG */
#define IFX_VADC_G_QCTRL0_SRCRESREG_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.SRCRESREG */
#define IFX_VADC_G_QCTRL0_SRCRESREG_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.SRCRESREG */
#define IFX_VADC_G_QCTRL0_SRCRESREG_OFF (0u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.TMEN */
#define IFX_VADC_G_QCTRL0_TMEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.TMEN */
#define IFX_VADC_G_QCTRL0_TMEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.TMEN */
#define IFX_VADC_G_QCTRL0_TMEN_OFF (28u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.TMWC */
#define IFX_VADC_G_QCTRL0_TMWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.TMWC */
#define IFX_VADC_G_QCTRL0_TMWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.TMWC */
#define IFX_VADC_G_QCTRL0_TMWC_OFF (31u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.XTLVL */
#define IFX_VADC_G_QCTRL0_XTLVL_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.XTLVL */
#define IFX_VADC_G_QCTRL0_XTLVL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.XTLVL */
#define IFX_VADC_G_QCTRL0_XTLVL_OFF (12u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.XTMODE */
#define IFX_VADC_G_QCTRL0_XTMODE_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.XTMODE */
#define IFX_VADC_G_QCTRL0_XTMODE_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.XTMODE */
#define IFX_VADC_G_QCTRL0_XTMODE_OFF (13u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.XTSEL */
#define IFX_VADC_G_QCTRL0_XTSEL_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.XTSEL */
#define IFX_VADC_G_QCTRL0_XTSEL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.XTSEL */
#define IFX_VADC_G_QCTRL0_XTSEL_OFF (8u)

/** \brief  Length for Ifx_VADC_G_QCTRL0_Bits.XTWC */
#define IFX_VADC_G_QCTRL0_XTWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QCTRL0_Bits.XTWC */
#define IFX_VADC_G_QCTRL0_XTWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QCTRL0_Bits.XTWC */
#define IFX_VADC_G_QCTRL0_XTWC_OFF (15u)

/** \brief  Length for Ifx_VADC_G_QINR0_Bits.ENSI */
#define IFX_VADC_G_QINR0_ENSI_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QINR0_Bits.ENSI */
#define IFX_VADC_G_QINR0_ENSI_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QINR0_Bits.ENSI */
#define IFX_VADC_G_QINR0_ENSI_OFF (6u)

/** \brief  Length for Ifx_VADC_G_QINR0_Bits.EXTR */
#define IFX_VADC_G_QINR0_EXTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QINR0_Bits.EXTR */
#define IFX_VADC_G_QINR0_EXTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QINR0_Bits.EXTR */
#define IFX_VADC_G_QINR0_EXTR_OFF (7u)

/** \brief  Length for Ifx_VADC_G_QINR0_Bits.REQCHNR */
#define IFX_VADC_G_QINR0_REQCHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_QINR0_Bits.REQCHNR */
#define IFX_VADC_G_QINR0_REQCHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_QINR0_Bits.REQCHNR */
#define IFX_VADC_G_QINR0_REQCHNR_OFF (0u)

/** \brief  Length for Ifx_VADC_G_QINR0_Bits.RF */
#define IFX_VADC_G_QINR0_RF_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QINR0_Bits.RF */
#define IFX_VADC_G_QINR0_RF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QINR0_Bits.RF */
#define IFX_VADC_G_QINR0_RF_OFF (5u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.CEV */
#define IFX_VADC_G_QMR0_CEV_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.CEV */
#define IFX_VADC_G_QMR0_CEV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.CEV */
#define IFX_VADC_G_QMR0_CEV_OFF (11u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.CLRV */
#define IFX_VADC_G_QMR0_CLRV_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.CLRV */
#define IFX_VADC_G_QMR0_CLRV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.CLRV */
#define IFX_VADC_G_QMR0_CLRV_OFF (8u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.ENGT */
#define IFX_VADC_G_QMR0_ENGT_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.ENGT */
#define IFX_VADC_G_QMR0_ENGT_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.ENGT */
#define IFX_VADC_G_QMR0_ENGT_OFF (0u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.ENTR */
#define IFX_VADC_G_QMR0_ENTR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.ENTR */
#define IFX_VADC_G_QMR0_ENTR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.ENTR */
#define IFX_VADC_G_QMR0_ENTR_OFF (2u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.FLUSH */
#define IFX_VADC_G_QMR0_FLUSH_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.FLUSH */
#define IFX_VADC_G_QMR0_FLUSH_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.FLUSH */
#define IFX_VADC_G_QMR0_FLUSH_OFF (10u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.RPTDIS */
#define IFX_VADC_G_QMR0_RPTDIS_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.RPTDIS */
#define IFX_VADC_G_QMR0_RPTDIS_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.RPTDIS */
#define IFX_VADC_G_QMR0_RPTDIS_OFF (16u)

/** \brief  Length for Ifx_VADC_G_QMR0_Bits.TREV */
#define IFX_VADC_G_QMR0_TREV_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QMR0_Bits.TREV */
#define IFX_VADC_G_QMR0_TREV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QMR0_Bits.TREV */
#define IFX_VADC_G_QMR0_TREV_OFF (9u)

/** \brief  Length for Ifx_VADC_G_QSR0_Bits.EMPTY */
#define IFX_VADC_G_QSR0_EMPTY_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QSR0_Bits.EMPTY */
#define IFX_VADC_G_QSR0_EMPTY_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QSR0_Bits.EMPTY */
#define IFX_VADC_G_QSR0_EMPTY_OFF (5u)

/** \brief  Length for Ifx_VADC_G_QSR0_Bits.EV */
#define IFX_VADC_G_QSR0_EV_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QSR0_Bits.EV */
#define IFX_VADC_G_QSR0_EV_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QSR0_Bits.EV */
#define IFX_VADC_G_QSR0_EV_OFF (8u)

/** \brief  Length for Ifx_VADC_G_QSR0_Bits.FILL */
#define IFX_VADC_G_QSR0_FILL_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_QSR0_Bits.FILL */
#define IFX_VADC_G_QSR0_FILL_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_QSR0_Bits.FILL */
#define IFX_VADC_G_QSR0_FILL_OFF (0u)

/** \brief  Length for Ifx_VADC_G_QSR0_Bits.REQGT */
#define IFX_VADC_G_QSR0_REQGT_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_QSR0_Bits.REQGT */
#define IFX_VADC_G_QSR0_REQGT_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_QSR0_Bits.REQGT */
#define IFX_VADC_G_QSR0_REQGT_OFF (7u)

/** \brief  Length for Ifx_VADC_G_RCR_Bits.DMM */
#define IFX_VADC_G_RCR_DMM_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_RCR_Bits.DMM */
#define IFX_VADC_G_RCR_DMM_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_RCR_Bits.DMM */
#define IFX_VADC_G_RCR_DMM_OFF (20u)

/** \brief  Length for Ifx_VADC_G_RCR_Bits.DRCTR */
#define IFX_VADC_G_RCR_DRCTR_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_RCR_Bits.DRCTR */
#define IFX_VADC_G_RCR_DRCTR_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_RCR_Bits.DRCTR */
#define IFX_VADC_G_RCR_DRCTR_OFF (16u)

/** \brief  Length for Ifx_VADC_G_RCR_Bits.FEN */
#define IFX_VADC_G_RCR_FEN_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_RCR_Bits.FEN */
#define IFX_VADC_G_RCR_FEN_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_RCR_Bits.FEN */
#define IFX_VADC_G_RCR_FEN_OFF (25u)

/** \brief  Length for Ifx_VADC_G_RCR_Bits.SRGEN */
#define IFX_VADC_G_RCR_SRGEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RCR_Bits.SRGEN */
#define IFX_VADC_G_RCR_SRGEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RCR_Bits.SRGEN */
#define IFX_VADC_G_RCR_SRGEN_OFF (31u)

/** \brief  Length for Ifx_VADC_G_RCR_Bits.WFR */
#define IFX_VADC_G_RCR_WFR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RCR_Bits.WFR */
#define IFX_VADC_G_RCR_WFR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RCR_Bits.WFR */
#define IFX_VADC_G_RCR_WFR_OFF (24u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV0 */
#define IFX_VADC_G_REFCLR_REV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV0 */
#define IFX_VADC_G_REFCLR_REV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV0 */
#define IFX_VADC_G_REFCLR_REV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV10 */
#define IFX_VADC_G_REFCLR_REV10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV10 */
#define IFX_VADC_G_REFCLR_REV10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV10 */
#define IFX_VADC_G_REFCLR_REV10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV11 */
#define IFX_VADC_G_REFCLR_REV11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV11 */
#define IFX_VADC_G_REFCLR_REV11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV11 */
#define IFX_VADC_G_REFCLR_REV11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV12 */
#define IFX_VADC_G_REFCLR_REV12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV12 */
#define IFX_VADC_G_REFCLR_REV12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV12 */
#define IFX_VADC_G_REFCLR_REV12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV13 */
#define IFX_VADC_G_REFCLR_REV13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV13 */
#define IFX_VADC_G_REFCLR_REV13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV13 */
#define IFX_VADC_G_REFCLR_REV13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV14 */
#define IFX_VADC_G_REFCLR_REV14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV14 */
#define IFX_VADC_G_REFCLR_REV14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV14 */
#define IFX_VADC_G_REFCLR_REV14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV15 */
#define IFX_VADC_G_REFCLR_REV15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV15 */
#define IFX_VADC_G_REFCLR_REV15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV15 */
#define IFX_VADC_G_REFCLR_REV15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV1 */
#define IFX_VADC_G_REFCLR_REV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV1 */
#define IFX_VADC_G_REFCLR_REV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV1 */
#define IFX_VADC_G_REFCLR_REV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV2 */
#define IFX_VADC_G_REFCLR_REV2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV2 */
#define IFX_VADC_G_REFCLR_REV2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV2 */
#define IFX_VADC_G_REFCLR_REV2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV3 */
#define IFX_VADC_G_REFCLR_REV3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV3 */
#define IFX_VADC_G_REFCLR_REV3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV3 */
#define IFX_VADC_G_REFCLR_REV3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV4 */
#define IFX_VADC_G_REFCLR_REV4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV4 */
#define IFX_VADC_G_REFCLR_REV4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV4 */
#define IFX_VADC_G_REFCLR_REV4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV5 */
#define IFX_VADC_G_REFCLR_REV5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV5 */
#define IFX_VADC_G_REFCLR_REV5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV5 */
#define IFX_VADC_G_REFCLR_REV5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV6 */
#define IFX_VADC_G_REFCLR_REV6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV6 */
#define IFX_VADC_G_REFCLR_REV6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV6 */
#define IFX_VADC_G_REFCLR_REV6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV7 */
#define IFX_VADC_G_REFCLR_REV7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV7 */
#define IFX_VADC_G_REFCLR_REV7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV7 */
#define IFX_VADC_G_REFCLR_REV7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV8 */
#define IFX_VADC_G_REFCLR_REV8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV8 */
#define IFX_VADC_G_REFCLR_REV8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV8 */
#define IFX_VADC_G_REFCLR_REV8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_REFCLR_Bits.REV9 */
#define IFX_VADC_G_REFCLR_REV9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFCLR_Bits.REV9 */
#define IFX_VADC_G_REFCLR_REV9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFCLR_Bits.REV9 */
#define IFX_VADC_G_REFCLR_REV9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV0 */
#define IFX_VADC_G_REFLAG_REV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV0 */
#define IFX_VADC_G_REFLAG_REV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV0 */
#define IFX_VADC_G_REFLAG_REV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV10 */
#define IFX_VADC_G_REFLAG_REV10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV10 */
#define IFX_VADC_G_REFLAG_REV10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV10 */
#define IFX_VADC_G_REFLAG_REV10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV11 */
#define IFX_VADC_G_REFLAG_REV11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV11 */
#define IFX_VADC_G_REFLAG_REV11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV11 */
#define IFX_VADC_G_REFLAG_REV11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV12 */
#define IFX_VADC_G_REFLAG_REV12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV12 */
#define IFX_VADC_G_REFLAG_REV12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV12 */
#define IFX_VADC_G_REFLAG_REV12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV13 */
#define IFX_VADC_G_REFLAG_REV13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV13 */
#define IFX_VADC_G_REFLAG_REV13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV13 */
#define IFX_VADC_G_REFLAG_REV13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV14 */
#define IFX_VADC_G_REFLAG_REV14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV14 */
#define IFX_VADC_G_REFLAG_REV14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV14 */
#define IFX_VADC_G_REFLAG_REV14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV15 */
#define IFX_VADC_G_REFLAG_REV15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV15 */
#define IFX_VADC_G_REFLAG_REV15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV15 */
#define IFX_VADC_G_REFLAG_REV15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV1 */
#define IFX_VADC_G_REFLAG_REV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV1 */
#define IFX_VADC_G_REFLAG_REV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV1 */
#define IFX_VADC_G_REFLAG_REV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV2 */
#define IFX_VADC_G_REFLAG_REV2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV2 */
#define IFX_VADC_G_REFLAG_REV2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV2 */
#define IFX_VADC_G_REFLAG_REV2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV3 */
#define IFX_VADC_G_REFLAG_REV3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV3 */
#define IFX_VADC_G_REFLAG_REV3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV3 */
#define IFX_VADC_G_REFLAG_REV3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV4 */
#define IFX_VADC_G_REFLAG_REV4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV4 */
#define IFX_VADC_G_REFLAG_REV4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV4 */
#define IFX_VADC_G_REFLAG_REV4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV5 */
#define IFX_VADC_G_REFLAG_REV5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV5 */
#define IFX_VADC_G_REFLAG_REV5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV5 */
#define IFX_VADC_G_REFLAG_REV5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV6 */
#define IFX_VADC_G_REFLAG_REV6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV6 */
#define IFX_VADC_G_REFLAG_REV6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV6 */
#define IFX_VADC_G_REFLAG_REV6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV7 */
#define IFX_VADC_G_REFLAG_REV7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV7 */
#define IFX_VADC_G_REFLAG_REV7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV7 */
#define IFX_VADC_G_REFLAG_REV7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV8 */
#define IFX_VADC_G_REFLAG_REV8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV8 */
#define IFX_VADC_G_REFLAG_REV8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV8 */
#define IFX_VADC_G_REFLAG_REV8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_REFLAG_Bits.REV9 */
#define IFX_VADC_G_REFLAG_REV9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_REFLAG_Bits.REV9 */
#define IFX_VADC_G_REFLAG_REV9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_REFLAG_Bits.REV9 */
#define IFX_VADC_G_REFLAG_REV9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.CHNR */
#define IFX_VADC_G_RES_CHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.CHNR */
#define IFX_VADC_G_RES_CHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.CHNR */
#define IFX_VADC_G_RES_CHNR_OFF (20u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.CRS */
#define IFX_VADC_G_RES_CRS_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.CRS */
#define IFX_VADC_G_RES_CRS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.CRS */
#define IFX_VADC_G_RES_CRS_OFF (28u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.DRC */
#define IFX_VADC_G_RES_DRC_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.DRC */
#define IFX_VADC_G_RES_DRC_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.DRC */
#define IFX_VADC_G_RES_DRC_OFF (16u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.EMUX */
#define IFX_VADC_G_RES_EMUX_LEN (3u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.EMUX */
#define IFX_VADC_G_RES_EMUX_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.EMUX */
#define IFX_VADC_G_RES_EMUX_OFF (25u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.FCR */
#define IFX_VADC_G_RES_FCR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.FCR */
#define IFX_VADC_G_RES_FCR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.FCR */
#define IFX_VADC_G_RES_FCR_OFF (30u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.RESULT */
#define IFX_VADC_G_RES_RESULT_LEN (16u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.RESULT */
#define IFX_VADC_G_RES_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.RESULT */
#define IFX_VADC_G_RES_RESULT_OFF (0u)

/** \brief  Length for Ifx_VADC_G_RES_Bits.VF */
#define IFX_VADC_G_RES_VF_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RES_Bits.VF */
#define IFX_VADC_G_RES_VF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RES_Bits.VF */
#define IFX_VADC_G_RES_VF_OFF (31u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.CHNR */
#define IFX_VADC_G_RESD_CHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.CHNR */
#define IFX_VADC_G_RESD_CHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.CHNR */
#define IFX_VADC_G_RESD_CHNR_OFF (20u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.CRS */
#define IFX_VADC_G_RESD_CRS_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.CRS */
#define IFX_VADC_G_RESD_CRS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.CRS */
#define IFX_VADC_G_RESD_CRS_OFF (28u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.DRC */
#define IFX_VADC_G_RESD_DRC_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.DRC */
#define IFX_VADC_G_RESD_DRC_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.DRC */
#define IFX_VADC_G_RESD_DRC_OFF (16u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.EMUX */
#define IFX_VADC_G_RESD_EMUX_LEN (3u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.EMUX */
#define IFX_VADC_G_RESD_EMUX_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.EMUX */
#define IFX_VADC_G_RESD_EMUX_OFF (25u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.FCR */
#define IFX_VADC_G_RESD_FCR_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.FCR */
#define IFX_VADC_G_RESD_FCR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.FCR */
#define IFX_VADC_G_RESD_FCR_OFF (30u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.RESULT */
#define IFX_VADC_G_RESD_RESULT_LEN (16u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.RESULT */
#define IFX_VADC_G_RESD_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.RESULT */
#define IFX_VADC_G_RESD_RESULT_OFF (0u)

/** \brief  Length for Ifx_VADC_G_RESD_Bits.VF */
#define IFX_VADC_G_RESD_VF_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RESD_Bits.VF */
#define IFX_VADC_G_RESD_VF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RESD_Bits.VF */
#define IFX_VADC_G_RESD_VF_OFF (31u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV0NP */
#define IFX_VADC_G_REVNP0_REV0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV0NP */
#define IFX_VADC_G_REVNP0_REV0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV0NP */
#define IFX_VADC_G_REVNP0_REV0NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV1NP */
#define IFX_VADC_G_REVNP0_REV1NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV1NP */
#define IFX_VADC_G_REVNP0_REV1NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV1NP */
#define IFX_VADC_G_REVNP0_REV1NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV2NP */
#define IFX_VADC_G_REVNP0_REV2NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV2NP */
#define IFX_VADC_G_REVNP0_REV2NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV2NP */
#define IFX_VADC_G_REVNP0_REV2NP_OFF (8u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV3NP */
#define IFX_VADC_G_REVNP0_REV3NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV3NP */
#define IFX_VADC_G_REVNP0_REV3NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV3NP */
#define IFX_VADC_G_REVNP0_REV3NP_OFF (12u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV4NP */
#define IFX_VADC_G_REVNP0_REV4NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV4NP */
#define IFX_VADC_G_REVNP0_REV4NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV4NP */
#define IFX_VADC_G_REVNP0_REV4NP_OFF (16u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV5NP */
#define IFX_VADC_G_REVNP0_REV5NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV5NP */
#define IFX_VADC_G_REVNP0_REV5NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV5NP */
#define IFX_VADC_G_REVNP0_REV5NP_OFF (20u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV6NP */
#define IFX_VADC_G_REVNP0_REV6NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV6NP */
#define IFX_VADC_G_REVNP0_REV6NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV6NP */
#define IFX_VADC_G_REVNP0_REV6NP_OFF (24u)

/** \brief  Length for Ifx_VADC_G_REVNP0_Bits.REV7NP */
#define IFX_VADC_G_REVNP0_REV7NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP0_Bits.REV7NP */
#define IFX_VADC_G_REVNP0_REV7NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP0_Bits.REV7NP */
#define IFX_VADC_G_REVNP0_REV7NP_OFF (28u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV10NP */
#define IFX_VADC_G_REVNP1_REV10NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV10NP */
#define IFX_VADC_G_REVNP1_REV10NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV10NP */
#define IFX_VADC_G_REVNP1_REV10NP_OFF (8u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV11NP */
#define IFX_VADC_G_REVNP1_REV11NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV11NP */
#define IFX_VADC_G_REVNP1_REV11NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV11NP */
#define IFX_VADC_G_REVNP1_REV11NP_OFF (12u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV12NP */
#define IFX_VADC_G_REVNP1_REV12NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV12NP */
#define IFX_VADC_G_REVNP1_REV12NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV12NP */
#define IFX_VADC_G_REVNP1_REV12NP_OFF (16u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV13NP */
#define IFX_VADC_G_REVNP1_REV13NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV13NP */
#define IFX_VADC_G_REVNP1_REV13NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV13NP */
#define IFX_VADC_G_REVNP1_REV13NP_OFF (20u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV14NP */
#define IFX_VADC_G_REVNP1_REV14NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV14NP */
#define IFX_VADC_G_REVNP1_REV14NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV14NP */
#define IFX_VADC_G_REVNP1_REV14NP_OFF (24u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV15NP */
#define IFX_VADC_G_REVNP1_REV15NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV15NP */
#define IFX_VADC_G_REVNP1_REV15NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV15NP */
#define IFX_VADC_G_REVNP1_REV15NP_OFF (28u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV8NP */
#define IFX_VADC_G_REVNP1_REV8NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV8NP */
#define IFX_VADC_G_REVNP1_REV8NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV8NP */
#define IFX_VADC_G_REVNP1_REV8NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_REVNP1_Bits.REV9NP */
#define IFX_VADC_G_REVNP1_REV9NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_REVNP1_Bits.REV9NP */
#define IFX_VADC_G_REVNP1_REV9NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_REVNP1_Bits.REV9NP */
#define IFX_VADC_G_REVNP1_REV9NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR0 */
#define IFX_VADC_G_RRASS_ASSRR0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR0 */
#define IFX_VADC_G_RRASS_ASSRR0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR0 */
#define IFX_VADC_G_RRASS_ASSRR0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR10 */
#define IFX_VADC_G_RRASS_ASSRR10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR10 */
#define IFX_VADC_G_RRASS_ASSRR10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR10 */
#define IFX_VADC_G_RRASS_ASSRR10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR11 */
#define IFX_VADC_G_RRASS_ASSRR11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR11 */
#define IFX_VADC_G_RRASS_ASSRR11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR11 */
#define IFX_VADC_G_RRASS_ASSRR11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR12 */
#define IFX_VADC_G_RRASS_ASSRR12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR12 */
#define IFX_VADC_G_RRASS_ASSRR12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR12 */
#define IFX_VADC_G_RRASS_ASSRR12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR13 */
#define IFX_VADC_G_RRASS_ASSRR13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR13 */
#define IFX_VADC_G_RRASS_ASSRR13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR13 */
#define IFX_VADC_G_RRASS_ASSRR13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR14 */
#define IFX_VADC_G_RRASS_ASSRR14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR14 */
#define IFX_VADC_G_RRASS_ASSRR14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR14 */
#define IFX_VADC_G_RRASS_ASSRR14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR15 */
#define IFX_VADC_G_RRASS_ASSRR15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR15 */
#define IFX_VADC_G_RRASS_ASSRR15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR15 */
#define IFX_VADC_G_RRASS_ASSRR15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR1 */
#define IFX_VADC_G_RRASS_ASSRR1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR1 */
#define IFX_VADC_G_RRASS_ASSRR1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR1 */
#define IFX_VADC_G_RRASS_ASSRR1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR2 */
#define IFX_VADC_G_RRASS_ASSRR2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR2 */
#define IFX_VADC_G_RRASS_ASSRR2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR2 */
#define IFX_VADC_G_RRASS_ASSRR2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR3 */
#define IFX_VADC_G_RRASS_ASSRR3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR3 */
#define IFX_VADC_G_RRASS_ASSRR3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR3 */
#define IFX_VADC_G_RRASS_ASSRR3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR4 */
#define IFX_VADC_G_RRASS_ASSRR4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR4 */
#define IFX_VADC_G_RRASS_ASSRR4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR4 */
#define IFX_VADC_G_RRASS_ASSRR4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR5 */
#define IFX_VADC_G_RRASS_ASSRR5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR5 */
#define IFX_VADC_G_RRASS_ASSRR5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR5 */
#define IFX_VADC_G_RRASS_ASSRR5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR6 */
#define IFX_VADC_G_RRASS_ASSRR6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR6 */
#define IFX_VADC_G_RRASS_ASSRR6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR6 */
#define IFX_VADC_G_RRASS_ASSRR6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR7 */
#define IFX_VADC_G_RRASS_ASSRR7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR7 */
#define IFX_VADC_G_RRASS_ASSRR7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR7 */
#define IFX_VADC_G_RRASS_ASSRR7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR8 */
#define IFX_VADC_G_RRASS_ASSRR8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR8 */
#define IFX_VADC_G_RRASS_ASSRR8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR8 */
#define IFX_VADC_G_RRASS_ASSRR8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_RRASS_Bits.ASSRR9 */
#define IFX_VADC_G_RRASS_ASSRR9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_RRASS_Bits.ASSRR9 */
#define IFX_VADC_G_RRASS_ASSRR9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_RRASS_Bits.ASSRR9 */
#define IFX_VADC_G_RRASS_ASSRR9_OFF (9u)

/** \brief  Length for Ifx_VADC_G_SEFCLR_Bits.SEV0 */
#define IFX_VADC_G_SEFCLR_SEV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SEFCLR_Bits.SEV0 */
#define IFX_VADC_G_SEFCLR_SEV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SEFCLR_Bits.SEV0 */
#define IFX_VADC_G_SEFCLR_SEV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_SEFCLR_Bits.SEV1 */
#define IFX_VADC_G_SEFCLR_SEV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SEFCLR_Bits.SEV1 */
#define IFX_VADC_G_SEFCLR_SEV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SEFCLR_Bits.SEV1 */
#define IFX_VADC_G_SEFCLR_SEV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_SEFLAG_Bits.SEV0 */
#define IFX_VADC_G_SEFLAG_SEV0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SEFLAG_Bits.SEV0 */
#define IFX_VADC_G_SEFLAG_SEV0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SEFLAG_Bits.SEV0 */
#define IFX_VADC_G_SEFLAG_SEV0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_SEFLAG_Bits.SEV1 */
#define IFX_VADC_G_SEFLAG_SEV1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SEFLAG_Bits.SEV1 */
#define IFX_VADC_G_SEFLAG_SEV1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SEFLAG_Bits.SEV1 */
#define IFX_VADC_G_SEFLAG_SEV1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_SEVNP_Bits.SEV0NP */
#define IFX_VADC_G_SEVNP_SEV0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_SEVNP_Bits.SEV0NP */
#define IFX_VADC_G_SEVNP_SEV0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_SEVNP_Bits.SEV0NP */
#define IFX_VADC_G_SEVNP_SEV0NP_OFF (0u)

/** \brief  Length for Ifx_VADC_G_SEVNP_Bits.SEV1NP */
#define IFX_VADC_G_SEVNP_SEV1NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_G_SEVNP_Bits.SEV1NP */
#define IFX_VADC_G_SEVNP_SEV1NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_G_SEVNP_Bits.SEV1NP */
#define IFX_VADC_G_SEVNP_SEV1NP_OFF (4u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.AGSR0 */
#define IFX_VADC_G_SRACT_AGSR0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.AGSR0 */
#define IFX_VADC_G_SRACT_AGSR0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.AGSR0 */
#define IFX_VADC_G_SRACT_AGSR0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.AGSR1 */
#define IFX_VADC_G_SRACT_AGSR1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.AGSR1 */
#define IFX_VADC_G_SRACT_AGSR1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.AGSR1 */
#define IFX_VADC_G_SRACT_AGSR1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.AGSR2 */
#define IFX_VADC_G_SRACT_AGSR2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.AGSR2 */
#define IFX_VADC_G_SRACT_AGSR2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.AGSR2 */
#define IFX_VADC_G_SRACT_AGSR2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.AGSR3 */
#define IFX_VADC_G_SRACT_AGSR3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.AGSR3 */
#define IFX_VADC_G_SRACT_AGSR3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.AGSR3 */
#define IFX_VADC_G_SRACT_AGSR3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.ASSR0 */
#define IFX_VADC_G_SRACT_ASSR0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.ASSR0 */
#define IFX_VADC_G_SRACT_ASSR0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.ASSR0 */
#define IFX_VADC_G_SRACT_ASSR0_OFF (8u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.ASSR1 */
#define IFX_VADC_G_SRACT_ASSR1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.ASSR1 */
#define IFX_VADC_G_SRACT_ASSR1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.ASSR1 */
#define IFX_VADC_G_SRACT_ASSR1_OFF (9u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.ASSR2 */
#define IFX_VADC_G_SRACT_ASSR2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.ASSR2 */
#define IFX_VADC_G_SRACT_ASSR2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.ASSR2 */
#define IFX_VADC_G_SRACT_ASSR2_OFF (10u)

/** \brief  Length for Ifx_VADC_G_SRACT_Bits.ASSR3 */
#define IFX_VADC_G_SRACT_ASSR3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SRACT_Bits.ASSR3 */
#define IFX_VADC_G_SRACT_ASSR3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SRACT_Bits.ASSR3 */
#define IFX_VADC_G_SRACT_ASSR3_OFF (11u)

/** \brief  Length for Ifx_VADC_G_SYNCTR_Bits.EVALR1 */
#define IFX_VADC_G_SYNCTR_EVALR1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SYNCTR_Bits.EVALR1 */
#define IFX_VADC_G_SYNCTR_EVALR1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SYNCTR_Bits.EVALR1 */
#define IFX_VADC_G_SYNCTR_EVALR1_OFF (4u)

/** \brief  Length for Ifx_VADC_G_SYNCTR_Bits.EVALR2 */
#define IFX_VADC_G_SYNCTR_EVALR2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SYNCTR_Bits.EVALR2 */
#define IFX_VADC_G_SYNCTR_EVALR2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SYNCTR_Bits.EVALR2 */
#define IFX_VADC_G_SYNCTR_EVALR2_OFF (5u)

/** \brief  Length for Ifx_VADC_G_SYNCTR_Bits.EVALR3 */
#define IFX_VADC_G_SYNCTR_EVALR3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_SYNCTR_Bits.EVALR3 */
#define IFX_VADC_G_SYNCTR_EVALR3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_SYNCTR_Bits.EVALR3 */
#define IFX_VADC_G_SYNCTR_EVALR3_OFF (6u)

/** \brief  Length for Ifx_VADC_G_SYNCTR_Bits.STSEL */
#define IFX_VADC_G_SYNCTR_STSEL_LEN (2u)

/** \brief  Mask for Ifx_VADC_G_SYNCTR_Bits.STSEL */
#define IFX_VADC_G_SYNCTR_STSEL_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_G_SYNCTR_Bits.STSEL */
#define IFX_VADC_G_SYNCTR_STSEL_OFF (0u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF0 */
#define IFX_VADC_G_VFR_VF0_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF0 */
#define IFX_VADC_G_VFR_VF0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF0 */
#define IFX_VADC_G_VFR_VF0_OFF (0u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF10 */
#define IFX_VADC_G_VFR_VF10_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF10 */
#define IFX_VADC_G_VFR_VF10_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF10 */
#define IFX_VADC_G_VFR_VF10_OFF (10u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF11 */
#define IFX_VADC_G_VFR_VF11_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF11 */
#define IFX_VADC_G_VFR_VF11_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF11 */
#define IFX_VADC_G_VFR_VF11_OFF (11u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF12 */
#define IFX_VADC_G_VFR_VF12_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF12 */
#define IFX_VADC_G_VFR_VF12_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF12 */
#define IFX_VADC_G_VFR_VF12_OFF (12u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF13 */
#define IFX_VADC_G_VFR_VF13_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF13 */
#define IFX_VADC_G_VFR_VF13_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF13 */
#define IFX_VADC_G_VFR_VF13_OFF (13u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF14 */
#define IFX_VADC_G_VFR_VF14_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF14 */
#define IFX_VADC_G_VFR_VF14_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF14 */
#define IFX_VADC_G_VFR_VF14_OFF (14u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF15 */
#define IFX_VADC_G_VFR_VF15_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF15 */
#define IFX_VADC_G_VFR_VF15_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF15 */
#define IFX_VADC_G_VFR_VF15_OFF (15u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF1 */
#define IFX_VADC_G_VFR_VF1_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF1 */
#define IFX_VADC_G_VFR_VF1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF1 */
#define IFX_VADC_G_VFR_VF1_OFF (1u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF2 */
#define IFX_VADC_G_VFR_VF2_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF2 */
#define IFX_VADC_G_VFR_VF2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF2 */
#define IFX_VADC_G_VFR_VF2_OFF (2u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF3 */
#define IFX_VADC_G_VFR_VF3_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF3 */
#define IFX_VADC_G_VFR_VF3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF3 */
#define IFX_VADC_G_VFR_VF3_OFF (3u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF4 */
#define IFX_VADC_G_VFR_VF4_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF4 */
#define IFX_VADC_G_VFR_VF4_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF4 */
#define IFX_VADC_G_VFR_VF4_OFF (4u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF5 */
#define IFX_VADC_G_VFR_VF5_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF5 */
#define IFX_VADC_G_VFR_VF5_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF5 */
#define IFX_VADC_G_VFR_VF5_OFF (5u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF6 */
#define IFX_VADC_G_VFR_VF6_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF6 */
#define IFX_VADC_G_VFR_VF6_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF6 */
#define IFX_VADC_G_VFR_VF6_OFF (6u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF7 */
#define IFX_VADC_G_VFR_VF7_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF7 */
#define IFX_VADC_G_VFR_VF7_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF7 */
#define IFX_VADC_G_VFR_VF7_OFF (7u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF8 */
#define IFX_VADC_G_VFR_VF8_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF8 */
#define IFX_VADC_G_VFR_VF8_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF8 */
#define IFX_VADC_G_VFR_VF8_OFF (8u)

/** \brief  Length for Ifx_VADC_G_VFR_Bits.VF9 */
#define IFX_VADC_G_VFR_VF9_LEN (1u)

/** \brief  Mask for Ifx_VADC_G_VFR_Bits.VF9 */
#define IFX_VADC_G_VFR_VF9_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_G_VFR_Bits.VF9 */
#define IFX_VADC_G_VFR_VF9_OFF (9u)

/** \brief  Length for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY0 */
#define IFX_VADC_GLOBBOUND_BOUNDARY0_LEN (12u)

/** \brief  Mask for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY0 */
#define IFX_VADC_GLOBBOUND_BOUNDARY0_MSK (0xfffu)

/** \brief  Offset for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY0 */
#define IFX_VADC_GLOBBOUND_BOUNDARY0_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY1 */
#define IFX_VADC_GLOBBOUND_BOUNDARY1_LEN (12u)

/** \brief  Mask for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY1 */
#define IFX_VADC_GLOBBOUND_BOUNDARY1_MSK (0xfffu)

/** \brief  Offset for Ifx_VADC_GLOBBOUND_Bits.BOUNDARY1 */
#define IFX_VADC_GLOBBOUND_BOUNDARY1_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DCMSB */
#define IFX_VADC_GLOBCFG_DCMSB_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DCMSB */
#define IFX_VADC_GLOBCFG_DCMSB_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DCMSB */
#define IFX_VADC_GLOBCFG_DCMSB_OFF (7u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DIVA */
#define IFX_VADC_GLOBCFG_DIVA_LEN (5u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DIVA */
#define IFX_VADC_GLOBCFG_DIVA_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DIVA */
#define IFX_VADC_GLOBCFG_DIVA_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DIVD */
#define IFX_VADC_GLOBCFG_DIVD_LEN (2u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DIVD */
#define IFX_VADC_GLOBCFG_DIVD_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DIVD */
#define IFX_VADC_GLOBCFG_DIVD_OFF (8u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DIVWC */
#define IFX_VADC_GLOBCFG_DIVWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DIVWC */
#define IFX_VADC_GLOBCFG_DIVWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DIVWC */
#define IFX_VADC_GLOBCFG_DIVWC_OFF (15u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DPCAL0 */
#define IFX_VADC_GLOBCFG_DPCAL0_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DPCAL0 */
#define IFX_VADC_GLOBCFG_DPCAL0_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DPCAL0 */
#define IFX_VADC_GLOBCFG_DPCAL0_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DPCAL1 */
#define IFX_VADC_GLOBCFG_DPCAL1_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DPCAL1 */
#define IFX_VADC_GLOBCFG_DPCAL1_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DPCAL1 */
#define IFX_VADC_GLOBCFG_DPCAL1_OFF (17u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DPCAL2 */
#define IFX_VADC_GLOBCFG_DPCAL2_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DPCAL2 */
#define IFX_VADC_GLOBCFG_DPCAL2_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DPCAL2 */
#define IFX_VADC_GLOBCFG_DPCAL2_OFF (18u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.DPCAL3 */
#define IFX_VADC_GLOBCFG_DPCAL3_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.DPCAL3 */
#define IFX_VADC_GLOBCFG_DPCAL3_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.DPCAL3 */
#define IFX_VADC_GLOBCFG_DPCAL3_OFF (19u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.LOSUP */
#define IFX_VADC_GLOBCFG_LOSUP_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.LOSUP */
#define IFX_VADC_GLOBCFG_LOSUP_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.LOSUP */
#define IFX_VADC_GLOBCFG_LOSUP_OFF (14u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.REFPC */
#define IFX_VADC_GLOBCFG_REFPC_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.REFPC */
#define IFX_VADC_GLOBCFG_REFPC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.REFPC */
#define IFX_VADC_GLOBCFG_REFPC_OFF (12u)

/** \brief  Length for Ifx_VADC_GLOBCFG_Bits.SUCAL */
#define IFX_VADC_GLOBCFG_SUCAL_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBCFG_Bits.SUCAL */
#define IFX_VADC_GLOBCFG_SUCAL_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBCFG_Bits.SUCAL */
#define IFX_VADC_GLOBCFG_SUCAL_OFF (31u)

/** \brief  Length for Ifx_VADC_GLOBEFLAG_Bits.REVGLB */
#define IFX_VADC_GLOBEFLAG_REVGLB_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBEFLAG_Bits.REVGLB */
#define IFX_VADC_GLOBEFLAG_REVGLB_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBEFLAG_Bits.REVGLB */
#define IFX_VADC_GLOBEFLAG_REVGLB_OFF (8u)

/** \brief  Length for Ifx_VADC_GLOBEFLAG_Bits.REVGLBCLR */
#define IFX_VADC_GLOBEFLAG_REVGLBCLR_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBEFLAG_Bits.REVGLBCLR */
#define IFX_VADC_GLOBEFLAG_REVGLBCLR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBEFLAG_Bits.REVGLBCLR */
#define IFX_VADC_GLOBEFLAG_REVGLBCLR_OFF (24u)

/** \brief  Length for Ifx_VADC_GLOBEFLAG_Bits.SEVGLB */
#define IFX_VADC_GLOBEFLAG_SEVGLB_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBEFLAG_Bits.SEVGLB */
#define IFX_VADC_GLOBEFLAG_SEVGLB_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBEFLAG_Bits.SEVGLB */
#define IFX_VADC_GLOBEFLAG_SEVGLB_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBEFLAG_Bits.SEVGLBCLR */
#define IFX_VADC_GLOBEFLAG_SEVGLBCLR_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBEFLAG_Bits.SEVGLBCLR */
#define IFX_VADC_GLOBEFLAG_SEVGLBCLR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBEFLAG_Bits.SEVGLBCLR */
#define IFX_VADC_GLOBEFLAG_SEVGLBCLR_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBEVNP_Bits.REV0NP */
#define IFX_VADC_GLOBEVNP_REV0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBEVNP_Bits.REV0NP */
#define IFX_VADC_GLOBEVNP_REV0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBEVNP_Bits.REV0NP */
#define IFX_VADC_GLOBEVNP_REV0NP_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBEVNP_Bits.SEV0NP */
#define IFX_VADC_GLOBEVNP_SEV0NP_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBEVNP_Bits.SEV0NP */
#define IFX_VADC_GLOBEVNP_SEV0NP_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBEVNP_Bits.SEV0NP */
#define IFX_VADC_GLOBEVNP_SEV0NP_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBRCR_Bits.DRCTR */
#define IFX_VADC_GLOBRCR_DRCTR_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBRCR_Bits.DRCTR */
#define IFX_VADC_GLOBRCR_DRCTR_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBRCR_Bits.DRCTR */
#define IFX_VADC_GLOBRCR_DRCTR_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBRCR_Bits.SRGEN */
#define IFX_VADC_GLOBRCR_SRGEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRCR_Bits.SRGEN */
#define IFX_VADC_GLOBRCR_SRGEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRCR_Bits.SRGEN */
#define IFX_VADC_GLOBRCR_SRGEN_OFF (31u)

/** \brief  Length for Ifx_VADC_GLOBRCR_Bits.WFR */
#define IFX_VADC_GLOBRCR_WFR_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRCR_Bits.WFR */
#define IFX_VADC_GLOBRCR_WFR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRCR_Bits.WFR */
#define IFX_VADC_GLOBRCR_WFR_OFF (24u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.CHNR */
#define IFX_VADC_GLOBRES_CHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.CHNR */
#define IFX_VADC_GLOBRES_CHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.CHNR */
#define IFX_VADC_GLOBRES_CHNR_OFF (20u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.CRS */
#define IFX_VADC_GLOBRES_CRS_LEN (2u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.CRS */
#define IFX_VADC_GLOBRES_CRS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.CRS */
#define IFX_VADC_GLOBRES_CRS_OFF (28u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.EMUX */
#define IFX_VADC_GLOBRES_EMUX_LEN (3u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.EMUX */
#define IFX_VADC_GLOBRES_EMUX_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.EMUX */
#define IFX_VADC_GLOBRES_EMUX_OFF (25u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.FCR */
#define IFX_VADC_GLOBRES_FCR_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.FCR */
#define IFX_VADC_GLOBRES_FCR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.FCR */
#define IFX_VADC_GLOBRES_FCR_OFF (30u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.GNR */
#define IFX_VADC_GLOBRES_GNR_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.GNR */
#define IFX_VADC_GLOBRES_GNR_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.GNR */
#define IFX_VADC_GLOBRES_GNR_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.RESULT */
#define IFX_VADC_GLOBRES_RESULT_LEN (16u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.RESULT */
#define IFX_VADC_GLOBRES_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.RESULT */
#define IFX_VADC_GLOBRES_RESULT_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBRES_Bits.VF */
#define IFX_VADC_GLOBRES_VF_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRES_Bits.VF */
#define IFX_VADC_GLOBRES_VF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRES_Bits.VF */
#define IFX_VADC_GLOBRES_VF_OFF (31u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.CHNR */
#define IFX_VADC_GLOBRESD_CHNR_LEN (5u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.CHNR */
#define IFX_VADC_GLOBRESD_CHNR_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.CHNR */
#define IFX_VADC_GLOBRESD_CHNR_OFF (20u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.CRS */
#define IFX_VADC_GLOBRESD_CRS_LEN (2u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.CRS */
#define IFX_VADC_GLOBRESD_CRS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.CRS */
#define IFX_VADC_GLOBRESD_CRS_OFF (28u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.EMUX */
#define IFX_VADC_GLOBRESD_EMUX_LEN (3u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.EMUX */
#define IFX_VADC_GLOBRESD_EMUX_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.EMUX */
#define IFX_VADC_GLOBRESD_EMUX_OFF (25u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.FCR */
#define IFX_VADC_GLOBRESD_FCR_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.FCR */
#define IFX_VADC_GLOBRESD_FCR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.FCR */
#define IFX_VADC_GLOBRESD_FCR_OFF (30u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.GNR */
#define IFX_VADC_GLOBRESD_GNR_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.GNR */
#define IFX_VADC_GLOBRESD_GNR_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.GNR */
#define IFX_VADC_GLOBRESD_GNR_OFF (16u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.RESULT */
#define IFX_VADC_GLOBRESD_RESULT_LEN (16u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.RESULT */
#define IFX_VADC_GLOBRESD_RESULT_MSK (0xffffu)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.RESULT */
#define IFX_VADC_GLOBRESD_RESULT_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBRESD_Bits.VF */
#define IFX_VADC_GLOBRESD_VF_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBRESD_Bits.VF */
#define IFX_VADC_GLOBRESD_VF_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBRESD_Bits.VF */
#define IFX_VADC_GLOBRESD_VF_OFF (31u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.CDCH */
#define IFX_VADC_GLOBTF_CDCH_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.CDCH */
#define IFX_VADC_GLOBTF_CDCH_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.CDCH */
#define IFX_VADC_GLOBTF_CDCH_OFF (0u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.CDEN */
#define IFX_VADC_GLOBTF_CDEN_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.CDEN */
#define IFX_VADC_GLOBTF_CDEN_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.CDEN */
#define IFX_VADC_GLOBTF_CDEN_OFF (8u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.CDGR */
#define IFX_VADC_GLOBTF_CDGR_LEN (4u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.CDGR */
#define IFX_VADC_GLOBTF_CDGR_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.CDGR */
#define IFX_VADC_GLOBTF_CDGR_OFF (4u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.CDSEL */
#define IFX_VADC_GLOBTF_CDSEL_LEN (2u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.CDSEL */
#define IFX_VADC_GLOBTF_CDSEL_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.CDSEL */
#define IFX_VADC_GLOBTF_CDSEL_OFF (9u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.CDWC */
#define IFX_VADC_GLOBTF_CDWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.CDWC */
#define IFX_VADC_GLOBTF_CDWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.CDWC */
#define IFX_VADC_GLOBTF_CDWC_OFF (15u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.MDPD */
#define IFX_VADC_GLOBTF_MDPD_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.MDPD */
#define IFX_VADC_GLOBTF_MDPD_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.MDPD */
#define IFX_VADC_GLOBTF_MDPD_OFF (17u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.MDPU */
#define IFX_VADC_GLOBTF_MDPU_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.MDPU */
#define IFX_VADC_GLOBTF_MDPU_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.MDPU */
#define IFX_VADC_GLOBTF_MDPU_OFF (18u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.MDWC */
#define IFX_VADC_GLOBTF_MDWC_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.MDWC */
#define IFX_VADC_GLOBTF_MDWC_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.MDWC */
#define IFX_VADC_GLOBTF_MDWC_OFF (23u)

/** \brief  Length for Ifx_VADC_GLOBTF_Bits.PDD */
#define IFX_VADC_GLOBTF_PDD_LEN (1u)

/** \brief  Mask for Ifx_VADC_GLOBTF_Bits.PDD */
#define IFX_VADC_GLOBTF_PDD_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_GLOBTF_Bits.PDD */
#define IFX_VADC_GLOBTF_PDD_OFF (16u)

/** \brief  Length for Ifx_VADC_ICLASS_Bits.CME */
#define IFX_VADC_ICLASS_CME_LEN (3u)

/** \brief  Mask for Ifx_VADC_ICLASS_Bits.CME */
#define IFX_VADC_ICLASS_CME_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_ICLASS_Bits.CME */
#define IFX_VADC_ICLASS_CME_OFF (24u)

/** \brief  Length for Ifx_VADC_ICLASS_Bits.CMS */
#define IFX_VADC_ICLASS_CMS_LEN (3u)

/** \brief  Mask for Ifx_VADC_ICLASS_Bits.CMS */
#define IFX_VADC_ICLASS_CMS_MSK (0x7u)

/** \brief  Offset for Ifx_VADC_ICLASS_Bits.CMS */
#define IFX_VADC_ICLASS_CMS_OFF (8u)

/** \brief  Length for Ifx_VADC_ICLASS_Bits.STCE */
#define IFX_VADC_ICLASS_STCE_LEN (5u)

/** \brief  Mask for Ifx_VADC_ICLASS_Bits.STCE */
#define IFX_VADC_ICLASS_STCE_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_ICLASS_Bits.STCE */
#define IFX_VADC_ICLASS_STCE_OFF (16u)

/** \brief  Length for Ifx_VADC_ICLASS_Bits.STCS */
#define IFX_VADC_ICLASS_STCS_LEN (5u)

/** \brief  Mask for Ifx_VADC_ICLASS_Bits.STCS */
#define IFX_VADC_ICLASS_STCS_MSK (0x1fu)

/** \brief  Offset for Ifx_VADC_ICLASS_Bits.STCS */
#define IFX_VADC_ICLASS_STCS_OFF (0u)

/** \brief  Length for Ifx_VADC_ID_Bits.MODNUMBER */
#define IFX_VADC_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_VADC_ID_Bits.MODNUMBER */
#define IFX_VADC_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_VADC_ID_Bits.MODNUMBER */
#define IFX_VADC_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_VADC_ID_Bits.MODREV */
#define IFX_VADC_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_VADC_ID_Bits.MODREV */
#define IFX_VADC_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_VADC_ID_Bits.MODREV */
#define IFX_VADC_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_VADC_ID_Bits.MODTYPE */
#define IFX_VADC_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_VADC_ID_Bits.MODTYPE */
#define IFX_VADC_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_VADC_ID_Bits.MODTYPE */
#define IFX_VADC_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_VADC_KRST0_Bits.RST */
#define IFX_VADC_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_VADC_KRST0_Bits.RST */
#define IFX_VADC_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_KRST0_Bits.RST */
#define IFX_VADC_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_VADC_KRST0_Bits.RSTSTAT */
#define IFX_VADC_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_VADC_KRST0_Bits.RSTSTAT */
#define IFX_VADC_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_KRST0_Bits.RSTSTAT */
#define IFX_VADC_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_VADC_KRST1_Bits.RST */
#define IFX_VADC_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_VADC_KRST1_Bits.RST */
#define IFX_VADC_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_KRST1_Bits.RST */
#define IFX_VADC_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_VADC_KRSTCLR_Bits.CLR */
#define IFX_VADC_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_VADC_KRSTCLR_Bits.CLR */
#define IFX_VADC_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_KRSTCLR_Bits.CLR */
#define IFX_VADC_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_VADC_OCS_Bits.SUS */
#define IFX_VADC_OCS_SUS_LEN (4u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.SUS */
#define IFX_VADC_OCS_SUS_MSK (0xfu)

/** \brief  Offset for Ifx_VADC_OCS_Bits.SUS */
#define IFX_VADC_OCS_SUS_OFF (24u)

/** \brief  Length for Ifx_VADC_OCS_Bits.SUS_P */
#define IFX_VADC_OCS_SUS_P_LEN (1u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.SUS_P */
#define IFX_VADC_OCS_SUS_P_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_OCS_Bits.SUS_P */
#define IFX_VADC_OCS_SUS_P_OFF (28u)

/** \brief  Length for Ifx_VADC_OCS_Bits.SUSSTA */
#define IFX_VADC_OCS_SUSSTA_LEN (1u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.SUSSTA */
#define IFX_VADC_OCS_SUSSTA_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_OCS_Bits.SUSSTA */
#define IFX_VADC_OCS_SUSSTA_OFF (29u)

/** \brief  Length for Ifx_VADC_OCS_Bits.TG_P */
#define IFX_VADC_OCS_TG_P_LEN (1u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.TG_P */
#define IFX_VADC_OCS_TG_P_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_OCS_Bits.TG_P */
#define IFX_VADC_OCS_TG_P_OFF (3u)

/** \brief  Length for Ifx_VADC_OCS_Bits.TGB */
#define IFX_VADC_OCS_TGB_LEN (1u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.TGB */
#define IFX_VADC_OCS_TGB_MSK (0x1u)

/** \brief  Offset for Ifx_VADC_OCS_Bits.TGB */
#define IFX_VADC_OCS_TGB_OFF (2u)

/** \brief  Length for Ifx_VADC_OCS_Bits.TGS */
#define IFX_VADC_OCS_TGS_LEN (2u)

/** \brief  Mask for Ifx_VADC_OCS_Bits.TGS */
#define IFX_VADC_OCS_TGS_MSK (0x3u)

/** \brief  Offset for Ifx_VADC_OCS_Bits.TGS */
#define IFX_VADC_OCS_TGS_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXVADC_BF_H */
