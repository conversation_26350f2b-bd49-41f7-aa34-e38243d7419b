	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35556a --dep-file=IfxStdIf_Timer.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.src ../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c'

	
$TC16X
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_initConfig',code,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_initConfig'
	.align	2
	
	.global	IfxStdIf_Timer_initConfig
; Function IfxStdIf_Timer_initConfig
.L32:
IfxStdIf_Timer_initConfig:	.type	func
	movh	d15,#17530
.L216:
	st.w	[a4],d15
.L217:
	mov	d15,#0
.L218:
	st.h	[a4]4,d15
.L219:
	mov	d15,#0
.L220:
	st.b	[a4]6,d15
.L221:
	mov	d15,#0
.L222:
	st.w	[a4]8,d15
.L223:
	mov	d15,#128
.L224:
	st.b	[a4]21,d15
.L225:
	mov	d15,#0
.L226:
	st.b	[a4]22,d15
.L227:
	mov	d15,#0
.L228:
	st.b	[a4]23,d15
.L229:
	mov	d15,#0
.L230:
	st.b	[a4]24,d15
.L231:
	mov	d15,#0
.L232:
	st.b	[a4]12,d15
.L233:
	mov	d15,#0
.L234:
	st.w	[a4]14,d15
.L235:
	mov	d15,#0
.L236:
	st.h	[a4]18,d15
.L237:
	mov	d15,#0
.L238:
	st.b	[a4]20,d15
.L239:
	mov	d15,#0
.L240:
	st.b	[a4]28,d15
.L241:
	mov	d15,#0
.L242:
	st.w	[a4]30,d15
.L243:
	ret
.L161:
	
__IfxStdIf_Timer_initConfig_function_end:
	.size	IfxStdIf_Timer_initConfig,__IfxStdIf_Timer_initConfig_function_end-IfxStdIf_Timer_initConfig
.L75:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetFrequency',code,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetFrequency'
	.align	2
	
; Function IfxStdIf_Timer_nopGetFrequency
.L34:
IfxStdIf_Timer_nopGetFrequency:	.type	func
	jz.a	a4,.L2
.L2:
	mov	d2,#0
.L285:
	j	.L3
.L3:
	ret
.L170:
	
__IfxStdIf_Timer_nopGetFrequency_function_end:
	.size	IfxStdIf_Timer_nopGetFrequency,__IfxStdIf_Timer_nopGetFrequency_function_end-IfxStdIf_Timer_nopGetFrequency
.L85:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetPeriod',code,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetPeriod'
	.align	2
	
; Function IfxStdIf_Timer_nopGetPeriod
.L36:
IfxStdIf_Timer_nopGetPeriod:	.type	func
	jz.a	a4,.L4
.L4:
	mov	d2,#0
.L290:
	j	.L5
.L5:
	ret
.L173:
	
__IfxStdIf_Timer_nopGetPeriod_function_end:
	.size	IfxStdIf_Timer_nopGetPeriod,__IfxStdIf_Timer_nopGetPeriod_function_end-IfxStdIf_Timer_nopGetPeriod
.L90:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetResolution',code,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetResolution'
	.align	2
	
; Function IfxStdIf_Timer_nopGetResolution
.L38:
IfxStdIf_Timer_nopGetResolution:	.type	func
	jz.a	a4,.L6
.L6:
	mov	d2,#0
.L295:
	j	.L7
.L7:
	ret
.L175:
	
__IfxStdIf_Timer_nopGetResolution_function_end:
	.size	IfxStdIf_Timer_nopGetResolution,__IfxStdIf_Timer_nopGetResolution_function_end-IfxStdIf_Timer_nopGetResolution
.L95:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetTrigger',code,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetTrigger'
	.align	2
	
; Function IfxStdIf_Timer_nopGetTrigger
.L40:
IfxStdIf_Timer_nopGetTrigger:	.type	func
	jz.a	a4,.L8
.L8:
	mov	d2,#0
.L300:
	j	.L9
.L9:
	ret
.L177:
	
__IfxStdIf_Timer_nopGetTrigger_function_end:
	.size	IfxStdIf_Timer_nopGetTrigger,__IfxStdIf_Timer_nopGetTrigger_function_end-IfxStdIf_Timer_nopGetTrigger
.L100:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetFrequency',code,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetFrequency'
	.align	2
	
; Function IfxStdIf_Timer_nopSetFrequency
.L42:
IfxStdIf_Timer_nopSetFrequency:	.type	func
	jz.a	a4,.L10
.L10:
	extr.u	d15,d4,#23,#8
	ne	d15,d15,#0
	jeq	d15,#0,.L11
.L11:
	mov	d2,#0
.L305:
	j	.L12
.L12:
	ret
.L180:
	
__IfxStdIf_Timer_nopSetFrequency_function_end:
	.size	IfxStdIf_Timer_nopSetFrequency,__IfxStdIf_Timer_nopSetFrequency_function_end-IfxStdIf_Timer_nopSetFrequency
.L105:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopUpdateInputFrequency',code,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopUpdateInputFrequency'
	.align	2
	
; Function IfxStdIf_Timer_nopUpdateInputFrequency
.L44:
IfxStdIf_Timer_nopUpdateInputFrequency:	.type	func
	jz.a	a4,.L13
.L13:
	ret
.L183:
	
__IfxStdIf_Timer_nopUpdateInputFrequency_function_end:
	.size	IfxStdIf_Timer_nopUpdateInputFrequency,__IfxStdIf_Timer_nopUpdateInputFrequency_function_end-IfxStdIf_Timer_nopUpdateInputFrequency
.L110:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopApplyUpdate',code,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopApplyUpdate'
	.align	2
	
; Function IfxStdIf_Timer_nopApplyUpdate
.L46:
IfxStdIf_Timer_nopApplyUpdate:	.type	func
	jz.a	a4,.L14
.L14:
	ret
.L185:
	
__IfxStdIf_Timer_nopApplyUpdate_function_end:
	.size	IfxStdIf_Timer_nopApplyUpdate,__IfxStdIf_Timer_nopApplyUpdate_function_end-IfxStdIf_Timer_nopApplyUpdate
.L115:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopDisableUpdate',code,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopDisableUpdate'
	.align	2
	
; Function IfxStdIf_Timer_nopDisableUpdate
.L48:
IfxStdIf_Timer_nopDisableUpdate:	.type	func
	jz.a	a4,.L15
.L15:
	ret
.L187:
	
__IfxStdIf_Timer_nopDisableUpdate_function_end:
	.size	IfxStdIf_Timer_nopDisableUpdate,__IfxStdIf_Timer_nopDisableUpdate_function_end-IfxStdIf_Timer_nopDisableUpdate
.L120:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetInputFrequency',code,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopGetInputFrequency'
	.align	2
	
; Function IfxStdIf_Timer_nopGetInputFrequency
.L50:
IfxStdIf_Timer_nopGetInputFrequency:	.type	func
	jz.a	a4,.L16
.L16:
	mov	d2,#0
.L322:
	j	.L17
.L17:
	ret
.L189:
	
__IfxStdIf_Timer_nopGetInputFrequency_function_end:
	.size	IfxStdIf_Timer_nopGetInputFrequency,__IfxStdIf_Timer_nopGetInputFrequency_function_end-IfxStdIf_Timer_nopGetInputFrequency
.L125:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopRun',code,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopRun'
	.align	2
	
; Function IfxStdIf_Timer_nopRun
.L52:
IfxStdIf_Timer_nopRun:	.type	func
	jz.a	a4,.L18
.L18:
	ret
.L191:
	
__IfxStdIf_Timer_nopRun_function_end:
	.size	IfxStdIf_Timer_nopRun,__IfxStdIf_Timer_nopRun_function_end-IfxStdIf_Timer_nopRun
.L130:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetPeriod',code,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetPeriod'
	.align	2
	
; Function IfxStdIf_Timer_nopSetPeriod
.L54:
IfxStdIf_Timer_nopSetPeriod:	.type	func
	jz.a	a4,.L19
.L19:
	jeq	d4,#0,.L20
.L20:
	mov	d2,#0
.L331:
	j	.L21
.L21:
	ret
.L193:
	
__IfxStdIf_Timer_nopSetPeriod_function_end:
	.size	IfxStdIf_Timer_nopSetPeriod,__IfxStdIf_Timer_nopSetPeriod_function_end-IfxStdIf_Timer_nopSetPeriod
.L135:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetSingleMode',code,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetSingleMode'
	.align	2
	
; Function IfxStdIf_Timer_nopSetSingleMode
.L56:
IfxStdIf_Timer_nopSetSingleMode:	.type	func
	jz.a	a4,.L22
.L22:
	jeq	d4,#0,.L23
.L23:
	ret
.L196:
	
__IfxStdIf_Timer_nopSetSingleMode_function_end:
	.size	IfxStdIf_Timer_nopSetSingleMode,__IfxStdIf_Timer_nopSetSingleMode_function_end-IfxStdIf_Timer_nopSetSingleMode
.L140:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetTrigger',code,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopSetTrigger'
	.align	2
	
; Function IfxStdIf_Timer_nopSetTrigger
.L58:
IfxStdIf_Timer_nopSetTrigger:	.type	func
	jz.a	a4,.L24
.L24:
	jeq	d4,#0,.L25
.L25:
	ret
.L199:
	
__IfxStdIf_Timer_nopSetTrigger_function_end:
	.size	IfxStdIf_Timer_nopSetTrigger,__IfxStdIf_Timer_nopSetTrigger_function_end-IfxStdIf_Timer_nopSetTrigger
.L145:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopStop',code,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopStop'
	.align	2
	
; Function IfxStdIf_Timer_nopStop
.L60:
IfxStdIf_Timer_nopStop:	.type	func
	jz.a	a4,.L26
.L26:
	ret
.L202:
	
__IfxStdIf_Timer_nopStop_function_end:
	.size	IfxStdIf_Timer_nopStop,__IfxStdIf_Timer_nopStop_function_end-IfxStdIf_Timer_nopStop
.L150:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopAckTimerIrq',code,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopAckTimerIrq'
	.align	2
	
; Function IfxStdIf_Timer_nopAckTimerIrq
.L62:
IfxStdIf_Timer_nopAckTimerIrq:	.type	func
	jz.a	a4,.L27
.L27:
	mov	d2,#0
.L348:
	j	.L28
.L28:
	ret
.L204:
	
__IfxStdIf_Timer_nopAckTimerIrq_function_end:
	.size	IfxStdIf_Timer_nopAckTimerIrq,__IfxStdIf_Timer_nopAckTimerIrq_function_end-IfxStdIf_Timer_nopAckTimerIrq
.L155:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopAckTriggerIrq',code,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_nopAckTriggerIrq'
	.align	2
	
; Function IfxStdIf_Timer_nopAckTriggerIrq
.L64:
IfxStdIf_Timer_nopAckTriggerIrq:	.type	func
	jz.a	a4,.L29
.L29:
	mov	d2,#0
.L353:
	j	.L30
.L30:
	ret
.L206:
	
__IfxStdIf_Timer_nopAckTriggerIrq_function_end:
	.size	IfxStdIf_Timer_nopAckTriggerIrq,__IfxStdIf_Timer_nopAckTriggerIrq_function_end-IfxStdIf_Timer_nopAckTriggerIrq
.L160:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Timer.IfxStdIf_Timer_initStdIf',code,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.text.IfxStdIf_Timer.IfxStdIf_Timer_initStdIf'
	.align	2
	
	.global	IfxStdIf_Timer_initStdIf
; Function IfxStdIf_Timer_initStdIf
.L66:
IfxStdIf_Timer_initStdIf:	.type	func
	st.a	[a4],a5
.L248:
	movh.a	a15,#@his(IfxStdIf_Timer_nopGetFrequency)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopGetFrequency)
.L249:
	st.a	[a4]4,a15
.L250:
	movh.a	a15,#@his(IfxStdIf_Timer_nopGetPeriod)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopGetPeriod)
.L251:
	st.a	[a4]8,a15
.L252:
	movh.a	a15,#@his(IfxStdIf_Timer_nopGetResolution)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopGetResolution)
.L253:
	st.a	[a4]12,a15
.L254:
	movh.a	a15,#@his(IfxStdIf_Timer_nopGetTrigger)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopGetTrigger)
.L255:
	st.a	[a4]16,a15
.L256:
	movh.a	a15,#@his(IfxStdIf_Timer_nopSetFrequency)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopSetFrequency)
.L257:
	st.a	[a4]20,a15
.L258:
	movh.a	a15,#@his(IfxStdIf_Timer_nopUpdateInputFrequency)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopUpdateInputFrequency)
.L259:
	st.a	[a4]24,a15
.L260:
	movh.a	a15,#@his(IfxStdIf_Timer_nopApplyUpdate)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopApplyUpdate)
.L261:
	st.a	[a4]28,a15
.L262:
	movh.a	a15,#@his(IfxStdIf_Timer_nopDisableUpdate)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopDisableUpdate)
.L263:
	st.a	[a4]32,a15
.L264:
	movh.a	a15,#@his(IfxStdIf_Timer_nopGetInputFrequency)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopGetInputFrequency)
.L265:
	st.a	[a4]36,a15
.L266:
	movh.a	a15,#@his(IfxStdIf_Timer_nopRun)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopRun)
.L267:
	st.a	[a4]40,a15
.L268:
	movh.a	a15,#@his(IfxStdIf_Timer_nopSetPeriod)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopSetPeriod)
.L269:
	st.a	[a4]44,a15
.L270:
	movh.a	a15,#@his(IfxStdIf_Timer_nopSetSingleMode)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopSetSingleMode)
.L271:
	st.a	[a4]48,a15
.L272:
	movh.a	a15,#@his(IfxStdIf_Timer_nopSetTrigger)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopSetTrigger)
.L273:
	st.a	[a4]52,a15
.L274:
	movh.a	a15,#@his(IfxStdIf_Timer_nopStop)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopStop)
.L275:
	st.a	[a4]56,a15
.L276:
	movh.a	a15,#@his(IfxStdIf_Timer_nopAckTimerIrq)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopAckTimerIrq)
.L277:
	st.a	[a4]60,a15
.L278:
	movh.a	a15,#@his(IfxStdIf_Timer_nopAckTriggerIrq)
	lea	a15,[a15]@los(IfxStdIf_Timer_nopAckTriggerIrq)
.L279:
	st.a	[a4]64,a15
.L280:
	ret
.L164:
	
__IfxStdIf_Timer_initStdIf_function_end:
	.size	IfxStdIf_Timer_initStdIf,__IfxStdIf_Timer_initStdIf_function_end-IfxStdIf_Timer_initStdIf
.L80:
	; End of function
	
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopGetFrequency'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopGetPeriod'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopGetResolution'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopGetTrigger'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopSetFrequency'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopUpdateInputFrequency'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopApplyUpdate'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopDisableUpdate'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopGetInputFrequency'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopRun'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopSetPeriod'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopSetSingleMode'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopSetTrigger'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopStop'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopAckTimerIrq'
	.calls	'__INDIRECT__','IfxStdIf_Timer_nopAckTriggerIrq'
	.calls	'IfxStdIf_Timer_initConfig','',0
	.calls	'IfxStdIf_Timer_nopGetFrequency','',0
	.calls	'IfxStdIf_Timer_nopGetPeriod','',0
	.calls	'IfxStdIf_Timer_nopGetResolution','',0
	.calls	'IfxStdIf_Timer_nopGetTrigger','',0
	.calls	'IfxStdIf_Timer_nopSetFrequency','',0
	.calls	'IfxStdIf_Timer_nopUpdateInputFrequency','',0
	.calls	'IfxStdIf_Timer_nopApplyUpdate','',0
	.calls	'IfxStdIf_Timer_nopDisableUpdate','',0
	.calls	'IfxStdIf_Timer_nopGetInputFrequency','',0
	.calls	'IfxStdIf_Timer_nopRun','',0
	.calls	'IfxStdIf_Timer_nopSetPeriod','',0
	.calls	'IfxStdIf_Timer_nopSetSingleMode','',0
	.calls	'IfxStdIf_Timer_nopSetTrigger','',0
	.calls	'IfxStdIf_Timer_nopStop','',0
	.calls	'IfxStdIf_Timer_nopAckTimerIrq','',0
	.calls	'IfxStdIf_Timer_nopAckTriggerIrq','',0
	.extern	__INDIRECT__
	.calls	'IfxStdIf_Timer_initStdIf','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L68:
	.word	44903
	.half	3
	.word	.L69
	.byte	4
.L67:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L70
	.byte	2,1,1,3
	.word	236
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	239
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L169:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	284
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	296
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	376
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	350
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	382
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	382
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	350
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5
.L179:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	491
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	491
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	508
	.byte	4,2,35,0,0,14
	.word	798
	.byte	3
	.word	837
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	842
	.byte	6,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	912
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1799
	.byte	4,2,35,0,0,15,4
	.word	491
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2574
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2794
	.byte	4,2,35,0,0,15,24
	.word	491
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3421
	.byte	4,2,35,0,0,15,8
	.word	491
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3746
	.byte	4,2,35,0,0,15,12
	.word	491
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4086
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4452
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5054
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5226
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	890
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5575
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5749
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6081
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6414
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6886
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6970
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7150
	.byte	4,2,35,0,0,15,76
	.word	491
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1188
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1759
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1878
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1918
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2102
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2317
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2534
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2754
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1918
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3068
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3108
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3381
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3697
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3737
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4037
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4077
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4412
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4698
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3737
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4845
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5014
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5186
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5361
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5535
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5709
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5885
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6041
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6374
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6722
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3737
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6846
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7095
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7354
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7394
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7450
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8017
	.byte	4,3,35,252,1,0,14
	.word	8057
	.byte	3
	.word	8660
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	8665
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	491
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	8670
	.byte	6,0
.L167:
	.byte	19
	.byte	'IfxStdIf_InterfaceDriver',0,8,118,15
	.word	382
	.byte	20
	.word	296
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	8884
	.byte	19
	.byte	'IfxStdIf_Timer_GetFrequency',0,7,102,19
	.word	8897
.L172:
	.byte	7
	.byte	'unsigned long int',0,4,7,20
	.word	8938
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	8959
	.byte	19
	.byte	'IfxStdIf_Timer_GetPeriod',0,7,108,26
	.word	8972
	.byte	19
	.byte	'IfxStdIf_Timer_GetResolution',0,7,114,19
	.word	8897
	.byte	19
	.byte	'IfxStdIf_Timer_GetTrigger',0,7,120,26
	.word	8972
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	21
	.word	296
	.byte	0,3
	.word	9081
	.byte	19
	.byte	'IfxStdIf_Timer_SetFrequency',0,7,128,1,19
	.word	9099
	.byte	22,1,1,21
	.word	382
	.byte	0,3
	.word	9141
	.byte	19
	.byte	'IfxStdIf_Timer_UpdateInputFrequency',0,7,134,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_ApplyUpdate',0,7,155,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_DisableUpdate',0,7,172,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_GetInputFrequency',0,7,178,1,19
	.word	8897
	.byte	19
	.byte	'IfxStdIf_Timer_Run',0,7,187,1,16
	.word	9150
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	21
	.word	8938
	.byte	0,3
	.word	9344
	.byte	19
	.byte	'IfxStdIf_Timer_SetPeriod',0,7,197,1,19
	.word	9362
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	491
	.byte	0,3
	.word	9401
	.byte	19
	.byte	'IfxStdIf_Timer_SetSingleMode',0,7,206,1,16
	.word	9415
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	8938
	.byte	0,3
	.word	9458
	.byte	19
	.byte	'IfxStdIf_Timer_SetTrigger',0,7,218,1,16
	.word	9472
	.byte	19
	.byte	'IfxStdIf_Timer_Stop',0,7,224,1,16
	.word	9150
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	9541
	.byte	19
	.byte	'IfxStdIf_Timer_AckTimerIrq',0,7,230,1,19
	.word	9554
	.byte	19
	.byte	'IfxStdIf_Timer_AckTriggerIrq',0,7,236,1,19
	.word	9554
	.byte	10
	.byte	'IfxStdIf_Timer_',0,7,240,1,8,68,13
	.byte	'driver',0
	.word	8851
	.byte	4,2,35,0,13
	.byte	'getFrequency',0
	.word	8902
	.byte	4,2,35,4,13
	.byte	'getPeriod',0
	.word	8977
	.byte	4,2,35,8,13
	.byte	'getResolution',0
	.word	9010
	.byte	4,2,35,12,13
	.byte	'getTrigger',0
	.word	9047
	.byte	4,2,35,16,13
	.byte	'setFrequency',0
	.word	9104
	.byte	4,2,35,20,13
	.byte	'updateInputFrequency',0
	.word	9155
	.byte	4,2,35,24,13
	.byte	'applyUpdate',0
	.word	9200
	.byte	4,2,35,28,13
	.byte	'disableUpdate',0
	.word	9236
	.byte	4,2,35,32,13
	.byte	'getInputFrequency',0
	.word	9274
	.byte	4,2,35,36,13
	.byte	'run',0
	.word	9316
	.byte	4,2,35,40,13
	.byte	'setPeriod',0
	.word	9367
	.byte	4,2,35,44,13
	.byte	'setSingleMode',0
	.word	9420
	.byte	4,2,35,48,13
	.byte	'setTrigger',0
	.word	9477
	.byte	4,2,35,52,13
	.byte	'stop',0
	.word	9512
	.byte	4,2,35,56,13
	.byte	'ackTimerIrq',0
	.word	9559
	.byte	4,2,35,60,13
	.byte	'ackTriggerIrq',0
	.word	9595
	.byte	4,2,35,64,0
.L165:
	.byte	3
	.word	9633
	.byte	8
	.byte	'IfxStdIf_Timer_getInputFrequency',0,3,7,236,2,20
	.word	296
	.byte	1,1,5
	.byte	'stdIf',0,7,236,2,69
	.word	10012
	.byte	6,0,3
	.word	376
	.byte	3
	.word	8884
	.byte	3
	.word	8959
	.byte	3
	.word	8884
	.byte	3
	.word	8959
	.byte	3
	.word	9081
	.byte	3
	.word	9141
	.byte	3
	.word	9141
	.byte	3
	.word	9141
	.byte	3
	.word	8884
	.byte	3
	.word	9141
	.byte	3
	.word	9344
	.byte	3
	.word	9401
	.byte	3
	.word	9458
	.byte	3
	.word	9141
	.byte	3
	.word	9541
	.byte	3
	.word	9541
	.byte	8
	.byte	'IfxStdIf_Timer_tickToS',0,3,7,182,3,20
	.word	296
	.byte	1,1,5
	.byte	'clockFreq',0,7,182,3,51
	.word	296
	.byte	5
	.byte	'ticks',0,7,182,3,77
	.word	8938
	.byte	6,0,23
	.word	244
	.byte	24
	.word	270
	.byte	6,0,23
	.word	305
	.byte	24
	.word	337
	.byte	6,0,23
	.word	387
	.byte	24
	.word	406
	.byte	6,0,23
	.word	422
	.byte	24
	.word	437
	.byte	24
	.word	451
	.byte	6,0,23
	.word	847
	.byte	24
	.word	875
	.byte	6,0,23
	.word	8773
	.byte	24
	.word	8801
	.byte	24
	.word	8815
	.byte	24
	.word	8833
	.byte	6,0,23
	.word	10017
	.byte	24
	.word	10062
	.byte	6,0,23
	.word	10164
	.byte	24
	.word	10199
	.byte	24
	.word	10218
	.byte	6,0,17,9,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,25,7,134,2,9,16,13
	.byte	'enabled',0
	.word	491
	.byte	1,2,35,0,13
	.byte	'triggerPoint',0
	.word	8938
	.byte	4,2,35,2,13
	.byte	'isrPriority',0
	.word	890
	.byte	2,2,35,6,13
	.byte	'isrProvider',0
	.word	10351
	.byte	1,2,35,8,13
	.byte	'outputMode',0
	.word	10410
	.byte	1,2,35,9,13
	.byte	'outputDriver',0
	.word	10480
	.byte	1,2,35,10,13
	.byte	'risingEdgeAtPeriod',0
	.word	491
	.byte	1,2,35,11,13
	.byte	'outputEnabled',0
	.word	491
	.byte	1,2,35,12,0,17,7,88,9,1,18
	.byte	'IfxStdIf_Timer_CountDir_up',0,0,18
	.byte	'IfxStdIf_Timer_CountDir_upAndDown',0,1,18
	.byte	'IfxStdIf_Timer_CountDir_down',0,2,0,25,7,147,2,9,36,13
	.byte	'frequency',0
	.word	296
	.byte	4,2,35,0,13
	.byte	'isrPriority',0
	.word	890
	.byte	2,2,35,4,13
	.byte	'isrProvider',0
	.word	10351
	.byte	1,2,35,6,13
	.byte	'minResolution',0
	.word	296
	.byte	4,2,35,8,13
	.byte	'trigger',0
	.word	10895
	.byte	16,2,35,12,13
	.byte	'countDir',0
	.word	11076
	.byte	1,2,35,28,13
	.byte	'startOffset',0
	.word	296
	.byte	4,2,35,30,0
.L162:
	.byte	3
	.word	11178
	.byte	26
	.byte	'__INDIRECT__',0,10,1,1,1,1,1,7
	.byte	'short int',0,2,5,19
	.byte	'__wchar_t',0,10,1,1
	.word	11350
	.byte	19
	.byte	'__size_t',0,10,1,1
	.word	468
	.byte	19
	.byte	'__ptrdiff_t',0,10,1,1
	.word	484
	.byte	27,1,3
	.word	11418
	.byte	19
	.byte	'__codeptr',0,10,1,1
	.word	11420
	.byte	19
	.byte	'boolean',0,11,101,29
	.word	491
	.byte	19
	.byte	'uint8',0,11,105,29
	.word	491
	.byte	19
	.byte	'uint16',0,11,109,29
	.word	890
	.byte	19
	.byte	'uint32',0,11,113,29
	.word	8938
	.byte	19
	.byte	'uint64',0,11,118,29
	.word	350
	.byte	19
	.byte	'sint16',0,11,126,29
	.word	11350
	.byte	7
	.byte	'long int',0,4,5,19
	.byte	'sint32',0,11,131,1,29
	.word	11533
	.byte	7
	.byte	'long long int',0,8,5,19
	.byte	'sint64',0,11,138,1,29
	.word	11561
	.byte	19
	.byte	'float32',0,11,167,1,29
	.word	296
	.byte	19
	.byte	'pvoid',0,12,57,28
	.word	382
	.byte	19
	.byte	'Ifx_TickTime',0,12,79,28
	.word	11561
	.byte	19
	.byte	'Ifx_Priority',0,12,103,16
	.word	890
	.byte	19
	.byte	'Ifx_TimerValue',0,12,104,16
	.word	8938
	.byte	19
	.byte	'IfxSrc_Tos',0,9,74,3
	.word	10351
	.byte	19
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	508
	.byte	19
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	798
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	11756
	.byte	19
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	11788
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,0,14
	.word	11814
	.byte	19
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	11873
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	11901
	.byte	19
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	11938
	.byte	15,64
	.word	798
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	11966
	.byte	64,2,35,0,0,14
	.word	11975
	.byte	19
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	12007
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	12032
	.byte	19
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	12104
	.byte	15,8
	.word	798
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	12130
	.byte	8,2,35,0,0,14
	.word	12139
	.byte	19
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	12175
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	12205
	.byte	19
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	12278
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12304
	.byte	19
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	12339
	.byte	15,192,1
	.word	798
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4077
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	12365
	.byte	192,1,2,35,16,0,14
	.word	12375
	.byte	19
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	12442
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	798
	.byte	4,2,35,4,0,14
	.word	12468
	.byte	19
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	12516
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12544
	.byte	19
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	12577
	.byte	15,40
	.word	491
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	12130
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	12130
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	12130
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	12130
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	798
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	798
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	12604
	.byte	40,2,35,40,0,14
	.word	12613
	.byte	19
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	12740
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12767
	.byte	19
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	12799
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12825
	.byte	19
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	12857
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	798
	.byte	4,2,35,8,0,14
	.word	12883
	.byte	19
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	12943
	.byte	15,16
	.word	491
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	12969
	.byte	16,2,35,16,0,14
	.word	12978
	.byte	19
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	13072
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3108
	.byte	24,2,35,24,0,14
	.word	13099
	.byte	19
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	13216
	.byte	15,12
	.word	798
	.byte	16,2,0,15,32
	.word	798
	.byte	16,7,0,15,32
	.word	13253
	.byte	16,0,0,15,88
	.word	491
	.byte	16,87,0,15,108
	.word	798
	.byte	16,26,0,15,96
	.word	491
	.byte	16,95,0,15,96
	.word	13253
	.byte	16,2,0,15,160,3
	.word	491
	.byte	16,159,3,0,15,64
	.word	13253
	.byte	16,1,0,15,192,3
	.word	491
	.byte	16,191,3,0,15,16
	.word	798
	.byte	16,3,0,15,64
	.word	13338
	.byte	16,3,0,15,192,2
	.word	491
	.byte	16,191,2,0,15,52
	.word	491
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	13244
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	1918
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	798
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	12130
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	3737
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	13262
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	13271
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	13280
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	13289
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	798
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4077
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	13298
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	13307
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	13298
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	13307
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	13318
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	13327
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	13347
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	13356
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	13244
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	13367
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	13244
	.byte	12,3,35,192,18,0,14
	.word	13376
	.byte	19
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	13836
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	13862
	.byte	19
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	13895
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	13922
	.byte	19
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	13995
	.byte	15,56
	.word	491
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	14022
	.byte	56,2,35,24,0,14
	.word	14031
	.byte	19
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	14154
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14180
	.byte	19
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	14212
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	798
	.byte	4,2,35,16,0,14
	.word	14238
	.byte	19
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	14323
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14349
	.byte	19
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	14381
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	13253
	.byte	32,2,35,0,0,14
	.word	14407
	.byte	19
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	14440
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	13253
	.byte	32,2,35,0,0,14
	.word	14467
	.byte	19
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	14501
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	798
	.byte	4,2,35,20,0,14
	.word	14529
	.byte	19
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	14622
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14649
	.byte	19
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	14681
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	13338
	.byte	16,2,35,4,0,14
	.word	14707
	.byte	19
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	14753
	.byte	15,24
	.word	798
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	14779
	.byte	24,2,35,0,0,14
	.word	14788
	.byte	19
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	14821
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	13244
	.byte	12,2,35,0,0,14
	.word	14848
	.byte	19
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	14880
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,0,14
	.word	14906
	.byte	19
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	14952
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	14978
	.byte	19
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	15053
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	15082
	.byte	19
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	15156
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	15184
	.byte	19
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	15218
	.byte	15,4
	.word	11756
	.byte	16,0,0,14
	.word	15245
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	15254
	.byte	4,2,35,0,0,14
	.word	15259
	.byte	19
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	15295
	.byte	15,48
	.word	11814
	.byte	16,3,0,14
	.word	15323
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	15332
	.byte	48,2,35,0,0,14
	.word	15337
	.byte	19
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	15377
	.byte	14
	.word	11901
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	15407
	.byte	4,2,35,0,0,14
	.word	15412
	.byte	19
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	15446
	.byte	15,64
	.word	11975
	.byte	16,0,0,14
	.word	15473
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	15482
	.byte	64,2,35,0,0,14
	.word	15487
	.byte	19
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	15521
	.byte	15,32
	.word	12032
	.byte	16,1,0,14
	.word	15548
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	15557
	.byte	32,2,35,0,0,14
	.word	15562
	.byte	19
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	15598
	.byte	14
	.word	12139
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	15626
	.byte	8,2,35,0,0,14
	.word	15631
	.byte	19
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	15675
	.byte	15,16
	.word	12205
	.byte	16,0,0,14
	.word	15707
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	15716
	.byte	16,2,35,0,0,14
	.word	15721
	.byte	19
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	15755
	.byte	15,8
	.word	12304
	.byte	16,1,0,14
	.word	15782
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	15791
	.byte	8,2,35,0,0,14
	.word	15796
	.byte	19
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	15830
	.byte	15,208,1
	.word	12375
	.byte	16,0,0,14
	.word	15857
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	15867
	.byte	208,1,2,35,0,0,14
	.word	15872
	.byte	19
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	15908
	.byte	14
	.word	12468
	.byte	14
	.word	12468
	.byte	14
	.word	12468
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	15935
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	3737
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	15940
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	15945
	.byte	8,2,35,24,0,14
	.word	15950
	.byte	19
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	16041
	.byte	15,4
	.word	12544
	.byte	16,0,0,14
	.word	16070
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	16079
	.byte	4,2,35,0,0,14
	.word	16084
	.byte	19
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	16120
	.byte	15,80
	.word	12613
	.byte	16,0,0,14
	.word	16148
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	16157
	.byte	80,2,35,0,0,14
	.word	16162
	.byte	19
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	16198
	.byte	15,4
	.word	12767
	.byte	16,0,0,14
	.word	16226
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	16235
	.byte	4,2,35,0,0,14
	.word	16240
	.byte	19
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	16274
	.byte	15,4
	.word	12825
	.byte	16,0,0,14
	.word	16301
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	16310
	.byte	4,2,35,0,0,14
	.word	16315
	.byte	19
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	16349
	.byte	15,12
	.word	12883
	.byte	16,0,0,14
	.word	16376
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	16385
	.byte	12,2,35,0,0,14
	.word	16390
	.byte	19
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	16424
	.byte	15,64
	.word	12978
	.byte	16,1,0,14
	.word	16451
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	16460
	.byte	64,2,35,0,0,14
	.word	16465
	.byte	19
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	16501
	.byte	15,48
	.word	13099
	.byte	16,0,0,14
	.word	16529
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	16538
	.byte	48,2,35,0,0,14
	.word	16543
	.byte	19
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	16581
	.byte	15,204,18
	.word	13376
	.byte	16,0,0,14
	.word	16610
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	16620
	.byte	204,18,2,35,0,0,14
	.word	16625
	.byte	19
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	16661
	.byte	15,4
	.word	13862
	.byte	16,0,0,14
	.word	16688
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	16697
	.byte	4,2,35,0,0,14
	.word	16702
	.byte	19
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	16738
	.byte	15,64
	.word	13922
	.byte	16,3,0,14
	.word	16766
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	16775
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	798
	.byte	4,2,35,64,0,14
	.word	16780
	.byte	19
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	16829
	.byte	15,80
	.word	14031
	.byte	16,0,0,14
	.word	16857
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	16866
	.byte	80,2,35,0,0,14
	.word	16871
	.byte	19
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	16905
	.byte	15,4
	.word	14180
	.byte	16,0,0,14
	.word	16932
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	16941
	.byte	4,2,35,0,0,14
	.word	16946
	.byte	19
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	16980
	.byte	15,40
	.word	14238
	.byte	16,1,0,14
	.word	17007
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	17016
	.byte	40,2,35,0,0,14
	.word	17021
	.byte	19
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	17055
	.byte	15,8
	.word	14349
	.byte	16,1,0,14
	.word	17082
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	17091
	.byte	8,2,35,0,0,14
	.word	17096
	.byte	19
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	17130
	.byte	15,32
	.word	14407
	.byte	16,0,0,14
	.word	17157
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	17166
	.byte	32,2,35,0,0,14
	.word	17171
	.byte	19
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	17207
	.byte	15,32
	.word	14467
	.byte	16,0,0,14
	.word	17235
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	17244
	.byte	32,2,35,0,0,14
	.word	17249
	.byte	19
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	17287
	.byte	15,96
	.word	14529
	.byte	16,3,0,14
	.word	17316
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	17325
	.byte	96,2,35,0,0,14
	.word	17330
	.byte	19
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	17366
	.byte	15,4
	.word	14649
	.byte	16,0,0,14
	.word	17394
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	17403
	.byte	4,2,35,0,0,14
	.word	17408
	.byte	19
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	17442
	.byte	14
	.word	14707
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	17469
	.byte	20,2,35,0,0,14
	.word	17474
	.byte	19
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	17508
	.byte	15,24
	.word	14788
	.byte	16,0,0,14
	.word	17535
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	17544
	.byte	24,2,35,0,0,14
	.word	17549
	.byte	19
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	17585
	.byte	15,12
	.word	14848
	.byte	16,0,0,14
	.word	17613
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	17622
	.byte	12,2,35,0,0,14
	.word	17627
	.byte	19
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	17661
	.byte	15,16
	.word	14906
	.byte	16,1,0,14
	.word	17688
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	17697
	.byte	16,2,35,0,0,14
	.word	17702
	.byte	19
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	17736
	.byte	15,64
	.word	15082
	.byte	16,3,0,14
	.word	17763
	.byte	15,224,1
	.word	491
	.byte	16,223,1,0,15,32
	.word	14978
	.byte	16,1,0,14
	.word	17788
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	17772
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	17777
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	17797
	.byte	32,3,35,160,2,0,14
	.word	17802
	.byte	19
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	17871
	.byte	14
	.word	15184
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	17899
	.byte	4,2,35,0,0,14
	.word	17904
	.byte	19
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	17940
	.byte	19
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	7490
	.byte	19
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7403
	.byte	19
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	3746
	.byte	19
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	1799
	.byte	19
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	2794
	.byte	19
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	1927
	.byte	19
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2574
	.byte	19
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2142
	.byte	19
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2357
	.byte	19
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	6762
	.byte	19
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	6886
	.byte	19
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	6970
	.byte	19
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7150
	.byte	19
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5401
	.byte	19
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	5925
	.byte	19
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	5575
	.byte	19
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	5749
	.byte	19
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6414
	.byte	19
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1228
	.byte	19
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	4738
	.byte	19
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5226
	.byte	19
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	4885
	.byte	19
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5054
	.byte	19
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6081
	.byte	19
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	912
	.byte	19
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4452
	.byte	19
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4086
	.byte	19
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3117
	.byte	19
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3421
	.byte	19
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8017
	.byte	19
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7450
	.byte	19
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4037
	.byte	19
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	1878
	.byte	19
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3068
	.byte	19
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2102
	.byte	19
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	2754
	.byte	19
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2317
	.byte	19
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	2534
	.byte	19
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	6846
	.byte	19
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7095
	.byte	19
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7354
	.byte	19
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	6722
	.byte	19
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	5535
	.byte	19
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6041
	.byte	19
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	5709
	.byte	19
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	5885
	.byte	19
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	1759
	.byte	19
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6374
	.byte	19
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	4845
	.byte	19
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5361
	.byte	19
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5014
	.byte	19
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5186
	.byte	19
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1188
	.byte	19
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	4698
	.byte	19
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4412
	.byte	19
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3381
	.byte	19
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	3697
	.byte	14
	.word	8057
	.byte	19
	.byte	'Ifx_P',0,6,139,6,3
	.word	19286
	.byte	17,13,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,19
	.byte	'IfxScu_WDTCON1_IR',0,13,255,10,3
	.word	19306
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,14,45,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_ACCEN0_Bits',0,14,79,3
	.word	19428
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,14,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN1_Bits',0,14,85,3
	.word	19985
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,14,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,19
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,14,94,3
	.word	20062
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,14,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	491
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	491
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON0_Bits',0,14,111,3
	.word	20198
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,14,114,16,4,11
	.byte	'CANDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	491
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON1_Bits',0,14,126,3
	.word	20478
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,14,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON2_Bits',0,14,135,1,3
	.word	20716
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,14,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	491
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON3_Bits',0,14,150,1,3
	.word	20844
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,14,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	491
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON4_Bits',0,14,165,1,3
	.word	21087
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,14,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON5_Bits',0,14,174,1,3
	.word	21322
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,14,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON6_Bits',0,14,181,1,3
	.word	21450
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,14,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON7_Bits',0,14,188,1,3
	.word	21550
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,14,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	491
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CHIPID_Bits',0,14,202,1,3
	.word	21650
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,14,205,1,16,4,11
	.byte	'PWD',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	468
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_DTSCON_Bits',0,14,213,1,3
	.word	21858
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,14,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	890
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_DTSLIM_Bits',0,14,225,1,3
	.word	22023
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,14,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,14,235,1,3
	.word	22206
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,14,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	468
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EICR_Bits',0,14,129,2,3
	.word	22360
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,14,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_EIFR_Bits',0,14,143,2,3
	.word	22724
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,14,146,2,16,4,11
	.byte	'POL',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	890
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	491
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_EMSR_Bits',0,14,159,2,3
	.word	22935
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,14,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,19
	.byte	'Ifx_SCU_ESRCFG_Bits',0,14,167,2,3
	.word	23187
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,14,170,2,16,4,11
	.byte	'ARI',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_ESROCFG_Bits',0,14,175,2,3
	.word	23305
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,14,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVR13CON_Bits',0,14,185,2,3
	.word	23416
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,14,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVR33CON_Bits',0,14,195,2,3
	.word	23579
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,14,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,14,205,2,3
	.word	23742
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,14,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,14,215,2,3
	.word	23900
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,14,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	491
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	491
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	491
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	491
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	890
	.byte	10,0,2,35,2,0,19
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,14,232,2,3
	.word	24065
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,14,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	491
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	890
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	491
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,14,245,2,3
	.word	24394
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,14,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVROVMON_Bits',0,14,255,2,3
	.word	24615
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,14,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,14,142,3,3
	.word	24778
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,14,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,14,152,3,3
	.word	25050
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,14,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,14,162,3,3
	.word	25203
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,14,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,14,172,3,3
	.word	25359
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,14,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,14,181,3,3
	.word	25521
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,14,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,14,191,3,3
	.word	25664
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,14,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,14,200,3,3
	.word	25829
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,14,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,14,211,3,3
	.word	25974
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,14,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	491
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,14,222,3,3
	.word	26155
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,14,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,14,232,3,3
	.word	26329
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,14,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,14,241,3,3
	.word	26489
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,14,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,14,130,4,3
	.word	26633
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,14,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,14,139,4,3
	.word	26907
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,14,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,14,149,4,3
	.word	27046
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,14,152,4,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	491
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	890
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	491
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	491
	.byte	8,0,2,35,3,0,19
	.byte	'Ifx_SCU_EXTCON_Bits',0,14,163,4,3
	.word	27209
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,14,166,4,16,4,11
	.byte	'STEP',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	890
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_FDR_Bits',0,14,174,4,3
	.word	27427
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,14,177,4,16,4,11
	.byte	'FS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	8,0,2,35,3,0,19
	.byte	'Ifx_SCU_FMR_Bits',0,14,197,4,3
	.word	27590
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,14,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_ID_Bits',0,14,205,4,3
	.word	27926
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,14,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	491
	.byte	2,0,2,35,3,0,19
	.byte	'Ifx_SCU_IGCR_Bits',0,14,232,4,3
	.word	28033
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,14,235,4,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_IN_Bits',0,14,240,4,3
	.word	28485
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,14,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_IOCR_Bits',0,14,250,4,3
	.word	28584
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,14,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	890
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,14,131,5,3
	.word	28734
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,14,134,5,16,4,11
	.byte	'SEED',0,4
	.word	468
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,14,141,5,3
	.word	28883
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,14,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,14,149,5,3
	.word	29044
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,14,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	890
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_LCLCON_Bits',0,14,158,5,3
	.word	29174
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,14,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_LCLTEST_Bits',0,14,166,5,3
	.word	29306
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,14,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	491
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	890
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_MANID_Bits',0,14,174,5,3
	.word	29421
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,14,177,5,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	890
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	890
	.byte	14,0,2,35,2,0,19
	.byte	'Ifx_SCU_OMR_Bits',0,14,185,5,3
	.word	29532
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,14,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	491
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	491
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	491
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_OSCCON_Bits',0,14,209,5,3
	.word	29690
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,14,212,5,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_OUT_Bits',0,14,217,5,3
	.word	30102
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,14,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	890
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	6,0,2,35,3,0,19
	.byte	'Ifx_SCU_OVCCON_Bits',0,14,233,5,3
	.word	30203
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,14,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,19
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,14,242,5,3
	.word	30470
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,14,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDISC_Bits',0,14,250,5,3
	.word	30606
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,14,253,5,16,4,11
	.byte	'PD0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDR_Bits',0,14,132,6,3
	.word	30717
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,14,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDRR_Bits',0,14,146,6,3
	.word	30850
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,14,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PLLCON0_Bits',0,14,166,6,3
	.word	31053
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,14,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	491
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	491
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	9,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLCON1_Bits',0,14,177,6,3
	.word	31409
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,14,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLCON2_Bits',0,14,184,6,3
	.word	31587
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,14,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	491
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,14,204,6,3
	.word	31687
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,14,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	491
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	9,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,14,215,6,3
	.word	32057
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,14,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,14,227,6,3
	.word	32243
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,14,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,14,241,6,3
	.word	32441
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,14,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,19
	.byte	'Ifx_SCU_PMCSR_Bits',0,14,251,6,3
	.word	32674
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,14,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	491
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	491
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	491
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,14,153,7,3
	.word	32826
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,14,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	491
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,14,170,7,3
	.word	33393
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,14,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	491
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,14,187,7,3
	.word	33687
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,14,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	491
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	491
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,14,214,7,3
	.word	33965
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,14,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	890
	.byte	14,0,2,35,2,0,19
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,14,230,7,3
	.word	34461
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,14,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	890
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_RSTCON2_Bits',0,14,243,7,3
	.word	34774
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,14,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	491
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_RSTCON_Bits',0,14,129,8,3
	.word	34983
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,14,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	491
	.byte	3,0,2,35,3,0,19
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,14,155,8,3
	.word	35194
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,14,158,8,16,4,11
	.byte	'HBT',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,19
	.byte	'Ifx_SCU_SAFECON_Bits',0,14,162,8,3
	.word	35626
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,14,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	491
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	491
	.byte	7,0,2,35,3,0,19
	.byte	'Ifx_SCU_STSTAT_Bits',0,14,178,8,3
	.word	35722
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,14,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,14,186,8,3
	.word	35982
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,14,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	491
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,19
	.byte	'Ifx_SCU_SYSCON_Bits',0,14,198,8,3
	.word	36107
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,14,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,14,208,8,3
	.word	36304
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,14,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,14,218,8,3
	.word	36457
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,14,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSET_Bits',0,14,228,8,3
	.word	36610
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,14,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,14,238,8,3
	.word	36763
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,14,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	36918
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36918
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36918
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36918
	.byte	16,0,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,14,247,8,3
	.word	36934
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,14,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,14,134,9,3
	.word	37064
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,14,137,9,16,4,11
	.byte	'AE',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,14,150,9,3
	.word	37302
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,14,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	36918
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36918
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36918
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36918
	.byte	16,0,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,14,159,9,3
	.word	37525
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,14,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,14,175,9,3
	.word	37651
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,14,178,9,16,4,11
	.byte	'AE',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,14,191,9,3
	.word	37903
	.byte	12,14,199,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19428
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN0',0,14,204,9,3
	.word	38122
	.byte	12,14,207,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19985
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN1',0,14,212,9,3
	.word	38186
	.byte	12,14,215,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20062
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ARSTDIS',0,14,220,9,3
	.word	38250
	.byte	12,14,223,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20198
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON0',0,14,228,9,3
	.word	38315
	.byte	12,14,231,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20478
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON1',0,14,236,9,3
	.word	38380
	.byte	12,14,239,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20716
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON2',0,14,244,9,3
	.word	38445
	.byte	12,14,247,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20844
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON3',0,14,252,9,3
	.word	38510
	.byte	12,14,255,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21087
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON4',0,14,132,10,3
	.word	38575
	.byte	12,14,135,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21322
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON5',0,14,140,10,3
	.word	38640
	.byte	12,14,143,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21450
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON6',0,14,148,10,3
	.word	38705
	.byte	12,14,151,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21550
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON7',0,14,156,10,3
	.word	38770
	.byte	12,14,159,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21650
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CHIPID',0,14,164,10,3
	.word	38835
	.byte	12,14,167,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21858
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSCON',0,14,172,10,3
	.word	38899
	.byte	12,14,175,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22023
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSLIM',0,14,180,10,3
	.word	38963
	.byte	12,14,183,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22206
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSSTAT',0,14,188,10,3
	.word	39027
	.byte	12,14,191,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22360
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EICR',0,14,196,10,3
	.word	39092
	.byte	12,14,199,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22724
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EIFR',0,14,204,10,3
	.word	39154
	.byte	12,14,207,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22935
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EMSR',0,14,212,10,3
	.word	39216
	.byte	12,14,215,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23187
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ESRCFG',0,14,220,10,3
	.word	39278
	.byte	12,14,223,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23305
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ESROCFG',0,14,228,10,3
	.word	39342
	.byte	12,14,231,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23416
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVR13CON',0,14,236,10,3
	.word	39407
	.byte	12,14,239,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23579
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVR33CON',0,14,244,10,3
	.word	39473
	.byte	12,14,247,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23742
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRADCSTAT',0,14,252,10,3
	.word	39539
	.byte	12,14,255,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23900
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRDVSTAT',0,14,132,11,3
	.word	39607
	.byte	12,14,135,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24065
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRMONCTRL',0,14,140,11,3
	.word	39674
	.byte	12,14,143,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24394
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVROSCCTRL',0,14,148,11,3
	.word	39742
	.byte	12,14,151,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24615
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVROVMON',0,14,156,11,3
	.word	39810
	.byte	12,14,159,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24778
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRRSTCON',0,14,164,11,3
	.word	39876
	.byte	12,14,167,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25050
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,14,172,11,3
	.word	39943
	.byte	12,14,175,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25203
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,14,180,11,3
	.word	40012
	.byte	12,14,183,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25359
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,14,188,11,3
	.word	40081
	.byte	12,14,191,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25521
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,14,196,11,3
	.word	40150
	.byte	12,14,199,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25664
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,14,204,11,3
	.word	40219
	.byte	12,14,207,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25829
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,14,212,11,3
	.word	40288
	.byte	12,14,215,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25974
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL1',0,14,220,11,3
	.word	40357
	.byte	12,14,223,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26155
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL2',0,14,228,11,3
	.word	40425
	.byte	12,14,231,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26329
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL3',0,14,236,11,3
	.word	40493
	.byte	12,14,239,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26489
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL4',0,14,244,11,3
	.word	40561
	.byte	12,14,247,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26633
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSTAT',0,14,252,11,3
	.word	40629
	.byte	12,14,255,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26907
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRTRIM',0,14,132,12,3
	.word	40694
	.byte	12,14,135,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27046
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRUVMON',0,14,140,12,3
	.word	40759
	.byte	12,14,143,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27209
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EXTCON',0,14,148,12,3
	.word	40825
	.byte	12,14,151,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27427
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_FDR',0,14,156,12,3
	.word	40889
	.byte	12,14,159,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27590
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_FMR',0,14,164,12,3
	.word	40950
	.byte	12,14,167,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27926
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ID',0,14,172,12,3
	.word	41011
	.byte	12,14,175,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28033
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IGCR',0,14,180,12,3
	.word	41071
	.byte	12,14,183,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28485
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IN',0,14,188,12,3
	.word	41133
	.byte	12,14,191,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28584
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IOCR',0,14,196,12,3
	.word	41193
	.byte	12,14,199,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28734
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL0',0,14,204,12,3
	.word	41255
	.byte	12,14,207,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28883
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL1',0,14,212,12,3
	.word	41323
	.byte	12,14,215,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29044
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL2',0,14,220,12,3
	.word	41391
	.byte	12,14,223,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29174
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LCLCON',0,14,228,12,3
	.word	41459
	.byte	12,14,231,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29306
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LCLTEST',0,14,236,12,3
	.word	41523
	.byte	12,14,239,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29421
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_MANID',0,14,244,12,3
	.word	41588
	.byte	12,14,247,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29532
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OMR',0,14,252,12,3
	.word	41651
	.byte	12,14,255,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29690
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OSCCON',0,14,132,13,3
	.word	41712
	.byte	12,14,135,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30102
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OUT',0,14,140,13,3
	.word	41776
	.byte	12,14,143,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30203
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OVCCON',0,14,148,13,3
	.word	41837
	.byte	12,14,151,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30470
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OVCENABLE',0,14,156,13,3
	.word	41901
	.byte	12,14,159,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30606
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDISC',0,14,164,13,3
	.word	41968
	.byte	12,14,167,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30717
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDR',0,14,172,13,3
	.word	42031
	.byte	12,14,175,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30850
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDRR',0,14,180,13,3
	.word	42092
	.byte	12,14,183,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31053
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON0',0,14,188,13,3
	.word	42154
	.byte	12,14,191,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31409
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON1',0,14,196,13,3
	.word	42219
	.byte	12,14,199,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31587
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON2',0,14,204,13,3
	.word	42284
	.byte	12,14,207,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31687
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYCON0',0,14,212,13,3
	.word	42349
	.byte	12,14,215,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32057
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYCON1',0,14,220,13,3
	.word	42418
	.byte	12,14,223,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32243
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYSTAT',0,14,228,13,3
	.word	42487
	.byte	12,14,231,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32441
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLSTAT',0,14,236,13,3
	.word	42556
	.byte	12,14,239,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32674
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMCSR',0,14,244,13,3
	.word	42621
	.byte	12,14,247,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32826
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR0',0,14,252,13,3
	.word	42684
	.byte	12,14,255,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33393
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR1',0,14,132,14,3
	.word	42749
	.byte	12,14,135,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33687
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR2',0,14,140,14,3
	.word	42814
	.byte	12,14,143,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33965
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWSTAT',0,14,148,14,3
	.word	42879
	.byte	12,14,151,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34461
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWSTATCLR',0,14,156,14,3
	.word	42945
	.byte	12,14,159,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34983
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTCON',0,14,164,14,3
	.word	43014
	.byte	12,14,167,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34774
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTCON2',0,14,172,14,3
	.word	43078
	.byte	12,14,175,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35194
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTSTAT',0,14,180,14,3
	.word	43143
	.byte	12,14,183,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35626
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SAFECON',0,14,188,14,3
	.word	43208
	.byte	12,14,191,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35722
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_STSTAT',0,14,196,14,3
	.word	43273
	.byte	12,14,199,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35982
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SWRSTCON',0,14,204,14,3
	.word	43337
	.byte	12,14,207,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36107
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SYSCON',0,14,212,14,3
	.word	43403
	.byte	12,14,215,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36304
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPCLR',0,14,220,14,3
	.word	43467
	.byte	12,14,223,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36457
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPDIS',0,14,228,14,3
	.word	43532
	.byte	12,14,231,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36610
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSET',0,14,236,14,3
	.word	43597
	.byte	12,14,239,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36763
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSTAT',0,14,244,14,3
	.word	43662
	.byte	12,14,247,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36934
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON0',0,14,252,14,3
	.word	43728
	.byte	12,14,255,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37064
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON1',0,14,132,15,3
	.word	43797
	.byte	12,14,135,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37302
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_SR',0,14,140,15,3
	.word	43866
	.byte	12,14,143,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37525
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON0',0,14,148,15,3
	.word	43933
	.byte	12,14,151,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37651
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON1',0,14,156,15,3
	.word	44000
	.byte	12,14,159,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37903
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_SR',0,14,164,15,3
	.word	44067
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,14,175,15,25,12,13
	.byte	'CON0',0
	.word	43728
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43797
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43866
	.byte	4,2,35,8,0,14
	.word	44132
	.byte	19
	.byte	'Ifx_SCU_WDTCPU',0,14,180,15,3
	.word	44195
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,14,183,15,25,12,13
	.byte	'CON0',0
	.word	43933
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44000
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44067
	.byte	4,2,35,8,0,14
	.word	44224
	.byte	19
	.byte	'Ifx_SCU_WDTS',0,14,188,15,3
	.word	44285
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,19
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	44312
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,19
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	44463
	.byte	19
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	10410
	.byte	19
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	10480
	.byte	19
	.byte	'IfxPort_State',0,5,178,1,3
	.word	8670
	.byte	19
	.byte	'IfxStdIf_Timer_CountDir',0,7,93,3
	.word	11076
	.byte	19
	.byte	'IfxStdIf_Timer',0,7,96,32
	.word	9633
	.byte	19
	.byte	'IfxStdIf_Timer_TrigConfig',0,7,144,2,3
	.word	10895
	.byte	19
	.byte	'IfxStdIf_Timer_Config',0,7,156,2,3
	.word	11178
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L69:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,22,0,3,8,58,15,59,15,57,15,73,19,0,0,20,21,1,73,19,54,15,39
	.byte	12,0,0,21,5,0,73,19,0,0,22,21,1,54,15,39,12,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,19,1,58,15,59,15
	.byte	57,15,11,15,0,0,26,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,27,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L70:
	.word	.L209-.L208
.L208:
	.half	3
	.word	.L211-.L210
.L210:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_Timer.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L211:
.L209:
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_info'
.L71:
	.word	316
	.half	3
	.word	.L72
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L74,.L73
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_initConfig',0,1,48,6,1,1,1
	.word	.L32,.L161,.L31
	.byte	4
	.byte	'config',0,1,48,55
	.word	.L162,.L163
	.byte	5
	.word	.L32,.L161
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_abbrev'
.L72:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_line'
.L73:
	.word	.L213-.L212
.L212:
	.half	3
	.word	.L215-.L214
.L214:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L215:
	.byte	5,42,7,0,5,2
	.word	.L32
	.byte	3,49,1,5,40,9
	.half	.L216-.L32
	.byte	1,5,42,9
	.half	.L217-.L216
	.byte	3,1,1,5,40,9
	.half	.L218-.L217
	.byte	1,5,42,9
	.half	.L219-.L218
	.byte	3,1,1,5,40,9
	.half	.L220-.L219
	.byte	1,5,42,9
	.half	.L221-.L220
	.byte	3,1,1,5,40,9
	.half	.L222-.L221
	.byte	1,5,42,9
	.half	.L223-.L222
	.byte	3,1,1,5,40,9
	.half	.L224-.L223
	.byte	1,5,42,9
	.half	.L225-.L224
	.byte	3,1,1,5,40,9
	.half	.L226-.L225
	.byte	1,5,42,9
	.half	.L227-.L226
	.byte	3,1,1,5,40,9
	.half	.L228-.L227
	.byte	1,5,42,9
	.half	.L229-.L228
	.byte	3,1,1,5,40,9
	.half	.L230-.L229
	.byte	1,5,42,9
	.half	.L231-.L230
	.byte	3,1,1,5,40,9
	.half	.L232-.L231
	.byte	1,5,42,9
	.half	.L233-.L232
	.byte	3,1,1,5,40,9
	.half	.L234-.L233
	.byte	1,5,42,9
	.half	.L235-.L234
	.byte	3,1,1,5,40,9
	.half	.L236-.L235
	.byte	1,5,42,9
	.half	.L237-.L236
	.byte	3,1,1,5,40,9
	.half	.L238-.L237
	.byte	1,5,42,9
	.half	.L239-.L238
	.byte	3,1,1,5,40,9
	.half	.L240-.L239
	.byte	1,5,35,9
	.half	.L241-.L240
	.byte	3,1,1,5,33,9
	.half	.L242-.L241
	.byte	1,5,1,9
	.half	.L243-.L242
	.byte	3,1,1,7,9
	.half	.L75-.L243
	.byte	0,1,1
.L213:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_ranges'
.L74:
	.word	-1,.L32,0,.L75-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_info'
.L76:
	.word	336
	.half	3
	.word	.L77
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79,.L78
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_initStdIf',0,1,146,1,6,1,1,1
	.word	.L66,.L164,.L65
	.byte	4
	.byte	'stdIf',0,1,146,1,47
	.word	.L165,.L166
	.byte	4
	.byte	'driver',0,1,146,1,79
	.word	.L167,.L168
	.byte	5
	.word	.L66,.L164
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_abbrev'
.L77:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_line'
.L78:
	.word	.L245-.L244
.L244:
	.half	3
	.word	.L247-.L246
.L246:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L247:
	.byte	5,33,7,0,5,2
	.word	.L66
	.byte	3,147,1,1,5,35,9
	.half	.L248-.L66
	.byte	3,1,1,5,33,9
	.half	.L249-.L248
	.byte	1,5,35,9
	.half	.L250-.L249
	.byte	3,1,1,5,33,9
	.half	.L251-.L250
	.byte	1,5,35,9
	.half	.L252-.L251
	.byte	3,1,1,5,33,9
	.half	.L253-.L252
	.byte	1,5,35,9
	.half	.L254-.L253
	.byte	3,1,1,5,33,9
	.half	.L255-.L254
	.byte	1,5,35,9
	.half	.L256-.L255
	.byte	3,1,1,5,33,9
	.half	.L257-.L256
	.byte	1,5,35,9
	.half	.L258-.L257
	.byte	3,1,1,5,33,9
	.half	.L259-.L258
	.byte	1,5,35,9
	.half	.L260-.L259
	.byte	3,1,1,5,33,9
	.half	.L261-.L260
	.byte	1,5,35,9
	.half	.L262-.L261
	.byte	3,1,1,5,33,9
	.half	.L263-.L262
	.byte	1,5,35,9
	.half	.L264-.L263
	.byte	3,1,1,5,33,9
	.half	.L265-.L264
	.byte	1,5,35,9
	.half	.L266-.L265
	.byte	3,1,1,5,33,9
	.half	.L267-.L266
	.byte	1,5,35,9
	.half	.L268-.L267
	.byte	3,1,1,5,33,9
	.half	.L269-.L268
	.byte	1,5,35,9
	.half	.L270-.L269
	.byte	3,1,1,5,33,9
	.half	.L271-.L270
	.byte	1,5,35,9
	.half	.L272-.L271
	.byte	3,1,1,5,33,9
	.half	.L273-.L272
	.byte	1,5,35,9
	.half	.L274-.L273
	.byte	3,1,1,5,33,9
	.half	.L275-.L274
	.byte	1,5,35,9
	.half	.L276-.L275
	.byte	3,1,1,5,33,9
	.half	.L277-.L276
	.byte	1,5,35,9
	.half	.L278-.L277
	.byte	3,1,1,5,33,9
	.half	.L279-.L278
	.byte	1,5,1,9
	.half	.L280-.L279
	.byte	3,1,1,7,9
	.half	.L80-.L280
	.byte	0,1,1
.L245:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_ranges'
.L79:
	.word	-1,.L66,0,.L80-.L66,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_info'
.L81:
	.word	323
	.half	3
	.word	.L82
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L84,.L83
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopGetFrequency',0,1,67,16
	.word	.L169
	.byte	1,1
	.word	.L34,.L170,.L33
	.byte	4
	.byte	'stdIf',0,1,67,72
	.word	.L167,.L171
	.byte	5
	.word	.L34,.L170
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_abbrev'
.L82:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_line'
.L83:
	.word	.L282-.L281
.L281:
	.half	3
	.word	.L284-.L283
.L283:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L284:
	.byte	5,2,7,0,5,2
	.word	.L34
	.byte	3,196,0,1,5,9,7,9
	.half	.L2-.L34
	.byte	3,1,1,5,2,9
	.half	.L285-.L2
	.byte	1,5,1,9
	.half	.L3-.L285
	.byte	3,1,1,7,9
	.half	.L85-.L3
	.byte	0,1,1
.L282:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_ranges'
.L84:
	.word	-1,.L34,0,.L85-.L34,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_info'
.L86:
	.word	320
	.half	3
	.word	.L87
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L89,.L88
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopGetPeriod',0,1,72,23
	.word	.L172
	.byte	1,1
	.word	.L36,.L173,.L35
	.byte	4
	.byte	'stdIf',0,1,72,76
	.word	.L167,.L174
	.byte	5
	.word	.L36,.L173
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_abbrev'
.L87:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_line'
.L88:
	.word	.L287-.L286
.L286:
	.half	3
	.word	.L289-.L288
.L288:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L289:
	.byte	5,2,7,0,5,2
	.word	.L36
	.byte	3,201,0,1,5,9,7,9
	.half	.L4-.L36
	.byte	3,1,1,5,2,9
	.half	.L290-.L4
	.byte	1,5,1,9
	.half	.L5-.L290
	.byte	3,1,1,7,9
	.half	.L90-.L5
	.byte	0,1,1
.L287:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_ranges'
.L89:
	.word	-1,.L36,0,.L90-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_info'
.L91:
	.word	324
	.half	3
	.word	.L92
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L94,.L93
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopGetResolution',0,1,77,16
	.word	.L169
	.byte	1,1
	.word	.L38,.L175,.L37
	.byte	4
	.byte	'stdIf',0,1,77,73
	.word	.L167,.L176
	.byte	5
	.word	.L38,.L175
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_abbrev'
.L92:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_line'
.L93:
	.word	.L292-.L291
.L291:
	.half	3
	.word	.L294-.L293
.L293:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L294:
	.byte	5,2,7,0,5,2
	.word	.L38
	.byte	3,206,0,1,5,9,7,9
	.half	.L6-.L38
	.byte	3,1,1,5,2,9
	.half	.L295-.L6
	.byte	1,5,1,9
	.half	.L7-.L295
	.byte	3,1,1,7,9
	.half	.L95-.L7
	.byte	0,1,1
.L292:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_ranges'
.L94:
	.word	-1,.L38,0,.L95-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_info'
.L96:
	.word	321
	.half	3
	.word	.L97
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L99,.L98
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopGetTrigger',0,1,82,23
	.word	.L172
	.byte	1,1
	.word	.L40,.L177,.L39
	.byte	4
	.byte	'stdIf',0,1,82,77
	.word	.L167,.L178
	.byte	5
	.word	.L40,.L177
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_abbrev'
.L97:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_line'
.L98:
	.word	.L297-.L296
.L296:
	.half	3
	.word	.L299-.L298
.L298:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L299:
	.byte	5,2,7,0,5,2
	.word	.L40
	.byte	3,211,0,1,5,9,7,9
	.half	.L8-.L40
	.byte	3,1,1,5,2,9
	.half	.L300-.L8
	.byte	1,5,1,9
	.half	.L9-.L300
	.byte	3,1,1,7,9
	.half	.L100-.L9
	.byte	0,1,1
.L297:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_ranges'
.L99:
	.word	-1,.L40,0,.L100-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_info'
.L101:
	.word	345
	.half	3
	.word	.L102
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L104,.L103
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopSetFrequency',0,1,87,16
	.word	.L179
	.byte	1,1
	.word	.L42,.L180,.L41
	.byte	4
	.byte	'stdIf',0,1,87,72
	.word	.L167,.L181
	.byte	4
	.byte	'frequency',0,1,87,87
	.word	.L169,.L182
	.byte	5
	.word	.L42,.L180
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_abbrev'
.L102:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_line'
.L103:
	.word	.L302-.L301
.L301:
	.half	3
	.word	.L304-.L303
.L303:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L304:
	.byte	5,2,7,0,5,2
	.word	.L42
	.byte	3,216,0,1,7,9
	.half	.L10-.L42
	.byte	3,1,1,5,9,9
	.half	.L11-.L10
	.byte	3,1,1,5,2,9
	.half	.L305-.L11
	.byte	1,5,1,9
	.half	.L12-.L305
	.byte	3,1,1,7,9
	.half	.L105-.L12
	.byte	0,1,1
.L302:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_ranges'
.L104:
	.word	-1,.L42,0,.L105-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_info'
.L106:
	.word	327
	.half	3
	.word	.L107
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L109,.L108
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopUpdateInputFrequency',0,1,93,13,1,1
	.word	.L44,.L183,.L43
	.byte	4
	.byte	'stdIf',0,1,93,77
	.word	.L167,.L184
	.byte	5
	.word	.L44,.L183
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_abbrev'
.L107:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_line'
.L108:
	.word	.L307-.L306
.L306:
	.half	3
	.word	.L309-.L308
.L308:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L309:
	.byte	5,2,7,0,5,2
	.word	.L44
	.byte	3,222,0,1,5,1,7,9
	.half	.L13-.L44
	.byte	3,1,1,7,9
	.half	.L110-.L13
	.byte	0,1,1
.L307:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_ranges'
.L109:
	.word	-1,.L44,0,.L110-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_info'
.L111:
	.word	318
	.half	3
	.word	.L112
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L114,.L113
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopApplyUpdate',0,1,97,13,1,1
	.word	.L46,.L185,.L45
	.byte	4
	.byte	'stdIf',0,1,97,68
	.word	.L167,.L186
	.byte	5
	.word	.L46,.L185
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_abbrev'
.L112:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_line'
.L113:
	.word	.L311-.L310
.L310:
	.half	3
	.word	.L313-.L312
.L312:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L313:
	.byte	5,2,7,0,5,2
	.word	.L46
	.byte	3,226,0,1,5,1,7,9
	.half	.L14-.L46
	.byte	3,1,1,7,9
	.half	.L115-.L14
	.byte	0,1,1
.L311:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_ranges'
.L114:
	.word	-1,.L46,0,.L115-.L46,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_info'
.L116:
	.word	320
	.half	3
	.word	.L117
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L119,.L118
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopDisableUpdate',0,1,101,13,1,1
	.word	.L48,.L187,.L47
	.byte	4
	.byte	'stdIf',0,1,101,70
	.word	.L167,.L188
	.byte	5
	.word	.L48,.L187
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_abbrev'
.L117:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_line'
.L118:
	.word	.L315-.L314
.L314:
	.half	3
	.word	.L317-.L316
.L316:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L317:
	.byte	5,2,7,0,5,2
	.word	.L48
	.byte	3,230,0,1,5,1,7,9
	.half	.L15-.L48
	.byte	3,1,1,7,9
	.half	.L120-.L15
	.byte	0,1,1
.L315:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_ranges'
.L119:
	.word	-1,.L48,0,.L120-.L48,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_info'
.L121:
	.word	328
	.half	3
	.word	.L122
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L124,.L123
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopGetInputFrequency',0,1,105,16
	.word	.L169
	.byte	1,1
	.word	.L50,.L189,.L49
	.byte	4
	.byte	'stdIf',0,1,105,77
	.word	.L167,.L190
	.byte	5
	.word	.L50,.L189
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_abbrev'
.L122:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_line'
.L123:
	.word	.L319-.L318
.L318:
	.half	3
	.word	.L321-.L320
.L320:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L321:
	.byte	5,2,7,0,5,2
	.word	.L50
	.byte	3,234,0,1,5,9,7,9
	.half	.L16-.L50
	.byte	3,1,1,5,2,9
	.half	.L322-.L16
	.byte	1,5,1,9
	.half	.L17-.L322
	.byte	3,1,1,7,9
	.half	.L125-.L17
	.byte	0,1,1
.L319:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_ranges'
.L124:
	.word	-1,.L50,0,.L125-.L50,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_info'
.L126:
	.word	310
	.half	3
	.word	.L127
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L129,.L128
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopRun',0,1,110,13,1,1
	.word	.L52,.L191,.L51
	.byte	4
	.byte	'stdIf',0,1,110,60
	.word	.L167,.L192
	.byte	5
	.word	.L52,.L191
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_abbrev'
.L127:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_line'
.L128:
	.word	.L324-.L323
.L323:
	.half	3
	.word	.L326-.L325
.L325:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L326:
	.byte	5,2,7,0,5,2
	.word	.L52
	.byte	3,239,0,1,5,1,7,9
	.half	.L18-.L52
	.byte	3,1,1,7,9
	.half	.L130-.L18
	.byte	0,1,1
.L324:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_ranges'
.L129:
	.word	-1,.L52,0,.L130-.L52,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_info'
.L131:
	.word	339
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134,.L133
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopSetPeriod',0,1,114,16
	.word	.L179
	.byte	1,1
	.word	.L54,.L193,.L53
	.byte	4
	.byte	'stdIf',0,1,114,69
	.word	.L167,.L194
	.byte	4
	.byte	'period',0,1,114,91
	.word	.L172,.L195
	.byte	5
	.word	.L54,.L193
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_line'
.L133:
	.word	.L328-.L327
.L327:
	.half	3
	.word	.L330-.L329
.L329:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L330:
	.byte	5,2,7,0,5,2
	.word	.L54
	.byte	3,243,0,1,7,9
	.half	.L19-.L54
	.byte	3,1,1,5,9,7,9
	.half	.L20-.L19
	.byte	3,1,1,5,2,9
	.half	.L331-.L20
	.byte	1,5,1,9
	.half	.L21-.L331
	.byte	3,1,1,7,9
	.half	.L135-.L21
	.byte	0,1,1
.L328:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L54,0,.L135-.L54,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_info'
.L136:
	.word	340
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L139,.L138
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopSetSingleMode',0,1,120,13,1,1
	.word	.L56,.L196,.L55
	.byte	4
	.byte	'stdIf',0,1,120,70
	.word	.L167,.L197
	.byte	4
	.byte	'enabled',0,1,120,85
	.word	.L179,.L198
	.byte	5
	.word	.L56,.L196
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_line'
.L138:
	.word	.L333-.L332
.L332:
	.half	3
	.word	.L335-.L334
.L334:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L335:
	.byte	5,2,7,0,5,2
	.word	.L56
	.byte	3,249,0,1,7,9
	.half	.L22-.L56
	.byte	3,1,1,5,1,7,9
	.half	.L23-.L22
	.byte	3,1,1,7,9
	.half	.L140-.L23
	.byte	0,1,1
.L333:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L56,0,.L140-.L56,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_info'
.L141:
	.word	342
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144,.L143
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopSetTrigger',0,1,125,13,1,1
	.word	.L58,.L199,.L57
	.byte	4
	.byte	'stdIf',0,1,125,67
	.word	.L167,.L200
	.byte	4
	.byte	'triggerPoint',0,1,125,89
	.word	.L172,.L201
	.byte	5
	.word	.L58,.L199
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_line'
.L143:
	.word	.L337-.L336
.L336:
	.half	3
	.word	.L339-.L338
.L338:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L339:
	.byte	5,2,7,0,5,2
	.word	.L58
	.byte	3,254,0,1,7,9
	.half	.L24-.L58
	.byte	3,1,1,5,1,7,9
	.half	.L25-.L24
	.byte	3,1,1,7,9
	.half	.L145-.L25
	.byte	0,1,1
.L337:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L58,0,.L145-.L58,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_info'
.L146:
	.word	313
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149,.L148
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopStop',0,1,130,1,13,1,1
	.word	.L60,.L202,.L59
	.byte	4
	.byte	'stdIf',0,1,130,1,61
	.word	.L167,.L203
	.byte	5
	.word	.L60,.L202
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_line'
.L148:
	.word	.L341-.L340
.L340:
	.half	3
	.word	.L343-.L342
.L342:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L343:
	.byte	5,2,7,0,5,2
	.word	.L60
	.byte	3,131,1,1,5,1,7,9
	.half	.L26-.L60
	.byte	3,1,1,7,9
	.half	.L150-.L26
	.byte	0,1,1
.L341:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_ranges'
.L149:
	.word	-1,.L60,0,.L150-.L60,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_info'
.L151:
	.word	324
	.half	3
	.word	.L152
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L154,.L153
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopAckTimerIrq',0,1,134,1,16
	.word	.L179
	.byte	1,1
	.word	.L62,.L204,.L61
	.byte	4
	.byte	'stdIf',0,1,134,1,71
	.word	.L167,.L205
	.byte	5
	.word	.L62,.L204
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_abbrev'
.L152:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_line'
.L153:
	.word	.L345-.L344
.L344:
	.half	3
	.word	.L347-.L346
.L346:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L347:
	.byte	5,2,7,0,5,2
	.word	.L62
	.byte	3,135,1,1,5,9,7,9
	.half	.L27-.L62
	.byte	3,1,1,5,2,9
	.half	.L348-.L27
	.byte	1,5,1,9
	.half	.L28-.L348
	.byte	3,1,1,7,9
	.half	.L155-.L28
	.byte	0,1,1
.L345:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_ranges'
.L154:
	.word	-1,.L62,0,.L155-.L62,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_info'
.L156:
	.word	326
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L159,.L158
	.byte	2
	.word	.L67
	.byte	3
	.byte	'IfxStdIf_Timer_nopAckTriggerIrq',0,1,139,1,16
	.word	.L179
	.byte	1,1
	.word	.L64,.L206,.L63
	.byte	4
	.byte	'stdIf',0,1,139,1,73
	.word	.L167,.L207
	.byte	5
	.word	.L64,.L206
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_line'
.L158:
	.word	.L350-.L349
.L349:
	.half	3
	.word	.L352-.L351
.L351:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Timer.c',0,0,0,0,0
.L352:
	.byte	5,2,7,0,5,2
	.word	.L64
	.byte	3,140,1,1,5,9,7,9
	.half	.L29-.L64
	.byte	3,1,1,5,2,9
	.half	.L353-.L29
	.byte	1,5,1,9
	.half	.L30-.L353
	.byte	3,1,1,7,9
	.half	.L160-.L30
	.byte	0,1,1
.L350:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_ranges'
.L159:
	.word	-1,.L64,0,.L160-.L64,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_loc'
.L31:
	.word	-1,.L32,0,.L161-.L32
	.half	2
	.byte	138,0
	.word	0,0
.L163:
	.word	-1,.L32,0,.L161-.L32
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_loc'
.L65:
	.word	-1,.L66,0,.L164-.L66
	.half	2
	.byte	138,0
	.word	0,0
.L168:
	.word	-1,.L66,0,.L164-.L66
	.half	1
	.byte	101
	.word	0,0
.L166:
	.word	-1,.L66,0,.L164-.L66
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_loc'
.L61:
	.word	-1,.L62,0,.L204-.L62
	.half	2
	.byte	138,0
	.word	0,0
.L205:
	.word	-1,.L62,0,.L204-.L62
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_loc'
.L63:
	.word	-1,.L64,0,.L206-.L64
	.half	2
	.byte	138,0
	.word	0,0
.L207:
	.word	-1,.L64,0,.L206-.L64
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_loc'
.L45:
	.word	-1,.L46,0,.L185-.L46
	.half	2
	.byte	138,0
	.word	0,0
.L186:
	.word	-1,.L46,0,.L185-.L46
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_loc'
.L47:
	.word	-1,.L48,0,.L187-.L48
	.half	2
	.byte	138,0
	.word	0,0
.L188:
	.word	-1,.L48,0,.L187-.L48
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_loc'
.L33:
	.word	-1,.L34,0,.L170-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L171:
	.word	-1,.L34,0,.L170-.L34
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_loc'
.L49:
	.word	-1,.L50,0,.L189-.L50
	.half	2
	.byte	138,0
	.word	0,0
.L190:
	.word	-1,.L50,0,.L189-.L50
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L173-.L36
	.half	2
	.byte	138,0
	.word	0,0
.L174:
	.word	-1,.L36,0,.L173-.L36
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_loc'
.L37:
	.word	-1,.L38,0,.L175-.L38
	.half	2
	.byte	138,0
	.word	0,0
.L176:
	.word	-1,.L38,0,.L175-.L38
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_loc'
.L39:
	.word	-1,.L40,0,.L177-.L40
	.half	2
	.byte	138,0
	.word	0,0
.L178:
	.word	-1,.L40,0,.L177-.L40
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_loc'
.L51:
	.word	-1,.L52,0,.L191-.L52
	.half	2
	.byte	138,0
	.word	0,0
.L192:
	.word	-1,.L52,0,.L191-.L52
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_loc'
.L41:
	.word	-1,.L42,0,.L180-.L42
	.half	2
	.byte	138,0
	.word	0,0
.L182:
	.word	-1,.L42,0,.L180-.L42
	.half	1
	.byte	84
	.word	0,0
.L181:
	.word	-1,.L42,0,.L180-.L42
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_loc'
.L53:
	.word	-1,.L54,0,.L193-.L54
	.half	2
	.byte	138,0
	.word	0,0
.L195:
	.word	-1,.L54,0,.L193-.L54
	.half	1
	.byte	84
	.word	0,0
.L194:
	.word	-1,.L54,0,.L193-.L54
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_loc'
.L55:
	.word	-1,.L56,0,.L196-.L56
	.half	2
	.byte	138,0
	.word	0,0
.L198:
	.word	-1,.L56,0,.L196-.L56
	.half	1
	.byte	84
	.word	0,0
.L197:
	.word	-1,.L56,0,.L196-.L56
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_loc'
.L57:
	.word	-1,.L58,0,.L199-.L58
	.half	2
	.byte	138,0
	.word	0,0
.L200:
	.word	-1,.L58,0,.L199-.L58
	.half	1
	.byte	100
	.word	0,0
.L201:
	.word	-1,.L58,0,.L199-.L58
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_loc'
.L59:
	.word	-1,.L60,0,.L202-.L60
	.half	2
	.byte	138,0
	.word	0,0
.L203:
	.word	-1,.L60,0,.L202-.L60
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_loc'
.L43:
	.word	-1,.L44,0,.L183-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L184:
	.word	-1,.L44,0,.L183-.L44
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L354:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_initConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L32,.L161-.L32
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopGetFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L34,.L170-.L34
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopGetPeriod')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L36,.L173-.L36
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopGetResolution')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L38,.L175-.L38
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopGetTrigger')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L40,.L177-.L40
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopSetFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L42,.L180-.L42
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopUpdateInputFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L44,.L183-.L44
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopApplyUpdate')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L46,.L185-.L46
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopDisableUpdate')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L48,.L187-.L48
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopGetInputFrequency')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L50,.L189-.L50
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopRun')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L52,.L191-.L52
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopSetPeriod')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L54,.L193-.L54
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopSetSingleMode')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L56,.L196-.L56
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopSetTrigger')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L58,.L199-.L58
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopStop')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L60,.L202-.L60
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopAckTimerIrq')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L62,.L204-.L62
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_nopAckTriggerIrq')
	.sect	'.debug_frame'
	.word	24
	.word	.L354,.L64,.L206-.L64
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Timer_initStdIf')
	.sect	'.debug_frame'
	.word	20
	.word	.L354,.L66,.L164-.L66
	.byte	8,18,8,19,8,22,8,23
	; Module end
