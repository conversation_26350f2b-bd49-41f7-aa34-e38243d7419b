	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20200a --dep-file=IfxScuWdt.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c'

	
$TC16X
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_changeCpuWatchdogPassword',code,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.text.IfxScuWdt.IfxScuWdt_changeCpuWatchdogPassword'
	.align	2
	
	.global	IfxScuWdt_changeCpuWatchdogPassword
; Function IfxScuWdt_changeCpuWatchdogPassword
.L112:
IfxScuWdt_changeCpuWatchdogPassword:	.type	func
	mfcr	d15,#65052
.L420:
	and	d15,#7
.L421:
	j	.L2
.L2:
	mul	d15,d15,#12
	mov.a	a15,d15
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24832
.L422:
	ld.w	d15,[a15]
.L423:
	jz.t	d15:1,.L3
.L682:
	or	d15,#1
.L683:
	insert	d15,d15,#0,#1,#1
.L684:
	insert	d15,d15,d4,#2,#14
.L685:
	st.w	[a15],d15
.L3:
	or	d15,#1
.L686:
	or	d15,#2
.L687:
	insert	d15,d15,d5,#2,#14
.L688:
	st.w	[a15],d15
.L689:
	j	.L4
.L5:
.L4:
	ld.w	d15,[a15]
.L690:
	jz.t	d15:0,.L5
.L691:
	ret
.L322:
	
__IfxScuWdt_changeCpuWatchdogPassword_function_end:
	.size	IfxScuWdt_changeCpuWatchdogPassword,__IfxScuWdt_changeCpuWatchdogPassword_function_end-IfxScuWdt_changeCpuWatchdogPassword
.L196:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_changeCpuWatchdogReload',code,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.text.IfxScuWdt.IfxScuWdt_changeCpuWatchdogReload'
	.align	2
	
	.global	IfxScuWdt_changeCpuWatchdogReload
; Function IfxScuWdt_changeCpuWatchdogReload
.L114:
IfxScuWdt_changeCpuWatchdogReload:	.type	func
	mfcr	d15,#65052
.L424:
	and	d15,#7
.L425:
	j	.L6
.L6:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L696:
	mul	d15,d15,#12
	addsc.a	a15,a15,d15,#0
.L426:
	ld.w	d15,[a15]
.L427:
	jz.t	d15:1,.L7
.L697:
	or	d15,#1
.L698:
	insert	d15,d15,#0,#1,#1
.L699:
	insert	d15,d15,d4,#2,#14
.L700:
	st.w	[a15],d15
.L7:
	or	d15,#1
.L701:
	or	d15,#2
.L702:
	insert	d15,d15,d5,#16,#16
.L703:
	st.w	[a15],d15
.L704:
	j	.L8
.L9:
.L8:
	ld.w	d15,[a15]
.L705:
	jz.t	d15:0,.L9
.L706:
	ret
.L328:
	
__IfxScuWdt_changeCpuWatchdogReload_function_end:
	.size	IfxScuWdt_changeCpuWatchdogReload,__IfxScuWdt_changeCpuWatchdogReload_function_end-IfxScuWdt_changeCpuWatchdogReload
.L201:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_changeSafetyWatchdogPassword',code,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.text.IfxScuWdt.IfxScuWdt_changeSafetyWatchdogPassword'
	.align	2
	
	.global	IfxScuWdt_changeSafetyWatchdogPassword
; Function IfxScuWdt_changeSafetyWatchdogPassword
.L116:
IfxScuWdt_changeSafetyWatchdogPassword:	.type	func
	movh.a	a15,#61443
.L428:
	lea	a15,[a15]@los(0xf00360f0)
.L711:
	ld.w	d15,[a15]
.L429:
	jz.t	d15:1,.L10
.L712:
	or	d15,#1
.L713:
	insert	d15,d15,#0,#1,#1
.L714:
	insert	d15,d15,d4,#2,#14
.L715:
	st.w	[a15],d15
.L10:
	or	d15,#1
.L716:
	or	d15,#2
.L717:
	insert	d15,d15,d5,#2,#14
.L718:
	st.w	[a15],d15
.L719:
	j	.L11
.L12:
.L11:
	ld.w	d15,[a15]
.L720:
	jz.t	d15:0,.L12
.L721:
	ret
.L336:
	
__IfxScuWdt_changeSafetyWatchdogPassword_function_end:
	.size	IfxScuWdt_changeSafetyWatchdogPassword,__IfxScuWdt_changeSafetyWatchdogPassword_function_end-IfxScuWdt_changeSafetyWatchdogPassword
.L206:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_changeSafetyWatchdogReload',code,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.text.IfxScuWdt.IfxScuWdt_changeSafetyWatchdogReload'
	.align	2
	
	.global	IfxScuWdt_changeSafetyWatchdogReload
; Function IfxScuWdt_changeSafetyWatchdogReload
.L118:
IfxScuWdt_changeSafetyWatchdogReload:	.type	func
	movh.a	a15,#61443
.L430:
	lea	a15,[a15]@los(0xf00360f0)
.L726:
	ld.w	d15,[a15]
.L431:
	jz.t	d15:1,.L13
.L727:
	or	d15,#1
.L728:
	insert	d15,d15,#0,#1,#1
.L729:
	insert	d15,d15,d4,#2,#14
.L730:
	st.w	[a15],d15
.L13:
	or	d15,#1
.L731:
	or	d15,#2
.L732:
	insert	d15,d15,d5,#16,#16
.L733:
	st.w	[a15],d15
.L734:
	j	.L14
.L15:
.L14:
	ld.w	d15,[a15]
.L735:
	jz.t	d15:0,.L15
.L736:
	ret
.L341:
	
__IfxScuWdt_changeSafetyWatchdogReload_function_end:
	.size	IfxScuWdt_changeSafetyWatchdogReload,__IfxScuWdt_changeSafetyWatchdogReload_function_end-IfxScuWdt_changeSafetyWatchdogReload
.L211:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_clearCpuEndinit',code,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.text.IfxScuWdt.IfxScuWdt_clearCpuEndinit'
	.align	2
	
	.global	IfxScuWdt_clearCpuEndinit
; Function IfxScuWdt_clearCpuEndinit
.L120:
IfxScuWdt_clearCpuEndinit:	.type	func
	mfcr	d15,#65052
.L433:
	and	d15,#7
.L434:
	j	.L16
.L16:
	mul	d15,d15,#12
	mov.a	a15,d15
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24832
.L290:
	ld.w	d15,[a15]
.L618:
	jz.t	d15:1,.L17
.L619:
	sha	d15,d4,#2
	or	d15,#1
.L620:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L621:
	sha	d0,d0,#16
.L622:
	or	d15,d0
.L623:
	st.w	[a15],d15
.L17:
	sha	d4,#2
.L432:
	or	d15,d4,#2
.L624:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L625:
	sha	d0,d0,#16
.L626:
	or	d15,d0
.L627:
	st.w	[a15],d15
.L628:
	j	.L18
.L19:
.L18:
	ld.w	d15,[a15]
	and	d15,#1
.L629:
	jeq	d15,#1,.L19
.L291:
	ret
.L282:
	
__IfxScuWdt_clearCpuEndinit_function_end:
	.size	IfxScuWdt_clearCpuEndinit,__IfxScuWdt_clearCpuEndinit_function_end-IfxScuWdt_clearCpuEndinit
.L176:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_clearSafetyEndinit',code,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.text.IfxScuWdt.IfxScuWdt_clearSafetyEndinit'
	.align	2
	
	.global	IfxScuWdt_clearSafetyEndinit
; Function IfxScuWdt_clearSafetyEndinit
.L122:
IfxScuWdt_clearSafetyEndinit:	.type	func
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L634:
	jz.t	d15:1,.L20
.L635:
	sha	d15,d4,#2
	or	d15,#1
.L636:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L637:
	sha	d0,d0,#16
.L638:
	or	d15,d0
.L639:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L20:
	sha	d4,#2
.L435:
	or	d15,d4,#2
.L640:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L641:
	sha	d0,d0,#16
.L642:
	or	d15,d0
.L643:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L644:
	j	.L21
.L22:
.L21:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
	and	d15,#1
.L645:
	jeq	d15,#1,.L22
.L300:
	ret
.L297:
	
__IfxScuWdt_clearSafetyEndinit_function_end:
	.size	IfxScuWdt_clearSafetyEndinit,__IfxScuWdt_clearSafetyEndinit_function_end-IfxScuWdt_clearSafetyEndinit
.L181:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_disableCpuWatchdog',code,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_disableCpuWatchdog'
	.align	2
	
	.global	IfxScuWdt_disableCpuWatchdog
; Function IfxScuWdt_disableCpuWatchdog
.L124:
IfxScuWdt_disableCpuWatchdog:	.type	func
	mfcr	d15,#65052
.L437:
	and	d15,#7
.L438:
	j	.L23
.L23:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L741:
	mul	d15,d15,#12
	addsc.a	a15,a15,d15,#0
.L351:
	ld.w	d15,[a15]
.L742:
	jz.t	d15:1,.L24
.L743:
	sha	d15,d4,#2
	or	d15,#1
.L744:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L745:
	sha	d0,d0,#16
.L746:
	or	d15,d0
.L747:
	st.w	[a15],d15
.L24:
	sha	d15,d4,#2
	or	d15,#2
.L748:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L749:
	sha	d0,d0,#16
.L750:
	or	d15,d0
.L751:
	st.w	[a15],d15
.L752:
	j	.L25
.L26:
.L25:
	ld.w	d15,[a15]
	and	d15,#1
.L753:
	jeq	d15,#1,.L26
.L352:
	ld.bu	d15,[a15]4
.L754:
	or	d15,#8
	st.b	[a15]4,d15
.L355:
	ld.w	d15,[a15]
.L755:
	jz.t	d15:1,.L27
.L756:
	sha	d15,d4,#2
	or	d15,#1
.L757:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L758:
	sha	d0,d0,#16
.L759:
	or	d15,d0
.L760:
	st.w	[a15],d15
.L27:
	sha	d4,#2
.L436:
	or	d15,d4,#3
.L761:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L762:
	sha	d0,d0,#16
.L763:
	or	d15,d0
.L764:
	st.w	[a15],d15
.L765:
	j	.L28
.L29:
.L28:
	ld.w	d15,[a15]
.L766:
	jz.t	d15:0,.L29
.L356:
	ret
.L346:
	
__IfxScuWdt_disableCpuWatchdog_function_end:
	.size	IfxScuWdt_disableCpuWatchdog,__IfxScuWdt_disableCpuWatchdog_function_end-IfxScuWdt_disableCpuWatchdog
.L216:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_disableSafetyWatchdog',code,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_disableSafetyWatchdog'
	.align	2
	
	.global	IfxScuWdt_disableSafetyWatchdog
; Function IfxScuWdt_disableSafetyWatchdog
.L126:
IfxScuWdt_disableSafetyWatchdog:	.type	func
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L771:
	jz.t	d15:1,.L30
.L772:
	sha	d15,d4,#2
	or	d15,#1
.L773:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L774:
	sha	d0,d0,#16
.L775:
	or	d15,d0
.L776:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L30:
	sha	d15,d4,#2
	or	d15,#2
.L777:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L778:
	sha	d0,d0,#16
.L779:
	or	d15,d0
.L780:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L781:
	j	.L31
.L32:
.L31:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
	and	d15,#1
.L782:
	jeq	d15,#1,.L32
.L361:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf00360f4)
.L783:
	or	d15,#8
	movh.a	a15,#61443
	st.b	[a15]@los(0xf00360f4),d15
.L363:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L784:
	jz.t	d15:1,.L33
.L785:
	sha	d15,d4,#2
	or	d15,#1
.L786:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L787:
	sha	d0,d0,#16
.L788:
	or	d15,d0
.L789:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L33:
	sha	d4,#2
.L439:
	or	d15,d4,#3
.L790:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L791:
	sha	d0,d0,#16
.L792:
	or	d15,d0
.L793:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L794:
	j	.L34
.L35:
.L34:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L795:
	jz.t	d15:0,.L35
.L364:
	ret
.L359:
	
__IfxScuWdt_disableSafetyWatchdog_function_end:
	.size	IfxScuWdt_disableSafetyWatchdog,__IfxScuWdt_disableSafetyWatchdog_function_end-IfxScuWdt_disableSafetyWatchdog
.L221:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_enableCpuWatchdog',code,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_enableCpuWatchdog'
	.align	2
	
	.global	IfxScuWdt_enableCpuWatchdog
; Function IfxScuWdt_enableCpuWatchdog
.L128:
IfxScuWdt_enableCpuWatchdog:	.type	func
	mfcr	d15,#65052
.L441:
	and	d15,#7
.L442:
	j	.L36
.L36:
	movh.a	a15,#61443
	lea	a15,[a15]@los(0xf0036100)
.L800:
	mul	d15,d15,#12
	addsc.a	a15,a15,d15,#0
.L371:
	ld.w	d15,[a15]
.L801:
	jz.t	d15:1,.L37
.L802:
	sha	d15,d4,#2
	or	d15,#1
.L803:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L804:
	sha	d0,d0,#16
.L805:
	or	d15,d0
.L806:
	st.w	[a15],d15
.L37:
	sha	d15,d4,#2
	or	d15,#2
.L807:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L808:
	sha	d0,d0,#16
.L809:
	or	d15,d0
.L810:
	st.w	[a15],d15
.L811:
	j	.L38
.L39:
.L38:
	ld.w	d15,[a15]
	and	d15,#1
.L812:
	jeq	d15,#1,.L39
.L372:
	ld.bu	d15,[a15]4
.L813:
	insert	d15,d15,#0,#3,#1
	st.b	[a15]4,d15
.L375:
	ld.w	d15,[a15]
.L814:
	jz.t	d15:1,.L40
.L815:
	sha	d15,d4,#2
	or	d15,#1
.L816:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L817:
	sha	d0,d0,#16
.L818:
	or	d15,d0
.L819:
	st.w	[a15],d15
.L40:
	sha	d4,#2
.L440:
	or	d15,d4,#3
.L820:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L821:
	sha	d0,d0,#16
.L822:
	or	d15,d0
.L823:
	st.w	[a15],d15
.L824:
	j	.L41
.L42:
.L41:
	ld.w	d15,[a15]
.L825:
	jz.t	d15:0,.L42
.L376:
	ret
.L366:
	
__IfxScuWdt_enableCpuWatchdog_function_end:
	.size	IfxScuWdt_enableCpuWatchdog,__IfxScuWdt_enableCpuWatchdog_function_end-IfxScuWdt_enableCpuWatchdog
.L226:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_enableSafetyWatchdog',code,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_enableSafetyWatchdog'
	.align	2
	
	.global	IfxScuWdt_enableSafetyWatchdog
; Function IfxScuWdt_enableSafetyWatchdog
.L130:
IfxScuWdt_enableSafetyWatchdog:	.type	func
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L830:
	jz.t	d15:1,.L43
.L831:
	sha	d15,d4,#2
	or	d15,#1
.L832:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L833:
	sha	d0,d0,#16
.L834:
	or	d15,d0
.L835:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L43:
	sha	d15,d4,#2
	or	d15,#2
.L836:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L837:
	sha	d0,d0,#16
.L838:
	or	d15,d0
.L839:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L840:
	j	.L44
.L45:
.L44:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
	and	d15,#1
.L841:
	jeq	d15,#1,.L45
.L381:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf00360f4)
.L842:
	insert	d15,d15,#0,#3,#1
	movh.a	a15,#61443
	st.b	[a15]@los(0xf00360f4),d15
.L383:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L843:
	jz.t	d15:1,.L46
.L844:
	sha	d15,d4,#2
	or	d15,#1
.L845:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L846:
	sha	d0,d0,#16
.L847:
	or	d15,d0
.L848:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L46:
	sha	d4,#2
.L443:
	or	d15,d4,#3
.L849:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L850:
	sha	d0,d0,#16
.L851:
	or	d15,d0
.L852:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L853:
	j	.L47
.L48:
.L47:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L854:
	jz.t	d15:0,.L48
.L384:
	ret
.L379:
	
__IfxScuWdt_enableSafetyWatchdog_function_end:
	.size	IfxScuWdt_enableSafetyWatchdog,__IfxScuWdt_enableSafetyWatchdog_function_end-IfxScuWdt_enableSafetyWatchdog
.L231:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_getCpuWatchdogPassword',code,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.text.IfxScuWdt.IfxScuWdt_getCpuWatchdogPassword'
	.align	2
	
	.global	IfxScuWdt_getCpuWatchdogPassword
; Function IfxScuWdt_getCpuWatchdogPassword
.L132:
IfxScuWdt_getCpuWatchdogPassword:	.type	func
	mfcr	d15,#65052
.L444:
	and	d15,#7
.L445:
	j	.L49
.L49:
	mul	d15,d15,#12
	mov.a	a15,d15
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24832
.L389:
	ld.w	d15,[a15]
	extr.u	d15,d15,#2,#14
.L446:
	xor	d2,d15,#63
.L447:
	j	.L50
.L50:
	j	.L51
.L51:
	ret
.L386:
	
__IfxScuWdt_getCpuWatchdogPassword_function_end:
	.size	IfxScuWdt_getCpuWatchdogPassword,__IfxScuWdt_getCpuWatchdogPassword_function_end-IfxScuWdt_getCpuWatchdogPassword
.L236:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_getCpuWatchdogEndInit',code,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.text.IfxScuWdt.IfxScuWdt_getCpuWatchdogEndInit'
	.align	2
	
	.global	IfxScuWdt_getCpuWatchdogEndInit
; Function IfxScuWdt_getCpuWatchdogEndInit
.L134:
IfxScuWdt_getCpuWatchdogEndInit:	.type	func
	mfcr	d15,#65052
.L448:
	and	d15,#7
.L449:
	j	.L52
.L52:
	mul	d15,d15,#12
	mov.a	a15,d15
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24832
.L398:
	ld.w	d15,[a15]
	and	d2,d15,#1
.L863:
	j	.L53
.L53:
	j	.L54
.L54:
	ret
.L395:
	
__IfxScuWdt_getCpuWatchdogEndInit_function_end:
	.size	IfxScuWdt_getCpuWatchdogEndInit,__IfxScuWdt_getCpuWatchdogEndInit_function_end-IfxScuWdt_getCpuWatchdogEndInit
.L241:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_getSafetyWatchdogPassword',code,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.text.IfxScuWdt.IfxScuWdt_getSafetyWatchdogPassword'
	.align	2
	
	.global	IfxScuWdt_getSafetyWatchdogPassword
; Function IfxScuWdt_getSafetyWatchdogPassword
.L136:
IfxScuWdt_getSafetyWatchdogPassword:	.type	func
	movh.a	a15,#61443
.L450:
	lea	a15,[a15]@los(0xf00360f0)
.L868:
	ld.w	d15,[a15]
	extr.u	d15,d15,#2,#14
.L451:
	xor	d2,d15,#63
.L452:
	j	.L55
.L55:
	j	.L56
.L56:
	ret
.L402:
	
__IfxScuWdt_getSafetyWatchdogPassword_function_end:
	.size	IfxScuWdt_getSafetyWatchdogPassword,__IfxScuWdt_getSafetyWatchdogPassword_function_end-IfxScuWdt_getSafetyWatchdogPassword
.L246:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_initConfig',code,cluster('IfxScuWdt_initConfig')
	.sect	'.text.IfxScuWdt.IfxScuWdt_initConfig'
	.align	2
	
	.global	IfxScuWdt_initConfig
; Function IfxScuWdt_initConfig
.L138:
IfxScuWdt_initConfig:	.type	func
	mov	d15,#60
.L492:
	st.h	[a4],d15
.L493:
	mov.u	d15,#65532
.L494:
	st.h	[a4]2,d15
.L495:
	mov	d15,#0
.L496:
	st.b	[a4]4,d15
.L497:
	mov	d15,#0
.L498:
	st.b	[a4]5,d15
.L499:
	mov	d15,#0
.L500:
	st.b	[a4]6,d15
.L501:
	mov	d15,#0
.L502:
	st.b	[a4]7,d15
.L503:
	mov	d15,#0
.L504:
	st.b	[a4]8,d15
.L505:
	mov	d15,#0
.L506:
	st.b	[a4]9,d15
.L507:
	mov	d15,#0
.L508:
	st.b	[a4]10,d15
.L509:
	ret
.L262:
	
__IfxScuWdt_initConfig_function_end:
	.size	IfxScuWdt_initConfig,__IfxScuWdt_initConfig_function_end-IfxScuWdt_initConfig
.L161:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_initCpuWatchdog',code,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_initCpuWatchdog'
	.align	2
	
	.global	IfxScuWdt_initCpuWatchdog
; Function IfxScuWdt_initCpuWatchdog
.L140:
IfxScuWdt_initCpuWatchdog:	.type	func
	ld.w	d15,[a4]
.L454:
	mov	d2,#0
.L456:
	jz.t	d15:1,.L57
.L514:
	or	d15,#1
.L515:
	insert	d0,d15,#0,#1,#1
.L455:
	extr.u	d1,d0,#2,#14
	xor	d1,d1,#63
	insert	d15,d0,d1,#2,#14
.L457:
	st.w	[a4],d15
.L57:
	insert	d15,d15,#0,#0,#1
.L516:
	or	d15,#2
.L517:
	ld.hu	d0,[a5]0
.L518:
	insert	d0,d15,d0,#2,#14
.L458:
	ld.hu	d1,[a5]2
.L519:
	insert	d0,d0,d1,#16,#16
.L520:
	st.w	[a4],d0
.L521:
	j	.L58
.L59:
.L58:
	ld.w	d15,[a4]
	and	d15,#1
.L522:
	jeq	d15,#1,.L59
.L523:
	ld.bu	d15,[a5]4
.L524:
	mov	d0,#0
.L459:
	jeq	d15,d0,.L60
.L525:
	mov	d0,#1
	jeq	d15,d0,.L61
.L526:
	mov	d0,#2
	jeq	d15,d0,.L62
	j	.L63
.L60:
	mov	d15,#0
.L527:
	insert	d2,d2,d15,#2,#1
.L528:
	mov	d15,#0
.L529:
	insert	d2,d2,d15,#5,#1
.L530:
	j	.L64
.L61:
	mov	d15,#1
.L531:
	insert	d2,d2,d15,#2,#1
.L532:
	mov	d15,#0
.L533:
	insert	d2,d2,d15,#5,#1
.L534:
	j	.L65
.L62:
	mov	d15,#0
.L535:
	insert	d2,d2,d15,#2,#1
.L536:
	mov	d15,#1
.L537:
	insert	d2,d2,d15,#5,#1
.L538:
	j	.L66
.L63:
.L66:
.L65:
.L64:
	ld.bu	d15,[a5]5
.L539:
	jeq	d15,#0,.L67
.L540:
	mov	d15,#1
.L541:
	j	.L68
.L67:
	mov	d15,#0
.L68:
	insert	d2,d2,d15,#3,#1
.L542:
	ld.bu	d15,[a5]6
.L543:
	jeq	d15,#0,.L69
.L544:
	mov	d15,#1
.L545:
	j	.L70
.L69:
	mov	d15,#0
.L70:
	insert	d2,d2,d15,#6,#1
.L546:
	ld.bu	d15,[a5]7
.L547:
	jeq	d15,#0,.L71
.L548:
	mov	d15,#1
.L549:
	j	.L72
.L71:
	mov	d15,#0
.L72:
	insert	d2,d2,d15,#7,#1
.L550:
	ld.bu	d15,[a5]8
.L551:
	jeq	d15,#0,.L73
.L552:
	mov	d15,#1
.L553:
	j	.L74
.L73:
	mov	d15,#0
.L74:
	insert	d2,d2,d15,#8,#1
.L554:
	ld.bu	d15,[a5]9
.L555:
	jeq	d15,#0,.L75
.L556:
	mov	d15,#1
.L557:
	j	.L76
.L75:
	mov	d15,#0
.L76:
	insert	d2,d2,d15,#9,#7
.L558:
	st.w	[a4]4,d2
.L559:
	ld.hu	d4,[a5]0
	call	IfxScuWdt_setCpuEndinit
.L453:
	ret
.L265:
	
__IfxScuWdt_initCpuWatchdog_function_end:
	.size	IfxScuWdt_initCpuWatchdog,__IfxScuWdt_initCpuWatchdog_function_end-IfxScuWdt_initCpuWatchdog
.L166:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_initSafetyWatchdog',code,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_initSafetyWatchdog'
	.align	2
	
	.global	IfxScuWdt_initSafetyWatchdog
; Function IfxScuWdt_initSafetyWatchdog
.L142:
IfxScuWdt_initSafetyWatchdog:	.type	func
	ld.w	d15,[a4]
.L461:
	mov	d2,#0
.L463:
	jz.t	d15:1,.L77
.L564:
	or	d15,#1
.L565:
	insert	d0,d15,#0,#1,#1
.L462:
	extr.u	d1,d0,#2,#14
	xor	d1,d1,#63
	insert	d15,d0,d1,#2,#14
.L464:
	st.w	[a4],d15
.L77:
	insert	d15,d15,#0,#0,#1
.L566:
	or	d15,#2
.L567:
	ld.hu	d0,[a5]0
.L568:
	insert	d0,d15,d0,#2,#14
.L465:
	ld.hu	d1,[a5]2
.L569:
	insert	d0,d0,d1,#16,#16
.L570:
	st.w	[a4],d0
.L571:
	j	.L78
.L79:
.L78:
	ld.w	d15,[a4]
	and	d15,#1
.L572:
	jeq	d15,#1,.L79
.L573:
	ld.bu	d15,[a5]4
.L574:
	mov	d0,#0
.L466:
	jeq	d15,d0,.L80
.L575:
	mov	d0,#1
	jeq	d15,d0,.L81
.L576:
	mov	d0,#2
	jeq	d15,d0,.L82
	j	.L83
.L80:
	mov	d15,#0
.L577:
	insert	d2,d2,d15,#2,#1
.L578:
	mov	d15,#0
.L579:
	insert	d2,d2,d15,#5,#1
.L580:
	j	.L84
.L81:
	mov	d15,#1
.L581:
	insert	d2,d2,d15,#2,#1
.L582:
	mov	d15,#0
.L583:
	insert	d2,d2,d15,#5,#1
.L584:
	j	.L85
.L82:
	mov	d15,#0
.L585:
	insert	d2,d2,d15,#2,#1
.L586:
	mov	d15,#1
.L587:
	insert	d2,d2,d15,#5,#1
.L588:
	j	.L86
.L83:
.L86:
.L85:
.L84:
	ld.bu	d15,[a5]5
.L589:
	jeq	d15,#0,.L87
.L590:
	mov	d15,#1
.L591:
	j	.L88
.L87:
	mov	d15,#0
.L88:
	insert	d2,d2,d15,#3,#1
.L592:
	ld.bu	d15,[a5]6
.L593:
	jeq	d15,#0,.L89
.L594:
	mov	d15,#1
.L595:
	j	.L90
.L89:
	mov	d15,#0
.L90:
	insert	d2,d2,d15,#6,#1
.L596:
	ld.bu	d15,[a5]7
.L597:
	jeq	d15,#0,.L91
.L598:
	mov	d15,#1
.L599:
	j	.L92
.L91:
	mov	d15,#0
.L92:
	insert	d2,d2,d15,#7,#1
.L600:
	ld.bu	d15,[a5]8
.L601:
	jeq	d15,#0,.L93
.L602:
	mov	d15,#1
.L603:
	j	.L94
.L93:
	mov	d15,#0
.L94:
	insert	d2,d2,d15,#8,#1
.L604:
	ld.bu	d15,[a5]9
.L605:
	jeq	d15,#0,.L95
.L606:
	mov	d15,#1
.L607:
	j	.L96
.L95:
	mov	d15,#0
.L96:
	insert	d2,d2,d15,#9,#7
.L608:
	ld.bu	d15,[a5]10
.L609:
	jeq	d15,#0,.L97
.L610:
	mov	d15,#0
.L611:
	j	.L98
.L97:
	mov	d15,#1
.L98:
	insert	d2,d2,d15,#0,#1
.L612:
	st.w	[a4]4,d2
.L613:
	ld.hu	d4,[a5]0
	call	IfxScuWdt_setSafetyEndinit
.L460:
	ret
.L274:
	
__IfxScuWdt_initSafetyWatchdog_function_end:
	.size	IfxScuWdt_initSafetyWatchdog,__IfxScuWdt_initSafetyWatchdog_function_end-IfxScuWdt_initSafetyWatchdog
.L171:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_serviceCpuWatchdog',code,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_serviceCpuWatchdog'
	.align	2
	
	.global	IfxScuWdt_serviceCpuWatchdog
; Function IfxScuWdt_serviceCpuWatchdog
.L144:
IfxScuWdt_serviceCpuWatchdog:	.type	func
	call	IfxScuWdt_setCpuEndinit
.L467:
	ret
.L407:
	
__IfxScuWdt_serviceCpuWatchdog_function_end:
	.size	IfxScuWdt_serviceCpuWatchdog,__IfxScuWdt_serviceCpuWatchdog_function_end-IfxScuWdt_serviceCpuWatchdog
.L251:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_serviceSafetyWatchdog',code,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.text.IfxScuWdt.IfxScuWdt_serviceSafetyWatchdog'
	.align	2
	
	.global	IfxScuWdt_serviceSafetyWatchdog
; Function IfxScuWdt_serviceSafetyWatchdog
.L146:
IfxScuWdt_serviceSafetyWatchdog:	.type	func
	call	IfxScuWdt_setSafetyEndinit
.L468:
	ret
.L409:
	
__IfxScuWdt_serviceSafetyWatchdog_function_end:
	.size	IfxScuWdt_serviceSafetyWatchdog,__IfxScuWdt_serviceSafetyWatchdog_function_end-IfxScuWdt_serviceSafetyWatchdog
.L256:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_setCpuEndinit',code,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.text.IfxScuWdt.IfxScuWdt_setCpuEndinit'
	.align	2
	
	.global	IfxScuWdt_setCpuEndinit
; Function IfxScuWdt_setCpuEndinit
.L148:
IfxScuWdt_setCpuEndinit:	.type	func
	mfcr	d15,#65052
.L470:
	and	d15,#7
.L471:
	j	.L99
.L99:
	mul	d15,d15,#12
	mov.a	a15,d15
	movh.a	a3,#61443
	add.a	a3,a15
	lea	a15,[a3]24832
.L308:
	ld.w	d15,[a15]
.L650:
	jz.t	d15:1,.L100
.L651:
	sha	d15,d4,#2
	or	d15,#1
.L652:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L653:
	sha	d0,d0,#16
.L654:
	or	d15,d0
.L655:
	st.w	[a15],d15
.L100:
	sha	d4,#2
.L469:
	or	d15,d4,#3
.L656:
	ld.w	d0,[a15]
	extr.u	d0,d0,#16,#16
.L657:
	sha	d0,d0,#16
.L658:
	or	d15,d0
.L659:
	st.w	[a15],d15
.L660:
	j	.L101
.L102:
.L101:
	ld.w	d15,[a15]
.L661:
	jz.t	d15:0,.L102
.L309:
	ret
.L304:
	
__IfxScuWdt_setCpuEndinit_function_end:
	.size	IfxScuWdt_setCpuEndinit,__IfxScuWdt_setCpuEndinit_function_end-IfxScuWdt_setCpuEndinit
.L186:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_setSafetyEndinit',code,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.text.IfxScuWdt.IfxScuWdt_setSafetyEndinit'
	.align	2
	
	.global	IfxScuWdt_setSafetyEndinit
; Function IfxScuWdt_setSafetyEndinit
.L150:
IfxScuWdt_setSafetyEndinit:	.type	func
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L666:
	jz.t	d15:1,.L103
.L667:
	sha	d15,d4,#2
	or	d15,#1
.L668:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L669:
	sha	d0,d0,#16
.L670:
	or	d15,d0
.L671:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L103:
	sha	d4,#2
.L472:
	or	d15,d4,#3
.L672:
	movh.a	a15,#61443
	ld.w	d0,[a15]@los(0xf00360f0)
	extr.u	d0,d0,#16,#16
.L673:
	sha	d0,d0,#16
.L674:
	or	d15,d0
.L675:
	movh.a	a15,#61443
	st.w	[a15]@los(0xf00360f0),d15
.L676:
	j	.L104
.L105:
.L104:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
.L677:
	jz.t	d15:0,.L105
.L318:
	ret
.L315:
	
__IfxScuWdt_setSafetyEndinit_function_end:
	.size	IfxScuWdt_setSafetyEndinit,__IfxScuWdt_setSafetyEndinit_function_end-IfxScuWdt_setSafetyEndinit
.L191:
	; End of function
	
	.sdecl	'.text.IfxScuWdt.IfxScuWdt_enableWatchdogWithDebugger',code,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.text.IfxScuWdt.IfxScuWdt_enableWatchdogWithDebugger'
	.align	2
	
	.global	IfxScuWdt_enableWatchdogWithDebugger
; Function IfxScuWdt_enableWatchdogWithDebugger
.L152:
IfxScuWdt_enableWatchdogWithDebugger:	.type	func
	mov	d2,#0
.L473:
	lea	a15,0xf0000478
.L474:
	lea	a2,0xf0000480
.L475:
	lea	a4,0xf000047c
.L476:
	ld.w	d15,[a2]
.L477:
	and	d15,#1
.L479:
	jne	d15,#0,.L106
.L881:
	mov	d15,#161
.L478:
	st.w	[a15],d15
.L882:
	mov	d15,#94
.L883:
	st.w	[a15],d15
.L884:
	mov	d15,#161
.L885:
	st.w	[a15],d15
.L886:
	mov	d15,#94
.L887:
	st.w	[a15],d15
.L888:
	ld.w	d15,[a2]
.L480:
	and	d15,#1
.L106:
	jeq	d15,#0,.L107
.L889:
	mov	d15,#12288
.L481:
	st.w	[a4],d15
.L890:
	j	.L108
.L107:
	mov	d2,#1
.L108:
	ld.w	d15,[a2]
.L482:
	and	d15,#128
.L483:
	jne	d15,#0,.L109
.L891:
	mov	d2,#1
.L109:
	j	.L110
.L110:
	ret
.L411:
	
__IfxScuWdt_enableWatchdogWithDebugger_function_end:
	.size	IfxScuWdt_enableWatchdogWithDebugger,__IfxScuWdt_enableWatchdogWithDebugger_function_end-IfxScuWdt_enableWatchdogWithDebugger
.L261:
	; End of function
	
	.calls	'IfxScuWdt_initCpuWatchdog','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuWdt_initSafetyWatchdog','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuWdt_serviceCpuWatchdog','IfxScuWdt_setCpuEndinit'
	.calls	'IfxScuWdt_serviceSafetyWatchdog','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxScuWdt_changeCpuWatchdogPassword','',0
	.calls	'IfxScuWdt_changeCpuWatchdogReload','',0
	.calls	'IfxScuWdt_changeSafetyWatchdogPassword','',0
	.calls	'IfxScuWdt_changeSafetyWatchdogReload','',0
	.calls	'IfxScuWdt_clearCpuEndinit','',0
	.calls	'IfxScuWdt_clearSafetyEndinit','',0
	.calls	'IfxScuWdt_disableCpuWatchdog','',0
	.calls	'IfxScuWdt_disableSafetyWatchdog','',0
	.calls	'IfxScuWdt_enableCpuWatchdog','',0
	.calls	'IfxScuWdt_enableSafetyWatchdog','',0
	.calls	'IfxScuWdt_getCpuWatchdogPassword','',0
	.calls	'IfxScuWdt_getCpuWatchdogEndInit','',0
	.calls	'IfxScuWdt_getSafetyWatchdogPassword','',0
	.calls	'IfxScuWdt_initConfig','',0
	.calls	'IfxScuWdt_initCpuWatchdog','',0
	.calls	'IfxScuWdt_initSafetyWatchdog','',0
	.calls	'IfxScuWdt_serviceCpuWatchdog','',0
	.calls	'IfxScuWdt_serviceSafetyWatchdog','',0
	.calls	'IfxScuWdt_setCpuEndinit','',0
	.calls	'IfxScuWdt_setSafetyEndinit','',0
	.calls	'IfxScuWdt_enableWatchdogWithDebugger','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L154:
	.word	77984
	.half	3
	.word	.L155
	.byte	4
.L153:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L156
	.byte	2,1,1,3
	.word	233
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	236
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	281
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	293
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	405
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	379
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	411
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	411
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	379
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0
.L270:
	.byte	12,4,247,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	536
	.byte	4,2,35,0,0
.L394:
	.byte	7
	.byte	'unsigned char',0,1,8
.L283:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0
.L272:
	.byte	12,4,255,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	711
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	955
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	632
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	915
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1146
	.byte	4,2,35,8,0,14
	.word	1186
.L266:
	.byte	3
	.word	1249
.L289:
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1
.L292:
	.byte	5
	.byte	'watchdog',0,3,181,3,65
	.word	1254
.L294:
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	689
.L296:
	.byte	6,0
.L299:
	.byte	4
	.byte	'IfxScuWdt_clearSafetyEndinitInline',0,3,3,204,3,17,1,1
.L301:
	.byte	5
	.byte	'password',0,3,204,3,59
	.word	689
.L303:
	.byte	6,0
.L307:
	.byte	4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1
.L310:
	.byte	5
	.byte	'watchdog',0,3,140,4,63
	.word	1254
.L312:
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	689
.L314:
	.byte	6,0
.L317:
	.byte	4
	.byte	'IfxScuWdt_setSafetyEndinitInline',0,3,3,163,4,17,1,1
.L319:
	.byte	5
	.byte	'password',0,3,163,4,57
	.word	689
.L321:
	.byte	6,0
.L388:
	.byte	8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	689
	.byte	1,1
.L390:
	.byte	5
	.byte	'watchdog',0,3,227,3,74
	.word	1254
.L392:
	.byte	6,0
.L397:
	.byte	8
	.byte	'IfxScuWdt_getCpuWatchdogEndInitInline',0,3,3,241,3,20
	.word	672
	.byte	1,1
.L399:
	.byte	5
	.byte	'watchdog',0,3,241,3,74
	.word	1254
.L401:
	.byte	6,0
.L403:
	.byte	8
	.byte	'IfxScuWdt_getSafetyWatchdogPasswordInline',0,3,3,253,3,19
	.word	689
	.byte	1,1
.L404:
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1734
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2050
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2621
	.byte	4,2,35,0,0,15,4
	.word	672
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2749
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2964
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3179
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	672
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3396
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3616
	.byte	4,2,35,0,0,15,24
	.word	672
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3939
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	672
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4243
	.byte	4,2,35,0,0,15,8
	.word	672
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4568
	.byte	4,2,35,0,0,15,12
	.word	672
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4908
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5274
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5560
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5707
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	497
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5876
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6048
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	689
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6223
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6397
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6571
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6747
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6903
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7236
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7584
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7708
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7792
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7972
	.byte	4,2,35,0,0,15,76
	.word	672
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8225
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8312
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2010
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2581
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2700
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2740
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2924
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3139
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3356
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3576
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2740
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3890
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3930
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4203
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4519
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4559
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4859
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4899
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5234
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5520
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4559
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5667
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5836
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6008
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6183
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6357
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6531
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6707
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6863
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7196
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7544
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4559
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7668
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7917
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8176
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8216
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8272
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8839
	.byte	4,3,35,252,1,0,14
	.word	8879
	.byte	3
	.word	9482
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9487
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	672
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9492
	.byte	6,0,17,8,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0
.L285:
	.byte	8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	9673
	.byte	1,1
.L286:
	.byte	6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	672
	.byte	1,1,6,0
.L331:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	9828
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	689
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	672
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	689
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	9828
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	9828
	.byte	19,6,0,0,14
	.word	513
	.byte	20
	.byte	'__mfcr',0
	.word	10059
	.byte	1,1,1,1,21
	.word	513
	.byte	0,22
	.word	241
	.byte	23
	.word	267
	.byte	6,0,22
	.word	302
	.byte	23
	.word	334
	.byte	6,0,22
	.word	347
	.byte	6,0,22
	.word	416
	.byte	23
	.word	435
	.byte	6,0,22
	.word	451
	.byte	23
	.word	466
	.byte	23
	.word	480
	.byte	6,0,17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,24,3,89,9,12,13
	.byte	'password',0
	.word	689
	.byte	2,2,35,0,13
	.byte	'reload',0
	.word	689
	.byte	2,2,35,2,13
	.byte	'inputFrequency',0
	.word	10146
	.byte	1,2,35,4,13
	.byte	'disableWatchdog',0
	.word	672
	.byte	1,2,35,5,13
	.byte	'enableSmuRestriction',0
	.word	672
	.byte	1,2,35,6,13
	.byte	'enableAutomaticPasswordChange',0
	.word	672
	.byte	1,2,35,7,13
	.byte	'enableTimerCheck',0
	.word	672
	.byte	1,2,35,8,13
	.byte	'enableTimerCheckTolerance',0
	.word	672
	.byte	1,2,35,9,13
	.byte	'clrInternalResetFlag',0
	.word	672
	.byte	1,2,35,10,0
.L263:
	.byte	3
	.word	10241
	.byte	25
	.word	10241
.L268:
	.byte	3
	.word	10495
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	520
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	520
	.byte	16,0,2,35,0,0
.L278:
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10505
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0
.L280:
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10639
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	689
	.byte	16,0,2,35,2,0,12,4,159,15,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10899
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	10599
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	10859
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	11088
	.byte	4,2,35,8,0,14
	.word	11128
.L275:
	.byte	3
	.word	11189
	.byte	22
	.word	1259
	.byte	23
	.word	1299
	.byte	23
	.word	1317
	.byte	6,0,22
	.word	1337
	.byte	23
	.word	1380
	.byte	6,0,22
	.word	1400
	.byte	23
	.word	1438
	.byte	23
	.word	1456
	.byte	6,0,22
	.word	1476
	.byte	23
	.word	1517
	.byte	6,0,10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,10,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0
.L287:
	.byte	12,10,223,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11257
	.byte	4,2,35,0,0,22
	.word	1537
	.byte	23
	.word	1588
	.byte	6,0,22
	.word	1608
	.byte	23
	.word	1658
	.byte	6,0,22
	.word	1678
	.byte	6,0,14
	.word	9828
.L416:
	.byte	3
	.word	11397
	.byte	22
	.word	9595
	.byte	23
	.word	9623
	.byte	23
	.word	9637
	.byte	23
	.word	9655
	.byte	6,0,22
	.word	9752
	.byte	6,0,22
	.word	9786
	.byte	6,0,22
	.word	9849
	.byte	23
	.word	9890
	.byte	6,0,22
	.word	9909
	.byte	23
	.word	9964
	.byte	6,0,22
	.word	9983
	.byte	23
	.word	10023
	.byte	23
	.word	10040
	.byte	19,6,0,0,7
	.byte	'short int',0,2,5,26
	.byte	'__wchar_t',0,11,1,1
	.word	11486
	.byte	26
	.byte	'__size_t',0,11,1,1
	.word	497
	.byte	26
	.byte	'__ptrdiff_t',0,11,1,1
	.word	513
	.byte	27,1,3
	.word	11554
	.byte	26
	.byte	'__codeptr',0,11,1,1
	.word	11556
	.byte	17,9,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,26
	.byte	'IfxScu_CCUCON0_CLKSEL',0,9,240,10,3
	.word	11579
	.byte	26
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	10146
	.byte	26
	.byte	'boolean',0,12,101,29
	.word	672
	.byte	26
	.byte	'uint8',0,12,105,29
	.word	672
	.byte	26
	.byte	'uint16',0,12,109,29
	.word	689
	.byte	26
	.byte	'uint32',0,12,113,29
	.word	9828
	.byte	26
	.byte	'uint64',0,12,118,29
	.word	379
	.byte	26
	.byte	'sint16',0,12,126,29
	.word	11486
	.byte	7
	.byte	'long int',0,4,5,26
	.byte	'sint32',0,12,131,1,29
	.word	11793
	.byte	7
	.byte	'long long int',0,8,5,26
	.byte	'sint64',0,12,138,1,29
	.word	11821
	.byte	26
	.byte	'float32',0,12,167,1,29
	.word	293
	.byte	26
	.byte	'pvoid',0,13,57,28
	.word	411
	.byte	26
	.byte	'Ifx_TickTime',0,13,79,28
	.word	11821
	.byte	17,13,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,26
	.byte	'Ifx_RxSel',0,13,140,1,3
	.word	11906
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	12044
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	12601
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	12678
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	12814
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	672
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	13094
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	13332
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	13460
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	672
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	13703
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	13938
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	14066
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	14166
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	672
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	14266
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	497
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	14474
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	14639
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	14822
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	497
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	672
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	14976
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	15340
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	672
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	15551
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	15803
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	15921
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	16032
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	16195
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	16358
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	16516
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	672
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	10,0,2,35,2,0,26
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	16681
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	672
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	672
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	689
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	17010
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	17231
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	17394
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	17666
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	17819
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	17975
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	18137
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	18280
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	18445
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	18590
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	18771
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	18945
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	19105
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	19249
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	19523
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	19662
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	672
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	689
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	672
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	672
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	19825
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	689
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	672
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	689
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	20043
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	20206
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	20542
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	672
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	20649
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	21101
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	21200
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	21350
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	497
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	21499
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	21660
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	689
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	21790
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	21922
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	689
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	22037
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	22148
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	672
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	22306
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	22718
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	689
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	6,0,2,35,3,0,26
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	22819
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	497
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	23086
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	23222
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	672
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	23333
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	23466
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	23669
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	24025
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	689
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	24203
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	672
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	24303
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	672
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	672
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	672
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	9,0,2,35,2,0,26
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	24673
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	24859
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	25057
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	497
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	25290
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	672
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	672
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	25442
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	672
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	672
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	672
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	26009
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	672
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	672
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	26303
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	672
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	672
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	672
	.byte	4,0,2,35,3,0,26
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	26581
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	27077
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	689
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	27390
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	672
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	672
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	27599
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	672
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	27810
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	28242
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	672
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	672
	.byte	7,0,2,35,3,0,26
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	28338
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	28598
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	497
	.byte	23,0,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	28723
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	28920
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	29073
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	29226
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	29379
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	536
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	711
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	955
	.byte	26
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	10505
	.byte	26
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	10639
	.byte	26
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	10899
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12044
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	29728
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12601
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	29792
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12678
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	29856
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12814
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	29921
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13094
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	29986
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13332
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	30051
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13460
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	30116
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13703
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	30181
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13938
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	30246
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14066
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	30311
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14166
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	30376
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14266
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	30441
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14474
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	30505
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14639
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	30569
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14822
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	30633
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14976
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	30698
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15340
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	30760
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15551
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	30822
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15803
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	30884
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15921
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	30948
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16032
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	31013
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16195
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	31079
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16358
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	31145
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16516
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	31213
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16681
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	31280
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17010
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	31348
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17231
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	31416
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17394
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	31482
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17666
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	31549
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17819
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	31618
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17975
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	31687
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18137
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	31756
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18280
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	31825
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18445
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	31894
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18590
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	31963
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18771
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	32031
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18945
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	32099
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19105
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	32167
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19249
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	32235
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19523
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	32300
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19662
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	32365
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19825
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	32431
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20043
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	32495
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20206
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	32556
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20542
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	32617
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20649
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	32677
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21101
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	32739
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21200
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	32799
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21350
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	32861
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21499
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	32929
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21660
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	32997
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21790
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	33065
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21922
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	33129
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22037
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	33194
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22148
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	33257
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22306
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	33318
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22718
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	33382
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22819
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	33443
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23086
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	33507
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23222
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	33574
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23333
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	33637
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23466
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	33698
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23669
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	33760
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24025
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	33825
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24203
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	33890
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24303
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	33955
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24673
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	34024
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24859
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	34093
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25057
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	34162
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25290
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	34227
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25442
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	34290
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26009
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	34355
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26303
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	34420
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26581
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	34485
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27077
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	34551
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27599
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	34620
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27390
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	34684
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27810
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	34749
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28242
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	34814
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28338
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	34879
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28598
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	34943
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28723
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	35009
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28920
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	35073
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29073
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	35138
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29226
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	35203
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29379
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	35268
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	632
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	915
	.byte	26
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1146
	.byte	26
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	10599
	.byte	26
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	10859
	.byte	26
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	11088
	.byte	14
	.word	1186
	.byte	26
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	35498
	.byte	14
	.word	11128
	.byte	26
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	35527
	.byte	15,8
	.word	30884
	.byte	16,1,0,15,20
	.word	672
	.byte	16,19,0,15,8
	.word	34227
	.byte	16,1,0,14
	.word	11128
	.byte	15,24
	.word	1186
	.byte	16,1,0,14
	.word	35586
	.byte	15,16
	.word	672
	.byte	16,15,0,15,28
	.word	672
	.byte	16,27,0,15,40
	.word	672
	.byte	16,39,0,15,16
	.word	30698
	.byte	16,3,0,15,16
	.word	32677
	.byte	16,3,0,15,180,3
	.word	672
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4559
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	32617
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2740
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	33318
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	34162
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	33760
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	33825
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	33890
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	34093
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	33955
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	34024
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	29921
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	29986
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	32495
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	32431
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	30051
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	30116
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	30181
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	30246
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	34749
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2740
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	34620
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	29856
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	34943
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	34684
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2740
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	31482
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	35554
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	30948
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	35009
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	30311
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	30376
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	35563
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	33637
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	32799
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	33382
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	33257
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	32739
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	32235
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	31213
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	31013
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	31079
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	34879
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2740
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	34290
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	34485
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	34551
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	35572
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2740
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	30633
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	30505
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	34355
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	34420
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	35581
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	30822
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	35595
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4899
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	35268
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	35203
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	35073
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	35138
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2740
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	33065
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	33129
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	30441
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	33194
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4559
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	34814
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	35600
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	32861
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	32929
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	32997
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	35609
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	33574
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4559
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	32300
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	31145
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	32365
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	31416
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	31280
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2740
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	31963
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	32031
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	32099
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	32167
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	31549
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	31618
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	31687
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	31756
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	31825
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	31894
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	31348
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2740
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	33507
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	33443
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	35618
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	35627
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	30760
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	32556
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	33698
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	35636
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2740
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	30569
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	35645
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	29792
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	29728
	.byte	4,3,35,252,7,0,14
	.word	35656
	.byte	26
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	37646
	.byte	26
	.byte	'IfxScuWdt_Config',0,3,100,3
	.word	10241
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,10,45,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_A_Bits',0,10,48,3
	.word	37693
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,10,51,16,4,11
	.byte	'VSS',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	520
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BIV_Bits',0,10,55,3
	.word	37754
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,10,58,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	520
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_BTV_Bits',0,10,62,3
	.word	37833
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,10,65,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT_Bits',0,10,69,3
	.word	37919
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,10,72,16,4,11
	.byte	'CM',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	520
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	520
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	520
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	21,0,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL_Bits',0,10,80,3
	.word	38008
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,10,83,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT_Bits',0,10,89,3
	.word	38154
	.byte	26
	.byte	'Ifx_CPU_CORE_ID_Bits',0,10,96,3
	.word	11257
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,10,99,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L_Bits',0,10,103,3
	.word	38310
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,10,106,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U_Bits',0,10,110,3
	.word	38403
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,10,113,16,4,11
	.byte	'MODREV',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	520
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID_Bits',0,10,118,3
	.word	38496
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,10,121,16,4,11
	.byte	'XE',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE_Bits',0,10,125,3
	.word	38603
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,10,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT_Bits',0,10,136,1,3
	.word	38690
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,10,139,1,16,4,11
	.byte	'CID',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID_Bits',0,10,143,1,3
	.word	38844
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,10,146,1,16,4,11
	.byte	'DATA',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_D_Bits',0,10,149,1,3
	.word	38938
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,10,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	520
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DATR_Bits',0,10,163,1,3
	.word	39001
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,10,166,1,16,4,11
	.byte	'DE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	520
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	520
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	19,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR_Bits',0,10,177,1,3
	.word	39219
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,10,180,1,16,4,11
	.byte	'DTA',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR_Bits',0,10,184,1,3
	.word	39434
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,10,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0_Bits',0,10,192,1,3
	.word	39528
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,10,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2_Bits',0,10,199,1,3
	.word	39644
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,10,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	520
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_CPU_DCX_Bits',0,10,206,1,3
	.word	39745
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,10,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD_Bits',0,10,212,1,3
	.word	39838
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,10,215,1,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR_Bits',0,10,218,1,3
	.word	39918
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,10,221,1,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR_Bits',0,10,233,1,3
	.word	39987
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,10,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	520
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_DMS_Bits',0,10,240,1,3
	.word	40216
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L_Bits',0,10,247,1,3
	.word	40309
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,10,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	520
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U_Bits',0,10,254,1,3
	.word	40404
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,10,129,2,16,4,11
	.byte	'RE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE_Bits',0,10,133,2,3
	.word	40499
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,10,136,2,16,4,11
	.byte	'WE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE_Bits',0,10,140,2,3
	.word	40589
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,10,143,2,16,4,11
	.byte	'SRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	520
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	520
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR_Bits',0,10,161,2,3
	.word	40679
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,10,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT_Bits',0,10,172,2,3
	.word	41003
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,10,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FCX_Bits',0,10,180,2,3
	.word	41157
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,10,183,2,16,4,11
	.byte	'TST',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	520
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	520
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	520
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	520
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	520
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,10,202,2,3
	.word	41263
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,10,205,2,16,4,11
	.byte	'OPC',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,10,212,2,3
	.word	41612
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,10,215,2,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,10,218,2,3
	.word	41772
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,10,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,10,224,2,3
	.word	41853
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,10,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,10,230,2,3
	.word	41940
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,10,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,10,236,2,3
	.word	42027
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,10,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT_Bits',0,10,243,2,3
	.word	42114
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,10,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	520
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	520
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	520
	.byte	6,0,2,35,0,0,26
	.byte	'Ifx_CPU_ICR_Bits',0,10,253,2,3
	.word	42205
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,10,128,3,16,4,11
	.byte	'ISP',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_ISP_Bits',0,10,131,3,3
	.word	42348
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,10,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	520
	.byte	12,0,2,35,0,0,26
	.byte	'Ifx_CPU_LCX_Bits',0,10,139,3,3
	.word	42414
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,10,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT_Bits',0,10,146,3,3
	.word	42520
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,10,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT_Bits',0,10,153,3,3
	.word	42613
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,10,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	520
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT_Bits',0,10,160,3,3
	.word	42706
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,10,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	520
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_CPU_PC_Bits',0,10,167,3,3
	.word	42799
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,10,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0_Bits',0,10,175,3,3
	.word	42884
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,10,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	520
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1_Bits',0,10,183,3,3
	.word	43000
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,10,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2_Bits',0,10,190,3,3
	.word	43111
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,10,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	520
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	520
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	520
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	520
	.byte	10,0,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI_Bits',0,10,200,3,3
	.word	43212
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,10,203,3,16,4,11
	.byte	'TA',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR_Bits',0,10,206,3,3
	.word	43342
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,10,209,3,16,4,11
	.byte	'IED',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	520
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	520
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	18,0,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR_Bits',0,10,221,3,3
	.word	43411
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,10,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	520
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0_Bits',0,10,229,3,3
	.word	43640
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,10,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	520
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	520
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1_Bits',0,10,237,3,3
	.word	43753
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,10,240,3,16,4,11
	.byte	'PSI',0,4
	.word	520
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	520
	.byte	16,0,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2_Bits',0,10,244,3,3
	.word	43866
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,10,247,3,16,4,11
	.byte	'FRE',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	17,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR_Bits',0,10,129,4,3
	.word	43957
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,10,132,4,16,4,11
	.byte	'CDC',0,4
	.word	520
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	520
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	520
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	520
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	520
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	520
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_PSW_Bits',0,10,147,4,3
	.word	44160
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,10,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	520
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	520
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	520
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	520
	.byte	1,0,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN_Bits',0,10,156,4,3
	.word	44403
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,10,159,4,16,4,11
	.byte	'PC',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	520
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	520
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	520
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	520
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	520
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	520
	.byte	7,0,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON_Bits',0,10,171,4,3
	.word	44531
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,10,174,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,10,177,4,3
	.word	44772
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,10,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,10,183,4,3
	.word	44855
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,10,186,4,16,4,11
	.byte	'EN',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,10,189,4,3
	.word	44946
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,10,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,10,195,4,3
	.word	45037
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,10,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,10,202,4,3
	.word	45136
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,10,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,10,209,4,3
	.word	45243
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,10,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT_Bits',0,10,220,4,3
	.word	45350
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,10,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON_Bits',0,10,231,4,3
	.word	45504
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,10,234,4,16,4,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	520
	.byte	27,0,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,10,238,4,3
	.word	45665
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,10,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	520
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	520
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	520
	.byte	15,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON_Bits',0,10,249,4,3
	.word	45763
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,10,252,4,16,4,11
	.byte	'Timer',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,10,255,4,3
	.word	45935
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,10,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	520
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR_Bits',0,10,133,5,3
	.word	46015
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,10,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	520
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	520
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	520
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	520
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	520
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	520
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	520
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	520
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	520
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	520
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	520
	.byte	3,0,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT_Bits',0,10,153,5,3
	.word	46088
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,10,156,5,16,4,11
	.byte	'T0',0,4
	.word	520
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	520
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	520
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	520
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	520
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	520
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	520
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	520
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	520
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,10,167,5,3
	.word	46406
	.byte	12,10,175,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37693
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_A',0,10,180,5,3
	.word	46601
	.byte	12,10,183,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37754
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BIV',0,10,188,5,3
	.word	46660
	.byte	12,10,191,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37833
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_BTV',0,10,196,5,3
	.word	46721
	.byte	12,10,199,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37919
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCNT',0,10,204,5,3
	.word	46782
	.byte	12,10,207,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38008
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CCTRL',0,10,212,5,3
	.word	46844
	.byte	12,10,215,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38154
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_COMPAT',0,10,220,5,3
	.word	46907
	.byte	26
	.byte	'Ifx_CPU_CORE_ID',0,10,228,5,3
	.word	11326
	.byte	12,10,231,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38310
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_L',0,10,236,5,3
	.word	46996
	.byte	12,10,239,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38403
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPR_U',0,10,244,5,3
	.word	47059
	.byte	12,10,247,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38496
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPU_ID',0,10,252,5,3
	.word	47122
	.byte	12,10,255,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38603
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CPXE',0,10,132,6,3
	.word	47186
	.byte	12,10,135,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38690
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CREVT',0,10,140,6,3
	.word	47248
	.byte	12,10,143,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38844
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_CUS_ID',0,10,148,6,3
	.word	47311
	.byte	12,10,151,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38938
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_D',0,10,156,6,3
	.word	47375
	.byte	12,10,159,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39001
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DATR',0,10,164,6,3
	.word	47434
	.byte	12,10,167,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39219
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGSR',0,10,172,6,3
	.word	47496
	.byte	12,10,175,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39434
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DBGTCR',0,10,180,6,3
	.word	47559
	.byte	12,10,183,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39528
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON0',0,10,188,6,3
	.word	47623
	.byte	12,10,191,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39644
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCON2',0,10,196,6,3
	.word	47686
	.byte	12,10,199,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39745
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DCX',0,10,204,6,3
	.word	47749
	.byte	12,10,207,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39838
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DEADD',0,10,212,6,3
	.word	47810
	.byte	12,10,215,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39918
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIEAR',0,10,220,6,3
	.word	47873
	.byte	12,10,223,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39987
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DIETR',0,10,228,6,3
	.word	47936
	.byte	12,10,231,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40216
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DMS',0,10,236,6,3
	.word	47999
	.byte	12,10,239,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40309
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_L',0,10,244,6,3
	.word	48060
	.byte	12,10,247,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40404
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPR_U',0,10,252,6,3
	.word	48123
	.byte	12,10,255,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40499
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPRE',0,10,132,7,3
	.word	48186
	.byte	12,10,135,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40589
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DPWE',0,10,140,7,3
	.word	48248
	.byte	12,10,143,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40679
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_DSTR',0,10,148,7,3
	.word	48310
	.byte	12,10,151,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41003
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_EXEVT',0,10,156,7,3
	.word	48372
	.byte	12,10,159,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41157
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FCX',0,10,164,7,3
	.word	48435
	.byte	12,10,167,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41263
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,10,172,7,3
	.word	48496
	.byte	12,10,175,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41612
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,10,180,7,3
	.word	48566
	.byte	12,10,183,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41772
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,10,188,7,3
	.word	48636
	.byte	12,10,191,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41853
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,10,196,7,3
	.word	48705
	.byte	12,10,199,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41940
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,10,204,7,3
	.word	48776
	.byte	12,10,207,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42027
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,10,212,7,3
	.word	48847
	.byte	12,10,215,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42114
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICNT',0,10,220,7,3
	.word	48918
	.byte	12,10,223,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42205
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ICR',0,10,228,7,3
	.word	48980
	.byte	12,10,231,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42348
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_ISP',0,10,236,7,3
	.word	49041
	.byte	12,10,239,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42414
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_LCX',0,10,244,7,3
	.word	49102
	.byte	12,10,247,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42520
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M1CNT',0,10,252,7,3
	.word	49163
	.byte	12,10,255,7,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42613
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M2CNT',0,10,132,8,3
	.word	49226
	.byte	12,10,135,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42706
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_M3CNT',0,10,140,8,3
	.word	49289
	.byte	12,10,143,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42799
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PC',0,10,148,8,3
	.word	49352
	.byte	12,10,151,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42884
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON0',0,10,156,8,3
	.word	49412
	.byte	12,10,159,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43000
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON1',0,10,164,8,3
	.word	49475
	.byte	12,10,167,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43111
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCON2',0,10,172,8,3
	.word	49538
	.byte	12,10,175,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43212
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PCXI',0,10,180,8,3
	.word	49601
	.byte	12,10,183,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43342
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIEAR',0,10,188,8,3
	.word	49663
	.byte	12,10,191,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43411
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PIETR',0,10,196,8,3
	.word	49726
	.byte	12,10,199,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43640
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA0',0,10,204,8,3
	.word	49789
	.byte	12,10,207,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43753
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA1',0,10,212,8,3
	.word	49851
	.byte	12,10,215,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43866
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PMA2',0,10,220,8,3
	.word	49913
	.byte	12,10,223,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43957
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSTR',0,10,228,8,3
	.word	49975
	.byte	12,10,231,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44160
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_PSW',0,10,236,8,3
	.word	50037
	.byte	12,10,239,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44403
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SEGEN',0,10,244,8,3
	.word	50098
	.byte	12,10,247,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44531
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SMACON',0,10,252,8,3
	.word	50161
	.byte	12,10,255,8,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44772
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENA',0,10,132,9,3
	.word	50225
	.byte	12,10,135,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44855
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_ACCENB',0,10,140,9,3
	.word	50295
	.byte	12,10,143,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44946
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,10,148,9,3
	.word	50365
	.byte	12,10,151,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45037
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,10,156,9,3
	.word	50439
	.byte	12,10,159,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45136
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,10,164,9,3
	.word	50513
	.byte	12,10,167,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45243
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,10,172,9,3
	.word	50583
	.byte	12,10,175,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45350
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SWEVT',0,10,180,9,3
	.word	50653
	.byte	12,10,183,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45504
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_SYSCON',0,10,188,9,3
	.word	50716
	.byte	12,10,191,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45665
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TASK_ASI',0,10,196,9,3
	.word	50780
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45763
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_CON',0,10,204,9,3
	.word	50846
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45935
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TPS_TIMER',0,10,212,9,3
	.word	50911
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46015
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_ADR',0,10,220,9,3
	.word	50978
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46088
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TR_EVT',0,10,228,9,3
	.word	51042
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46406
	.byte	4,2,35,0,0,26
	.byte	'Ifx_CPU_TRIG_ACC',0,10,236,9,3
	.word	51106
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,10,247,9,25,8,13
	.byte	'L',0
	.word	46996
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	47059
	.byte	4,2,35,4,0,14
	.word	51172
	.byte	26
	.byte	'Ifx_CPU_CPR',0,10,251,9,3
	.word	51214
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,10,254,9,25,8,13
	.byte	'L',0
	.word	48060
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	48123
	.byte	4,2,35,4,0,14
	.word	51240
	.byte	26
	.byte	'Ifx_CPU_DPR',0,10,130,10,3
	.word	51282
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,10,133,10,25,16,13
	.byte	'LA',0
	.word	50513
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	50583
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	50365
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	50439
	.byte	4,2,35,12,0,14
	.word	51308
	.byte	26
	.byte	'Ifx_CPU_SPROT_RGN',0,10,139,10,3
	.word	51390
	.byte	15,12
	.word	50911
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,10,142,10,25,16,13
	.byte	'CON',0
	.word	50846
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	51422
	.byte	12,2,35,4,0,14
	.word	51431
	.byte	26
	.byte	'Ifx_CPU_TPS',0,10,146,10,3
	.word	51479
	.byte	10
	.byte	'_Ifx_CPU_TR',0,10,149,10,25,8,13
	.byte	'EVT',0
	.word	51042
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	50978
	.byte	4,2,35,4,0,14
	.word	51505
	.byte	26
	.byte	'Ifx_CPU_TR',0,10,153,10,3
	.word	51550
	.byte	15,176,32
	.word	672
	.byte	16,175,32,0,15,208,223,1
	.word	672
	.byte	16,207,223,1,0,15,248,1
	.word	672
	.byte	16,247,1,0,15,244,29
	.word	672
	.byte	16,243,29,0,15,188,3
	.word	672
	.byte	16,187,3,0,15,232,3
	.word	672
	.byte	16,231,3,0,15,252,23
	.word	672
	.byte	16,251,23,0,15,228,63
	.word	672
	.byte	16,227,63,0,15,128,1
	.word	51240
	.byte	16,15,0,14
	.word	51665
	.byte	15,128,31
	.word	672
	.byte	16,255,30,0,15,64
	.word	51172
	.byte	16,7,0,14
	.word	51691
	.byte	15,192,31
	.word	672
	.byte	16,191,31,0,15,16
	.word	47186
	.byte	16,3,0,15,16
	.word	48186
	.byte	16,3,0,15,16
	.word	48248
	.byte	16,3,0,15,208,7
	.word	672
	.byte	16,207,7,0,14
	.word	51431
	.byte	15,240,23
	.word	672
	.byte	16,239,23,0,15,64
	.word	51505
	.byte	16,7,0,14
	.word	51770
	.byte	15,192,23
	.word	672
	.byte	16,191,23,0,15,232,1
	.word	672
	.byte	16,231,1,0,15,180,1
	.word	672
	.byte	16,179,1,0,15,172,1
	.word	672
	.byte	16,171,1,0,15,64
	.word	47375
	.byte	16,15,0,15,64
	.word	672
	.byte	16,63,0,15,64
	.word	46601
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,10,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	51575
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	50098
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	51586
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	50780
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	51599
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	49789
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	49851
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	49913
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	51610
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	47686
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4559
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	50161
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	48310
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2740
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	47434
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	47810
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	47873
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	47936
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3930
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	47623
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	51621
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	49975
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	49475
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	49538
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	49412
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	49663
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	49726
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	51632
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	46907
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	51643
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	48496
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	48636
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	48566
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2740
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	48705
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	48776
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	48847
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	51654
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	51675
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	51680
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	51700
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	51705
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	51716
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	51725
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	51734
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	51743
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	51754
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	51759
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	51779
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	51784
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	46844
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	46782
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	48918
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	49163
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	49226
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	49289
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	51795
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	47496
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2740
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	48372
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	47248
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	50653
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	35609
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	51106
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4899
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	47999
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	47749
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	47559
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	51806
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	49601
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	50037
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	49352
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4559
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	50716
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	47122
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	11326
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	46660
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	46721
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	49041
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	48980
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4559
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	48435
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	49102
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	35600
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	47311
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	51817
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	51828
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	51837
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	51846
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	51837
	.byte	64,4,35,192,255,3,0,14
	.word	51855
	.byte	26
	.byte	'Ifx_CPU',0,10,130,11,3
	.word	53646
	.byte	17,8,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,26
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	53668
	.byte	26
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	9673
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,14,45,16,4,11
	.byte	'SRPN',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	672
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	672
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_SRC_SRCR_Bits',0,14,62,3
	.word	53766
	.byte	12,14,70,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53766
	.byte	4,2,35,0,0,26
	.byte	'Ifx_SRC_SRCR',0,14,75,3
	.word	54082
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,14,86,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	54142
	.byte	26
	.byte	'Ifx_SRC_AGBT',0,14,89,3
	.word	54174
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,14,92,25,12,13
	.byte	'TX',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,8,0,14
	.word	54200
	.byte	26
	.byte	'Ifx_SRC_ASCLIN',0,14,97,3
	.word	54259
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,14,100,25,4,13
	.byte	'SBSRC',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	54287
	.byte	26
	.byte	'Ifx_SRC_BCUSPB',0,14,103,3
	.word	54324
	.byte	15,64
	.word	54082
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,14,106,25,64,13
	.byte	'INT',0
	.word	54352
	.byte	64,2,35,0,0,14
	.word	54361
	.byte	26
	.byte	'Ifx_SRC_CAN',0,14,109,3
	.word	54393
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,14,112,25,16,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54082
	.byte	4,2,35,12,0,14
	.word	54418
	.byte	26
	.byte	'Ifx_SRC_CCU6',0,14,118,3
	.word	54490
	.byte	15,8
	.word	54082
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,14,121,25,8,13
	.byte	'SR',0
	.word	54516
	.byte	8,2,35,0,0,14
	.word	54525
	.byte	26
	.byte	'Ifx_SRC_CERBERUS',0,14,124,3
	.word	54561
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,14,127,25,16,13
	.byte	'MI',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	54082
	.byte	4,2,35,12,0,14
	.word	54591
	.byte	26
	.byte	'Ifx_SRC_CIF',0,14,133,1,3
	.word	54664
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,14,136,1,25,4,13
	.byte	'SBSRC',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	54690
	.byte	26
	.byte	'Ifx_SRC_CPU',0,14,139,1,3
	.word	54725
	.byte	15,192,1
	.word	54082
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,14,142,1,25,208,1,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4899
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	54751
	.byte	192,1,2,35,16,0,14
	.word	54761
	.byte	26
	.byte	'Ifx_SRC_DMA',0,14,147,1,3
	.word	54828
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,14,150,1,25,8,13
	.byte	'SRM',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	54082
	.byte	4,2,35,4,0,14
	.word	54854
	.byte	26
	.byte	'Ifx_SRC_DSADC',0,14,154,1,3
	.word	54902
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,14,157,1,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	54930
	.byte	26
	.byte	'Ifx_SRC_EMEM',0,14,160,1,3
	.word	54963
	.byte	10
	.byte	'_Ifx_SRC_ERAY',0,14,163,1,25,80,13
	.byte	'INT',0
	.word	54516
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	54516
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	54516
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	54516
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	54082
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	54082
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	35618
	.byte	40,2,35,40,0,14
	.word	54990
	.byte	26
	.byte	'Ifx_SRC_ERAY',0,14,172,1,3
	.word	55117
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,14,175,1,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	55144
	.byte	26
	.byte	'Ifx_SRC_ETH',0,14,178,1,3
	.word	55176
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,14,181,1,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	55202
	.byte	26
	.byte	'Ifx_SRC_FCE',0,14,184,1,3
	.word	55234
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,14,187,1,25,12,13
	.byte	'DONE',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	54082
	.byte	4,2,35,8,0,14
	.word	55260
	.byte	26
	.byte	'Ifx_SRC_FFT',0,14,192,1,3
	.word	55320
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,14,195,1,25,32,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54082
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	35600
	.byte	16,2,35,16,0,14
	.word	55346
	.byte	26
	.byte	'Ifx_SRC_GPSR',0,14,202,1,3
	.word	55440
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,14,205,1,25,48,13
	.byte	'CIRQ',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	54082
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	54082
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	54082
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3930
	.byte	24,2,35,24,0,14
	.word	55467
	.byte	26
	.byte	'Ifx_SRC_GPT12',0,14,214,1,3
	.word	55584
	.byte	15,12
	.word	54082
	.byte	16,2,0,15,32
	.word	54082
	.byte	16,7,0,15,32
	.word	55621
	.byte	16,0,0,15,88
	.word	672
	.byte	16,87,0,15,108
	.word	54082
	.byte	16,26,0,15,96
	.word	672
	.byte	16,95,0,15,96
	.word	55621
	.byte	16,2,0,15,160,3
	.word	672
	.byte	16,159,3,0,15,64
	.word	55621
	.byte	16,1,0,15,192,3
	.word	672
	.byte	16,191,3,0,15,16
	.word	54082
	.byte	16,3,0,15,64
	.word	55706
	.byte	16,3,0,15,192,2
	.word	672
	.byte	16,191,2,0,15,52
	.word	672
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,14,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	55612
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2740
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	54082
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	54082
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	54516
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4559
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	55630
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	55639
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	55648
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	55657
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	54082
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4899
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	55666
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	55675
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	55666
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	55675
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	55686
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	55695
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	55715
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	55724
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	55612
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	55735
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	55612
	.byte	12,3,35,192,18,0,14
	.word	55744
	.byte	26
	.byte	'Ifx_SRC_GTM',0,14,243,1,3
	.word	56204
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,14,246,1,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	56230
	.byte	26
	.byte	'Ifx_SRC_HSCT',0,14,249,1,3
	.word	56263
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,14,252,1,25,16,13
	.byte	'COK',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	54082
	.byte	4,2,35,12,0,14
	.word	56290
	.byte	26
	.byte	'Ifx_SRC_HSSL',0,14,130,2,3
	.word	56363
	.byte	15,56
	.word	672
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,14,133,2,25,80,13
	.byte	'BREQ',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	54082
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	54082
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	56390
	.byte	56,2,35,24,0,14
	.word	56399
	.byte	26
	.byte	'Ifx_SRC_I2C',0,14,142,2,3
	.word	56522
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,14,145,2,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	56548
	.byte	26
	.byte	'Ifx_SRC_LMU',0,14,148,2,3
	.word	56580
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,14,151,2,25,20,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54082
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	54082
	.byte	4,2,35,16,0,14
	.word	56606
	.byte	26
	.byte	'Ifx_SRC_MSC',0,14,158,2,3
	.word	56691
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,14,161,2,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	56717
	.byte	26
	.byte	'Ifx_SRC_PMU',0,14,164,2,3
	.word	56749
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,14,167,2,25,32,13
	.byte	'SR',0
	.word	55621
	.byte	32,2,35,0,0,14
	.word	56775
	.byte	26
	.byte	'Ifx_SRC_PSI5',0,14,170,2,3
	.word	56808
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,14,173,2,25,32,13
	.byte	'SR',0
	.word	55621
	.byte	32,2,35,0,0,14
	.word	56835
	.byte	26
	.byte	'Ifx_SRC_PSI5S',0,14,176,2,3
	.word	56869
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,14,179,2,25,24,13
	.byte	'TX',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	54082
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	54082
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	54082
	.byte	4,2,35,20,0,14
	.word	56897
	.byte	26
	.byte	'Ifx_SRC_QSPI',0,14,187,2,3
	.word	56990
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,14,190,2,25,4,13
	.byte	'SR',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	57017
	.byte	26
	.byte	'Ifx_SRC_SCR',0,14,193,2,3
	.word	57049
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,14,196,2,25,20,13
	.byte	'DTS',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	55706
	.byte	16,2,35,4,0,14
	.word	57075
	.byte	26
	.byte	'Ifx_SRC_SCU',0,14,200,2,3
	.word	57121
	.byte	15,24
	.word	54082
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,14,203,2,25,24,13
	.byte	'SR',0
	.word	57147
	.byte	24,2,35,0,0,14
	.word	57156
	.byte	26
	.byte	'Ifx_SRC_SENT',0,14,206,2,3
	.word	57189
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,14,209,2,25,12,13
	.byte	'SR',0
	.word	55612
	.byte	12,2,35,0,0,14
	.word	57216
	.byte	26
	.byte	'Ifx_SRC_SMU',0,14,212,2,3
	.word	57248
	.byte	10
	.byte	'_Ifx_SRC_STM',0,14,215,2,25,8,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,0,14
	.word	57274
	.byte	26
	.byte	'Ifx_SRC_STM',0,14,219,2,3
	.word	57320
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,14,222,2,25,16,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54082
	.byte	4,2,35,12,0,14
	.word	57346
	.byte	26
	.byte	'Ifx_SRC_VADCCG',0,14,228,2,3
	.word	57421
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,14,231,2,25,16,13
	.byte	'SR0',0
	.word	54082
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	54082
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	54082
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	54082
	.byte	4,2,35,12,0,14
	.word	57450
	.byte	26
	.byte	'Ifx_SRC_VADCG',0,14,237,2,3
	.word	57524
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,14,240,2,25,4,13
	.byte	'SRC',0
	.word	54082
	.byte	4,2,35,0,0,14
	.word	57552
	.byte	26
	.byte	'Ifx_SRC_XBAR',0,14,243,2,3
	.word	57586
	.byte	15,4
	.word	54142
	.byte	16,0,0,14
	.word	57613
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,14,128,3,25,4,13
	.byte	'AGBT',0
	.word	57622
	.byte	4,2,35,0,0,14
	.word	57627
	.byte	26
	.byte	'Ifx_SRC_GAGBT',0,14,131,3,3
	.word	57663
	.byte	15,48
	.word	54200
	.byte	16,3,0,14
	.word	57691
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,14,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	57700
	.byte	48,2,35,0,0,14
	.word	57705
	.byte	26
	.byte	'Ifx_SRC_GASCLIN',0,14,137,3,3
	.word	57745
	.byte	14
	.word	54287
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,14,140,3,25,4,13
	.byte	'SPB',0
	.word	57775
	.byte	4,2,35,0,0,14
	.word	57780
	.byte	26
	.byte	'Ifx_SRC_GBCU',0,14,143,3,3
	.word	57814
	.byte	15,64
	.word	54361
	.byte	16,0,0,14
	.word	57841
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,14,146,3,25,64,13
	.byte	'CAN',0
	.word	57850
	.byte	64,2,35,0,0,14
	.word	57855
	.byte	26
	.byte	'Ifx_SRC_GCAN',0,14,149,3,3
	.word	57889
	.byte	15,32
	.word	54418
	.byte	16,1,0,14
	.word	57916
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,14,152,3,25,32,13
	.byte	'CCU6',0
	.word	57925
	.byte	32,2,35,0,0,14
	.word	57930
	.byte	26
	.byte	'Ifx_SRC_GCCU6',0,14,155,3,3
	.word	57966
	.byte	14
	.word	54525
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,14,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	57994
	.byte	8,2,35,0,0,14
	.word	57999
	.byte	26
	.byte	'Ifx_SRC_GCERBERUS',0,14,161,3,3
	.word	58043
	.byte	15,16
	.word	54591
	.byte	16,0,0,14
	.word	58075
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,14,164,3,25,16,13
	.byte	'CIF',0
	.word	58084
	.byte	16,2,35,0,0,14
	.word	58089
	.byte	26
	.byte	'Ifx_SRC_GCIF',0,14,167,3,3
	.word	58123
	.byte	15,8
	.word	54690
	.byte	16,1,0,14
	.word	58150
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,14,170,3,25,8,13
	.byte	'CPU',0
	.word	58159
	.byte	8,2,35,0,0,14
	.word	58164
	.byte	26
	.byte	'Ifx_SRC_GCPU',0,14,173,3,3
	.word	58198
	.byte	15,208,1
	.word	54761
	.byte	16,0,0,14
	.word	58225
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,14,176,3,25,208,1,13
	.byte	'DMA',0
	.word	58235
	.byte	208,1,2,35,0,0,14
	.word	58240
	.byte	26
	.byte	'Ifx_SRC_GDMA',0,14,179,3,3
	.word	58276
	.byte	14
	.word	54854
	.byte	14
	.word	54854
	.byte	14
	.word	54854
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,14,182,3,25,32,13
	.byte	'DSADC0',0
	.word	58303
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4559
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	58308
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	58313
	.byte	8,2,35,24,0,14
	.word	58318
	.byte	26
	.byte	'Ifx_SRC_GDSADC',0,14,188,3,3
	.word	58409
	.byte	15,4
	.word	54930
	.byte	16,0,0,14
	.word	58438
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,14,191,3,25,4,13
	.byte	'EMEM',0
	.word	58447
	.byte	4,2,35,0,0,14
	.word	58452
	.byte	26
	.byte	'Ifx_SRC_GEMEM',0,14,194,3,3
	.word	58488
	.byte	15,80
	.word	54990
	.byte	16,0,0,14
	.word	58516
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,14,197,3,25,80,13
	.byte	'ERAY',0
	.word	58525
	.byte	80,2,35,0,0,14
	.word	58530
	.byte	26
	.byte	'Ifx_SRC_GERAY',0,14,200,3,3
	.word	58566
	.byte	15,4
	.word	55144
	.byte	16,0,0,14
	.word	58594
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,14,203,3,25,4,13
	.byte	'ETH',0
	.word	58603
	.byte	4,2,35,0,0,14
	.word	58608
	.byte	26
	.byte	'Ifx_SRC_GETH',0,14,206,3,3
	.word	58642
	.byte	15,4
	.word	55202
	.byte	16,0,0,14
	.word	58669
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,14,209,3,25,4,13
	.byte	'FCE',0
	.word	58678
	.byte	4,2,35,0,0,14
	.word	58683
	.byte	26
	.byte	'Ifx_SRC_GFCE',0,14,212,3,3
	.word	58717
	.byte	15,12
	.word	55260
	.byte	16,0,0,14
	.word	58744
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,14,215,3,25,12,13
	.byte	'FFT',0
	.word	58753
	.byte	12,2,35,0,0,14
	.word	58758
	.byte	26
	.byte	'Ifx_SRC_GFFT',0,14,218,3,3
	.word	58792
	.byte	15,64
	.word	55346
	.byte	16,1,0,14
	.word	58819
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,14,221,3,25,64,13
	.byte	'GPSR',0
	.word	58828
	.byte	64,2,35,0,0,14
	.word	58833
	.byte	26
	.byte	'Ifx_SRC_GGPSR',0,14,224,3,3
	.word	58869
	.byte	15,48
	.word	55467
	.byte	16,0,0,14
	.word	58897
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,14,227,3,25,48,13
	.byte	'GPT12',0
	.word	58906
	.byte	48,2,35,0,0,14
	.word	58911
	.byte	26
	.byte	'Ifx_SRC_GGPT12',0,14,230,3,3
	.word	58949
	.byte	15,204,18
	.word	55744
	.byte	16,0,0,14
	.word	58978
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,14,233,3,25,204,18,13
	.byte	'GTM',0
	.word	58988
	.byte	204,18,2,35,0,0,14
	.word	58993
	.byte	26
	.byte	'Ifx_SRC_GGTM',0,14,236,3,3
	.word	59029
	.byte	15,4
	.word	56230
	.byte	16,0,0,14
	.word	59056
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,14,239,3,25,4,13
	.byte	'HSCT',0
	.word	59065
	.byte	4,2,35,0,0,14
	.word	59070
	.byte	26
	.byte	'Ifx_SRC_GHSCT',0,14,242,3,3
	.word	59106
	.byte	15,64
	.word	56290
	.byte	16,3,0,14
	.word	59134
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,14,245,3,25,68,13
	.byte	'HSSL',0
	.word	59143
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	54082
	.byte	4,2,35,64,0,14
	.word	59148
	.byte	26
	.byte	'Ifx_SRC_GHSSL',0,14,249,3,3
	.word	59197
	.byte	15,80
	.word	56399
	.byte	16,0,0,14
	.word	59225
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,14,252,3,25,80,13
	.byte	'I2C',0
	.word	59234
	.byte	80,2,35,0,0,14
	.word	59239
	.byte	26
	.byte	'Ifx_SRC_GI2C',0,14,255,3,3
	.word	59273
	.byte	15,4
	.word	56548
	.byte	16,0,0,14
	.word	59300
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,14,130,4,25,4,13
	.byte	'LMU',0
	.word	59309
	.byte	4,2,35,0,0,14
	.word	59314
	.byte	26
	.byte	'Ifx_SRC_GLMU',0,14,133,4,3
	.word	59348
	.byte	15,40
	.word	56606
	.byte	16,1,0,14
	.word	59375
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,14,136,4,25,40,13
	.byte	'MSC',0
	.word	59384
	.byte	40,2,35,0,0,14
	.word	59389
	.byte	26
	.byte	'Ifx_SRC_GMSC',0,14,139,4,3
	.word	59423
	.byte	15,8
	.word	56717
	.byte	16,1,0,14
	.word	59450
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,14,142,4,25,8,13
	.byte	'PMU',0
	.word	59459
	.byte	8,2,35,0,0,14
	.word	59464
	.byte	26
	.byte	'Ifx_SRC_GPMU',0,14,145,4,3
	.word	59498
	.byte	15,32
	.word	56775
	.byte	16,0,0,14
	.word	59525
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,14,148,4,25,32,13
	.byte	'PSI5',0
	.word	59534
	.byte	32,2,35,0,0,14
	.word	59539
	.byte	26
	.byte	'Ifx_SRC_GPSI5',0,14,151,4,3
	.word	59575
	.byte	15,32
	.word	56835
	.byte	16,0,0,14
	.word	59603
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,14,154,4,25,32,13
	.byte	'PSI5S',0
	.word	59612
	.byte	32,2,35,0,0,14
	.word	59617
	.byte	26
	.byte	'Ifx_SRC_GPSI5S',0,14,157,4,3
	.word	59655
	.byte	15,96
	.word	56897
	.byte	16,3,0,14
	.word	59684
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,14,160,4,25,96,13
	.byte	'QSPI',0
	.word	59693
	.byte	96,2,35,0,0,14
	.word	59698
	.byte	26
	.byte	'Ifx_SRC_GQSPI',0,14,163,4,3
	.word	59734
	.byte	15,4
	.word	57017
	.byte	16,0,0,14
	.word	59762
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,14,166,4,25,4,13
	.byte	'SCR',0
	.word	59771
	.byte	4,2,35,0,0,14
	.word	59776
	.byte	26
	.byte	'Ifx_SRC_GSCR',0,14,169,4,3
	.word	59810
	.byte	14
	.word	57075
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,14,172,4,25,20,13
	.byte	'SCU',0
	.word	59837
	.byte	20,2,35,0,0,14
	.word	59842
	.byte	26
	.byte	'Ifx_SRC_GSCU',0,14,175,4,3
	.word	59876
	.byte	15,24
	.word	57156
	.byte	16,0,0,14
	.word	59903
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,14,178,4,25,24,13
	.byte	'SENT',0
	.word	59912
	.byte	24,2,35,0,0,14
	.word	59917
	.byte	26
	.byte	'Ifx_SRC_GSENT',0,14,181,4,3
	.word	59953
	.byte	15,12
	.word	57216
	.byte	16,0,0,14
	.word	59981
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,14,184,4,25,12,13
	.byte	'SMU',0
	.word	59990
	.byte	12,2,35,0,0,14
	.word	59995
	.byte	26
	.byte	'Ifx_SRC_GSMU',0,14,187,4,3
	.word	60029
	.byte	15,16
	.word	57274
	.byte	16,1,0,14
	.word	60056
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,14,190,4,25,16,13
	.byte	'STM',0
	.word	60065
	.byte	16,2,35,0,0,14
	.word	60070
	.byte	26
	.byte	'Ifx_SRC_GSTM',0,14,193,4,3
	.word	60104
	.byte	15,64
	.word	57450
	.byte	16,3,0,14
	.word	60131
	.byte	15,224,1
	.word	672
	.byte	16,223,1,0,15,32
	.word	57346
	.byte	16,1,0,14
	.word	60156
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,14,196,4,25,192,2,13
	.byte	'G',0
	.word	60140
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	60145
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	60165
	.byte	32,3,35,160,2,0,14
	.word	60170
	.byte	26
	.byte	'Ifx_SRC_GVADC',0,14,201,4,3
	.word	60239
	.byte	14
	.word	57552
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,14,204,4,25,4,13
	.byte	'XBAR',0
	.word	60267
	.byte	4,2,35,0,0,14
	.word	60272
	.byte	26
	.byte	'Ifx_SRC_GXBAR',0,14,207,4,3
	.word	60308
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_STM_ACCEN0_Bits',0,15,79,3
	.word	60336
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1_Bits',0,15,85,3
	.word	60893
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,15,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAP_Bits',0,15,91,3
	.word	60970
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,15,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV_Bits',0,15,97,3
	.word	61042
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,15,100,16,4,11
	.byte	'DISR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_CLC_Bits',0,15,107,3
	.word	61118
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,15,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	672
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	672
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	672
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	672
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	3,0,2,35,3,0,26
	.byte	'Ifx_STM_CMCON_Bits',0,15,120,3
	.word	61259
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,15,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_CMP_Bits',0,15,126,3
	.word	61477
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,15,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	497
	.byte	25,0,2,35,0,0,26
	.byte	'Ifx_STM_ICR_Bits',0,15,139,1,3
	.word	61544
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,15,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_STM_ID_Bits',0,15,147,1,3
	.word	61747
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,15,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_STM_ISCR_Bits',0,15,157,1,3
	.word	61854
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,15,160,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	497
	.byte	30,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST0_Bits',0,15,165,1,3
	.word	62005
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,15,168,1,16,4,11
	.byte	'RST',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRST1_Bits',0,15,172,1,3
	.word	62116
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,15,175,1,16,4,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR_Bits',0,15,179,1,3
	.word	62208
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,15,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	672
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	672
	.byte	2,0,2,35,3,0,26
	.byte	'Ifx_STM_OCS_Bits',0,15,189,1,3
	.word	62304
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,15,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0_Bits',0,15,195,1,3
	.word	62450
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,15,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV_Bits',0,15,201,1,3
	.word	62522
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,15,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM1_Bits',0,15,207,1,3
	.word	62598
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,15,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM2_Bits',0,15,213,1,3
	.word	62670
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,15,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM3_Bits',0,15,219,1,3
	.word	62742
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,15,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM4_Bits',0,15,225,1,3
	.word	62815
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,15,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM5_Bits',0,15,231,1,3
	.word	62888
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,15,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_STM_TIM6_Bits',0,15,237,1,3
	.word	62961
	.byte	12,15,245,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60336
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN0',0,15,250,1,3
	.word	63034
	.byte	12,15,253,1,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60893
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ACCEN1',0,15,130,2,3
	.word	63098
	.byte	12,15,133,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60970
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAP',0,15,138,2,3
	.word	63162
	.byte	12,15,141,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61042
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CAPSV',0,15,146,2,3
	.word	63223
	.byte	12,15,149,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61118
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CLC',0,15,154,2,3
	.word	63286
	.byte	12,15,157,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61259
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMCON',0,15,162,2,3
	.word	63347
	.byte	12,15,165,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61477
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_CMP',0,15,170,2,3
	.word	63410
	.byte	12,15,173,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61544
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ICR',0,15,178,2,3
	.word	63471
	.byte	12,15,181,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61747
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ID',0,15,186,2,3
	.word	63532
	.byte	12,15,189,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61854
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_ISCR',0,15,194,2,3
	.word	63592
	.byte	12,15,197,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62005
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST0',0,15,202,2,3
	.word	63654
	.byte	12,15,205,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62116
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRST1',0,15,210,2,3
	.word	63717
	.byte	12,15,213,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62208
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_KRSTCLR',0,15,218,2,3
	.word	63780
	.byte	12,15,221,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62304
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_OCS',0,15,226,2,3
	.word	63845
	.byte	12,15,229,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62450
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0',0,15,234,2,3
	.word	63906
	.byte	12,15,237,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62522
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM0SV',0,15,242,2,3
	.word	63968
	.byte	12,15,245,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62598
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM1',0,15,250,2,3
	.word	64032
	.byte	12,15,253,2,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62670
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM2',0,15,130,3,3
	.word	64094
	.byte	12,15,133,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62742
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM3',0,15,138,3,3
	.word	64156
	.byte	12,15,141,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62815
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM4',0,15,146,3,3
	.word	64218
	.byte	12,15,149,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62888
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM5',0,15,154,3,3
	.word	64280
	.byte	12,15,157,3,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62961
	.byte	4,2,35,0,0,26
	.byte	'Ifx_STM_TIM6',0,15,162,3,3
	.word	64342
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,16,45,16,4,11
	.byte	'EN0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,16,79,3
	.word	64404
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,16,82,16,4,11
	.byte	'reserved_0',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,16,85,3
	.word	64965
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,16,88,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,16,95,3
	.word	65046
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,16,98,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,16,111,3
	.word	65199
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,16,114,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,16,121,3
	.word	65447
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,16,124,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	497
	.byte	24,0,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0_Bits',0,16,128,1,3
	.word	65593
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,16,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM1_Bits',0,16,136,1,3
	.word	65691
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,16,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_COMM2_Bits',0,16,144,1,3
	.word	65807
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,16,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRD_Bits',0,16,153,1,3
	.word	65923
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,16,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCRP_Bits',0,16,162,1,3
	.word	66063
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,16,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	497
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	689
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_ECCW_Bits',0,16,171,1,3
	.word	66203
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,16,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	672
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	672
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	689
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	672
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	672
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FCON_Bits',0,16,193,1,3
	.word	66342
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,16,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	672
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	672
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	672
	.byte	8,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FPRO_Bits',0,16,218,1,3
	.word	66704
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,16,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	689
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	672
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	672
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_FSR_Bits',0,16,254,1,3
	.word	67145
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,16,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	672
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	672
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_ID_Bits',0,16,134,2,3
	.word	67751
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,16,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	689
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARD_Bits',0,16,147,2,3
	.word	67862
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,16,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	689
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_MARP_Bits',0,16,159,2,3
	.word	68076
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,16,162,2,16,4,11
	.byte	'L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	672
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	689
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	672
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCOND_Bits',0,16,179,2,3
	.word	68263
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,16,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	672
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	497
	.byte	28,0,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,16,188,2,3
	.word	68587
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,16,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	689
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,16,199,2,3
	.word	68730
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	689
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	672
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	672
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	672
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	689
	.byte	14,0,2,35,2,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,16,219,2,3
	.word	68919
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,16,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	672
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,16,254,2,3
	.word	69282
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,16,129,3,16,4,11
	.byte	'S0L',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONP_Bits',0,16,160,3,3
	.word	69877
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,16,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	672
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	672
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	672
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	672
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	672
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	672
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	672
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	672
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	672
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	672
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	672
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	672
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	672
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	672
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	672
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	672
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	672
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	672
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	672
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	672
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	672
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,16,194,3,3
	.word	70401
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,16,197,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,16,201,3,3
	.word	70983
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,16,204,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,16,208,3,3
	.word	71085
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,16,211,3,16,4,11
	.byte	'TAG',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	497
	.byte	26,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,16,215,3,3
	.word	71187
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,16,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	497
	.byte	29,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD_Bits',0,16,222,3,3
	.word	71289
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,16,225,3,16,4,11
	.byte	'STRT',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	672
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	672
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	672
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	672
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	672
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	689
	.byte	16,0,2,35,2,0,26
	.byte	'Ifx_FLASH_RRCT_Bits',0,16,236,3,3
	.word	71383
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,16,239,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0_Bits',0,16,242,3,3
	.word	71593
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,16,245,3,16,4,11
	.byte	'DATA',0,4
	.word	497
	.byte	32,0,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1_Bits',0,16,248,3,3
	.word	71666
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,16,251,3,16,4,11
	.byte	'SEL',0,1
	.word	672
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	672
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	672
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	497
	.byte	22,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,16,130,4,3
	.word	71739
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,16,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	672
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	497
	.byte	31,0,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,16,137,4,3
	.word	71894
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,16,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	672
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	497
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	672
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	672
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	672
	.byte	1,0,2,35,3,0,26
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,16,147,4,3
	.word	71999
	.byte	12,16,155,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64404
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN0',0,16,160,4,3
	.word	72147
	.byte	12,16,163,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64965
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ACCEN1',0,16,168,4,3
	.word	72213
	.byte	12,16,171,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65046
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_CFG',0,16,176,4,3
	.word	72279
	.byte	12,16,179,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65199
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_STAT',0,16,184,4,3
	.word	72347
	.byte	12,16,187,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65447
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_CBAB_TOP',0,16,192,4,3
	.word	72416
	.byte	12,16,195,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65593
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM0',0,16,200,4,3
	.word	72484
	.byte	12,16,203,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65691
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM1',0,16,208,4,3
	.word	72549
	.byte	12,16,211,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65807
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_COMM2',0,16,216,4,3
	.word	72614
	.byte	12,16,219,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65923
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRD',0,16,224,4,3
	.word	72679
	.byte	12,16,227,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66063
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCRP',0,16,232,4,3
	.word	72744
	.byte	12,16,235,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66203
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ECCW',0,16,240,4,3
	.word	72809
	.byte	12,16,243,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66342
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FCON',0,16,248,4,3
	.word	72873
	.byte	12,16,251,4,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	66704
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FPRO',0,16,128,5,3
	.word	72937
	.byte	12,16,131,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67145
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_FSR',0,16,136,5,3
	.word	73001
	.byte	12,16,139,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67751
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_ID',0,16,144,5,3
	.word	73064
	.byte	12,16,147,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67862
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARD',0,16,152,5,3
	.word	73126
	.byte	12,16,155,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68076
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_MARP',0,16,160,5,3
	.word	73190
	.byte	12,16,163,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68263
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCOND',0,16,168,5,3
	.word	73254
	.byte	12,16,171,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68587
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONDBG',0,16,176,5,3
	.word	73321
	.byte	12,16,179,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68730
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSM',0,16,184,5,3
	.word	73390
	.byte	12,16,187,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68919
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,16,192,5,3
	.word	73459
	.byte	12,16,195,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69282
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONOTP',0,16,200,5,3
	.word	73532
	.byte	12,16,203,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69877
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONP',0,16,208,5,3
	.word	73601
	.byte	12,16,211,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70401
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_PROCONWOP',0,16,216,5,3
	.word	73668
	.byte	12,16,219,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70983
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG0',0,16,224,5,3
	.word	73737
	.byte	12,16,227,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71085
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG1',0,16,232,5,3
	.word	73805
	.byte	12,16,235,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71187
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RDB_CFG2',0,16,240,5,3
	.word	73873
	.byte	12,16,243,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71289
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRAD',0,16,248,5,3
	.word	73941
	.byte	12,16,251,5,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71383
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRCT',0,16,128,6,3
	.word	74005
	.byte	12,16,131,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71593
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD0',0,16,136,6,3
	.word	74069
	.byte	12,16,139,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71666
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_RRD1',0,16,144,6,3
	.word	74133
	.byte	12,16,147,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71739
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_CFG',0,16,152,6,3
	.word	74197
	.byte	12,16,155,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71894
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_STAT',0,16,160,6,3
	.word	74265
	.byte	12,16,163,6,9,4,13
	.byte	'U',0
	.word	497
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	513
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71999
	.byte	4,2,35,0,0,26
	.byte	'Ifx_FLASH_UBAB_TOP',0,16,168,6,3
	.word	74334
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,16,179,6,25,12,13
	.byte	'CFG',0
	.word	72279
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	72347
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	72416
	.byte	4,2,35,8,0,14
	.word	74402
	.byte	26
	.byte	'Ifx_FLASH_CBAB',0,16,184,6,3
	.word	74465
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,16,187,6,25,12,13
	.byte	'CFG0',0
	.word	73737
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	73805
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	73873
	.byte	4,2,35,8,0,14
	.word	74494
	.byte	26
	.byte	'Ifx_FLASH_RDB',0,16,192,6,3
	.word	74558
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,16,195,6,25,12,13
	.byte	'CFG',0
	.word	74197
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	74265
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	74334
	.byte	4,2,35,8,0,14
	.word	74586
	.byte	26
	.byte	'Ifx_FLASH_UBAB',0,16,200,6,3
	.word	74649
	.byte	26
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8312
	.byte	26
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8225
	.byte	26
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4568
	.byte	26
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2621
	.byte	26
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3616
	.byte	26
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2749
	.byte	26
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3396
	.byte	26
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2964
	.byte	26
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	3179
	.byte	26
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7584
	.byte	26
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7708
	.byte	26
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7792
	.byte	26
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7972
	.byte	26
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6223
	.byte	26
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6747
	.byte	26
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6397
	.byte	26
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6571
	.byte	26
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7236
	.byte	26
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	2050
	.byte	26
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5560
	.byte	26
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	6048
	.byte	26
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5707
	.byte	26
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5876
	.byte	26
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6903
	.byte	26
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1734
	.byte	26
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5274
	.byte	26
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4908
	.byte	26
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3939
	.byte	26
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4243
	.byte	26
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8839
	.byte	26
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8272
	.byte	26
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4859
	.byte	26
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2700
	.byte	26
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3890
	.byte	26
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2924
	.byte	26
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3576
	.byte	26
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	3139
	.byte	26
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3356
	.byte	26
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7668
	.byte	26
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7917
	.byte	26
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	8176
	.byte	26
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7544
	.byte	26
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6357
	.byte	26
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6863
	.byte	26
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6531
	.byte	26
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6707
	.byte	26
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2581
	.byte	26
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	7196
	.byte	26
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5667
	.byte	26
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	6183
	.byte	26
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5836
	.byte	26
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	6008
	.byte	26
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	2010
	.byte	26
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5520
	.byte	26
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5234
	.byte	26
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	4203
	.byte	26
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4519
	.byte	14
	.word	8879
	.byte	26
	.byte	'Ifx_P',0,6,139,6,3
	.word	75996
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,26
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	76016
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,26
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	76167
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,26
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	76411
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,26
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	76509
	.byte	26
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9492
	.byte	24,5,190,1,9,8,13
	.byte	'port',0
	.word	9487
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	672
	.byte	1,2,35,4,0,26
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	76974
	.byte	26
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,17,148,1,16
	.word	236
	.byte	24,17,212,5,9,8,13
	.byte	'value',0
	.word	9828
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9828
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_CcuconRegConfig',0,17,216,5,3
	.word	77074
	.byte	24,17,221,5,9,8,13
	.byte	'pDivider',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	672
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	672
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_InitialStepConfig',0,17,227,5,3
	.word	77145
	.byte	24,17,231,5,9,12,13
	.byte	'k2Step',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	293
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	77034
	.byte	4,2,35,8,0,26
	.byte	'IfxScuCcu_PllStepsConfig',0,17,236,5,3
	.word	77262
	.byte	3
	.word	233
	.byte	24,17,244,5,9,48,13
	.byte	'ccucon0',0
	.word	77074
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	77074
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	77074
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	77074
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	77074
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	77074
	.byte	8,2,35,40,0,26
	.byte	'IfxScuCcu_ClockDistributionConfig',0,17,252,5,3
	.word	77364
	.byte	24,17,128,6,9,8,13
	.byte	'value',0
	.word	9828
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9828
	.byte	4,2,35,4,0,26
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,17,132,6,3
	.word	77516
	.byte	3
	.word	77262
	.byte	24,17,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	672
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	77592
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	77145
	.byte	8,2,35,8,0,26
	.byte	'IfxScuCcu_SysPllConfig',0,17,142,6,3
	.word	77597
	.byte	17,7,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,26
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	77714
	.byte	24,7,160,1,9,6,13
	.byte	'counter',0
	.word	9828
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	672
	.byte	1,2,35,4,0,26
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	77803
	.byte	24,7,172,1,9,32,13
	.byte	'instruction',0
	.word	77803
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	77803
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	77803
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	77803
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	77803
	.byte	6,2,35,24,0,26
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	77869
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L155:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,19,1,58,15,59,15,57,15,11,15,0,0,25,38,0,73,19,0,0
	.byte	26,22,0,3,8,58,15,59,15,57,15,73,19,0,0,27,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L156:
	.word	.L485-.L484
.L484:
	.half	3
	.word	.L487-.L486
.L486:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L487:
.L485:
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_info'
.L157:
	.word	310
	.half	3
	.word	.L158
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L160,.L159
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_initConfig',0,1,134,2,6,1,1,1
	.word	.L138,.L262,.L137
	.byte	4
	.byte	'config',0,1,134,2,45
	.word	.L263,.L264
	.byte	5
	.word	.L138,.L262
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_abbrev'
.L158:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_line'
.L159:
	.word	.L489-.L488
.L488:
	.half	3
	.word	.L491-.L490
.L490:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L491:
	.byte	5,45,7,0,5,2
	.word	.L138
	.byte	3,135,2,1,5,43,9
	.half	.L492-.L138
	.byte	1,5,45,9
	.half	.L493-.L492
	.byte	3,1,1,5,43,9
	.half	.L494-.L493
	.byte	1,5,45,9
	.half	.L495-.L494
	.byte	3,1,1,5,43,9
	.half	.L496-.L495
	.byte	1,5,45,9
	.half	.L497-.L496
	.byte	3,1,1,5,43,9
	.half	.L498-.L497
	.byte	1,5,45,9
	.half	.L499-.L498
	.byte	3,1,1,5,43,9
	.half	.L500-.L499
	.byte	1,5,45,9
	.half	.L501-.L500
	.byte	3,1,1,5,43,9
	.half	.L502-.L501
	.byte	1,5,45,9
	.half	.L503-.L502
	.byte	3,1,1,5,43,9
	.half	.L504-.L503
	.byte	1,5,45,9
	.half	.L505-.L504
	.byte	3,1,1,5,43,9
	.half	.L506-.L505
	.byte	1,5,45,9
	.half	.L507-.L506
	.byte	3,1,1,5,43,9
	.half	.L508-.L507
	.byte	1,5,1,9
	.half	.L509-.L508
	.byte	3,1,1,7,9
	.half	.L161-.L509
	.byte	0,1,1
.L489:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_ranges'
.L160:
	.word	-1,.L138,0,.L161-.L138,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_info'
.L162:
	.word	377
	.half	3
	.word	.L163
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L165,.L164
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_initCpuWatchdog',0,1,148,2,6,1,1,1
	.word	.L140,.L265,.L139
	.byte	4
	.byte	'wdt',0,1,148,2,48
	.word	.L266,.L267
	.byte	4
	.byte	'config',0,1,148,2,77
	.word	.L268,.L269
	.byte	5
	.word	.L140,.L265
	.byte	6
	.byte	'wdt_con0',0,1,150,2,25
	.word	.L270,.L271
	.byte	6
	.byte	'wdt_con1',0,1,151,2,25
	.word	.L272,.L273
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_abbrev'
.L163:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_line'
.L164:
	.word	.L511-.L510
.L510:
	.half	3
	.word	.L513-.L512
.L512:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L513:
	.byte	5,27,7,0,5,2
	.word	.L140
	.byte	3,153,2,1,5,16,9
	.half	.L454-.L140
	.byte	3,1,1,5,5,9
	.half	.L456-.L454
	.byte	3,2,1,5,28,7,9
	.half	.L514-.L456
	.byte	3,3,1,9
	.half	.L515-.L514
	.byte	3,1,1,5,27,9
	.half	.L455-.L515
	.byte	3,1,1,5,21,9
	.half	.L457-.L455
	.byte	3,3,1,5,24,9
	.half	.L57-.L457
	.byte	3,6,1,9
	.half	.L516-.L57
	.byte	3,1,1,5,32,9
	.half	.L517-.L516
	.byte	3,1,1,5,24,9
	.half	.L518-.L517
	.byte	1,5,32,9
	.half	.L458-.L518
	.byte	3,1,1,5,24,9
	.half	.L519-.L458
	.byte	1,5,17,9
	.half	.L520-.L519
	.byte	3,3,1,5,36,9
	.half	.L521-.L520
	.byte	3,3,1,5,23,9
	.half	.L58-.L521
	.byte	1,5,36,9
	.half	.L522-.L58
	.byte	1,5,19,7,9
	.half	.L523-.L522
	.byte	3,4,1,5,10,9
	.half	.L524-.L523
	.byte	3,2,1,9
	.half	.L525-.L524
	.byte	3,4,1,9
	.half	.L526-.L525
	.byte	3,4,1,5,26,9
	.half	.L60-.L526
	.byte	3,121,1,5,24,9
	.half	.L527-.L60
	.byte	1,5,26,9
	.half	.L528-.L527
	.byte	3,1,1,5,24,9
	.half	.L529-.L528
	.byte	1,5,9,9
	.half	.L530-.L529
	.byte	3,1,1,5,26,9
	.half	.L61-.L530
	.byte	3,2,1,5,24,9
	.half	.L531-.L61
	.byte	1,5,26,9
	.half	.L532-.L531
	.byte	3,1,1,5,24,9
	.half	.L533-.L532
	.byte	1,5,9,9
	.half	.L534-.L533
	.byte	3,1,1,5,26,9
	.half	.L62-.L534
	.byte	3,2,1,5,24,9
	.half	.L535-.L62
	.byte	1,5,26,9
	.half	.L536-.L535
	.byte	3,1,1,5,24,9
	.half	.L537-.L536
	.byte	1,5,9,9
	.half	.L538-.L537
	.byte	3,1,1,5,29,9
	.half	.L64-.L538
	.byte	3,3,1,5,23,9
	.half	.L539-.L64
	.byte	1,5,47,7,9
	.half	.L540-.L539
	.byte	1,5,51,9
	.half	.L541-.L540
	.byte	1,5,47,9
	.half	.L67-.L541
	.byte	1,5,21,9
	.half	.L68-.L67
	.byte	1,5,29,9
	.half	.L542-.L68
	.byte	3,1,1,5,23,9
	.half	.L543-.L542
	.byte	1,5,52,7,9
	.half	.L544-.L543
	.byte	1,5,56,9
	.half	.L545-.L544
	.byte	1,5,52,9
	.half	.L69-.L545
	.byte	1,5,21,9
	.half	.L70-.L69
	.byte	1,5,29,9
	.half	.L546-.L70
	.byte	3,1,1,5,23,9
	.half	.L547-.L546
	.byte	1,5,61,7,9
	.half	.L548-.L547
	.byte	1,5,65,9
	.half	.L549-.L548
	.byte	1,5,61,9
	.half	.L71-.L549
	.byte	1,5,21,9
	.half	.L72-.L71
	.byte	1,5,29,9
	.half	.L550-.L72
	.byte	3,1,1,5,23,9
	.half	.L551-.L550
	.byte	1,5,48,7,9
	.half	.L552-.L551
	.byte	1,5,52,9
	.half	.L553-.L552
	.byte	1,5,48,9
	.half	.L73-.L553
	.byte	1,5,21,9
	.half	.L74-.L73
	.byte	1,5,29,9
	.half	.L554-.L74
	.byte	3,1,1,5,23,9
	.half	.L555-.L554
	.byte	1,5,57,7,9
	.half	.L556-.L555
	.byte	1,5,61,9
	.half	.L557-.L556
	.byte	1,5,57,9
	.half	.L75-.L557
	.byte	1,5,21,9
	.half	.L76-.L75
	.byte	1,5,17,9
	.half	.L558-.L76
	.byte	3,3,1,5,35,9
	.half	.L559-.L558
	.byte	3,3,1,5,1,9
	.half	.L453-.L559
	.byte	3,1,1,7,9
	.half	.L166-.L453
	.byte	0,1,1
.L511:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_ranges'
.L165:
	.word	-1,.L140,0,.L166-.L140,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_info'
.L167:
	.word	380
	.half	3
	.word	.L168
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L170,.L169
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_initSafetyWatchdog',0,1,214,2,6,1,1,1
	.word	.L142,.L274,.L141
	.byte	4
	.byte	'wdt',0,1,214,2,49
	.word	.L275,.L276
	.byte	4
	.byte	'config',0,1,214,2,78
	.word	.L268,.L277
	.byte	5
	.word	.L142,.L274
	.byte	6
	.byte	'wdt_con0',0,1,216,2,23
	.word	.L278,.L279
	.byte	6
	.byte	'wdt_con1',0,1,217,2,23
	.word	.L280,.L281
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_abbrev'
.L168:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_line'
.L169:
	.word	.L561-.L560
.L560:
	.half	3
	.word	.L563-.L562
.L562:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L563:
	.byte	5,27,7,0,5,2
	.word	.L142
	.byte	3,219,2,1,5,16,9
	.half	.L461-.L142
	.byte	3,1,1,5,5,9
	.half	.L463-.L461
	.byte	3,2,1,5,28,7,9
	.half	.L564-.L463
	.byte	3,3,1,9
	.half	.L565-.L564
	.byte	3,1,1,5,27,9
	.half	.L462-.L565
	.byte	3,1,1,5,21,9
	.half	.L464-.L462
	.byte	3,3,1,5,24,9
	.half	.L77-.L464
	.byte	3,6,1,9
	.half	.L566-.L77
	.byte	3,1,1,5,32,9
	.half	.L567-.L566
	.byte	3,1,1,5,24,9
	.half	.L568-.L567
	.byte	1,5,32,9
	.half	.L465-.L568
	.byte	3,1,1,5,24,9
	.half	.L569-.L465
	.byte	1,5,17,9
	.half	.L570-.L569
	.byte	3,3,1,5,36,9
	.half	.L571-.L570
	.byte	3,3,1,5,23,9
	.half	.L78-.L571
	.byte	1,5,36,9
	.half	.L572-.L78
	.byte	1,5,19,7,9
	.half	.L573-.L572
	.byte	3,4,1,5,10,9
	.half	.L574-.L573
	.byte	3,2,1,9
	.half	.L575-.L574
	.byte	3,4,1,9
	.half	.L576-.L575
	.byte	3,4,1,5,26,9
	.half	.L80-.L576
	.byte	3,121,1,5,24,9
	.half	.L577-.L80
	.byte	1,5,26,9
	.half	.L578-.L577
	.byte	3,1,1,5,24,9
	.half	.L579-.L578
	.byte	1,5,9,9
	.half	.L580-.L579
	.byte	3,1,1,5,26,9
	.half	.L81-.L580
	.byte	3,2,1,5,24,9
	.half	.L581-.L81
	.byte	1,5,26,9
	.half	.L582-.L581
	.byte	3,1,1,5,24,9
	.half	.L583-.L582
	.byte	1,5,9,9
	.half	.L584-.L583
	.byte	3,1,1,5,26,9
	.half	.L82-.L584
	.byte	3,2,1,5,24,9
	.half	.L585-.L82
	.byte	1,5,26,9
	.half	.L586-.L585
	.byte	3,1,1,5,24,9
	.half	.L587-.L586
	.byte	1,5,9,9
	.half	.L588-.L587
	.byte	3,1,1,5,31,9
	.half	.L84-.L588
	.byte	3,3,1,5,25,9
	.half	.L589-.L84
	.byte	1,5,49,7,9
	.half	.L590-.L589
	.byte	1,5,53,9
	.half	.L591-.L590
	.byte	1,5,49,9
	.half	.L87-.L591
	.byte	1,5,23,9
	.half	.L88-.L87
	.byte	1,5,31,9
	.half	.L592-.L88
	.byte	3,1,1,5,25,9
	.half	.L593-.L592
	.byte	1,5,54,7,9
	.half	.L594-.L593
	.byte	1,5,58,9
	.half	.L595-.L594
	.byte	1,5,54,9
	.half	.L89-.L595
	.byte	1,5,23,9
	.half	.L90-.L89
	.byte	1,5,31,9
	.half	.L596-.L90
	.byte	3,1,1,5,25,9
	.half	.L597-.L596
	.byte	1,5,63,7,9
	.half	.L598-.L597
	.byte	1,5,67,9
	.half	.L599-.L598
	.byte	1,5,63,9
	.half	.L91-.L599
	.byte	1,5,23,9
	.half	.L92-.L91
	.byte	1,5,31,9
	.half	.L600-.L92
	.byte	3,1,1,5,25,9
	.half	.L601-.L600
	.byte	1,5,50,7,9
	.half	.L602-.L601
	.byte	1,5,54,9
	.half	.L603-.L602
	.byte	1,5,50,9
	.half	.L93-.L603
	.byte	1,5,23,9
	.half	.L94-.L93
	.byte	1,5,31,9
	.half	.L604-.L94
	.byte	3,1,1,5,25,9
	.half	.L605-.L604
	.byte	1,5,59,7,9
	.half	.L606-.L605
	.byte	1,5,63,9
	.half	.L607-.L606
	.byte	1,5,59,9
	.half	.L95-.L607
	.byte	1,5,23,9
	.half	.L96-.L95
	.byte	1,5,31,9
	.half	.L608-.L96
	.byte	3,1,1,5,25,9
	.half	.L609-.L608
	.byte	1,5,54,7,9
	.half	.L610-.L609
	.byte	1,5,58,9
	.half	.L611-.L610
	.byte	1,5,54,9
	.half	.L97-.L611
	.byte	1,5,23,9
	.half	.L98-.L97
	.byte	1,5,17,9
	.half	.L612-.L98
	.byte	3,3,1,5,38,9
	.half	.L613-.L612
	.byte	3,3,1,5,1,9
	.half	.L460-.L613
	.byte	3,1,1,7,9
	.half	.L171-.L460
	.byte	0,1,1
.L561:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_ranges'
.L170:
	.word	-1,.L142,0,.L171-.L142,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_info'
.L172:
	.word	408
	.half	3
	.word	.L173
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L175,.L174
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_clearCpuEndinit',0,1,192,1,6,1,1,1
	.word	.L120,.L282,.L119
	.byte	4
	.byte	'password',0,1,192,1,39
	.word	.L283,.L284
	.byte	5
	.word	.L120,.L282
	.byte	6
	.word	.L285,.L120,.L16
	.byte	7
	.word	.L286,.L120,.L16
	.byte	8
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L288
	.byte	0,0,6
	.word	.L289,.L290,.L291
	.byte	9
	.word	.L292,.L293
	.byte	9
	.word	.L294,.L295
	.byte	10
	.word	.L296,.L290,.L291
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_abbrev'
.L173:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,8,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,9,5,0,49,16,2,6
	.byte	0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_line'
.L174:
	.word	.L615-.L614
.L614:
	.half	3
	.word	.L617-.L616
.L616:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L617:
	.byte	4,2,5,19,7,0,5,2
	.word	.L120
	.byte	3,143,6,1,5,37,9
	.half	.L433-.L120
	.byte	3,1,1,5,5,9
	.half	.L434-.L433
	.byte	1,4,1,5,55,9
	.half	.L16-.L434
	.byte	3,177,123,1,4,3,5,25,9
	.half	.L290-.L16
	.byte	3,245,1,1,5,5,9
	.half	.L618-.L290
	.byte	1,5,38,7,9
	.half	.L619-.L618
	.byte	3,5,1,5,45,9
	.half	.L620-.L619
	.byte	3,1,1,5,50,9
	.half	.L621-.L620
	.byte	1,5,69,9
	.half	.L622-.L621
	.byte	3,127,1,5,26,9
	.half	.L623-.L622
	.byte	3,126,1,5,34,9
	.half	.L17-.L623
	.byte	3,9,1,5,41,9
	.half	.L624-.L17
	.byte	3,1,1,5,46,9
	.half	.L625-.L624
	.byte	1,5,65,9
	.half	.L626-.L625
	.byte	3,127,1,5,22,9
	.half	.L627-.L626
	.byte	3,126,1,5,41,9
	.half	.L628-.L627
	.byte	3,6,1,5,28,9
	.half	.L18-.L628
	.byte	1,5,41,9
	.half	.L629-.L18
	.byte	1,4,1,5,1,7,9
	.half	.L291-.L629
	.byte	3,252,125,1,7,9
	.half	.L176-.L291
	.byte	0,1,1
.L615:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_ranges'
.L175:
	.word	-1,.L120,0,.L176-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_info'
.L177:
	.word	357
	.half	3
	.word	.L178
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L180,.L179
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_clearSafetyEndinit',0,1,198,1,6,1,1,1
	.word	.L122,.L297,.L121
	.byte	4
	.byte	'password',0,1,198,1,42
	.word	.L283,.L298
	.byte	5
	.word	.L122,.L297
	.byte	6
	.word	.L299,.L122,.L300
	.byte	7
	.word	.L301,.L302
	.byte	8
	.word	.L303,.L122,.L300
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_abbrev'
.L178:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_line'
.L179:
	.word	.L631-.L630
.L630:
	.half	3
	.word	.L633-.L632
.L632:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L633:
	.byte	4,2,5,24,7,0,5,2
	.word	.L122
	.byte	3,205,3,1,5,5,9
	.half	.L634-.L122
	.byte	1,5,37,7,9
	.half	.L635-.L634
	.byte	3,5,1,5,43,9
	.half	.L636-.L635
	.byte	3,1,1,5,48,9
	.half	.L637-.L636
	.byte	1,5,66,9
	.half	.L638-.L637
	.byte	3,127,1,5,25,9
	.half	.L639-.L638
	.byte	3,126,1,5,33,9
	.half	.L20-.L639
	.byte	3,9,1,5,39,9
	.half	.L640-.L20
	.byte	3,1,1,5,44,9
	.half	.L641-.L640
	.byte	1,5,62,9
	.half	.L642-.L641
	.byte	3,127,1,5,21,9
	.half	.L643-.L642
	.byte	3,126,1,5,40,9
	.half	.L644-.L643
	.byte	3,6,1,5,27,9
	.half	.L21-.L644
	.byte	1,5,40,9
	.half	.L645-.L21
	.byte	1,4,1,5,1,7,9
	.half	.L300-.L645
	.byte	3,235,125,1,7,9
	.half	.L181-.L300
	.byte	0,1,1
.L631:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_ranges'
.L180:
	.word	-1,.L122,0,.L181-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_info'
.L182:
	.word	406
	.half	3
	.word	.L183
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L185,.L184
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_setCpuEndinit',0,1,165,3,6,1,1,1
	.word	.L148,.L304,.L147
	.byte	4
	.byte	'password',0,1,165,3,37
	.word	.L283,.L305
	.byte	5
	.word	.L148,.L304
	.byte	6
	.word	.L285,.L148,.L99
	.byte	7
	.word	.L286,.L148,.L99
	.byte	8
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L306
	.byte	0,0,6
	.word	.L307,.L308,.L309
	.byte	9
	.word	.L310,.L311
	.byte	9
	.word	.L312,.L313
	.byte	10
	.word	.L314,.L308,.L309
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_abbrev'
.L183:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,11,1,49,16,17,1,18,1,0,0,8,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,9,5,0,49,16,2,6
	.byte	0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_line'
.L184:
	.word	.L647-.L646
.L646:
	.half	3
	.word	.L649-.L648
.L648:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L649:
	.byte	4,2,5,19,7,0,5,2
	.word	.L148
	.byte	3,143,6,1,5,37,9
	.half	.L470-.L148
	.byte	3,1,1,5,5,9
	.half	.L471-.L470
	.byte	1,4,1,5,53,9
	.half	.L99-.L471
	.byte	3,150,125,1,4,3,5,25,9
	.half	.L308-.L99
	.byte	3,231,0,1,5,5,9
	.half	.L650-.L308
	.byte	1,5,38,7,9
	.half	.L651-.L650
	.byte	3,5,1,5,45,9
	.half	.L652-.L651
	.byte	3,1,1,5,50,9
	.half	.L653-.L652
	.byte	1,5,69,9
	.half	.L654-.L653
	.byte	3,127,1,5,26,9
	.half	.L655-.L654
	.byte	3,126,1,5,34,9
	.half	.L100-.L655
	.byte	3,9,1,5,41,9
	.half	.L656-.L100
	.byte	3,1,1,5,46,9
	.half	.L657-.L656
	.byte	1,5,65,9
	.half	.L658-.L657
	.byte	3,127,1,5,22,9
	.half	.L659-.L658
	.byte	3,126,1,5,41,9
	.half	.L660-.L659
	.byte	3,6,1,5,28,9
	.half	.L101-.L660
	.byte	1,5,41,9
	.half	.L661-.L101
	.byte	1,4,1,5,1,7,9
	.half	.L309-.L661
	.byte	3,138,127,1,7,9
	.half	.L186-.L309
	.byte	0,1,1
.L647:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_ranges'
.L185:
	.word	-1,.L148,0,.L186-.L148,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_info'
.L187:
	.word	355
	.half	3
	.word	.L188
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L190,.L189
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_setSafetyEndinit',0,1,171,3,6,1,1,1
	.word	.L150,.L315,.L149
	.byte	4
	.byte	'password',0,1,171,3,40
	.word	.L283,.L316
	.byte	5
	.word	.L150,.L315
	.byte	6
	.word	.L317,.L150,.L318
	.byte	7
	.word	.L319,.L320
	.byte	8
	.word	.L321,.L150,.L318
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_abbrev'
.L188:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_line'
.L189:
	.word	.L663-.L662
.L662:
	.half	3
	.word	.L665-.L664
.L664:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L665:
	.byte	4,2,5,24,7,0,5,2
	.word	.L150
	.byte	3,164,4,1,5,5,9
	.half	.L666-.L150
	.byte	1,5,37,7,9
	.half	.L667-.L666
	.byte	3,5,1,5,43,9
	.half	.L668-.L667
	.byte	3,1,1,5,48,9
	.half	.L669-.L668
	.byte	1,5,66,9
	.half	.L670-.L669
	.byte	3,127,1,5,25,9
	.half	.L671-.L670
	.byte	3,126,1,5,33,9
	.half	.L103-.L671
	.byte	3,9,1,5,39,9
	.half	.L672-.L103
	.byte	3,1,1,5,44,9
	.half	.L673-.L672
	.byte	1,5,62,9
	.half	.L674-.L673
	.byte	3,127,1,5,21,9
	.half	.L675-.L674
	.byte	3,126,1,5,40,9
	.half	.L676-.L675
	.byte	3,6,1,5,27,9
	.half	.L104-.L676
	.byte	1,5,40,9
	.half	.L677-.L104
	.byte	1,4,1,5,1,7,9
	.half	.L318-.L677
	.byte	3,249,126,1,7,9
	.half	.L191-.L318
	.byte	0,1,1
.L663:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_ranges'
.L190:
	.word	-1,.L150,0,.L191-.L150,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_info'
.L192:
	.word	437
	.half	3
	.word	.L193
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L195,.L194
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_changeCpuWatchdogPassword',0,1,65,6,1,1,1
	.word	.L112,.L322,.L111
	.byte	4
	.byte	'password',0,1,65,49
	.word	.L283,.L323
	.byte	4
	.byte	'newPassword',0,1,65,66
	.word	.L283,.L324
	.byte	5
	.word	.L112,.L322
	.byte	6
	.byte	'watchdog',0,1,67,25
	.word	.L266,.L325
	.byte	6
	.byte	'wdt_con0',0,1,70,25
	.word	.L270,.L326
	.byte	7
	.word	.L285,.L112,.L2
	.byte	8
	.word	.L286,.L112,.L2
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L327
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_abbrev'
.L193:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_line'
.L194:
	.word	.L679-.L678
.L678:
	.half	3
	.word	.L681-.L680
.L680:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L681:
	.byte	4,2,5,19,7,0,5,2
	.word	.L112
	.byte	3,143,6,1,5,37,9
	.half	.L420-.L112
	.byte	3,1,1,5,5,9
	.half	.L421-.L420
	.byte	1,4,1,5,54,9
	.half	.L2-.L421
	.byte	3,178,122,1,5,32,9
	.half	.L422-.L2
	.byte	3,4,1,5,5,9
	.half	.L423-.L422
	.byte	3,2,1,5,28,7,9
	.half	.L682-.L423
	.byte	3,3,1,9
	.half	.L683-.L682
	.byte	3,1,1,9
	.half	.L684-.L683
	.byte	3,1,1,5,26,9
	.half	.L685-.L684
	.byte	3,3,1,5,24,9
	.half	.L3-.L685
	.byte	3,4,1,9
	.half	.L686-.L3
	.byte	3,1,1,9
	.half	.L687-.L686
	.byte	3,1,1,9
	.half	.L688-.L687
	.byte	3,1,1,5,41,9
	.half	.L689-.L688
	.byte	3,3,1,5,28,9
	.half	.L4-.L689
	.byte	1,5,41,9
	.half	.L690-.L4
	.byte	1,5,1,7,9
	.half	.L691-.L690
	.byte	3,2,1,7,9
	.half	.L196-.L691
	.byte	0,1,1
.L679:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_ranges'
.L195:
	.word	-1,.L112,0,.L196-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_info'
.L197:
	.word	444
	.half	3
	.word	.L198
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L200,.L199
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_changeCpuWatchdogReload',0,1,96,6,1,1,1
	.word	.L114,.L328,.L113
	.byte	4
	.byte	'password',0,1,96,47
	.word	.L283,.L329
	.byte	4
	.byte	'reload',0,1,96,64
	.word	.L283,.L330
	.byte	5
	.word	.L114,.L328
	.byte	6
	.byte	'coreId',0,1,99,25
	.word	.L331,.L332
	.byte	6
	.byte	'wdt',0,1,100,25
	.word	.L266,.L333
	.byte	6
	.byte	'wdt_con0',0,1,103,25
	.word	.L270,.L334
	.byte	7
	.word	.L285,.L114,.L6
	.byte	8
	.word	.L286,.L114,.L6
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L335
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_abbrev'
.L198:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_line'
.L199:
	.word	.L693-.L692
.L692:
	.half	3
	.word	.L695-.L694
.L694:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0,0
.L695:
	.byte	4,2,5,19,7,0,5,2
	.word	.L114
	.byte	3,143,6,1,5,37,9
	.half	.L424-.L114
	.byte	3,1,1,5,5,9
	.half	.L425-.L424
	.byte	1,4,1,5,45,9
	.half	.L6-.L425
	.byte	3,211,122,1,5,52,9
	.half	.L696-.L6
	.byte	1,5,27,9
	.half	.L426-.L696
	.byte	3,4,1,5,5,9
	.half	.L427-.L426
	.byte	3,2,1,5,28,7,9
	.half	.L697-.L427
	.byte	3,3,1,9
	.half	.L698-.L697
	.byte	3,1,1,9
	.half	.L699-.L698
	.byte	3,1,1,5,21,9
	.half	.L700-.L699
	.byte	3,3,1,5,24,9
	.half	.L7-.L700
	.byte	3,4,1,9
	.half	.L701-.L7
	.byte	3,1,1,9
	.half	.L702-.L701
	.byte	3,1,1,9
	.half	.L703-.L702
	.byte	3,1,1,5,36,9
	.half	.L704-.L703
	.byte	3,3,1,5,23,9
	.half	.L8-.L704
	.byte	1,5,36,9
	.half	.L705-.L8
	.byte	1,5,1,7,9
	.half	.L706-.L705
	.byte	3,2,1,7,9
	.half	.L201-.L706
	.byte	0,1,1
.L693:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_ranges'
.L200:
	.word	-1,.L114,0,.L201-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_info'
.L202:
	.word	400
	.half	3
	.word	.L203
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L205,.L204
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_changeSafetyWatchdogPassword',0,1,129,1,6,1,1,1
	.word	.L116,.L336,.L115
	.byte	4
	.byte	'password',0,1,129,1,52
	.word	.L283,.L337
	.byte	4
	.byte	'newPassword',0,1,129,1,69
	.word	.L283,.L338
	.byte	5
	.word	.L116,.L336
	.byte	6
	.byte	'watchdog',0,1,131,1,23
	.word	.L275,.L339
	.byte	6
	.byte	'wdt_con0',0,1,134,1,23
	.word	.L278,.L340
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_abbrev'
.L203:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_line'
.L204:
	.word	.L708-.L707
.L707:
	.half	3
	.word	.L710-.L709
.L709:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L710:
	.byte	5,45,7,0,5,2
	.word	.L116
	.byte	3,130,1,1,5,32,9
	.half	.L711-.L116
	.byte	3,4,1,5,5,9
	.half	.L429-.L711
	.byte	3,2,1,5,28,7,9
	.half	.L712-.L429
	.byte	3,3,1,9
	.half	.L713-.L712
	.byte	3,1,1,9
	.half	.L714-.L713
	.byte	3,1,1,5,26,9
	.half	.L715-.L714
	.byte	3,3,1,5,24,9
	.half	.L10-.L715
	.byte	3,4,1,9
	.half	.L716-.L10
	.byte	3,1,1,9
	.half	.L717-.L716
	.byte	3,1,1,9
	.half	.L718-.L717
	.byte	3,1,1,5,41,9
	.half	.L719-.L718
	.byte	3,3,1,5,28,9
	.half	.L11-.L719
	.byte	1,5,41,9
	.half	.L720-.L11
	.byte	1,5,1,7,9
	.half	.L721-.L720
	.byte	3,2,1,7,9
	.half	.L206-.L721
	.byte	0,1,1
.L708:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_ranges'
.L205:
	.word	-1,.L116,0,.L206-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_info'
.L207:
	.word	388
	.half	3
	.word	.L208
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L210,.L209
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_changeSafetyWatchdogReload',0,1,160,1,6,1,1,1
	.word	.L118,.L341,.L117
	.byte	4
	.byte	'password',0,1,160,1,50
	.word	.L283,.L342
	.byte	4
	.byte	'reload',0,1,160,1,67
	.word	.L283,.L343
	.byte	5
	.word	.L118,.L341
	.byte	6
	.byte	'wdt',0,1,163,1,23
	.word	.L275,.L344
	.byte	6
	.byte	'wdt_con0',0,1,166,1,23
	.word	.L278,.L345
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_abbrev'
.L208:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_line'
.L209:
	.word	.L723-.L722
.L722:
	.half	3
	.word	.L725-.L724
.L724:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L725:
	.byte	5,40,7,0,5,2
	.word	.L118
	.byte	3,162,1,1,5,27,9
	.half	.L726-.L118
	.byte	3,4,1,5,5,9
	.half	.L431-.L726
	.byte	3,2,1,5,28,7,9
	.half	.L727-.L431
	.byte	3,3,1,9
	.half	.L728-.L727
	.byte	3,1,1,9
	.half	.L729-.L728
	.byte	3,1,1,5,21,9
	.half	.L730-.L729
	.byte	3,3,1,5,24,9
	.half	.L13-.L730
	.byte	3,4,1,9
	.half	.L731-.L13
	.byte	3,1,1,9
	.half	.L732-.L731
	.byte	3,1,1,9
	.half	.L733-.L732
	.byte	3,1,1,5,36,9
	.half	.L734-.L733
	.byte	3,3,1,5,23,9
	.half	.L14-.L734
	.byte	1,5,36,9
	.half	.L735-.L14
	.byte	1,5,1,7,9
	.half	.L736-.L735
	.byte	3,2,1,7,9
	.half	.L211-.L736
	.byte	0,1,1
.L723:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_ranges'
.L210:
	.word	-1,.L118,0,.L211-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_info'
.L212:
	.word	493
	.half	3
	.word	.L213
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L215,.L214
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_disableCpuWatchdog',0,1,204,1,6,1,1,1
	.word	.L124,.L346,.L123
	.byte	4
	.byte	'password',0,1,204,1,42
	.word	.L283,.L347
	.byte	5
	.word	.L124,.L346
	.byte	6
	.byte	'coreId',0,1,207,1,21
	.word	.L331,.L348
	.byte	6
	.byte	'wdt',0,1,208,1,21
	.word	.L266,.L349
	.byte	7
	.word	.L285,.L124,.L23
	.byte	8
	.word	.L286,.L124,.L23
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L350
	.byte	0,0,7
	.word	.L289,.L351,.L352
	.byte	9
	.word	.L292,.L353
	.byte	9
	.word	.L294,.L354
	.byte	10
	.word	.L296,.L351,.L352
	.byte	0,7
	.word	.L307,.L355,.L356
	.byte	9
	.word	.L310,.L357
	.byte	9
	.word	.L312,.L358
	.byte	10
	.word	.L314,.L355,.L356
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_abbrev'
.L213:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_line'
.L214:
	.word	.L738-.L737
.L737:
	.half	3
	.word	.L740-.L739
.L739:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L740:
	.byte	4,2,5,19,7,0,5,2
	.word	.L124
	.byte	3,143,6,1,5,37,9
	.half	.L437-.L124
	.byte	3,1,1,5,5,9
	.half	.L438-.L437
	.byte	1,4,1,5,41,9
	.half	.L23-.L438
	.byte	3,191,123,1,5,48,9
	.half	.L741-.L23
	.byte	1,4,3,5,25,9
	.half	.L351-.L741
	.byte	3,231,1,1,5,5,9
	.half	.L742-.L351
	.byte	1,5,38,7,9
	.half	.L743-.L742
	.byte	3,5,1,5,45,9
	.half	.L744-.L743
	.byte	3,1,1,5,50,9
	.half	.L745-.L744
	.byte	1,5,69,9
	.half	.L746-.L745
	.byte	3,127,1,5,26,9
	.half	.L747-.L746
	.byte	3,126,1,5,34,9
	.half	.L24-.L747
	.byte	3,9,1,5,41,9
	.half	.L748-.L24
	.byte	3,1,1,5,46,9
	.half	.L749-.L748
	.byte	1,5,65,9
	.half	.L750-.L749
	.byte	3,127,1,5,22,9
	.half	.L751-.L750
	.byte	3,126,1,5,41,9
	.half	.L752-.L751
	.byte	3,6,1,5,28,9
	.half	.L25-.L752
	.byte	1,5,41,9
	.half	.L753-.L25
	.byte	1,4,1,5,16,7,9
	.half	.L352-.L753
	.byte	3,140,126,1,5,20,9
	.half	.L754-.L352
	.byte	1,4,3,5,25,9
	.half	.L355-.L754
	.byte	3,187,2,1,5,5,9
	.half	.L755-.L355
	.byte	1,5,38,7,9
	.half	.L756-.L755
	.byte	3,5,1,5,45,9
	.half	.L757-.L756
	.byte	3,1,1,5,50,9
	.half	.L758-.L757
	.byte	1,5,69,9
	.half	.L759-.L758
	.byte	3,127,1,5,26,9
	.half	.L760-.L759
	.byte	3,126,1,5,34,9
	.half	.L27-.L760
	.byte	3,9,1,5,41,9
	.half	.L761-.L27
	.byte	3,1,1,5,46,9
	.half	.L762-.L761
	.byte	1,5,65,9
	.half	.L763-.L762
	.byte	3,127,1,5,22,9
	.half	.L764-.L763
	.byte	3,126,1,5,41,9
	.half	.L765-.L764
	.byte	3,6,1,5,28,9
	.half	.L28-.L765
	.byte	1,5,41,9
	.half	.L766-.L28
	.byte	1,4,1,5,1,7,9
	.half	.L356-.L766
	.byte	3,183,125,1,7,9
	.half	.L216-.L356
	.byte	0,1,1
.L738:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_ranges'
.L215:
	.word	-1,.L124,0,.L216-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_info'
.L217:
	.word	396
	.half	3
	.word	.L218
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L220,.L219
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_disableSafetyWatchdog',0,1,216,1,6,1,1,1
	.word	.L126,.L359,.L125
	.byte	4
	.byte	'password',0,1,216,1,45
	.word	.L283,.L360
	.byte	5
	.word	.L126,.L359
	.byte	6
	.word	.L299,.L126,.L361
	.byte	7
	.word	.L301,.L362
	.byte	8
	.word	.L303,.L126,.L361
	.byte	0,6
	.word	.L317,.L363,.L364
	.byte	7
	.word	.L319,.L365
	.byte	8
	.word	.L321,.L363,.L364
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_abbrev'
.L218:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_line'
.L219:
	.word	.L768-.L767
.L767:
	.half	3
	.word	.L770-.L769
.L769:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L770:
	.byte	4,2,5,24,7,0,5,2
	.word	.L126
	.byte	3,205,3,1,5,5,9
	.half	.L771-.L126
	.byte	1,5,37,7,9
	.half	.L772-.L771
	.byte	3,5,1,5,43,9
	.half	.L773-.L772
	.byte	3,1,1,5,48,9
	.half	.L774-.L773
	.byte	1,5,66,9
	.half	.L775-.L774
	.byte	3,127,1,5,25,9
	.half	.L776-.L775
	.byte	3,126,1,5,33,9
	.half	.L30-.L776
	.byte	3,9,1,5,39,9
	.half	.L777-.L30
	.byte	3,1,1,5,44,9
	.half	.L778-.L777
	.byte	1,5,62,9
	.half	.L779-.L778
	.byte	3,127,1,5,21,9
	.half	.L780-.L779
	.byte	3,126,1,5,40,9
	.half	.L781-.L780
	.byte	3,6,1,5,27,9
	.half	.L31-.L781
	.byte	1,5,40,9
	.half	.L782-.L31
	.byte	1,4,1,5,20,7,9
	.half	.L361-.L782
	.byte	3,253,125,1,5,24,9
	.half	.L783-.L361
	.byte	1,4,2,9
	.half	.L363-.L783
	.byte	3,202,2,1,5,5,9
	.half	.L784-.L363
	.byte	1,5,37,7,9
	.half	.L785-.L784
	.byte	3,5,1,5,43,9
	.half	.L786-.L785
	.byte	3,1,1,5,48,9
	.half	.L787-.L786
	.byte	1,5,66,9
	.half	.L788-.L787
	.byte	3,127,1,5,25,9
	.half	.L789-.L788
	.byte	3,126,1,5,33,9
	.half	.L33-.L789
	.byte	3,9,1,5,39,9
	.half	.L790-.L33
	.byte	3,1,1,5,44,9
	.half	.L791-.L790
	.byte	1,5,62,9
	.half	.L792-.L791
	.byte	3,127,1,5,21,9
	.half	.L793-.L792
	.byte	3,126,1,5,40,9
	.half	.L794-.L793
	.byte	3,6,1,5,27,9
	.half	.L34-.L794
	.byte	1,5,40,9
	.half	.L795-.L34
	.byte	1,4,1,5,1,7,9
	.half	.L364-.L795
	.byte	3,168,125,1,7,9
	.half	.L221-.L364
	.byte	0,1,1
.L768:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_ranges'
.L220:
	.word	-1,.L126,0,.L221-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_info'
.L222:
	.word	492
	.half	3
	.word	.L223
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L225,.L224
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_enableCpuWatchdog',0,1,224,1,6,1,1,1
	.word	.L128,.L366,.L127
	.byte	4
	.byte	'password',0,1,224,1,41
	.word	.L283,.L367
	.byte	5
	.word	.L128,.L366
	.byte	6
	.byte	'coreId',0,1,227,1,21
	.word	.L331,.L368
	.byte	6
	.byte	'wdt',0,1,228,1,21
	.word	.L266,.L369
	.byte	7
	.word	.L285,.L128,.L36
	.byte	8
	.word	.L286,.L128,.L36
	.byte	6
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L370
	.byte	0,0,7
	.word	.L289,.L371,.L372
	.byte	9
	.word	.L292,.L373
	.byte	9
	.word	.L294,.L374
	.byte	10
	.word	.L296,.L371,.L372
	.byte	0,7
	.word	.L307,.L375,.L376
	.byte	9
	.word	.L310,.L377
	.byte	9
	.word	.L312,.L378
	.byte	10
	.word	.L314,.L375,.L376
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_abbrev'
.L223:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_line'
.L224:
	.word	.L797-.L796
.L796:
	.half	3
	.word	.L799-.L798
.L798:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L799:
	.byte	4,2,5,19,7,0,5,2
	.word	.L128
	.byte	3,143,6,1,5,37,9
	.half	.L441-.L128
	.byte	3,1,1,5,5,9
	.half	.L442-.L441
	.byte	1,4,1,5,41,9
	.half	.L36-.L442
	.byte	3,211,123,1,5,48,9
	.half	.L800-.L36
	.byte	1,4,3,5,25,9
	.half	.L371-.L800
	.byte	3,211,1,1,5,5,9
	.half	.L801-.L371
	.byte	1,5,38,7,9
	.half	.L802-.L801
	.byte	3,5,1,5,45,9
	.half	.L803-.L802
	.byte	3,1,1,5,50,9
	.half	.L804-.L803
	.byte	1,5,69,9
	.half	.L805-.L804
	.byte	3,127,1,5,26,9
	.half	.L806-.L805
	.byte	3,126,1,5,34,9
	.half	.L37-.L806
	.byte	3,9,1,5,41,9
	.half	.L807-.L37
	.byte	3,1,1,5,46,9
	.half	.L808-.L807
	.byte	1,5,65,9
	.half	.L809-.L808
	.byte	3,127,1,5,22,9
	.half	.L810-.L809
	.byte	3,126,1,5,41,9
	.half	.L811-.L810
	.byte	3,6,1,5,28,9
	.half	.L38-.L811
	.byte	1,5,41,9
	.half	.L812-.L38
	.byte	1,4,1,5,16,7,9
	.half	.L372-.L812
	.byte	3,160,126,1,5,20,9
	.half	.L813-.L372
	.byte	1,4,3,5,25,9
	.half	.L375-.L813
	.byte	3,167,2,1,5,5,9
	.half	.L814-.L375
	.byte	1,5,38,7,9
	.half	.L815-.L814
	.byte	3,5,1,5,45,9
	.half	.L816-.L815
	.byte	3,1,1,5,50,9
	.half	.L817-.L816
	.byte	1,5,69,9
	.half	.L818-.L817
	.byte	3,127,1,5,26,9
	.half	.L819-.L818
	.byte	3,126,1,5,34,9
	.half	.L40-.L819
	.byte	3,9,1,5,41,9
	.half	.L820-.L40
	.byte	3,1,1,5,46,9
	.half	.L821-.L820
	.byte	1,5,65,9
	.half	.L822-.L821
	.byte	3,127,1,5,22,9
	.half	.L823-.L822
	.byte	3,126,1,5,41,9
	.half	.L824-.L823
	.byte	3,6,1,5,28,9
	.half	.L41-.L824
	.byte	1,5,41,9
	.half	.L825-.L41
	.byte	1,4,1,5,1,7,9
	.half	.L376-.L825
	.byte	3,203,125,1,7,9
	.half	.L226-.L376
	.byte	0,1,1
.L797:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_ranges'
.L225:
	.word	-1,.L128,0,.L226-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_info'
.L227:
	.word	395
	.half	3
	.word	.L228
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L230,.L229
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_enableSafetyWatchdog',0,1,236,1,6,1,1,1
	.word	.L130,.L379,.L129
	.byte	4
	.byte	'password',0,1,236,1,44
	.word	.L283,.L380
	.byte	5
	.word	.L130,.L379
	.byte	6
	.word	.L299,.L130,.L381
	.byte	7
	.word	.L301,.L382
	.byte	8
	.word	.L303,.L130,.L381
	.byte	0,6
	.word	.L317,.L383,.L384
	.byte	7
	.word	.L319,.L385
	.byte	8
	.word	.L321,.L383,.L384
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_abbrev'
.L228:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_line'
.L229:
	.word	.L827-.L826
.L826:
	.half	3
	.word	.L829-.L828
.L828:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L829:
	.byte	4,2,5,24,7,0,5,2
	.word	.L130
	.byte	3,205,3,1,5,5,9
	.half	.L830-.L130
	.byte	1,5,37,7,9
	.half	.L831-.L830
	.byte	3,5,1,5,43,9
	.half	.L832-.L831
	.byte	3,1,1,5,48,9
	.half	.L833-.L832
	.byte	1,5,66,9
	.half	.L834-.L833
	.byte	3,127,1,5,25,9
	.half	.L835-.L834
	.byte	3,126,1,5,33,9
	.half	.L43-.L835
	.byte	3,9,1,5,39,9
	.half	.L836-.L43
	.byte	3,1,1,5,44,9
	.half	.L837-.L836
	.byte	1,5,62,9
	.half	.L838-.L837
	.byte	3,127,1,5,21,9
	.half	.L839-.L838
	.byte	3,126,1,5,40,9
	.half	.L840-.L839
	.byte	3,6,1,5,27,9
	.half	.L44-.L840
	.byte	1,5,40,9
	.half	.L841-.L44
	.byte	1,4,1,5,20,7,9
	.half	.L381-.L841
	.byte	3,145,126,1,5,24,9
	.half	.L842-.L381
	.byte	1,4,2,9
	.half	.L383-.L842
	.byte	3,182,2,1,5,5,9
	.half	.L843-.L383
	.byte	1,5,37,7,9
	.half	.L844-.L843
	.byte	3,5,1,5,43,9
	.half	.L845-.L844
	.byte	3,1,1,5,48,9
	.half	.L846-.L845
	.byte	1,5,66,9
	.half	.L847-.L846
	.byte	3,127,1,5,25,9
	.half	.L848-.L847
	.byte	3,126,1,5,33,9
	.half	.L46-.L848
	.byte	3,9,1,5,39,9
	.half	.L849-.L46
	.byte	3,1,1,5,44,9
	.half	.L850-.L849
	.byte	1,5,62,9
	.half	.L851-.L850
	.byte	3,127,1,5,21,9
	.half	.L852-.L851
	.byte	3,126,1,5,40,9
	.half	.L853-.L852
	.byte	3,6,1,5,27,9
	.half	.L47-.L853
	.byte	1,5,40,9
	.half	.L854-.L47
	.byte	1,4,1,5,1,7,9
	.half	.L384-.L854
	.byte	3,188,125,1,7,9
	.half	.L231-.L384
	.byte	0,1,1
.L827:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_ranges'
.L230:
	.word	-1,.L130,0,.L231-.L130,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_info'
.L232:
	.word	411
	.half	3
	.word	.L233
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L235,.L234
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,1,244,1,8
	.word	.L283
	.byte	1,1,1
	.word	.L132,.L386,.L131
	.byte	4
	.word	.L132,.L386
	.byte	5
	.word	.L285,.L132,.L49
	.byte	6
	.word	.L286,.L132,.L49
	.byte	7
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L387
	.byte	0,0,5
	.word	.L388,.L389,.L50
	.byte	8
	.word	.L390,.L391
	.byte	6
	.word	.L392,.L389,.L50
	.byte	7
	.byte	'password',0,3,229,3,12
	.word	.L283,.L393
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_abbrev'
.L233:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,11,1,49,16,17,1
	.byte	18,1,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,5,0,49,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_line'
.L234:
	.word	.L856-.L855
.L855:
	.half	3
	.word	.L858-.L857
.L857:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L858:
	.byte	4,2,5,19,7,0,5,2
	.word	.L132
	.byte	3,143,6,1,5,37,9
	.half	.L444-.L132
	.byte	3,1,1,5,5,9
	.half	.L445-.L444
	.byte	1,4,1,5,69,9
	.half	.L49-.L445
	.byte	3,229,123,1,4,3,5,33,9
	.half	.L389-.L49
	.byte	3,244,1,1,5,14,9
	.half	.L446-.L389
	.byte	3,1,1,5,5,9
	.half	.L447-.L446
	.byte	3,2,1,4,1,9
	.half	.L50-.L447
	.byte	3,137,126,1,5,1,9
	.half	.L51-.L50
	.byte	3,1,1,7,9
	.half	.L236-.L51
	.byte	0,1,1
.L856:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_ranges'
.L235:
	.word	-1,.L132,0,.L236-.L132,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_info'
.L237:
	.word	387
	.half	3
	.word	.L238
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L240,.L239
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_getCpuWatchdogEndInit',0,1,250,1,9
	.word	.L394
	.byte	1,1,1
	.word	.L134,.L395,.L133
	.byte	4
	.word	.L134,.L395
	.byte	5
	.word	.L285,.L134,.L52
	.byte	6
	.word	.L286,.L134,.L52
	.byte	7
	.byte	'reg',0,2,143,6,21
	.word	.L287,.L396
	.byte	0,0,5
	.word	.L397,.L398,.L53
	.byte	8
	.word	.L399,.L400
	.byte	9
	.word	.L401,.L398,.L53
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_abbrev'
.L238:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,11,1,49,16,17,1
	.byte	18,1,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_line'
.L239:
	.word	.L860-.L859
.L859:
	.half	3
	.word	.L862-.L861
.L861:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L862:
	.byte	4,2,5,19,7,0,5,2
	.word	.L134
	.byte	3,143,6,1,5,37,9
	.half	.L448-.L134
	.byte	3,1,1,5,5,9
	.half	.L449-.L448
	.byte	1,4,1,5,77,9
	.half	.L52-.L449
	.byte	3,235,123,1,4,3,5,37,9
	.half	.L398-.L52
	.byte	3,247,1,1,5,5,9
	.half	.L863-.L398
	.byte	1,4,1,9
	.half	.L53-.L863
	.byte	3,137,126,1,5,1,9
	.half	.L54-.L53
	.byte	3,1,1,7,9
	.half	.L241-.L54
	.byte	0,1,1
.L860:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_ranges'
.L240:
	.word	-1,.L134,0,.L241-.L134,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_info'
.L242:
	.word	382
	.half	3
	.word	.L243
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L245,.L244
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,1,128,2,8
	.word	.L283
	.byte	1,1,1
	.word	.L136,.L402,.L135
	.byte	4
	.word	.L136,.L402
	.byte	5
	.word	.L403,.L136,.L55
	.byte	6
	.word	.L404,.L136,.L55
	.byte	7
	.byte	'password',0,2,255,3,19
	.word	.L283,.L405
	.byte	7
	.byte	'watchdog',0,2,128,4,19
	.word	.L275,.L406
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_abbrev'
.L243:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,29,1,49,16,17,1,18,1,0,0,6,11,1,49,16,17,1
	.byte	18,1,0,0,7,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_line'
.L244:
	.word	.L865-.L864
.L864:
	.half	3
	.word	.L867-.L866
.L866:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Scu\\Std\\IfxScuWdt.h',0,0,0,0,0
.L867:
	.byte	4,2,5,41,7,0,5,2
	.word	.L136
	.byte	3,255,3,1,5,33,9
	.half	.L868-.L136
	.byte	3,5,1,5,14,9
	.half	.L451-.L868
	.byte	3,1,1,5,5,9
	.half	.L452-.L451
	.byte	3,2,1,4,1,9
	.half	.L55-.L452
	.byte	3,250,125,1,5,1,9
	.half	.L56-.L55
	.byte	3,1,1,7,9
	.half	.L246-.L56
	.byte	0,1,1
.L865:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_ranges'
.L245:
	.word	-1,.L136,0,.L246-.L136,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_info'
.L247:
	.word	320
	.half	3
	.word	.L248
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L250,.L249
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_serviceCpuWatchdog',0,1,153,3,6,1,1,1
	.word	.L144,.L407,.L143
	.byte	4
	.byte	'password',0,1,153,3,42
	.word	.L283,.L408
	.byte	5
	.word	.L144,.L407
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_abbrev'
.L248:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_line'
.L249:
	.word	.L870-.L869
.L869:
	.half	3
	.word	.L872-.L871
.L871:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L872:
	.byte	5,29,7,0,5,2
	.word	.L144
	.byte	3,154,3,1,5,1,9
	.half	.L467-.L144
	.byte	3,1,1,7,9
	.half	.L251-.L467
	.byte	0,1,1
.L870:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_ranges'
.L250:
	.word	-1,.L144,0,.L251-.L144,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_info'
.L252:
	.word	323
	.half	3
	.word	.L253
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L255,.L254
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_serviceSafetyWatchdog',0,1,159,3,6,1,1,1
	.word	.L146,.L409,.L145
	.byte	4
	.byte	'password',0,1,159,3,45
	.word	.L283,.L410
	.byte	5
	.word	.L146,.L409
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_abbrev'
.L253:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_line'
.L254:
	.word	.L874-.L873
.L873:
	.half	3
	.word	.L876-.L875
.L875:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L876:
	.byte	5,32,7,0,5,2
	.word	.L146
	.byte	3,160,3,1,5,1,9
	.half	.L468-.L146
	.byte	3,1,1,7,9
	.half	.L256-.L468
	.byte	0,1,1
.L874:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_ranges'
.L255:
	.word	-1,.L146,0,.L256-.L146,0,0
	.sdecl	'.debug_info',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_info'
.L257:
	.word	475
	.half	3
	.word	.L258
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L260,.L259
	.byte	2
	.word	.L153
	.byte	3
	.byte	'IfxScuWdt_enableWatchdogWithDebugger',0,1,177,3,9
	.word	.L394
	.byte	1,1,1
	.word	.L152,.L411,.L151
	.byte	4
	.word	.L152,.L411
	.byte	5
	.byte	'status',0,1,179,3,22
	.word	.L394,.L412
	.byte	5
	.byte	'oenEnabled',0,1,179,3,34
	.word	.L394,.L413
	.byte	5
	.byte	'watchdogEnabled',0,1,179,3,50
	.word	.L394,.L414
	.byte	5
	.byte	'ostateValue',0,1,180,3,22
	.word	.L331,.L415
	.byte	5
	.byte	'oecPtr',0,1,182,3,22
	.word	.L416,.L417
	.byte	5
	.byte	'ostatePtr',0,1,183,3,22
	.word	.L416,.L418
	.byte	5
	.byte	'ocntrlPtr',0,1,184,3,22
	.word	.L416,.L419
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_abbrev'
.L258:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_line'
.L259:
	.word	.L878-.L877
.L877:
	.half	3
	.word	.L880-.L879
.L879:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Scu/Std/IfxScuWdt.c',0,0,0,0,0
.L880:
	.byte	5,29,7,0,5,2
	.word	.L152
	.byte	3,178,3,1,5,34,9
	.half	.L473-.L152
	.byte	3,3,1,9
	.half	.L474-.L473
	.byte	3,1,1,9
	.half	.L475-.L474
	.byte	3,1,1,5,19,9
	.half	.L476-.L475
	.byte	3,3,1,5,32,9
	.half	.L477-.L476
	.byte	3,1,1,5,10,9
	.half	.L479-.L477
	.byte	3,2,1,5,19,7,9
	.half	.L881-.L479
	.byte	3,4,1,5,17,9
	.half	.L478-.L881
	.byte	1,5,19,9
	.half	.L882-.L478
	.byte	3,1,1,5,17,9
	.half	.L883-.L882
	.byte	1,5,19,9
	.half	.L884-.L883
	.byte	3,1,1,5,17,9
	.half	.L885-.L884
	.byte	1,5,19,9
	.half	.L886-.L885
	.byte	3,1,1,5,17,9
	.half	.L887-.L886
	.byte	1,5,23,9
	.half	.L888-.L887
	.byte	3,3,1,5,36,9
	.half	.L480-.L888
	.byte	3,1,1,5,5,9
	.half	.L106-.L480
	.byte	3,3,1,5,22,7,9
	.half	.L889-.L106
	.byte	3,3,1,5,20,9
	.half	.L481-.L889
	.byte	1,5,32,9
	.half	.L890-.L481
	.byte	1,5,16,9
	.half	.L107-.L890
	.byte	3,4,1,5,23,9
	.half	.L108-.L107
	.byte	3,6,1,5,36,9
	.half	.L482-.L108
	.byte	3,1,1,5,10,9
	.half	.L483-.L482
	.byte	3,2,1,5,16,7,9
	.half	.L891-.L483
	.byte	3,2,1,5,5,9
	.half	.L109-.L891
	.byte	3,5,1,5,1,9
	.half	.L110-.L109
	.byte	3,1,1,7,9
	.half	.L261-.L110
	.byte	0,1,1
.L878:
	.sdecl	'.debug_ranges',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_ranges'
.L260:
	.word	-1,.L152,0,.L261-.L152,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L322-.L112
	.half	2
	.byte	138,0
	.word	0,0
.L324:
	.word	-1,.L112,0,.L322-.L112
	.half	1
	.byte	85
	.word	0,0
.L323:
	.word	-1,.L112,0,.L322-.L112
	.half	1
	.byte	84
	.word	0,0
.L327:
	.word	-1,.L112,.L420-.L112,.L421-.L112
	.half	1
	.byte	95
	.word	0,0
.L325:
	.word	-1,.L112,.L422-.L112,.L322-.L112
	.half	1
	.byte	111
	.word	0,0
.L326:
	.word	-1,.L112,.L423-.L112,.L4-.L112
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_loc'
.L113:
	.word	-1,.L114,0,.L328-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L332:
	.word	0,0
.L329:
	.word	-1,.L114,0,.L328-.L114
	.half	1
	.byte	84
	.word	0,0
.L335:
	.word	-1,.L114,.L424-.L114,.L425-.L114
	.half	1
	.byte	95
	.word	0,0
.L330:
	.word	-1,.L114,0,.L328-.L114
	.half	1
	.byte	85
	.word	0,0
.L333:
	.word	-1,.L114,.L426-.L114,.L328-.L114
	.half	1
	.byte	111
	.word	0,0
.L334:
	.word	-1,.L114,.L427-.L114,.L8-.L114
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L336-.L116
	.half	2
	.byte	138,0
	.word	0,0
.L338:
	.word	-1,.L116,0,.L336-.L116
	.half	1
	.byte	85
	.word	0,0
.L337:
	.word	-1,.L116,0,.L336-.L116
	.half	1
	.byte	84
	.word	0,0
.L339:
	.word	-1,.L116,.L428-.L116,.L336-.L116
	.half	1
	.byte	111
	.word	0,0
.L340:
	.word	-1,.L116,.L429-.L116,.L11-.L116
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L341-.L118
	.half	2
	.byte	138,0
	.word	0,0
.L342:
	.word	-1,.L118,0,.L341-.L118
	.half	1
	.byte	84
	.word	0,0
.L343:
	.word	-1,.L118,0,.L341-.L118
	.half	1
	.byte	85
	.word	0,0
.L344:
	.word	-1,.L118,.L430-.L118,.L341-.L118
	.half	1
	.byte	111
	.word	0,0
.L345:
	.word	-1,.L118,.L431-.L118,.L14-.L118
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L120,0,.L282-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L284:
	.word	-1,.L120,0,.L432-.L120
	.half	1
	.byte	84
	.word	0,0
.L295:
	.word	0,0
.L288:
	.word	-1,.L120,.L433-.L120,.L434-.L120
	.half	1
	.byte	95
	.word	0,0
.L293:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L122,0,.L297-.L122
	.half	2
	.byte	138,0
	.word	0,0
.L298:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	84
	.word	0,0
.L302:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_loc'
.L123:
	.word	-1,.L124,0,.L346-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L348:
	.word	0,0
.L347:
	.word	-1,.L124,0,.L436-.L124
	.half	1
	.byte	84
	.word	0,0
.L354:
	.word	0,0
.L358:
	.word	0,0
.L350:
	.word	-1,.L124,.L437-.L124,.L438-.L124
	.half	1
	.byte	95
	.word	0,0
.L353:
	.word	0,0
.L357:
	.word	0,0
.L349:
	.word	-1,.L124,.L351-.L124,.L346-.L124
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_loc'
.L125:
	.word	-1,.L126,0,.L359-.L126
	.half	2
	.byte	138,0
	.word	0,0
.L360:
	.word	-1,.L126,0,.L439-.L126
	.half	1
	.byte	84
	.word	0,0
.L362:
	.word	0,0
.L365:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_loc'
.L127:
	.word	-1,.L128,0,.L366-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L368:
	.word	0,0
.L367:
	.word	-1,.L128,0,.L440-.L128
	.half	1
	.byte	84
	.word	0,0
.L374:
	.word	0,0
.L378:
	.word	0,0
.L370:
	.word	-1,.L128,.L441-.L128,.L442-.L128
	.half	1
	.byte	95
	.word	0,0
.L373:
	.word	0,0
.L377:
	.word	0,0
.L369:
	.word	-1,.L128,.L371-.L128,.L366-.L128
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_loc'
.L129:
	.word	-1,.L130,0,.L379-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L380:
	.word	-1,.L130,0,.L443-.L130
	.half	1
	.byte	84
	.word	0,0
.L382:
	.word	0,0
.L385:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_loc'
.L151:
	.word	-1,.L152,0,.L411-.L152
	.half	2
	.byte	138,0
	.word	0,0
.L419:
	.word	-1,.L152,.L476-.L152,.L411-.L152
	.half	1
	.byte	100
	.word	0,0
.L417:
	.word	-1,.L152,.L474-.L152,.L411-.L152
	.half	1
	.byte	111
	.word	0,0
.L413:
	.word	-1,.L152,.L477-.L152,.L478-.L152
	.half	1
	.byte	95
	.word	.L480-.L152,.L481-.L152
	.half	1
	.byte	95
	.word	.L107-.L152,.L108-.L152
	.half	1
	.byte	95
	.word	0,0
.L418:
	.word	-1,.L152,.L475-.L152,.L411-.L152
	.half	1
	.byte	98
	.word	0,0
.L415:
	.word	-1,.L152,.L477-.L152,.L479-.L152
	.half	1
	.byte	95
	.word	.L480-.L152,.L106-.L152
	.half	1
	.byte	95
	.word	.L482-.L152,.L483-.L152
	.half	1
	.byte	95
	.word	0,0
.L412:
	.word	-1,.L152,.L473-.L152,.L411-.L152
	.half	1
	.byte	82
	.word	0,0
.L414:
	.word	-1,.L152,.L482-.L152,.L411-.L152
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L134,0,.L395-.L134
	.half	2
	.byte	138,0
	.word	0,0
.L396:
	.word	-1,.L134,.L448-.L134,.L449-.L134
	.half	1
	.byte	95
	.word	0,0
.L400:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L132,0,.L386-.L132
	.half	2
	.byte	138,0
	.word	0,0
.L393:
	.word	-1,.L132,.L446-.L132,.L447-.L132
	.half	1
	.byte	95
	.word	.L447-.L132,.L386-.L132
	.half	1
	.byte	82
	.word	0,0
.L387:
	.word	-1,.L132,.L444-.L132,.L445-.L132
	.half	1
	.byte	95
	.word	0,0
.L391:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_loc'
.L135:
	.word	-1,.L136,0,.L402-.L136
	.half	2
	.byte	138,0
	.word	0,0
.L405:
	.word	-1,.L136,.L451-.L136,.L452-.L136
	.half	1
	.byte	95
	.word	.L452-.L136,.L402-.L136
	.half	1
	.byte	82
	.word	0,0
.L406:
	.word	-1,.L136,.L450-.L136,.L402-.L136
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_loc'
.L137:
	.word	-1,.L138,0,.L262-.L138
	.half	2
	.byte	138,0
	.word	0,0
.L264:
	.word	-1,.L138,0,.L262-.L138
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_loc'
.L139:
	.word	-1,.L140,0,.L265-.L140
	.half	2
	.byte	138,0
	.word	0,0
.L269:
	.word	-1,.L140,0,.L453-.L140
	.half	1
	.byte	101
	.word	0,0
.L267:
	.word	-1,.L140,0,.L453-.L140
	.half	1
	.byte	100
	.word	0,0
.L271:
	.word	-1,.L140,.L454-.L140,.L455-.L140
	.half	1
	.byte	95
	.word	.L455-.L140,.L457-.L140
	.half	5
	.byte	144,32,157,32,0
	.word	.L457-.L140,.L458-.L140
	.half	1
	.byte	95
	.word	.L458-.L140,.L459-.L140
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L273:
	.word	-1,.L140,.L456-.L140,.L453-.L140
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_loc'
.L141:
	.word	-1,.L142,0,.L274-.L142
	.half	2
	.byte	138,0
	.word	0,0
.L277:
	.word	-1,.L142,0,.L460-.L142
	.half	1
	.byte	101
	.word	0,0
.L276:
	.word	-1,.L142,0,.L460-.L142
	.half	1
	.byte	100
	.word	0,0
.L279:
	.word	-1,.L142,.L461-.L142,.L462-.L142
	.half	1
	.byte	95
	.word	.L462-.L142,.L464-.L142
	.half	5
	.byte	144,32,157,32,0
	.word	.L464-.L142,.L465-.L142
	.half	1
	.byte	95
	.word	.L465-.L142,.L466-.L142
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L281:
	.word	-1,.L142,.L463-.L142,.L460-.L142
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_loc'
.L143:
	.word	-1,.L144,0,.L407-.L144
	.half	2
	.byte	138,0
	.word	0,0
.L408:
	.word	-1,.L144,0,.L467-.L144
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_loc'
.L145:
	.word	-1,.L146,0,.L409-.L146
	.half	2
	.byte	138,0
	.word	0,0
.L410:
	.word	-1,.L146,0,.L468-.L146
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_loc'
.L147:
	.word	-1,.L148,0,.L304-.L148
	.half	2
	.byte	138,0
	.word	0,0
.L305:
	.word	-1,.L148,0,.L469-.L148
	.half	1
	.byte	84
	.word	0,0
.L313:
	.word	0,0
.L306:
	.word	-1,.L148,.L470-.L148,.L471-.L148
	.half	1
	.byte	95
	.word	0,0
.L311:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_loc'
.L149:
	.word	-1,.L150,0,.L315-.L150
	.half	2
	.byte	138,0
	.word	0,0
.L316:
	.word	-1,.L150,0,.L472-.L150
	.half	1
	.byte	84
	.word	0,0
.L320:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L892:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_changeCpuWatchdogPassword')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L112,.L322-.L112
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_changeCpuWatchdogReload')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L114,.L328-.L114
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_changeSafetyWatchdogPassword')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L116,.L336-.L116
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_changeSafetyWatchdogReload')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L118,.L341-.L118
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_clearCpuEndinit')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L120,.L282-.L120
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_clearSafetyEndinit')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L122,.L297-.L122
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_disableCpuWatchdog')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L124,.L346-.L124
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_disableSafetyWatchdog')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L126,.L359-.L126
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_enableCpuWatchdog')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L128,.L366-.L128
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_enableSafetyWatchdog')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L130,.L379-.L130
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_getCpuWatchdogPassword')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L132,.L386-.L132
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_getCpuWatchdogEndInit')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L134,.L395-.L134
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_getSafetyWatchdogPassword')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L136,.L402-.L136
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_initConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L138,.L262-.L138
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_initCpuWatchdog')
	.sect	'.debug_frame'
	.word	12
	.word	.L892,.L140,.L265-.L140
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_initSafetyWatchdog')
	.sect	'.debug_frame'
	.word	12
	.word	.L892,.L142,.L274-.L142
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_serviceCpuWatchdog')
	.sect	'.debug_frame'
	.word	12
	.word	.L892,.L144,.L407-.L144
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_serviceSafetyWatchdog')
	.sect	'.debug_frame'
	.word	12
	.word	.L892,.L146,.L409-.L146
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_setCpuEndinit')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L148,.L304-.L148
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_setSafetyEndinit')
	.sect	'.debug_frame'
	.word	24
	.word	.L892,.L150,.L315-.L150
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxScuWdt_enableWatchdogWithDebugger')
	.sect	'.debug_frame'
	.word	20
	.word	.L892,.L152,.L411-.L152
	.byte	8,19,8,21,8,22,8,23
	; Module end
