/**
 * \file Ifx_LutLSincosF32.c
 * \brief Table data for linear interpolation Sin/Cos lookup functions
 *
 *
 * \version disabled
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * $Revision: 614 $
 * $Date: 2013-03-04 14:09:48 +0100 (Mon, 04 Mar 2013) $
 *
 */

#include "Ifx_Lut.h"
#include "Ifx_LutIndexedLinearF32.h"

#if IFX_LUT_TABLE_CONST == 0
/* FIXME Table size not consistent  */
IFX_LUT_TABLE float32                   Ifx_g_LutLSincosF32_table[IFX_LUT_ANGLE_RESOLUTION + (IFX_LUT_ANGLE_RESOLUTION / 4)];
IFX_LUT_TABLE Ifx_LutIndexedLinearF32 Ifx_g_LutLSincosF32;
#else

#if (IFX_LUT_ANGLE_RESOLUTION != 4096)
#error "Inconsistent between Lookup Table and configuration. Please regenerate."
#endif

/*lint -e915*/
#define IFX_F32_LUTL_SINCOS_ELEMENT_COUNT (128)

const Ifx_LutIndexedLinearF32_Item Ifx_g_LutLSincosF32_table[128] = {
    {0.00153336482273181,   0                   },
    {0.00152967081256696,   0.000118208325275426},
    {0.00152229169143129,   0.000590472077958315},
    {0.00151124523627395,   0.00165093177306225 },
    {0.00149655805897299,   0.00353089046758576 },
    {0.00147826554222495,   0.00645769314727157 },
    {0.00145641175430493,   0.010653620427916   },
    {0.00143104934290218,   0.0163348005821319  },
    {0.00140223940828726,   0.0237101438435514  },
    {0.00137005135611611,   0.0329803028688421  },
    {0.00133456273022575,   0.0443366631537574  },
    {0.00129585902582439,   0.057960367103036   },
    {0.00125403348352597,   0.074021375345628   },
    {0.00120918686472538,   0.0926775687666757  },
    {0.0011614272088554,    0.114073894596426   },
    {0.00111086957311029,   0.138341559754081   },
    {0.00105763575526286,   0.165597274491961   },
    {0.00100185400024306,   0.195942549222735   },
    {0.00094365869118462,   0.229463047240396   },
    {0.000883190025684388,  0.266227995864537   },
    {0.000820593678053965,  0.306289658348008   },
    {0.000756020448377588,  0.349682868690533   },
    {0.000689625899221512,  0.39642463129641    },
    {0.000621569980870106,  0.446513787203045   },
    {0.00055201664599169,   0.499930748389669   },
    {0.000481133454662129,  0.556637301453318   },
    {0.00040909117069797,   0.616576481711498   },
    {0.000336063350271452,  0.67967251856001    },
    {0.000262225923798456,  0.745830852679814   },
    {0.000187756772106744,  0.814938225449723   },
    {0.000112835297905487,  0.886862840682929   },
    {3.76419935883623E-05,  0.961454598565517   },
    {-3.76419935883623E-05, 1.03854540143448    },
    {-0.000112835297905484, 1.11794953079336    },
    {-0.000187756772106747, 1.19946409472434    },
    {-0.000262225923798456, 1.28286954461905    },
    {-0.000336063350271452, 1.36793025991594    },
    {-0.00040909117069797,  1.45439519930094    },
    {-0.000481133454662129, 1.54199861660136    },
    {-0.00055201664599169,  1.63046083938065    },
    {-0.000621569980870103, 1.71948910802502    },
    {-0.000689625899221512, 1.80877847290207    },
    {-0.000756020448377592, 1.89801274696784    },
    {-0.000820593678053958, 1.98686551100252    },
    {-0.000883190025684391, 2.07500116846617    },
    {-0.00094365869118462,  2.1620760467865     },
    {-0.00100185400024307,  2.24773954172053    },
    {-0.00105763575526286,  2.3316353012703     },
    {-0.00111086957311028,  2.41340244548394    },
    {-0.00116142720885541,  2.4926768183323     },
    {-0.00120918686472538,  2.56909226772425    },
    {-0.00125403348352598,  2.64228194960683    },
    {-0.00129585902582439,  2.71187965199138    },
    {-0.00133456273022575,  2.77752113465609    },
    {-0.00137005135611612,  2.83884548019465    },
    {-0.00140223940828725,  2.89549645201585    },
    {-0.00143104934290217,  2.94712385484579    },
    {-0.00145641175430494,  2.99338489324442    },
    {-0.00147826554222495,  3.03394552362397    },
    {-0.00149655805897298,  3.06848179524426    },
    {-0.00151124523627396,  3.09668117566214    },
    {-0.00152229169143128,  3.11824385612922    },
    {-0.00152967081256696,  3.13288403246242    },
    {-0.00153336482273181,  3.14033115695474    },
    {-0.00153336482273181,  3.14033115695474    },
    {-0.00152967081256696,  3.13264761581187    },
    {-0.00152229169143128,  3.11706291197331    },
    {-0.00151124523627396,  3.09337931211601    },
    {-0.00149655805897298,  3.06142001430908    },
    {-0.00147826554222495,  3.02103013732942    },
    {-0.00145641175430494,  2.9720776523886     },
    {-0.00143104934290217,  2.91445425368152    },
    {-0.00140223940828725,  2.84807616432874    },
    {-0.00137005135611612,  2.77288487445697    },
    {-0.00133456273022575,  2.68884780834857    },
    {-0.00129585902582439,  2.59595891778531    },
    {-0.00125403348352598,  2.49423919891558    },
    {-0.00120918686472538,  2.38373713019089    },
    {-0.00116142720885541,  2.26452902913946    },
    {-0.00111086957311028,  2.13671932597577    },
    {-0.00105763575526286,  2.00044075228637    },
    {-0.00100185400024306,  1.85585444327504    },
    {-0.000943658691184637, 1.70314995230575    },
    {-0.000883190025684381, 1.54254517673707    },
    {-0.000820593678053962, 1.3742861943065     },
    {-0.000756020448377592, 1.19864700958678    },
    {-0.000689625899221506, 1.01592921030923    },
    {-0.000621569980870106, 0.826461533618933   },
    {-0.000552016645991697, 0.630599342601331   },
    {-0.000481133454662126, 0.428724013694713   },
    {-0.000409091170697973, 0.221242235877955   },
    {-0.000336063350271448, 0.00858522279591345 },
    {-0.000262225923798456, -0.208792160740577  },
    {-0.000187756772106751, -0.430412356175091  },
    {-0.000112835297905484, -0.655776150572502  },
    {-3.76419935883623E-05, -0.884363795696551  },
    {3.76419935883623E-05,  -1.11563620430345   },
    {0.000112835297905484,  -1.34903622090379   },
    {0.000187756772106751,  -1.58398996399897   },
    {0.000262225923798452,  -1.81990823655828   },
    {0.000336063350271452,  -2.05618800127188   },
    {0.00040909117069797,   -2.29221391689038   },
    {0.000481133454662126,  -2.52735993174939   },
    {0.000552016645991697,  -2.76099093037165   },
    {0.000621569980870103,  -2.99246442884699   },
    {0.000689625899221509,  -3.22113231450771   },
    {0.000756020448377588,  -3.44634262524513   },
    {0.000820593678053962,  -3.66744136365704   },
    {0.000883190025684381,  -3.88377434106776   },
    {0.000943658691184637,  -4.09468904633266   },
    {0.00100185400024306,   -4.29953653421829   },
    {0.00105763575526286,   -4.49767332804863   },
    {0.00111086957311028,   -4.6884633312138    },
    {0.0011614272088554,    -4.87127974206814   },
    {0.0012091868647254,    -5.04550696668189   },
    {0.00125403348352597,   -5.21054252386801   },
    {0.00129585902582438,   -5.36579893687972   },
    {0.00133456273022575,   -5.51070560615843   },
    {0.00137005135611611,   -5.64471065752041   },
    {0.00140223940828725,   -5.76728276018815   },
    {0.0014310493429022,    -5.87791290910954   },
    {0.00145641175430492,   -5.97611616606088   },
    {0.00147826554222495,   -6.06143335410066   },
    {0.00149655805897298,   -6.13343270002092   },
    {0.00151124523627395,   -6.19171141955115   },
    {0.00152229169143131,   -6.2358972401806    },
    {0.00152967081256695,   -6.2656498565995    },
    {0.00153336482273181,   -6.28066231390948   },
};

const Ifx_LutIndexedLinearF32      Ifx_g_LutLSincosF32 = {
    .segmentCount = IFX_F32_LUTL_SINCOS_ELEMENT_COUNT,
    .shift        = 5,
    .segments     = Ifx_g_LutLSincosF32_table
};
#endif
