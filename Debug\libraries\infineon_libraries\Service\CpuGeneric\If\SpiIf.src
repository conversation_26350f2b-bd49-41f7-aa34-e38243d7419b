	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc20448a --dep-file=SpiIf.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.src ../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c'

	
$TC16X
	
	.sdecl	'.text.SpiIf.SpiIf_initConfig',code,cluster('SpiIf_initConfig')
	.sect	'.text.SpiIf.SpiIf_initConfig'
	.align	2
	
	.global	SpiIf_initConfig
; Function SpiIf_initConfig
.L3:
SpiIf_initConfig:	.type	func
	mov	d15,#0
.L38:
	st.b	[a4],d15
.L39:
	mov	d15,#0
.L40:
	st.h	[a4]2,d15
.L41:
	mov	d15,#0
.L42:
	st.h	[a4]4,d15
.L43:
	mov	d15,#0
.L44:
	st.h	[a4]6,d15
.L45:
	mov	d15,#0
.L46:
	st.b	[a4]8,d15
.L47:
	mov	d15,#0
.L48:
	st.h	[a4]10,d15
.L49:
	mov.a	a15,#0
.L50:
	st.a	[a4]12,a15
.L51:
	mov	d15,#0
.L52:
	st.w	[a4]16,d15
.L53:
	ret
.L20:
	
__SpiIf_initConfig_function_end:
	.size	SpiIf_initConfig,__SpiIf_initConfig_function_end-SpiIf_initConfig
.L14:
	; End of function
	
	.sdecl	'.text.SpiIf.SpiIf_initChannelConfig',code,cluster('SpiIf_initChannelConfig')
	.sect	'.text.SpiIf.SpiIf_initChannelConfig'
	.align	2
	
	.global	SpiIf_initChannelConfig
; Function SpiIf_initChannelConfig
.L5:
SpiIf_initChannelConfig:	.type	func
	st.a	[a4],a5
.L58:
	mov	d15,#0
.L59:
	st.w	[a4]4,d15
.L60:
	ld.bu	d15,[a4]8
.L61:
	or	d15,#1
	st.b	[a4]8,d15
.L62:
	ld.bu	d15,[a4]8
.L63:
	or	d15,#2
	st.b	[a4]8,d15
.L64:
	ld.bu	d15,[a4]8
.L65:
	insert	d15,d15,#0,#2,#1
	st.b	[a4]8,d15
.L66:
	ld.bu	d15,[a4]8
.L67:
	insert	d15,d15,#0,#3,#1
	st.b	[a4]8,d15
.L68:
	ld.bu	d15,[a4]8
.L69:
	insert	d15,d15,#0,#4,#1
	st.b	[a4]8,d15
.L70:
	ld.bu	d15,[a4]8
.L71:
	or	d15,#32
	st.b	[a4]8,d15
.L72:
	ld.hu	d0,[a4]8
.L73:
	mov	d15,#8
.L74:
	insert	d15,d0,d15,#6,#6
	st.h	[a4]8,d15
.L75:
	ld.bu	d15,[a4]9
.L76:
	insert	d15,d15,#0,#4,#1
	st.b	[a4]9,d15
.L77:
	mov	d15,#0
.L78:
	st.w	[a4]14,d15
.L79:
	mov	d15,#0
.L80:
	st.w	[a4]18,d15
.L81:
	mov	d15,#0
.L82:
	st.w	[a4]10,d15
.L83:
	ld.bu	d15,[a4]9
.L84:
	insert	d15,d15,#0,#5,#1
	st.b	[a4]9,d15
.L85:
	ld.bu	d15,[a4]9
.L86:
	insert	d15,d15,#0,#6,#1
	st.b	[a4]9,d15
.L87:
	ld.bu	d15,[a4]24
.L88:
	insert	d15,d15,#0,#0,#1
	st.b	[a4]24,d15
.L89:
	ld.bu	d15,[a4]24
.L90:
	insert	d15,d15,#0,#1,#1
	st.b	[a4]24,d15
.L91:
	ld.bu	d15,[a4]24
.L92:
	insert	d15,d15,#0,#2,#1
	st.b	[a4]24,d15
.L93:
	ld.bu	d15,[a4]24
.L94:
	insert	d15,d15,#0,#3,#1
	st.b	[a4]24,d15
.L95:
	ret
.L23:
	
__SpiIf_initChannelConfig_function_end:
	.size	SpiIf_initChannelConfig,__SpiIf_initChannelConfig_function_end-SpiIf_initChannelConfig
.L19:
	; End of function
	
	.calls	'SpiIf_initConfig','',0
	.calls	'SpiIf_initChannelConfig','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L7:
	.word	9518
	.half	3
	.word	.L8
	.byte	4
.L6:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L9
	.byte	2,1,1,3
	.word	224
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	227
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	272
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	284
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	364
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	338
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	370
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	370
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	338
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	479
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	479
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	479
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	479
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	479
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	479
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	479
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	479
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	479
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	479
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	479
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	479
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	479
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	479
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	479
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	456
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	472
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	496
	.byte	4,2,35,0,0,14
	.word	786
	.byte	3
	.word	825
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	830
	.byte	6,0,15
	.word	232
	.byte	16
	.word	258
	.byte	6,0,15
	.word	293
	.byte	16
	.word	325
	.byte	6,0,15
	.word	375
	.byte	16
	.word	394
	.byte	6,0,15
	.word	410
	.byte	16
	.word	425
	.byte	16
	.word	439
	.byte	6,0,15
	.word	835
	.byte	16
	.word	863
	.byte	6,0,17,5,131,1,9,1,18
	.byte	'SpiIf_Mode_master',0,0,18
	.byte	'SpiIf_Mode_slave',0,1,18
	.byte	'SpiIf_Mode_undefined',0,2,0,7
	.byte	'unsigned short int',0,2,7,17,6,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,7
	.byte	'short int',0,2,5,19,5,163,1,9,20,13
	.byte	'mode',0
	.word	943
	.byte	1,2,35,0,13
	.byte	'rxPriority',0
	.word	1012
	.byte	2,2,35,2,13
	.byte	'txPriority',0
	.word	1012
	.byte	2,2,35,4,13
	.byte	'erPriority',0
	.word	1012
	.byte	2,2,35,6,13
	.byte	'isrProvider',0
	.word	1034
	.byte	1,2,35,8,13
	.byte	'bufferSize',0
	.word	1093
	.byte	2,2,35,10,13
	.byte	'buffer',0
	.word	370
	.byte	4,2,35,12,13
	.byte	'maximumBaudrate',0
	.word	284
	.byte	4,2,35,16,0
.L21:
	.byte	3
	.word	1106
	.byte	20
	.byte	'pvoid',0,7,57,28
	.word	370
	.byte	7
	.byte	'unsigned long int',0,4,7,19,5,118,18,1,11
	.byte	'onTransfer',0,1
	.word	479
	.byte	1,7,2,35,0,11
	.byte	'byteAccess',0,1
	.word	479
	.byte	1,6,2,35,0,0,14
	.word	1309
	.byte	19,5,179,1,9,4,11
	.byte	'baudrate',0,1
	.word	479
	.byte	1,7,2,35,0,11
	.byte	'phase',0,1
	.word	479
	.byte	1,6,2,35,0,11
	.byte	'receive',0,1
	.word	479
	.byte	1,5,2,35,0,11
	.byte	'transmit',0,1
	.word	479
	.byte	1,4,2,35,0,11
	.byte	'reserved',0,4
	.word	1288
	.byte	28,0,2,35,0,0,7
	.byte	'long int',0,4,5,19,5,124,9,8,13
	.byte	'data',0
	.word	370
	.byte	4,2,35,0,13
	.byte	'remaining',0
	.word	1093
	.byte	2,2,35,4,0,21,1,1,22
	.word	370
	.byte	0,3
	.word	1518
	.byte	20
	.byte	'SpiIf_Cbk',0,5,211,1,16
	.word	1527
	.byte	3
	.word	.L28-.L7
	.byte	21,1,1,22
	.word	1551
	.byte	0,3
	.word	1556
	.byte	20
	.byte	'TxRxHandler',0,5,212,1,16
	.word	1565
.L28:
	.byte	10
	.byte	'SpiIf_Ch_',0,5,214,1,8,48,13
	.byte	'driver',0
	.word	.L26-.L7
	.byte	4,2,35,0,13
	.byte	'flags',0
	.word	1359
	.byte	1,2,35,4,13
	.byte	'errorChecks',0
	.word	1364
	.byte	4,2,35,6,13
	.byte	'baudrate',0
	.word	1467
	.byte	4,2,35,10,13
	.byte	'tx',0
	.word	1479
	.byte	8,2,35,16,13
	.byte	'rx',0
	.word	1479
	.byte	8,2,35,24,13
	.byte	'onExchangeEnd',0
	.word	1532
	.byte	4,2,35,32,13
	.byte	'callbackData',0
	.word	370
	.byte	4,2,35,36,13
	.byte	'txHandler',0
	.word	1570
	.byte	4,2,35,40,13
	.byte	'rxHandler',0
	.word	1570
	.byte	4,2,35,44,0,3
	.word	1591
	.byte	17,5,69,9,1,18
	.byte	'SpiIf_Status_ok',0,0,18
	.byte	'SpiIf_Status_busy',0,1,18
	.byte	'SpiIf_Status_unknown',0,2,0,23
	.word	364
	.byte	3
	.word	1857
	.byte	24
	.word	1790
	.byte	1,1,22
	.word	1551
	.byte	22
	.word	1862
	.byte	22
	.word	370
	.byte	22
	.word	1093
	.byte	0,3
	.word	1867
	.byte	20
	.byte	'SpiIf_Exchange',0,5,138,1,24
	.word	1895
	.byte	24
	.word	1790
	.byte	1,1,22
	.word	1551
	.byte	0,3
	.word	1924
	.byte	20
	.byte	'SpiIf_GetStatus',0,5,139,1,24
	.word	1937
	.byte	3
	.word	.L29-.L7
	.byte	21,1,1,22
	.word	1967
	.byte	0,3
	.word	1972
	.byte	20
	.byte	'SpiIf_OnEvent',0,5,140,1,24
	.word	1981
	.byte	19,5,144,1,9,20,13
	.byte	'exchange',0
	.word	1900
	.byte	4,2,35,0,13
	.byte	'getStatus',0
	.word	1942
	.byte	4,2,35,4,13
	.byte	'onTx',0
	.word	1986
	.byte	4,2,35,8,13
	.byte	'onRx',0
	.word	1986
	.byte	4,2,35,12,13
	.byte	'onError',0
	.word	1986
	.byte	4,2,35,16,0
.L29:
	.byte	10
	.byte	'SpiIf_',0,5,153,1,8,40,13
	.byte	'driver',0
	.word	1274
	.byte	4,2,35,0,13
	.byte	'sending',0
	.word	1288
	.byte	4,2,35,4,13
	.byte	'activeChannel',0
	.word	1785
	.byte	4,2,35,8,13
	.byte	'txCount',0
	.word	1288
	.byte	4,2,35,12,13
	.byte	'rxCount',0
	.word	1288
	.byte	4,2,35,16,13
	.byte	'functions',0
	.word	2009
	.byte	20,2,35,20,0
.L26:
	.byte	3
	.word	2098
	.byte	19,5,189,1,9,16,11
	.byte	'enabled',0,1
	.word	479
	.byte	1,7,2,35,0,11
	.byte	'autoCS',0,1
	.word	479
	.byte	1,6,2,35,0,11
	.byte	'loopback',0,1
	.word	479
	.byte	1,5,2,35,0,11
	.byte	'clockPolarity',0,1
	.word	479
	.byte	1,4,2,35,0,11
	.byte	'shiftClock',0,1
	.word	479
	.byte	1,3,2,35,0,11
	.byte	'dataHeading',0,1
	.word	479
	.byte	1,2,2,35,0,11
	.byte	'dataWidth',0,2
	.word	1012
	.byte	6,4,2,35,0,11
	.byte	'csActiveLevel',0,1
	.word	479
	.byte	1,3,2,35,1,11
	.byte	'parityCheck',0,1
	.word	479
	.byte	1,2,2,35,1,11
	.byte	'parityMode',0,1
	.word	479
	.byte	1,1,2,35,1,13
	.byte	'csInactiveDelay',0
	.word	1288
	.byte	4,2,35,2,13
	.byte	'csLeadDelay',0
	.word	1288
	.byte	4,2,35,6,13
	.byte	'csTrailDelay',0
	.word	1288
	.byte	4,2,35,10,0,10
	.byte	'SpiIf_ChConfig_',0,5,228,1,8,28,13
	.byte	'driver',0
	.word	2221
	.byte	4,2,35,0,13
	.byte	'baudrate',0
	.word	284
	.byte	4,2,35,4,13
	.byte	'mode',0
	.word	2226
	.byte	16,2,35,8,13
	.byte	'errorChecks',0
	.word	1364
	.byte	4,2,35,24,0
.L24:
	.byte	3
	.word	2519
	.byte	3
	.word	364
	.byte	3
	.word	1518
	.byte	3
	.word	1556
	.byte	3
	.word	1867
	.byte	3
	.word	1924
	.byte	3
	.word	1972
	.byte	20
	.byte	'__wchar_t',0,8,1,1
	.word	1093
	.byte	20
	.byte	'__size_t',0,8,1,1
	.word	456
	.byte	20
	.byte	'__ptrdiff_t',0,8,1,1
	.word	472
	.byte	25,1,3
	.word	2701
	.byte	20
	.byte	'__codeptr',0,8,1,1
	.word	2703
	.byte	20
	.byte	'IfxSrc_Tos',0,6,74,3
	.word	1034
	.byte	20
	.byte	'boolean',0,9,101,29
	.word	479
	.byte	20
	.byte	'uint8',0,9,105,29
	.word	479
	.byte	20
	.byte	'uint16',0,9,109,29
	.word	1012
	.byte	20
	.byte	'uint32',0,9,113,29
	.word	1288
	.byte	20
	.byte	'uint64',0,9,118,29
	.word	338
	.byte	20
	.byte	'sint16',0,9,126,29
	.word	1093
	.byte	20
	.byte	'sint32',0,9,131,1,29
	.word	1467
	.byte	7
	.byte	'long long int',0,8,5,20
	.byte	'sint64',0,9,138,1,29
	.word	2851
	.byte	20
	.byte	'float32',0,9,167,1,29
	.word	284
	.byte	20
	.byte	'Ifx_TickTime',0,7,79,28
	.word	2851
	.byte	20
	.byte	'Ifx_SizeT',0,7,92,16
	.word	1093
	.byte	20
	.byte	'Ifx_Priority',0,7,103,16
	.word	1012
	.byte	20
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	496
	.byte	20
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	786
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	3008
	.byte	20
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	3040
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,8,0,14
	.word	3066
	.byte	20
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	3125
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	3153
	.byte	20
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	3190
	.byte	26,64
	.word	786
	.byte	27,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	3218
	.byte	64,2,35,0,0,14
	.word	3227
	.byte	20
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	3259
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	786
	.byte	4,2,35,12,0,14
	.word	3284
	.byte	20
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	3356
	.byte	26,8
	.word	786
	.byte	27,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	3382
	.byte	8,2,35,0,0,14
	.word	3391
	.byte	20
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	3427
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	786
	.byte	4,2,35,12,0,14
	.word	3457
	.byte	20
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	3530
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	3556
	.byte	20
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	3591
	.byte	26,12
	.word	479
	.byte	27,11,0,26,192,1
	.word	786
	.byte	27,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	3617
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	3626
	.byte	192,1,2,35,16,0,14
	.word	3636
	.byte	20
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	3703
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	786
	.byte	4,2,35,4,0,14
	.word	3729
	.byte	20
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	3777
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	3805
	.byte	20
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	3838
	.byte	26,40
	.word	479
	.byte	27,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	3382
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	3382
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	3382
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	3382
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	786
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	786
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3865
	.byte	40,2,35,40,0,14
	.word	3874
	.byte	20
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	4001
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	4028
	.byte	20
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	4060
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	4086
	.byte	20
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	4118
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	786
	.byte	4,2,35,8,0,14
	.word	4144
	.byte	20
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	4204
	.byte	26,16
	.word	479
	.byte	27,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	786
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	4230
	.byte	16,2,35,16,0,14
	.word	4239
	.byte	20
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	4333
	.byte	26,24
	.word	479
	.byte	27,23,0,10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	786
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	786
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	786
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4360
	.byte	24,2,35,24,0,14
	.word	4369
	.byte	20
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	4486
	.byte	26,12
	.word	786
	.byte	27,2,0,26,4
	.word	479
	.byte	27,3,0,26,8
	.word	479
	.byte	27,7,0,26,32
	.word	786
	.byte	27,7,0,26,32
	.word	4541
	.byte	27,0,0,26,88
	.word	479
	.byte	27,87,0,26,108
	.word	786
	.byte	27,26,0,26,96
	.word	479
	.byte	27,95,0,26,96
	.word	4541
	.byte	27,2,0,26,160,3
	.word	479
	.byte	27,159,3,0,26,64
	.word	4541
	.byte	27,1,0,26,192,3
	.word	479
	.byte	27,191,3,0,26,16
	.word	786
	.byte	27,3,0,26,64
	.word	4626
	.byte	27,3,0,26,192,2
	.word	479
	.byte	27,191,2,0,26,52
	.word	479
	.byte	27,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	4514
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	4523
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	786
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	786
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	3382
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4532
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	4550
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	4559
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	4568
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	4577
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	786
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	3617
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	4586
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	4595
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	4586
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	4595
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	4606
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	4615
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	4635
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	4644
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	4514
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	4655
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	4514
	.byte	12,3,35,192,18,0,14
	.word	4664
	.byte	20
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	5124
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	5150
	.byte	20
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	5183
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	786
	.byte	4,2,35,12,0,14
	.word	5210
	.byte	20
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	5283
	.byte	26,56
	.word	479
	.byte	27,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	786
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	786
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	5310
	.byte	56,2,35,24,0,14
	.word	5319
	.byte	20
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	5442
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	5468
	.byte	20
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	5500
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	786
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	786
	.byte	4,2,35,16,0,14
	.word	5526
	.byte	20
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	5611
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	5637
	.byte	20
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	5669
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	4541
	.byte	32,2,35,0,0,14
	.word	5695
	.byte	20
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	5728
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	4541
	.byte	32,2,35,0,0,14
	.word	5755
	.byte	20
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	5789
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	786
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	786
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	786
	.byte	4,2,35,20,0,14
	.word	5817
	.byte	20
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	5910
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	5937
	.byte	20
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	5969
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	4626
	.byte	16,2,35,4,0,14
	.word	5995
	.byte	20
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	6041
	.byte	26,24
	.word	786
	.byte	27,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	6067
	.byte	24,2,35,0,0,14
	.word	6076
	.byte	20
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	6109
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	4514
	.byte	12,2,35,0,0,14
	.word	6136
	.byte	20
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	6168
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,0,14
	.word	6194
	.byte	20
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	6240
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	786
	.byte	4,2,35,12,0,14
	.word	6266
	.byte	20
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	6341
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	786
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	786
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	786
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	786
	.byte	4,2,35,12,0,14
	.word	6370
	.byte	20
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	6444
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	786
	.byte	4,2,35,0,0,14
	.word	6472
	.byte	20
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	6506
	.byte	26,4
	.word	3008
	.byte	27,0,0,14
	.word	6533
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	6542
	.byte	4,2,35,0,0,14
	.word	6547
	.byte	20
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	6583
	.byte	26,48
	.word	3066
	.byte	27,3,0,14
	.word	6611
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	6620
	.byte	48,2,35,0,0,14
	.word	6625
	.byte	20
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	6665
	.byte	14
	.word	3153
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	6695
	.byte	4,2,35,0,0,14
	.word	6700
	.byte	20
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	6734
	.byte	26,64
	.word	3227
	.byte	27,0,0,14
	.word	6761
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	6770
	.byte	64,2,35,0,0,14
	.word	6775
	.byte	20
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	6809
	.byte	26,32
	.word	3284
	.byte	27,1,0,14
	.word	6836
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	6845
	.byte	32,2,35,0,0,14
	.word	6850
	.byte	20
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	6886
	.byte	14
	.word	3391
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	6914
	.byte	8,2,35,0,0,14
	.word	6919
	.byte	20
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	6963
	.byte	26,16
	.word	3457
	.byte	27,0,0,14
	.word	6995
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	7004
	.byte	16,2,35,0,0,14
	.word	7009
	.byte	20
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	7043
	.byte	26,8
	.word	3556
	.byte	27,1,0,14
	.word	7070
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	7079
	.byte	8,2,35,0,0,14
	.word	7084
	.byte	20
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	7118
	.byte	26,208,1
	.word	3636
	.byte	27,0,0,14
	.word	7145
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	7155
	.byte	208,1,2,35,0,0,14
	.word	7160
	.byte	20
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	7196
	.byte	14
	.word	3729
	.byte	14
	.word	3729
	.byte	14
	.word	3729
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	7223
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4532
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	7228
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	7233
	.byte	8,2,35,24,0,14
	.word	7238
	.byte	20
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	7329
	.byte	26,4
	.word	3805
	.byte	27,0,0,14
	.word	7358
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	7367
	.byte	4,2,35,0,0,14
	.word	7372
	.byte	20
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	7408
	.byte	26,80
	.word	3874
	.byte	27,0,0,14
	.word	7436
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	7445
	.byte	80,2,35,0,0,14
	.word	7450
	.byte	20
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	7486
	.byte	26,4
	.word	4028
	.byte	27,0,0,14
	.word	7514
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	7523
	.byte	4,2,35,0,0,14
	.word	7528
	.byte	20
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	7562
	.byte	26,4
	.word	4086
	.byte	27,0,0,14
	.word	7589
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	7598
	.byte	4,2,35,0,0,14
	.word	7603
	.byte	20
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	7637
	.byte	26,12
	.word	4144
	.byte	27,0,0,14
	.word	7664
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	7673
	.byte	12,2,35,0,0,14
	.word	7678
	.byte	20
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	7712
	.byte	26,64
	.word	4239
	.byte	27,1,0,14
	.word	7739
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	7748
	.byte	64,2,35,0,0,14
	.word	7753
	.byte	20
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	7789
	.byte	26,48
	.word	4369
	.byte	27,0,0,14
	.word	7817
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	7826
	.byte	48,2,35,0,0,14
	.word	7831
	.byte	20
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	7869
	.byte	26,204,18
	.word	4664
	.byte	27,0,0,14
	.word	7898
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	7908
	.byte	204,18,2,35,0,0,14
	.word	7913
	.byte	20
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	7949
	.byte	26,4
	.word	5150
	.byte	27,0,0,14
	.word	7976
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	7985
	.byte	4,2,35,0,0,14
	.word	7990
	.byte	20
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	8026
	.byte	26,64
	.word	5210
	.byte	27,3,0,14
	.word	8054
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	8063
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	786
	.byte	4,2,35,64,0,14
	.word	8068
	.byte	20
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	8117
	.byte	26,80
	.word	5319
	.byte	27,0,0,14
	.word	8145
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	8154
	.byte	80,2,35,0,0,14
	.word	8159
	.byte	20
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	8193
	.byte	26,4
	.word	5468
	.byte	27,0,0,14
	.word	8220
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	8229
	.byte	4,2,35,0,0,14
	.word	8234
	.byte	20
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	8268
	.byte	26,40
	.word	5526
	.byte	27,1,0,14
	.word	8295
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	8304
	.byte	40,2,35,0,0,14
	.word	8309
	.byte	20
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	8343
	.byte	26,8
	.word	5637
	.byte	27,1,0,14
	.word	8370
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	8379
	.byte	8,2,35,0,0,14
	.word	8384
	.byte	20
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	8418
	.byte	26,32
	.word	5695
	.byte	27,0,0,14
	.word	8445
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	8454
	.byte	32,2,35,0,0,14
	.word	8459
	.byte	20
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	8495
	.byte	26,32
	.word	5755
	.byte	27,0,0,14
	.word	8523
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	8532
	.byte	32,2,35,0,0,14
	.word	8537
	.byte	20
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	8575
	.byte	26,96
	.word	5817
	.byte	27,3,0,14
	.word	8604
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	8613
	.byte	96,2,35,0,0,14
	.word	8618
	.byte	20
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	8654
	.byte	26,4
	.word	5937
	.byte	27,0,0,14
	.word	8682
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	8691
	.byte	4,2,35,0,0,14
	.word	8696
	.byte	20
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	8730
	.byte	14
	.word	5995
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	8757
	.byte	20,2,35,0,0,14
	.word	8762
	.byte	20
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	8796
	.byte	26,24
	.word	6076
	.byte	27,0,0,14
	.word	8823
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	8832
	.byte	24,2,35,0,0,14
	.word	8837
	.byte	20
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	8873
	.byte	26,12
	.word	6136
	.byte	27,0,0,14
	.word	8901
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	8910
	.byte	12,2,35,0,0,14
	.word	8915
	.byte	20
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	8949
	.byte	26,16
	.word	6194
	.byte	27,1,0,14
	.word	8976
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	8985
	.byte	16,2,35,0,0,14
	.word	8990
	.byte	20
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	9024
	.byte	26,64
	.word	6370
	.byte	27,3,0,14
	.word	9051
	.byte	26,224,1
	.word	479
	.byte	27,223,1,0,26,32
	.word	6266
	.byte	27,1,0,14
	.word	9076
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	9060
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	9065
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	9085
	.byte	32,3,35,160,2,0,14
	.word	9090
	.byte	20
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	9159
	.byte	14
	.word	6472
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	9187
	.byte	4,2,35,0,0,14
	.word	9192
	.byte	20
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	9228
	.byte	20
	.byte	'SpiIf_Status',0,5,74,3
	.word	1790
	.byte	20
	.byte	'SpiIf_Ch',0,5,114,32
	.word	1591
	.byte	20
	.byte	'SpiIf_ChConfig',0,5,115,32
	.word	2519
	.byte	20
	.byte	'SpiIf',0,5,116,32
	.word	2098
	.byte	14
	.word	1309
	.byte	20
	.byte	'SpiIf_Flags',0,5,122,3
	.word	9331
	.byte	20
	.byte	'SpiIf_Job',0,5,128,1,3
	.word	1479
	.byte	20
	.byte	'SpiIf_Mode',0,5,136,1,3
	.word	943
	.byte	20
	.byte	'SpiIf_SlsoTiming_HalfTsclk',0,5,141,1,16
	.word	1288
	.byte	20
	.byte	'SpiIf_funcs',0,5,151,1,3
	.word	2009
	.byte	20
	.byte	'SpiIf_Config',0,5,177,1,3
	.word	1106
	.byte	20
	.byte	'Spi_ErrorChecks',0,5,186,1,3
	.word	1364
	.byte	20
	.byte	'SpiIf_ChMode',0,5,208,1,3
	.word	2226
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L8:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,46,1,49,19,0,0,16,5,0,49,19,0,0,17,4,1,58,15,59,15,57
	.byte	15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,19,1,58,15,59,15,57,15,11,15,0,0,20,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,21,21,1,54,15,39,12,0,0,22,5,0,73,19,0,0,23,38,0,73,19,0,0,24,21,1,73,19,54,15,39,12,0,0,25,21
	.byte	0,54,15,0,0,26,1,1,11,15,73,19,0,0,27,33,0,47,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L9:
	.word	.L31-.L30
.L30:
	.half	3
	.word	.L33-.L32
.L32:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\If\\SpiIf.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L33:
.L31:
	.sdecl	'.debug_info',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_info'
.L10:
	.word	295
	.half	3
	.word	.L11
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L13,.L12
	.byte	2
	.word	.L6
	.byte	3
	.byte	'SpiIf_initConfig',0,1,47,6,1,1,1
	.word	.L3,.L20,.L2
	.byte	4
	.byte	'config',0,1,47,37
	.word	.L21,.L22
	.byte	5
	.word	.L3,.L20
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_abbrev'
.L11:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_line'
.L12:
	.word	.L35-.L34
.L34:
	.half	3
	.word	.L37-.L36
.L36:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0,0,0,0,0
.L37:
	.byte	5,31,7,0,5,2
	.word	.L3
	.byte	3,48,1,5,29,9
	.half	.L38-.L3
	.byte	1,5,31,9
	.half	.L39-.L38
	.byte	3,1,1,5,29,9
	.half	.L40-.L39
	.byte	1,5,31,9
	.half	.L41-.L40
	.byte	3,1,1,5,29,9
	.half	.L42-.L41
	.byte	1,5,31,9
	.half	.L43-.L42
	.byte	3,1,1,5,29,9
	.half	.L44-.L43
	.byte	1,5,31,9
	.half	.L45-.L44
	.byte	3,1,1,5,29,9
	.half	.L46-.L45
	.byte	1,5,31,9
	.half	.L47-.L46
	.byte	3,1,1,5,29,9
	.half	.L48-.L47
	.byte	1,5,31,9
	.half	.L49-.L48
	.byte	3,1,1,5,29,9
	.half	.L50-.L49
	.byte	1,5,31,9
	.half	.L51-.L50
	.byte	3,1,1,5,29,9
	.half	.L52-.L51
	.byte	1,5,1,9
	.half	.L53-.L52
	.byte	3,1,1,7,9
	.half	.L14-.L53
	.byte	0,1,1
.L35:
	.sdecl	'.debug_ranges',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_ranges'
.L13:
	.word	-1,.L3,0,.L14-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_info'
.L15:
	.word	321
	.half	3
	.word	.L16
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L18,.L17
	.byte	2
	.word	.L6
	.byte	3
	.byte	'SpiIf_initChannelConfig',0,1,60,6,1,1,1
	.word	.L5,.L23,.L4
	.byte	4
	.byte	'config',0,1,60,46
	.word	.L24,.L25
	.byte	4
	.byte	'driver',0,1,60,61
	.word	.L26,.L27
	.byte	5
	.word	.L5,.L23
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_line'
.L17:
	.word	.L55-.L54
.L54:
	.half	3
	.word	.L57-.L56
.L56:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/If/SpiIf.c',0,0,0,0,0
.L57:
	.byte	5,34,7,0,5,2
	.word	.L5
	.byte	3,61,1,5,36,9
	.half	.L58-.L5
	.byte	3,1,1,5,34,9
	.half	.L59-.L58
	.byte	1,5,17,9
	.half	.L60-.L59
	.byte	3,1,1,5,34,9
	.half	.L61-.L60
	.byte	1,5,17,9
	.half	.L62-.L61
	.byte	3,1,1,5,34,9
	.half	.L63-.L62
	.byte	1,5,17,9
	.half	.L64-.L63
	.byte	3,1,1,5,34,9
	.half	.L65-.L64
	.byte	1,5,17,9
	.half	.L66-.L65
	.byte	3,1,1,5,34,9
	.half	.L67-.L66
	.byte	1,5,17,9
	.half	.L68-.L67
	.byte	3,1,1,5,34,9
	.half	.L69-.L68
	.byte	1,5,17,9
	.half	.L70-.L69
	.byte	3,1,1,5,34,9
	.half	.L71-.L70
	.byte	1,5,17,9
	.half	.L72-.L71
	.byte	3,1,1,5,36,9
	.half	.L73-.L72
	.byte	1,5,34,9
	.half	.L74-.L73
	.byte	1,5,17,9
	.half	.L75-.L74
	.byte	3,1,1,5,34,9
	.half	.L76-.L75
	.byte	1,5,36,9
	.half	.L77-.L76
	.byte	3,1,1,5,34,9
	.half	.L78-.L77
	.byte	1,5,36,9
	.half	.L79-.L78
	.byte	3,1,1,5,34,9
	.half	.L80-.L79
	.byte	1,5,36,9
	.half	.L81-.L80
	.byte	3,1,1,5,34,9
	.half	.L82-.L81
	.byte	1,5,17,9
	.half	.L83-.L82
	.byte	3,1,1,5,34,9
	.half	.L84-.L83
	.byte	1,5,17,9
	.half	.L85-.L84
	.byte	3,1,1,5,34,9
	.half	.L86-.L85
	.byte	1,5,24,9
	.half	.L87-.L86
	.byte	3,1,1,5,34,9
	.half	.L88-.L87
	.byte	1,5,24,9
	.half	.L89-.L88
	.byte	3,1,1,5,34,9
	.half	.L90-.L89
	.byte	1,5,24,9
	.half	.L91-.L90
	.byte	3,1,1,5,34,9
	.half	.L92-.L91
	.byte	1,5,24,9
	.half	.L93-.L92
	.byte	3,1,1,5,34,9
	.half	.L94-.L93
	.byte	1,5,1,9
	.half	.L95-.L94
	.byte	3,1,1,7,9
	.half	.L19-.L95
	.byte	0,1,1
.L55:
	.sdecl	'.debug_ranges',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_ranges'
.L18:
	.word	-1,.L5,0,.L19-.L5,0,0
	.sdecl	'.debug_loc',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L23-.L5
	.half	2
	.byte	138,0
	.word	0,0
.L25:
	.word	-1,.L5,0,.L23-.L5
	.half	1
	.byte	100
	.word	0,0
.L27:
	.word	-1,.L5,0,.L23-.L5
	.half	1
	.byte	101
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L20-.L3
	.half	2
	.byte	138,0
	.word	0,0
.L22:
	.word	-1,.L3,0,.L20-.L3
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L96:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('SpiIf_initConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L96,.L3,.L20-.L3
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('SpiIf_initChannelConfig')
	.sect	'.debug_frame'
	.word	20
	.word	.L96,.L5,.L23-.L5
	.byte	8,18,8,19,8,22,8,23
	; Module end
