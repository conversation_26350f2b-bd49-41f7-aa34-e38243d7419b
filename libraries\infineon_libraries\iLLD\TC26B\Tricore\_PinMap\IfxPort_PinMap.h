/**
 * \file IfxPort_PinMap.h
 * \brief PORT I/O map
 * \ingroup IfxLld_Port
 *
 * \version iLLD_1_0_1_11_0
 * \copyright Copyright (c) 2013 Infineon Technologies AG. All rights reserved.
 *
 *
 *                                 IMPORTANT NOTICE
 *
 *
 * Use of this file is subject to the terms of use agreed between (i) you or 
 * the company in which ordinary course of business you are acting and (ii) 
 * Infineon Technologies AG or its licensees. If and as long as no such 
 * terms of use are agreed, use of this file is subject to following:


 * Boost Software License - Version 1.0 - August 17th, 2003

 * Permission is hereby granted, free of charge, to any person or 
 * organization obtaining a copy of the software and accompanying 
 * documentation covered by this license (the "Software") to use, reproduce,
 * display, distribute, execute, and transmit the Software, and to prepare
 * derivative works of the Software, and to permit third-parties to whom the 
 * Software is furnished to do so, all subject to the following:

 * The copyright notices in the Software and this entire statement, including
 * the above license grant, this restriction and the following disclaimer, must
 * be included in all copies of the Software, in whole or in part, and all
 * derivative works of the Software, unless such copies or derivative works are
 * solely in the form of machine-executable object code generated by a source
 * language processor.

 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR 
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
 * SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE 
 * FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.

 *
 * \defgroup IfxLld_Port_pinmap PORT Pin Mapping
 * \ingroup IfxLld_Port
 */

#ifndef IFXPORT_PINMAP_H
#define IFXPORT_PINMAP_H

#include <Port/Std/IfxPort.h>

/** \addtogroup IfxLld_Port_pinmap
 * \{ */


IFX_EXTERN IfxPort_Pin IfxPort_P00_0;  /**< \brief IfxPort_P00_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_1;  /**< \brief IfxPort_P00_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_10;  /**< \brief IfxPort_P00_10 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_11;  /**< \brief IfxPort_P00_11 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_12;  /**< \brief IfxPort_P00_12 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_2;  /**< \brief IfxPort_P00_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_3;  /**< \brief IfxPort_P00_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_4;  /**< \brief IfxPort_P00_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_5;  /**< \brief IfxPort_P00_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_6;  /**< \brief IfxPort_P00_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_7;  /**< \brief IfxPort_P00_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_8;  /**< \brief IfxPort_P00_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P00_9;  /**< \brief IfxPort_P00_9 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_0;  /**< \brief IfxPort_P02_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_1;  /**< \brief IfxPort_P02_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_2;  /**< \brief IfxPort_P02_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_3;  /**< \brief IfxPort_P02_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_4;  /**< \brief IfxPort_P02_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_5;  /**< \brief IfxPort_P02_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_6;  /**< \brief IfxPort_P02_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_7;  /**< \brief IfxPort_P02_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P02_8;  /**< \brief IfxPort_P02_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_0;  /**< \brief IfxPort_P10_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_1;  /**< \brief IfxPort_P10_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_2;  /**< \brief IfxPort_P10_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_3;  /**< \brief IfxPort_P10_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_4;  /**< \brief IfxPort_P10_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_5;  /**< \brief IfxPort_P10_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_6;  /**< \brief IfxPort_P10_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_7;  /**< \brief IfxPort_P10_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P10_8;  /**< \brief IfxPort_P10_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_10;  /**< \brief IfxPort_P11_10 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_11;  /**< \brief IfxPort_P11_11 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_12;  /**< \brief IfxPort_P11_12 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_2;  /**< \brief IfxPort_P11_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_3;  /**< \brief IfxPort_P11_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_6;  /**< \brief IfxPort_P11_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P11_9;  /**< \brief IfxPort_P11_9 */
IFX_EXTERN IfxPort_Pin IfxPort_P13_0;  /**< \brief IfxPort_P13_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P13_1;  /**< \brief IfxPort_P13_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P13_2;  /**< \brief IfxPort_P13_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P13_3;  /**< \brief IfxPort_P13_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_0;  /**< \brief IfxPort_P14_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_1;  /**< \brief IfxPort_P14_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_10;  /**< \brief IfxPort_P14_10 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_2;  /**< \brief IfxPort_P14_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_3;  /**< \brief IfxPort_P14_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_4;  /**< \brief IfxPort_P14_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_5;  /**< \brief IfxPort_P14_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_6;  /**< \brief IfxPort_P14_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_7;  /**< \brief IfxPort_P14_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_8;  /**< \brief IfxPort_P14_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P14_9;  /**< \brief IfxPort_P14_9 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_0;  /**< \brief IfxPort_P15_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_1;  /**< \brief IfxPort_P15_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_2;  /**< \brief IfxPort_P15_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_3;  /**< \brief IfxPort_P15_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_4;  /**< \brief IfxPort_P15_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_5;  /**< \brief IfxPort_P15_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_6;  /**< \brief IfxPort_P15_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_7;  /**< \brief IfxPort_P15_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P15_8;  /**< \brief IfxPort_P15_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_0;  /**< \brief IfxPort_P20_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_1;  /**< \brief IfxPort_P20_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_10;  /**< \brief IfxPort_P20_10 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_11;  /**< \brief IfxPort_P20_11 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_12;  /**< \brief IfxPort_P20_12 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_13;  /**< \brief IfxPort_P20_13 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_14;  /**< \brief IfxPort_P20_14 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_3;  /**< \brief IfxPort_P20_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_6;  /**< \brief IfxPort_P20_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_7;  /**< \brief IfxPort_P20_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_8;  /**< \brief IfxPort_P20_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P20_9;  /**< \brief IfxPort_P20_9 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_0;  /**< \brief IfxPort_P21_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_1;  /**< \brief IfxPort_P21_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_2;  /**< \brief IfxPort_P21_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_3;  /**< \brief IfxPort_P21_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_4;  /**< \brief IfxPort_P21_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_5;  /**< \brief IfxPort_P21_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_6;  /**< \brief IfxPort_P21_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P21_7;  /**< \brief IfxPort_P21_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P22_0;  /**< \brief IfxPort_P22_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P22_1;  /**< \brief IfxPort_P22_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P22_2;  /**< \brief IfxPort_P22_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P22_3;  /**< \brief IfxPort_P22_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_0;  /**< \brief IfxPort_P23_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_1;  /**< \brief IfxPort_P23_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_2;  /**< \brief IfxPort_P23_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_3;  /**< \brief IfxPort_P23_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_4;  /**< \brief IfxPort_P23_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P23_5;  /**< \brief IfxPort_P23_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P32_0;  /**< \brief IfxPort_P32_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P32_2;  /**< \brief IfxPort_P32_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P32_3;  /**< \brief IfxPort_P32_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P32_4;  /**< \brief IfxPort_P32_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_0;  /**< \brief IfxPort_P33_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_1;  /**< \brief IfxPort_P33_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_10;  /**< \brief IfxPort_P33_10 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_11;  /**< \brief IfxPort_P33_11 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_12;  /**< \brief IfxPort_P33_12 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_13;  /**< \brief IfxPort_P33_13 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_2;  /**< \brief IfxPort_P33_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_3;  /**< \brief IfxPort_P33_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_4;  /**< \brief IfxPort_P33_4 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_5;  /**< \brief IfxPort_P33_5 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_6;  /**< \brief IfxPort_P33_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_7;  /**< \brief IfxPort_P33_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_8;  /**< \brief IfxPort_P33_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P33_9;  /**< \brief IfxPort_P33_9 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_0;  /**< \brief IfxPort_P40_0 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_1;  /**< \brief IfxPort_P40_1 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_2;  /**< \brief IfxPort_P40_2 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_3;  /**< \brief IfxPort_P40_3 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_6;  /**< \brief IfxPort_P40_6 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_7;  /**< \brief IfxPort_P40_7 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_8;  /**< \brief IfxPort_P40_8 */
IFX_EXTERN IfxPort_Pin IfxPort_P40_9;  /**< \brief IfxPort_P40_9 */

/** \brief Table dimensions */
#define IFXPORT_PINMAP_NUM_MODULES 41
#define IFXPORT_PINMAP_PIN_NUM_ITEMS 15


/** \brief IfxPort_Pin table */
IFX_EXTERN const IfxPort_Pin *IfxPort_Pin_pinTable[IFXPORT_PINMAP_NUM_MODULES][IFXPORT_PINMAP_PIN_NUM_ITEMS];

/** \} */

#endif /* IFXPORT_PINMAP_H */
