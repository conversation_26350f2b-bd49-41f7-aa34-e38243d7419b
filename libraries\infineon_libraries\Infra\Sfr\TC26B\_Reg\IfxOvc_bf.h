/**
 * \file IfxOvc_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Ovc_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Ovc
 * 
 */
#ifndef IFXOVC_BF_H
#define IFXOVC_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Ovc_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_OVC_BLK_OMASK_Bits.OMASK */
#define IFX_OVC_BLK_OMASK_OMASK_LEN (12u)

/** \brief  Mask for Ifx_OVC_BLK_OMASK_Bits.OMASK */
#define IFX_OVC_BLK_OMASK_OMASK_MSK (0xfffu)

/** \brief  Offset for Ifx_OVC_BLK_OMASK_Bits.OMASK */
#define IFX_OVC_BLK_OMASK_OMASK_OFF (5u)

/** \brief  Length for Ifx_OVC_BLK_OMASK_Bits.ONE */
#define IFX_OVC_BLK_OMASK_ONE_LEN (11u)

/** \brief  Mask for Ifx_OVC_BLK_OMASK_Bits.ONE */
#define IFX_OVC_BLK_OMASK_ONE_MSK (0x7ffu)

/** \brief  Offset for Ifx_OVC_BLK_OMASK_Bits.ONE */
#define IFX_OVC_BLK_OMASK_ONE_OFF (17u)

/** \brief  Length for Ifx_OVC_BLK_OTAR_Bits.TBASE */
#define IFX_OVC_BLK_OTAR_TBASE_LEN (23u)

/** \brief  Mask for Ifx_OVC_BLK_OTAR_Bits.TBASE */
#define IFX_OVC_BLK_OTAR_TBASE_MSK (0x7fffffu)

/** \brief  Offset for Ifx_OVC_BLK_OTAR_Bits.TBASE */
#define IFX_OVC_BLK_OTAR_TBASE_OFF (5u)

/** \brief  Length for Ifx_OVC_BLK_RABR_Bits.OBASE */
#define IFX_OVC_BLK_RABR_OBASE_LEN (17u)

/** \brief  Mask for Ifx_OVC_BLK_RABR_Bits.OBASE */
#define IFX_OVC_BLK_RABR_OBASE_MSK (0x1ffffu)

/** \brief  Offset for Ifx_OVC_BLK_RABR_Bits.OBASE */
#define IFX_OVC_BLK_RABR_OBASE_OFF (5u)

/** \brief  Length for Ifx_OVC_BLK_RABR_Bits.OMEM */
#define IFX_OVC_BLK_RABR_OMEM_LEN (3u)

/** \brief  Mask for Ifx_OVC_BLK_RABR_Bits.OMEM */
#define IFX_OVC_BLK_RABR_OMEM_MSK (0x7u)

/** \brief  Offset for Ifx_OVC_BLK_RABR_Bits.OMEM */
#define IFX_OVC_BLK_RABR_OMEM_OFF (24u)

/** \brief  Length for Ifx_OVC_BLK_RABR_Bits.OVEN */
#define IFX_OVC_BLK_RABR_OVEN_LEN (1u)

/** \brief  Mask for Ifx_OVC_BLK_RABR_Bits.OVEN */
#define IFX_OVC_BLK_RABR_OVEN_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_BLK_RABR_Bits.OVEN */
#define IFX_OVC_BLK_RABR_OVEN_OFF (31u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN0 */
#define IFX_OVC_OSEL_SHOVEN0_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN0 */
#define IFX_OVC_OSEL_SHOVEN0_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN0 */
#define IFX_OVC_OSEL_SHOVEN0_OFF (0u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN10 */
#define IFX_OVC_OSEL_SHOVEN10_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN10 */
#define IFX_OVC_OSEL_SHOVEN10_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN10 */
#define IFX_OVC_OSEL_SHOVEN10_OFF (10u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN11 */
#define IFX_OVC_OSEL_SHOVEN11_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN11 */
#define IFX_OVC_OSEL_SHOVEN11_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN11 */
#define IFX_OVC_OSEL_SHOVEN11_OFF (11u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN12 */
#define IFX_OVC_OSEL_SHOVEN12_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN12 */
#define IFX_OVC_OSEL_SHOVEN12_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN12 */
#define IFX_OVC_OSEL_SHOVEN12_OFF (12u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN13 */
#define IFX_OVC_OSEL_SHOVEN13_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN13 */
#define IFX_OVC_OSEL_SHOVEN13_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN13 */
#define IFX_OVC_OSEL_SHOVEN13_OFF (13u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN14 */
#define IFX_OVC_OSEL_SHOVEN14_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN14 */
#define IFX_OVC_OSEL_SHOVEN14_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN14 */
#define IFX_OVC_OSEL_SHOVEN14_OFF (14u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN15 */
#define IFX_OVC_OSEL_SHOVEN15_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN15 */
#define IFX_OVC_OSEL_SHOVEN15_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN15 */
#define IFX_OVC_OSEL_SHOVEN15_OFF (15u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN16 */
#define IFX_OVC_OSEL_SHOVEN16_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN16 */
#define IFX_OVC_OSEL_SHOVEN16_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN16 */
#define IFX_OVC_OSEL_SHOVEN16_OFF (16u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN17 */
#define IFX_OVC_OSEL_SHOVEN17_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN17 */
#define IFX_OVC_OSEL_SHOVEN17_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN17 */
#define IFX_OVC_OSEL_SHOVEN17_OFF (17u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN18 */
#define IFX_OVC_OSEL_SHOVEN18_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN18 */
#define IFX_OVC_OSEL_SHOVEN18_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN18 */
#define IFX_OVC_OSEL_SHOVEN18_OFF (18u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN19 */
#define IFX_OVC_OSEL_SHOVEN19_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN19 */
#define IFX_OVC_OSEL_SHOVEN19_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN19 */
#define IFX_OVC_OSEL_SHOVEN19_OFF (19u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN1 */
#define IFX_OVC_OSEL_SHOVEN1_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN1 */
#define IFX_OVC_OSEL_SHOVEN1_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN1 */
#define IFX_OVC_OSEL_SHOVEN1_OFF (1u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN20 */
#define IFX_OVC_OSEL_SHOVEN20_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN20 */
#define IFX_OVC_OSEL_SHOVEN20_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN20 */
#define IFX_OVC_OSEL_SHOVEN20_OFF (20u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN21 */
#define IFX_OVC_OSEL_SHOVEN21_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN21 */
#define IFX_OVC_OSEL_SHOVEN21_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN21 */
#define IFX_OVC_OSEL_SHOVEN21_OFF (21u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN22 */
#define IFX_OVC_OSEL_SHOVEN22_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN22 */
#define IFX_OVC_OSEL_SHOVEN22_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN22 */
#define IFX_OVC_OSEL_SHOVEN22_OFF (22u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN23 */
#define IFX_OVC_OSEL_SHOVEN23_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN23 */
#define IFX_OVC_OSEL_SHOVEN23_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN23 */
#define IFX_OVC_OSEL_SHOVEN23_OFF (23u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN24 */
#define IFX_OVC_OSEL_SHOVEN24_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN24 */
#define IFX_OVC_OSEL_SHOVEN24_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN24 */
#define IFX_OVC_OSEL_SHOVEN24_OFF (24u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN25 */
#define IFX_OVC_OSEL_SHOVEN25_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN25 */
#define IFX_OVC_OSEL_SHOVEN25_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN25 */
#define IFX_OVC_OSEL_SHOVEN25_OFF (25u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN26 */
#define IFX_OVC_OSEL_SHOVEN26_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN26 */
#define IFX_OVC_OSEL_SHOVEN26_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN26 */
#define IFX_OVC_OSEL_SHOVEN26_OFF (26u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN27 */
#define IFX_OVC_OSEL_SHOVEN27_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN27 */
#define IFX_OVC_OSEL_SHOVEN27_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN27 */
#define IFX_OVC_OSEL_SHOVEN27_OFF (27u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN28 */
#define IFX_OVC_OSEL_SHOVEN28_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN28 */
#define IFX_OVC_OSEL_SHOVEN28_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN28 */
#define IFX_OVC_OSEL_SHOVEN28_OFF (28u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN29 */
#define IFX_OVC_OSEL_SHOVEN29_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN29 */
#define IFX_OVC_OSEL_SHOVEN29_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN29 */
#define IFX_OVC_OSEL_SHOVEN29_OFF (29u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN2 */
#define IFX_OVC_OSEL_SHOVEN2_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN2 */
#define IFX_OVC_OSEL_SHOVEN2_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN2 */
#define IFX_OVC_OSEL_SHOVEN2_OFF (2u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN30 */
#define IFX_OVC_OSEL_SHOVEN30_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN30 */
#define IFX_OVC_OSEL_SHOVEN30_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN30 */
#define IFX_OVC_OSEL_SHOVEN30_OFF (30u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN31 */
#define IFX_OVC_OSEL_SHOVEN31_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN31 */
#define IFX_OVC_OSEL_SHOVEN31_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN31 */
#define IFX_OVC_OSEL_SHOVEN31_OFF (31u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN3 */
#define IFX_OVC_OSEL_SHOVEN3_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN3 */
#define IFX_OVC_OSEL_SHOVEN3_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN3 */
#define IFX_OVC_OSEL_SHOVEN3_OFF (3u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN4 */
#define IFX_OVC_OSEL_SHOVEN4_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN4 */
#define IFX_OVC_OSEL_SHOVEN4_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN4 */
#define IFX_OVC_OSEL_SHOVEN4_OFF (4u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN5 */
#define IFX_OVC_OSEL_SHOVEN5_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN5 */
#define IFX_OVC_OSEL_SHOVEN5_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN5 */
#define IFX_OVC_OSEL_SHOVEN5_OFF (5u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN6 */
#define IFX_OVC_OSEL_SHOVEN6_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN6 */
#define IFX_OVC_OSEL_SHOVEN6_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN6 */
#define IFX_OVC_OSEL_SHOVEN6_OFF (6u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN7 */
#define IFX_OVC_OSEL_SHOVEN7_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN7 */
#define IFX_OVC_OSEL_SHOVEN7_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN7 */
#define IFX_OVC_OSEL_SHOVEN7_OFF (7u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN8 */
#define IFX_OVC_OSEL_SHOVEN8_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN8 */
#define IFX_OVC_OSEL_SHOVEN8_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN8 */
#define IFX_OVC_OSEL_SHOVEN8_OFF (8u)

/** \brief  Length for Ifx_OVC_OSEL_Bits.SHOVEN9 */
#define IFX_OVC_OSEL_SHOVEN9_LEN (1u)

/** \brief  Mask for Ifx_OVC_OSEL_Bits.SHOVEN9 */
#define IFX_OVC_OSEL_SHOVEN9_MSK (0x1u)

/** \brief  Offset for Ifx_OVC_OSEL_Bits.SHOVEN9 */
#define IFX_OVC_OSEL_SHOVEN9_OFF (9u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXOVC_BF_H */
