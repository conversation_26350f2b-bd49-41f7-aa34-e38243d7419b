	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc15920a --dep-file=IfxStdIf_Pos.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.src ../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c'

	
$TC16X
	
	.sdecl	'.text.IfxStdIf_Pos.IfxStdIf_Pos_initConfig',code,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.text.IfxStdIf_Pos.IfxStdIf_Pos_initConfig'
	.align	2
	
	.global	IfxStdIf_Pos_initConfig
; Function IfxStdIf_Pos_initConfig
.L11:
IfxStdIf_Pos_initConfig:	.type	func
	mov	d15,#0
.L62:
	st.w	[a4],d15
.L63:
	mov	d15,#0
.L64:
	st.b	[a4]4,d15
.L65:
	mov	d15,#0
.L66:
	st.w	[a4]6,d15
.L67:
	mov	d15,#1
.L68:
	st.h	[a4]10,d15
.L69:
	mov	d15,#1
.L70:
	st.b	[a4]12,d15
.L71:
	mov	d15,#4719
	addih	d15,d15,#14979
.L72:
	st.w	[a4]14,d15
.L73:
	mov	d15,#0
.L74:
	st.w	[a4]18,d15
.L75:
	mov	d15,#0
.L76:
	st.w	[a4]22,d15
.L77:
	mov.u	d15,#58963
	addih	d15,d15,#17666
.L78:
	st.w	[a4]26,d15
.L79:
	mov	d15,#0
.L80:
	st.b	[a4]30,d15
.L81:
	mov	d15,#0
.L82:
	st.w	[a4]32,d15
.L83:
	ret
.L28:
	
__IfxStdIf_Pos_initConfig_function_end:
	.size	IfxStdIf_Pos_initConfig,__IfxStdIf_Pos_initConfig_function_end-IfxStdIf_Pos_initConfig
.L22:
	; End of function
	
	.sdecl	'.text.IfxStdIf_Pos.IfxStdIf_Pos_printStatus',code,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.text.IfxStdIf_Pos.IfxStdIf_Pos_printStatus'
	.align	2
	
	.global	IfxStdIf_Pos_printStatus
; Function IfxStdIf_Pos_printStatus
.L13:
IfxStdIf_Pos_printStatus:	.type	func
	mov.aa	a15,a5
.L39:
	ld.a	a2,[a4]24
.L88:
	ld.a	a4,[a4]
.L43:
	calli	a2
.L44:
	j	.L2
.L2:
	mov	d8,d2
.L45:
	movh.a	a5,#@his(.1.str)
	lea	a5,[a5]@los(.1.str)
	mov.aa	a4,a15
.L46:
	call	IfxStdIf_DPipe_print
.L47:
	jeq	d8,#0,.L3
.L89:
	extr.u	d15,d8,#0,#8
.L90:
	jz.t	d15:4,.L4
.L91:
	movh.a	a5,#@his(.2.str)
	lea	a5,[a5]@los(.2.str)
	mov.aa	a4,a15
.L48:
	call	IfxStdIf_DPipe_print
.L4:
	extr.u	d15,d8,#0,#8
.L92:
	jz.t	d15:0,.L5
.L93:
	movh.a	a5,#@his(.3.str)
	lea	a5,[a5]@los(.3.str)
	mov.aa	a4,a15
.L49:
	call	IfxStdIf_DPipe_print
.L5:
	extr.u	d15,d8,#0,#8
.L94:
	jz.t	d15:2,.L6
.L95:
	movh.a	a5,#@his(.4.str)
	lea	a5,[a5]@los(.4.str)
	mov.aa	a4,a15
.L50:
	call	IfxStdIf_DPipe_print
.L6:
	extr.u	d15,d8,#0,#8
.L96:
	jz.t	d15:1,.L7
.L97:
	movh.a	a5,#@his(.5.str)
	lea	a5,[a5]@los(.5.str)
	mov.aa	a4,a15
.L51:
	call	IfxStdIf_DPipe_print
.L7:
	extr.u	d15,d8,#0,#8
.L98:
	jz.t	d15:3,.L8
.L99:
	movh.a	a5,#@his(.6.str)
	lea	a5,[a5]@los(.6.str)
	mov.aa	a4,a15
.L52:
	call	IfxStdIf_DPipe_print
.L8:
	j	.L9
.L3:
	movh.a	a5,#@his(.7.str)
	lea	a5,[a5]@los(.7.str)
	mov.aa	a4,a15
.L53:
	call	IfxStdIf_DPipe_print
.L9:
	ret
.L31:
	
__IfxStdIf_Pos_printStatus_function_end:
	.size	IfxStdIf_Pos_printStatus,__IfxStdIf_Pos_printStatus_function_end-IfxStdIf_Pos_printStatus
.L27:
	; End of function
	
	.sdecl	'.rodata.IfxStdIf_Pos..1.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..1.str'
.1.str:	.type	object
	.size	.1.str,20
	.byte	68,83,65,68,67,32,82,68
	.byte	67,32,115,116,97,116,117,115
	.byte	58,13,10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..2.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..2.str'
.2.str:	.type	object
	.size	.2.str,24
	.byte	45,32,67,111,109,109,117,110
	.byte	105,99,97,116,105,111,110,32
	.byte	101,114,114,111
	.byte	114,13,10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..3.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..3.str'
.3.str:	.type	object
	.size	.3.str,26
	.byte	45,32,83,121,110,99,104,114
	.byte	111,110,105,122,97,116,105,111
	.byte	110,32,101,114,114,111,114,13
	.byte	10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..4.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..4.str'
.4.str:	.type	object
	.size	.4.str,29
	.byte	45,32,83,105,103,110,97,108
	.byte	32,100,101,103,114,97,100,97
	.byte	116,105,111,110,32,101,114,114
	.byte	111,114,13,10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..5.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..5.str'
.5.str:	.type	object
	.size	.5.str,22
	.byte	45,32,83,105,103,110,97,108
	.byte	32,108,111,115,115,32,101,114
	.byte	114,111,114,13
	.byte	10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..6.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..6.str'
.6.str:	.type	object
	.size	.6.str,19
	.byte	45,32,84,114,97,99,107,105
	.byte	110,103,32,101,114,114,111,114
	.byte	13,10
	.space	1
	.sdecl	'.rodata.IfxStdIf_Pos..7.str',data,rom
	.sect	'.rodata.IfxStdIf_Pos..7.str'
.7.str:	.type	object
	.size	.7.str,10
	.byte	45,32,82,101,97,100,121,13
	.byte	10
	.space	1
	.calls	'IfxStdIf_Pos_printStatus','__INDIRECT__'
	.calls	'IfxStdIf_Pos_printStatus','IfxStdIf_DPipe_print'
	.calls	'IfxStdIf_Pos_initConfig','',0
	.extern	IfxStdIf_DPipe_print
	.extern	__INDIRECT__
	.calls	'IfxStdIf_Pos_printStatus','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L15:
	.word	4759
	.half	3
	.word	.L16
	.byte	4
.L14:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L17
	.byte	2
	.byte	'unsigned long int',0,4,7,2
	.byte	'unsigned char',0,1,8,3,1,106,5,1,4
	.byte	'notSynchronised',0,1
	.word	255
	.byte	1,7,2,35,0,4
	.byte	'signalLoss',0,1
	.word	255
	.byte	1,6,2,35,0,4
	.byte	'signalDegradation',0,1
	.word	255
	.byte	1,5,2,35,0,4
	.byte	'trackingLoss',0,1
	.word	255
	.byte	1,4,2,35,0,4
	.byte	'commError',0,1
	.word	255
	.byte	1,3,2,35,0,0
.L36:
	.byte	5,1,103,9,4,6
	.byte	'status',0
	.word	234
	.byte	4,2,35,0,6
	.byte	'B',0
	.word	272
	.byte	1,2,35,0,0,7
	.byte	'void',0,8
	.word	434
	.byte	9
	.byte	'IfxStdIf_InterfaceDriver',0,2,118,15
	.word	440
	.byte	10,1,1,11
	.word	440
	.byte	0,8
	.word	478
	.byte	9
	.byte	'IfxStdIf_Pos_OnZeroIrq',0,1,135,1,16
	.word	487
	.byte	2
	.byte	'float',0,4,4,12
	.word	524
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	533
	.byte	9
	.byte	'IfxStdIf_Pos_GetAbsolutePosition',0,1,129,1,19
	.word	546
	.byte	2
	.byte	'long int',0,4,5,12
	.word	593
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	605
	.byte	9
	.byte	'IfxStdIf_Pos_GetOffset',0,1,142,1,18
	.word	618
	.byte	9
	.byte	'IfxStdIf_Pos_GetPosition',0,1,152,1,19
	.word	546
	.byte	13,1,95,9,1,14
	.byte	'IfxStdIf_Pos_Dir_forward',0,0,14
	.byte	'IfxStdIf_Pos_Dir_backward',0,1,14
	.byte	'IfxStdIf_Pos_Dir_unknown',0,2,0,12
	.word	689
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	777
	.byte	9
	.byte	'IfxStdIf_Pos_GetDirection',0,1,161,1,28
	.word	790
	.byte	12
	.word	401
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	830
	.byte	9
	.byte	'IfxStdIf_Pos_GetFault',0,1,168,1,31
	.word	843
	.byte	9
	.byte	'IfxStdIf_Pos_GetRawPosition',0,1,184,1,18
	.word	618
	.byte	2
	.byte	'unsigned short int',0,2,7,12
	.word	916
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	938
	.byte	9
	.byte	'IfxStdIf_Pos_GetPeriodPerRotation',0,1,175,1,18
	.word	951
	.byte	9
	.byte	'IfxStdIf_Pos_GetRefreshPeriod',0,1,190,1,19
	.word	546
	.byte	9
	.byte	'IfxStdIf_Pos_GetResolution',0,1,196,1,18
	.word	618
	.byte	13,1,84,9,1,14
	.byte	'IfxStdIf_Pos_SensorType_encoder',0,0,14
	.byte	'IfxStdIf_Pos_SensorType_hall',0,1,14
	.byte	'IfxStdIf_Pos_SensorType_resolver',0,2,14
	.byte	'IfxStdIf_Pos_SensorType_angletrk',0,3,14
	.byte	'IfxStdIf_Pos_SensorType_igmr',0,4,14
	.byte	'IfxStdIf_Pos_SensorType_virtual',0,5,0,12
	.word	1074
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	1280
	.byte	9
	.byte	'IfxStdIf_Pos_GetSensorType',0,1,202,1,35
	.word	1293
	.byte	9
	.byte	'IfxStdIf_Pos_GetTurn',0,1,214,1,18
	.word	618
	.byte	9
	.byte	'IfxStdIf_Pos_OnEventA',0,1,221,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_Pos_Reset',0,1,239,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_Pos_ResetFaults',0,1,248,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_Pos_GetSpeed',0,1,208,1,19
	.word	546
	.byte	9
	.byte	'IfxStdIf_Pos_Update',0,1,230,1,16
	.word	487
	.byte	10,1,1,11
	.word	440
	.byte	11
	.word	593
	.byte	0,8
	.word	1517
	.byte	9
	.byte	'IfxStdIf_Pos_SetOffset',0,1,255,1,16
	.word	1531
	.byte	10,1,1,11
	.word	440
	.byte	11
	.word	524
	.byte	0,8
	.word	1568
	.byte	9
	.byte	'IfxStdIf_Pos_SetPosition',0,1,134,2,16
	.word	1582
	.byte	9
	.byte	'IfxStdIf_Pos_SetRawPosition',0,1,140,2,16
	.word	1531
	.byte	9
	.byte	'IfxStdIf_Pos_SetSpeed',0,1,147,2,16
	.word	1582
	.byte	9
	.byte	'IfxStdIf_Pos_SetRefreshPeriod',0,1,154,2,16
	.word	1582
	.byte	15
	.byte	'IfxStdIf_Pos_',0,1,158,2,8,92,6
	.byte	'driver',0
	.word	445
	.byte	4,2,35,0,6
	.byte	'onZeroIrq',0
	.word	492
	.byte	4,2,35,4,6
	.byte	'getAbsolutePosition',0
	.word	551
	.byte	4,2,35,8,6
	.byte	'getOffset',0
	.word	623
	.byte	4,2,35,12,6
	.byte	'getPosition',0
	.word	655
	.byte	4,2,35,16,6
	.byte	'getDirection',0
	.word	795
	.byte	4,2,35,20,6
	.byte	'getFault',0
	.word	848
	.byte	4,2,35,24,6
	.byte	'getRawPosition',0
	.word	879
	.byte	4,2,35,28,6
	.byte	'getPeriodPerRotation',0
	.word	956
	.byte	4,2,35,32,6
	.byte	'getRefreshPeriod',0
	.word	999
	.byte	4,2,35,36,6
	.byte	'getResolution',0
	.word	1038
	.byte	4,2,35,40,6
	.byte	'getSensorType',0
	.word	1298
	.byte	4,2,35,44,6
	.byte	'getTurn',0
	.word	1334
	.byte	4,2,35,48,6
	.byte	'onEventA',0
	.word	1364
	.byte	4,2,35,52,6
	.byte	'reset',0
	.word	1395
	.byte	4,2,35,56,6
	.byte	'resetFaults',0
	.word	1423
	.byte	4,2,35,60,6
	.byte	'getSpeed',0
	.word	1457
	.byte	4,2,35,64,6
	.byte	'update',0
	.word	1488
	.byte	4,2,35,68,6
	.byte	'setOffset',0
	.word	1536
	.byte	4,2,35,72,6
	.byte	'setPosition',0
	.word	1587
	.byte	4,2,35,76,6
	.byte	'setRawPosition',0
	.word	1621
	.byte	4,2,35,80,6
	.byte	'setSpeed',0
	.word	1658
	.byte	4,2,35,84,6
	.byte	'setRefreshPeriod',0
	.word	1689
	.byte	4,2,35,88,0
.L32:
	.byte	8
	.word	1728
.L38:
	.byte	16
	.byte	'IfxStdIf_Pos_getFault',0,3,1,225,2,32
	.word	401
	.byte	1,1
.L40:
	.byte	17
	.byte	'stdIf',0,1,225,2,68
	.word	2232
.L42:
	.byte	18,0,8
	.word	434
	.byte	8
	.word	478
	.byte	8
	.word	533
	.byte	8
	.word	605
	.byte	8
	.word	533
	.byte	8
	.word	777
	.byte	8
	.word	830
	.byte	8
	.word	605
	.byte	8
	.word	938
	.byte	8
	.word	533
	.byte	8
	.word	605
	.byte	8
	.word	1280
	.byte	8
	.word	605
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	533
	.byte	8
	.word	478
	.byte	8
	.word	1517
	.byte	8
	.word	1568
	.byte	8
	.word	1517
	.byte	8
	.word	1568
	.byte	8
	.word	1568
	.byte	2
	.byte	'short int',0,2,5,8
	.word	2403
	.byte	2
	.byte	'long long int',0,8,5,12
	.word	255
	.byte	1,1,11
	.word	440
	.byte	11
	.word	440
	.byte	11
	.word	2416
	.byte	11
	.word	2421
	.byte	0,8
	.word	2438
	.byte	9
	.byte	'IfxStdIf_DPipe_Write',0,3,92,19
	.word	2466
	.byte	9
	.byte	'IfxStdIf_DPipe_Read',0,3,107,19
	.word	2466
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadCount',0,3,115,18
	.word	618
	.byte	19
	.word	255
	.byte	8
	.word	2564
	.byte	12
	.word	2569
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	2574
	.byte	9
	.byte	'IfxStdIf_DPipe_GetReadEvent',0,3,123,36
	.word	2587
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteCount',0,3,147,1,18
	.word	618
	.byte	8
	.word	2574
	.byte	9
	.byte	'IfxStdIf_DPipe_GetWriteEvent',0,3,155,1,37
	.word	2666
	.byte	12
	.word	255
	.byte	1,1,11
	.word	440
	.byte	11
	.word	2403
	.byte	11
	.word	2421
	.byte	0,8
	.word	2709
	.byte	9
	.byte	'IfxStdIf_DPipe_CanReadCount',0,3,166,1,19
	.word	2732
	.byte	9
	.byte	'IfxStdIf_DPipe_CanWriteCount',0,3,177,1,19
	.word	2732
	.byte	12
	.word	255
	.byte	1,1,11
	.word	440
	.byte	11
	.word	2421
	.byte	0,8
	.word	2812
	.byte	9
	.byte	'IfxStdIf_DPipe_FlushTx',0,3,186,1,19
	.word	2830
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearTx',0,3,200,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_DPipe_ClearRx',0,3,193,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_DPipe_OnReceive',0,3,208,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_DPipe_OnTransmit',0,3,215,1,16
	.word	487
	.byte	9
	.byte	'IfxStdIf_DPipe_OnError',0,3,222,1,16
	.word	487
	.byte	12
	.word	234
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	3032
	.byte	9
	.byte	'IfxStdIf_DPipe_GetSendCount',0,3,131,1,18
	.word	3045
	.byte	12
	.word	2421
	.byte	1,1,11
	.word	440
	.byte	0,8
	.word	3087
	.byte	9
	.byte	'IfxStdIf_DPipe_GetTxTimeStamp',0,3,139,1,24
	.word	3100
	.byte	9
	.byte	'IfxStdIf_DPipe_ResetSendCount',0,3,229,1,16
	.word	487
	.byte	15
	.byte	'IfxStdIf_DPipe_',0,3,233,1,8,76,6
	.byte	'driver',0
	.word	445
	.byte	4,2,35,0,6
	.byte	'txDisabled',0
	.word	255
	.byte	1,2,35,4,6
	.byte	'write',0
	.word	2471
	.byte	4,2,35,8,6
	.byte	'read',0
	.word	2500
	.byte	4,2,35,12,6
	.byte	'getReadCount',0
	.word	2528
	.byte	4,2,35,16,6
	.byte	'getReadEvent',0
	.word	2592
	.byte	4,2,35,20,6
	.byte	'getWriteCount',0
	.word	2628
	.byte	4,2,35,24,6
	.byte	'getWriteEvent',0
	.word	2671
	.byte	4,2,35,28,6
	.byte	'canReadCount',0
	.word	2737
	.byte	4,2,35,32,6
	.byte	'canWriteCount',0
	.word	2774
	.byte	4,2,35,36,6
	.byte	'flushTx',0
	.word	2835
	.byte	4,2,35,40,6
	.byte	'clearTx',0
	.word	2867
	.byte	4,2,35,44,6
	.byte	'clearRx',0
	.word	2899
	.byte	4,2,35,48,6
	.byte	'onReceive',0
	.word	2931
	.byte	4,2,35,52,6
	.byte	'onTransmit',0
	.word	2965
	.byte	4,2,35,56,6
	.byte	'onError',0
	.word	3000
	.byte	4,2,35,60,6
	.byte	'getSendCount',0
	.word	3050
	.byte	4,2,35,64,6
	.byte	'getTxTimeStamp',0
	.word	3105
	.byte	4,2,35,68,6
	.byte	'resetSendCount',0
	.word	3144
	.byte	4,2,35,72,0
.L34:
	.byte	8
	.word	3183
	.byte	2
	.byte	'char',0,1,6,20
	.word	3588
	.byte	8
	.word	3596
	.byte	9
	.byte	'pchar',0,4,56,28
	.word	3601
	.byte	21
	.byte	'IfxStdIf_DPipe_print',0,3,140,3,17,1,1,1,1,17
	.byte	'stdIf',0,3,140,3,54
	.word	3583
	.byte	17
	.byte	'format',0,3,140,3,67
	.word	3606
	.byte	22,3,140,3,75,0,8
	.word	2438
	.byte	8
	.word	2438
	.byte	8
	.word	605
	.byte	8
	.word	2574
	.byte	8
	.word	605
	.byte	8
	.word	2574
	.byte	8
	.word	2709
	.byte	8
	.word	2709
	.byte	8
	.word	2812
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	478
	.byte	8
	.word	3032
	.byte	8
	.word	3087
	.byte	8
	.word	478
	.byte	20
	.word	3588
	.byte	8
	.word	3772
	.byte	23
	.word	2237
	.byte	24
	.word	2271
	.byte	18,0,13,1,76,9,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_oneFold',0,1,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_twoFold',0,2,14
	.byte	'IfxStdIf_Pos_ResolutionFactor_fourFold',0,4,0,3,1,186,2,9,36,6
	.byte	'offset',0
	.word	593
	.byte	4,2,35,0,6
	.byte	'reversed',0
	.word	255
	.byte	1,2,35,4,6
	.byte	'resolution',0
	.word	593
	.byte	4,2,35,6,6
	.byte	'periodPerRotation',0
	.word	916
	.byte	2,2,35,10,6
	.byte	'resolutionFactor',0
	.word	3794
	.byte	1,2,35,12,6
	.byte	'updatePeriod',0
	.word	524
	.byte	4,2,35,14,6
	.byte	'speedModeThreshold',0
	.word	524
	.byte	4,2,35,18,6
	.byte	'minSpeed',0
	.word	524
	.byte	4,2,35,22,6
	.byte	'maxSpeed',0
	.word	524
	.byte	4,2,35,26,6
	.byte	'speedFilterEnabled',0
	.word	255
	.byte	1,2,35,30,6
	.byte	'speedFilerCutOffFrequency',0
	.word	524
	.byte	4,2,35,32,0
.L29:
	.byte	8
	.word	3921
	.byte	25
	.byte	'__INDIRECT__',0,5,1,1,1,1,1,9
	.byte	'__wchar_t',0,5,1,1
	.word	2403
	.byte	2
	.byte	'unsigned int',0,4,7,9
	.byte	'__size_t',0,5,1,1
	.word	4227
	.byte	2
	.byte	'int',0,4,5,9
	.byte	'__ptrdiff_t',0,5,1,1
	.word	4260
	.byte	26,1,8
	.word	4287
	.byte	9
	.byte	'__codeptr',0,5,1,1
	.word	4289
	.byte	9
	.byte	'boolean',0,6,101,29
	.word	255
	.byte	9
	.byte	'uint8',0,6,105,29
	.word	255
	.byte	9
	.byte	'uint16',0,6,109,29
	.word	916
	.byte	9
	.byte	'uint32',0,6,113,29
	.word	234
	.byte	9
	.byte	'sint16',0,6,126,29
	.word	2403
	.byte	9
	.byte	'sint32',0,6,131,1,29
	.word	593
	.byte	9
	.byte	'sint64',0,6,138,1,29
	.word	2421
	.byte	9
	.byte	'float32',0,6,167,1,29
	.word	524
	.byte	9
	.byte	'pvoid',0,4,57,28
	.word	440
	.byte	9
	.byte	'Ifx_TickTime',0,4,79,28
	.word	2421
	.byte	9
	.byte	'Ifx_SizeT',0,4,92,16
	.word	2403
	.byte	9
	.byte	'IfxStdIf_DPipe',0,3,71,32
	.word	3183
	.byte	19
	.word	255
	.byte	8
	.word	4512
	.byte	9
	.byte	'IfxStdIf_DPipe_WriteEvent',0,3,73,32
	.word	4517
	.byte	9
	.byte	'IfxStdIf_DPipe_ReadEvent',0,3,74,32
	.word	4517
	.byte	9
	.byte	'IfxStdIf_Pos_ResolutionFactor',0,1,81,3
	.word	3794
	.byte	9
	.byte	'IfxStdIf_Pos_SensorType',0,1,92,3
	.word	1074
	.byte	9
	.byte	'IfxStdIf_Pos_Dir',0,1,100,3
	.word	689
	.byte	9
	.byte	'IfxStdIf_Pos_Status',0,1,114,3
	.word	401
	.byte	9
	.byte	'IfxStdIf_Pos',0,1,119,30
	.word	1728
	.byte	9
	.byte	'IfxStdIf_Pos_Config',0,1,199,2,3
	.word	3921
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L16:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,5,23,1,58,15,59,15,57,15,11,15,0,0,6,13,0,3,8,73,19,11
	.byte	15,56,9,0,0,7,59,0,3,8,0,0,8,15,0,73,19,0,0,9,22,0,3,8,58,15,59,15,57,15,73,19,0,0,10,21,1,54,15,39,12
	.byte	0,0,11,5,0,73,19,0,0,12,21,1,73,19,54,15,39,12,0,0,13,4,1,58,15,59,15,57,15,11,15,0,0,14,40,0,3,8,28,13
	.byte	0,0,15,19,1,3,8,58,15,59,15,57,15,11,15,0,0,16,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0
	.byte	17,5,0,3,8,58,15,59,15,57,15,73,19,0,0,18,11,0,0,0,19,53,0,73,19,0,0,20,38,0,73,19,0,0,21,46,1,3,8,58
	.byte	15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,22,24,0,58,15,59,15,57,15,0,0,23,46,1,49,19,0,0,24,5,0,49,19
	.byte	0,0,25,46,0,3,8,58,15,59,15,57,15,54,15,63,12,60,12,0,0,26,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L17:
	.word	.L55-.L54
.L54:
	.half	3
	.word	.L57-.L56
.L56:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_Pos.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_DPipe.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0,0
.L57:
.L55:
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_info'
.L18:
	.word	312
	.half	3
	.word	.L19
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L21,.L20
	.byte	2
	.word	.L14
	.byte	3
	.byte	'IfxStdIf_Pos_initConfig',0,1,48,6,1,1,1
	.word	.L11,.L28,.L10
	.byte	4
	.byte	'config',0,1,48,53
	.word	.L29,.L30
	.byte	5
	.word	.L11,.L28
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_abbrev'
.L19:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_line'
.L20:
	.word	.L59-.L58
.L58:
	.half	3
	.word	.L61-.L60
.L60:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0,0,0,0,0
.L61:
	.byte	5,22,7,0,5,2
	.word	.L11
	.byte	3,49,1,5,20,9
	.half	.L62-.L11
	.byte	1,5,24,9
	.half	.L63-.L62
	.byte	3,1,1,5,22,9
	.half	.L64-.L63
	.byte	1,5,26,9
	.half	.L65-.L64
	.byte	3,1,1,5,24,9
	.half	.L66-.L65
	.byte	1,5,33,9
	.half	.L67-.L66
	.byte	3,1,1,5,31,9
	.half	.L68-.L67
	.byte	1,5,32,9
	.half	.L69-.L68
	.byte	3,1,1,5,30,9
	.half	.L70-.L69
	.byte	1,5,28,9
	.half	.L71-.L70
	.byte	3,1,1,5,26,9
	.half	.L72-.L71
	.byte	1,5,34,9
	.half	.L73-.L72
	.byte	3,1,1,5,32,9
	.half	.L74-.L73
	.byte	1,5,24,9
	.half	.L75-.L74
	.byte	3,1,1,5,22,9
	.half	.L76-.L75
	.byte	1,5,39,9
	.half	.L77-.L76
	.byte	3,1,1,5,22,9
	.half	.L78-.L77
	.byte	1,5,34,9
	.half	.L79-.L78
	.byte	3,1,1,5,32,9
	.half	.L80-.L79
	.byte	1,5,41,9
	.half	.L81-.L80
	.byte	3,1,1,5,39,9
	.half	.L82-.L81
	.byte	1,5,1,9
	.half	.L83-.L82
	.byte	3,2,1,7,9
	.half	.L22-.L83
	.byte	0,1,1
.L59:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_ranges'
.L21:
	.word	-1,.L11,0,.L22-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_info'
.L23:
	.word	384
	.half	3
	.word	.L24
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L26,.L25
	.byte	2
	.word	.L14
	.byte	3
	.byte	'IfxStdIf_Pos_printStatus',0,1,64,6,1,1,1
	.word	.L13,.L31,.L12
	.byte	4
	.byte	'driver',0,1,64,45
	.word	.L32,.L33
	.byte	4
	.byte	'io',0,1,64,69
	.word	.L34,.L35
	.byte	5
	.word	.L13,.L31
	.byte	6
	.byte	'status',0,1,66,25
	.word	.L36,.L37
	.byte	7
	.word	.L38,.L39,.L2
	.byte	8
	.word	.L40,.L41
	.byte	9
	.word	.L42,.L39,.L2
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_abbrev'
.L24:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_line'
.L25:
	.word	.L85-.L84
.L84:
	.half	3
	.word	.L87-.L86
.L86:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_Pos.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_Pos.h',0,0,0,0,0
.L87:
	.byte	5,6,7,0,5,2
	.word	.L13
	.byte	3,63,1,4,2,5,17,9
	.half	.L39-.L13
	.byte	3,163,2,1,5,33,9
	.half	.L88-.L39
	.byte	1,5,5,9
	.half	.L44-.L88
	.byte	1,4,1,5,12,9
	.half	.L2-.L44
	.byte	3,224,125,1,5,30,9
	.half	.L45-.L2
	.byte	3,2,1,5,5,9
	.half	.L47-.L45
	.byte	3,1,1,5,21,7,9
	.half	.L89-.L47
	.byte	3,2,1,5,9,9
	.half	.L90-.L89
	.byte	1,5,35,7,9
	.half	.L91-.L90
	.byte	3,2,1,5,21,9
	.half	.L4-.L91
	.byte	3,2,1,5,9,9
	.half	.L92-.L4
	.byte	1,5,35,7,9
	.half	.L93-.L92
	.byte	3,2,1,5,21,9
	.half	.L5-.L93
	.byte	3,2,1,5,9,9
	.half	.L94-.L5
	.byte	1,5,35,7,9
	.half	.L95-.L94
	.byte	3,2,1,5,21,9
	.half	.L6-.L95
	.byte	3,2,1,5,9,9
	.half	.L96-.L6
	.byte	1,5,35,7,9
	.half	.L97-.L96
	.byte	3,2,1,5,21,9
	.half	.L7-.L97
	.byte	3,2,1,5,9,9
	.half	.L98-.L7
	.byte	1,5,35,7,9
	.half	.L99-.L98
	.byte	3,2,1,5,9,9
	.half	.L8-.L99
	.byte	3,126,1,5,31,9
	.half	.L3-.L8
	.byte	3,7,1,5,1,9
	.half	.L9-.L3
	.byte	3,2,1,7,9
	.half	.L27-.L9
	.byte	0,1,1
.L85:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_ranges'
.L26:
	.word	-1,.L13,0,.L27-.L13,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L11,0,.L28-.L11
	.half	2
	.byte	138,0
	.word	0,0
.L30:
	.word	-1,.L11,0,.L28-.L11
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L31-.L13
	.half	2
	.byte	138,0
	.word	0,0
.L33:
	.word	-1,.L13,0,.L43-.L13
	.half	1
	.byte	100
	.word	0,0
.L35:
	.word	-1,.L13,0,.L44-.L13
	.half	1
	.byte	101
	.word	.L39-.L13,.L31-.L13
	.half	1
	.byte	111
	.word	.L46-.L13,.L47-.L13
	.half	1
	.byte	100
	.word	.L48-.L13,.L4-.L13
	.half	1
	.byte	100
	.word	.L49-.L13,.L5-.L13
	.half	1
	.byte	100
	.word	.L50-.L13,.L6-.L13
	.half	1
	.byte	100
	.word	.L51-.L13,.L7-.L13
	.half	1
	.byte	100
	.word	.L52-.L13,.L8-.L13
	.half	1
	.byte	100
	.word	.L53-.L13,.L9-.L13
	.half	1
	.byte	100
	.word	0,0
.L37:
	.word	-1,.L13,.L45-.L13,.L31-.L13
	.half	1
	.byte	88
	.word	0,0
.L41:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L100:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Pos_initConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L100,.L11,.L28-.L11
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_Pos_printStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L100,.L13,.L31-.L13
	; Module end
