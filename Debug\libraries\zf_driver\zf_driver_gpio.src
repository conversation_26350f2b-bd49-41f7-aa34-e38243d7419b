	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc25912a --dep-file=zf_driver_gpio.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/zf_driver/zf_driver_gpio.src ../libraries/zf_driver/zf_driver_gpio.c"
	.compiler_name		"ctc"
	;source	'../libraries/zf_driver/zf_driver_gpio.c'

	
$TC16X
	
	.sdecl	'.text.zf_driver_gpio.get_port',code,cluster('get_port')
	.sect	'.text.zf_driver_gpio.get_port'
	.align	2
	
	.global	get_port
; Function get_port
.L65:
get_port:	.type	func
	mov.u	d15,#65504
.L253:
	and	d4,d15
.L193:
	mov	d15,#0
	jeq	d15,d4,.L2
.L254:
	mov	d15,#64
	jeq	d15,d4,.L3
.L255:
	mov	d15,#320
	jeq	d15,d4,.L4
.L256:
	mov	d15,#352
	jeq	d15,d4,.L5
.L257:
	mov	d15,#416
	jeq	d15,d4,.L6
.L258:
	mov	d15,#448
	jeq	d15,d4,.L7
.L259:
	mov	d15,#480
	jeq	d15,d4,.L8
.L260:
	mov	d15,#640
	jeq	d15,d4,.L9
.L261:
	mov	d15,#672
	jeq	d15,d4,.L10
.L262:
	mov	d15,#704
	jeq	d15,d4,.L11
.L263:
	mov	d15,#736
	jeq	d15,d4,.L12
.L264:
	mov	d15,#1024
	jeq	d15,d4,.L13
.L265:
	mov	d15,#1056
	jeq	d15,d4,.L14
	j	.L15
.L2:
	movh.a	a2,#61444
.L194:
	lea	a2,[a2]@los(0xf003a000)
.L266:
	j	.L16
.L3:
	movh.a	a2,#61444
.L195:
	lea	a2,[a2]@los(0xf003a200)
.L267:
	j	.L17
.L4:
	movh.a	a2,#61444
.L196:
	lea	a2,[a2]@los(0xf003b000)
.L268:
	j	.L18
.L5:
	movh.a	a2,#61444
.L197:
	lea	a2,[a2]@los(0xf003b100)
.L269:
	j	.L19
.L6:
	movh.a	a2,#61444
.L198:
	lea	a2,[a2]@los(0xf003b300)
.L270:
	j	.L20
.L7:
	movh.a	a2,#61444
.L199:
	lea	a2,[a2]@los(0xf003b400)
.L271:
	j	.L21
.L8:
	movh.a	a2,#61444
.L200:
	lea	a2,[a2]@los(0xf003b500)
.L272:
	j	.L22
.L9:
	movh.a	a2,#61444
.L201:
	lea	a2,[a2]@los(0xf003c000)
.L273:
	j	.L23
.L10:
	movh.a	a2,#61444
.L202:
	lea	a2,[a2]@los(0xf003c100)
.L274:
	j	.L24
.L11:
	movh.a	a2,#61444
.L203:
	lea	a2,[a2]@los(0xf003c200)
.L275:
	j	.L25
.L12:
	movh.a	a2,#61444
.L204:
	lea	a2,[a2]@los(0xf003c300)
.L276:
	j	.L26
.L13:
	movh.a	a2,#61444
.L205:
	lea	a2,[a2]@los(0xf003d200)
.L277:
	j	.L27
.L14:
	movh.a	a2,#61444
.L206:
	lea	a2,[a2]@los(0xf003d300)
.L278:
	j	.L28
.L15:
	j	.L29
.L29:
.L28:
.L27:
.L26:
.L25:
.L24:
.L23:
.L22:
.L21:
.L20:
.L19:
.L18:
.L17:
.L16:
	j	.L30
.L30:
	ret
.L111:
	
__get_port_function_end:
	.size	get_port,__get_port_function_end-get_port
.L84:
	; End of function
	
	.sdecl	'.text.zf_driver_gpio.gpio_set_level',code,cluster('gpio_set_level')
	.sect	'.text.zf_driver_gpio.gpio_set_level'
	.align	2
	
	.global	gpio_set_level
; Function gpio_set_level
.L67:
gpio_set_level:	.type	func
	mov	d15,d4
.L208:
	jeq	d5,#0,.L31
.L283:
	mov	d4,d15
	call	get_port
.L207:
	and	d15,#31
.L120:
	mov	d0,#1
.L128:
	sha	d0,d0,d15
.L284:
	st.w	[a2]4,d0
.L121:
	j	.L32
.L31:
	mov	d4,d15
	call	get_port
.L209:
	and	d15,#31
.L137:
	movh	d0,#1
.L143:
	sha	d0,d0,d15
.L285:
	st.w	[a2]4,d0
.L32:
	ret
.L115:
	
__gpio_set_level_function_end:
	.size	gpio_set_level,__gpio_set_level_function_end-gpio_set_level
.L89:
	; End of function
	
	.sdecl	'.text.zf_driver_gpio.gpio_get_level',code,cluster('gpio_get_level')
	.sect	'.text.zf_driver_gpio.gpio_get_level'
	.align	2
	
	.global	gpio_get_level
; Function gpio_get_level
.L69:
gpio_get_level:	.type	func
	mov	d15,d4
.L211:
	mov	d4,d15
	call	get_port
.L210:
	and	d15,#31
.L147:
	ld.w	d0,[a2]36
	extr.u	d15,d0,d15,#1
.L290:
	jeq	d15,#0,.L33
.L291:
	mov	d2,#1
.L292:
	j	.L34
.L33:
	mov	d2,#0
.L34:
	j	.L35
.L35:
	j	.L36
.L36:
	ret
.L144:
	
__gpio_get_level_function_end:
	.size	gpio_get_level,__gpio_get_level_function_end-gpio_get_level
.L94:
	; End of function
	
	.sdecl	'.text.zf_driver_gpio.gpio_toggle_level',code,cluster('gpio_toggle_level')
	.sect	'.text.zf_driver_gpio.gpio_toggle_level'
	.align	2
	
	.global	gpio_toggle_level
; Function gpio_toggle_level
.L71:
gpio_toggle_level:	.type	func
	mov	d15,d4
.L213:
	mov	d4,d15
	call	get_port
.L212:
	and	d15,#31
.L156:
	mov	d0,#1
	addih	d0,d0,#1
.L163:
	sha	d0,d0,d15
.L297:
	st.w	[a2]4,d0
.L157:
	ret
.L153:
	
__gpio_toggle_level_function_end:
	.size	gpio_toggle_level,__gpio_toggle_level_function_end-gpio_toggle_level
.L99:
	; End of function
	
	.sdecl	'.text.zf_driver_gpio.gpio_set_dir',code,cluster('gpio_set_dir')
	.sect	'.text.zf_driver_gpio.gpio_set_dir'
	.align	2
	
	.global	gpio_set_dir
; Function gpio_set_dir
.L73:
gpio_set_dir:	.type	func
	mov	d8,d4
.L215:
	jne	d5,#0,.L37
.L302:
	mov	d15,#0
	jeq	d15,d6,.L38
.L303:
	mov	d15,#2
	jeq	d15,d6,.L39
	j	.L40
.L38:
	mov	d15,#0
.L216:
	j	.L41
.L39:
	mov	d15,#8
.L217:
	j	.L42
.L40:
	mov	d15,#16
.L218:
	j	.L43
.L43:
.L42:
.L41:
	j	.L44
.L37:
	mov	d15,#4
	jeq	d15,d6,.L45
	j	.L46
.L45:
	mov	d15,#192
.L219:
	j	.L47
.L46:
	mov	d15,#128
.L220:
	j	.L48
.L48:
.L47:
.L44:
	mov	d4,d8
	call	get_port
.L214:
	and	d4,d8,#31
.L304:
	mov.aa	a4,a2
	mov	d5,d15
.L221:
	call	IfxPort_setPinMode
.L222:
	ret
.L167:
	
__gpio_set_dir_function_end:
	.size	gpio_set_dir,__gpio_set_dir_function_end-gpio_set_dir
.L104:
	; End of function
	
	.sdecl	'.text.zf_driver_gpio.gpio_init',code,cluster('gpio_init')
	.sect	'.text.zf_driver_gpio.gpio_init'
	.align	2
	
	.global	gpio_init
; Function gpio_init
.L75:
gpio_init:	.type	func
	mov	e8,d5,d4
	mov	d10,d6
.L224:
	jne	d9,#0,.L49
.L225:
	mov	d15,#0
	jeq	d15,d7,.L50
.L309:
	mov	d15,#2
	jeq	d15,d7,.L51
	j	.L52
.L50:
	mov	d15,#0
.L226:
	j	.L53
.L51:
	mov	d15,#8
.L227:
	j	.L54
.L52:
	mov	d15,#16
.L228:
	j	.L55
.L55:
.L54:
.L53:
	j	.L56
.L49:
	mov	d15,#4
	jeq	d15,d7,.L57
	j	.L58
.L57:
	mov	d15,#192
.L229:
	j	.L59
.L58:
	mov	d15,#128
.L230:
	j	.L60
.L60:
.L59:
.L56:
	mov	d4,d8
.L231:
	call	get_port
.L223:
	and	d4,d8,#31
.L232:
	mov.aa	a4,a2
	mov	d5,d15
.L233:
	call	IfxPort_setPinMode
.L234:
	mov	d4,d8
.L235:
	call	get_port
.L236:
	and	d4,d8,#31
.L237:
	mov	d5,#0
	mov.aa	a4,a2
	call	IfxPort_setPinPadDriver
.L238:
	jne	d9,#1,.L61
.L239:
	jeq	d10,#0,.L62
.L240:
	mov	d4,d8
.L241:
	call	get_port
.L242:
	and	d15,d8,#31
.L181:
	mov	d0,#1
.L185:
	sha	d0,d0,d15
.L310:
	st.w	[a2]4,d0
.L182:
	j	.L63
.L62:
	mov	d4,d8
.L243:
	call	get_port
.L244:
	and	d15,d8,#31
.L189:
	movh	d0,#1
.L192:
	sha	d0,d0,d15
.L311:
	st.w	[a2]4,d0
.L63:
.L61:
	ret
.L175:
	
__gpio_init_function_end:
	.size	gpio_init,__gpio_init_function_end-gpio_init
.L109:
	; End of function
	
	.calls	'gpio_set_level','get_port'
	.calls	'gpio_get_level','get_port'
	.calls	'gpio_toggle_level','get_port'
	.calls	'gpio_set_dir','get_port'
	.calls	'gpio_set_dir','IfxPort_setPinMode'
	.calls	'gpio_init','get_port'
	.calls	'gpio_init','IfxPort_setPinMode'
	.calls	'gpio_init','IfxPort_setPinPadDriver'
	.calls	'get_port','',0
	.calls	'gpio_set_level','',0
	.calls	'gpio_get_level','',0
	.calls	'gpio_toggle_level','',0
	.calls	'gpio_set_dir','',0
	.extern	IfxPort_setPinMode
	.extern	IfxPort_setPinPadDriver
	.calls	'gpio_init','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L77:
	.word	39648
	.half	3
	.word	.L78
	.byte	4
.L76:
	.byte	1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L79
	.byte	2,1,1,3
	.word	202
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	205
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	250
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	262
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	342
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	316
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	348
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	348
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	316
	.byte	6,0
.L117:
	.byte	7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	496
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	812
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1383
	.byte	4,2,35,0,0,14,4
	.word	434
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	434
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	434
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1511
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	434
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	434
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1726
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	434
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	434
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1941
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	434
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	434
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2158
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2378
	.byte	4,2,35,0,0,14,24
	.word	434
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	434
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	434
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	434
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	434
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2701
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	434
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	434
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	434
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	434
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	434
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3005
	.byte	4,2,35,0,0,14,8
	.word	434
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3330
	.byte	4,2,35,0,0,14,12
	.word	434
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3670
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	451
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4036
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4322
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	451
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4638
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4810
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	474
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4985
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5159
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5333
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5509
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5665
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5998
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6346
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6470
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6554
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	434
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6734
	.byte	4,2,35,0,0,14,76
	.word	434
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6987
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	434
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7074
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	772
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1343
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1462
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1502
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1686
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1901
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2118
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2338
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1502
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2652
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2692
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	2965
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3281
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3321
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3621
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3661
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	3996
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4282
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3321
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4429
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4598
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4770
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4945
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5119
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5293
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5469
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5625
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5958
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6306
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3321
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6430
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6679
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6938
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	6978
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7034
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7601
	.byte	4,3,35,252,1,0,16
	.word	7641
.L110:
	.byte	3
	.word	8244
.L146:
	.byte	8
	.byte	'IfxPort_getPinState',0,3,3,172,4,20
	.word	434
	.byte	1,1
.L148:
	.byte	5
	.byte	'port',0,3,172,4,47
	.word	8249
.L150:
	.byte	5
	.byte	'pinIndex',0,3,172,4,59
	.word	434
.L152:
	.byte	6,0
.L119:
	.byte	4
	.byte	'IfxPort_setPinHigh',0,3,3,184,4,17,1,1
.L122:
	.byte	5
	.byte	'port',0,3,184,4,43
	.word	8249
.L124:
	.byte	5
	.byte	'pinIndex',0,3,184,4,55
	.word	434
.L126:
	.byte	17,6,0,0
.L136:
	.byte	4
	.byte	'IfxPort_setPinLow',0,3,3,190,4,17,1,1
.L138:
	.byte	5
	.byte	'port',0,3,190,4,42
	.word	8249
.L140:
	.byte	5
	.byte	'pinIndex',0,3,190,4,54
	.word	434
.L142:
	.byte	17,6,0,0,18,3,172,1,9,4,19
	.byte	'IfxPort_State_notChanged',0,0,19
	.byte	'IfxPort_State_high',0,1,19
	.byte	'IfxPort_State_low',0,128,128,4,19
	.byte	'IfxPort_State_toggled',0,129,128,4,0
.L127:
	.byte	4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1
.L129:
	.byte	5
	.byte	'port',0,3,208,4,44
	.word	8249
.L131:
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	434
.L133:
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8445
.L135:
	.byte	6,0
.L155:
	.byte	4
	.byte	'IfxPort_togglePin',0,3,3,214,4,17,1,1
.L158:
	.byte	5
	.byte	'port',0,3,214,4,42
	.word	8249
.L160:
	.byte	5
	.byte	'pinIndex',0,3,214,4,54
	.word	434
.L162:
	.byte	17,6,0,0,20
	.byte	'__extru',0
	.word	451
	.byte	1,1,1,1,21
	.word	467
	.byte	21
	.word	467
	.byte	21
	.word	467
	.byte	0,22
	.word	210
	.byte	23
	.word	236
	.byte	6,0,22
	.word	271
	.byte	23
	.word	303
	.byte	6,0,22
	.word	353
	.byte	23
	.word	372
	.byte	6,0,22
	.word	388
	.byte	23
	.word	403
	.byte	23
	.word	417
	.byte	6,0,22
	.word	8254
	.byte	23
	.word	8286
	.byte	23
	.word	8300
	.byte	6,0,22
	.word	8320
	.byte	23
	.word	8347
	.byte	23
	.word	8361
	.byte	17,24
	.word	8548
	.byte	23
	.word	8576
	.byte	23
	.word	8590
	.byte	23
	.word	8608
	.byte	25
	.word	8624
	.byte	0,6,0,0,22
	.word	8383
	.byte	23
	.word	8409
	.byte	23
	.word	8423
	.byte	17,24
	.word	8548
	.byte	23
	.word	8576
	.byte	23
	.word	8590
	.byte	23
	.word	8608
	.byte	25
	.word	8624
	.byte	0,6,0,0,22
	.word	8548
	.byte	23
	.word	8576
	.byte	23
	.word	8590
	.byte	23
	.word	8608
	.byte	6,0,22
	.word	8626
	.byte	23
	.word	8652
	.byte	23
	.word	8666
	.byte	17,24
	.word	8548
	.byte	23
	.word	8576
	.byte	23
	.word	8590
	.byte	23
	.word	8608
	.byte	25
	.word	8624
	.byte	0,6,0,0
.L173:
	.byte	18,3,95,9,1,19
	.byte	'IfxPort_Mode_inputNoPullDevice',0,0,19
	.byte	'IfxPort_Mode_inputPullDown',0,8,19
	.byte	'IfxPort_Mode_inputPullUp',0,16,19
	.byte	'IfxPort_Mode_outputPushPullGeneral',0,128,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt1',0,136,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt2',0,144,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt3',0,152,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt4',0,160,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt5',0,168,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt6',0,176,1,19
	.byte	'IfxPort_Mode_outputPushPullAlt7',0,184,1,19
	.byte	'IfxPort_Mode_outputOpenDrainGeneral',0,192,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt1',0,200,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt2',0,208,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt3',0,216,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt4',0,224,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt5',0,232,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt6',0,240,1,19
	.byte	'IfxPort_Mode_outputOpenDrainAlt7',0,248,1,0,26
	.byte	'IfxPort_setPinMode',0,3,247,2,17,1,1,1,1,5
	.byte	'port',0,3,247,2,43
	.word	8249
	.byte	5
	.byte	'pinIndex',0,3,247,2,55
	.word	434
	.byte	5
	.byte	'mode',0,3,247,2,78
	.word	8948
	.byte	0,18,3,144,1,9,1,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,19
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,19
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,19
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,19
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,19
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,19
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,19
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,19
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,19
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,26
	.byte	'IfxPort_setPinPadDriver',0,3,134,3,17,1,1,1,1,5
	.byte	'port',0,3,134,3,48
	.word	8249
	.byte	5
	.byte	'pinIndex',0,3,134,3,60
	.word	434
	.byte	5
	.byte	'padDriver',0,3,134,3,88
	.word	9692
	.byte	0
.L112:
	.byte	18,5,42,9,2,19
	.byte	'P00_0',0,0,19
	.byte	'P00_1',0,1,19
	.byte	'P00_2',0,2,19
	.byte	'P00_3',0,3,19
	.byte	'P00_4',0,4,19
	.byte	'P00_5',0,5,19
	.byte	'P00_6',0,6,19
	.byte	'P00_7',0,7,19
	.byte	'P00_8',0,8,19
	.byte	'P00_9',0,9,19
	.byte	'P00_10',0,10,19
	.byte	'P00_11',0,11,19
	.byte	'P00_12',0,12,19
	.byte	'P00_13',0,13,19
	.byte	'P00_14',0,14,19
	.byte	'P00_15',0,15,19
	.byte	'P02_0',0,192,0,19
	.byte	'P02_1',0,193,0,19
	.byte	'P02_2',0,194,0,19
	.byte	'P02_3',0,195,0,19
	.byte	'P02_4',0,196,0,19
	.byte	'P02_5',0,197,0,19
	.byte	'P02_6',0,198,0,19
	.byte	'P02_7',0,199,0,19
	.byte	'P02_8',0,200,0,19
	.byte	'P02_9',0,201,0,19
	.byte	'P02_10',0,202,0,19
	.byte	'P02_11',0,203,0,19
	.byte	'P02_12',0,204,0,19
	.byte	'P02_13',0,205,0,19
	.byte	'P02_14',0,206,0,19
	.byte	'P02_15',0,207,0,19
	.byte	'P10_0',0,192,2,19
	.byte	'P10_1',0,193,2,19
	.byte	'P10_2',0,194,2,19
	.byte	'P10_3',0,195,2,19
	.byte	'P10_4',0,196,2,19
	.byte	'P10_5',0,197,2,19
	.byte	'P10_6',0,198,2,19
	.byte	'P10_7',0,199,2,19
	.byte	'P10_8',0,200,2,19
	.byte	'P10_9',0,201,2,19
	.byte	'P10_10',0,202,2,19
	.byte	'P10_11',0,203,2,19
	.byte	'P10_12',0,204,2,19
	.byte	'P10_13',0,205,2,19
	.byte	'P10_14',0,206,2,19
	.byte	'P10_15',0,207,2,19
	.byte	'P11_0',0,224,2,19
	.byte	'P11_1',0,225,2,19
	.byte	'P11_2',0,226,2,19
	.byte	'P11_3',0,227,2,19
	.byte	'P11_4',0,228,2,19
	.byte	'P11_5',0,229,2,19
	.byte	'P11_6',0,230,2,19
	.byte	'P11_7',0,231,2,19
	.byte	'P11_8',0,232,2,19
	.byte	'P11_9',0,233,2,19
	.byte	'P11_10',0,234,2,19
	.byte	'P11_11',0,235,2,19
	.byte	'P11_12',0,236,2,19
	.byte	'P11_13',0,237,2,19
	.byte	'P11_14',0,238,2,19
	.byte	'P11_15',0,239,2,19
	.byte	'P13_0',0,160,3,19
	.byte	'P13_1',0,161,3,19
	.byte	'P13_2',0,162,3,19
	.byte	'P13_3',0,163,3,19
	.byte	'P13_4',0,164,3,19
	.byte	'P13_5',0,165,3,19
	.byte	'P13_6',0,166,3,19
	.byte	'P13_7',0,167,3,19
	.byte	'P13_8',0,168,3,19
	.byte	'P13_9',0,169,3,19
	.byte	'P13_10',0,170,3,19
	.byte	'P13_11',0,171,3,19
	.byte	'P13_12',0,172,3,19
	.byte	'P13_13',0,173,3,19
	.byte	'P13_14',0,174,3,19
	.byte	'P13_15',0,175,3,19
	.byte	'P14_0',0,192,3,19
	.byte	'P14_1',0,193,3,19
	.byte	'P14_2',0,194,3,19
	.byte	'P14_3',0,195,3,19
	.byte	'P14_4',0,196,3,19
	.byte	'P14_5',0,197,3,19
	.byte	'P14_6',0,198,3,19
	.byte	'P14_7',0,199,3,19
	.byte	'P14_8',0,200,3,19
	.byte	'P14_9',0,201,3,19
	.byte	'P14_10',0,202,3,19
	.byte	'P14_11',0,203,3,19
	.byte	'P14_12',0,204,3,19
	.byte	'P14_13',0,205,3,19
	.byte	'P14_14',0,206,3,19
	.byte	'P14_15',0,207,3,19
	.byte	'P15_0',0,224,3,19
	.byte	'P15_1',0,225,3,19
	.byte	'P15_2',0,226,3,19
	.byte	'P15_3',0,227,3,19
	.byte	'P15_4',0,228,3,19
	.byte	'P15_5',0,229,3,19
	.byte	'P15_6',0,230,3,19
	.byte	'P15_7',0,231,3,19
	.byte	'P15_8',0,232,3,19
	.byte	'P15_9',0,233,3,19
	.byte	'P15_10',0,234,3,19
	.byte	'P15_11',0,235,3,19
	.byte	'P15_12',0,236,3,19
	.byte	'P15_13',0,237,3,19
	.byte	'P15_14',0,238,3,19
	.byte	'P15_15',0,239,3,19
	.byte	'P20_0',0,128,5,19
	.byte	'P20_1',0,129,5,19
	.byte	'P20_2',0,130,5,19
	.byte	'P20_3',0,131,5,19
	.byte	'P20_4',0,132,5,19
	.byte	'P20_5',0,133,5,19
	.byte	'P20_6',0,134,5,19
	.byte	'P20_7',0,135,5,19
	.byte	'P20_8',0,136,5,19
	.byte	'P20_9',0,137,5,19
	.byte	'P20_10',0,138,5,19
	.byte	'P20_11',0,139,5,19
	.byte	'P20_12',0,140,5,19
	.byte	'P20_13',0,141,5,19
	.byte	'P20_14',0,142,5,19
	.byte	'P20_15',0,143,5,19
	.byte	'P21_0',0,160,5,19
	.byte	'P21_1',0,161,5,19
	.byte	'P21_2',0,162,5,19
	.byte	'P21_3',0,163,5,19
	.byte	'P21_4',0,164,5,19
	.byte	'P21_5',0,165,5,19
	.byte	'P21_6',0,166,5,19
	.byte	'P21_7',0,167,5,19
	.byte	'P21_8',0,168,5,19
	.byte	'P21_9',0,169,5,19
	.byte	'P21_10',0,170,5,19
	.byte	'P21_11',0,171,5,19
	.byte	'P21_12',0,172,5,19
	.byte	'P21_13',0,173,5,19
	.byte	'P21_14',0,174,5,19
	.byte	'P21_15',0,175,5,19
	.byte	'P22_0',0,192,5,19
	.byte	'P22_1',0,193,5,19
	.byte	'P22_2',0,194,5,19
	.byte	'P22_3',0,195,5,19
	.byte	'P22_4',0,196,5,19
	.byte	'P22_5',0,197,5,19
	.byte	'P22_6',0,198,5,19
	.byte	'P22_7',0,199,5,19
	.byte	'P22_8',0,200,5,19
	.byte	'P22_9',0,201,5,19
	.byte	'P22_10',0,202,5,19
	.byte	'P22_11',0,203,5,19
	.byte	'P22_12',0,204,5,19
	.byte	'P22_13',0,205,5,19
	.byte	'P22_14',0,206,5,19
	.byte	'P22_15',0,207,5,19
	.byte	'P23_0',0,224,5,19
	.byte	'P23_1',0,225,5,19
	.byte	'P23_2',0,226,5,19
	.byte	'P23_3',0,227,5,19
	.byte	'P23_4',0,228,5,19
	.byte	'P23_5',0,229,5,19
	.byte	'P23_6',0,230,5,19
	.byte	'P23_7',0,231,5,19
	.byte	'P23_8',0,232,5,19
	.byte	'P23_9',0,233,5,19
	.byte	'P23_10',0,234,5,19
	.byte	'P23_11',0,235,5,19
	.byte	'P23_12',0,236,5,19
	.byte	'P23_13',0,237,5,19
	.byte	'P23_14',0,238,5,19
	.byte	'P23_15',0,239,5,19
	.byte	'P32_0',0,128,8,19
	.byte	'P32_1',0,129,8,19
	.byte	'P32_2',0,130,8,19
	.byte	'P32_3',0,131,8,19
	.byte	'P32_4',0,132,8,19
	.byte	'P32_5',0,133,8,19
	.byte	'P32_6',0,134,8,19
	.byte	'P32_7',0,135,8,19
	.byte	'P32_8',0,136,8,19
	.byte	'P32_9',0,137,8,19
	.byte	'P32_10',0,138,8,19
	.byte	'P32_11',0,139,8,19
	.byte	'P32_12',0,140,8,19
	.byte	'P32_13',0,141,8,19
	.byte	'P32_14',0,142,8,19
	.byte	'P32_15',0,143,8,19
	.byte	'P33_0',0,160,8,19
	.byte	'P33_1',0,161,8,19
	.byte	'P33_2',0,162,8,19
	.byte	'P33_3',0,163,8,19
	.byte	'P33_4',0,164,8,19
	.byte	'P33_5',0,165,8,19
	.byte	'P33_6',0,166,8,19
	.byte	'P33_7',0,167,8,19
	.byte	'P33_8',0,168,8,19
	.byte	'P33_9',0,169,8,19
	.byte	'P33_10',0,170,8,19
	.byte	'P33_11',0,171,8,19
	.byte	'P33_12',0,172,8,19
	.byte	'P33_13',0,173,8,19
	.byte	'P33_14',0,174,8,19
	.byte	'P33_15',0,175,8,0
.L169:
	.byte	18,5,91,9,1,19
	.byte	'GPI',0,0,19
	.byte	'GPO',0,1,0
.L171:
	.byte	18,5,103,9,1,19
	.byte	'GPI_FLOATING_IN',0,0,19
	.byte	'GPI_PULL_UP',0,1,19
	.byte	'GPI_PULL_DOWN',0,2,19
	.byte	'GPO_PUSH_PULL',0,3,19
	.byte	'GPO_OPEN_DTAIN',0,4,0,7
	.byte	'short int',0,2,5,27
	.byte	'__wchar_t',0,6,1,1
	.word	12237
	.byte	27
	.byte	'__size_t',0,6,1,1
	.word	451
	.byte	27
	.byte	'__ptrdiff_t',0,6,1,1
	.word	467
	.byte	28,1,3
	.word	12305
	.byte	27
	.byte	'__codeptr',0,6,1,1
	.word	12307
	.byte	27
	.byte	'__intptr_t',0,6,1,1
	.word	467
	.byte	27
	.byte	'__uintptr_t',0,6,1,1
	.word	451
	.byte	27
	.byte	'boolean',0,7,101,29
	.word	434
	.byte	27
	.byte	'uint8',0,7,105,29
	.word	434
	.byte	27
	.byte	'uint16',0,7,109,29
	.word	474
	.byte	7
	.byte	'unsigned long int',0,4,7,27
	.byte	'uint32',0,7,113,29
	.word	12414
	.byte	27
	.byte	'uint64',0,7,118,29
	.word	316
	.byte	27
	.byte	'sint16',0,7,126,29
	.word	12237
	.byte	7
	.byte	'long int',0,4,5,27
	.byte	'sint32',0,7,131,1,29
	.word	12480
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,7,138,1,29
	.word	12508
	.byte	27
	.byte	'float32',0,7,167,1,29
	.word	262
	.byte	27
	.byte	'pvoid',0,8,57,28
	.word	348
	.byte	27
	.byte	'Ifx_TickTime',0,8,79,28
	.word	12508
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7074
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	6987
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3330
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1383
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2378
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1511
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2158
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1726
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1941
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6346
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6470
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6554
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6734
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	4985
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5509
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5159
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5333
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	5998
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	812
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4322
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4810
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4469
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4638
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5665
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	496
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4036
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3670
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2701
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3005
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7601
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7034
	.byte	27
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3621
	.byte	27
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1462
	.byte	27
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2652
	.byte	27
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1686
	.byte	27
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2338
	.byte	27
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1901
	.byte	27
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2118
	.byte	27
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6430
	.byte	27
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6679
	.byte	27
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6938
	.byte	27
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6306
	.byte	27
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5119
	.byte	27
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5625
	.byte	27
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5293
	.byte	27
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5469
	.byte	27
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1343
	.byte	27
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5958
	.byte	27
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4429
	.byte	27
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4945
	.byte	27
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4598
	.byte	27
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4770
	.byte	27
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	772
	.byte	27
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4282
	.byte	27
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	3996
	.byte	27
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	2965
	.byte	27
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3281
	.byte	16
	.word	7641
	.byte	27
	.byte	'Ifx_P',0,4,139,6,3
	.word	13911
	.byte	18,9,250,10,9,1,19
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,19
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,19
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	13931
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	14053
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	14610
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	451
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	14687
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	434
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	434
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	434
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	434
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	434
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	434
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	434
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	14823
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	434
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	434
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	434
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	434
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	434
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	434
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	15103
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	15341
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	434
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	434
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	434
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	434
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	434
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	15469
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	434
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	434
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	434
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	434
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	434
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	15712
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	15947
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	434
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	451
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	16075
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	434
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	451
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	16175
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	434
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	434
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	434
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	434
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	434
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	16275
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	451
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	16483
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	434
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	474
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	16648
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	434
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	16831
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	434
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	434
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	451
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	434
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	434
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	16985
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	17349
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	474
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	434
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	434
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	434
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	17560
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	451
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	17812
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	17930
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	18041
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	451
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	18204
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	18367
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	18525
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	434
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	434
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	434
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	434
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	434
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	434
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	434
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	434
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	474
	.byte	10,0,2,35,2,0,27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	18690
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	434
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	434
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	474
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	434
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	19019
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	19240
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	19403
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	19675
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	19828
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	19984
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	20146
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	20289
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	20454
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	434
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	20599
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	434
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	20780
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	20954
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	451
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	21114
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	451
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	21258
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	21532
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	21671
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	434
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	474
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	434
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	434
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	434
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	21834
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	474
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	434
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	474
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	22052
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	22215
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	22551
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	434
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	22658
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	23110
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	434
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	23209
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	474
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	23359
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	451
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	23508
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	451
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	23669
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	474
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	23799
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	23931
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	434
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	474
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	24046
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	474
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	474
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	24157
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	434
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	434
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	434
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	434
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	24315
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	24727
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	474
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	6,0,2,35,3,0,27
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	24828
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	451
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	25095
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	25231
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	434
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	434
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	25342
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	25475
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	434
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	434
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	25678
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	434
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	434
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	434
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	26034
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	474
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	26212
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	474
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	434
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	434
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	434
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	26312
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	434
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	434
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	434
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	26682
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	451
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	26868
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	451
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	27066
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	434
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	451
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	27299
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	434
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	434
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	434
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	434
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	434
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	434
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	27451
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	434
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	434
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	434
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	434
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	28018
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	434
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	434
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	434
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	434
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	434
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	434
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	434
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	28312
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	434
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	434
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	434
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	434
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	434
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	474
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	434
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	434
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	28590
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	474
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	29086
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	474
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	434
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	434
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	434
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	29399
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	434
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	434
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	434
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	434
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	434
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	434
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	434
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	29608
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	434
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	434
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	434
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	434
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	434
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	434
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	434
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	29819
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	451
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	30251
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	434
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	434
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	434
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	434
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	434
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	434
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	434
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	434
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	434
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	434
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	434
	.byte	7,0,2,35,3,0,27
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	30347
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	451
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	30607
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	434
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	434
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	451
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	30732
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	30929
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	31082
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	31235
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	451
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	31388
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	31543
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	31543
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	31543
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	31543
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	31559
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	434
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	434
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	31689
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	434
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	31927
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	31543
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	31543
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	31543
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	31543
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	32150
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	434
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	32276
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	434
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	434
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	434
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	434
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	434
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	434
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	434
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	434
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	434
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	434
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	474
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	32528
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14053
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	32747
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14610
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	32811
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14687
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	32875
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14823
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	32940
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15103
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	33005
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15341
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	33070
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15469
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	33135
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15712
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	33200
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15947
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	33265
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16075
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	33330
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16175
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	33395
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16275
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	33460
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16483
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	33524
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16648
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	33588
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16831
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	33652
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16985
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	33717
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17349
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	33779
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17560
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	33841
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17812
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	33903
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17930
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	33967
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18041
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	34032
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18204
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	34098
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18367
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	34164
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18525
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	34232
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18690
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	34299
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19019
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	34367
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19240
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	34435
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19403
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	34501
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19675
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	34568
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19828
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	34637
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19984
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	34706
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20146
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	34775
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20289
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	34844
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20454
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	34913
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20599
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	34982
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20780
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	35050
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20954
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	35118
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21114
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	35186
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21258
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	35254
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21532
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	35319
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21671
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	35384
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21834
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	35450
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22052
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	35514
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22215
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	35575
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22551
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	35636
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22658
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	35696
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23110
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	35758
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23209
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	35818
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23359
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	35880
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23508
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	35948
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23669
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	36016
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23799
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	36084
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23931
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	36148
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24046
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	36213
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24157
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	36276
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24315
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	36337
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24727
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	36401
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24828
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	36462
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25095
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	36526
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25231
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	36593
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25342
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	36656
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25475
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	36717
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25678
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	36779
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26034
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	36844
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26212
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	36909
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26312
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	36974
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26682
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	37043
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26868
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	37112
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27066
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	37181
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27299
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	37246
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27451
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	37309
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28018
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	37374
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28312
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	37439
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28590
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	37504
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29086
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	37570
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29608
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	37639
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29399
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	37703
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29819
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	37768
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30251
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	37833
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30347
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	37898
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30607
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	37962
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30732
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	38028
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30929
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	38092
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31082
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	38157
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31235
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	38222
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31388
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	38287
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31559
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	38353
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31689
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	38422
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31927
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	38491
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32150
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	38558
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32276
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	38625
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	451
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	467
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32528
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	38692
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	38353
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	38422
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	38491
	.byte	4,2,35,8,0,16
	.word	38757
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	38820
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	38558
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	38625
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	38692
	.byte	4,2,35,8,0,16
	.word	38849
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	38910
	.byte	18,3,83,9,1,19
	.byte	'IfxPort_InputMode_undefined',0,127,19
	.byte	'IfxPort_InputMode_noPullDevice',0,0,19
	.byte	'IfxPort_InputMode_pullDown',0,8,19
	.byte	'IfxPort_InputMode_pullUp',0,16,0,27
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	38937
	.byte	27
	.byte	'IfxPort_Mode',0,3,116,3
	.word	8948
	.byte	18,3,120,9,1,19
	.byte	'IfxPort_OutputIdx_general',0,128,1,19
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,19
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,19
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,19
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,19
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,19
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,19
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,27
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	39109
	.byte	18,3,134,1,9,1,19
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,19
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,27
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	39353
	.byte	27
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	9692
	.byte	27
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8445
	.byte	27
	.byte	'_iob_flag_t',0,11,82,25
	.word	474
	.byte	7
	.byte	'char',0,1,6,27
	.byte	'int8',0,12,54,29
	.word	39521
	.byte	27
	.byte	'int16',0,12,55,29
	.word	12237
	.byte	27
	.byte	'int32',0,12,56,29
	.word	467
	.byte	27
	.byte	'int64',0,12,57,29
	.word	12508
	.byte	27
	.byte	'gpio_pin_enum',0,5,89,2
	.word	10192
	.byte	27
	.byte	'gpio_dir_enum',0,5,95,2
	.word	12132
	.byte	27
	.byte	'gpio_mode_enum',0,5,111,2
	.word	12150
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L78:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,11,1,0,0,18,4
	.byte	1,58,15,59,15,57,15,11,15,0,0,19,40,0,3,8,28,13,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21,5
	.byte	0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,29,1,49,19,0,0,25,11,0,49,19,0,0,26,46,1,3,8,58,15,59
	.byte	15,57,15,54,15,39,12,63,12,60,12,0,0,27,22,0,3,8,58,15,59,15,57,15,73,19,0,0,28,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L79:
	.word	.L246-.L245
.L245:
	.half	3
	.word	.L248-.L247
.L247:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	'F:\\ADS\\AURIX-Studio-1.10.2\\tools\\Compilers\\Tasking_1.1r8\\ctc\\include\\',0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'IFXPORT.h',0,1,0,0
	.byte	'IfxPort_regdef.h',0,2,0,0
	.byte	'..\\libraries\\zf_driver\\zf_driver_gpio.h',0,0,0,0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,2,0,0
	.byte	'stdio.h',0,3,0,0
	.byte	'zf_common_typedef.h',0,4,0,0,0
.L248:
.L246:
	.sdecl	'.debug_info',debug,cluster('get_port')
	.sect	'.debug_info'
.L80:
	.word	284
	.half	3
	.word	.L81
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L83,.L82
	.byte	2
	.word	.L76
	.byte	3
	.byte	'get_port',0,1,45,8
	.word	.L110
	.byte	1,1,1
	.word	.L65,.L111,.L64
	.byte	4
	.byte	'pin',0,1,45,32
	.word	.L112,.L113
	.byte	5
	.word	.L65,.L111
	.byte	6
	.byte	'port',0,1,47,21
	.word	.L110,.L114
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('get_port')
	.sect	'.debug_abbrev'
.L81:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('get_port')
	.sect	'.debug_line'
.L82:
	.word	.L250-.L249
.L249:
	.half	3
	.word	.L252-.L251
.L251:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0,0
.L252:
	.byte	5,16,7,0,5,2
	.word	.L65
	.byte	3,48,1,5,15,9
	.half	.L253-.L65
	.byte	1,5,14,9
	.half	.L193-.L253
	.byte	3,2,1,9
	.half	.L254-.L193
	.byte	3,1,1,9
	.half	.L255-.L254
	.byte	3,1,1,9
	.half	.L256-.L255
	.byte	3,1,1,9
	.half	.L257-.L256
	.byte	3,1,1,9
	.half	.L258-.L257
	.byte	3,1,1,9
	.half	.L259-.L258
	.byte	3,1,1,9
	.half	.L260-.L259
	.byte	3,1,1,9
	.half	.L261-.L260
	.byte	3,1,1,9
	.half	.L262-.L261
	.byte	3,1,1,9
	.half	.L263-.L262
	.byte	3,1,1,9
	.half	.L264-.L263
	.byte	3,1,1,9
	.half	.L265-.L264
	.byte	3,1,1,5,29,9
	.half	.L2-.L265
	.byte	3,116,1,5,41,9
	.half	.L266-.L2
	.byte	1,5,29,9
	.half	.L3-.L266
	.byte	3,1,1,5,41,9
	.half	.L267-.L3
	.byte	1,5,29,9
	.half	.L4-.L267
	.byte	3,1,1,5,41,9
	.half	.L268-.L4
	.byte	1,5,29,9
	.half	.L5-.L268
	.byte	3,1,1,5,41,9
	.half	.L269-.L5
	.byte	1,5,29,9
	.half	.L6-.L269
	.byte	3,1,1,5,41,9
	.half	.L270-.L6
	.byte	1,5,29,9
	.half	.L7-.L270
	.byte	3,1,1,5,41,9
	.half	.L271-.L7
	.byte	1,5,29,9
	.half	.L8-.L271
	.byte	3,1,1,5,41,9
	.half	.L272-.L8
	.byte	1,5,29,9
	.half	.L9-.L272
	.byte	3,1,1,5,41,9
	.half	.L273-.L9
	.byte	1,5,29,9
	.half	.L10-.L273
	.byte	3,1,1,5,41,9
	.half	.L274-.L10
	.byte	1,5,29,9
	.half	.L11-.L274
	.byte	3,1,1,5,41,9
	.half	.L275-.L11
	.byte	1,5,29,9
	.half	.L12-.L275
	.byte	3,1,1,5,41,9
	.half	.L276-.L12
	.byte	1,5,29,9
	.half	.L13-.L276
	.byte	3,1,1,5,41,9
	.half	.L277-.L13
	.byte	1,5,29,9
	.half	.L14-.L277
	.byte	3,1,1,5,41,9
	.half	.L278-.L14
	.byte	1,5,17,9
	.half	.L15-.L278
	.byte	3,1,1,5,5,9
	.half	.L16-.L15
	.byte	3,3,1,5,1,9
	.half	.L30-.L16
	.byte	3,2,1,7,9
	.half	.L84-.L30
	.byte	0,1,1
.L250:
	.sdecl	'.debug_ranges',debug,cluster('get_port')
	.sect	'.debug_ranges'
.L83:
	.word	-1,.L65,0,.L84-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('gpio_set_level')
	.sect	'.debug_info'
.L85:
	.word	485
	.half	3
	.word	.L86
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L88,.L87
	.byte	2
	.word	.L76
	.byte	3
	.byte	'gpio_set_level',0,1,79,6,1,1,1
	.word	.L67,.L115,.L66
	.byte	4
	.byte	'pin',0,1,79,36
	.word	.L112,.L116
	.byte	4
	.byte	'dat',0,1,79,47
	.word	.L117,.L118
	.byte	5
	.word	.L67,.L115
	.byte	6
	.word	.L119,.L120,.L121
	.byte	7
	.word	.L122,.L123
	.byte	7
	.word	.L124,.L125
	.byte	8
	.word	.L126,.L120,.L121
	.byte	6
	.word	.L127,.L128,.L121
	.byte	7
	.word	.L129,.L130
	.byte	7
	.word	.L131,.L132
	.byte	7
	.word	.L133,.L134
	.byte	9
	.word	.L135,.L128,.L121
	.byte	0,0,0,6
	.word	.L136,.L137,.L32
	.byte	7
	.word	.L138,.L139
	.byte	7
	.word	.L140,.L141
	.byte	8
	.word	.L142,.L137,.L32
	.byte	6
	.word	.L127,.L143,.L32
	.byte	7
	.word	.L129,.L130
	.byte	7
	.word	.L131,.L132
	.byte	7
	.word	.L133,.L134
	.byte	9
	.word	.L135,.L143,.L32
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gpio_set_level')
	.sect	'.debug_abbrev'
.L86:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('gpio_set_level')
	.sect	'.debug_line'
.L87:
	.word	.L280-.L279
.L279:
	.half	3
	.word	.L282-.L281
.L281:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0
	.byte	'IFXPORT.h',0,1,0,0,0
.L282:
	.byte	5,6,7,0,5,2
	.word	.L67
	.byte	3,206,0,1,5,5,9
	.half	.L208-.L67
	.byte	3,2,1,5,37,7,9
	.half	.L283-.L208
	.byte	3,2,1,5,46,9
	.half	.L207-.L283
	.byte	1,4,2,5,41,9
	.half	.L120-.L207
	.byte	3,231,3,1,5,26,9
	.half	.L128-.L120
	.byte	3,24,1,5,17,9
	.half	.L284-.L128
	.byte	1,4,1,5,36,9
	.half	.L121-.L284
	.byte	3,129,124,1,9
	.half	.L31-.L121
	.byte	3,4,1,5,45,9
	.half	.L209-.L31
	.byte	1,4,2,5,41,9
	.half	.L137-.L209
	.byte	3,233,3,1,5,26,9
	.half	.L143-.L137
	.byte	3,18,1,5,17,9
	.half	.L285-.L143
	.byte	1,4,1,5,1,9
	.half	.L32-.L285
	.byte	3,135,124,1,7,9
	.half	.L89-.L32
	.byte	0,1,1
.L280:
	.sdecl	'.debug_ranges',debug,cluster('gpio_set_level')
	.sect	'.debug_ranges'
.L88:
	.word	-1,.L67,0,.L89-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('gpio_get_level')
	.sect	'.debug_info'
.L90:
	.word	318
	.half	3
	.word	.L91
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L93,.L92
	.byte	2
	.word	.L76
	.byte	3
	.byte	'gpio_get_level',0,1,97,7
	.word	.L117
	.byte	1,1,1
	.word	.L69,.L144,.L68
	.byte	4
	.byte	'pin',0,1,97,37
	.word	.L112,.L145
	.byte	5
	.word	.L69,.L144
	.byte	6
	.word	.L146,.L147,.L35
	.byte	7
	.word	.L148,.L149
	.byte	7
	.word	.L150,.L151
	.byte	8
	.word	.L152,.L147,.L35
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gpio_get_level')
	.sect	'.debug_abbrev'
.L91:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	29,1,49,16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('gpio_get_level')
	.sect	'.debug_line'
.L92:
	.word	.L287-.L286
.L286:
	.half	3
	.word	.L289-.L288
.L288:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0
	.byte	'IFXPORT.h',0,1,0,0,0
.L289:
	.byte	5,7,7,0,5,2
	.word	.L69
	.byte	3,224,0,1,5,41,9
	.half	.L211-.L69
	.byte	3,2,1,5,50,9
	.half	.L210-.L211
	.byte	1,4,2,5,13,9
	.half	.L147-.L210
	.byte	3,203,3,1,5,12,9
	.half	.L290-.L147
	.byte	1,5,51,7,9
	.half	.L291-.L290
	.byte	1,5,58,9
	.half	.L292-.L291
	.byte	1,5,51,9
	.half	.L33-.L292
	.byte	1,5,5,9
	.half	.L34-.L33
	.byte	1,4,1,9
	.half	.L35-.L34
	.byte	3,181,124,1,5,1,9
	.half	.L36-.L35
	.byte	3,1,1,7,9
	.half	.L94-.L36
	.byte	0,1,1
.L287:
	.sdecl	'.debug_ranges',debug,cluster('gpio_get_level')
	.sect	'.debug_ranges'
.L93:
	.word	-1,.L69,0,.L94-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('gpio_toggle_level')
	.sect	'.debug_info'
.L95:
	.word	372
	.half	3
	.word	.L96
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L98,.L97
	.byte	2
	.word	.L76
	.byte	3
	.byte	'gpio_toggle_level',0,1,109,6,1,1,1
	.word	.L71,.L153,.L70
	.byte	4
	.byte	'pin',0,1,109,39
	.word	.L112,.L154
	.byte	5
	.word	.L71,.L153
	.byte	6
	.word	.L155,.L156,.L157
	.byte	7
	.word	.L158,.L159
	.byte	7
	.word	.L160,.L161
	.byte	8
	.word	.L162,.L156,.L157
	.byte	6
	.word	.L127,.L163,.L157
	.byte	7
	.word	.L129,.L164
	.byte	7
	.word	.L131,.L165
	.byte	7
	.word	.L133,.L166
	.byte	9
	.word	.L135,.L163,.L157
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gpio_toggle_level')
	.sect	'.debug_abbrev'
.L96:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('gpio_toggle_level')
	.sect	'.debug_line'
.L97:
	.word	.L294-.L293
.L293:
	.half	3
	.word	.L296-.L295
.L295:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0
	.byte	'IFXPORT.h',0,1,0,0,0
.L296:
	.byte	5,6,7,0,5,2
	.word	.L71
	.byte	3,236,0,1,5,32,9
	.half	.L213-.L71
	.byte	3,2,1,5,41,9
	.half	.L212-.L213
	.byte	1,4,2,9
	.half	.L156-.L212
	.byte	3,233,3,1,5,26,9
	.half	.L163-.L156
	.byte	3,122,1,5,17,9
	.half	.L297-.L163
	.byte	1,4,1,5,1,9
	.half	.L157-.L297
	.byte	3,158,124,1,7,9
	.half	.L99-.L157
	.byte	0,1,1
.L294:
	.sdecl	'.debug_ranges',debug,cluster('gpio_toggle_level')
	.sect	'.debug_ranges'
.L98:
	.word	-1,.L71,0,.L99-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('gpio_set_dir')
	.sect	'.debug_info'
.L100:
	.word	325
	.half	3
	.word	.L101
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L103,.L102
	.byte	2
	.word	.L76
	.byte	3
	.byte	'gpio_set_dir',0,1,123,6,1,1,1
	.word	.L73,.L167,.L72
	.byte	4
	.byte	'pin',0,1,123,34
	.word	.L112,.L168
	.byte	4
	.byte	'dir',0,1,123,53
	.word	.L169,.L170
	.byte	4
	.byte	'pinmode',0,1,123,73
	.word	.L171,.L172
	.byte	5
	.word	.L73,.L167
	.byte	6
	.byte	'port_mode',0,1,125,18
	.word	.L173,.L174
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gpio_set_dir')
	.sect	'.debug_abbrev'
.L101:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('gpio_set_dir')
	.sect	'.debug_line'
.L102:
	.word	.L299-.L298
.L298:
	.half	3
	.word	.L301-.L300
.L300:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0,0
.L301:
	.byte	5,6,7,0,5,2
	.word	.L73
	.byte	3,250,0,1,5,5,9
	.half	.L215-.L73
	.byte	3,3,1,5,18,7,9
	.half	.L302-.L215
	.byte	3,4,1,9
	.half	.L303-.L302
	.byte	3,1,1,5,44,9
	.half	.L38-.L303
	.byte	3,127,1,5,85,9
	.half	.L216-.L38
	.byte	1,5,44,9
	.half	.L39-.L216
	.byte	3,1,1,5,85,9
	.half	.L217-.L39
	.byte	1,5,44,9
	.half	.L40-.L217
	.byte	3,1,1,5,85,9
	.half	.L218-.L40
	.byte	1,5,5,9
	.half	.L41-.L218
	.byte	3,2,1,5,18,9
	.half	.L37-.L41
	.byte	3,5,1,5,44,9
	.half	.L45-.L37
	.byte	1,5,85,9
	.half	.L219-.L45
	.byte	1,5,44,9
	.half	.L46-.L219
	.byte	3,1,1,5,85,9
	.half	.L220-.L46
	.byte	1,5,33,9
	.half	.L44-.L220
	.byte	3,4,1,5,42,9
	.half	.L214-.L44
	.byte	1,5,49,9
	.half	.L304-.L214
	.byte	1,5,1,9
	.half	.L222-.L304
	.byte	3,1,1,7,9
	.half	.L104-.L222
	.byte	0,1,1
.L299:
	.sdecl	'.debug_ranges',debug,cluster('gpio_set_dir')
	.sect	'.debug_ranges'
.L103:
	.word	-1,.L73,0,.L104-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('gpio_init')
	.sect	'.debug_info'
.L105:
	.word	544
	.half	3
	.word	.L106
	.byte	4,1
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L108,.L107
	.byte	2
	.word	.L76
	.byte	3
	.byte	'gpio_init',0,1,157,1,6,1,1,1
	.word	.L75,.L175,.L74
	.byte	4
	.byte	'pin',0,1,157,1,31
	.word	.L112,.L176
	.byte	4
	.byte	'dir',0,1,157,1,50
	.word	.L169,.L177
	.byte	4
	.byte	'dat',0,1,157,1,61
	.word	.L117,.L178
	.byte	4
	.byte	'pinmode',0,1,157,1,81
	.word	.L171,.L179
	.byte	5
	.word	.L75,.L175
	.byte	6
	.byte	'port_mode',0,1,159,1,18
	.word	.L173,.L180
	.byte	7
	.word	.L119,.L181,.L182
	.byte	8
	.word	.L122,.L183
	.byte	8
	.word	.L124,.L184
	.byte	9
	.word	.L126,.L181,.L182
	.byte	7
	.word	.L127,.L185,.L182
	.byte	8
	.word	.L129,.L186
	.byte	8
	.word	.L131,.L187
	.byte	8
	.word	.L133,.L188
	.byte	10
	.word	.L135,.L185,.L182
	.byte	0,0,0,7
	.word	.L136,.L189,.L61
	.byte	8
	.word	.L138,.L190
	.byte	8
	.word	.L140,.L191
	.byte	9
	.word	.L142,.L189,.L61
	.byte	7
	.word	.L127,.L192,.L61
	.byte	8
	.word	.L129,.L186
	.byte	8
	.word	.L131,.L187
	.byte	8
	.word	.L133,.L188
	.byte	10
	.word	.L135,.L192,.L61
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('gpio_init')
	.sect	'.debug_abbrev'
.L106:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,10,11,0,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('gpio_init')
	.sect	'.debug_line'
.L107:
	.word	.L306-.L305
.L305:
	.half	3
	.word	.L308-.L307
.L307:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std',0
	.byte	0
	.byte	'../libraries/zf_driver/zf_driver_gpio.c',0,0,0,0
	.byte	'IFXPORT.h',0,1,0,0,0
.L308:
	.byte	5,6,7,0,5,2
	.word	.L75
	.byte	3,156,1,1,5,5,9
	.half	.L224-.L75
	.byte	3,4,1,5,18,7,9
	.half	.L225-.L224
	.byte	3,4,1,9
	.half	.L309-.L225
	.byte	3,1,1,5,44,9
	.half	.L50-.L309
	.byte	3,127,1,5,85,9
	.half	.L226-.L50
	.byte	1,5,44,9
	.half	.L51-.L226
	.byte	3,1,1,5,85,9
	.half	.L227-.L51
	.byte	1,5,44,9
	.half	.L52-.L227
	.byte	3,1,1,5,85,9
	.half	.L228-.L52
	.byte	1,5,5,9
	.half	.L53-.L228
	.byte	3,2,1,5,18,9
	.half	.L49-.L53
	.byte	3,5,1,5,44,9
	.half	.L57-.L49
	.byte	1,5,85,9
	.half	.L229-.L57
	.byte	1,5,44,9
	.half	.L58-.L229
	.byte	3,1,1,5,85,9
	.half	.L230-.L58
	.byte	1,5,33,9
	.half	.L56-.L230
	.byte	3,4,1,5,42,9
	.half	.L223-.L56
	.byte	1,5,49,9
	.half	.L232-.L223
	.byte	1,5,38,9
	.half	.L234-.L232
	.byte	3,2,1,5,47,9
	.half	.L236-.L234
	.byte	1,5,54,9
	.half	.L237-.L236
	.byte	1,5,5,9
	.half	.L238-.L237
	.byte	3,2,1,5,9,7,9
	.half	.L239-.L238
	.byte	3,2,1,5,41,7,9
	.half	.L240-.L239
	.byte	3,2,1,5,50,9
	.half	.L242-.L240
	.byte	1,4,2,5,41,9
	.half	.L181-.L242
	.byte	3,255,2,1,5,26,9
	.half	.L185-.L181
	.byte	3,24,1,5,17,9
	.half	.L310-.L185
	.byte	1,4,1,5,40,9
	.half	.L182-.L310
	.byte	3,233,124,1,9
	.half	.L62-.L182
	.byte	3,4,1,5,49,9
	.half	.L244-.L62
	.byte	1,4,2,5,41,9
	.half	.L189-.L244
	.byte	3,129,3,1,5,26,9
	.half	.L192-.L189
	.byte	3,18,1,5,17,9
	.half	.L311-.L192
	.byte	1,4,1,5,1,9
	.half	.L61-.L311
	.byte	3,241,124,1,7,9
	.half	.L109-.L61
	.byte	0,1,1
.L306:
	.sdecl	'.debug_ranges',debug,cluster('gpio_init')
	.sect	'.debug_ranges'
.L108:
	.word	-1,.L75,0,.L109-.L75,0,0
	.sdecl	'.debug_loc',debug,cluster('get_port')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L65,0,.L111-.L65
	.half	2
	.byte	138,0
	.word	0,0
.L113:
	.word	-1,.L65,0,.L193-.L65
	.half	1
	.byte	84
	.word	0,0
.L114:
	.word	-1,.L65,.L194-.L65,.L3-.L65
	.half	1
	.byte	98
	.word	.L195-.L65,.L4-.L65
	.half	1
	.byte	98
	.word	.L196-.L65,.L5-.L65
	.half	1
	.byte	98
	.word	.L197-.L65,.L6-.L65
	.half	1
	.byte	98
	.word	.L198-.L65,.L7-.L65
	.half	1
	.byte	98
	.word	.L199-.L65,.L8-.L65
	.half	1
	.byte	98
	.word	.L200-.L65,.L9-.L65
	.half	1
	.byte	98
	.word	.L201-.L65,.L10-.L65
	.half	1
	.byte	98
	.word	.L202-.L65,.L11-.L65
	.half	1
	.byte	98
	.word	.L203-.L65,.L12-.L65
	.half	1
	.byte	98
	.word	.L204-.L65,.L13-.L65
	.half	1
	.byte	98
	.word	.L205-.L65,.L14-.L65
	.half	1
	.byte	98
	.word	.L206-.L65,.L15-.L65
	.half	1
	.byte	98
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gpio_get_level')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L69,0,.L144-.L69
	.half	2
	.byte	138,0
	.word	0,0
.L145:
	.word	-1,.L69,0,.L210-.L69
	.half	1
	.byte	84
	.word	.L211-.L69,.L147-.L69
	.half	1
	.byte	95
	.word	0,0
.L151:
	.word	0,0
.L149:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gpio_init')
	.sect	'.debug_loc'
.L188:
	.word	0,0
.L178:
	.word	-1,.L75,0,.L223-.L75
	.half	1
	.byte	86
	.word	.L224-.L75,.L175-.L75
	.half	1
	.byte	90
	.word	0,0
.L177:
	.word	-1,.L75,0,.L223-.L75
	.half	1
	.byte	85
	.word	.L224-.L75,.L225-.L75
	.half	1
	.byte	89
	.word	.L238-.L75,.L239-.L75
	.half	1
	.byte	89
	.word	0,0
.L74:
	.word	-1,.L75,0,.L175-.L75
	.half	2
	.byte	138,0
	.word	0,0
.L176:
	.word	-1,.L75,0,.L223-.L75
	.half	1
	.byte	84
	.word	.L56-.L75,.L231-.L75
	.half	1
	.byte	88
	.word	.L223-.L75,.L232-.L75
	.half	1
	.byte	88
	.word	.L234-.L75,.L235-.L75
	.half	1
	.byte	88
	.word	.L235-.L75,.L236-.L75
	.half	1
	.byte	84
	.word	.L236-.L75,.L237-.L75
	.half	1
	.byte	88
	.word	.L240-.L75,.L241-.L75
	.half	1
	.byte	88
	.word	.L241-.L75,.L242-.L75
	.half	1
	.byte	84
	.word	.L242-.L75,.L181-.L75
	.half	1
	.byte	88
	.word	.L62-.L75,.L243-.L75
	.half	1
	.byte	88
	.word	.L243-.L75,.L244-.L75
	.half	1
	.byte	84
	.word	.L244-.L75,.L189-.L75
	.half	1
	.byte	88
	.word	0,0
.L184:
	.word	0,0
.L191:
	.word	0,0
.L187:
	.word	0,0
.L179:
	.word	-1,.L75,0,.L223-.L75
	.half	1
	.byte	87
	.word	0,0
.L183:
	.word	0,0
.L190:
	.word	0,0
.L186:
	.word	0,0
.L180:
	.word	-1,.L75,.L226-.L75,.L51-.L75
	.half	1
	.byte	95
	.word	.L227-.L75,.L52-.L75
	.half	1
	.byte	95
	.word	.L228-.L75,.L49-.L75
	.half	1
	.byte	95
	.word	.L229-.L75,.L58-.L75
	.half	1
	.byte	95
	.word	.L230-.L75,.L181-.L75
	.half	1
	.byte	95
	.word	.L233-.L75,.L234-.L75
	.half	1
	.byte	85
	.word	.L62-.L75,.L189-.L75
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gpio_set_dir')
	.sect	'.debug_loc'
.L170:
	.word	-1,.L73,0,.L214-.L73
	.half	1
	.byte	85
	.word	0,0
.L72:
	.word	-1,.L73,0,.L167-.L73
	.half	2
	.byte	138,0
	.word	0,0
.L168:
	.word	-1,.L73,0,.L214-.L73
	.half	1
	.byte	84
	.word	.L215-.L73,.L167-.L73
	.half	1
	.byte	88
	.word	0,0
.L172:
	.word	-1,.L73,0,.L214-.L73
	.half	1
	.byte	86
	.word	0,0
.L174:
	.word	-1,.L73,.L216-.L73,.L39-.L73
	.half	1
	.byte	95
	.word	.L217-.L73,.L40-.L73
	.half	1
	.byte	95
	.word	.L218-.L73,.L37-.L73
	.half	1
	.byte	95
	.word	.L219-.L73,.L46-.L73
	.half	1
	.byte	95
	.word	.L220-.L73,.L167-.L73
	.half	1
	.byte	95
	.word	.L221-.L73,.L222-.L73
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gpio_set_level')
	.sect	'.debug_loc'
.L134:
	.word	0,0
.L118:
	.word	-1,.L67,0,.L207-.L67
	.half	1
	.byte	85
	.word	.L31-.L67,.L209-.L67
	.half	1
	.byte	85
	.word	0,0
.L66:
	.word	-1,.L67,0,.L115-.L67
	.half	2
	.byte	138,0
	.word	0,0
.L116:
	.word	-1,.L67,0,.L207-.L67
	.half	1
	.byte	84
	.word	.L208-.L67,.L120-.L67
	.half	1
	.byte	95
	.word	.L31-.L67,.L137-.L67
	.half	1
	.byte	95
	.word	.L31-.L67,.L209-.L67
	.half	1
	.byte	84
	.word	0,0
.L125:
	.word	0,0
.L141:
	.word	0,0
.L132:
	.word	0,0
.L123:
	.word	0,0
.L139:
	.word	0,0
.L130:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('gpio_toggle_level')
	.sect	'.debug_loc'
.L166:
	.word	0,0
.L70:
	.word	-1,.L71,0,.L153-.L71
	.half	2
	.byte	138,0
	.word	0,0
.L154:
	.word	-1,.L71,0,.L212-.L71
	.half	1
	.byte	84
	.word	.L213-.L71,.L156-.L71
	.half	1
	.byte	95
	.word	0,0
.L165:
	.word	0,0
.L161:
	.word	0,0
.L164:
	.word	0,0
.L159:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L312:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('get_port')
	.sect	'.debug_frame'
	.word	24
	.word	.L312,.L65,.L111-.L65
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('gpio_set_level')
	.sect	'.debug_frame'
	.word	12
	.word	.L312,.L67,.L115-.L67
	.sdecl	'.debug_frame',debug,cluster('gpio_get_level')
	.sect	'.debug_frame'
	.word	12
	.word	.L312,.L69,.L144-.L69
	.sdecl	'.debug_frame',debug,cluster('gpio_toggle_level')
	.sect	'.debug_frame'
	.word	12
	.word	.L312,.L71,.L153-.L71
	.sdecl	'.debug_frame',debug,cluster('gpio_set_dir')
	.sect	'.debug_frame'
	.word	12
	.word	.L312,.L73,.L167-.L73
	.sdecl	'.debug_frame',debug,cluster('gpio_init')
	.sect	'.debug_frame'
	.word	12
	.word	.L312,.L75,.L175-.L75
	; Module end
