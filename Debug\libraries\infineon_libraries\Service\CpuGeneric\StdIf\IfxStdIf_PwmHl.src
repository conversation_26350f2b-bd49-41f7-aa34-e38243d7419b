	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc17436a --dep-file=IfxStdIf_PwmHl.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.src ../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c'

	
$TC16X
	
	.sdecl	'.text.IfxStdIf_PwmHl.IfxStdIf_PwmHl_initConfig',code,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.text.IfxStdIf_PwmHl.IfxStdIf_PwmHl_initConfig'
	.align	2
	
	.global	IfxStdIf_PwmHl_initConfig
; Function IfxStdIf_PwmHl_initConfig
.L3:
IfxStdIf_PwmHl_initConfig:	.type	func
	mov	d15,#0
.L24:
	st.w	[a4],d15
.L25:
	mov	d15,#0
.L26:
	st.w	[a4]4,d15
.L27:
	mov	d15,#0
.L28:
	st.b	[a4]8,d15
.L29:
	mov	d15,#0
.L30:
	st.b	[a4]9,d15
.L31:
	mov	d15,#128
.L32:
	st.b	[a4]10,d15
.L33:
	mov	d15,#0
.L34:
	st.b	[a4]11,d15
.L35:
	mov	d15,#1
.L36:
	st.b	[a4]12,d15
.L37:
	mov	d15,#1
.L38:
	st.b	[a4]13,d15
.L39:
	ret
.L13:
	
__IfxStdIf_PwmHl_initConfig_function_end:
	.size	IfxStdIf_PwmHl_initConfig,__IfxStdIf_PwmHl_initConfig_function_end-IfxStdIf_PwmHl_initConfig
.L12:
	; End of function
	
	.calls	'IfxStdIf_PwmHl_initConfig','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L5:
	.word	46033
	.half	3
	.word	.L6
	.byte	4
.L4:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L7
	.byte	2,1,1,3
	.word	236
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	239
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	284
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	296
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	376
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	350
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	382
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	382
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	350
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	491
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	491
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	508
	.byte	4,2,35,0,0,14
	.word	798
	.byte	3
	.word	837
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	842
	.byte	6,0,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	912
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1228
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1799
	.byte	4,2,35,0,0,15,4
	.word	491
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1927
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2142
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	491
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	491
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2574
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2794
	.byte	4,2,35,0,0,15,24
	.word	491
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3117
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	491
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3421
	.byte	4,2,35,0,0,15,8
	.word	491
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3746
	.byte	4,2,35,0,0,15,12
	.word	491
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4086
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4452
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4738
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4885
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	468
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5054
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5226
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	890
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5401
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5575
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5749
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5925
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6081
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6414
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6762
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6886
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6970
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7150
	.byte	4,2,35,0,0,15,76
	.word	491
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7403
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	491
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7490
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1188
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1759
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1878
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1918
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2102
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2317
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2534
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2754
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1918
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3068
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3108
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3381
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3697
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3737
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4037
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4077
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4412
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4698
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3737
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4845
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5014
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5186
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5361
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5535
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5709
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5885
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6041
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6374
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6722
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3737
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6846
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7095
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7354
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7394
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7450
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8017
	.byte	4,3,35,252,1,0,14
	.word	8057
	.byte	3
	.word	8660
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	8665
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	491
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	8670
	.byte	6,0,19
	.byte	'IfxStdIf_InterfaceDriver',0,8,118,15
	.word	382
	.byte	20
	.word	296
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	8884
	.byte	19
	.byte	'IfxStdIf_Timer_GetFrequency',0,7,102,19
	.word	8897
	.byte	7
	.byte	'unsigned long int',0,4,7,20
	.word	8938
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	8959
	.byte	19
	.byte	'IfxStdIf_Timer_GetPeriod',0,7,108,26
	.word	8972
	.byte	19
	.byte	'IfxStdIf_Timer_GetResolution',0,7,114,19
	.word	8897
	.byte	19
	.byte	'IfxStdIf_Timer_GetTrigger',0,7,120,26
	.word	8972
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	21
	.word	296
	.byte	0,3
	.word	9081
	.byte	19
	.byte	'IfxStdIf_Timer_SetFrequency',0,7,128,1,19
	.word	9099
	.byte	22,1,1,21
	.word	382
	.byte	0,3
	.word	9141
	.byte	19
	.byte	'IfxStdIf_Timer_UpdateInputFrequency',0,7,134,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_ApplyUpdate',0,7,155,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_DisableUpdate',0,7,172,1,16
	.word	9150
	.byte	19
	.byte	'IfxStdIf_Timer_GetInputFrequency',0,7,178,1,19
	.word	8897
	.byte	19
	.byte	'IfxStdIf_Timer_Run',0,7,187,1,16
	.word	9150
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	21
	.word	8938
	.byte	0,3
	.word	9344
	.byte	19
	.byte	'IfxStdIf_Timer_SetPeriod',0,7,197,1,19
	.word	9362
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	491
	.byte	0,3
	.word	9401
	.byte	19
	.byte	'IfxStdIf_Timer_SetSingleMode',0,7,206,1,16
	.word	9415
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	8938
	.byte	0,3
	.word	9458
	.byte	19
	.byte	'IfxStdIf_Timer_SetTrigger',0,7,218,1,16
	.word	9472
	.byte	19
	.byte	'IfxStdIf_Timer_Stop',0,7,224,1,16
	.word	9150
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	9541
	.byte	19
	.byte	'IfxStdIf_Timer_AckTimerIrq',0,7,230,1,19
	.word	9554
	.byte	19
	.byte	'IfxStdIf_Timer_AckTriggerIrq',0,7,236,1,19
	.word	9554
	.byte	10
	.byte	'IfxStdIf_Timer_',0,7,240,1,8,68,13
	.byte	'driver',0
	.word	8851
	.byte	4,2,35,0,13
	.byte	'getFrequency',0
	.word	8902
	.byte	4,2,35,4,13
	.byte	'getPeriod',0
	.word	8977
	.byte	4,2,35,8,13
	.byte	'getResolution',0
	.word	9010
	.byte	4,2,35,12,13
	.byte	'getTrigger',0
	.word	9047
	.byte	4,2,35,16,13
	.byte	'setFrequency',0
	.word	9104
	.byte	4,2,35,20,13
	.byte	'updateInputFrequency',0
	.word	9155
	.byte	4,2,35,24,13
	.byte	'applyUpdate',0
	.word	9200
	.byte	4,2,35,28,13
	.byte	'disableUpdate',0
	.word	9236
	.byte	4,2,35,32,13
	.byte	'getInputFrequency',0
	.word	9274
	.byte	4,2,35,36,13
	.byte	'run',0
	.word	9316
	.byte	4,2,35,40,13
	.byte	'setPeriod',0
	.word	9367
	.byte	4,2,35,44,13
	.byte	'setSingleMode',0
	.word	9420
	.byte	4,2,35,48,13
	.byte	'setTrigger',0
	.word	9477
	.byte	4,2,35,52,13
	.byte	'stop',0
	.word	9512
	.byte	4,2,35,56,13
	.byte	'ackTimerIrq',0
	.word	9559
	.byte	4,2,35,60,13
	.byte	'ackTriggerIrq',0
	.word	9595
	.byte	4,2,35,64,0,3
	.word	9633
	.byte	8
	.byte	'IfxStdIf_Timer_getInputFrequency',0,3,7,236,2,20
	.word	296
	.byte	1,1,5
	.byte	'stdIf',0,7,236,2,69
	.word	10012
	.byte	6,0,3
	.word	376
	.byte	3
	.word	8884
	.byte	3
	.word	8959
	.byte	3
	.word	8884
	.byte	3
	.word	8959
	.byte	3
	.word	9081
	.byte	3
	.word	9141
	.byte	3
	.word	9141
	.byte	3
	.word	9141
	.byte	3
	.word	8884
	.byte	3
	.word	9141
	.byte	3
	.word	9344
	.byte	3
	.word	9401
	.byte	3
	.word	9458
	.byte	3
	.word	9141
	.byte	3
	.word	9541
	.byte	3
	.word	9541
	.byte	8
	.byte	'IfxStdIf_Timer_tickToS',0,3,7,182,3,20
	.word	296
	.byte	1,1,5
	.byte	'clockFreq',0,7,182,3,51
	.word	296
	.byte	5
	.byte	'ticks',0,7,182,3,77
	.word	8938
	.byte	6,0,23
	.word	244
	.byte	24
	.word	270
	.byte	6,0,23
	.word	305
	.byte	24
	.word	337
	.byte	6,0,23
	.word	387
	.byte	24
	.word	406
	.byte	6,0,23
	.word	422
	.byte	24
	.word	437
	.byte	24
	.word	451
	.byte	6,0,23
	.word	847
	.byte	24
	.word	875
	.byte	6,0,23
	.word	8773
	.byte	24
	.word	8801
	.byte	24
	.word	8815
	.byte	24
	.word	8833
	.byte	6,0,23
	.word	10017
	.byte	24
	.word	10062
	.byte	6,0,23
	.word	10164
	.byte	24
	.word	10199
	.byte	24
	.word	10218
	.byte	6,0,17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,17,10,116,9,1,18
	.byte	'Ifx_ActiveState_low',0,0,18
	.byte	'Ifx_ActiveState_high',0,1,0,25,9,195,1,9,16,13
	.byte	'deadtime',0
	.word	296
	.byte	4,2,35,0,13
	.byte	'minPulse',0
	.word	296
	.byte	4,2,35,4,13
	.byte	'channelCount',0
	.word	491
	.byte	1,2,35,8,13
	.byte	'emergencyEnabled',0
	.word	491
	.byte	1,2,35,9,13
	.byte	'outputMode',0
	.word	10351
	.byte	1,2,35,10,13
	.byte	'outputDriver',0
	.word	10421
	.byte	1,2,35,11,13
	.byte	'ccxActiveState',0
	.word	10836
	.byte	1,2,35,12,13
	.byte	'coutxActiveState',0
	.word	10836
	.byte	1,2,35,13,0
.L14:
	.byte	3
	.word	10887
	.byte	7
	.byte	'short int',0,2,5,19
	.byte	'__wchar_t',0,11,1,1
	.word	11075
	.byte	19
	.byte	'__size_t',0,11,1,1
	.word	468
	.byte	19
	.byte	'__ptrdiff_t',0,11,1,1
	.word	484
	.byte	26,1,3
	.word	11143
	.byte	19
	.byte	'__codeptr',0,11,1,1
	.word	11145
	.byte	19
	.byte	'boolean',0,12,101,29
	.word	491
	.byte	19
	.byte	'uint8',0,12,105,29
	.word	491
	.byte	19
	.byte	'uint16',0,12,109,29
	.word	890
	.byte	19
	.byte	'uint32',0,12,113,29
	.word	8938
	.byte	19
	.byte	'uint64',0,12,118,29
	.word	350
	.byte	19
	.byte	'sint16',0,12,126,29
	.word	11075
	.byte	7
	.byte	'long int',0,4,5,19
	.byte	'sint32',0,12,131,1,29
	.word	11258
	.byte	7
	.byte	'long long int',0,8,5,19
	.byte	'sint64',0,12,138,1,29
	.word	11286
	.byte	19
	.byte	'float32',0,12,167,1,29
	.word	296
	.byte	19
	.byte	'pvoid',0,10,57,28
	.word	382
	.byte	19
	.byte	'Ifx_TickTime',0,10,79,28
	.word	11286
	.byte	19
	.byte	'Ifx_Priority',0,10,103,16
	.word	890
	.byte	19
	.byte	'Ifx_TimerValue',0,10,104,16
	.word	8938
	.byte	19
	.byte	'Ifx_ActiveState',0,10,120,3
	.word	10836
	.byte	17,10,178,1,9,1,18
	.byte	'Ifx_Pwm_Mode_centerAligned',0,0,18
	.byte	'Ifx_Pwm_Mode_centerAlignedInverted',0,1,18
	.byte	'Ifx_Pwm_Mode_leftAligned',0,2,18
	.byte	'Ifx_Pwm_Mode_rightAligned',0,3,18
	.byte	'Ifx_Pwm_Mode_off',0,4,18
	.byte	'Ifx_Pwm_Mode_init',0,5,18
	.byte	'Ifx_Pwm_Mode_count',0,6,0,19
	.byte	'Ifx_Pwm_Mode',0,10,187,1,3
	.word	11439
	.byte	17,13,69,9,1,18
	.byte	'IfxSrc_Tos_cpu0',0,0,18
	.byte	'IfxSrc_Tos_cpu1',0,1,18
	.byte	'IfxSrc_Tos_dma',0,3,0,19
	.byte	'IfxSrc_Tos',0,13,74,3
	.word	11649
	.byte	19
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	508
	.byte	19
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	798
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	11774
	.byte	19
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	11806
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,0,14
	.word	11832
	.byte	19
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	11891
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	11919
	.byte	19
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	11956
	.byte	15,64
	.word	798
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	11984
	.byte	64,2,35,0,0,14
	.word	11993
	.byte	19
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	12025
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	12050
	.byte	19
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	12122
	.byte	15,8
	.word	798
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	12148
	.byte	8,2,35,0,0,14
	.word	12157
	.byte	19
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	12193
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	12223
	.byte	19
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	12296
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12322
	.byte	19
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	12357
	.byte	15,192,1
	.word	798
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4077
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	12383
	.byte	192,1,2,35,16,0,14
	.word	12393
	.byte	19
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	12460
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	798
	.byte	4,2,35,4,0,14
	.word	12486
	.byte	19
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	12534
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12562
	.byte	19
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	12595
	.byte	15,40
	.word	491
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	12148
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	12148
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	12148
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	12148
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	798
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	798
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	12622
	.byte	40,2,35,40,0,14
	.word	12631
	.byte	19
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	12758
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12785
	.byte	19
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	12817
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	12843
	.byte	19
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	12875
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	798
	.byte	4,2,35,8,0,14
	.word	12901
	.byte	19
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	12961
	.byte	15,16
	.word	491
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	12987
	.byte	16,2,35,16,0,14
	.word	12996
	.byte	19
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	13090
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3108
	.byte	24,2,35,24,0,14
	.word	13117
	.byte	19
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	13234
	.byte	15,12
	.word	798
	.byte	16,2,0,15,32
	.word	798
	.byte	16,7,0,15,32
	.word	13271
	.byte	16,0,0,15,88
	.word	491
	.byte	16,87,0,15,108
	.word	798
	.byte	16,26,0,15,96
	.word	491
	.byte	16,95,0,15,96
	.word	13271
	.byte	16,2,0,15,160,3
	.word	491
	.byte	16,159,3,0,15,64
	.word	13271
	.byte	16,1,0,15,192,3
	.word	491
	.byte	16,191,3,0,15,16
	.word	798
	.byte	16,3,0,15,64
	.word	13356
	.byte	16,3,0,15,192,2
	.word	491
	.byte	16,191,2,0,15,52
	.word	491
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	13262
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	1918
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	798
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	12148
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	3737
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	13280
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	13289
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	13298
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	13307
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	798
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4077
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	13316
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	13325
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	13316
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	13325
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	13336
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	13345
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	13365
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	13374
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	13262
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	13385
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	13262
	.byte	12,3,35,192,18,0,14
	.word	13394
	.byte	19
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	13854
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	13880
	.byte	19
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	13913
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	13940
	.byte	19
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	14013
	.byte	15,56
	.word	491
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	798
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	14040
	.byte	56,2,35,24,0,14
	.word	14049
	.byte	19
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	14172
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14198
	.byte	19
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	14230
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	798
	.byte	4,2,35,16,0,14
	.word	14256
	.byte	19
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	14341
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14367
	.byte	19
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	14399
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	13271
	.byte	32,2,35,0,0,14
	.word	14425
	.byte	19
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	14458
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	13271
	.byte	32,2,35,0,0,14
	.word	14485
	.byte	19
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	14519
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	798
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	798
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	798
	.byte	4,2,35,20,0,14
	.word	14547
	.byte	19
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	14640
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	14667
	.byte	19
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	14699
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	13356
	.byte	16,2,35,4,0,14
	.word	14725
	.byte	19
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	14771
	.byte	15,24
	.word	798
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	14797
	.byte	24,2,35,0,0,14
	.word	14806
	.byte	19
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	14839
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	13262
	.byte	12,2,35,0,0,14
	.word	14866
	.byte	19
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	14898
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,0,14
	.word	14924
	.byte	19
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	14970
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	14996
	.byte	19
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	15071
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	798
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	798
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	798
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	798
	.byte	4,2,35,12,0,14
	.word	15100
	.byte	19
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	15174
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	798
	.byte	4,2,35,0,0,14
	.word	15202
	.byte	19
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	15236
	.byte	15,4
	.word	11774
	.byte	16,0,0,14
	.word	15263
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	15272
	.byte	4,2,35,0,0,14
	.word	15277
	.byte	19
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	15313
	.byte	15,48
	.word	11832
	.byte	16,3,0,14
	.word	15341
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	15350
	.byte	48,2,35,0,0,14
	.word	15355
	.byte	19
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	15395
	.byte	14
	.word	11919
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	15425
	.byte	4,2,35,0,0,14
	.word	15430
	.byte	19
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	15464
	.byte	15,64
	.word	11993
	.byte	16,0,0,14
	.word	15491
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	15500
	.byte	64,2,35,0,0,14
	.word	15505
	.byte	19
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	15539
	.byte	15,32
	.word	12050
	.byte	16,1,0,14
	.word	15566
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	15575
	.byte	32,2,35,0,0,14
	.word	15580
	.byte	19
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	15616
	.byte	14
	.word	12157
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	15644
	.byte	8,2,35,0,0,14
	.word	15649
	.byte	19
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	15693
	.byte	15,16
	.word	12223
	.byte	16,0,0,14
	.word	15725
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	15734
	.byte	16,2,35,0,0,14
	.word	15739
	.byte	19
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	15773
	.byte	15,8
	.word	12322
	.byte	16,1,0,14
	.word	15800
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	15809
	.byte	8,2,35,0,0,14
	.word	15814
	.byte	19
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	15848
	.byte	15,208,1
	.word	12393
	.byte	16,0,0,14
	.word	15875
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	15885
	.byte	208,1,2,35,0,0,14
	.word	15890
	.byte	19
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	15926
	.byte	14
	.word	12486
	.byte	14
	.word	12486
	.byte	14
	.word	12486
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	15953
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	3737
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	15958
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	15963
	.byte	8,2,35,24,0,14
	.word	15968
	.byte	19
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	16059
	.byte	15,4
	.word	12562
	.byte	16,0,0,14
	.word	16088
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	16097
	.byte	4,2,35,0,0,14
	.word	16102
	.byte	19
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	16138
	.byte	15,80
	.word	12631
	.byte	16,0,0,14
	.word	16166
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	16175
	.byte	80,2,35,0,0,14
	.word	16180
	.byte	19
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	16216
	.byte	15,4
	.word	12785
	.byte	16,0,0,14
	.word	16244
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	16253
	.byte	4,2,35,0,0,14
	.word	16258
	.byte	19
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	16292
	.byte	15,4
	.word	12843
	.byte	16,0,0,14
	.word	16319
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	16328
	.byte	4,2,35,0,0,14
	.word	16333
	.byte	19
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	16367
	.byte	15,12
	.word	12901
	.byte	16,0,0,14
	.word	16394
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	16403
	.byte	12,2,35,0,0,14
	.word	16408
	.byte	19
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	16442
	.byte	15,64
	.word	12996
	.byte	16,1,0,14
	.word	16469
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	16478
	.byte	64,2,35,0,0,14
	.word	16483
	.byte	19
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	16519
	.byte	15,48
	.word	13117
	.byte	16,0,0,14
	.word	16547
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	16556
	.byte	48,2,35,0,0,14
	.word	16561
	.byte	19
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	16599
	.byte	15,204,18
	.word	13394
	.byte	16,0,0,14
	.word	16628
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	16638
	.byte	204,18,2,35,0,0,14
	.word	16643
	.byte	19
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	16679
	.byte	15,4
	.word	13880
	.byte	16,0,0,14
	.word	16706
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	16715
	.byte	4,2,35,0,0,14
	.word	16720
	.byte	19
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	16756
	.byte	15,64
	.word	13940
	.byte	16,3,0,14
	.word	16784
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	16793
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	798
	.byte	4,2,35,64,0,14
	.word	16798
	.byte	19
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	16847
	.byte	15,80
	.word	14049
	.byte	16,0,0,14
	.word	16875
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	16884
	.byte	80,2,35,0,0,14
	.word	16889
	.byte	19
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	16923
	.byte	15,4
	.word	14198
	.byte	16,0,0,14
	.word	16950
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	16959
	.byte	4,2,35,0,0,14
	.word	16964
	.byte	19
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	16998
	.byte	15,40
	.word	14256
	.byte	16,1,0,14
	.word	17025
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	17034
	.byte	40,2,35,0,0,14
	.word	17039
	.byte	19
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	17073
	.byte	15,8
	.word	14367
	.byte	16,1,0,14
	.word	17100
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	17109
	.byte	8,2,35,0,0,14
	.word	17114
	.byte	19
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	17148
	.byte	15,32
	.word	14425
	.byte	16,0,0,14
	.word	17175
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	17184
	.byte	32,2,35,0,0,14
	.word	17189
	.byte	19
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	17225
	.byte	15,32
	.word	14485
	.byte	16,0,0,14
	.word	17253
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	17262
	.byte	32,2,35,0,0,14
	.word	17267
	.byte	19
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	17305
	.byte	15,96
	.word	14547
	.byte	16,3,0,14
	.word	17334
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	17343
	.byte	96,2,35,0,0,14
	.word	17348
	.byte	19
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	17384
	.byte	15,4
	.word	14667
	.byte	16,0,0,14
	.word	17412
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	17421
	.byte	4,2,35,0,0,14
	.word	17426
	.byte	19
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	17460
	.byte	14
	.word	14725
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	17487
	.byte	20,2,35,0,0,14
	.word	17492
	.byte	19
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	17526
	.byte	15,24
	.word	14806
	.byte	16,0,0,14
	.word	17553
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	17562
	.byte	24,2,35,0,0,14
	.word	17567
	.byte	19
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	17603
	.byte	15,12
	.word	14866
	.byte	16,0,0,14
	.word	17631
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	17640
	.byte	12,2,35,0,0,14
	.word	17645
	.byte	19
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	17679
	.byte	15,16
	.word	14924
	.byte	16,1,0,14
	.word	17706
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	17715
	.byte	16,2,35,0,0,14
	.word	17720
	.byte	19
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	17754
	.byte	15,64
	.word	15100
	.byte	16,3,0,14
	.word	17781
	.byte	15,224,1
	.word	491
	.byte	16,223,1,0,15,32
	.word	14996
	.byte	16,1,0,14
	.word	17806
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	17790
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	17795
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	17815
	.byte	32,3,35,160,2,0,14
	.word	17820
	.byte	19
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	17889
	.byte	14
	.word	15202
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	17917
	.byte	4,2,35,0,0,14
	.word	17922
	.byte	19
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	17958
	.byte	19
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	7490
	.byte	19
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	7403
	.byte	19
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	3746
	.byte	19
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	1799
	.byte	19
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	2794
	.byte	19
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	1927
	.byte	19
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	2574
	.byte	19
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2142
	.byte	19
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2357
	.byte	19
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	6762
	.byte	19
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	6886
	.byte	19
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	6970
	.byte	19
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7150
	.byte	19
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	5401
	.byte	19
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	5925
	.byte	19
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	5575
	.byte	19
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	5749
	.byte	19
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	6414
	.byte	19
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1228
	.byte	19
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	4738
	.byte	19
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5226
	.byte	19
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	4885
	.byte	19
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5054
	.byte	19
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6081
	.byte	19
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	912
	.byte	19
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	4452
	.byte	19
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4086
	.byte	19
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3117
	.byte	19
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	3421
	.byte	19
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8017
	.byte	19
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	7450
	.byte	19
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4037
	.byte	19
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	1878
	.byte	19
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3068
	.byte	19
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2102
	.byte	19
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	2754
	.byte	19
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2317
	.byte	19
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	2534
	.byte	19
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	6846
	.byte	19
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7095
	.byte	19
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7354
	.byte	19
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	6722
	.byte	19
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	5535
	.byte	19
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6041
	.byte	19
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	5709
	.byte	19
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	5885
	.byte	19
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	1759
	.byte	19
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6374
	.byte	19
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	4845
	.byte	19
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5361
	.byte	19
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5014
	.byte	19
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5186
	.byte	19
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1188
	.byte	19
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	4698
	.byte	19
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	4412
	.byte	19
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3381
	.byte	19
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	3697
	.byte	14
	.word	8057
	.byte	19
	.byte	'Ifx_P',0,6,139,6,3
	.word	19304
	.byte	17,14,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,19
	.byte	'IfxScu_WDTCON1_IR',0,14,255,10,3
	.word	19324
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,15,45,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_ACCEN0_Bits',0,15,79,3
	.word	19446
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,15,82,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	32,0,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN1_Bits',0,15,85,3
	.word	20003
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,15,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,19
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,15,94,3
	.word	20080
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,15,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	491
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	491
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON0_Bits',0,15,111,3
	.word	20216
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,15,114,16,4,11
	.byte	'CANDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	491
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON1_Bits',0,15,126,3
	.word	20496
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,15,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON2_Bits',0,15,135,1,3
	.word	20734
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,15,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	491
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON3_Bits',0,15,150,1,3
	.word	20862
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,15,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	491
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	491
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON4_Bits',0,15,165,1,3
	.word	21105
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,15,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CCUCON5_Bits',0,15,174,1,3
	.word	21340
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,15,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON6_Bits',0,15,181,1,3
	.word	21468
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,15,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON7_Bits',0,15,188,1,3
	.word	21568
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,15,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	491
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	491
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_CHIPID_Bits',0,15,202,1,3
	.word	21668
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,15,205,1,16,4,11
	.byte	'PWD',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	468
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_DTSCON_Bits',0,15,213,1,3
	.word	21876
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,15,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	890
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_DTSLIM_Bits',0,15,225,1,3
	.word	22041
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,15,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,15,235,1,3
	.word	22224
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,15,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	491
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	468
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	491
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EICR_Bits',0,15,129,2,3
	.word	22378
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,15,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_EIFR_Bits',0,15,143,2,3
	.word	22742
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,15,146,2,16,4,11
	.byte	'POL',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	890
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	491
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_EMSR_Bits',0,15,159,2,3
	.word	22953
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,15,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,19
	.byte	'Ifx_SCU_ESRCFG_Bits',0,15,167,2,3
	.word	23205
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,15,170,2,16,4,11
	.byte	'ARI',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_ESROCFG_Bits',0,15,175,2,3
	.word	23323
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,15,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVR13CON_Bits',0,15,185,2,3
	.word	23434
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,15,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	468
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVR33CON_Bits',0,15,195,2,3
	.word	23597
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,15,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,15,205,2,3
	.word	23760
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,15,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,15,215,2,3
	.word	23918
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,15,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	491
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	491
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	491
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	491
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	890
	.byte	10,0,2,35,2,0,19
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,15,232,2,3
	.word	24083
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,15,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	491
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	491
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	890
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	491
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,15,245,2,3
	.word	24412
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,15,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVROVMON_Bits',0,15,255,2,3
	.word	24633
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,15,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,15,142,3,3
	.word	24796
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,15,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,15,152,3,3
	.word	25068
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,15,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,15,162,3,3
	.word	25221
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,15,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,15,172,3,3
	.word	25377
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,15,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,15,181,3,3
	.word	25539
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,15,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,15,191,3,3
	.word	25682
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,15,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,15,200,3,3
	.word	25847
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,15,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,15,211,3,3
	.word	25992
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,15,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	491
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,15,222,3,3
	.word	26173
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,15,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,15,232,3,3
	.word	26347
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,15,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,15,241,3,3
	.word	26507
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,15,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,15,130,4,3
	.word	26651
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,15,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,15,139,4,3
	.word	26925
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,15,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,15,149,4,3
	.word	27064
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,15,152,4,16,4,11
	.byte	'EN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	491
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	890
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	491
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	491
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	491
	.byte	8,0,2,35,3,0,19
	.byte	'Ifx_SCU_EXTCON_Bits',0,15,163,4,3
	.word	27227
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,15,166,4,16,4,11
	.byte	'STEP',0,2
	.word	890
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	491
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	890
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_FDR_Bits',0,15,174,4,3
	.word	27445
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,15,177,4,16,4,11
	.byte	'FS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	8,0,2,35,3,0,19
	.byte	'Ifx_SCU_FMR_Bits',0,15,197,4,3
	.word	27608
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,15,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_ID_Bits',0,15,205,4,3
	.word	27944
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,15,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	491
	.byte	2,0,2,35,3,0,19
	.byte	'Ifx_SCU_IGCR_Bits',0,15,232,4,3
	.word	28051
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,15,235,4,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_IN_Bits',0,15,240,4,3
	.word	28503
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,15,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	491
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_IOCR_Bits',0,15,250,4,3
	.word	28602
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,15,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	890
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,15,131,5,3
	.word	28752
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,15,134,5,16,4,11
	.byte	'SEED',0,4
	.word	468
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,15,141,5,3
	.word	28901
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,15,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	468
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,15,149,5,3
	.word	29062
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,15,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	890
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_LCLCON_Bits',0,15,158,5,3
	.word	29192
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,15,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_LCLTEST_Bits',0,15,166,5,3
	.word	29324
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,15,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	491
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	890
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_MANID_Bits',0,15,174,5,3
	.word	29439
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,15,177,5,16,4,11
	.byte	'PS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	890
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	890
	.byte	14,0,2,35,2,0,19
	.byte	'Ifx_SCU_OMR_Bits',0,15,185,5,3
	.word	29550
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,15,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	491
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	491
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	491
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_OSCCON_Bits',0,15,209,5,3
	.word	29708
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,15,212,5,16,4,11
	.byte	'P0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_OUT_Bits',0,15,217,5,3
	.word	30120
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,15,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	890
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	6,0,2,35,3,0,19
	.byte	'Ifx_SCU_OVCCON_Bits',0,15,233,5,3
	.word	30221
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,15,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	468
	.byte	29,0,2,35,0,0,19
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,15,242,5,3
	.word	30488
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,15,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDISC_Bits',0,15,250,5,3
	.word	30624
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,15,253,5,16,4,11
	.byte	'PD0',0,1
	.word	491
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	491
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDR_Bits',0,15,132,6,3
	.word	30735
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,15,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PDRR_Bits',0,15,146,6,3
	.word	30868
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,15,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PLLCON0_Bits',0,15,166,6,3
	.word	31071
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,15,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	491
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	491
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	9,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLCON1_Bits',0,15,177,6,3
	.word	31427
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,15,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	890
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLCON2_Bits',0,15,184,6,3
	.word	31605
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,15,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	890
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	491
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	491
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	491
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,15,204,6,3
	.word	31705
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,15,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	491
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	491
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	491
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	9,0,2,35,2,0,19
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,15,215,6,3
	.word	32075
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,15,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	468
	.byte	26,0,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,15,227,6,3
	.word	32261
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,15,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	468
	.byte	24,0,2,35,0,0,19
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,15,241,6,3
	.word	32459
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,15,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	491
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	468
	.byte	21,0,2,35,0,0,19
	.byte	'Ifx_SCU_PMCSR_Bits',0,15,251,6,3
	.word	32692
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,15,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	491
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	491
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	491
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	491
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	491
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,15,153,7,3
	.word	32844
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,15,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	491
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	491
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	491
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	491
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,15,170,7,3
	.word	33411
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,15,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	491
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	491
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	491
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	491
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	491
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	491
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	491
	.byte	1,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,15,187,7,3
	.word	33705
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,15,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	491
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	491
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	491
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	491
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	491
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	890
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	491
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	491
	.byte	4,0,2,35,3,0,19
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,15,214,7,3
	.word	33983
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,15,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	890
	.byte	14,0,2,35,2,0,19
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,15,230,7,3
	.word	34479
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,15,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	890
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	491
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	491
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	491
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_RSTCON2_Bits',0,15,243,7,3
	.word	34792
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,15,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	491
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	491
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	491
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	491
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	491
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_RSTCON_Bits',0,15,129,8,3
	.word	35001
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,15,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	491
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	491
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	491
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	491
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	491
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	491
	.byte	3,0,2,35,3,0,19
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,15,155,8,3
	.word	35212
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,15,158,8,16,4,11
	.byte	'HBT',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	468
	.byte	31,0,2,35,0,0,19
	.byte	'Ifx_SCU_SAFECON_Bits',0,15,162,8,3
	.word	35644
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,15,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	491
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	491
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	491
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	491
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	491
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	491
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	491
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	491
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	491
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	491
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	491
	.byte	7,0,2,35,3,0,19
	.byte	'Ifx_SCU_STSTAT_Bits',0,15,178,8,3
	.word	35740
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,15,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	468
	.byte	30,0,2,35,0,0,19
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,15,186,8,3
	.word	36000
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,15,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	491
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	491
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	468
	.byte	23,0,2,35,0,0,19
	.byte	'Ifx_SCU_SYSCON_Bits',0,15,198,8,3
	.word	36125
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,15,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,15,208,8,3
	.word	36322
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,15,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,15,218,8,3
	.word	36475
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,15,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSET_Bits',0,15,228,8,3
	.word	36628
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,15,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	468
	.byte	28,0,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,15,238,8,3
	.word	36781
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,15,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	36936
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36936
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36936
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36936
	.byte	16,0,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,15,247,8,3
	.word	36952
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,15,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	491
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,15,134,9,3
	.word	37082
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,15,137,9,16,4,11
	.byte	'AE',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,15,150,9,3
	.word	37320
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,15,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	36936
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	36936
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	36936
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	36936
	.byte	16,0,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,15,159,9,3
	.word	37543
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,15,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,15,175,9,3
	.word	37669
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,15,178,9,16,4,11
	.byte	'AE',0,1
	.word	491
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	491
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	491
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	491
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	491
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	491
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	491
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	491
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	491
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	491
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	890
	.byte	16,0,2,35,2,0,19
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,15,191,9,3
	.word	37921
	.byte	12,15,199,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19446
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN0',0,15,204,9,3
	.word	38140
	.byte	12,15,207,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20003
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ACCEN1',0,15,212,9,3
	.word	38204
	.byte	12,15,215,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20080
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ARSTDIS',0,15,220,9,3
	.word	38268
	.byte	12,15,223,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20216
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON0',0,15,228,9,3
	.word	38333
	.byte	12,15,231,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20496
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON1',0,15,236,9,3
	.word	38398
	.byte	12,15,239,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20734
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON2',0,15,244,9,3
	.word	38463
	.byte	12,15,247,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20862
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON3',0,15,252,9,3
	.word	38528
	.byte	12,15,255,9,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21105
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON4',0,15,132,10,3
	.word	38593
	.byte	12,15,135,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21340
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON5',0,15,140,10,3
	.word	38658
	.byte	12,15,143,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21468
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON6',0,15,148,10,3
	.word	38723
	.byte	12,15,151,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21568
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CCUCON7',0,15,156,10,3
	.word	38788
	.byte	12,15,159,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21668
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_CHIPID',0,15,164,10,3
	.word	38853
	.byte	12,15,167,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21876
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSCON',0,15,172,10,3
	.word	38917
	.byte	12,15,175,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22041
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSLIM',0,15,180,10,3
	.word	38981
	.byte	12,15,183,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22224
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_DTSSTAT',0,15,188,10,3
	.word	39045
	.byte	12,15,191,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22378
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EICR',0,15,196,10,3
	.word	39110
	.byte	12,15,199,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22742
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EIFR',0,15,204,10,3
	.word	39172
	.byte	12,15,207,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22953
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EMSR',0,15,212,10,3
	.word	39234
	.byte	12,15,215,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23205
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ESRCFG',0,15,220,10,3
	.word	39296
	.byte	12,15,223,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23323
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ESROCFG',0,15,228,10,3
	.word	39360
	.byte	12,15,231,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23434
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVR13CON',0,15,236,10,3
	.word	39425
	.byte	12,15,239,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23597
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVR33CON',0,15,244,10,3
	.word	39491
	.byte	12,15,247,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23760
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRADCSTAT',0,15,252,10,3
	.word	39557
	.byte	12,15,255,10,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23918
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRDVSTAT',0,15,132,11,3
	.word	39625
	.byte	12,15,135,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24083
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRMONCTRL',0,15,140,11,3
	.word	39692
	.byte	12,15,143,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24412
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVROSCCTRL',0,15,148,11,3
	.word	39760
	.byte	12,15,151,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24633
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVROVMON',0,15,156,11,3
	.word	39828
	.byte	12,15,159,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24796
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRRSTCON',0,15,164,11,3
	.word	39894
	.byte	12,15,167,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25068
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,15,172,11,3
	.word	39961
	.byte	12,15,175,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25221
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,15,180,11,3
	.word	40030
	.byte	12,15,183,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25377
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,15,188,11,3
	.word	40099
	.byte	12,15,191,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25539
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,15,196,11,3
	.word	40168
	.byte	12,15,199,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25682
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,15,204,11,3
	.word	40237
	.byte	12,15,207,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25847
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,15,212,11,3
	.word	40306
	.byte	12,15,215,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25992
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL1',0,15,220,11,3
	.word	40375
	.byte	12,15,223,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26173
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL2',0,15,228,11,3
	.word	40443
	.byte	12,15,231,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26347
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL3',0,15,236,11,3
	.word	40511
	.byte	12,15,239,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26507
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSDCTRL4',0,15,244,11,3
	.word	40579
	.byte	12,15,247,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26651
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRSTAT',0,15,252,11,3
	.word	40647
	.byte	12,15,255,11,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26925
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRTRIM',0,15,132,12,3
	.word	40712
	.byte	12,15,135,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27064
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EVRUVMON',0,15,140,12,3
	.word	40777
	.byte	12,15,143,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27227
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_EXTCON',0,15,148,12,3
	.word	40843
	.byte	12,15,151,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27445
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_FDR',0,15,156,12,3
	.word	40907
	.byte	12,15,159,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27608
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_FMR',0,15,164,12,3
	.word	40968
	.byte	12,15,167,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27944
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_ID',0,15,172,12,3
	.word	41029
	.byte	12,15,175,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28051
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IGCR',0,15,180,12,3
	.word	41089
	.byte	12,15,183,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28503
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IN',0,15,188,12,3
	.word	41151
	.byte	12,15,191,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28602
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_IOCR',0,15,196,12,3
	.word	41211
	.byte	12,15,199,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28752
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL0',0,15,204,12,3
	.word	41273
	.byte	12,15,207,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28901
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL1',0,15,212,12,3
	.word	41341
	.byte	12,15,215,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29062
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LBISTCTRL2',0,15,220,12,3
	.word	41409
	.byte	12,15,223,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29192
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LCLCON',0,15,228,12,3
	.word	41477
	.byte	12,15,231,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29324
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_LCLTEST',0,15,236,12,3
	.word	41541
	.byte	12,15,239,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29439
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_MANID',0,15,244,12,3
	.word	41606
	.byte	12,15,247,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29550
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OMR',0,15,252,12,3
	.word	41669
	.byte	12,15,255,12,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29708
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OSCCON',0,15,132,13,3
	.word	41730
	.byte	12,15,135,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30120
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OUT',0,15,140,13,3
	.word	41794
	.byte	12,15,143,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30221
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OVCCON',0,15,148,13,3
	.word	41855
	.byte	12,15,151,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30488
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_OVCENABLE',0,15,156,13,3
	.word	41919
	.byte	12,15,159,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30624
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDISC',0,15,164,13,3
	.word	41986
	.byte	12,15,167,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30735
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDR',0,15,172,13,3
	.word	42049
	.byte	12,15,175,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30868
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PDRR',0,15,180,13,3
	.word	42110
	.byte	12,15,183,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31071
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON0',0,15,188,13,3
	.word	42172
	.byte	12,15,191,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31427
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON1',0,15,196,13,3
	.word	42237
	.byte	12,15,199,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31605
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLCON2',0,15,204,13,3
	.word	42302
	.byte	12,15,207,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31705
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYCON0',0,15,212,13,3
	.word	42367
	.byte	12,15,215,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32075
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYCON1',0,15,220,13,3
	.word	42436
	.byte	12,15,223,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32261
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLERAYSTAT',0,15,228,13,3
	.word	42505
	.byte	12,15,231,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32459
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PLLSTAT',0,15,236,13,3
	.word	42574
	.byte	12,15,239,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32692
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMCSR',0,15,244,13,3
	.word	42639
	.byte	12,15,247,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32844
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR0',0,15,252,13,3
	.word	42702
	.byte	12,15,255,13,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33411
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR1',0,15,132,14,3
	.word	42767
	.byte	12,15,135,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33705
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWCR2',0,15,140,14,3
	.word	42832
	.byte	12,15,143,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33983
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWSTAT',0,15,148,14,3
	.word	42897
	.byte	12,15,151,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34479
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_PMSWSTATCLR',0,15,156,14,3
	.word	42963
	.byte	12,15,159,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35001
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTCON',0,15,164,14,3
	.word	43032
	.byte	12,15,167,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34792
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTCON2',0,15,172,14,3
	.word	43096
	.byte	12,15,175,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35212
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_RSTSTAT',0,15,180,14,3
	.word	43161
	.byte	12,15,183,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35644
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SAFECON',0,15,188,14,3
	.word	43226
	.byte	12,15,191,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35740
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_STSTAT',0,15,196,14,3
	.word	43291
	.byte	12,15,199,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36000
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SWRSTCON',0,15,204,14,3
	.word	43355
	.byte	12,15,207,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36125
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_SYSCON',0,15,212,14,3
	.word	43421
	.byte	12,15,215,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36322
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPCLR',0,15,220,14,3
	.word	43485
	.byte	12,15,223,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36475
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPDIS',0,15,228,14,3
	.word	43550
	.byte	12,15,231,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36628
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSET',0,15,236,14,3
	.word	43615
	.byte	12,15,239,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36781
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_TRAPSTAT',0,15,244,14,3
	.word	43680
	.byte	12,15,247,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36952
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON0',0,15,252,14,3
	.word	43746
	.byte	12,15,255,14,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37082
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_CON1',0,15,132,15,3
	.word	43815
	.byte	12,15,135,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37320
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTCPU_SR',0,15,140,15,3
	.word	43884
	.byte	12,15,143,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37543
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON0',0,15,148,15,3
	.word	43951
	.byte	12,15,151,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37669
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_CON1',0,15,156,15,3
	.word	44018
	.byte	12,15,159,15,9,4,13
	.byte	'U',0
	.word	468
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	484
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37921
	.byte	4,2,35,0,0,19
	.byte	'Ifx_SCU_WDTS_SR',0,15,164,15,3
	.word	44085
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,15,175,15,25,12,13
	.byte	'CON0',0
	.word	43746
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43815
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43884
	.byte	4,2,35,8,0,14
	.word	44150
	.byte	19
	.byte	'Ifx_SCU_WDTCPU',0,15,180,15,3
	.word	44213
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,15,183,15,25,12,13
	.byte	'CON0',0
	.word	43951
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	44018
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	44085
	.byte	4,2,35,8,0,14
	.word	44242
	.byte	19
	.byte	'Ifx_SCU_WDTS',0,15,188,15,3
	.word	44303
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,19
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	44330
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,19
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	44481
	.byte	19
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	10351
	.byte	19
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	10421
	.byte	19
	.byte	'IfxPort_State',0,5,178,1,3
	.word	8670
	.byte	17,7,88,9,1,18
	.byte	'IfxStdIf_Timer_CountDir_up',0,0,18
	.byte	'IfxStdIf_Timer_CountDir_upAndDown',0,1,18
	.byte	'IfxStdIf_Timer_CountDir_down',0,2,0,19
	.byte	'IfxStdIf_Timer_CountDir',0,7,93,3
	.word	44803
	.byte	19
	.byte	'IfxStdIf_Timer',0,7,96,32
	.word	9633
	.byte	25,7,134,2,9,16,13
	.byte	'enabled',0
	.word	491
	.byte	1,2,35,0,13
	.byte	'triggerPoint',0
	.word	8938
	.byte	4,2,35,2,13
	.byte	'isrPriority',0
	.word	890
	.byte	2,2,35,6,13
	.byte	'isrProvider',0
	.word	11649
	.byte	1,2,35,8,13
	.byte	'outputMode',0
	.word	10351
	.byte	1,2,35,9,13
	.byte	'outputDriver',0
	.word	10421
	.byte	1,2,35,10,13
	.byte	'risingEdgeAtPeriod',0
	.word	491
	.byte	1,2,35,11,13
	.byte	'outputEnabled',0
	.word	491
	.byte	1,2,35,12,0,19
	.byte	'IfxStdIf_Timer_TrigConfig',0,7,144,2,3
	.word	44960
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetDeadtime',0,9,89,19
	.word	9099
	.byte	19
	.byte	'IfxStdIf_PwmHl_GetDeadtime',0,9,97,19
	.word	8897
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetMinPulse',0,9,105,19
	.word	9099
	.byte	19
	.byte	'IfxStdIf_PwmHl_GetMinPulse',0,9,113,19
	.word	8897
	.byte	20
	.word	11439
	.byte	1,1,21
	.word	382
	.byte	0,3
	.word	45316
	.byte	19
	.byte	'IfxStdIf_PwmHl_GetMode',0,9,121,24
	.word	45329
	.byte	20
	.word	491
	.byte	1,1,21
	.word	382
	.byte	21
	.word	11439
	.byte	0,3
	.word	45365
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetMode',0,9,129,1,19
	.word	45383
	.byte	3
	.word	8938
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	45420
	.byte	0,3
	.word	45425
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetOnTime',0,9,136,1,16
	.word	45439
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	45420
	.byte	21
	.word	45420
	.byte	0,3
	.word	45478
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetOnTimeAndShift',0,9,144,1,16
	.word	45497
	.byte	3
	.word	296
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	45544
	.byte	21
	.word	45544
	.byte	0,3
	.word	45549
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetPulse',0,9,152,1,16
	.word	45568
	.byte	3
	.word	491
	.byte	22,1,1,21
	.word	382
	.byte	21
	.word	45606
	.byte	21
	.word	45606
	.byte	0,3
	.word	45611
	.byte	19
	.byte	'IfxStdIf_PwmHl_SetupChannels',0,9,171,1,16
	.word	45630
	.byte	10
	.byte	'IfxStdIf_PwmHl_',0,9,175,1,8,112,13
	.byte	'driver',0
	.word	8851
	.byte	4,2,35,0,13
	.byte	'setDeadtime',0
	.word	45176
	.byte	4,2,35,4,13
	.byte	'getDeadtime',0
	.word	45211
	.byte	4,2,35,8,13
	.byte	'setMinPulse',0
	.word	45246
	.byte	4,2,35,12,13
	.byte	'getMinPulse',0
	.word	45281
	.byte	4,2,35,16,13
	.byte	'getMode',0
	.word	45334
	.byte	4,2,35,20,13
	.byte	'setMode',0
	.word	45388
	.byte	4,2,35,24,13
	.byte	'setOnTime',0
	.word	45444
	.byte	4,2,35,28,13
	.byte	'setOnTimeAndShift',0
	.word	45502
	.byte	4,2,35,32,13
	.byte	'setPulse',0
	.word	45573
	.byte	4,2,35,36,13
	.byte	'setupChannels',0
	.word	45635
	.byte	4,2,35,40,13
	.byte	'timer',0
	.word	9633
	.byte	68,2,35,44,0,19
	.byte	'IfxStdIf_PwmHl',0,9,81,32
	.word	45673
	.byte	3
	.word	9081
	.byte	3
	.word	8884
	.byte	3
	.word	9081
	.byte	3
	.word	8884
	.byte	3
	.word	45316
	.byte	3
	.word	45365
	.byte	3
	.word	45425
	.byte	3
	.word	45478
	.byte	3
	.word	45549
	.byte	3
	.word	45611
	.byte	19
	.byte	'IfxStdIf_PwmHl_Config',0,9,207,1,3
	.word	10887
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L6:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,22,0,3,8,58,15,59,15,57,15,73,19,0,0,20,21,1,73,19,54,15,39
	.byte	12,0,0,21,5,0,73,19,0,0,22,21,1,54,15,39,12,0,0,23,46,1,49,19,0,0,24,5,0,49,19,0,0,25,19,1,58,15,59,15
	.byte	57,15,11,15,0,0,26,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L7:
	.word	.L17-.L16
.L16:
	.half	3
	.word	.L19-.L18
.L18:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_Timer.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf.h',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\Service\\CpuGeneric\\StdIf\\IfxStdIf_PwmHl.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0,0
.L19:
.L17:
	.sdecl	'.debug_info',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_info'
.L8:
	.word	316
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L11,.L10
	.byte	2
	.word	.L4
	.byte	3
	.byte	'IfxStdIf_PwmHl_initConfig',0,1,48,6,1,1,1
	.word	.L3,.L13,.L2
	.byte	4
	.byte	'config',0,1,48,55
	.word	.L14,.L15
	.byte	5
	.word	.L3,.L13
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_line'
.L10:
	.word	.L21-.L20
.L20:
	.half	3
	.word	.L23-.L22
.L22:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/StdIf/IfxStdIf_PwmHl.c',0,0,0,0,0
.L23:
	.byte	5,32,7,0,5,2
	.word	.L3
	.byte	3,49,1,5,30,9
	.half	.L24-.L3
	.byte	1,5,32,9
	.half	.L25-.L24
	.byte	3,1,1,5,30,9
	.half	.L26-.L25
	.byte	1,5,32,9
	.half	.L27-.L26
	.byte	3,1,1,5,30,9
	.half	.L28-.L27
	.byte	1,5,32,9
	.half	.L29-.L28
	.byte	3,1,1,5,30,9
	.half	.L30-.L29
	.byte	1,5,32,9
	.half	.L31-.L30
	.byte	3,1,1,5,30,9
	.half	.L32-.L31
	.byte	1,5,32,9
	.half	.L33-.L32
	.byte	3,1,1,5,30,9
	.half	.L34-.L33
	.byte	1,5,32,9
	.half	.L35-.L34
	.byte	3,1,1,5,30,9
	.half	.L36-.L35
	.byte	1,5,32,9
	.half	.L37-.L36
	.byte	3,1,1,5,30,9
	.half	.L38-.L37
	.byte	1,5,1,9
	.half	.L39-.L38
	.byte	3,1,1,7,9
	.half	.L12-.L39
	.byte	0,1,1
.L21:
	.sdecl	'.debug_ranges',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_ranges'
.L11:
	.word	-1,.L3,0,.L12-.L3,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L13-.L3
	.half	2
	.byte	138,0
	.word	0,0
.L15:
	.word	-1,.L3,0,.L13-.L3
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L40:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxStdIf_PwmHl_initConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L40,.L3,.L13-.L3
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
