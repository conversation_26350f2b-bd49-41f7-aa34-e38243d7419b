	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18480a --dep-file=Ifx_LowPassPt1F32.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.src ../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c'

	
$TC16X
	
	.sdecl	'.text.Ifx_LowPassPt1F32.Ifx_LowPassPt1F32_init',code,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.text.Ifx_LowPassPt1F32.Ifx_LowPassPt1F32_init'
	.align	2
	
	.global	Ifx_LowPassPt1F32_init
; Function Ifx_LowPassPt1F32_init
.L4:
Ifx_LowPassPt1F32_init:	.type	func
	movh	d15,#16256
.L43:
	ld.w	d0,[a5]
.L44:
	div.f	d0,d15,d0
.L32:
	movh	d1,#16256
.L45:
	ld.w	d15,[a5]8
.L46:
	div.f	d15,d0,d15
.L47:
	movh	d0,#16256
.L33:
	add.f	d15,d15,d0
.L48:
	div.f	d0,d1,d15
.L34:
	ld.w	d15,[a5]4
.L49:
	mul.f	d15,d15,d0
.L50:
	st.w	[a4],d15
.L51:
	st.w	[a4]4,d0
.L52:
	mov	d15,#0
.L53:
	st.w	[a4]8,d15
.L54:
	ret
.L21:
	
__Ifx_LowPassPt1F32_init_function_end:
	.size	Ifx_LowPassPt1F32_init,__Ifx_LowPassPt1F32_init_function_end-Ifx_LowPassPt1F32_init
.L15:
	; End of function
	
	.sdecl	'.text.Ifx_LowPassPt1F32.Ifx_LowPassPt1F32_do',code,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.text.Ifx_LowPassPt1F32.Ifx_LowPassPt1F32_do'
	.align	2
	
	.global	Ifx_LowPassPt1F32_do
; Function Ifx_LowPassPt1F32_do
.L6:
Ifx_LowPassPt1F32_do:	.type	func
	ld.w	d0,[a4]
.L59:
	ld.w	d15,[a4]8
.L60:
	madd.f	d0,d15,d0,d4
.L61:
	ld.w	d15,[a4]4
.L62:
	ld.w	d1,[a4]8
.L63:
	msub.f	d15,d0,d15,d1
.L64:
	st.w	[a4]8,d15
.L65:
	ld.w	d2,[a4]8
.L66:
	j	.L2
.L2:
	ret
.L29:
	
__Ifx_LowPassPt1F32_do_function_end:
	.size	Ifx_LowPassPt1F32_do,__Ifx_LowPassPt1F32_do_function_end-Ifx_LowPassPt1F32_do
.L20:
	; End of function
	
	.calls	'Ifx_LowPassPt1F32_init','',0
	.calls	'Ifx_LowPassPt1F32_do','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L8:
	.word	791
	.half	3
	.word	.L9
	.byte	4
.L7:
	.byte	1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L10
.L26:
	.byte	2
	.byte	'float',0,4,4,3,1,64,9,12,4
	.byte	'a',0
	.word	244
	.byte	4,2,35,0,4
	.byte	'b',0
	.word	244
	.byte	4,2,35,4,4
	.byte	'out',0
	.word	244
	.byte	4,2,35,8,0
.L22:
	.byte	5
	.word	253
	.byte	3,1,72,9,12,4
	.byte	'cutOffFrequency',0
	.word	244
	.byte	4,2,35,0,4
	.byte	'gain',0
	.word	244
	.byte	4,2,35,4,4
	.byte	'samplingTime',0
	.word	244
	.byte	4,2,35,8,0,6
	.word	299
.L24:
	.byte	5
	.word	366
	.byte	2
	.byte	'short int',0,2,5,7
	.byte	'__wchar_t',0,2,1,1
	.word	376
	.byte	2
	.byte	'unsigned int',0,4,7,7
	.byte	'__size_t',0,2,1,1
	.word	407
	.byte	2
	.byte	'int',0,4,5,7
	.byte	'__ptrdiff_t',0,2,1,1
	.word	440
	.byte	8,1,5
	.word	467
	.byte	7
	.byte	'__codeptr',0,2,1,1
	.word	469
	.byte	2
	.byte	'unsigned char',0,1,8,7
	.byte	'uint8',0,3,105,29
	.word	492
	.byte	2
	.byte	'unsigned short int',0,2,7,7
	.byte	'uint16',0,3,109,29
	.word	523
	.byte	2
	.byte	'unsigned long int',0,4,7,7
	.byte	'uint32',0,3,113,29
	.word	560
	.byte	7
	.byte	'sint16',0,3,126,29
	.word	376
	.byte	2
	.byte	'long int',0,4,5,7
	.byte	'sint32',0,3,131,1,29
	.word	611
	.byte	2
	.byte	'long long int',0,8,5,7
	.byte	'sint64',0,3,138,1,29
	.word	639
	.byte	7
	.byte	'float32',0,3,167,1,29
	.word	244
	.byte	9
	.byte	'void',0,5
	.word	689
	.byte	7
	.byte	'pvoid',0,4,57,28
	.word	695
	.byte	7
	.byte	'Ifx_TickTime',0,4,79,28
	.word	639
	.byte	7
	.byte	'Ifx_LowPassPt1F32',0,1,69,3
	.word	253
	.byte	7
	.byte	'Ifx_LowPassPt1F32_Config',0,1,77,3
	.word	299
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,73,19,11,15,56,9,0,0,5,15,0,73,19,0,0,6,38,0,73,19,0,0,7,22,0,3,8,58,15,59,15,57,15,73
	.byte	19,0,0,8,21,0,54,15,0,0,9,59,0,3,8,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L10:
	.word	.L36-.L35
.L35:
	.half	3
	.word	.L38-.L37
.L37:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\SysSe\\Math\\Ifx_LowPassPt1F32.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0,0
.L38:
.L36:
	.sdecl	'.debug_info',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_info'
.L11:
	.word	373
	.half	3
	.word	.L12
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L14,.L13
	.byte	2
	.word	.L7
	.byte	3
	.byte	'Ifx_LowPassPt1F32_init',0,1,58,6,1,1,1
	.word	.L4,.L21,.L3
	.byte	4
	.byte	'filter',0,1,58,48
	.word	.L22,.L23
	.byte	4
	.byte	'config',0,1,58,88
	.word	.L24,.L25
	.byte	5
	.word	.L4,.L21
	.byte	6
	.byte	'tStar',0,1,60,13
	.word	.L26,.L27
	.byte	6
	.byte	'T',0,1,61,13
	.word	.L26,.L28
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_abbrev'
.L12:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_line'
.L13:
	.word	.L40-.L39
.L39:
	.half	3
	.word	.L42-.L41
.L41:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0,0,0,0,0
.L42:
	.byte	5,17,7,0,5,2
	.word	.L4
	.byte	3,60,1,5,27,9
	.half	.L43-.L4
	.byte	1,5,19,9
	.half	.L44-.L43
	.byte	1,9
	.half	.L32-.L44
	.byte	3,1,1,5,34,9
	.half	.L45-.L32
	.byte	1,5,26,9
	.half	.L46-.L45
	.byte	1,5,51,9
	.half	.L47-.L46
	.byte	1,5,49,9
	.half	.L33-.L47
	.byte	1,5,21,9
	.half	.L48-.L33
	.byte	1,5,25,9
	.half	.L34-.L48
	.byte	3,2,1,5,32,9
	.half	.L49-.L34
	.byte	1,5,17,9
	.half	.L50-.L49
	.byte	1,9
	.half	.L51-.L50
	.byte	3,1,1,5,19,9
	.half	.L52-.L51
	.byte	3,1,1,5,17,9
	.half	.L53-.L52
	.byte	1,5,1,9
	.half	.L54-.L53
	.byte	3,1,1,7,9
	.half	.L15-.L54
	.byte	0,1,1
.L40:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_ranges'
.L14:
	.word	-1,.L4,0,.L15-.L4,0,0
	.sdecl	'.debug_info',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_info'
.L16:
	.word	341
	.half	3
	.word	.L17
	.byte	4,1
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L19,.L18
	.byte	2
	.word	.L7
	.byte	3
	.byte	'Ifx_LowPassPt1F32_do',0,1,76,9
	.word	.L26
	.byte	1,1,1
	.word	.L6,.L29,.L5
	.byte	4
	.byte	'filter',0,1,76,49
	.word	.L22,.L30
	.byte	4
	.byte	'input',0,1,76,65
	.word	.L26,.L31
	.byte	5
	.word	.L6,.L29
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_abbrev'
.L17:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_line'
.L18:
	.word	.L56-.L55
.L55:
	.half	3
	.word	.L58-.L57
.L57:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/Service/CpuGeneric/SysSe/Math/Ifx_LowPassPt1F32.c',0,0,0,0,0
.L58:
	.byte	5,39,7,0,5,2
	.word	.L6
	.byte	3,205,0,1,5,25,9
	.half	.L59-.L6
	.byte	1,5,31,9
	.half	.L60-.L59
	.byte	1,5,59,9
	.half	.L61-.L60
	.byte	1,5,71,9
	.half	.L62-.L61
	.byte	1,5,51,9
	.half	.L63-.L62
	.byte	1,5,17,9
	.half	.L64-.L63
	.byte	1,5,18,9
	.half	.L65-.L64
	.byte	3,1,1,5,5,9
	.half	.L66-.L65
	.byte	1,5,1,9
	.half	.L2-.L66
	.byte	3,1,1,7,9
	.half	.L20-.L2
	.byte	0,1,1
.L56:
	.sdecl	'.debug_ranges',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_ranges'
.L19:
	.word	-1,.L6,0,.L20-.L6,0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_loc'
.L5:
	.word	-1,.L6,0,.L29-.L6
	.half	2
	.byte	138,0
	.word	0,0
.L30:
	.word	-1,.L6,0,.L29-.L6
	.half	1
	.byte	100
	.word	0,0
.L31:
	.word	-1,.L6,0,.L29-.L6
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_loc'
.L3:
	.word	-1,.L4,0,.L21-.L4
	.half	2
	.byte	138,0
	.word	0,0
.L28:
	.word	-1,.L4,.L32-.L4,.L33-.L4
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L25:
	.word	-1,.L4,0,.L21-.L4
	.half	1
	.byte	101
	.word	0,0
.L23:
	.word	-1,.L4,0,.L21-.L4
	.half	1
	.byte	100
	.word	0,0
.L27:
	.word	-1,.L4,.L34-.L4,.L21-.L4
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L67:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Ifx_LowPassPt1F32_init')
	.sect	'.debug_frame'
	.word	20
	.word	.L67,.L4,.L21-.L4
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Ifx_LowPassPt1F32_do')
	.sect	'.debug_frame'
	.word	24
	.word	.L67,.L6,.L29-.L6
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	; Module end
