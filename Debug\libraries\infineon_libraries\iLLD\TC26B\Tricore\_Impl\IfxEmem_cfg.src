	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc35148a --dep-file=IfxEmem_cfg.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c'

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	3603
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2
	.byte	'short int',0,2,5,3
	.byte	'__wchar_t',0,1,1,1
	.word	233
	.byte	2
	.byte	'unsigned int',0,4,7,3
	.byte	'__size_t',0,1,1,1
	.word	264
	.byte	2
	.byte	'int',0,4,5,3
	.byte	'__ptrdiff_t',0,1,1,1
	.word	297
	.byte	4,1,5
	.word	324
	.byte	3
	.byte	'__codeptr',0,1,1,1
	.word	326
	.byte	2
	.byte	'unsigned char',0,1,8,3
	.byte	'uint8',0,2,105,29
	.word	349
	.byte	2
	.byte	'unsigned short int',0,2,7,3
	.byte	'uint16',0,2,109,29
	.word	380
	.byte	2
	.byte	'unsigned long int',0,4,7,3
	.byte	'uint32',0,2,113,29
	.word	417
	.byte	3
	.byte	'sint16',0,2,126,29
	.word	233
	.byte	2
	.byte	'long int',0,4,5,3
	.byte	'sint32',0,2,131,1,29
	.word	468
	.byte	2
	.byte	'long long int',0,8,5,3
	.byte	'sint64',0,2,138,1,29
	.word	496
	.byte	2
	.byte	'float',0,4,4,3
	.byte	'float32',0,2,167,1,29
	.word	529
	.byte	6
	.byte	'void',0,5
	.word	555
	.byte	3
	.byte	'pvoid',0,3,57,28
	.word	561
	.byte	3
	.byte	'Ifx_TickTime',0,3,79,28
	.word	496
	.byte	2
	.byte	'unsigned int',0,4,7,7
	.byte	'_Ifx_EMEM_CLC_Bits',0,4,45,16,4,8
	.byte	'DISR',0,4
	.word	601
	.byte	1,31,2,35,0,8
	.byte	'DISS',0,4
	.word	601
	.byte	1,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	601
	.byte	30,0,2,35,0,0,3
	.byte	'Ifx_EMEM_CLC_Bits',0,4,50,3
	.word	617
	.byte	7
	.byte	'_Ifx_EMEM_ID_Bits',0,4,53,16,4,8
	.byte	'MOD_REV',0,4
	.word	601
	.byte	8,24,2,35,0,8
	.byte	'MOD_TYPE',0,4
	.word	601
	.byte	8,16,2,35,0,8
	.byte	'MODNUMBER',0,4
	.word	601
	.byte	16,0,2,35,0,0,3
	.byte	'Ifx_EMEM_ID_Bits',0,4,58,3
	.word	722
	.byte	7
	.byte	'_Ifx_EMEM_SBRCTR_Bits',0,4,61,16,4,8
	.byte	'STBLOCK',0,4
	.word	601
	.byte	1,31,2,35,0,8
	.byte	'STBULK',0,4
	.word	601
	.byte	3,28,2,35,0,8
	.byte	'STBSLK',0,4
	.word	601
	.byte	4,24,2,35,0,8
	.byte	'ACGSXCM0',0,4
	.word	601
	.byte	1,23,2,35,0,8
	.byte	'reserved_9',0,4
	.word	601
	.byte	3,20,2,35,0,8
	.byte	'ACGEN',0,4
	.word	601
	.byte	1,19,2,35,0,8
	.byte	'ACGSXTM0',0,4
	.word	601
	.byte	1,18,2,35,0,8
	.byte	'ACGSXTM1',0,4
	.word	601
	.byte	1,17,2,35,0,8
	.byte	'reserved_15',0,4
	.word	601
	.byte	1,16,2,35,0,8
	.byte	'ACGST0',0,4
	.word	601
	.byte	1,15,2,35,0,8
	.byte	'ACGST1',0,4
	.word	601
	.byte	1,14,2,35,0,8
	.byte	'ACGST2',0,4
	.word	601
	.byte	1,13,2,35,0,8
	.byte	'ACGST3',0,4
	.word	601
	.byte	1,12,2,35,0,8
	.byte	'ACGST4',0,4
	.word	601
	.byte	1,11,2,35,0,8
	.byte	'ACGST5',0,4
	.word	601
	.byte	1,10,2,35,0,8
	.byte	'ACGST6',0,4
	.word	601
	.byte	1,9,2,35,0,8
	.byte	'ACGST7',0,4
	.word	601
	.byte	1,8,2,35,0,8
	.byte	'ACGST8',0,4
	.word	601
	.byte	1,7,2,35,0,8
	.byte	'ACGST9',0,4
	.word	601
	.byte	1,6,2,35,0,8
	.byte	'ACGST10',0,4
	.word	601
	.byte	1,5,2,35,0,8
	.byte	'ACGST11',0,4
	.word	601
	.byte	1,4,2,35,0,8
	.byte	'ACGST12',0,4
	.word	601
	.byte	1,3,2,35,0,8
	.byte	'ACGST13',0,4
	.word	601
	.byte	1,2,2,35,0,8
	.byte	'ACGST14',0,4
	.word	601
	.byte	1,1,2,35,0,8
	.byte	'ACGST15',0,4
	.word	601
	.byte	1,0,2,35,0,0,3
	.byte	'Ifx_EMEM_SBRCTR_Bits',0,4,88,3
	.word	831
	.byte	7
	.byte	'_Ifx_EMEM_TILECC_Bits',0,4,91,16,4,8
	.byte	'T0',0,4
	.word	601
	.byte	1,31,2,35,0,8
	.byte	'T1',0,4
	.word	601
	.byte	1,30,2,35,0,8
	.byte	'T2',0,4
	.word	601
	.byte	1,29,2,35,0,8
	.byte	'T3',0,4
	.word	601
	.byte	1,28,2,35,0,8
	.byte	'T4',0,4
	.word	601
	.byte	1,27,2,35,0,8
	.byte	'T5',0,4
	.word	601
	.byte	1,26,2,35,0,8
	.byte	'T6',0,4
	.word	601
	.byte	1,25,2,35,0,8
	.byte	'T7',0,4
	.word	601
	.byte	1,24,2,35,0,8
	.byte	'T8',0,4
	.word	601
	.byte	1,23,2,35,0,8
	.byte	'T9',0,4
	.word	601
	.byte	1,22,2,35,0,8
	.byte	'T10',0,4
	.word	601
	.byte	1,21,2,35,0,8
	.byte	'T11',0,4
	.word	601
	.byte	1,20,2,35,0,8
	.byte	'T12',0,4
	.word	601
	.byte	1,19,2,35,0,8
	.byte	'T13',0,4
	.word	601
	.byte	1,18,2,35,0,8
	.byte	'T14',0,4
	.word	601
	.byte	1,17,2,35,0,8
	.byte	'T15',0,4
	.word	601
	.byte	1,16,2,35,0,8
	.byte	'XTM0',0,4
	.word	601
	.byte	1,15,2,35,0,8
	.byte	'XTM1',0,4
	.word	601
	.byte	1,14,2,35,0,8
	.byte	'reserved_18',0,4
	.word	601
	.byte	14,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECC_Bits',0,4,112,3
	.word	1359
	.byte	7
	.byte	'_Ifx_EMEM_TILECONFIG_Bits',0,4,115,16,4,8
	.byte	'T0',0,4
	.word	601
	.byte	2,30,2,35,0,8
	.byte	'T1',0,4
	.word	601
	.byte	2,28,2,35,0,8
	.byte	'T2',0,4
	.word	601
	.byte	2,26,2,35,0,8
	.byte	'T3',0,4
	.word	601
	.byte	2,24,2,35,0,8
	.byte	'T4',0,4
	.word	601
	.byte	2,22,2,35,0,8
	.byte	'T5',0,4
	.word	601
	.byte	2,20,2,35,0,8
	.byte	'T6',0,4
	.word	601
	.byte	2,18,2,35,0,8
	.byte	'T7',0,4
	.word	601
	.byte	2,16,2,35,0,8
	.byte	'T8',0,4
	.word	601
	.byte	2,14,2,35,0,8
	.byte	'T9',0,4
	.word	601
	.byte	2,12,2,35,0,8
	.byte	'T10',0,4
	.word	601
	.byte	2,10,2,35,0,8
	.byte	'T11',0,4
	.word	601
	.byte	2,8,2,35,0,8
	.byte	'T12',0,4
	.word	601
	.byte	2,6,2,35,0,8
	.byte	'T13',0,4
	.word	601
	.byte	2,4,2,35,0,8
	.byte	'T14',0,4
	.word	601
	.byte	2,2,2,35,0,8
	.byte	'T15',0,4
	.word	601
	.byte	2,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECONFIG_Bits',0,4,133,1,3
	.word	1701
	.byte	7
	.byte	'_Ifx_EMEM_TILECONFIGXM_Bits',0,4,136,1,16,4,8
	.byte	'XCM0',0,4
	.word	601
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	601
	.byte	14,16,2,35,0,8
	.byte	'XTM0',0,4
	.word	601
	.byte	2,14,2,35,0,8
	.byte	'XTM1',0,4
	.word	601
	.byte	2,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	601
	.byte	12,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECONFIGXM_Bits',0,4,143,1,3
	.word	1997
	.byte	7
	.byte	'_Ifx_EMEM_TILECT_Bits',0,4,146,1,16,4,8
	.byte	'T0',0,4
	.word	601
	.byte	1,31,2,35,0,8
	.byte	'T1',0,4
	.word	601
	.byte	1,30,2,35,0,8
	.byte	'T2',0,4
	.word	601
	.byte	1,29,2,35,0,8
	.byte	'T3',0,4
	.word	601
	.byte	1,28,2,35,0,8
	.byte	'T4',0,4
	.word	601
	.byte	1,27,2,35,0,8
	.byte	'T5',0,4
	.word	601
	.byte	1,26,2,35,0,8
	.byte	'T6',0,4
	.word	601
	.byte	1,25,2,35,0,8
	.byte	'T7',0,4
	.word	601
	.byte	1,24,2,35,0,8
	.byte	'T8',0,4
	.word	601
	.byte	1,23,2,35,0,8
	.byte	'T9',0,4
	.word	601
	.byte	1,22,2,35,0,8
	.byte	'T10',0,4
	.word	601
	.byte	1,21,2,35,0,8
	.byte	'T11',0,4
	.word	601
	.byte	1,20,2,35,0,8
	.byte	'T12',0,4
	.word	601
	.byte	1,19,2,35,0,8
	.byte	'T13',0,4
	.word	601
	.byte	1,18,2,35,0,8
	.byte	'T14',0,4
	.word	601
	.byte	1,17,2,35,0,8
	.byte	'T15',0,4
	.word	601
	.byte	1,16,2,35,0,8
	.byte	'XTM0',0,4
	.word	601
	.byte	1,15,2,35,0,8
	.byte	'XTM1',0,4
	.word	601
	.byte	1,14,2,35,0,8
	.byte	'reserved_18',0,4
	.word	601
	.byte	14,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECT_Bits',0,4,167,1,3
	.word	2161
	.byte	7
	.byte	'_Ifx_EMEM_TILESTATE_Bits',0,4,170,1,16,4,8
	.byte	'TILE0',0,4
	.word	601
	.byte	2,30,2,35,0,8
	.byte	'TILE1',0,4
	.word	601
	.byte	2,28,2,35,0,8
	.byte	'TILE2',0,4
	.word	601
	.byte	2,26,2,35,0,8
	.byte	'TILE3',0,4
	.word	601
	.byte	2,24,2,35,0,8
	.byte	'TILE4',0,4
	.word	601
	.byte	2,22,2,35,0,8
	.byte	'TILE5',0,4
	.word	601
	.byte	2,20,2,35,0,8
	.byte	'TILE6',0,4
	.word	601
	.byte	2,18,2,35,0,8
	.byte	'TILE7',0,4
	.word	601
	.byte	2,16,2,35,0,8
	.byte	'TILE8',0,4
	.word	601
	.byte	2,14,2,35,0,8
	.byte	'TILE9',0,4
	.word	601
	.byte	2,12,2,35,0,8
	.byte	'TILE10',0,4
	.word	601
	.byte	2,10,2,35,0,8
	.byte	'TILE11',0,4
	.word	601
	.byte	2,8,2,35,0,8
	.byte	'TILE12',0,4
	.word	601
	.byte	2,6,2,35,0,8
	.byte	'TILE13',0,4
	.word	601
	.byte	2,4,2,35,0,8
	.byte	'TILE14',0,4
	.word	601
	.byte	2,2,2,35,0,8
	.byte	'TILE15',0,4
	.word	601
	.byte	2,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILESTATE_Bits',0,4,188,1,3
	.word	2505
	.byte	7
	.byte	'_Ifx_EMEM_TILESTATEXM_Bits',0,4,191,1,16,4,8
	.byte	'XCM0',0,4
	.word	601
	.byte	2,30,2,35,0,8
	.byte	'reserved_2',0,4
	.word	601
	.byte	14,16,2,35,0,8
	.byte	'XTM0',0,4
	.word	601
	.byte	2,14,2,35,0,8
	.byte	'XTM1',0,4
	.word	601
	.byte	2,12,2,35,0,8
	.byte	'reserved_20',0,4
	.word	601
	.byte	12,0,2,35,0,0,3
	.byte	'Ifx_EMEM_TILESTATEXM_Bits',0,4,198,1,3
	.word	2848
	.byte	9,4,206,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	617
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_CLC',0,4,211,1,3
	.word	3010
	.byte	9,4,214,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	722
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_ID',0,4,219,1,3
	.word	3072
	.byte	9,4,222,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	831
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_SBRCTR',0,4,227,1,3
	.word	3133
	.byte	9,4,230,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	1359
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECC',0,4,235,1,3
	.word	3198
	.byte	9,4,238,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	1701
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECONFIG',0,4,243,1,3
	.word	3263
	.byte	9,4,246,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	1997
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECONFIGXM',0,4,251,1,3
	.word	3332
	.byte	9,4,254,1,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	2161
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILECT',0,4,131,2,3
	.word	3403
	.byte	9,4,134,2,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	2505
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILESTATE',0,4,139,2,3
	.word	3468
	.byte	9,4,142,2,9,4,10
	.byte	'U',0
	.word	264
	.byte	4,2,35,0,10
	.byte	'I',0
	.word	297
	.byte	4,2,35,0,10
	.byte	'B',0
	.word	2848
	.byte	4,2,35,0,0,3
	.byte	'Ifx_EMEM_TILESTATEXM',0,4,147,2,3
	.word	3536
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,22,0,3,8,58,15,59,15,57,15
	.byte	73,19,0,0,4,21,0,54,15,0,0,5,15,0,73,19,0,0,6,59,0,3,8,0,0,7,19,1,3,8,58,15,59,15,57,15,11,15,0,0,8,13
	.byte	0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,9,23,1,58,15,59,15,57,15,11,15,0,0,10,13,0,3,8,73,19,11,15,56,9
	.byte	0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_Impl/IfxEmem_cfg.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxEmem_regdef.h',0,1,0,0,0
.L9:
.L7:
	; Module end
