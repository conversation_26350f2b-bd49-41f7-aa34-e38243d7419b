#ifndef CODE_ASR_CTRL_H_
#define CODE_ASR_CTRL_H_

#define ASR_TARGET_IP           "ws-api.xfyun.cn"
#define ASR_TARGET_PORT         "80"
#define ASR_DOMAIN              "wss://ws-api.xfyun.cn/v2/iat"
#define ASR_AUDIO_ADC           ADC0_CH4_A4
#define ASR_BUTTON              P11_3
#define ASR_PIT                 CCU60_CH0

#define ASR_WIFI_SSID           "xxxxxxxxxxxxx"                         // wifi名称 wifi需要是2.4G频率
#define ASR_WIFI_PASSWORD       "xxxxxxxxxx"                            // wifi密码

#define ASR_APIID               "xxxxxxxx"                              // 讯飞的id
#define ASR_APISecret           "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"      // 讯飞的Secret
#define ASR_APIKey              "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"      // 讯飞的Key

#define RANDOM_NUM_ADC          ADC0_CH5_A5                             // 使用ADC生成随机数

#endif /* CODE_ASR_CTRL_H_ */
