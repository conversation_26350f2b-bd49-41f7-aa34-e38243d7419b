	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18272a --dep-file=IfxQspi.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c'

	
$TC16X
	
	.sdecl	'.text.IfxQspi.IfxQspi_calcRealBaudrate',code,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.text.IfxQspi.IfxQspi_calcRealBaudrate'
	.align	2
	
	.global	IfxQspi_calcRealBaudrate
; Function IfxQspi_calcRealBaudrate
.L108:
IfxQspi_calcRealBaudrate:	.type	func
	sub.a	a10,#32
.L406:
	mov.aa	a12,a4
.L408:
	mov	d15,#8
.L610:
	div	e8,d4,d15
.L409:
	call	IfxScuCcu_getMaxFrequency
.L407:
	mul	d15,d9,#4
	addsc.a	a2,a10,d15,#0
.L611:
	mul	d15,d9,#4
	addsc.a	a15,a12,d15,#0
.L612:
	ld.w	d15,[a15]32
.L613:
	st.w	[a2],d15
.L614:
	ld.bu	d15,[a12]16
	and	d15,#255
.L615:
	add	d15,#1
	itof	d15,d15
.L616:
	div.f	d0,d2,d15
.L410:
	mul	d15,d9,#4
	addsc.a	a15,a10,d15,#0
.L617:
	ld.bu	d15,[a15]
	and	d15,#63
.L618:
	add	d15,#1
	itof	d15,d15
.L619:
	div.f	d0,d0,d15
.L620:
	mul	d15,d9,#4
	addsc.a	a15,a10,d15,#0
.L621:
	ld.bu	d15,[a15]
	extr.u	d15,d15,#6,#2
.L622:
	add	d1,d15,#1
.L623:
	mul	d15,d9,#4
	addsc.a	a15,a10,d15,#0
.L624:
	ld.bu	d15,[a15]1
	and	d15,#3
.L625:
	add	d1,d15
.L626:
	mul	d15,d9,#4
	addsc.a	a15,a10,d15,#0
.L627:
	ld.bu	d15,[a15]1
	extr.u	d15,d15,#2,#2
.L628:
	add	d1,d15
	itof	d15,d1
.L629:
	div.f	d2,d0,d15
.L411:
	j	.L2
.L2:
	ret
.L280:
	
__IfxQspi_calcRealBaudrate_function_end:
	.size	IfxQspi_calcRealBaudrate,__IfxQspi_calcRealBaudrate_function_end-IfxQspi_calcRealBaudrate
.L184:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_calculateBasicConfigurationValue',code,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.text.IfxQspi.IfxQspi_calculateBasicConfigurationValue'
	.align	2
	
	.global	IfxQspi_calculateBasicConfigurationValue
; Function IfxQspi_calculateBasicConfigurationValue
.L110:
IfxQspi_calculateBasicConfigurationValue:	.type	func
	sub.a	a10,#8
.L412:
	mov	d8,d4
.L414:
	mov.aa	a15,a5
.L415:
	extr.u	d15,d5,#23,#8
	ne	d15,d15,#0
	jeq	d15,#0,.L3
.L3:
	mov	d9,#0
.L416:
	lea	a6,[a10]0
	mov	d4,d8
	mov.aa	a5,a15
	call	IfxQspi_calculateDelayConstants
.L413:
	mov	d15,#0
.L634:
	insert	d9,d9,d15,#0,#1
.L635:
	ld.bu	d15,[a10]
.L636:
	insert	d9,d9,d15,#1,#3
.L637:
	ld.bu	d15,[a10]1
.L638:
	insert	d9,d9,d15,#4,#3
.L639:
	ld.bu	d15,[a10]2
.L640:
	insert	d9,d9,d15,#7,#3
.L641:
	ld.bu	d15,[a10]3
.L642:
	insert	d9,d9,d15,#10,#3
.L643:
	ld.bu	d15,[a10]4
.L644:
	insert	d9,d9,d15,#13,#3
.L645:
	ld.bu	d15,[a10]5
.L646:
	insert	d9,d9,d15,#16,#3
.L647:
	ld.bu	d15,[a15]1
.L648:
	jnz.t	d15:6,.L4
.L649:
	mov	d15,#0
.L650:
	j	.L5
.L4:
	mov	d15,#1
.L5:
	insert	d9,d9,d15,#19,#1
.L651:
	mov	d15,#0
.L652:
	insert	d9,d9,d15,#20,#1
.L653:
	ld.bu	d15,[a15]
.L654:
	jnz.t	d15:5,.L6
.L655:
	mov	d15,#0
.L656:
	j	.L7
.L6:
	mov	d15,#1
.L7:
	insert	d9,d9,d15,#21,#1
.L657:
	mov	d15,#0
.L658:
	insert	d9,d9,d15,#22,#1
.L659:
	ld.hu	d15,[a15]0
	extr.u	d15,d15,#6,#6
.L660:
	add	d15,#-1
	extr.u	d15,d15,#0,#16
.L661:
	insert	d9,d9,d15,#23,#5
.L662:
	insert	d9,d9,d8,#28,#4
.L663:
	mov	d2,d9
.L417:
	j	.L8
.L8:
	ret
.L288:
	
__IfxQspi_calculateBasicConfigurationValue_function_end:
	.size	IfxQspi_calculateBasicConfigurationValue,__IfxQspi_calculateBasicConfigurationValue_function_end-IfxQspi_calculateBasicConfigurationValue
.L189:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_calculateExtendedConfigurationValue',code,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.text.IfxQspi.IfxQspi_calculateExtendedConfigurationValue'
	.align	2
	
	.global	IfxQspi_calculateExtendedConfigurationValue
; Function IfxQspi_calculateExtendedConfigurationValue
.L112:
IfxQspi_calculateExtendedConfigurationValue:	.type	func
	sub.a	a10,#8
.L418:
	mov.aa	a12,a4
.L420:
	mov.aa	a15,a5
.L421:
	jeq	d4,#0,.L9
.L9:
	mov	d15,#0
.L422:
	st.w	[a10]4,d15
.L308:
	ld.w	d15,[a12]
.L423:
	jeq	d15,#0,.L10
.L10:
	call	IfxScuCcu_getMaxFrequency
.L419:
	j	.L11
.L11:
	ld.bu	d15,[a12]16
	and	d15,#255
.L668:
	add	d15,#1
	itof	d15,d15
.L669:
	div.f	d4,d2,d15
.L670:
	j	.L12
.L12:
	mov	d8,#0
	mov	d9,#0
	addih	d9,d9,#16368
.L671:
	call	__f_ftod
	mov	e6,d3,d2
.L672:
	mov	e4,d9,d8
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L424:
	st.w	[a10],d2
.L425:
	ld.w	d8,[a15]4
.L426:
	mov	d10,#8
.L427:
	mov	d14,#1
.L428:
	mov	d4,d8
.L430:
	call	__f_ftod
.L431:
	extr.u	d0,d3,#20,#11
	eq	d15,d0,#0
.L673:
	jeq	d15,#0,.L13
.L674:
	movh	d8,#16256
.L13:
	mov	d12,#0
	mov	d13,#0
	addih	d13,d13,#16368
.L675:
	mov	d4,d8
.L432:
	call	__f_ftod
.L433:
	mov	e6,d3,d2
.L676:
	mov	e4,d13,d12
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L434:
	mov	d12,d2
.L435:
	mov	d9,#9216
.L436:
	addih	d9,d9,#18804
.L337:
	mov	d8,#8
.L338:
	j	.L14
.L15:
	itof	d15,d8
.L677:
	ld.w	d0,[a10]
.L437:
	mul.f	d11,d0,d15
.L439:
	div.f	d4,d12,d11
	call	__f_ftod
.L438:
	mov	e4,d3,d2
.L678:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L679:
	call	__d_add
	mov	e4,d3,d2
.L680:
	call	__d_dtoi
.L441:
	mov	d13,d2
.L442:
	mov	d15,#64
.L681:
	jge	d15,d13,.L16
.L682:
	mov	d13,#64
.L683:
	j	.L17
.L16:
	mul	d15,d13,d8
.L684:
	jge	d15,#4,.L18
.L685:
	mov	d13,#2
.L686:
	j	.L19
.L18:
	jge	d13,#1,.L20
.L687:
	mov	d13,#1
.L20:
.L19:
.L17:
	itof	d15,d13
.L688:
	mul.f	d11,d11,d15
.L440:
	sub.f	d4,d11,d12
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jne	d2,#0,.L21
	sub.f	d11,d11,d12
.L443:
	j	.L22
.L21:
	sub.f	d15,d11,d12
	insn.t	d11,d15:31,d15:31
.L22:
	cmp.f	d15,d11,d9
	jnz.t	d15:2,.L23
.L689:
	cmp.f	d15,d11,d9
	jnz.t	d15:2,.L24
	cmp.f	d15,d11,d9
	jnz.t	d15:0,.L25
.L444:
	jnz.t	d10:0,.L26
.L25:
.L24:
	mov	d9,d11
.L690:
	mov	d10,d8
.L445:
	mov	d14,d13
.L26:
	jnz.t	d10:0,.L27
.L446:
	mov	d4,d11
	call	__f_ftod
	mov	e4,d3,d2
	mov	d6,#0
	mov	d7,#0
	call	__d_fgt
	jne	d2,#0,.L28
	mov	d4,d11
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jeq	d2,#0,.L29
.L28:
	mov	d15,#0
.L447:
	j	.L30
.L29:
	mov	d15,#1
.L30:
	jeq	d15,#0,.L31
.L691:
	j	.L32
.L31:
.L27:
.L23:
	add	d8,#-1
.L14:
	jge	d8,#2,.L15
.L32:
	mov	d15,#8
.L339:
	jlt	d15,d14,.L33
.L449:
	jz.t	d10:0,.L34
.L450:
	jnz.t	d14:0,.L35
.L692:
	mov	d15,d14
.L448:
	mov	d14,d10
.L451:
	mov	d10,d15
.L35:
.L34:
.L33:
	mov	d15,#2
.L452:
	div	e2,d10,d15
.L453:
	add	d15,d2,#-3
	extr	d4,d15,#0,#8
.L455:
	add	d14,#-1
.L429:
	extr.u	d0,d14,#0,#8
.L693:
	ld.w	d15,[a10]4
.L456:
	insert	d15,d15,d0,#0,#6
	st.w	[a10]4,d15
.L694:
	mov	d15,#2
.L457:
	div	e0,d10,d15
.L458:
	add	d15,d2,d1
.L695:
	add	d15,#-1
	extr.u	d0,d15,#0,#8
.L696:
	ld.w	d15,[a10]4
.L459:
	insert	d15,d15,d0,#6,#2
	st.w	[a10]4,d15
.L697:
	jlt	d4,#1,.L36
.L698:
	mov	d0,d4
.L699:
	j	.L37
.L36:
	mov	d0,#0
.L37:
	insert	d15,d15,d0,#10,#2
	st.w	[a10]4,d15
.L700:
	jlt	d4,#1,.L38
.L701:
	mov	d2,#3
.L702:
	j	.L39
.L38:
.L39:
	extr.u	d0,d2,#0,#8
.L703:
	insert	d15,d15,d0,#8,#2
	st.w	[a10]4,d15
.L704:
	ld.bu	d15,[a15]8
.L460:
	jnz.t	d15:4,.L40
.L705:
	mov	d0,#1
.L706:
	j	.L41
.L40:
	mov	d0,#0
.L41:
	ld.w	d15,[a10]4
.L461:
	insert	d15,d15,d0,#12,#1
	st.w	[a10]4,d15
.L707:
	ld.bu	d15,[a15]8
.L462:
	jnz.t	d15:3,.L42
.L708:
	mov	d0,#0
.L709:
	j	.L43
.L42:
	mov	d0,#1
.L43:
	ld.w	d15,[a10]4
.L463:
	insert	d15,d15,d0,#13,#1
	st.w	[a10]4,d15
.L710:
	ld.bu	d15,[a15]9
.L464:
	extr.u	d15,d15,#5,#1
.L711:
	ld.w	d2,[a10]4
.L454:
	insert	d2,d2,d15,#14,#1
	st.w	[a10]4,d2
.L712:
	j	.L44
.L44:
	ret
.L300:
	
__IfxQspi_calculateExtendedConfigurationValue_function_end:
	.size	IfxQspi_calculateExtendedConfigurationValue,__IfxQspi_calculateExtendedConfigurationValue_function_end-IfxQspi_calculateExtendedConfigurationValue
.L194:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_calculatePrescaler',code,cluster('IfxQspi_calculatePrescaler')
	.sect	'.text.IfxQspi.IfxQspi_calculatePrescaler'
	.align	2
	
	.global	IfxQspi_calculatePrescaler
; Function IfxQspi_calculatePrescaler
.L114:
IfxQspi_calculatePrescaler:	.type	func
	movh	d15,#16384
.L717:
	div.f	d11,d4,d15
.L348:
	ld.w	d15,[a4]
	jeq	d15,#0,.L45
.L45:
	call	IfxScuCcu_getMaxFrequency
.L465:
	mov	d8,d2
	j	.L46
.L46:
	mov.u	d9,#38528
.L466:
	addih	d9,d9,#19224
.L350:
	mov	d10,#0
.L467:
	mov	d12,#0
.L468:
	j	.L47
.L48:
	mov	d15,#1
.L718:
	mul	d0,d12,#4
.L719:
	sh	d15,d15,d0
	utof	d15,d15
.L720:
	div.f	d15,d8,d15
.L469:
	sub.f	d4,d15,d11
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jne	d2,#0,.L49
	sub.f	d0,d15,d11
	j	.L50
.L49:
	sub.f	d15,d15,d11
.L470:
	insn.t	d0,d15:31,d15:31
.L50:
	cmp.f	d15,d0,d9
	jnz.t	d15:2,.L51
.L721:
	mov	d9,d0
.L722:
	mov	d10,d12
.L51:
	add	d12,#1
.L47:
	jlt.u	d12,#8,.L48
.L723:
	mov	d2,d10
.L471:
	j	.L52
.L52:
	ret
.L341:
	
__IfxQspi_calculatePrescaler_function_end:
	.size	IfxQspi_calculatePrescaler,__IfxQspi_calculatePrescaler_function_end-IfxQspi_calculatePrescaler
.L199:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_calculateTimeQuantumLength',code,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.text.IfxQspi.IfxQspi_calculateTimeQuantumLength'
	.align	2
	
	.global	IfxQspi_calculateTimeQuantumLength
; Function IfxQspi_calculateTimeQuantumLength
.L116:
IfxQspi_calculateTimeQuantumLength:	.type	func
	mov	d8,d4
.L473:
	jz.a	a4,.L53
.L53:
	call	IfxScuCcu_getMaxFrequency
.L472:
	mov	d9,d2
.L475:
	mov	d4,d8
.L476:
	call	__f_ftod
.L474:
	mov	e4,d3,d2
	mov	d6,#0
	mov	d7,#0
	call	__d_fgt
	jne	d2,#0,.L54
.L54:
	mov	d4,d9
.L477:
	call	__f_ftod
.L478:
	mov	e10,d3,d2
.L728:
	mov	d4,d8
.L479:
	call	__f_ftod
.L480:
	mov	e4,d3,d2
.L729:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16400
.L730:
	call	__d_mul
	mov	e6,d3,d2
.L731:
	mov	e4,d11,d10
	call	__d_div
	mov	e4,d3,d2
	call	__d_dtof
.L481:
	mov	d10,d2
.L482:
	ftoiz	d15,d10
	itof	d15,d15
	sub.f	d4,d10,d15
	call	__f_ftod
	mov	e4,d3,d2
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
	call	__d_fgt
	jne	d2,#0,.L55
	ftoiz	d15,d10
	j	.L56
.L55:
	ftoiz	d15,d10
	add	d15,#1
.L56:
	max	d12,d15,#1
.L484:
	utof	d15,d12
	div.f	d15,d9,d15
	sub.f	d4,d8,d15
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jne	d2,#0,.L57
	utof	d15,d12
	div.f	d15,d9,d15
	sub.f	d10,d8,d15
.L483:
	j	.L58
.L57:
	utof	d15,d12
	div.f	d15,d9,d15
	sub.f	d15,d8,d15
	insn.t	d10,d15:31,d15:31
.L58:
	mov	d11,#4
.L486:
	j	.L59
.L60:
	utof	d15,d11
.L732:
	mul.f	d15,d8,d15
.L733:
	div.f	d4,d9,d15
.L487:
	call	__f_ftod
.L488:
	mov	e4,d3,d2
.L734:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L735:
	call	__d_add
	mov	e4,d3,d2
.L736:
	call	__d_dtoui
.L489:
	mov	d13,d2
.L490:
	mul	d15,d13,d11
	utof	d15,d15
.L737:
	div.f	d15,d9,d15
.L491:
	sub.f	d4,d8,d15
	call	__f_ftod
	mov	e6,d3,d2
	mov	d4,#0
	mov	d5,#0
	call	__d_fgt
	jne	d2,#0,.L61
	sub.f	d0,d8,d15
	j	.L62
.L61:
	sub.f	d15,d8,d15
.L492:
	insn.t	d0,d15:31,d15:31
.L62:
	cmp.f	d15,d0,d10
	jnz.t	d15:2,.L63
.L738:
	jlt.u	d13,#1,.L64
.L739:
	mov	d10,d0
.L740:
	mov	d12,d13
.L64:
.L63:
	extr.u	d15,d10,#23,#8
	jeq	d15,#0,.L65
.L741:
	jge.u	d13,#1,.L66
.L65:
	j	.L67
.L66:
	add	d11,#1
.L59:
	mov	d15,#504
.L742:
	jge.u	d15,d11,.L60
.L67:
	add	d12,#-1
.L485:
	max	d2,d12,#0
.L743:
	j	.L68
.L68:
	ret
.L354:
	
__IfxQspi_calculateTimeQuantumLength_function_end:
	.size	IfxQspi_calculateTimeQuantumLength,__IfxQspi_calculateTimeQuantumLength_function_end-IfxQspi_calculateTimeQuantumLength
.L204:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_getAddress',code,cluster('IfxQspi_getAddress')
	.sect	'.text.IfxQspi.IfxQspi_getAddress'
	.align	2
	
	.global	IfxQspi_getAddress
; Function IfxQspi_getAddress
.L118:
IfxQspi_getAddress:	.type	func
	jge	d4,#4,.L69
.L748:
	mul	d15,d4,#8
.L749:
	movh.a	a15,#@his(IfxQspi_cfg_indexMap)
	lea	a15,[a15]@los(IfxQspi_cfg_indexMap)
.L750:
	addsc.a	a15,a15,d15,#0
.L751:
	ld.a	a2,[a15]
.L493:
	j	.L70
.L69:
	mov.a	a2,#0
.L70:
	j	.L71
.L71:
	ret
.L365:
	
__IfxQspi_getAddress_function_end:
	.size	IfxQspi_getAddress,__IfxQspi_getAddress_function_end-IfxQspi_getAddress
.L209:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_getIndex',code,cluster('IfxQspi_getIndex')
	.sect	'.text.IfxQspi.IfxQspi_getIndex'
	.align	2
	
	.global	IfxQspi_getIndex
; Function IfxQspi_getIndex
.L120:
IfxQspi_getIndex:	.type	func
	mov	d2,#-1
.L494:
	mov	d0,#0
.L495:
	j	.L72
.L73:
	mul	d15,d0,#8
.L756:
	movh.a	a15,#@his(IfxQspi_cfg_indexMap)
	lea	a15,[a15]@los(IfxQspi_cfg_indexMap)
.L757:
	addsc.a	a15,a15,d15,#0
.L758:
	ld.a	a15,[a15]
.L759:
	jne.a	a15,a4,.L74
.L760:
	mul	d15,d0,#8
.L761:
	movh.a	a15,#@his(IfxQspi_cfg_indexMap)
	lea	a15,[a15]@los(IfxQspi_cfg_indexMap)
.L762:
	addsc.a	a15,a15,d15,#0
.L763:
	ld.w	d15,[a15]4
.L764:
	extr	d2,d15,#0,#8
.L765:
	j	.L75
.L74:
	add	d0,#1
.L72:
	jlt.u	d0,#4,.L73
.L75:
	j	.L76
.L76:
	ret
.L369:
	
__IfxQspi_getIndex_function_end:
	.size	IfxQspi_getIndex,__IfxQspi_getIndex_function_end-IfxQspi_getIndex
.L214:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_read16',code,cluster('IfxQspi_read16')
	.sect	'.text.IfxQspi.IfxQspi_read16'
	.align	2
	
	.global	IfxQspi_read16
; Function IfxQspi_read16
.L122:
IfxQspi_read16:	.type	func
	lea	a15,[a4]144
.L496:
	j	.L77
.L78:
	ld.w	d15,[a15]
.L547:
	st.h	[a5],d15
.L548:
	add.a	a5,#2
.L549:
	add	d4,#-1
.L77:
	jge	d4,#1,.L78
.L550:
	ret
.L230:
	
__IfxQspi_read16_function_end:
	.size	IfxQspi_read16,__IfxQspi_read16_function_end-IfxQspi_read16
.L149:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_read32',code,cluster('IfxQspi_read32')
	.sect	'.text.IfxQspi.IfxQspi_read32'
	.align	2
	
	.global	IfxQspi_read32
; Function IfxQspi_read32
.L124:
IfxQspi_read32:	.type	func
	lea	a15,[a4]144
.L497:
	j	.L79
.L80:
	ld.w	d15,[a15]
.L555:
	st.w	[a5],d15
.L556:
	add.a	a5,#4
.L557:
	add	d4,#-1
.L79:
	jge	d4,#1,.L80
.L558:
	ret
.L239:
	
__IfxQspi_read32_function_end:
	.size	IfxQspi_read32,__IfxQspi_read32_function_end-IfxQspi_read32
.L154:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_read8',code,cluster('IfxQspi_read8')
	.sect	'.text.IfxQspi.IfxQspi_read8'
	.align	2
	
	.global	IfxQspi_read8
; Function IfxQspi_read8
.L126:
IfxQspi_read8:	.type	func
	lea	a15,[a4]144
.L498:
	j	.L81
.L82:
	ld.w	d15,[a15]
.L563:
	st.b	[a5],d15
.L564:
	add.a	a5,#1
.L565:
	add	d4,#-1
.L81:
	jge	d4,#1,.L82
.L566:
	ret
.L245:
	
__IfxQspi_read8_function_end:
	.size	IfxQspi_read8,__IfxQspi_read8_function_end-IfxQspi_read8
.L159:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_recalcBasicConfiguration',code,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.text.IfxQspi.IfxQspi_recalcBasicConfiguration'
	.align	2
	
	.global	IfxQspi_recalcBasicConfiguration
; Function IfxQspi_recalcBasicConfiguration
.L128:
IfxQspi_recalcBasicConfiguration:	.type	func
	jne	d6,#0,.L83
.L770:
	extr.u	d15,d5,#0,#16
.L771:
	insert	d4,d4,d15,#23,#5
.L772:
	mov	d15,#1
.L773:
	insert	d4,d4,d15,#22,#1
.L83:
	insert	d4,d4,d7,#0,#1
.L774:
	mov	d2,d4
.L499:
	j	.L84
.L84:
	ret
.L373:
	
__IfxQspi_recalcBasicConfiguration_function_end:
	.size	IfxQspi_recalcBasicConfiguration,__IfxQspi_recalcBasicConfiguration_function_end-IfxQspi_recalcBasicConfiguration
.L219:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_resetModule',code,cluster('IfxQspi_resetModule')
	.sect	'.text.IfxQspi.IfxQspi_resetModule'
	.align	2
	
	.global	IfxQspi_resetModule
; Function IfxQspi_resetModule
.L130:
IfxQspi_resetModule:	.type	func
	mov.aa	a15,a4
.L501:
	call	IfxScuWdt_getCpuWatchdogPassword
.L500:
	mov	d8,d2
.L503:
	mov	d4,d8
.L502:
	call	IfxScuWdt_clearCpuEndinit
.L504:
	ld.bu	d15,[a15]244
.L571:
	or	d15,#1
	st.b	[a15]244,d15
.L572:
	ld.bu	d15,[a15]240
.L573:
	or	d15,#1
	st.b	[a15]240,d15
.L574:
	mov	d4,d8
.L505:
	call	IfxScuWdt_setCpuEndinit
.L506:
	j	.L85
.L86:
.L85:
	ld.bu	d15,[a15]244
.L575:
	jz.t	d15:1,.L86
.L576:
	mov	d4,d8
.L507:
	call	IfxScuWdt_clearCpuEndinit
.L508:
	ld.bu	d15,[a15]236
.L577:
	or	d15,#1
	st.b	[a15]236,d15
.L578:
	mov	d4,d8
.L509:
	call	IfxScuWdt_setCpuEndinit
.L510:
	ret
.L251:
	
__IfxQspi_resetModule_function_end:
	.size	IfxQspi_resetModule,__IfxQspi_resetModule_function_end-IfxQspi_resetModule
.L164:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_setSlaveSelectOutputControl',code,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.text.IfxQspi.IfxQspi_setSlaveSelectOutputControl'
	.align	2
	
	.global	IfxQspi_setSlaveSelectOutputControl
; Function IfxQspi_setSlaveSelectOutputControl
.L132:
IfxQspi_setSlaveSelectOutputControl:	.type	func
	mov	d15,#1
.L779:
	sha	d15,d15,d4
	extr.u	d15,d15,#0,#16
.L511:
	ld.w	d1,[a4]72
.L513:
	jeq	d5,#0,.L87
.L780:
	extr.u	d0,d1,#16,#16
	or	d0,d15
	insert	d1,d1,d0,#16,#16
.L781:
	j	.L88
.L87:
	extr.u	d0,d1,#16,#16
.L782:
	mov	d2,#-1
	xor	d2,d15
.L783:
	and	d0,d2
	insert	d1,d1,d0,#16,#16
.L88:
	jeq	d6,#0,.L89
.L784:
	extr.u	d0,d1,#0,#16
	or	d0,d15
	insert	d1,d1,d0,#0,#16
.L785:
	j	.L90
.L89:
	extr.u	d0,d1,#0,#16
.L786:
	mov	d2,#-1
	xor	d15,d2
.L512:
	and	d0,d15
	insert	d1,d1,d0,#0,#16
.L90:
	st.w	[a4]72,d1
.L787:
	ret
.L379:
	
__IfxQspi_setSlaveSelectOutputControl_function_end:
	.size	IfxQspi_setSlaveSelectOutputControl,__IfxQspi_setSlaveSelectOutputControl_function_end-IfxQspi_setSlaveSelectOutputControl
.L224:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_write16',code,cluster('IfxQspi_write16')
	.sect	'.text.IfxQspi.IfxQspi_write16'
	.align	2
	
	.global	IfxQspi_write16
; Function IfxQspi_write16
.L134:
IfxQspi_write16:	.type	func
	mov	d15,#8
.L583:
	div	e0,d4,d15
.L514:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
	lea	a15,[a15]100
.L515:
	j	.L91
.L92:
	ld.hu	d15,[a5]0
.L584:
	st.w	[a15],d15
.L585:
	add.a	a5,#2
.L586:
	add	d5,#-1
.L91:
	jge	d5,#1,.L92
.L587:
	ret
.L255:
	
__IfxQspi_write16_function_end:
	.size	IfxQspi_write16,__IfxQspi_write16_function_end-IfxQspi_write16
.L169:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_write32',code,cluster('IfxQspi_write32')
	.sect	'.text.IfxQspi.IfxQspi_write32'
	.align	2
	
	.global	IfxQspi_write32
; Function IfxQspi_write32
.L136:
IfxQspi_write32:	.type	func
	mov	d15,#8
.L592:
	div	e0,d4,d15
.L516:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
	lea	a15,[a15]100
.L517:
	j	.L93
.L94:
	ld.w	d15,[a5]
.L593:
	st.w	[a15],d15
.L594:
	add.a	a5,#4
.L595:
	add	d5,#-1
.L93:
	jge	d5,#1,.L94
.L596:
	ret
.L265:
	
__IfxQspi_write32_function_end:
	.size	IfxQspi_write32,__IfxQspi_write32_function_end-IfxQspi_write32
.L174:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_write8',code,cluster('IfxQspi_write8')
	.sect	'.text.IfxQspi.IfxQspi_write8'
	.align	2
	
	.global	IfxQspi_write8
; Function IfxQspi_write8
.L138:
IfxQspi_write8:	.type	func
	mov	d15,#8
.L601:
	div	e0,d4,d15
.L518:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
	lea	a15,[a15]100
.L519:
	j	.L95
.L96:
	ld.bu	d15,[a5]
.L602:
	st.w	[a15],d15
.L603:
	add.a	a5,#1
.L604:
	add	d5,#-1
.L95:
	jge	d5,#1,.L96
.L605:
	ret
.L272:
	
__IfxQspi_write8_function_end:
	.size	IfxQspi_write8,__IfxQspi_write8_function_end-IfxQspi_write8
.L179:
	; End of function
	
	.sdecl	'.text.IfxQspi.IfxQspi_calculateDelayConstants',code,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.text.IfxQspi.IfxQspi_calculateDelayConstants'
	.align	2
	
	.global	IfxQspi_calculateDelayConstants
; Function IfxQspi_calculateDelayConstants
.L140:
IfxQspi_calculateDelayConstants:	.type	func
	mov.aa	a12,a6
.L520:
	mov	d14,#0
.L521:
	mov	d12,#0
.L522:
	mov	d15,#8
.L792:
	div	e0,d4,d15
.L523:
	ld.bu	d15,[a4]16
	and	d15,#255
.L793:
	add	d13,d15,#1
.L794:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
.L795:
	ld.bu	d15,[a15]32
	and	d15,#63
.L796:
	add	d15,#1
.L797:
	mul	d13,d15
.L798:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
.L799:
	ld.bu	d15,[a15]32
	extr.u	d15,d15,#6,#2
.L800:
	add	d2,d15,#1
.L801:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
.L802:
	ld.bu	d15,[a15]33
	and	d15,#3
.L803:
	add	d2,d15
.L804:
	mul	d15,d1,#4
	addsc.a	a15,a4,d15,#0
.L805:
	ld.bu	d15,[a15]33
	extr.u	d15,d15,#2,#2
.L806:
	add	d2,d15
.L524:
	mul	d13,d2
.L807:
	add.a	a5,#2
.L808:
	mov.aa	a13,a5
.L525:
	mov	d8,#0
.L526:
	j	.L97
.L98:
	mul	d15,d8,#4
	addsc.a	a15,a13,d15,#0
	ld.w	d15,[a15]
.L809:
	mul	d15,d13
	utof	d15,d15
.L810:
	movh	d0,#16384
.L811:
	div.f	d11,d15,d0
.L527:
	mov	d10,#0
.L528:
	mov	d9,#0
.L529:
	j	.L99
.L100:
	mov	d15,#1
.L812:
	mul	d0,d9,#2
.L813:
	sha	d15,d15,d0
	itof	d15,d15
.L814:
	div.f	d4,d11,d15
	call	__f_ftod
	mov	e4,d3,d2
.L815:
	mov	d6,#0
	mov	d7,#0
	addih	d7,d7,#16352
.L816:
	call	__d_add
	mov	e4,d3,d2
.L817:
	call	__d_dtouc
.L530:
	jge.u	d2,#9,.L101
.L531:
	mul	d15,d9,#2
.L532:
	sha	d15,d2,d15
.L533:
	itof	d15,d15
.L818:
	cmp.f	d15,d15,d11
	and	d15,#6
	ne	d15,d15,#0
.L819:
	jeq	d15,#0,.L102
.L820:
	add	d2,#-1
.L821:
	max	d15,d2,#0
	extr.u	d12,d15,#0,#8
.L822:
	mov	d14,d9
.L823:
	mov	d10,#1
.L824:
	j	.L103
.L102:
	jge.u	d2,#8,.L104
.L825:
	add	d15,d2,#1
.L534:
	add	d15,#-1
.L535:
	max	d15,d15,#0
	extr.u	d12,d15,#0,#8
.L826:
	mov	d14,d9
.L827:
	mov	d10,#1
.L828:
	j	.L105
.L104:
.L101:
	add	d9,#1
.L99:
	jlt.u	d9,#8,.L100
.L105:
.L103:
	jne	d10,#0,.L106
.L829:
	mov	d12,#7
.L830:
	mov	d14,#7
.L106:
	mul	d15,d8,#2
	addsc.a	a15,a12,d15,#0
.L831:
	st.b	[a15]1,d12
.L832:
	mul	d15,d8,#2
	addsc.a	a15,a12,d15,#0
.L833:
	st.b	[a15],d14
.L834:
	add	d8,#1
.L97:
	jlt.u	d8,#3,.L98
.L835:
	ret
.L387:
	
__IfxQspi_calculateDelayConstants_function_end:
	.size	IfxQspi_calculateDelayConstants,__IfxQspi_calculateDelayConstants_function_end-IfxQspi_calculateDelayConstants
.L229:
	; End of function
	
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__f_ftod'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__d_div'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__d_dtof'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__d_add'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__d_dtoi'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','__d_fgt'
	.calls	'IfxQspi_calculatePrescaler','__f_ftod'
	.calls	'IfxQspi_calculatePrescaler','__d_fgt'
	.calls	'IfxQspi_calculateTimeQuantumLength','__f_ftod'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_fgt'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_mul'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_div'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_dtof'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_add'
	.calls	'IfxQspi_calculateTimeQuantumLength','__d_dtoui'
	.calls	'IfxQspi_calculateDelayConstants','__f_ftod'
	.calls	'IfxQspi_calculateDelayConstants','__d_add'
	.calls	'IfxQspi_calculateDelayConstants','__d_dtouc'
	.calls	'IfxQspi_calcRealBaudrate','IfxScuCcu_getMaxFrequency'
	.calls	'IfxQspi_calculateBasicConfigurationValue','IfxQspi_calculateDelayConstants'
	.calls	'IfxQspi_calculateExtendedConfigurationValue','IfxScuCcu_getMaxFrequency'
	.calls	'IfxQspi_calculatePrescaler','IfxScuCcu_getMaxFrequency'
	.calls	'IfxQspi_calculateTimeQuantumLength','IfxScuCcu_getMaxFrequency'
	.calls	'IfxQspi_resetModule','IfxScuWdt_getCpuWatchdogPassword'
	.calls	'IfxQspi_resetModule','IfxScuWdt_clearCpuEndinit'
	.calls	'IfxQspi_resetModule','IfxScuWdt_setCpuEndinit'
	.calls	'IfxQspi_calcRealBaudrate','',32
	.calls	'IfxQspi_calculateBasicConfigurationValue','',8
	.calls	'IfxQspi_calculateExtendedConfigurationValue','',8
	.calls	'IfxQspi_calculatePrescaler','',0
	.calls	'IfxQspi_calculateTimeQuantumLength','',0
	.calls	'IfxQspi_getAddress','',0
	.calls	'IfxQspi_getIndex','',0
	.calls	'IfxQspi_read16','',0
	.calls	'IfxQspi_read32','',0
	.calls	'IfxQspi_read8','',0
	.calls	'IfxQspi_recalcBasicConfiguration','',0
	.calls	'IfxQspi_resetModule','',0
	.calls	'IfxQspi_setSlaveSelectOutputControl','',0
	.calls	'IfxQspi_write16','',0
	.calls	'IfxQspi_write32','',0
	.calls	'IfxQspi_write8','',0
	.extern	IfxQspi_cfg_indexMap
	.extern	IfxScuWdt_clearCpuEndinit
	.extern	IfxScuWdt_setCpuEndinit
	.extern	IfxScuWdt_getCpuWatchdogPassword
	.extern	IfxScuCcu_getMaxFrequency
	.extern	__f_ftod
	.extern	__d_div
	.extern	__d_dtof
	.extern	__d_add
	.extern	__d_dtoi
	.extern	__d_fgt
	.extern	__d_mul
	.extern	__d_dtoui
	.extern	__d_dtouc
	.calls	'IfxQspi_calculateDelayConstants','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L142:
	.word	88982
	.half	3
	.word	.L143
	.byte	4
.L141:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144
	.byte	2,1,1,3
	.word	232
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	235
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L279:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	280
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	292
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	404
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	378
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	410
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	410
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	378
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L261:
	.byte	7
	.byte	'int',0,4,5
.L326:
	.byte	7
	.byte	'unsigned char',0,1,8,10
	.byte	'_Ifx_SRC_SRCR_Bits',0,4,45,16,4,11
	.byte	'SRPN',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	519
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	519
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,4,70,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	536
	.byte	4,2,35,0,0,14
	.word	826
	.byte	3
	.word	865
	.byte	4
	.byte	'IfxSrc_clearRequest',0,3,3,250,1,17,1,1,5
	.byte	'src',0,3,250,1,60
	.word	870
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,6,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	918
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	918
	.byte	16,0,2,35,0,0,12,6,247,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	934
	.byte	4,2,35,0,0
.L253:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,6,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,6,255,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1092
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,6,137,9,16,4,11
	.byte	'AE',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,6,135,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1336
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,6,175,15,25,12,13
	.byte	'CON0',0
	.word	1030
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	1296
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1527
	.byte	4,2,35,8,0,14
	.word	1567
	.byte	3
	.word	1630
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,5,181,3,17,1,1,5
	.byte	'watchdog',0,5,181,3,65
	.word	1635
	.byte	5
	.byte	'password',0,5,181,3,82
	.word	1070
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,5,140,4,17,1,1,5
	.byte	'watchdog',0,5,140,4,63
	.word	1635
	.byte	5
	.byte	'password',0,5,140,4,80
	.word	1070
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,5,227,3,19
	.word	1070
	.byte	1,1,5
	.byte	'watchdog',0,5,227,3,74
	.word	1635
	.byte	6,0,15,8,156,1,9,1,16
	.byte	'IfxCpu_ResourceCpu_0',0,0,16
	.byte	'IfxCpu_ResourceCpu_1',0,1,16
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,7,141,6,31
	.word	1865
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,7,139,5,20
	.word	519
	.byte	1,1,6,0
.L287:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,7,161,6,19
	.word	2020
	.byte	1,1,5
	.byte	'address',0,7,161,6,55
	.word	1070
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,7,190,6,20
	.word	519
	.byte	1,1,5
	.byte	'address',0,7,190,6,70
	.word	1070
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,7,172,8,17,1,1,5
	.byte	'address',0,7,172,8,56
	.word	2020
	.byte	5
	.byte	'count',0,7,172,8,72
	.word	2020
	.byte	17,6,0,0,10
	.byte	'_Ifx_P_OUT_Bits',0,10,143,3,16,4,11
	.byte	'P0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,181,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2251
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,10,169,2,16,4,11
	.byte	'PS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,133,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2567
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,10,110,16,4,11
	.byte	'MODREV',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,148,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3138
	.byte	4,2,35,0,0,18,4
	.word	519
	.byte	19,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,10,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	519
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	519
	.byte	5,0,2,35,3,0,12,10,164,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3266
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,10,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	519
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	519
	.byte	5,0,2,35,3,0,12,10,180,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3481
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,10,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	519
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	519
	.byte	5,0,2,35,3,0,12,10,188,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3696
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,10,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	519
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	519
	.byte	5,0,2,35,3,0,12,10,172,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3913
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,10,118,16,4,11
	.byte	'P0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,156,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4133
	.byte	4,2,35,0,0,18,24
	.word	519
	.byte	19,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,10,205,3,16,4,11
	.byte	'PD0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	519
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	519
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	519
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,205,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4456
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,10,226,3,16,4,11
	.byte	'PD8',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	519
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	519
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	519
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,213,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4760
	.byte	4,2,35,0,0,18,8
	.word	519
	.byte	19,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,10,88,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,140,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5085
	.byte	4,2,35,0,0,18,12
	.word	519
	.byte	19,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,10,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,197,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5425
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,10,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	496
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,189,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5791
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,10,206,2,16,4,11
	.byte	'PS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,12,10,149,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6077
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,10,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,12,10,165,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6224
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,10,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	496
	.byte	20,0,2,35,0,0,12,10,173,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6393
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,10,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1070
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,157,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6565
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,10,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	1070
	.byte	12,0,2,35,2,0,12,10,229,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6740
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,10,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	8,0,2,35,3,0,12,10,245,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6914
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,10,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,12,10,253,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7088
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,10,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,237,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7264
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,10,249,2,16,4,11
	.byte	'PS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,141,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7420
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,10,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,221,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7753
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,10,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,12,10,196,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8101
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,10,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,10,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,12,10,204,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8225
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	8309
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,10,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	519
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,10,213,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8489
	.byte	4,2,35,0,0,18,76
	.word	519
	.byte	19,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,10,132,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8742
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,10,252,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8829
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,10,229,5,25,128,2,13
	.byte	'OUT',0
	.word	2527
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	3098
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	3217
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3257
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	3441
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	3656
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3873
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	4093
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	3257
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	4407
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	4447
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	4720
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	5036
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	5076
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	5376
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	5416
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5751
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	6037
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	5076
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	6184
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	6353
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	6525
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	6700
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6874
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	7048
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	7224
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	7380
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	7713
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	8061
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5076
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	8185
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	8434
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	8693
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8733
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8789
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	9356
	.byte	4,3,35,252,1,0,14
	.word	9396
	.byte	3
	.word	9999
	.byte	15,9,83,9,1,16
	.byte	'IfxPort_InputMode_undefined',0,127,16
	.byte	'IfxPort_InputMode_noPullDevice',0,0,16
	.byte	'IfxPort_InputMode_pullDown',0,8,16
	.byte	'IfxPort_InputMode_pullUp',0,16,0,4
	.byte	'IfxPort_setPinModeInput',0,3,9,196,4,17,1,1,5
	.byte	'port',0,9,196,4,48
	.word	10004
	.byte	5
	.byte	'pinIndex',0,9,196,4,60
	.word	519
	.byte	5
	.byte	'mode',0,9,196,4,88
	.word	10009
	.byte	6,0,15,9,134,1,9,1,16
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,16
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,15,9,120,9,1,16
	.byte	'IfxPort_OutputIdx_general',0,128,1,16
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,16
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,16
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,16
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,16
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,16
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,16
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,4
	.byte	'IfxPort_setPinModeOutput',0,3,9,202,4,17,1,1,5
	.byte	'port',0,9,202,4,49
	.word	10004
	.byte	5
	.byte	'pinIndex',0,9,202,4,61
	.word	519
	.byte	5
	.byte	'mode',0,9,202,4,90
	.word	10214
	.byte	5
	.byte	'index',0,9,202,4,114
	.word	10284
	.byte	6,0,15,9,172,1,9,4,16
	.byte	'IfxPort_State_notChanged',0,0,16
	.byte	'IfxPort_State_high',0,1,16
	.byte	'IfxPort_State_low',0,128,128,4,16
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,9,208,4,17,1,1,5
	.byte	'port',0,9,208,4,44
	.word	10004
	.byte	5
	.byte	'pinIndex',0,9,208,4,56
	.word	519
	.byte	5
	.byte	'action',0,9,208,4,80
	.word	10597
	.byte	6,0,10
	.byte	'_Ifx_QSPI_CLC_Bits',0,12,127,16,4,11
	.byte	'DISR',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,12,12,239,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10778
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_PISEL_Bits',0,12,254,1,16,4,11
	.byte	'MRIS',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'SRIS',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'SCIS',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'SLSIS',0,1
	.word	519
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	496
	.byte	17,0,2,35,0,0,12,12,207,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10935
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_ID_Bits',0,12,208,1,16,4,11
	.byte	'MODREV',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,12,159,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11158
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_GLOBALCON_Bits',0,12,189,1,16,4,11
	.byte	'TQ',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'SI',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EXPECT',0,1
	.word	519
	.byte	4,2,2,35,1,11
	.byte	'LB',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'DEL0',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'STROBE',0,1
	.word	519
	.byte	5,3,2,35,2,11
	.byte	'SRF',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'STIP',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'MS',0,1
	.word	519
	.byte	2,5,2,35,3,11
	.byte	'AREN',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'RESETS',0,1
	.word	519
	.byte	4,0,2,35,3,0,12,12,143,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11281
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_GLOBALCON1_Bits',0,12,170,1,16,4,11
	.byte	'ERRORENS',0,2
	.word	1070
	.byte	9,7,2,35,0,11
	.byte	'TXEN',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'RXEN',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PT1EN',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PT2EN',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	2,1,2,35,1,11
	.byte	'USREN',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOINT',0,1
	.word	519
	.byte	2,6,2,35,2,11
	.byte	'RXFIFOINT',0,1
	.word	519
	.byte	2,4,2,35,2,11
	.byte	'PT1',0,1
	.word	519
	.byte	3,1,2,35,2,11
	.byte	'PT2',0,2
	.word	1070
	.byte	3,6,2,35,2,11
	.byte	'TXFM',0,1
	.word	519
	.byte	2,4,2,35,3,11
	.byte	'RXFM',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	2,0,2,35,3,0,12,12,151,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11585
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_BACON_Bits',0,12,88,16,4,11
	.byte	'LAST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'IPRE',0,1
	.word	519
	.byte	3,4,2,35,0,11
	.byte	'IDLE',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'LPRE',0,2
	.word	1070
	.byte	3,6,2,35,0,11
	.byte	'LEAD',0,1
	.word	519
	.byte	3,3,2,35,1,11
	.byte	'TPRE',0,1
	.word	519
	.byte	3,0,2,35,1,11
	.byte	'TRAIL',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'PARTYP',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'UINT',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'MSB',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'BYTE',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'DL',0,2
	.word	1070
	.byte	5,4,2,35,2,11
	.byte	'CS',0,1
	.word	519
	.byte	4,0,2,35,3,0
.L296:
	.byte	12,12,215,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11911
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_ECON_Bits',0,12,143,1,16,4,11
	.byte	'Q',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'A',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'B',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'C',0,1
	.word	519
	.byte	2,4,2,35,1,11
	.byte	'CPH',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'CPOL',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PAREN',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	496
	.byte	15,2,2,35,0,11
	.byte	'BE',0,1
	.word	519
	.byte	2,0,2,35,3,0
.L306:
	.byte	12,12,255,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12184
	.byte	4,2,35,0,0
.L285:
	.byte	18,32
	.word	12348
	.byte	19,7,0,10
	.byte	'_Ifx_QSPI_STATUS_Bits',0,12,169,2,16,4,11
	.byte	'ERRORFLAGS',0,2
	.word	1070
	.byte	9,7,2,35,0,11
	.byte	'TXF',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'RXF',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PT1F',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PT2F',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	2,1,2,35,1,11
	.byte	'USRF',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'TXFIFOLEVEL',0,1
	.word	519
	.byte	3,5,2,35,2,11
	.byte	'RXFIFOLEVEL',0,1
	.word	519
	.byte	3,2,2,35,2,11
	.byte	'SLAVESEL',0,2
	.word	1070
	.byte	4,6,2,35,2,11
	.byte	'RPV',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'TPV',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'PHASE',0,1
	.word	519
	.byte	4,0,2,35,3,0,12,12,239,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12397
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_STATUS1_Bits',0,12,158,2,16,4,11
	.byte	'BITCOUNT',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	20,4,2,35,0,11
	.byte	'BRDEN',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'BRD',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'SPDEN',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'SPD',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,12,247,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12702
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_SSOC_Bits',0,12,151,2,16,4,11
	.byte	'AOL',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'OEN',0,2
	.word	1070
	.byte	16,0,2,35,2,0
.L385:
	.byte	12,12,231,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12878
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_FLAGSCLEAR_Bits',0,12,157,1,16,4,11
	.byte	'ERRORCLEARS',0,2
	.word	1070
	.byte	9,7,2,35,0,11
	.byte	'TXC',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'RXC',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PT1C',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'PT2C',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	2,1,2,35,1,11
	.byte	'USRC',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,12,135,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12975
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_XXLCON_Bits',0,12,187,2,16,4,11
	.byte	'XDL',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'BYTECOUNT',0,2
	.word	1070
	.byte	16,0,2,35,2,0,12,12,255,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13195
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_MIXENTRY_Bits',0,12,238,1,16,4,11
	.byte	'E',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,191,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13300
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_BACONENTRY_Bits',0,12,106,16,4,11
	.byte	'E',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,223,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13384
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_DATAENTRY_Bits',0,12,137,1,16,4,11
	.byte	'E',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,247,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13469
	.byte	4,2,35,0,0,18,32
	.word	13514
	.byte	19,7,0,10
	.byte	'_Ifx_QSPI_RXEXIT_Bits',0,12,139,2,16,4,11
	.byte	'E',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,215,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13563
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_RXEXITD_Bits',0,12,145,2,16,4,11
	.byte	'E',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,223,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13645
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_CAPCON_Bits',0,12,112,16,4,11
	.byte	'CAP',0,2
	.word	1070
	.byte	15,1,2,35,0,11
	.byte	'OVF',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EDGECON',0,1
	.word	519
	.byte	2,6,2,35,2,11
	.byte	'INS',0,1
	.word	519
	.byte	2,4,2,35,2,11
	.byte	'EN',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,2
	.word	1070
	.byte	7,4,2,35,2,11
	.byte	'CAPC',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'CAPS',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'CAPF',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'CAPSEL',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,12,231,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13728
	.byte	4,2,35,0,0,18,68
	.word	519
	.byte	19,67,0,10
	.byte	'_Ifx_QSPI_OCS_Bits',0,12,244,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	2,0,2,35,3,0,12,12,199,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13972
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_KRSTCLR_Bits',0,12,231,1,16,4,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,12,12,183,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14133
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_KRST1_Bits',0,12,224,1,16,4,11
	.byte	'RST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,12,12,175,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14240
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_KRST0_Bits',0,12,216,1,16,4,11
	.byte	'RST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,12,12,167,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14345
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_ACCEN1_Bits',0,12,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,12,12,207,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14469
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI_ACCEN0_Bits',0,12,45,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	519
	.byte	1,0,2,35,3,0,12,12,199,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14559
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_QSPI',0,12,143,4,25,128,2,13
	.byte	'CLC',0
	.word	10895
	.byte	4,2,35,0,13
	.byte	'PISEL',0
	.word	11118
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	11241
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3257
	.byte	4,2,35,12,13
	.byte	'GLOBALCON',0
	.word	11545
	.byte	4,2,35,16,13
	.byte	'GLOBALCON1',0
	.word	11871
	.byte	4,2,35,20,13
	.byte	'BACON',0
	.word	12144
	.byte	4,2,35,24,13
	.byte	'reserved_1C',0
	.word	3257
	.byte	4,2,35,28,13
	.byte	'ECON',0
	.word	12388
	.byte	32,2,35,32,13
	.byte	'STATUS',0
	.word	12662
	.byte	4,2,35,64,13
	.byte	'STATUS1',0
	.word	12838
	.byte	4,2,35,68,13
	.byte	'SSOC',0
	.word	12935
	.byte	4,2,35,72,13
	.byte	'reserved_4C',0
	.word	5076
	.byte	8,2,35,76,13
	.byte	'FLAGSCLEAR',0
	.word	13155
	.byte	4,2,35,84,13
	.byte	'XXLCON',0
	.word	13260
	.byte	4,2,35,88,13
	.byte	'MIXENTRY',0
	.word	13344
	.byte	4,2,35,92,13
	.byte	'BACONENTRY',0
	.word	13429
	.byte	4,2,35,96,13
	.byte	'DATAENTRY',0
	.word	13554
	.byte	32,2,35,100,13
	.byte	'reserved_84',0
	.word	5416
	.byte	12,3,35,132,1,13
	.byte	'RXEXIT',0
	.word	13605
	.byte	4,3,35,144,1,13
	.byte	'RXEXITD',0
	.word	13688
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	5076
	.byte	8,3,35,152,1,13
	.byte	'CAPCON',0
	.word	13923
	.byte	4,3,35,160,1,13
	.byte	'reserved_A4',0
	.word	13963
	.byte	68,3,35,164,1,13
	.byte	'OCS',0
	.word	14093
	.byte	4,3,35,232,1,13
	.byte	'KRSTCLR',0
	.word	14200
	.byte	4,3,35,236,1,13
	.byte	'KRST1',0
	.word	14305
	.byte	4,3,35,240,1,13
	.byte	'KRST0',0
	.word	14429
	.byte	4,3,35,244,1,13
	.byte	'ACCEN1',0
	.word	14519
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	15089
	.byte	4,3,35,252,1,0,14
	.word	15129
.L231:
	.byte	3
	.word	15673
.L332:
	.byte	8
	.byte	'IfxQspi_getModuleFrequency',0,3,11,211,7,18
	.word	292
	.byte	1,1
.L333:
	.byte	5
	.byte	'qspi',0,11,211,7,55
	.word	15678
.L335:
	.byte	6,0,15,11,179,1,9,1,16
	.byte	'IfxQspi_Phase_wait',0,0,16
	.byte	'IfxQspi_Phase_idleA',0,1,16
	.byte	'IfxQspi_Phase_idleB',0,2,16
	.byte	'IfxQspi_Phase_lead',0,3,16
	.byte	'IfxQspi_Phase_data',0,4,16
	.byte	'IfxQspi_Phase_trail',0,5,16
	.byte	'IfxQspi_Phase_expect',0,6,16
	.byte	'IfxQspi_Phase_leadStrobe',0,7,16
	.byte	'IfxQspi_Phase_trailStrobe',0,8,0,8
	.byte	'IfxQspi_getPhase',0,3,11,219,7,26
	.word	15738
	.byte	1,1,5
	.byte	'qspi',0,11,219,7,53
	.word	15678
	.byte	6,0
.L328:
	.byte	8
	.byte	'IfxQspi_getTimeQuantaFrequency',0,3,11,244,7,18
	.word	292
	.byte	1,1
.L329:
	.byte	5
	.byte	'qspi',0,11,244,7,59
	.word	15678
.L331:
	.byte	17,6,0,0,20
	.byte	'__max',0
	.word	512
	.byte	1,1,1,1,21
	.word	512
	.byte	21
	.word	512
	.byte	0,22
	.word	240
	.byte	23
	.word	266
	.byte	6,0,22
	.word	301
	.byte	23
	.word	333
	.byte	6,0,22
	.word	346
	.byte	6,0,22
	.word	415
	.byte	23
	.word	434
	.byte	6,0,22
	.word	450
	.byte	23
	.word	465
	.byte	23
	.word	479
	.byte	6,0,22
	.word	875
	.byte	23
	.word	903
	.byte	6,0,22
	.word	1640
	.byte	23
	.word	1680
	.byte	23
	.word	1698
	.byte	6,0,22
	.word	1718
	.byte	23
	.word	1756
	.byte	23
	.word	1774
	.byte	6,0,24
	.byte	'IfxScuWdt_clearCpuEndinit',0,5,217,1,17,1,1,1,1,5
	.byte	'password',0,5,217,1,50
	.word	1070
	.byte	0,24
	.byte	'IfxScuWdt_setCpuEndinit',0,5,239,1,17,1,1,1,1,5
	.byte	'password',0,5,239,1,48
	.word	1070
	.byte	0,22
	.word	1794
	.byte	23
	.word	1845
	.byte	6,0,25
	.byte	'IfxScuWdt_getCpuWatchdogPassword',0,5,129,3,19
	.word	1070
	.byte	1,1,1,1,22
	.word	1944
	.byte	6,0,22
	.word	1978
	.byte	6,0,22
	.word	2041
	.byte	23
	.word	2082
	.byte	6,0,22
	.word	2101
	.byte	23
	.word	2156
	.byte	6,0,22
	.word	2175
	.byte	23
	.word	2215
	.byte	23
	.word	2232
	.byte	17,6,0,0,22
	.word	10134
	.byte	23
	.word	10166
	.byte	23
	.word	10180
	.byte	23
	.word	10198
	.byte	6,0,22
	.word	10501
	.byte	23
	.word	10534
	.byte	23
	.word	10548
	.byte	23
	.word	10566
	.byte	23
	.word	10580
	.byte	6,0,22
	.word	10700
	.byte	23
	.word	10728
	.byte	23
	.word	10742
	.byte	23
	.word	10760
	.byte	6,0,25
	.byte	'IfxScuCcu_getMaxFrequency',0,13,253,6,20
	.word	292
	.byte	1,1,1,1
.L233:
	.byte	3
	.word	1070
.L235:
	.byte	7
	.byte	'short int',0,2,5,14
	.word	13605
.L237:
	.byte	3
	.word	16539
.L241:
	.byte	3
	.word	2020
.L247:
	.byte	3
	.word	519
.L257:
	.byte	15,11,95,9,1,16
	.byte	'IfxQspi_ChannelId_0',0,0,16
	.byte	'IfxQspi_ChannelId_1',0,1,16
	.byte	'IfxQspi_ChannelId_2',0,2,16
	.byte	'IfxQspi_ChannelId_3',0,3,16
	.byte	'IfxQspi_ChannelId_4',0,4,16
	.byte	'IfxQspi_ChannelId_5',0,5,16
	.byte	'IfxQspi_ChannelId_6',0,6,16
	.byte	'IfxQspi_ChannelId_7',0,7,16
	.byte	'IfxQspi_ChannelId_8',0,8,16
	.byte	'IfxQspi_ChannelId_9',0,9,16
	.byte	'IfxQspi_ChannelId_10',0,10,16
	.byte	'IfxQspi_ChannelId_11',0,11,16
	.byte	'IfxQspi_ChannelId_12',0,12,16
	.byte	'IfxQspi_ChannelId_13',0,13,16
	.byte	'IfxQspi_ChannelId_14',0,14,0,14
	.word	13514
.L263:
	.byte	3
	.word	16900
	.byte	22
	.word	15683
	.byte	23
	.word	15722
	.byte	6,0,22
	.word	15952
	.byte	23
	.word	15981
	.byte	6,0,22
	.word	15997
	.byte	23
	.word	16040
	.byte	17,26
	.word	15683
	.byte	23
	.word	15722
	.byte	27
	.word	15736
	.byte	0,6,0,0
.L290:
	.byte	28
	.word	16559
	.byte	29,14,189,1,9,16,11
	.byte	'enabled',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'autoCS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'loopback',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'clockPolarity',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'shiftClock',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'dataHeading',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'dataWidth',0,2
	.word	1070
	.byte	6,4,2,35,0,11
	.byte	'csActiveLevel',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'parityCheck',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'parityMode',0,1
	.word	519
	.byte	1,1,2,35,1,13
	.byte	'csInactiveDelay',0
	.word	2020
	.byte	4,2,35,2,13
	.byte	'csLeadDelay',0
	.word	2020
	.byte	4,2,35,6,13
	.byte	'csTrailDelay',0
	.word	2020
	.byte	4,2,35,10,0,28
	.word	16969
.L292:
	.byte	3
	.word	17262
.L294:
	.byte	28
	.word	292
	.byte	29,11,181,2,9,2,13
	.byte	'pre',0
	.word	519
	.byte	1,2,35,0,13
	.byte	'delay',0
	.word	519
	.byte	1,2,35,1,0
.L298:
	.byte	18,6
	.word	17277
	.byte	19,2,0
.L302:
	.byte	28
	.word	519
	.byte	30
	.byte	'pvoid',0,15,57,28
	.word	410
	.byte	29,14,118,18,1,11
	.byte	'onTransfer',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'byteAccess',0,1
	.word	519
	.byte	1,6,2,35,0,0,14
	.word	17340
	.byte	29,14,179,1,9,4,11
	.byte	'baudrate',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'phase',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'receive',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'transmit',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved',0,4
	.word	2020
	.byte	28,0,2,35,0,0,7
	.byte	'long int',0,4,5,29,14,124,9,8,13
	.byte	'data',0
	.word	410
	.byte	4,2,35,0,13
	.byte	'remaining',0
	.word	16526
	.byte	2,2,35,4,0,31,1,1,21
	.word	410
	.byte	0,3
	.word	17549
	.byte	30
	.byte	'SpiIf_Cbk',0,14,211,1,16
	.word	17558
	.byte	3
	.word	.L536-.L142
	.byte	31,1,1,21
	.word	17582
	.byte	0,3
	.word	17587
	.byte	30
	.byte	'TxRxHandler',0,14,212,1,16
	.word	17596
.L536:
	.byte	10
	.byte	'SpiIf_Ch_',0,14,214,1,8,48,13
	.byte	'driver',0
	.word	.L537-.L142
	.byte	4,2,35,0,13
	.byte	'flags',0
	.word	17390
	.byte	1,2,35,4,13
	.byte	'errorChecks',0
	.word	17395
	.byte	4,2,35,6,13
	.byte	'baudrate',0
	.word	17498
	.byte	4,2,35,10,13
	.byte	'tx',0
	.word	17510
	.byte	8,2,35,16,13
	.byte	'rx',0
	.word	17510
	.byte	8,2,35,24,13
	.byte	'onExchangeEnd',0
	.word	17563
	.byte	4,2,35,32,13
	.byte	'callbackData',0
	.word	410
	.byte	4,2,35,36,13
	.byte	'txHandler',0
	.word	17601
	.byte	4,2,35,40,13
	.byte	'rxHandler',0
	.word	17601
	.byte	4,2,35,44,0,3
	.word	17622
	.byte	15,14,69,9,1,16
	.byte	'SpiIf_Status_ok',0,0,16
	.byte	'SpiIf_Status_busy',0,1,16
	.byte	'SpiIf_Status_unknown',0,2,0,28
	.word	404
	.byte	3
	.word	17888
	.byte	32
	.word	17821
	.byte	1,1,21
	.word	17582
	.byte	21
	.word	17893
	.byte	21
	.word	410
	.byte	21
	.word	16526
	.byte	0,3
	.word	17898
	.byte	30
	.byte	'SpiIf_Exchange',0,14,138,1,24
	.word	17926
	.byte	32
	.word	17821
	.byte	1,1,21
	.word	17582
	.byte	0,3
	.word	17955
	.byte	30
	.byte	'SpiIf_GetStatus',0,14,139,1,24
	.word	17968
	.byte	3
	.word	.L538-.L142
	.byte	31,1,1,21
	.word	17998
	.byte	0,3
	.word	18003
	.byte	30
	.byte	'SpiIf_OnEvent',0,14,140,1,24
	.word	18012
	.byte	29,14,144,1,9,20,13
	.byte	'exchange',0
	.word	17931
	.byte	4,2,35,0,13
	.byte	'getStatus',0
	.word	17973
	.byte	4,2,35,4,13
	.byte	'onTx',0
	.word	18017
	.byte	4,2,35,8,13
	.byte	'onRx',0
	.word	18017
	.byte	4,2,35,12,13
	.byte	'onError',0
	.word	18017
	.byte	4,2,35,16,0
.L538:
	.byte	10
	.byte	'SpiIf_',0,14,153,1,8,40,13
	.byte	'driver',0
	.word	17326
	.byte	4,2,35,0,13
	.byte	'sending',0
	.word	2020
	.byte	4,2,35,4,13
	.byte	'activeChannel',0
	.word	17816
	.byte	4,2,35,8,13
	.byte	'txCount',0
	.word	2020
	.byte	4,2,35,12,13
	.byte	'rxCount',0
	.word	2020
	.byte	4,2,35,16,13
	.byte	'functions',0
	.word	18040
	.byte	20,2,35,20,0
.L537:
	.byte	3
	.word	18129
	.byte	10
	.byte	'SpiIf_ChConfig_',0,14,228,1,8,28,13
	.byte	'driver',0
	.word	18252
	.byte	4,2,35,0,13
	.byte	'baudrate',0
	.word	292
	.byte	4,2,35,4,13
	.byte	'mode',0
	.word	16969
	.byte	16,2,35,8,13
	.byte	'errorChecks',0
	.word	17395
	.byte	4,2,35,24,0,28
	.word	18257
.L304:
	.byte	3
	.word	18349
	.byte	3
	.word	404
	.byte	3
	.word	17549
	.byte	3
	.word	17587
	.byte	3
	.word	17898
	.byte	3
	.word	17955
	.byte	3
	.word	18003
.L309:
	.byte	28
	.word	512
.L320:
	.byte	7
	.byte	'char',0,1,6
.L366:
	.byte	15,16,80,9,1,16
	.byte	'IfxQspi_Index_none',0,127,16
	.byte	'IfxQspi_Index_0',0,0,16
	.byte	'IfxQspi_Index_1',0,1,16
	.byte	'IfxQspi_Index_2',0,2,16
	.byte	'IfxQspi_Index_3',0,3,0,14
	.word	15129
	.byte	28
	.word	18501
.L388:
	.byte	3
	.word	18506
.L390:
	.byte	28
	.word	16559
.L393:
	.byte	3
	.word	17277
	.byte	28
	.word	2020
.L396:
	.byte	3
	.word	18526
	.byte	30
	.byte	'__wchar_t',0,17,1,1
	.word	16526
	.byte	30
	.byte	'__size_t',0,17,1,1
	.word	496
	.byte	30
	.byte	'__ptrdiff_t',0,17,1,1
	.word	512
	.byte	33,1,3
	.word	18591
	.byte	30
	.byte	'__codeptr',0,17,1,1
	.word	18593
	.byte	30
	.byte	'boolean',0,18,101,29
	.word	519
	.byte	30
	.byte	'uint8',0,18,105,29
	.word	519
	.byte	30
	.byte	'uint16',0,18,109,29
	.word	1070
	.byte	30
	.byte	'uint32',0,18,113,29
	.word	2020
	.byte	30
	.byte	'uint64',0,18,118,29
	.word	378
	.byte	30
	.byte	'sint16',0,18,126,29
	.word	16526
	.byte	30
	.byte	'sint32',0,18,131,1,29
	.word	17498
	.byte	7
	.byte	'long long int',0,8,5,30
	.byte	'sint64',0,18,138,1,29
	.word	18722
	.byte	30
	.byte	'float32',0,18,167,1,29
	.word	292
	.byte	30
	.byte	'Ifx_TickTime',0,15,79,28
	.word	18722
	.byte	30
	.byte	'Ifx_SizeT',0,15,92,16
	.word	16526
	.byte	30
	.byte	'Ifx_Priority',0,15,103,16
	.word	1070
	.byte	15,15,130,1,9,1,16
	.byte	'Ifx_RxSel_a',0,0,16
	.byte	'Ifx_RxSel_b',0,1,16
	.byte	'Ifx_RxSel_c',0,2,16
	.byte	'Ifx_RxSel_d',0,3,16
	.byte	'Ifx_RxSel_e',0,4,16
	.byte	'Ifx_RxSel_f',0,5,16
	.byte	'Ifx_RxSel_g',0,6,16
	.byte	'Ifx_RxSel_h',0,7,0,30
	.byte	'Ifx_RxSel',0,15,140,1,3
	.word	18832
	.byte	14
	.word	404
	.byte	3
	.word	18970
	.byte	29,15,143,1,9,8,13
	.byte	'module',0
	.word	18975
	.byte	4,2,35,0,13
	.byte	'index',0
	.word	17498
	.byte	4,2,35,4,0,30
	.byte	'IfxModule_IndexMap',0,15,147,1,3
	.word	18980
	.byte	30
	.byte	'Ifx_QSPI_ACCEN0_Bits',0,12,79,3
	.word	14559
	.byte	30
	.byte	'Ifx_QSPI_ACCEN1_Bits',0,12,85,3
	.word	14469
	.byte	30
	.byte	'Ifx_QSPI_BACON_Bits',0,12,103,3
	.word	11911
	.byte	30
	.byte	'Ifx_QSPI_BACONENTRY_Bits',0,12,109,3
	.word	13384
	.byte	30
	.byte	'Ifx_QSPI_CAPCON_Bits',0,12,124,3
	.word	13728
	.byte	30
	.byte	'Ifx_QSPI_CLC_Bits',0,12,134,1,3
	.word	10778
	.byte	30
	.byte	'Ifx_QSPI_DATAENTRY_Bits',0,12,140,1,3
	.word	13469
	.byte	30
	.byte	'Ifx_QSPI_ECON_Bits',0,12,154,1,3
	.word	12184
	.byte	30
	.byte	'Ifx_QSPI_FLAGSCLEAR_Bits',0,12,167,1,3
	.word	12975
	.byte	30
	.byte	'Ifx_QSPI_GLOBALCON1_Bits',0,12,186,1,3
	.word	11585
	.byte	30
	.byte	'Ifx_QSPI_GLOBALCON_Bits',0,12,205,1,3
	.word	11281
	.byte	30
	.byte	'Ifx_QSPI_ID_Bits',0,12,213,1,3
	.word	11158
	.byte	30
	.byte	'Ifx_QSPI_KRST0_Bits',0,12,221,1,3
	.word	14345
	.byte	30
	.byte	'Ifx_QSPI_KRST1_Bits',0,12,228,1,3
	.word	14240
	.byte	30
	.byte	'Ifx_QSPI_KRSTCLR_Bits',0,12,235,1,3
	.word	14133
	.byte	30
	.byte	'Ifx_QSPI_MIXENTRY_Bits',0,12,241,1,3
	.word	13300
	.byte	30
	.byte	'Ifx_QSPI_OCS_Bits',0,12,251,1,3
	.word	13972
	.byte	30
	.byte	'Ifx_QSPI_PISEL_Bits',0,12,136,2,3
	.word	10935
	.byte	30
	.byte	'Ifx_QSPI_RXEXIT_Bits',0,12,142,2,3
	.word	13563
	.byte	30
	.byte	'Ifx_QSPI_RXEXITD_Bits',0,12,148,2,3
	.word	13645
	.byte	30
	.byte	'Ifx_QSPI_SSOC_Bits',0,12,155,2,3
	.word	12878
	.byte	30
	.byte	'Ifx_QSPI_STATUS1_Bits',0,12,166,2,3
	.word	12702
	.byte	30
	.byte	'Ifx_QSPI_STATUS_Bits',0,12,184,2,3
	.word	12397
	.byte	30
	.byte	'Ifx_QSPI_XXLCON_Bits',0,12,191,2,3
	.word	13195
	.byte	30
	.byte	'Ifx_QSPI_ACCEN0',0,12,204,2,3
	.word	15089
	.byte	30
	.byte	'Ifx_QSPI_ACCEN1',0,12,212,2,3
	.word	14519
	.byte	30
	.byte	'Ifx_QSPI_BACON',0,12,220,2,3
	.word	12144
	.byte	30
	.byte	'Ifx_QSPI_BACONENTRY',0,12,228,2,3
	.word	13429
	.byte	30
	.byte	'Ifx_QSPI_CAPCON',0,12,236,2,3
	.word	13923
	.byte	30
	.byte	'Ifx_QSPI_CLC',0,12,244,2,3
	.word	10895
	.byte	30
	.byte	'Ifx_QSPI_DATAENTRY',0,12,252,2,3
	.word	13514
	.byte	30
	.byte	'Ifx_QSPI_ECON',0,12,132,3,3
	.word	12348
	.byte	30
	.byte	'Ifx_QSPI_FLAGSCLEAR',0,12,140,3,3
	.word	13155
	.byte	30
	.byte	'Ifx_QSPI_GLOBALCON',0,12,148,3,3
	.word	11545
	.byte	30
	.byte	'Ifx_QSPI_GLOBALCON1',0,12,156,3,3
	.word	11871
	.byte	30
	.byte	'Ifx_QSPI_ID',0,12,164,3,3
	.word	11241
	.byte	30
	.byte	'Ifx_QSPI_KRST0',0,12,172,3,3
	.word	14429
	.byte	30
	.byte	'Ifx_QSPI_KRST1',0,12,180,3,3
	.word	14305
	.byte	30
	.byte	'Ifx_QSPI_KRSTCLR',0,12,188,3,3
	.word	14200
	.byte	30
	.byte	'Ifx_QSPI_MIXENTRY',0,12,196,3,3
	.word	13344
	.byte	30
	.byte	'Ifx_QSPI_OCS',0,12,204,3,3
	.word	14093
	.byte	30
	.byte	'Ifx_QSPI_PISEL',0,12,212,3,3
	.word	11118
	.byte	30
	.byte	'Ifx_QSPI_RXEXIT',0,12,220,3,3
	.word	13605
	.byte	30
	.byte	'Ifx_QSPI_RXEXITD',0,12,228,3,3
	.word	13688
	.byte	30
	.byte	'Ifx_QSPI_SSOC',0,12,236,3,3
	.word	12935
	.byte	30
	.byte	'Ifx_QSPI_STATUS',0,12,244,3,3
	.word	12662
	.byte	30
	.byte	'Ifx_QSPI_STATUS1',0,12,252,3,3
	.word	12838
	.byte	30
	.byte	'Ifx_QSPI_XXLCON',0,12,132,4,3
	.word	13260
	.byte	14
	.word	15129
	.byte	30
	.byte	'Ifx_QSPI',0,12,175,4,3
	.word	20371
	.byte	30
	.byte	'IfxQspi_Index',0,16,87,3
	.word	18402
	.byte	18,32
	.word	18980
	.byte	19,3,0,28
	.word	20416
	.byte	34
	.byte	'IfxQspi_cfg_indexMap',0,16,93,41
	.word	20425
	.byte	1,1,15,19,69,9,1,16
	.byte	'IfxSrc_Tos_cpu0',0,0,16
	.byte	'IfxSrc_Tos_cpu1',0,1,16
	.byte	'IfxSrc_Tos_dma',0,3,0,30
	.byte	'IfxSrc_Tos',0,19,74,3
	.word	20461
	.byte	30
	.byte	'Ifx_SRC_SRCR_Bits',0,4,62,3
	.word	536
	.byte	30
	.byte	'Ifx_SRC_SRCR',0,4,75,3
	.word	826
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,4,86,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	20586
	.byte	30
	.byte	'Ifx_SRC_AGBT',0,4,89,3
	.word	20618
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,4,92,25,12,13
	.byte	'TX',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,8,0,14
	.word	20644
	.byte	30
	.byte	'Ifx_SRC_ASCLIN',0,4,97,3
	.word	20703
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,4,100,25,4,13
	.byte	'SBSRC',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	20731
	.byte	30
	.byte	'Ifx_SRC_BCUSPB',0,4,103,3
	.word	20768
	.byte	18,64
	.word	826
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_CAN',0,4,106,25,64,13
	.byte	'INT',0
	.word	20796
	.byte	64,2,35,0,0,14
	.word	20805
	.byte	30
	.byte	'Ifx_SRC_CAN',0,4,109,3
	.word	20837
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,4,112,25,16,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	826
	.byte	4,2,35,12,0,14
	.word	20862
	.byte	30
	.byte	'Ifx_SRC_CCU6',0,4,118,3
	.word	20934
	.byte	18,8
	.word	826
	.byte	19,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,4,121,25,8,13
	.byte	'SR',0
	.word	20960
	.byte	8,2,35,0,0,14
	.word	20969
	.byte	30
	.byte	'Ifx_SRC_CERBERUS',0,4,124,3
	.word	21005
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,4,127,25,16,13
	.byte	'MI',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	826
	.byte	4,2,35,12,0,14
	.word	21035
	.byte	30
	.byte	'Ifx_SRC_CIF',0,4,133,1,3
	.word	21108
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,4,136,1,25,4,13
	.byte	'SBSRC',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	21134
	.byte	30
	.byte	'Ifx_SRC_CPU',0,4,139,1,3
	.word	21169
	.byte	18,192,1
	.word	826
	.byte	19,47,0,10
	.byte	'_Ifx_SRC_DMA',0,4,142,1,25,208,1,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	5416
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	21195
	.byte	192,1,2,35,16,0,14
	.word	21205
	.byte	30
	.byte	'Ifx_SRC_DMA',0,4,147,1,3
	.word	21272
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,4,150,1,25,8,13
	.byte	'SRM',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	826
	.byte	4,2,35,4,0,14
	.word	21298
	.byte	30
	.byte	'Ifx_SRC_DSADC',0,4,154,1,3
	.word	21346
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,4,157,1,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	21374
	.byte	30
	.byte	'Ifx_SRC_EMEM',0,4,160,1,3
	.word	21407
	.byte	18,40
	.word	519
	.byte	19,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,4,163,1,25,80,13
	.byte	'INT',0
	.word	20960
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	20960
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	20960
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	20960
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	826
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	826
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	21434
	.byte	40,2,35,40,0,14
	.word	21443
	.byte	30
	.byte	'Ifx_SRC_ERAY',0,4,172,1,3
	.word	21570
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,4,175,1,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	21597
	.byte	30
	.byte	'Ifx_SRC_ETH',0,4,178,1,3
	.word	21629
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,4,181,1,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	21655
	.byte	30
	.byte	'Ifx_SRC_FCE',0,4,184,1,3
	.word	21687
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,4,187,1,25,12,13
	.byte	'DONE',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	826
	.byte	4,2,35,8,0,14
	.word	21713
	.byte	30
	.byte	'Ifx_SRC_FFT',0,4,192,1,3
	.word	21773
	.byte	18,16
	.word	519
	.byte	19,15,0,10
	.byte	'_Ifx_SRC_GPSR',0,4,195,1,25,32,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	826
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	21799
	.byte	16,2,35,16,0,14
	.word	21808
	.byte	30
	.byte	'Ifx_SRC_GPSR',0,4,202,1,3
	.word	21902
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,4,205,1,25,48,13
	.byte	'CIRQ',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	826
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	826
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	826
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	4447
	.byte	24,2,35,24,0,14
	.word	21929
	.byte	30
	.byte	'Ifx_SRC_GPT12',0,4,214,1,3
	.word	22046
	.byte	18,12
	.word	826
	.byte	19,2,0,18,32
	.word	826
	.byte	19,7,0,18,32
	.word	22083
	.byte	19,0,0,18,88
	.word	519
	.byte	19,87,0,18,108
	.word	826
	.byte	19,26,0,18,96
	.word	519
	.byte	19,95,0,18,96
	.word	22083
	.byte	19,2,0,18,160,3
	.word	519
	.byte	19,159,3,0,18,64
	.word	22083
	.byte	19,1,0,18,192,3
	.word	519
	.byte	19,191,3,0,18,16
	.word	826
	.byte	19,3,0,18,64
	.word	22168
	.byte	19,3,0,18,192,2
	.word	519
	.byte	19,191,2,0,18,52
	.word	519
	.byte	19,51,0,10
	.byte	'_Ifx_SRC_GTM',0,4,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	22074
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	3257
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	826
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	826
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	20960
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	5076
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	22092
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	22101
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	22110
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	22119
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	826
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	5416
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	22128
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	22137
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	22128
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	22137
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	22148
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	22157
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	22177
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	22186
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	22074
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	22197
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	22074
	.byte	12,3,35,192,18,0,14
	.word	22206
	.byte	30
	.byte	'Ifx_SRC_GTM',0,4,243,1,3
	.word	22666
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,4,246,1,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	22692
	.byte	30
	.byte	'Ifx_SRC_HSCT',0,4,249,1,3
	.word	22725
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,4,252,1,25,16,13
	.byte	'COK',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	826
	.byte	4,2,35,12,0,14
	.word	22752
	.byte	30
	.byte	'Ifx_SRC_HSSL',0,4,130,2,3
	.word	22825
	.byte	18,56
	.word	519
	.byte	19,55,0,10
	.byte	'_Ifx_SRC_I2C',0,4,133,2,25,80,13
	.byte	'BREQ',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	826
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	826
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	22852
	.byte	56,2,35,24,0,14
	.word	22861
	.byte	30
	.byte	'Ifx_SRC_I2C',0,4,142,2,3
	.word	22984
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,4,145,2,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	23010
	.byte	30
	.byte	'Ifx_SRC_LMU',0,4,148,2,3
	.word	23042
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,4,151,2,25,20,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	826
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	826
	.byte	4,2,35,16,0,14
	.word	23068
	.byte	30
	.byte	'Ifx_SRC_MSC',0,4,158,2,3
	.word	23153
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,4,161,2,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	23179
	.byte	30
	.byte	'Ifx_SRC_PMU',0,4,164,2,3
	.word	23211
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,4,167,2,25,32,13
	.byte	'SR',0
	.word	22083
	.byte	32,2,35,0,0,14
	.word	23237
	.byte	30
	.byte	'Ifx_SRC_PSI5',0,4,170,2,3
	.word	23270
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,4,173,2,25,32,13
	.byte	'SR',0
	.word	22083
	.byte	32,2,35,0,0,14
	.word	23297
	.byte	30
	.byte	'Ifx_SRC_PSI5S',0,4,176,2,3
	.word	23331
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,4,179,2,25,24,13
	.byte	'TX',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	826
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	826
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	826
	.byte	4,2,35,20,0,14
	.word	23359
	.byte	30
	.byte	'Ifx_SRC_QSPI',0,4,187,2,3
	.word	23452
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,4,190,2,25,4,13
	.byte	'SR',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	23479
	.byte	30
	.byte	'Ifx_SRC_SCR',0,4,193,2,3
	.word	23511
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,4,196,2,25,20,13
	.byte	'DTS',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	22168
	.byte	16,2,35,4,0,14
	.word	23537
	.byte	30
	.byte	'Ifx_SRC_SCU',0,4,200,2,3
	.word	23583
	.byte	18,24
	.word	826
	.byte	19,5,0,10
	.byte	'_Ifx_SRC_SENT',0,4,203,2,25,24,13
	.byte	'SR',0
	.word	23609
	.byte	24,2,35,0,0,14
	.word	23618
	.byte	30
	.byte	'Ifx_SRC_SENT',0,4,206,2,3
	.word	23651
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,4,209,2,25,12,13
	.byte	'SR',0
	.word	22074
	.byte	12,2,35,0,0,14
	.word	23678
	.byte	30
	.byte	'Ifx_SRC_SMU',0,4,212,2,3
	.word	23710
	.byte	10
	.byte	'_Ifx_SRC_STM',0,4,215,2,25,8,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,0,14
	.word	23736
	.byte	30
	.byte	'Ifx_SRC_STM',0,4,219,2,3
	.word	23782
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,4,222,2,25,16,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	826
	.byte	4,2,35,12,0,14
	.word	23808
	.byte	30
	.byte	'Ifx_SRC_VADCCG',0,4,228,2,3
	.word	23883
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,4,231,2,25,16,13
	.byte	'SR0',0
	.word	826
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	826
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	826
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	826
	.byte	4,2,35,12,0,14
	.word	23912
	.byte	30
	.byte	'Ifx_SRC_VADCG',0,4,237,2,3
	.word	23986
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,4,240,2,25,4,13
	.byte	'SRC',0
	.word	826
	.byte	4,2,35,0,0,14
	.word	24014
	.byte	30
	.byte	'Ifx_SRC_XBAR',0,4,243,2,3
	.word	24048
	.byte	18,4
	.word	20586
	.byte	19,0,0,14
	.word	24075
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,4,128,3,25,4,13
	.byte	'AGBT',0
	.word	24084
	.byte	4,2,35,0,0,14
	.word	24089
	.byte	30
	.byte	'Ifx_SRC_GAGBT',0,4,131,3,3
	.word	24125
	.byte	18,48
	.word	20644
	.byte	19,3,0,14
	.word	24153
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,4,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	24162
	.byte	48,2,35,0,0,14
	.word	24167
	.byte	30
	.byte	'Ifx_SRC_GASCLIN',0,4,137,3,3
	.word	24207
	.byte	14
	.word	20731
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,4,140,3,25,4,13
	.byte	'SPB',0
	.word	24237
	.byte	4,2,35,0,0,14
	.word	24242
	.byte	30
	.byte	'Ifx_SRC_GBCU',0,4,143,3,3
	.word	24276
	.byte	18,64
	.word	20805
	.byte	19,0,0,14
	.word	24303
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,4,146,3,25,64,13
	.byte	'CAN',0
	.word	24312
	.byte	64,2,35,0,0,14
	.word	24317
	.byte	30
	.byte	'Ifx_SRC_GCAN',0,4,149,3,3
	.word	24351
	.byte	18,32
	.word	20862
	.byte	19,1,0,14
	.word	24378
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,4,152,3,25,32,13
	.byte	'CCU6',0
	.word	24387
	.byte	32,2,35,0,0,14
	.word	24392
	.byte	30
	.byte	'Ifx_SRC_GCCU6',0,4,155,3,3
	.word	24428
	.byte	14
	.word	20969
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,4,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	24456
	.byte	8,2,35,0,0,14
	.word	24461
	.byte	30
	.byte	'Ifx_SRC_GCERBERUS',0,4,161,3,3
	.word	24505
	.byte	18,16
	.word	21035
	.byte	19,0,0,14
	.word	24537
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,4,164,3,25,16,13
	.byte	'CIF',0
	.word	24546
	.byte	16,2,35,0,0,14
	.word	24551
	.byte	30
	.byte	'Ifx_SRC_GCIF',0,4,167,3,3
	.word	24585
	.byte	18,8
	.word	21134
	.byte	19,1,0,14
	.word	24612
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,4,170,3,25,8,13
	.byte	'CPU',0
	.word	24621
	.byte	8,2,35,0,0,14
	.word	24626
	.byte	30
	.byte	'Ifx_SRC_GCPU',0,4,173,3,3
	.word	24660
	.byte	18,208,1
	.word	21205
	.byte	19,0,0,14
	.word	24687
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,4,176,3,25,208,1,13
	.byte	'DMA',0
	.word	24697
	.byte	208,1,2,35,0,0,14
	.word	24702
	.byte	30
	.byte	'Ifx_SRC_GDMA',0,4,179,3,3
	.word	24738
	.byte	14
	.word	21298
	.byte	14
	.word	21298
	.byte	14
	.word	21298
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,4,182,3,25,32,13
	.byte	'DSADC0',0
	.word	24765
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	5076
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	24770
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	24775
	.byte	8,2,35,24,0,14
	.word	24780
	.byte	30
	.byte	'Ifx_SRC_GDSADC',0,4,188,3,3
	.word	24871
	.byte	18,4
	.word	21374
	.byte	19,0,0,14
	.word	24900
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,4,191,3,25,4,13
	.byte	'EMEM',0
	.word	24909
	.byte	4,2,35,0,0,14
	.word	24914
	.byte	30
	.byte	'Ifx_SRC_GEMEM',0,4,194,3,3
	.word	24950
	.byte	18,80
	.word	21443
	.byte	19,0,0,14
	.word	24978
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,4,197,3,25,80,13
	.byte	'ERAY',0
	.word	24987
	.byte	80,2,35,0,0,14
	.word	24992
	.byte	30
	.byte	'Ifx_SRC_GERAY',0,4,200,3,3
	.word	25028
	.byte	18,4
	.word	21597
	.byte	19,0,0,14
	.word	25056
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,4,203,3,25,4,13
	.byte	'ETH',0
	.word	25065
	.byte	4,2,35,0,0,14
	.word	25070
	.byte	30
	.byte	'Ifx_SRC_GETH',0,4,206,3,3
	.word	25104
	.byte	18,4
	.word	21655
	.byte	19,0,0,14
	.word	25131
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,4,209,3,25,4,13
	.byte	'FCE',0
	.word	25140
	.byte	4,2,35,0,0,14
	.word	25145
	.byte	30
	.byte	'Ifx_SRC_GFCE',0,4,212,3,3
	.word	25179
	.byte	18,12
	.word	21713
	.byte	19,0,0,14
	.word	25206
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,4,215,3,25,12,13
	.byte	'FFT',0
	.word	25215
	.byte	12,2,35,0,0,14
	.word	25220
	.byte	30
	.byte	'Ifx_SRC_GFFT',0,4,218,3,3
	.word	25254
	.byte	18,64
	.word	21808
	.byte	19,1,0,14
	.word	25281
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,4,221,3,25,64,13
	.byte	'GPSR',0
	.word	25290
	.byte	64,2,35,0,0,14
	.word	25295
	.byte	30
	.byte	'Ifx_SRC_GGPSR',0,4,224,3,3
	.word	25331
	.byte	18,48
	.word	21929
	.byte	19,0,0,14
	.word	25359
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,4,227,3,25,48,13
	.byte	'GPT12',0
	.word	25368
	.byte	48,2,35,0,0,14
	.word	25373
	.byte	30
	.byte	'Ifx_SRC_GGPT12',0,4,230,3,3
	.word	25411
	.byte	18,204,18
	.word	22206
	.byte	19,0,0,14
	.word	25440
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,4,233,3,25,204,18,13
	.byte	'GTM',0
	.word	25450
	.byte	204,18,2,35,0,0,14
	.word	25455
	.byte	30
	.byte	'Ifx_SRC_GGTM',0,4,236,3,3
	.word	25491
	.byte	18,4
	.word	22692
	.byte	19,0,0,14
	.word	25518
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,4,239,3,25,4,13
	.byte	'HSCT',0
	.word	25527
	.byte	4,2,35,0,0,14
	.word	25532
	.byte	30
	.byte	'Ifx_SRC_GHSCT',0,4,242,3,3
	.word	25568
	.byte	18,64
	.word	22752
	.byte	19,3,0,14
	.word	25596
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,4,245,3,25,68,13
	.byte	'HSSL',0
	.word	25605
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	826
	.byte	4,2,35,64,0,14
	.word	25610
	.byte	30
	.byte	'Ifx_SRC_GHSSL',0,4,249,3,3
	.word	25659
	.byte	18,80
	.word	22861
	.byte	19,0,0,14
	.word	25687
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,4,252,3,25,80,13
	.byte	'I2C',0
	.word	25696
	.byte	80,2,35,0,0,14
	.word	25701
	.byte	30
	.byte	'Ifx_SRC_GI2C',0,4,255,3,3
	.word	25735
	.byte	18,4
	.word	23010
	.byte	19,0,0,14
	.word	25762
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,4,130,4,25,4,13
	.byte	'LMU',0
	.word	25771
	.byte	4,2,35,0,0,14
	.word	25776
	.byte	30
	.byte	'Ifx_SRC_GLMU',0,4,133,4,3
	.word	25810
	.byte	18,40
	.word	23068
	.byte	19,1,0,14
	.word	25837
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,4,136,4,25,40,13
	.byte	'MSC',0
	.word	25846
	.byte	40,2,35,0,0,14
	.word	25851
	.byte	30
	.byte	'Ifx_SRC_GMSC',0,4,139,4,3
	.word	25885
	.byte	18,8
	.word	23179
	.byte	19,1,0,14
	.word	25912
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,4,142,4,25,8,13
	.byte	'PMU',0
	.word	25921
	.byte	8,2,35,0,0,14
	.word	25926
	.byte	30
	.byte	'Ifx_SRC_GPMU',0,4,145,4,3
	.word	25960
	.byte	18,32
	.word	23237
	.byte	19,0,0,14
	.word	25987
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,4,148,4,25,32,13
	.byte	'PSI5',0
	.word	25996
	.byte	32,2,35,0,0,14
	.word	26001
	.byte	30
	.byte	'Ifx_SRC_GPSI5',0,4,151,4,3
	.word	26037
	.byte	18,32
	.word	23297
	.byte	19,0,0,14
	.word	26065
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,4,154,4,25,32,13
	.byte	'PSI5S',0
	.word	26074
	.byte	32,2,35,0,0,14
	.word	26079
	.byte	30
	.byte	'Ifx_SRC_GPSI5S',0,4,157,4,3
	.word	26117
	.byte	18,96
	.word	23359
	.byte	19,3,0,14
	.word	26146
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,4,160,4,25,96,13
	.byte	'QSPI',0
	.word	26155
	.byte	96,2,35,0,0,14
	.word	26160
	.byte	30
	.byte	'Ifx_SRC_GQSPI',0,4,163,4,3
	.word	26196
	.byte	18,4
	.word	23479
	.byte	19,0,0,14
	.word	26224
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,4,166,4,25,4,13
	.byte	'SCR',0
	.word	26233
	.byte	4,2,35,0,0,14
	.word	26238
	.byte	30
	.byte	'Ifx_SRC_GSCR',0,4,169,4,3
	.word	26272
	.byte	14
	.word	23537
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,4,172,4,25,20,13
	.byte	'SCU',0
	.word	26299
	.byte	20,2,35,0,0,14
	.word	26304
	.byte	30
	.byte	'Ifx_SRC_GSCU',0,4,175,4,3
	.word	26338
	.byte	18,24
	.word	23618
	.byte	19,0,0,14
	.word	26365
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,4,178,4,25,24,13
	.byte	'SENT',0
	.word	26374
	.byte	24,2,35,0,0,14
	.word	26379
	.byte	30
	.byte	'Ifx_SRC_GSENT',0,4,181,4,3
	.word	26415
	.byte	18,12
	.word	23678
	.byte	19,0,0,14
	.word	26443
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,4,184,4,25,12,13
	.byte	'SMU',0
	.word	26452
	.byte	12,2,35,0,0,14
	.word	26457
	.byte	30
	.byte	'Ifx_SRC_GSMU',0,4,187,4,3
	.word	26491
	.byte	18,16
	.word	23736
	.byte	19,1,0,14
	.word	26518
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,4,190,4,25,16,13
	.byte	'STM',0
	.word	26527
	.byte	16,2,35,0,0,14
	.word	26532
	.byte	30
	.byte	'Ifx_SRC_GSTM',0,4,193,4,3
	.word	26566
	.byte	18,64
	.word	23912
	.byte	19,3,0,14
	.word	26593
	.byte	18,224,1
	.word	519
	.byte	19,223,1,0,18,32
	.word	23808
	.byte	19,1,0,14
	.word	26618
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,4,196,4,25,192,2,13
	.byte	'G',0
	.word	26602
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	26607
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	26627
	.byte	32,3,35,160,2,0,14
	.word	26632
	.byte	30
	.byte	'Ifx_SRC_GVADC',0,4,201,4,3
	.word	26701
	.byte	14
	.word	24014
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,4,204,4,25,4,13
	.byte	'XBAR',0
	.word	26729
	.byte	4,2,35,0,0,14
	.word	26734
	.byte	30
	.byte	'Ifx_SRC_GXBAR',0,4,207,4,3
	.word	26770
	.byte	30
	.byte	'SpiIf_Status',0,14,74,3
	.word	17821
	.byte	30
	.byte	'SpiIf_Ch',0,14,114,32
	.word	17622
	.byte	30
	.byte	'SpiIf_ChConfig',0,14,115,32
	.word	18257
	.byte	30
	.byte	'SpiIf',0,14,116,32
	.word	18129
	.byte	14
	.word	17340
	.byte	30
	.byte	'SpiIf_Flags',0,14,122,3
	.word	26873
	.byte	30
	.byte	'SpiIf_Job',0,14,128,1,3
	.word	17510
	.byte	15,14,131,1,9,1,16
	.byte	'SpiIf_Mode_master',0,0,16
	.byte	'SpiIf_Mode_slave',0,1,16
	.byte	'SpiIf_Mode_undefined',0,2,0,30
	.byte	'SpiIf_Mode',0,14,136,1,3
	.word	26917
	.byte	30
	.byte	'SpiIf_SlsoTiming_HalfTsclk',0,14,141,1,16
	.word	2020
	.byte	30
	.byte	'SpiIf_funcs',0,14,151,1,3
	.word	18040
	.byte	30
	.byte	'Spi_ErrorChecks',0,14,186,1,3
	.word	17395
	.byte	30
	.byte	'SpiIf_ChMode',0,14,208,1,3
	.word	16969
	.byte	15,20,236,10,9,1,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,16
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,30
	.byte	'IfxScu_CCUCON0_CLKSEL',0,20,240,10,3
	.word	27110
	.byte	15,20,250,10,9,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,16
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,16
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,30
	.byte	'IfxScu_WDTCON1_IR',0,20,255,10,3
	.word	27207
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_ACCEN0_Bits',0,6,79,3
	.word	27329
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1_Bits',0,6,85,3
	.word	27886
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,6,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	496
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,6,94,3
	.word	27963
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,6,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	519
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	519
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	519
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	519
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	519
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON0_Bits',0,6,111,3
	.word	28099
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,6,114,16,4,11
	.byte	'CANDIV',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	519
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	519
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	519
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	519
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON1_Bits',0,6,126,3
	.word	28379
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,6,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON2_Bits',0,6,135,1,3
	.word	28617
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,6,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	519
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	519
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON3_Bits',0,6,150,1,3
	.word	28745
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,6,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	519
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	519
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON4_Bits',0,6,165,1,3
	.word	28988
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,6,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CCUCON5_Bits',0,6,174,1,3
	.word	29223
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,6,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6_Bits',0,6,181,1,3
	.word	29351
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,6,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7_Bits',0,6,188,1,3
	.word	29451
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,6,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	519
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_CHIPID_Bits',0,6,202,1,3
	.word	29551
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,6,205,1,16,4,11
	.byte	'PWD',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	496
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSCON_Bits',0,6,213,1,3
	.word	29759
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,6,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	1070
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	519
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	1070
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_DTSLIM_Bits',0,6,225,1,3
	.word	29924
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,6,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	1070
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	519
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,6,235,1,3
	.word	30107
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,6,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	519
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	496
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	519
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	519
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EICR_Bits',0,6,129,2,3
	.word	30261
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,6,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR_Bits',0,6,143,2,3
	.word	30625
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,6,146,2,16,4,11
	.byte	'POL',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1070
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	519
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	519
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	519
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_EMSR_Bits',0,6,159,2,3
	.word	30836
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,6,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	1070
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	496
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG_Bits',0,6,167,2,3
	.word	31088
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,6,170,2,16,4,11
	.byte	'ARI',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG_Bits',0,6,175,2,3
	.word	31206
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,6,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR13CON_Bits',0,6,185,2,3
	.word	31317
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,6,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVR33CON_Bits',0,6,195,2,3
	.word	31480
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,6,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,6,205,2,3
	.word	31643
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,6,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,6,215,2,3
	.word	31801
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,6,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	519
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	519
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	519
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	519
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	519
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	519
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	1070
	.byte	10,0,2,35,2,0,30
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,6,232,2,3
	.word	31966
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,6,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	1070
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	519
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	519
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	1070
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	519
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,6,245,2,3
	.word	32295
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,6,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVROVMON_Bits',0,6,255,2,3
	.word	32516
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,6,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,6,142,3,3
	.word	32679
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,6,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,6,152,3,3
	.word	32951
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,6,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,6,162,3,3
	.word	33104
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,6,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,6,172,3,3
	.word	33260
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,6,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,6,181,3,3
	.word	33422
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,6,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,6,191,3,3
	.word	33565
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,6,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,6,200,3,3
	.word	33730
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,6,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,6,211,3,3
	.word	33875
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,6,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	519
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,6,222,3,3
	.word	34056
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,6,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,6,232,3,3
	.word	34230
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,6,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,6,241,3,3
	.word	34390
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,6,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,6,130,4,3
	.word	34534
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,6,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,6,139,4,3
	.word	34808
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,6,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,6,149,4,3
	.word	34947
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,6,152,4,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	519
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	1070
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	519
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	519
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_EXTCON_Bits',0,6,163,4,3
	.word	35110
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,6,166,4,16,4,11
	.byte	'STEP',0,2
	.word	1070
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	519
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	1070
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_FDR_Bits',0,6,174,4,3
	.word	35328
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,6,177,4,16,4,11
	.byte	'FS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_SCU_FMR_Bits',0,6,197,4,3
	.word	35491
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,6,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_ID_Bits',0,6,205,4,3
	.word	35827
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,6,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	519
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_SCU_IGCR_Bits',0,6,232,4,3
	.word	35934
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,6,235,4,16,4,11
	.byte	'P0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_IN_Bits',0,6,240,4,3
	.word	36386
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,6,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	519
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_IOCR_Bits',0,6,250,4,3
	.word	36485
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,6,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	1070
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,6,131,5,3
	.word	36635
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,6,134,5,16,4,11
	.byte	'SEED',0,4
	.word	496
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,6,141,5,3
	.word	36784
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,6,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,6,149,5,3
	.word	36945
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,6,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	1070
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_LCLCON_Bits',0,6,158,5,3
	.word	37075
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,6,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST_Bits',0,6,166,5,3
	.word	37207
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,6,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	1070
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_MANID_Bits',0,6,174,5,3
	.word	37322
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,6,177,5,16,4,11
	.byte	'PS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1070
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1070
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_OMR_Bits',0,6,185,5,3
	.word	37433
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,6,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	519
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	519
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	519
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	519
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_OSCCON_Bits',0,6,209,5,3
	.word	37591
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,6,212,5,16,4,11
	.byte	'P0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_OUT_Bits',0,6,217,5,3
	.word	38003
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,6,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	1070
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	6,0,2,35,3,0,30
	.byte	'Ifx_SCU_OVCCON_Bits',0,6,233,5,3
	.word	38104
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,6,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	496
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,6,242,5,3
	.word	38371
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,6,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC_Bits',0,6,250,5,3
	.word	38507
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,6,253,5,16,4,11
	.byte	'PD0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	519
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDR_Bits',0,6,132,6,3
	.word	38618
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,6,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR_Bits',0,6,146,6,3
	.word	38751
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,6,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1070
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLCON0_Bits',0,6,166,6,3
	.word	38954
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,6,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	519
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	519
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	519
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1070
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON1_Bits',0,6,177,6,3
	.word	39310
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,6,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	1070
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLCON2_Bits',0,6,184,6,3
	.word	39488
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,6,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	1070
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	519
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	519
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,6,204,6,3
	.word	39588
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,6,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	519
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	519
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	519
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1070
	.byte	9,0,2,35,2,0,30
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,6,215,6,3
	.word	39958
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,6,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,6,227,6,3
	.word	40144
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,6,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,6,241,6,3
	.word	40342
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,6,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	519
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	496
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR_Bits',0,6,251,6,3
	.word	40575
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,6,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	519
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	519
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	519
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,6,153,7,3
	.word	40727
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,6,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	519
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	519
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	519
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,6,170,7,3
	.word	41294
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,6,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	519
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	519
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	519
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,6,187,7,3
	.word	41588
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,6,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	519
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	519
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1070
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	519
	.byte	4,0,2,35,3,0,30
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,6,214,7,3
	.word	41866
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,6,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1070
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,6,230,7,3
	.word	42362
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,6,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	1070
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON2_Bits',0,6,243,7,3
	.word	42675
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,6,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	519
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	519
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	519
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	519
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	519
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_RSTCON_Bits',0,6,129,8,3
	.word	42884
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,6,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	519
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	519
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	519
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,6,155,8,3
	.word	43095
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,6,158,8,16,4,11
	.byte	'HBT',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON_Bits',0,6,162,8,3
	.word	43527
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,6,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	519
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	519
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	519
	.byte	7,0,2,35,3,0,30
	.byte	'Ifx_SCU_STSTAT_Bits',0,6,178,8,3
	.word	43623
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,6,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,6,186,8,3
	.word	43883
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,6,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	519
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	496
	.byte	23,0,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON_Bits',0,6,198,8,3
	.word	44008
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,6,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,6,208,8,3
	.word	44205
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,6,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,6,218,8,3
	.word	44358
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,6,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET_Bits',0,6,228,8,3
	.word	44511
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,6,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,6,238,8,3
	.word	44664
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,6,247,8,3
	.word	934
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,6,134,9,3
	.word	1092
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,6,150,9,3
	.word	1336
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,6,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	918
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,6,159,9,3
	.word	44919
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,6,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,6,175,9,3
	.word	45045
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,6,178,9,16,4,11
	.byte	'AE',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,6,191,9,3
	.word	45297
	.byte	12,6,199,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27329
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN0',0,6,204,9,3
	.word	45516
	.byte	12,6,207,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27886
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ACCEN1',0,6,212,9,3
	.word	45580
	.byte	12,6,215,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27963
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ARSTDIS',0,6,220,9,3
	.word	45644
	.byte	12,6,223,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28099
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON0',0,6,228,9,3
	.word	45709
	.byte	12,6,231,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28379
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON1',0,6,236,9,3
	.word	45774
	.byte	12,6,239,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28617
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON2',0,6,244,9,3
	.word	45839
	.byte	12,6,247,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28745
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON3',0,6,252,9,3
	.word	45904
	.byte	12,6,255,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28988
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON4',0,6,132,10,3
	.word	45969
	.byte	12,6,135,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29223
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON5',0,6,140,10,3
	.word	46034
	.byte	12,6,143,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29351
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON6',0,6,148,10,3
	.word	46099
	.byte	12,6,151,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29451
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CCUCON7',0,6,156,10,3
	.word	46164
	.byte	12,6,159,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29551
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_CHIPID',0,6,164,10,3
	.word	46229
	.byte	12,6,167,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29759
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSCON',0,6,172,10,3
	.word	46293
	.byte	12,6,175,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29924
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSLIM',0,6,180,10,3
	.word	46357
	.byte	12,6,183,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30107
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_DTSSTAT',0,6,188,10,3
	.word	46421
	.byte	12,6,191,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30261
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EICR',0,6,196,10,3
	.word	46486
	.byte	12,6,199,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30625
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EIFR',0,6,204,10,3
	.word	46548
	.byte	12,6,207,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30836
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EMSR',0,6,212,10,3
	.word	46610
	.byte	12,6,215,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31088
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESRCFG',0,6,220,10,3
	.word	46672
	.byte	12,6,223,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31206
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ESROCFG',0,6,228,10,3
	.word	46736
	.byte	12,6,231,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31317
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR13CON',0,6,236,10,3
	.word	46801
	.byte	12,6,239,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31480
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVR33CON',0,6,244,10,3
	.word	46867
	.byte	12,6,247,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31643
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRADCSTAT',0,6,252,10,3
	.word	46933
	.byte	12,6,255,10,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31801
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRDVSTAT',0,6,132,11,3
	.word	47001
	.byte	12,6,135,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31966
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRMONCTRL',0,6,140,11,3
	.word	47068
	.byte	12,6,143,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32295
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROSCCTRL',0,6,148,11,3
	.word	47136
	.byte	12,6,151,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32516
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVROVMON',0,6,156,11,3
	.word	47204
	.byte	12,6,159,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32679
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRRSTCON',0,6,164,11,3
	.word	47270
	.byte	12,6,167,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32951
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,6,172,11,3
	.word	47337
	.byte	12,6,175,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33104
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,6,180,11,3
	.word	47406
	.byte	12,6,183,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33260
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,6,188,11,3
	.word	47475
	.byte	12,6,191,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33422
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,6,196,11,3
	.word	47544
	.byte	12,6,199,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33565
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,6,204,11,3
	.word	47613
	.byte	12,6,207,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33730
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,6,212,11,3
	.word	47682
	.byte	12,6,215,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33875
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL1',0,6,220,11,3
	.word	47751
	.byte	12,6,223,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34056
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL2',0,6,228,11,3
	.word	47819
	.byte	12,6,231,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34230
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL3',0,6,236,11,3
	.word	47887
	.byte	12,6,239,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34390
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSDCTRL4',0,6,244,11,3
	.word	47955
	.byte	12,6,247,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34534
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRSTAT',0,6,252,11,3
	.word	48023
	.byte	12,6,255,11,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34808
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRTRIM',0,6,132,12,3
	.word	48088
	.byte	12,6,135,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34947
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EVRUVMON',0,6,140,12,3
	.word	48153
	.byte	12,6,143,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35110
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_EXTCON',0,6,148,12,3
	.word	48219
	.byte	12,6,151,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35328
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FDR',0,6,156,12,3
	.word	48283
	.byte	12,6,159,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35491
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_FMR',0,6,164,12,3
	.word	48344
	.byte	12,6,167,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35827
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_ID',0,6,172,12,3
	.word	48405
	.byte	12,6,175,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35934
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IGCR',0,6,180,12,3
	.word	48465
	.byte	12,6,183,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36386
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IN',0,6,188,12,3
	.word	48527
	.byte	12,6,191,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36485
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_IOCR',0,6,196,12,3
	.word	48587
	.byte	12,6,199,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36635
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL0',0,6,204,12,3
	.word	48649
	.byte	12,6,207,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36784
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL1',0,6,212,12,3
	.word	48717
	.byte	12,6,215,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36945
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LBISTCTRL2',0,6,220,12,3
	.word	48785
	.byte	12,6,223,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37075
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLCON',0,6,228,12,3
	.word	48853
	.byte	12,6,231,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37207
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_LCLTEST',0,6,236,12,3
	.word	48917
	.byte	12,6,239,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37322
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_MANID',0,6,244,12,3
	.word	48982
	.byte	12,6,247,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37433
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OMR',0,6,252,12,3
	.word	49045
	.byte	12,6,255,12,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37591
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OSCCON',0,6,132,13,3
	.word	49106
	.byte	12,6,135,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38003
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OUT',0,6,140,13,3
	.word	49170
	.byte	12,6,143,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38104
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCCON',0,6,148,13,3
	.word	49231
	.byte	12,6,151,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38371
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_OVCENABLE',0,6,156,13,3
	.word	49295
	.byte	12,6,159,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38507
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDISC',0,6,164,13,3
	.word	49362
	.byte	12,6,167,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38618
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDR',0,6,172,13,3
	.word	49425
	.byte	12,6,175,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38751
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PDRR',0,6,180,13,3
	.word	49486
	.byte	12,6,183,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38954
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON0',0,6,188,13,3
	.word	49548
	.byte	12,6,191,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39310
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON1',0,6,196,13,3
	.word	49613
	.byte	12,6,199,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39488
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLCON2',0,6,204,13,3
	.word	49678
	.byte	12,6,207,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39588
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON0',0,6,212,13,3
	.word	49743
	.byte	12,6,215,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39958
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYCON1',0,6,220,13,3
	.word	49812
	.byte	12,6,223,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40144
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLERAYSTAT',0,6,228,13,3
	.word	49881
	.byte	12,6,231,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40342
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PLLSTAT',0,6,236,13,3
	.word	49950
	.byte	12,6,239,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40575
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMCSR',0,6,244,13,3
	.word	50015
	.byte	12,6,247,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40727
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR0',0,6,252,13,3
	.word	50078
	.byte	12,6,255,13,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41294
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR1',0,6,132,14,3
	.word	50143
	.byte	12,6,135,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41588
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWCR2',0,6,140,14,3
	.word	50208
	.byte	12,6,143,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41866
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTAT',0,6,148,14,3
	.word	50273
	.byte	12,6,151,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42362
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_PMSWSTATCLR',0,6,156,14,3
	.word	50339
	.byte	12,6,159,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42884
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON',0,6,164,14,3
	.word	50408
	.byte	12,6,167,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42675
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTCON2',0,6,172,14,3
	.word	50472
	.byte	12,6,175,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43095
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_RSTSTAT',0,6,180,14,3
	.word	50537
	.byte	12,6,183,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43527
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SAFECON',0,6,188,14,3
	.word	50602
	.byte	12,6,191,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43623
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_STSTAT',0,6,196,14,3
	.word	50667
	.byte	12,6,199,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43883
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SWRSTCON',0,6,204,14,3
	.word	50731
	.byte	12,6,207,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44008
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_SYSCON',0,6,212,14,3
	.word	50797
	.byte	12,6,215,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44205
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPCLR',0,6,220,14,3
	.word	50861
	.byte	12,6,223,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44358
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPDIS',0,6,228,14,3
	.word	50926
	.byte	12,6,231,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44511
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSET',0,6,236,14,3
	.word	50991
	.byte	12,6,239,14,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44664
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_TRAPSTAT',0,6,244,14,3
	.word	51056
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON0',0,6,252,14,3
	.word	1030
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_CON1',0,6,132,15,3
	.word	1296
	.byte	30
	.byte	'Ifx_SCU_WDTCPU_SR',0,6,140,15,3
	.word	1527
	.byte	12,6,143,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44919
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON0',0,6,148,15,3
	.word	51207
	.byte	12,6,151,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45045
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_CON1',0,6,156,15,3
	.word	51274
	.byte	12,6,159,15,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45297
	.byte	4,2,35,0,0,30
	.byte	'Ifx_SCU_WDTS_SR',0,6,164,15,3
	.word	51341
	.byte	14
	.word	1567
	.byte	30
	.byte	'Ifx_SCU_WDTCPU',0,6,180,15,3
	.word	51406
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,6,183,15,25,12,13
	.byte	'CON0',0
	.word	51207
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	51274
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	51341
	.byte	4,2,35,8,0,14
	.word	51435
	.byte	30
	.byte	'Ifx_SCU_WDTS',0,6,188,15,3
	.word	51496
	.byte	18,8
	.word	46672
	.byte	19,1,0,18,20
	.word	519
	.byte	19,19,0,18,8
	.word	50015
	.byte	19,1,0,14
	.word	51435
	.byte	18,24
	.word	1567
	.byte	19,1,0,14
	.word	51555
	.byte	18,28
	.word	519
	.byte	19,27,0,18,16
	.word	46486
	.byte	19,3,0,18,16
	.word	48465
	.byte	19,3,0,18,180,3
	.word	519
	.byte	19,179,3,0,10
	.byte	'_Ifx_SCU',0,6,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	5076
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	48405
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	3257
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	49106
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	49950
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	49548
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	49613
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	49678
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	49881
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	49743
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	49812
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	45709
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	45774
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	48283
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	48219
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	45839
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	45904
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	45969
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	46034
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	50537
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3257
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	50408
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	45644
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	50731
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	50472
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3257
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	47270
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	51523
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	46736
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	50797
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	46099
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	46164
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	51532
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	49425
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	48587
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	49170
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	49045
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	48527
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	48023
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	47001
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	46801
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	46867
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	50667
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	3257
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	50078
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	50273
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	50339
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	51541
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	3257
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	46421
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	46293
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	50143
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	50208
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	51550
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	46610
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	51564
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	5416
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	51056
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	50991
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	50861
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	50926
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	3257
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	48853
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	48917
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	46229
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	48982
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	5076
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	50602
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	21799
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	48649
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	48717
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	48785
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	51569
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	49362
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	5076
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	48088
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	46933
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	48153
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	47204
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	47068
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	3257
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	47751
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	47819
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	47887
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	47955
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	47337
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	47406
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	47475
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	47544
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	47613
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	47682
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	47136
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	3257
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	49295
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	49231
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	21434
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	51578
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	46548
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	48344
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	49486
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	51587
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	3257
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	46357
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	51596
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	45580
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	45516
	.byte	4,3,35,252,7,0,14
	.word	51607
	.byte	30
	.byte	'Ifx_SCU',0,6,181,16,3
	.word	53597
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,21,45,16,4,11
	.byte	'ADDR',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_A_Bits',0,21,48,3
	.word	53619
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,21,51,16,4,11
	.byte	'VSS',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	918
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BIV_Bits',0,21,55,3
	.word	53680
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,21,58,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	918
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_BTV_Bits',0,21,62,3
	.word	53759
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,21,65,16,4,11
	.byte	'CountValue',0,4
	.word	918
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT_Bits',0,21,69,3
	.word	53845
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,21,72,16,4,11
	.byte	'CM',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	918
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	918
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	918
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	918
	.byte	21,0,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL_Bits',0,21,80,3
	.word	53934
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,21,83,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	918
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT_Bits',0,21,89,3
	.word	54080
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,21,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID_Bits',0,21,96,3
	.word	54207
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,21,99,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L_Bits',0,21,103,3
	.word	54305
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,21,106,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U_Bits',0,21,110,3
	.word	54398
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,21,113,16,4,11
	.byte	'MODREV',0,4
	.word	918
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	918
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID_Bits',0,21,118,3
	.word	54491
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,21,121,16,4,11
	.byte	'XE',0,4
	.word	918
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE_Bits',0,21,125,3
	.word	54598
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,21,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	918
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT_Bits',0,21,136,1,3
	.word	54685
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,21,139,1,16,4,11
	.byte	'CID',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID_Bits',0,21,143,1,3
	.word	54839
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,21,146,1,16,4,11
	.byte	'DATA',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_D_Bits',0,21,149,1,3
	.word	54933
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,21,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	918
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	918
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	918
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	918
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	918
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	918
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DATR_Bits',0,21,163,1,3
	.word	54996
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,21,166,1,16,4,11
	.byte	'DE',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	918
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	918
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	918
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	918
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	918
	.byte	19,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR_Bits',0,21,177,1,3
	.word	55214
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,21,180,1,16,4,11
	.byte	'DTA',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	918
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR_Bits',0,21,184,1,3
	.word	55429
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,21,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	918
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0_Bits',0,21,192,1,3
	.word	55523
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,21,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2_Bits',0,21,199,1,3
	.word	55639
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,21,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	918
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_CPU_DCX_Bits',0,21,206,1,3
	.word	55740
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,21,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD_Bits',0,21,212,1,3
	.word	55833
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,21,215,1,16,4,11
	.byte	'TA',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR_Bits',0,21,218,1,3
	.word	55913
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,21,221,1,16,4,11
	.byte	'IED',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	918
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	918
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	918
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	918
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	918
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR_Bits',0,21,233,1,3
	.word	55982
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,21,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	918
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_DMS_Bits',0,21,240,1,3
	.word	56211
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,21,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L_Bits',0,21,247,1,3
	.word	56304
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,21,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	918
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U_Bits',0,21,254,1,3
	.word	56399
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,21,129,2,16,4,11
	.byte	'RE',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE_Bits',0,21,133,2,3
	.word	56494
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,21,136,2,16,4,11
	.byte	'WE',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE_Bits',0,21,140,2,3
	.word	56584
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,21,143,2,16,4,11
	.byte	'SRE',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	918
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	918
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	918
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	918
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	918
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	918
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	918
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	918
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	918
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	918
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	918
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	918
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR_Bits',0,21,161,2,3
	.word	56674
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,21,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	918
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT_Bits',0,21,172,2,3
	.word	56998
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,21,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	918
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	918
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FCX_Bits',0,21,180,2,3
	.word	57152
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,21,183,2,16,4,11
	.byte	'TST',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	918
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	918
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	918
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	918
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	918
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	918
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	918
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	918
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	918
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	918
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	918
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	918
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	918
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	918
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,21,202,2,3
	.word	57258
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,205,2,16,4,11
	.byte	'OPC',0,4
	.word	918
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	918
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	918
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	918
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	918
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,21,212,2,3
	.word	57607
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,21,215,2,16,4,11
	.byte	'PC',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,21,218,2,3
	.word	57767
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,21,224,2,3
	.word	57848
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,21,230,2,3
	.word	57935
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,21,236,2,3
	.word	58022
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,21,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	918
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT_Bits',0,21,243,2,3
	.word	58109
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,21,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	918
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	918
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	918
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	918
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	918
	.byte	6,0,2,35,0,0,30
	.byte	'Ifx_CPU_ICR_Bits',0,21,253,2,3
	.word	58200
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,21,128,3,16,4,11
	.byte	'ISP',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_ISP_Bits',0,21,131,3,3
	.word	58343
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,21,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	918
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	918
	.byte	12,0,2,35,0,0,30
	.byte	'Ifx_CPU_LCX_Bits',0,21,139,3,3
	.word	58409
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,21,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	918
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT_Bits',0,21,146,3,3
	.word	58515
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,21,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	918
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT_Bits',0,21,153,3,3
	.word	58608
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,21,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	918
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT_Bits',0,21,160,3,3
	.word	58701
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,21,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	918
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_CPU_PC_Bits',0,21,167,3,3
	.word	58794
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,21,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	918
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0_Bits',0,21,175,3,3
	.word	58879
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,21,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	918
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1_Bits',0,21,183,3,3
	.word	58995
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,21,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2_Bits',0,21,190,3,3
	.word	59106
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,21,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	918
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	918
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	918
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	918
	.byte	10,0,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI_Bits',0,21,200,3,3
	.word	59207
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,21,203,3,16,4,11
	.byte	'TA',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR_Bits',0,21,206,3,3
	.word	59337
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,21,209,3,16,4,11
	.byte	'IED',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	918
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	918
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	918
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	918
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	918
	.byte	18,0,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR_Bits',0,21,221,3,3
	.word	59406
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,21,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	918
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0_Bits',0,21,229,3,3
	.word	59635
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,21,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	918
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	918
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1_Bits',0,21,237,3,3
	.word	59748
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,21,240,3,16,4,11
	.byte	'PSI',0,4
	.word	918
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	918
	.byte	16,0,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2_Bits',0,21,244,3,3
	.word	59861
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,21,247,3,16,4,11
	.byte	'FRE',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	918
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	918
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	918
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	918
	.byte	17,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR_Bits',0,21,129,4,3
	.word	59952
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,21,132,4,16,4,11
	.byte	'CDC',0,4
	.word	918
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	918
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	918
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	918
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	918
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	918
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	918
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	918
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	918
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	918
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	918
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	918
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_PSW_Bits',0,21,147,4,3
	.word	60155
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,21,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	918
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	918
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	918
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	918
	.byte	1,0,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN_Bits',0,21,156,4,3
	.word	60398
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,21,159,4,16,4,11
	.byte	'PC',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	918
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	918
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	918
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	918
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	918
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	918
	.byte	7,0,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON_Bits',0,21,171,4,3
	.word	60526
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,21,174,4,16,4,11
	.byte	'EN',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,21,177,4,3
	.word	60767
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,21,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,21,183,4,3
	.word	60850
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,186,4,16,4,11
	.byte	'EN',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,21,189,4,3
	.word	60941
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,21,195,4,3
	.word	61032
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,21,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,21,202,4,3
	.word	61131
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,21,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,21,209,4,3
	.word	61238
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,21,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	918
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT_Bits',0,21,220,4,3
	.word	61345
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,21,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	918
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON_Bits',0,21,231,4,3
	.word	61499
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,21,234,4,16,4,11
	.byte	'ASI',0,4
	.word	918
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	918
	.byte	27,0,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,21,238,4,3
	.word	61660
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,21,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	918
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	918
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	918
	.byte	15,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON_Bits',0,21,249,4,3
	.word	61758
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,21,252,4,16,4,11
	.byte	'Timer',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,21,255,4,3
	.word	61930
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,21,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	918
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR_Bits',0,21,133,5,3
	.word	62010
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,21,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	918
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	918
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	918
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	918
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	918
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	918
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	918
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	918
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	918
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	918
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	918
	.byte	3,0,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT_Bits',0,21,153,5,3
	.word	62083
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,21,156,5,16,4,11
	.byte	'T0',0,4
	.word	918
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	918
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	918
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	918
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	918
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	918
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	918
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	918
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	918
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,21,167,5,3
	.word	62401
	.byte	12,21,175,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53619
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_A',0,21,180,5,3
	.word	62596
	.byte	12,21,183,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53680
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BIV',0,21,188,5,3
	.word	62655
	.byte	12,21,191,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53759
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_BTV',0,21,196,5,3
	.word	62716
	.byte	12,21,199,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53845
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCNT',0,21,204,5,3
	.word	62777
	.byte	12,21,207,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53934
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CCTRL',0,21,212,5,3
	.word	62839
	.byte	12,21,215,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54080
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_COMPAT',0,21,220,5,3
	.word	62902
	.byte	12,21,223,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54207
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CORE_ID',0,21,228,5,3
	.word	62966
	.byte	12,21,231,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54305
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_L',0,21,236,5,3
	.word	63031
	.byte	12,21,239,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54398
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPR_U',0,21,244,5,3
	.word	63094
	.byte	12,21,247,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54491
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPU_ID',0,21,252,5,3
	.word	63157
	.byte	12,21,255,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54598
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CPXE',0,21,132,6,3
	.word	63221
	.byte	12,21,135,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54685
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CREVT',0,21,140,6,3
	.word	63283
	.byte	12,21,143,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54839
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_CUS_ID',0,21,148,6,3
	.word	63346
	.byte	12,21,151,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54933
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_D',0,21,156,6,3
	.word	63410
	.byte	12,21,159,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54996
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DATR',0,21,164,6,3
	.word	63469
	.byte	12,21,167,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55214
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGSR',0,21,172,6,3
	.word	63531
	.byte	12,21,175,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55429
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DBGTCR',0,21,180,6,3
	.word	63594
	.byte	12,21,183,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55523
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON0',0,21,188,6,3
	.word	63658
	.byte	12,21,191,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55639
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCON2',0,21,196,6,3
	.word	63721
	.byte	12,21,199,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55740
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DCX',0,21,204,6,3
	.word	63784
	.byte	12,21,207,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55833
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DEADD',0,21,212,6,3
	.word	63845
	.byte	12,21,215,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55913
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIEAR',0,21,220,6,3
	.word	63908
	.byte	12,21,223,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	55982
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DIETR',0,21,228,6,3
	.word	63971
	.byte	12,21,231,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56211
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DMS',0,21,236,6,3
	.word	64034
	.byte	12,21,239,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56304
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_L',0,21,244,6,3
	.word	64095
	.byte	12,21,247,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56399
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPR_U',0,21,252,6,3
	.word	64158
	.byte	12,21,255,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56494
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPRE',0,21,132,7,3
	.word	64221
	.byte	12,21,135,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56584
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DPWE',0,21,140,7,3
	.word	64283
	.byte	12,21,143,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56674
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_DSTR',0,21,148,7,3
	.word	64345
	.byte	12,21,151,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	56998
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_EXEVT',0,21,156,7,3
	.word	64407
	.byte	12,21,159,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57152
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FCX',0,21,164,7,3
	.word	64470
	.byte	12,21,167,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57258
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,21,172,7,3
	.word	64531
	.byte	12,21,175,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57607
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,21,180,7,3
	.word	64601
	.byte	12,21,183,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57767
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,21,188,7,3
	.word	64671
	.byte	12,21,191,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57848
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,21,196,7,3
	.word	64740
	.byte	12,21,199,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	57935
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,21,204,7,3
	.word	64811
	.byte	12,21,207,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58022
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,21,212,7,3
	.word	64882
	.byte	12,21,215,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58109
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICNT',0,21,220,7,3
	.word	64953
	.byte	12,21,223,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58200
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ICR',0,21,228,7,3
	.word	65015
	.byte	12,21,231,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58343
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_ISP',0,21,236,7,3
	.word	65076
	.byte	12,21,239,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58409
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_LCX',0,21,244,7,3
	.word	65137
	.byte	12,21,247,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58515
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M1CNT',0,21,252,7,3
	.word	65198
	.byte	12,21,255,7,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58608
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M2CNT',0,21,132,8,3
	.word	65261
	.byte	12,21,135,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58701
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_M3CNT',0,21,140,8,3
	.word	65324
	.byte	12,21,143,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58794
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PC',0,21,148,8,3
	.word	65387
	.byte	12,21,151,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58879
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON0',0,21,156,8,3
	.word	65447
	.byte	12,21,159,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	58995
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON1',0,21,164,8,3
	.word	65510
	.byte	12,21,167,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59106
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCON2',0,21,172,8,3
	.word	65573
	.byte	12,21,175,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59207
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PCXI',0,21,180,8,3
	.word	65636
	.byte	12,21,183,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59337
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIEAR',0,21,188,8,3
	.word	65698
	.byte	12,21,191,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59406
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PIETR',0,21,196,8,3
	.word	65761
	.byte	12,21,199,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59635
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA0',0,21,204,8,3
	.word	65824
	.byte	12,21,207,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59748
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA1',0,21,212,8,3
	.word	65886
	.byte	12,21,215,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59861
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PMA2',0,21,220,8,3
	.word	65948
	.byte	12,21,223,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	59952
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSTR',0,21,228,8,3
	.word	66010
	.byte	12,21,231,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60155
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_PSW',0,21,236,8,3
	.word	66072
	.byte	12,21,239,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60398
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SEGEN',0,21,244,8,3
	.word	66133
	.byte	12,21,247,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60526
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SMACON',0,21,252,8,3
	.word	66196
	.byte	12,21,255,8,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60767
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENA',0,21,132,9,3
	.word	66260
	.byte	12,21,135,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60850
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_ACCENB',0,21,140,9,3
	.word	66330
	.byte	12,21,143,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	60941
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,21,148,9,3
	.word	66400
	.byte	12,21,151,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61032
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,21,156,9,3
	.word	66474
	.byte	12,21,159,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61131
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,21,164,9,3
	.word	66548
	.byte	12,21,167,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61238
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,21,172,9,3
	.word	66618
	.byte	12,21,175,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61345
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SWEVT',0,21,180,9,3
	.word	66688
	.byte	12,21,183,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61499
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_SYSCON',0,21,188,9,3
	.word	66751
	.byte	12,21,191,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61660
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TASK_ASI',0,21,196,9,3
	.word	66815
	.byte	12,21,199,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61758
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_CON',0,21,204,9,3
	.word	66881
	.byte	12,21,207,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	61930
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TPS_TIMER',0,21,212,9,3
	.word	66946
	.byte	12,21,215,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62010
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_ADR',0,21,220,9,3
	.word	67013
	.byte	12,21,223,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62083
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TR_EVT',0,21,228,9,3
	.word	67077
	.byte	12,21,231,9,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	62401
	.byte	4,2,35,0,0,30
	.byte	'Ifx_CPU_TRIG_ACC',0,21,236,9,3
	.word	67141
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,21,247,9,25,8,13
	.byte	'L',0
	.word	63031
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	63094
	.byte	4,2,35,4,0,14
	.word	67207
	.byte	30
	.byte	'Ifx_CPU_CPR',0,21,251,9,3
	.word	67249
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,21,254,9,25,8,13
	.byte	'L',0
	.word	64095
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	64158
	.byte	4,2,35,4,0,14
	.word	67275
	.byte	30
	.byte	'Ifx_CPU_DPR',0,21,130,10,3
	.word	67317
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,21,133,10,25,16,13
	.byte	'LA',0
	.word	66548
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	66618
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	66400
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	66474
	.byte	4,2,35,12,0,14
	.word	67343
	.byte	30
	.byte	'Ifx_CPU_SPROT_RGN',0,21,139,10,3
	.word	67425
	.byte	18,12
	.word	66946
	.byte	19,2,0,10
	.byte	'_Ifx_CPU_TPS',0,21,142,10,25,16,13
	.byte	'CON',0
	.word	66881
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	67457
	.byte	12,2,35,4,0,14
	.word	67466
	.byte	30
	.byte	'Ifx_CPU_TPS',0,21,146,10,3
	.word	67514
	.byte	10
	.byte	'_Ifx_CPU_TR',0,21,149,10,25,8,13
	.byte	'EVT',0
	.word	67077
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	67013
	.byte	4,2,35,4,0,14
	.word	67540
	.byte	30
	.byte	'Ifx_CPU_TR',0,21,153,10,3
	.word	67585
	.byte	18,176,32
	.word	519
	.byte	19,175,32,0,18,208,223,1
	.word	519
	.byte	19,207,223,1,0,18,248,1
	.word	519
	.byte	19,247,1,0,18,244,29
	.word	519
	.byte	19,243,29,0,18,188,3
	.word	519
	.byte	19,187,3,0,18,232,3
	.word	519
	.byte	19,231,3,0,18,252,23
	.word	519
	.byte	19,251,23,0,18,228,63
	.word	519
	.byte	19,227,63,0,18,128,1
	.word	67275
	.byte	19,15,0,14
	.word	67700
	.byte	18,128,31
	.word	519
	.byte	19,255,30,0,18,64
	.word	67207
	.byte	19,7,0,14
	.word	67726
	.byte	18,192,31
	.word	519
	.byte	19,191,31,0,18,16
	.word	63221
	.byte	19,3,0,18,16
	.word	64221
	.byte	19,3,0,18,16
	.word	64283
	.byte	19,3,0,18,208,7
	.word	519
	.byte	19,207,7,0,14
	.word	67466
	.byte	18,240,23
	.word	519
	.byte	19,239,23,0,18,64
	.word	67540
	.byte	19,7,0,14
	.word	67805
	.byte	18,192,23
	.word	519
	.byte	19,191,23,0,18,232,1
	.word	519
	.byte	19,231,1,0,18,180,1
	.word	519
	.byte	19,179,1,0,18,172,1
	.word	519
	.byte	19,171,1,0,18,64
	.word	63410
	.byte	19,15,0,18,64
	.word	519
	.byte	19,63,0,18,64
	.word	62596
	.byte	19,15,0,10
	.byte	'_Ifx_CPU',0,21,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	67610
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	66133
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	67621
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	66815
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	67634
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	65824
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	65886
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	65948
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	67645
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	63721
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	5076
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	66196
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	64345
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	3257
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	63469
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	63845
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	63908
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	63971
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	4447
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	63658
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	67656
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	66010
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	65510
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	65573
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	65447
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	65698
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	65761
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	67667
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	62902
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	67678
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	64531
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	64671
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	64601
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	3257
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	64740
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	64811
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	64882
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	67689
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	67710
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	67715
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	67735
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	67740
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	67751
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	67760
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	67769
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	67778
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	67789
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	67794
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	67814
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	67819
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	62839
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	62777
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	64953
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	65198
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	65261
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	65324
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	67830
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	63531
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	3257
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	64407
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	63283
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	66688
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	51569
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	67141
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	5416
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	64034
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	63784
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	63594
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	67841
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	65636
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	66072
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	65387
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	5076
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	66751
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	63157
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	62966
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	62655
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	62716
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	65076
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	65015
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	5076
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	64470
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	65137
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	21799
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	63346
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	67852
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	67863
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	67872
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	67881
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	67872
	.byte	64,4,35,192,255,3,0,14
	.word	67890
	.byte	30
	.byte	'Ifx_CPU',0,21,130,11,3
	.word	69681
	.byte	15,8,127,9,1,16
	.byte	'IfxCpu_Id_0',0,0,16
	.byte	'IfxCpu_Id_1',0,1,16
	.byte	'IfxCpu_Id_none',0,2,0,30
	.byte	'IfxCpu_Id',0,8,132,1,3
	.word	69703
	.byte	30
	.byte	'IfxCpu_ResourceCpu',0,8,161,1,3
	.word	1865
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,22,45,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_STM_ACCEN0_Bits',0,22,79,3
	.word	69801
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,22,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN1_Bits',0,22,85,3
	.word	70358
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,22,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CAP_Bits',0,22,91,3
	.word	70435
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,22,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CAPSV_Bits',0,22,97,3
	.word	70507
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,22,100,16,4,11
	.byte	'DISR',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_STM_CLC_Bits',0,22,107,3
	.word	70583
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,22,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	519
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	519
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	519
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	519
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	519
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	519
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	519
	.byte	3,0,2,35,3,0,30
	.byte	'Ifx_STM_CMCON_Bits',0,22,120,3
	.word	70724
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,22,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_CMP_Bits',0,22,126,3
	.word	70942
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,22,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	496
	.byte	25,0,2,35,0,0,30
	.byte	'Ifx_STM_ICR_Bits',0,22,139,1,3
	.word	71009
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,22,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_STM_ID_Bits',0,22,147,1,3
	.word	71212
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,22,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_STM_ISCR_Bits',0,22,157,1,3
	.word	71319
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,22,160,1,16,4,11
	.byte	'RST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	496
	.byte	30,0,2,35,0,0,30
	.byte	'Ifx_STM_KRST0_Bits',0,22,165,1,3
	.word	71470
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,22,168,1,16,4,11
	.byte	'RST',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_STM_KRST1_Bits',0,22,172,1,3
	.word	71581
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,22,175,1,16,4,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_STM_KRSTCLR_Bits',0,22,179,1,3
	.word	71673
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,22,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	519
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	519
	.byte	2,0,2,35,3,0,30
	.byte	'Ifx_STM_OCS_Bits',0,22,189,1,3
	.word	71769
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,22,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM0_Bits',0,22,195,1,3
	.word	71915
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,22,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM0SV_Bits',0,22,201,1,3
	.word	71987
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,22,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM1_Bits',0,22,207,1,3
	.word	72063
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,22,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM2_Bits',0,22,213,1,3
	.word	72135
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,22,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM3_Bits',0,22,219,1,3
	.word	72207
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,22,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM4_Bits',0,22,225,1,3
	.word	72280
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,22,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM5_Bits',0,22,231,1,3
	.word	72353
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,22,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_STM_TIM6_Bits',0,22,237,1,3
	.word	72426
	.byte	12,22,245,1,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69801
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN0',0,22,250,1,3
	.word	72499
	.byte	12,22,253,1,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70358
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ACCEN1',0,22,130,2,3
	.word	72563
	.byte	12,22,133,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70435
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CAP',0,22,138,2,3
	.word	72627
	.byte	12,22,141,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70507
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CAPSV',0,22,146,2,3
	.word	72688
	.byte	12,22,149,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70583
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CLC',0,22,154,2,3
	.word	72751
	.byte	12,22,157,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70724
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CMCON',0,22,162,2,3
	.word	72812
	.byte	12,22,165,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70942
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_CMP',0,22,170,2,3
	.word	72875
	.byte	12,22,173,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71009
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ICR',0,22,178,2,3
	.word	72936
	.byte	12,22,181,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71212
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ID',0,22,186,2,3
	.word	72997
	.byte	12,22,189,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71319
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_ISCR',0,22,194,2,3
	.word	73057
	.byte	12,22,197,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71470
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRST0',0,22,202,2,3
	.word	73119
	.byte	12,22,205,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71581
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRST1',0,22,210,2,3
	.word	73182
	.byte	12,22,213,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71673
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_KRSTCLR',0,22,218,2,3
	.word	73245
	.byte	12,22,221,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71769
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_OCS',0,22,226,2,3
	.word	73310
	.byte	12,22,229,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71915
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM0',0,22,234,2,3
	.word	73371
	.byte	12,22,237,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71987
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM0SV',0,22,242,2,3
	.word	73433
	.byte	12,22,245,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72063
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM1',0,22,250,2,3
	.word	73497
	.byte	12,22,253,2,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72135
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM2',0,22,130,3,3
	.word	73559
	.byte	12,22,133,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72207
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM3',0,22,138,3,3
	.word	73621
	.byte	12,22,141,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72280
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM4',0,22,146,3,3
	.word	73683
	.byte	12,22,149,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72353
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM5',0,22,154,3,3
	.word	73745
	.byte	12,22,157,3,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72426
	.byte	4,2,35,0,0,30
	.byte	'Ifx_STM_TIM6',0,22,162,3,3
	.word	73807
	.byte	15,7,144,1,9,1,16
	.byte	'IfxCpu_CounterMode_normal',0,0,16
	.byte	'IfxCpu_CounterMode_task',0,1,0,30
	.byte	'IfxCpu_CounterMode',0,7,148,1,3
	.word	73869
	.byte	29,7,160,1,9,6,13
	.byte	'counter',0
	.word	2020
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	519
	.byte	1,2,35,4,0,30
	.byte	'IfxCpu_Counter',0,7,164,1,3
	.word	73958
	.byte	29,7,172,1,9,32,13
	.byte	'instruction',0
	.word	73958
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	73958
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	73958
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	73958
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	73958
	.byte	6,2,35,24,0,30
	.byte	'IfxCpu_Perf',0,7,179,1,3
	.word	74024
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,23,45,16,4,11
	.byte	'EN0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,23,79,3
	.word	74142
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,23,82,16,4,11
	.byte	'reserved_0',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,23,85,3
	.word	74703
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,23,88,16,4,11
	.byte	'SEL',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,23,95,3
	.word	74784
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,23,98,16,4,11
	.byte	'VLD0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,23,111,3
	.word	74937
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,23,114,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	519
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,23,121,3
	.word	75185
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,23,124,16,4,11
	.byte	'STATUS',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	496
	.byte	24,0,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0_Bits',0,23,128,1,3
	.word	75331
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,23,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM1_Bits',0,23,136,1,3
	.word	75429
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,23,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_COMM2_Bits',0,23,144,1,3
	.word	75545
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,23,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1070
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRD_Bits',0,23,153,1,3
	.word	75661
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,23,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1070
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCRP_Bits',0,23,162,1,3
	.word	75801
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,23,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	496
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	1070
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_ECCW_Bits',0,23,171,1,3
	.word	75941
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,23,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	519
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	519
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	1070
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	519
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	519
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	519
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FCON_Bits',0,23,193,1,3
	.word	76080
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,23,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	519
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	519
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	519
	.byte	8,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FPRO_Bits',0,23,218,1,3
	.word	76442
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,23,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	1070
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	519
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	519
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	519
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_FSR_Bits',0,23,254,1,3
	.word	76883
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,23,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	519
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	519
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_ID_Bits',0,23,134,2,3
	.word	77489
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,23,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	1070
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARD_Bits',0,23,147,2,3
	.word	77600
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,23,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	1070
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_MARP_Bits',0,23,159,2,3
	.word	77814
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,23,162,2,16,4,11
	.byte	'L',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	519
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	519
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	1070
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	519
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCOND_Bits',0,23,179,2,3
	.word	78001
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,23,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	519
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	496
	.byte	28,0,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,23,188,2,3
	.word	78325
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,23,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	1070
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,23,199,2,3
	.word	78468
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	1070
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	519
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	519
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	519
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	1070
	.byte	14,0,2,35,2,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,23,219,2,3
	.word	78657
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,23,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	519
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	519
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,23,254,2,3
	.word	79020
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,23,129,3,16,4,11
	.byte	'S0L',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	519
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONP_Bits',0,23,160,3,3
	.word	79615
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,23,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	519
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	519
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	519
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	519
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	519
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	519
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	519
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	519
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	519
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	519
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	519
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	519
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	519
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	519
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	519
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	519
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	519
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	519
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	519
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	519
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	519
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,23,194,3,3
	.word	80139
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,23,197,3,16,4,11
	.byte	'TAG',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,23,201,3,3
	.word	80721
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,23,204,3,16,4,11
	.byte	'TAG',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,23,208,3,3
	.word	80823
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,23,211,3,16,4,11
	.byte	'TAG',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	496
	.byte	26,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,23,215,3,3
	.word	80925
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,23,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	496
	.byte	29,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD_Bits',0,23,222,3,3
	.word	81027
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,23,225,3,16,4,11
	.byte	'STRT',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	519
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	519
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	519
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	519
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	519
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	519
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	1070
	.byte	16,0,2,35,2,0,30
	.byte	'Ifx_FLASH_RRCT_Bits',0,23,236,3,3
	.word	81121
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,23,239,3,16,4,11
	.byte	'DATA',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0_Bits',0,23,242,3,3
	.word	81331
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,23,245,3,16,4,11
	.byte	'DATA',0,4
	.word	496
	.byte	32,0,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1_Bits',0,23,248,3,3
	.word	81404
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,23,251,3,16,4,11
	.byte	'SEL',0,1
	.word	519
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	519
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	519
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	496
	.byte	22,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,23,130,4,3
	.word	81477
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,23,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	519
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	496
	.byte	31,0,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,23,137,4,3
	.word	81632
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,23,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	519
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	496
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	519
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	519
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	519
	.byte	1,0,2,35,3,0,30
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,23,147,4,3
	.word	81737
	.byte	12,23,155,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74142
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN0',0,23,160,4,3
	.word	81885
	.byte	12,23,163,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74703
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ACCEN1',0,23,168,4,3
	.word	81951
	.byte	12,23,171,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74784
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_CFG',0,23,176,4,3
	.word	82017
	.byte	12,23,179,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74937
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_STAT',0,23,184,4,3
	.word	82085
	.byte	12,23,187,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75185
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_CBAB_TOP',0,23,192,4,3
	.word	82154
	.byte	12,23,195,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75331
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM0',0,23,200,4,3
	.word	82222
	.byte	12,23,203,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75429
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM1',0,23,208,4,3
	.word	82287
	.byte	12,23,211,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75545
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_COMM2',0,23,216,4,3
	.word	82352
	.byte	12,23,219,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75661
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRD',0,23,224,4,3
	.word	82417
	.byte	12,23,227,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75801
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCRP',0,23,232,4,3
	.word	82482
	.byte	12,23,235,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75941
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ECCW',0,23,240,4,3
	.word	82547
	.byte	12,23,243,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76080
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FCON',0,23,248,4,3
	.word	82611
	.byte	12,23,251,4,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76442
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FPRO',0,23,128,5,3
	.word	82675
	.byte	12,23,131,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	76883
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_FSR',0,23,136,5,3
	.word	82739
	.byte	12,23,139,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77489
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_ID',0,23,144,5,3
	.word	82802
	.byte	12,23,147,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77600
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARD',0,23,152,5,3
	.word	82864
	.byte	12,23,155,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	77814
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_MARP',0,23,160,5,3
	.word	82928
	.byte	12,23,163,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78001
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCOND',0,23,168,5,3
	.word	82992
	.byte	12,23,171,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78325
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONDBG',0,23,176,5,3
	.word	83059
	.byte	12,23,179,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78468
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSM',0,23,184,5,3
	.word	83128
	.byte	12,23,187,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	78657
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,23,192,5,3
	.word	83197
	.byte	12,23,195,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79020
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONOTP',0,23,200,5,3
	.word	83270
	.byte	12,23,203,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	79615
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONP',0,23,208,5,3
	.word	83339
	.byte	12,23,211,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80139
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_PROCONWOP',0,23,216,5,3
	.word	83406
	.byte	12,23,219,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80721
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG0',0,23,224,5,3
	.word	83475
	.byte	12,23,227,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80823
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG1',0,23,232,5,3
	.word	83543
	.byte	12,23,235,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	80925
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RDB_CFG2',0,23,240,5,3
	.word	83611
	.byte	12,23,243,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81027
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRAD',0,23,248,5,3
	.word	83679
	.byte	12,23,251,5,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81121
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRCT',0,23,128,6,3
	.word	83743
	.byte	12,23,131,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81331
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD0',0,23,136,6,3
	.word	83807
	.byte	12,23,139,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81404
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_RRD1',0,23,144,6,3
	.word	83871
	.byte	12,23,147,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81477
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_CFG',0,23,152,6,3
	.word	83935
	.byte	12,23,155,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81632
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_STAT',0,23,160,6,3
	.word	84003
	.byte	12,23,163,6,9,4,13
	.byte	'U',0
	.word	496
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	512
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81737
	.byte	4,2,35,0,0,30
	.byte	'Ifx_FLASH_UBAB_TOP',0,23,168,6,3
	.word	84072
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,23,179,6,25,12,13
	.byte	'CFG',0
	.word	82017
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	82085
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	82154
	.byte	4,2,35,8,0,14
	.word	84140
	.byte	30
	.byte	'Ifx_FLASH_CBAB',0,23,184,6,3
	.word	84203
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,23,187,6,25,12,13
	.byte	'CFG0',0
	.word	83475
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	83543
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	83611
	.byte	4,2,35,8,0,14
	.word	84232
	.byte	30
	.byte	'Ifx_FLASH_RDB',0,23,192,6,3
	.word	84296
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,23,195,6,25,12,13
	.byte	'CFG',0
	.word	83935
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	84003
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	84072
	.byte	4,2,35,8,0,14
	.word	84324
	.byte	30
	.byte	'Ifx_FLASH_UBAB',0,23,200,6,3
	.word	84387
	.byte	30
	.byte	'Ifx_P_ACCEN0_Bits',0,10,79,3
	.word	8829
	.byte	30
	.byte	'Ifx_P_ACCEN1_Bits',0,10,85,3
	.word	8742
	.byte	30
	.byte	'Ifx_P_ESR_Bits',0,10,107,3
	.word	5085
	.byte	30
	.byte	'Ifx_P_ID_Bits',0,10,115,3
	.word	3138
	.byte	30
	.byte	'Ifx_P_IN_Bits',0,10,137,1,3
	.word	4133
	.byte	30
	.byte	'Ifx_P_IOCR0_Bits',0,10,150,1,3
	.word	3266
	.byte	30
	.byte	'Ifx_P_IOCR12_Bits',0,10,163,1,3
	.word	3913
	.byte	30
	.byte	'Ifx_P_IOCR4_Bits',0,10,176,1,3
	.word	3481
	.byte	30
	.byte	'Ifx_P_IOCR8_Bits',0,10,189,1,3
	.word	3696
	.byte	30
	.byte	'Ifx_P_LPCR0_Bits',0,10,197,1,3
	.word	8101
	.byte	30
	.byte	'Ifx_P_LPCR1_Bits',0,10,205,1,3
	.word	8225
	.byte	30
	.byte	'Ifx_P_LPCR1_P21_Bits',0,10,215,1,3
	.word	8309
	.byte	30
	.byte	'Ifx_P_LPCR2_Bits',0,10,229,1,3
	.word	8489
	.byte	30
	.byte	'Ifx_P_OMCR0_Bits',0,10,240,1,3
	.word	6740
	.byte	30
	.byte	'Ifx_P_OMCR12_Bits',0,10,250,1,3
	.word	7264
	.byte	30
	.byte	'Ifx_P_OMCR4_Bits',0,10,133,2,3
	.word	6914
	.byte	30
	.byte	'Ifx_P_OMCR8_Bits',0,10,144,2,3
	.word	7088
	.byte	30
	.byte	'Ifx_P_OMCR_Bits',0,10,166,2,3
	.word	7753
	.byte	30
	.byte	'Ifx_P_OMR_Bits',0,10,203,2,3
	.word	2567
	.byte	30
	.byte	'Ifx_P_OMSR0_Bits',0,10,213,2,3
	.word	6077
	.byte	30
	.byte	'Ifx_P_OMSR12_Bits',0,10,224,2,3
	.word	6565
	.byte	30
	.byte	'Ifx_P_OMSR4_Bits',0,10,235,2,3
	.word	6224
	.byte	30
	.byte	'Ifx_P_OMSR8_Bits',0,10,246,2,3
	.word	6393
	.byte	30
	.byte	'Ifx_P_OMSR_Bits',0,10,140,3,3
	.word	7420
	.byte	30
	.byte	'Ifx_P_OUT_Bits',0,10,162,3,3
	.word	2251
	.byte	30
	.byte	'Ifx_P_PCSR_Bits',0,10,180,3,3
	.word	5791
	.byte	30
	.byte	'Ifx_P_PDISC_Bits',0,10,202,3,3
	.word	5425
	.byte	30
	.byte	'Ifx_P_PDR0_Bits',0,10,223,3,3
	.word	4456
	.byte	30
	.byte	'Ifx_P_PDR1_Bits',0,10,244,3,3
	.word	4760
	.byte	30
	.byte	'Ifx_P_ACCEN0',0,10,129,4,3
	.word	9356
	.byte	30
	.byte	'Ifx_P_ACCEN1',0,10,137,4,3
	.word	8789
	.byte	30
	.byte	'Ifx_P_ESR',0,10,145,4,3
	.word	5376
	.byte	30
	.byte	'Ifx_P_ID',0,10,153,4,3
	.word	3217
	.byte	30
	.byte	'Ifx_P_IN',0,10,161,4,3
	.word	4407
	.byte	30
	.byte	'Ifx_P_IOCR0',0,10,169,4,3
	.word	3441
	.byte	30
	.byte	'Ifx_P_IOCR12',0,10,177,4,3
	.word	4093
	.byte	30
	.byte	'Ifx_P_IOCR4',0,10,185,4,3
	.word	3656
	.byte	30
	.byte	'Ifx_P_IOCR8',0,10,193,4,3
	.word	3873
	.byte	30
	.byte	'Ifx_P_LPCR0',0,10,201,4,3
	.word	8185
	.byte	30
	.byte	'Ifx_P_LPCR1',0,10,210,4,3
	.word	8434
	.byte	30
	.byte	'Ifx_P_LPCR2',0,10,218,4,3
	.word	8693
	.byte	30
	.byte	'Ifx_P_OMCR',0,10,226,4,3
	.word	8061
	.byte	30
	.byte	'Ifx_P_OMCR0',0,10,234,4,3
	.word	6874
	.byte	30
	.byte	'Ifx_P_OMCR12',0,10,242,4,3
	.word	7380
	.byte	30
	.byte	'Ifx_P_OMCR4',0,10,250,4,3
	.word	7048
	.byte	30
	.byte	'Ifx_P_OMCR8',0,10,130,5,3
	.word	7224
	.byte	30
	.byte	'Ifx_P_OMR',0,10,138,5,3
	.word	3098
	.byte	30
	.byte	'Ifx_P_OMSR',0,10,146,5,3
	.word	7713
	.byte	30
	.byte	'Ifx_P_OMSR0',0,10,154,5,3
	.word	6184
	.byte	30
	.byte	'Ifx_P_OMSR12',0,10,162,5,3
	.word	6700
	.byte	30
	.byte	'Ifx_P_OMSR4',0,10,170,5,3
	.word	6353
	.byte	30
	.byte	'Ifx_P_OMSR8',0,10,178,5,3
	.word	6525
	.byte	30
	.byte	'Ifx_P_OUT',0,10,186,5,3
	.word	2527
	.byte	30
	.byte	'Ifx_P_PCSR',0,10,194,5,3
	.word	6037
	.byte	30
	.byte	'Ifx_P_PDISC',0,10,202,5,3
	.word	5751
	.byte	30
	.byte	'Ifx_P_PDR0',0,10,210,5,3
	.word	4720
	.byte	30
	.byte	'Ifx_P_PDR1',0,10,218,5,3
	.word	5036
	.byte	14
	.word	9396
	.byte	30
	.byte	'Ifx_P',0,10,139,6,3
	.word	85734
	.byte	30
	.byte	'IfxPort_InputMode',0,9,89,3
	.word	10009
	.byte	30
	.byte	'IfxPort_OutputIdx',0,9,130,1,3
	.word	10284
	.byte	30
	.byte	'IfxPort_OutputMode',0,9,138,1,3
	.word	10214
	.byte	15,9,144,1,9,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,16
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,16
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,16
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,16
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,16
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,16
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,16
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,16
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,16
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,30
	.byte	'IfxPort_PadDriver',0,9,158,1,3
	.word	85835
	.byte	30
	.byte	'IfxPort_State',0,9,178,1,3
	.word	10597
	.byte	29,9,190,1,9,8,13
	.byte	'port',0
	.word	10004
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	519
	.byte	1,2,35,4,0,30
	.byte	'IfxPort_Pin',0,9,194,1,3
	.word	86300
	.byte	30
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,13,148,1,16
	.word	235
	.byte	29,13,212,5,9,8,13
	.byte	'value',0
	.word	2020
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2020
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_CcuconRegConfig',0,13,216,5,3
	.word	86400
	.byte	29,13,221,5,9,8,13
	.byte	'pDivider',0
	.word	519
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	519
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	519
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_InitialStepConfig',0,13,227,5,3
	.word	86471
	.byte	29,13,231,5,9,12,13
	.byte	'k2Step',0
	.word	519
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	292
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	86360
	.byte	4,2,35,8,0,30
	.byte	'IfxScuCcu_PllStepsConfig',0,13,236,5,3
	.word	86588
	.byte	3
	.word	232
	.byte	29,13,244,5,9,48,13
	.byte	'ccucon0',0
	.word	86400
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	86400
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	86400
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	86400
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	86400
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	86400
	.byte	8,2,35,40,0,30
	.byte	'IfxScuCcu_ClockDistributionConfig',0,13,252,5,3
	.word	86690
	.byte	29,13,128,6,9,8,13
	.byte	'value',0
	.word	2020
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	2020
	.byte	4,2,35,4,0,30
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,13,132,6,3
	.word	86842
	.byte	3
	.word	86588
	.byte	29,13,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	519
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	86918
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	86471
	.byte	8,2,35,8,0,30
	.byte	'IfxScuCcu_SysPllConfig',0,13,142,6,3
	.word	86923
	.byte	29,24,59,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18832
	.byte	1,2,35,12,0,28
	.word	87040
	.byte	30
	.byte	'IfxQspi_Mrst_In',0,24,64,3
	.word	87091
	.byte	29,24,67,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18832
	.byte	1,2,35,12,0,28
	.word	87120
	.byte	30
	.byte	'IfxQspi_Mtsr_In',0,24,72,3
	.word	87171
	.byte	29,24,75,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18832
	.byte	1,2,35,12,0,28
	.word	87200
	.byte	30
	.byte	'IfxQspi_Sclk_In',0,24,80,3
	.word	87251
	.byte	29,24,83,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	18832
	.byte	1,2,35,12,0,28
	.word	87280
	.byte	30
	.byte	'IfxQspi_Slsi_In',0,24,88,3
	.word	87331
	.byte	29,24,99,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10284
	.byte	1,2,35,12,0,28
	.word	87360
	.byte	30
	.byte	'IfxQspi_Mrst_Out',0,24,104,3
	.word	87411
	.byte	29,24,107,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10284
	.byte	1,2,35,12,0,28
	.word	87441
	.byte	30
	.byte	'IfxQspi_Mtsr_Out',0,24,112,3
	.word	87492
	.byte	29,24,115,15,16,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	10284
	.byte	1,2,35,12,0,28
	.word	87522
	.byte	30
	.byte	'IfxQspi_Sclk_Out',0,24,120,3
	.word	87573
	.byte	29,24,123,15,20,13
	.byte	'module',0
	.word	15678
	.byte	4,2,35,0,13
	.byte	'slsoNr',0
	.word	17498
	.byte	4,2,35,4,13
	.byte	'pin',0
	.word	86300
	.byte	8,2,35,8,13
	.byte	'select',0
	.word	10284
	.byte	1,2,35,16,0,28
	.word	87603
	.byte	30
	.byte	'IfxQspi_Slso_Out',0,24,129,1,3
	.word	87670
	.byte	30
	.byte	'IfxQspi_ChannelId',0,11,112,3
	.word	16559
	.byte	15,11,162,1,9,1,16
	.byte	'IfxQspi_Mode_master',0,0,16
	.byte	'IfxQspi_Mode_pwmOverQspi',0,1,16
	.byte	'IfxQspi_Mode_slave',0,2,0,30
	.byte	'IfxQspi_Mode',0,11,167,1,3
	.word	87727
	.byte	30
	.byte	'IfxQspi_Phase',0,11,190,1,3
	.word	15738
	.byte	15,11,194,1,9,1,16
	.byte	'IfxQspi_PhaseTransitionEvent_endOfWait',0,0,16
	.byte	'IfxQspi_PhaseTransitionEvent_serialClockPolarityChange',0,1,16
	.byte	'IfxQspi_PhaseTransitionEvent_startOfFrame',0,2,16
	.byte	'IfxQspi_PhaseTransitionEvent_transmitBufferEmptied',0,3,16
	.byte	'IfxQspi_PhaseTransitionEvent_receiveBufferFilled',0,4,16
	.byte	'IfxQspi_PhaseTransitionEvent_endOfFrame',0,5,16
	.byte	'IfxQspi_PhaseTransitionEvent_dataNotAvailable',0,6,16
	.byte	'IfxQspi_PhaseTransitionEvent_endOfExpect',0,7,0,30
	.byte	'IfxQspi_PhaseTransitionEvent',0,11,204,1,3
	.word	87849
	.byte	15,11,208,1,9,1,16
	.byte	'IfxQspi_Reset_none',0,0,16
	.byte	'IfxQspi_Reset_stateMachineAndFifo',0,7,16
	.byte	'IfxQspi_Reset_kernel',0,15,0,30
	.byte	'IfxQspi_Reset',0,11,213,1,3
	.word	88273
	.byte	15,11,217,1,9,1,16
	.byte	'IfxQspi_RxFifoInt_0',0,0,16
	.byte	'IfxQspi_RxFifoInt_1',0,1,16
	.byte	'IfxQspi_RxFifoInt_2',0,2,16
	.byte	'IfxQspi_RxFifoInt_3',0,3,0,30
	.byte	'IfxQspi_RxFifoInt',0,11,223,1,3
	.word	88383
	.byte	15,11,228,1,9,1,16
	.byte	'IfxQspi_SleepMode_enable',0,0,16
	.byte	'IfxQspi_SleepMode_disable',0,1,0,30
	.byte	'IfxQspi_SleepMode',0,11,232,1,3
	.word	88505
	.byte	15,11,146,2,9,1,16
	.byte	'IfxQspi_TxFifoInt_1',0,0,16
	.byte	'IfxQspi_TxFifoInt_2',0,1,16
	.byte	'IfxQspi_TxFifoInt_3',0,2,16
	.byte	'IfxQspi_TxFifoInt_4',0,3,0,30
	.byte	'IfxQspi_TxFifoInt',0,11,152,2,3
	.word	88594
	.byte	15,11,158,2,9,1,16
	.byte	'IfxQspi_FifoMode_combinedMove',0,0,16
	.byte	'IfxQspi_FifoMode_singleMove',0,1,16
	.byte	'IfxQspi_FifoMode_batchMove',0,2,0,30
	.byte	'IfxQspi_FifoMode',0,11,163,2,3
	.word	88716
	.byte	15,11,167,2,9,1,16
	.byte	'IfxQspi_SuspendMode_none',0,0,16
	.byte	'IfxQspi_SuspendMode_hard',0,1,16
	.byte	'IfxQspi_SuspendMode_soft',0,2,0,30
	.byte	'IfxQspi_SuspendMode',0,11,172,2,3
	.word	88840
	.byte	30
	.byte	'IfxQspi_DelayConst',0,11,185,2,3
	.word	17277
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L143:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0
	.byte	17,11,1,0,0,18,1,1,11,15,73,19,0,0,19,33,0,47,15,0,0,20,46,1,3,8,73,19,54,15,39,12,63,12,60,12,0,0,21
	.byte	5,0,73,19,0,0,22,46,1,49,19,0,0,23,5,0,49,19,0,0,24,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12
	.byte	0,0,25,46,0,3,8,58,15,59,15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,26,29,1,49,19,0,0,27,11,0,49,19,0
	.byte	0,28,38,0,73,19,0,0,29,19,1,58,15,59,15,57,15,11,15,0,0,30,22,0,3,8,58,15,59,15,57,15,73,19,0,0,31,21
	.byte	1,54,15,39,12,0,0,32,21,1,73,19,54,15,39,12,0,0,33,21,0,54,15,0,0,34,52,0,3,8,58,15,59,15,57,15,73,19
	.byte	63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L144:
	.word	.L540-.L539
.L539:
	.half	3
	.word	.L542-.L541
.L541:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Src\\Std\\IfxSrc.h',0
	.byte	0,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi\\Std\\IfxQspi.h',0,0,0,0
	.byte	'IfxQspi_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\If\\SpiIf.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxQspi_cfg.h',0
	.byte	0,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxSrc_cfg.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_PinMap\\IfxQspi_PinMap.h',0
	.byte	0,0,0,0
.L542:
.L540:
	.sdecl	'.debug_info',debug,cluster('IfxQspi_read16')
	.sect	'.debug_info'
.L145:
	.word	359
	.half	3
	.word	.L146
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L148,.L147
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_read16',0,1,186,2,6,1,1,1
	.word	.L122,.L230,.L121
	.byte	4
	.byte	'qspi',0,1,186,2,31
	.word	.L231,.L232
	.byte	4
	.byte	'data',0,1,186,2,45
	.word	.L233,.L234
	.byte	4
	.byte	'count',0,1,186,2,61
	.word	.L235,.L236
	.byte	5
	.word	.L122,.L230
	.byte	6
	.byte	'rxFifo',0,1,188,2,31
	.word	.L237,.L238
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_read16')
	.sect	'.debug_abbrev'
.L146:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_read16')
	.sect	'.debug_line'
.L147:
	.word	.L544-.L543
.L543:
	.half	3
	.word	.L546-.L545
.L545:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L546:
	.byte	5,45,7,0,5,2
	.word	.L122
	.byte	3,187,2,1,5,21,9
	.half	.L496-.L122
	.byte	3,2,1,5,35,9
	.half	.L78-.L496
	.byte	3,2,1,5,19,9
	.half	.L547-.L78
	.byte	1,5,15,9
	.half	.L548-.L547
	.byte	1,5,14,9
	.half	.L549-.L548
	.byte	3,1,1,5,21,9
	.half	.L77-.L549
	.byte	3,125,1,5,1,7,9
	.half	.L550-.L77
	.byte	3,5,1,7,9
	.half	.L149-.L550
	.byte	0,1,1
.L544:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_read16')
	.sect	'.debug_ranges'
.L148:
	.word	-1,.L122,0,.L149-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_read32')
	.sect	'.debug_info'
.L150:
	.word	359
	.half	3
	.word	.L151
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L153,.L152
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_read32',0,1,198,2,6,1,1,1
	.word	.L124,.L239,.L123
	.byte	4
	.byte	'qspi',0,1,198,2,31
	.word	.L231,.L240
	.byte	4
	.byte	'data',0,1,198,2,45
	.word	.L241,.L242
	.byte	4
	.byte	'count',0,1,198,2,61
	.word	.L235,.L243
	.byte	5
	.word	.L124,.L239
	.byte	6
	.byte	'rxFifo',0,1,200,2,31
	.word	.L237,.L244
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_read32')
	.sect	'.debug_abbrev'
.L151:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_read32')
	.sect	'.debug_line'
.L152:
	.word	.L552-.L551
.L551:
	.half	3
	.word	.L554-.L553
.L553:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L554:
	.byte	5,45,7,0,5,2
	.word	.L124
	.byte	3,199,2,1,5,21,9
	.half	.L497-.L124
	.byte	3,2,1,5,27,9
	.half	.L80-.L497
	.byte	3,2,1,5,19,9
	.half	.L555-.L80
	.byte	1,5,15,9
	.half	.L556-.L555
	.byte	1,5,14,9
	.half	.L557-.L556
	.byte	3,1,1,5,21,9
	.half	.L79-.L557
	.byte	3,125,1,5,1,7,9
	.half	.L558-.L79
	.byte	3,5,1,7,9
	.half	.L154-.L558
	.byte	0,1,1
.L552:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_read32')
	.sect	'.debug_ranges'
.L153:
	.word	-1,.L124,0,.L154-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_read8')
	.sect	'.debug_info'
.L155:
	.word	358
	.half	3
	.word	.L156
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L158,.L157
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_read8',0,1,210,2,6,1,1,1
	.word	.L126,.L245,.L125
	.byte	4
	.byte	'qspi',0,1,210,2,30
	.word	.L231,.L246
	.byte	4
	.byte	'data',0,1,210,2,43
	.word	.L247,.L248
	.byte	4
	.byte	'count',0,1,210,2,59
	.word	.L235,.L249
	.byte	5
	.word	.L126,.L245
	.byte	6
	.byte	'rxFifo',0,1,212,2,31
	.word	.L237,.L250
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_read8')
	.sect	'.debug_abbrev'
.L156:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_read8')
	.sect	'.debug_line'
.L157:
	.word	.L560-.L559
.L559:
	.half	3
	.word	.L562-.L561
.L561:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L562:
	.byte	5,45,7,0,5,2
	.word	.L126
	.byte	3,211,2,1,5,21,9
	.half	.L498-.L126
	.byte	3,2,1,5,34,9
	.half	.L82-.L498
	.byte	3,2,1,5,19,9
	.half	.L563-.L82
	.byte	1,5,15,9
	.half	.L564-.L563
	.byte	1,5,14,9
	.half	.L565-.L564
	.byte	3,1,1,5,21,9
	.half	.L81-.L565
	.byte	3,125,1,5,1,7,9
	.half	.L566-.L81
	.byte	3,5,1,7,9
	.half	.L159-.L566
	.byte	0,1,1
.L560:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_read8')
	.sect	'.debug_ranges'
.L158:
	.word	-1,.L126,0,.L159-.L126,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_info'
.L160:
	.word	327
	.half	3
	.word	.L161
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L163,.L162
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_resetModule',0,1,238,2,6,1,1,1
	.word	.L130,.L251,.L129
	.byte	4
	.byte	'qspi',0,1,238,2,36
	.word	.L231,.L252
	.byte	5
	.word	.L130,.L251
	.byte	6
	.byte	'passwd',0,1,240,2,12
	.word	.L253,.L254
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_abbrev'
.L161:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_line'
.L162:
	.word	.L568-.L567
.L567:
	.half	3
	.word	.L570-.L569
.L569:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L570:
	.byte	5,6,7,0,5,2
	.word	.L130
	.byte	3,237,2,1,5,53,9
	.half	.L501-.L130
	.byte	3,2,1,5,19,9
	.half	.L500-.L501
	.byte	1,5,31,9
	.half	.L503-.L500
	.byte	3,1,1,5,18,9
	.half	.L504-.L503
	.byte	3,1,1,5,23,9
	.half	.L571-.L504
	.byte	1,5,18,9
	.half	.L572-.L571
	.byte	3,1,1,5,23,9
	.half	.L573-.L572
	.byte	1,5,29,9
	.half	.L574-.L573
	.byte	3,1,1,5,38,9
	.half	.L506-.L574
	.byte	3,2,1,5,30,9
	.half	.L85-.L506
	.byte	1,5,38,9
	.half	.L575-.L85
	.byte	1,5,31,7,9
	.half	.L576-.L575
	.byte	3,4,1,5,20,9
	.half	.L508-.L576
	.byte	3,1,1,5,25,9
	.half	.L577-.L508
	.byte	1,5,29,9
	.half	.L578-.L577
	.byte	3,1,1,5,1,9
	.half	.L510-.L578
	.byte	3,1,1,7,9
	.half	.L164-.L510
	.byte	0,1,1
.L568:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_ranges'
.L163:
	.word	-1,.L130,0,.L164-.L130,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_write16')
	.sect	'.debug_info'
.L165:
	.word	402
	.half	3
	.word	.L166
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L168,.L167
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_write16',0,1,157,3,6,1,1,1
	.word	.L134,.L255,.L133
	.byte	4
	.byte	'qspi',0,1,157,3,32
	.word	.L231,.L256
	.byte	4
	.byte	'channelId',0,1,157,3,56
	.word	.L257,.L258
	.byte	4
	.byte	'data',0,1,157,3,75
	.word	.L233,.L259
	.byte	4
	.byte	'count',0,1,157,3,91
	.word	.L235,.L260
	.byte	5
	.word	.L134,.L255
	.byte	6
	.byte	'cs',0,1,159,3,34
	.word	.L261,.L262
	.byte	6
	.byte	'dataEntry',0,1,160,3,34
	.word	.L263,.L264
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_write16')
	.sect	'.debug_abbrev'
.L166:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_write16')
	.sect	'.debug_line'
.L167:
	.word	.L580-.L579
.L579:
	.half	3
	.word	.L582-.L581
.L581:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L582:
	.byte	5,58,7,0,5,2
	.word	.L134
	.byte	3,158,3,1,5,56,9
	.half	.L583-.L134
	.byte	1,5,62,9
	.half	.L514-.L583
	.byte	3,1,1,5,21,9
	.half	.L515-.L514
	.byte	3,2,1,5,24,9
	.half	.L92-.L515
	.byte	3,2,1,5,22,9
	.half	.L584-.L92
	.byte	1,5,30,9
	.half	.L585-.L584
	.byte	1,5,14,9
	.half	.L586-.L585
	.byte	3,1,1,5,21,9
	.half	.L91-.L586
	.byte	3,125,1,5,1,7,9
	.half	.L587-.L91
	.byte	3,5,1,7,9
	.half	.L169-.L587
	.byte	0,1,1
.L580:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_write16')
	.sect	'.debug_ranges'
.L168:
	.word	-1,.L134,0,.L169-.L134,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_write32')
	.sect	'.debug_info'
.L170:
	.word	402
	.half	3
	.word	.L171
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L173,.L172
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_write32',0,1,170,3,6,1,1,1
	.word	.L136,.L265,.L135
	.byte	4
	.byte	'qspi',0,1,170,3,32
	.word	.L231,.L266
	.byte	4
	.byte	'channelId',0,1,170,3,56
	.word	.L257,.L267
	.byte	4
	.byte	'data',0,1,170,3,75
	.word	.L241,.L268
	.byte	4
	.byte	'count',0,1,170,3,91
	.word	.L235,.L269
	.byte	5
	.word	.L136,.L265
	.byte	6
	.byte	'cs',0,1,172,3,34
	.word	.L261,.L270
	.byte	6
	.byte	'dataEntry',0,1,173,3,34
	.word	.L263,.L271
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_write32')
	.sect	'.debug_abbrev'
.L171:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_write32')
	.sect	'.debug_line'
.L172:
	.word	.L589-.L588
.L588:
	.half	3
	.word	.L591-.L590
.L590:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L591:
	.byte	5,58,7,0,5,2
	.word	.L136
	.byte	3,171,3,1,5,56,9
	.half	.L592-.L136
	.byte	1,5,62,9
	.half	.L516-.L592
	.byte	3,1,1,5,21,9
	.half	.L517-.L516
	.byte	3,2,1,5,24,9
	.half	.L94-.L517
	.byte	3,2,1,5,22,9
	.half	.L593-.L94
	.byte	1,5,30,9
	.half	.L594-.L593
	.byte	1,5,14,9
	.half	.L595-.L594
	.byte	3,1,1,5,21,9
	.half	.L93-.L595
	.byte	3,125,1,5,1,7,9
	.half	.L596-.L93
	.byte	3,5,1,7,9
	.half	.L174-.L596
	.byte	0,1,1
.L589:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_write32')
	.sect	'.debug_ranges'
.L173:
	.word	-1,.L136,0,.L174-.L136,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_write8')
	.sect	'.debug_info'
.L175:
	.word	401
	.half	3
	.word	.L176
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L178,.L177
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_write8',0,1,183,3,6,1,1,1
	.word	.L138,.L272,.L137
	.byte	4
	.byte	'qspi',0,1,183,3,31
	.word	.L231,.L273
	.byte	4
	.byte	'channelId',0,1,183,3,55
	.word	.L257,.L274
	.byte	4
	.byte	'data',0,1,183,3,73
	.word	.L247,.L275
	.byte	4
	.byte	'count',0,1,183,3,89
	.word	.L235,.L276
	.byte	5
	.word	.L138,.L272
	.byte	6
	.byte	'cs',0,1,185,3,34
	.word	.L261,.L277
	.byte	6
	.byte	'dataEntry',0,1,186,3,34
	.word	.L263,.L278
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_write8')
	.sect	'.debug_abbrev'
.L176:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_write8')
	.sect	'.debug_line'
.L177:
	.word	.L598-.L597
.L597:
	.half	3
	.word	.L600-.L599
.L599:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L600:
	.byte	5,58,7,0,5,2
	.word	.L138
	.byte	3,184,3,1,5,56,9
	.half	.L601-.L138
	.byte	1,5,62,9
	.half	.L518-.L601
	.byte	3,1,1,5,21,9
	.half	.L519-.L518
	.byte	3,2,1,5,24,9
	.half	.L96-.L519
	.byte	3,2,1,5,22,9
	.half	.L602-.L96
	.byte	1,5,30,9
	.half	.L603-.L602
	.byte	1,5,14,9
	.half	.L604-.L603
	.byte	3,1,1,5,21,9
	.half	.L95-.L604
	.byte	3,125,1,5,1,7,9
	.half	.L605-.L95
	.byte	3,5,1,7,9
	.half	.L179-.L605
	.byte	0,1,1
.L598:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_write8')
	.sect	'.debug_ranges'
.L178:
	.word	-1,.L138,0,.L179-.L138,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_info'
.L180:
	.word	386
	.half	3
	.word	.L181
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L183,.L182
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calcRealBaudrate',0,1,55,7
	.word	.L279
	.byte	1,1,1
	.word	.L108,.L280,.L107
	.byte	4
	.byte	'qspi',0,1,55,42
	.word	.L231,.L281
	.byte	4
	.byte	'channelId',0,1,55,66
	.word	.L257,.L282
	.byte	5
	.word	.L108,.L280
	.byte	6
	.byte	'cs',0,1,57,19
	.word	.L261,.L283
	.byte	6
	.byte	'fQspi',0,1,58,19
	.word	.L279,.L284
	.byte	6
	.byte	'econ',0,1,59,19
	.word	.L285,.L286
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_abbrev'
.L181:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_line'
.L182:
	.word	.L607-.L606
.L606:
	.half	3
	.word	.L609-.L608
.L608:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L609:
	.byte	5,7,7,0,5,2
	.word	.L108
	.byte	3,54,1,5,39,9
	.half	.L408-.L108
	.byte	3,2,1,5,37,9
	.half	.L610-.L408
	.byte	1,5,52,9
	.half	.L409-.L610
	.byte	3,1,1,5,9,9
	.half	.L407-.L409
	.byte	3,2,1,5,28,9
	.half	.L611-.L407
	.byte	1,5,32,9
	.half	.L612-.L611
	.byte	1,5,16,9
	.half	.L613-.L612
	.byte	1,5,44,9
	.half	.L614-.L613
	.byte	3,1,1,5,48,9
	.half	.L615-.L614
	.byte	1,5,24,9
	.half	.L616-.L615
	.byte	1,5,31,9
	.half	.L410-.L616
	.byte	3,1,1,5,37,9
	.half	.L617-.L410
	.byte	1,5,40,9
	.half	.L618-.L617
	.byte	1,5,24,9
	.half	.L619-.L618
	.byte	1,5,32,9
	.half	.L620-.L619
	.byte	3,1,1,5,38,9
	.half	.L621-.L620
	.byte	1,5,41,9
	.half	.L622-.L621
	.byte	1,5,52,9
	.half	.L623-.L622
	.byte	1,5,58,9
	.half	.L624-.L623
	.byte	1,5,46,9
	.half	.L625-.L624
	.byte	1,5,67,9
	.half	.L626-.L625
	.byte	1,5,73,9
	.half	.L627-.L626
	.byte	1,5,61,9
	.half	.L628-.L627
	.byte	1,5,24,9
	.half	.L629-.L628
	.byte	1,5,5,9
	.half	.L411-.L629
	.byte	3,1,1,5,1,9
	.half	.L2-.L411
	.byte	3,1,1,7,9
	.half	.L184-.L2
	.byte	0,1,1
.L607:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_ranges'
.L183:
	.word	-1,.L108,0,.L184-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_info'
.L185:
	.word	444
	.half	3
	.word	.L186
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L188,.L187
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calculateBasicConfigurationValue',0,1,68,8
	.word	.L287
	.byte	1,1,1
	.word	.L110,.L288,.L109
	.byte	4
	.byte	'qspi',0,1,68,59
	.word	.L231,.L289
	.byte	4
	.byte	'channelId',0,1,68,89
	.word	.L290,.L291
	.byte	4
	.byte	'chMode',0,1,68,120
	.word	.L292,.L293
	.byte	4
	.byte	'baudrate',0,1,68,140,1
	.word	.L294,.L295
	.byte	5
	.word	.L110,.L288
	.byte	5
	.word	.L3,.L288
	.byte	6
	.byte	'bacon',0,1,72,24
	.word	.L296,.L297
	.byte	6
	.byte	'delayConst',0,1,73,24
	.word	.L298,.L299
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_abbrev'
.L186:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_line'
.L187:
	.word	.L631-.L630
.L630:
	.half	3
	.word	.L633-.L632
.L632:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L633:
	.byte	5,8,7,0,5,2
	.word	.L110
	.byte	3,195,0,1,5,5,9
	.half	.L415-.L110
	.byte	3,2,1,5,13,9
	.half	.L3-.L415
	.byte	3,4,1,5,62,9
	.half	.L416-.L3
	.byte	3,2,1,5,22,9
	.half	.L413-.L416
	.byte	3,2,1,5,20,9
	.half	.L634-.L413
	.byte	1,5,35,9
	.half	.L635-.L634
	.byte	3,2,1,5,20,9
	.half	.L636-.L635
	.byte	1,5,35,9
	.half	.L637-.L636
	.byte	3,1,1,5,20,9
	.half	.L638-.L637
	.byte	1,5,35,9
	.half	.L639-.L638
	.byte	3,1,1,5,20,9
	.half	.L640-.L639
	.byte	1,5,35,9
	.half	.L641-.L640
	.byte	3,1,1,5,20,9
	.half	.L642-.L641
	.byte	1,5,35,9
	.half	.L643-.L642
	.byte	3,1,1,5,20,9
	.half	.L644-.L643
	.byte	1,5,35,9
	.half	.L645-.L644
	.byte	3,1,1,5,20,9
	.half	.L646-.L645
	.byte	1,5,29,9
	.half	.L647-.L646
	.byte	3,1,1,5,22,9
	.half	.L648-.L647
	.byte	1,5,66,7,9
	.half	.L649-.L648
	.byte	1,5,70,9
	.half	.L650-.L649
	.byte	1,5,66,9
	.half	.L4-.L650
	.byte	1,5,20,9
	.half	.L5-.L4
	.byte	1,5,22,9
	.half	.L651-.L5
	.byte	3,1,1,5,20,9
	.half	.L652-.L651
	.byte	1,5,29,9
	.half	.L653-.L652
	.byte	3,1,1,5,22,9
	.half	.L654-.L653
	.byte	1,5,74,7,9
	.half	.L655-.L654
	.byte	1,5,78,9
	.half	.L656-.L655
	.byte	1,5,74,9
	.half	.L6-.L656
	.byte	1,5,20,9
	.half	.L7-.L6
	.byte	1,5,22,9
	.half	.L657-.L7
	.byte	3,1,1,5,20,9
	.half	.L658-.L657
	.byte	1,5,28,9
	.half	.L659-.L658
	.byte	3,1,1,5,40,9
	.half	.L660-.L659
	.byte	1,5,20,9
	.half	.L661-.L660
	.byte	1,9
	.half	.L662-.L661
	.byte	3,1,1,5,5,9
	.half	.L663-.L662
	.byte	3,2,1,5,1,9
	.half	.L8-.L663
	.byte	3,1,1,7,9
	.half	.L189-.L8
	.byte	0,1,1
.L631:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_ranges'
.L188:
	.word	-1,.L110,0,.L189-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_info'
.L190:
	.word	796
	.half	3
	.word	.L191
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L193,.L192
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calculateExtendedConfigurationValue',0,1,97,8
	.word	.L287
	.byte	1,1,1
	.word	.L112,.L300,.L111
	.byte	4
	.byte	'qspi',0,1,97,62
	.word	.L231,.L301
	.byte	4
	.byte	'cs',0,1,97,80
	.word	.L302,.L303
	.byte	4
	.byte	'chConfig',0,1,97,106
	.word	.L304,.L305
	.byte	5
	.word	.L112,.L300
	.byte	5
	.word	.L9,.L300
	.byte	6
	.byte	'econ',0,1,101,19
	.word	.L306,.L307
	.byte	5
	.word	.L308,.L300
	.byte	6
	.byte	'maxB',0,1,104,19
	.word	.L309,.L310
	.byte	6
	.byte	'tQspi',0,1,105,19
	.word	.L279,.L311
	.byte	6
	.byte	'fBaud',0,1,106,19
	.word	.L279,.L312
	.byte	6
	.byte	'abcMin',0,1,107,19
	.word	.L261,.L313
	.byte	6
	.byte	'abcMax',0,1,108,19
	.word	.L261,.L314
	.byte	6
	.byte	'q',0,1,109,19
	.word	.L261,.L315
	.byte	6
	.byte	'bestQ',0,1,109,22
	.word	.L261,.L316
	.byte	6
	.byte	'abc',0,1,109,33
	.word	.L261,.L317
	.byte	6
	.byte	'bestAbc',0,1,109,38
	.word	.L261,.L318
	.byte	6
	.byte	'halfBaud',0,1,109,56
	.word	.L261,.L319
	.byte	6
	.byte	'diffB',0,1,110,19
	.word	.L320,.L321
	.byte	6
	.byte	'error',0,1,111,19
	.word	.L279,.L322
	.byte	6
	.byte	'bestError',0,1,111,26
	.word	.L279,.L323
	.byte	6
	.byte	'tTmp',0,1,112,19
	.word	.L279,.L324
	.byte	6
	.byte	'tBaudTmp',0,1,112,25
	.word	.L279,.L325
	.byte	6
	.byte	'done',0,1,113,19
	.word	.L326,.L327
	.byte	7
	.word	.L328,.L308,.L12
	.byte	8
	.word	.L329,.L330
	.byte	9
	.word	.L331,.L308,.L12
	.byte	7
	.word	.L332,.L308,.L11
	.byte	8
	.word	.L333,.L334
	.byte	10
	.word	.L335,.L308,.L11
	.byte	0,0,0,11
	.word	.L336
	.byte	6
	.byte	'tBaud',0,1,121,13
	.word	.L279,.L340
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_abbrev'
.L191:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17
	.byte	1,18,1,0,0,10,11,0,49,16,17,1,18,1,0,0,11,11,1,85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_line'
.L192:
	.word	.L665-.L664
.L664:
	.half	3
	.word	.L667-.L666
.L666:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi\\Std\\IfxQspi.h',0,0,0,0,0
.L667:
	.byte	5,8,7,0,5,2
	.word	.L112
	.byte	3,224,0,1,5,5,9
	.half	.L421-.L112
	.byte	3,2,1,5,12,7,9
	.half	.L9-.L421
	.byte	3,3,1,4,2,5,5,9
	.half	.L308-.L9
	.byte	3,239,6,1,5,37,9
	.half	.L10-.L308
	.byte	3,2,1,5,5,9
	.half	.L419-.L10
	.byte	1,5,65,9
	.half	.L11-.L419
	.byte	3,31,1,5,69,9
	.half	.L668-.L11
	.byte	1,5,45,9
	.half	.L669-.L668
	.byte	1,5,5,9
	.half	.L670-.L669
	.byte	1,4,1,5,28,9
	.half	.L12-.L670
	.byte	3,243,120,1,5,64,9
	.half	.L671-.L12
	.byte	1,5,32,9
	.half	.L672-.L671
	.byte	1,5,26,9
	.half	.L424-.L672
	.byte	1,5,37,9
	.half	.L425-.L424
	.byte	3,1,1,5,26,9
	.half	.L426-.L425
	.byte	3,2,1,5,28,9
	.half	.L427-.L426
	.byte	3,1,1,5,9,9
	.half	.L428-.L427
	.byte	3,6,1,5,15,9
	.half	.L431-.L428
	.byte	1,5,5,9
	.half	.L673-.L431
	.byte	1,5,17,7,9
	.half	.L674-.L673
	.byte	3,3,1,5,21,9
	.half	.L13-.L674
	.byte	3,3,1,5,27,9
	.half	.L675-.L13
	.byte	1,5,25,9
	.half	.L676-.L675
	.byte	1,5,19,9
	.half	.L434-.L676
	.byte	1,5,17,9
	.half	.L435-.L434
	.byte	3,2,1,5,26,9
	.half	.L337-.L435
	.byte	3,113,1,5,37,9
	.half	.L338-.L337
	.byte	3,17,1,5,24,9
	.half	.L15-.L338
	.byte	3,2,1,5,22,9
	.half	.L677-.L15
	.byte	1,5,29,9
	.half	.L439-.L677
	.byte	3,1,1,5,39,9
	.half	.L678-.L439
	.byte	1,5,37,9
	.half	.L679-.L678
	.byte	1,5,16,9
	.half	.L680-.L679
	.byte	1,5,14,9
	.half	.L441-.L680
	.byte	1,5,17,9
	.half	.L442-.L441
	.byte	3,2,1,5,9,9
	.half	.L681-.L442
	.byte	1,5,15,7,9
	.half	.L682-.L681
	.byte	3,2,1,5,19,9
	.half	.L683-.L682
	.byte	1,5,21,9
	.half	.L16-.L683
	.byte	3,3,1,5,14,9
	.half	.L684-.L16
	.byte	1,5,15,7,9
	.half	.L685-.L684
	.byte	3,2,1,5,18,9
	.half	.L686-.L685
	.byte	1,5,14,9
	.half	.L18-.L686
	.byte	3,3,1,5,15,7,9
	.half	.L687-.L18
	.byte	3,2,1,5,27,9
	.half	.L17-.L687
	.byte	3,3,1,5,25,9
	.half	.L688-.L17
	.byte	1,5,20,9
	.half	.L440-.L688
	.byte	3,1,1,5,13,9
	.half	.L22-.L440
	.byte	3,2,1,5,17,9
	.half	.L689-.L22
	.byte	3,3,1,5,78,9
	.half	.L444-.L689
	.byte	1,5,27,7,9
	.half	.L24-.L444
	.byte	3,2,1,9
	.half	.L690-.L24
	.byte	3,1,1,9
	.half	.L445-.L690
	.byte	3,1,1,5,13,9
	.half	.L26-.L445
	.byte	3,4,1,5,25,7,9
	.half	.L446-.L26
	.byte	3,2,1,5,45,9
	.half	.L28-.L446
	.byte	1,5,53,9
	.half	.L447-.L28
	.byte	1,5,45,9
	.half	.L29-.L447
	.byte	1,5,17,9
	.half	.L30-.L29
	.byte	3,2,1,5,21,7,9
	.half	.L691-.L30
	.byte	3,2,1,5,42,9
	.half	.L23-.L691
	.byte	3,88,1,5,37,9
	.half	.L14-.L23
	.byte	1,5,26,7,9
	.half	.L32-.L14
	.byte	3,111,1,5,9,9
	.half	.L339-.L32
	.byte	3,195,0,1,5,45,7,9
	.half	.L449-.L339
	.byte	3,1,1,5,43,7,9
	.half	.L450-.L449
	.byte	3,1,1,5,17,7,9
	.half	.L692-.L450
	.byte	3,2,1,9
	.half	.L448-.L692
	.byte	3,1,1,9
	.half	.L451-.L448
	.byte	3,1,1,5,30,9
	.half	.L33-.L451
	.byte	3,13,1,5,28,9
	.half	.L452-.L33
	.byte	1,5,29,9
	.half	.L453-.L452
	.byte	3,1,1,5,26,9
	.half	.L455-.L453
	.byte	3,2,1,5,18,9
	.half	.L693-.L455
	.byte	1,5,42,9
	.half	.L694-.L693
	.byte	3,1,1,5,40,9
	.half	.L457-.L694
	.byte	1,5,29,9
	.half	.L458-.L457
	.byte	1,5,45,9
	.half	.L695-.L458
	.byte	1,5,18,9
	.half	.L696-.L695
	.byte	1,5,20,9
	.half	.L697-.L696
	.byte	3,1,1,5,32,7,9
	.half	.L698-.L697
	.byte	1,5,40,9
	.half	.L699-.L698
	.byte	1,5,32,9
	.half	.L36-.L699
	.byte	1,5,18,9
	.half	.L37-.L36
	.byte	1,5,20,9
	.half	.L700-.L37
	.byte	3,1,1,5,32,7,9
	.half	.L701-.L700
	.byte	1,5,39,9
	.half	.L702-.L701
	.byte	1,5,32,9
	.half	.L39-.L702
	.byte	1,5,18,9
	.half	.L703-.L39
	.byte	1,5,35,9
	.half	.L704-.L703
	.byte	3,2,1,5,20,9
	.half	.L460-.L704
	.byte	1,5,99,7,9
	.half	.L705-.L460
	.byte	1,5,103,9
	.half	.L706-.L705
	.byte	1,5,99,9
	.half	.L40-.L706
	.byte	1,5,18,9
	.half	.L41-.L40
	.byte	1,5,35,9
	.half	.L707-.L41
	.byte	3,1,1,5,20,9
	.half	.L462-.L707
	.byte	1,5,82,7,9
	.half	.L708-.L462
	.byte	1,5,86,9
	.half	.L709-.L708
	.byte	1,5,82,9
	.half	.L42-.L709
	.byte	1,5,18,9
	.half	.L43-.L42
	.byte	1,5,34,9
	.half	.L710-.L43
	.byte	3,1,1,5,18,9
	.half	.L711-.L710
	.byte	1,5,5,9
	.half	.L712-.L711
	.byte	3,2,1,5,1,9
	.half	.L44-.L712
	.byte	3,1,1,7,9
	.half	.L194-.L44
	.byte	0,1,1
.L665:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_ranges'
.L193:
	.word	-1,.L112,0,.L194-.L112,0,0
.L336:
	.word	-1,.L112,.L13-.L112,.L337-.L112,.L338-.L112,.L32-.L112,.L339-.L112,.L300-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_info'
.L195:
	.word	543
	.half	3
	.word	.L196
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L198,.L197
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calculatePrescaler',0,1,210,1,8
	.word	.L287
	.byte	1,1,1
	.word	.L114,.L341,.L113
	.byte	4
	.byte	'qspi',0,1,210,1,45
	.word	.L231,.L342
	.byte	4
	.byte	'baudrate',0,1,210,1,57
	.word	.L279,.L343
	.byte	5
	.word	.L114,.L341
	.byte	6
	.byte	'error',0,1,212,1,12
	.word	.L279,.L344
	.byte	6
	.byte	'bestError',0,1,212,1,19
	.word	.L279,.L345
	.byte	6
	.byte	'halfBaud',0,1,213,1,12
	.word	.L279,.L346
	.byte	6
	.byte	'fQspiIn',0,1,214,1,12
	.word	.L279,.L347
	.byte	7
	.word	.L332,.L348,.L46
	.byte	8
	.word	.L333,.L349
	.byte	9
	.word	.L335,.L348,.L46
	.byte	0,5
	.word	.L350,.L341
	.byte	6
	.byte	'i',0,1,216,1,12
	.word	.L287,.L351
	.byte	6
	.byte	'bestPre',0,1,216,1,15
	.word	.L287,.L352
	.byte	5
	.word	.L48,.L51
	.byte	6
	.byte	'tempHalfBaud',0,1,220,1,15
	.word	.L279,.L353
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_abbrev'
.L196:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,0,49,16,17
	.byte	1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_line'
.L197:
	.word	.L714-.L713
.L713:
	.half	3
	.word	.L716-.L715
.L715:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Qspi\\Std\\IfxQspi.h',0,0,0,0,0
.L716:
	.byte	5,34,7,0,5,2
	.word	.L114
	.byte	3,212,1,1,5,32,9
	.half	.L717-.L114
	.byte	1,4,2,5,5,9
	.half	.L348-.L717
	.byte	3,128,6,1,5,37,9
	.half	.L45-.L348
	.byte	3,2,1,5,5,9
	.half	.L465-.L45
	.byte	1,4,1,5,17,9
	.half	.L46-.L465
	.byte	3,128,122,1,5,23,9
	.half	.L350-.L46
	.byte	3,1,1,5,12,9
	.half	.L467-.L350
	.byte	3,2,1,5,22,9
	.half	.L468-.L467
	.byte	1,5,41,9
	.half	.L48-.L468
	.byte	3,2,1,5,50,9
	.half	.L718-.L48
	.byte	1,5,44,9
	.half	.L719-.L718
	.byte	1,5,38,9
	.half	.L720-.L719
	.byte	1,5,17,9
	.half	.L469-.L720
	.byte	3,1,1,5,13,9
	.half	.L50-.L469
	.byte	3,2,1,5,23,9
	.half	.L721-.L50
	.byte	3,2,1,9
	.half	.L722-.L721
	.byte	3,1,1,5,25,9
	.half	.L51-.L722
	.byte	3,120,1,5,22,9
	.half	.L47-.L51
	.byte	1,5,5,7,9
	.half	.L723-.L47
	.byte	3,12,1,5,1,9
	.half	.L52-.L723
	.byte	3,1,1,7,9
	.half	.L199-.L52
	.byte	0,1,1
.L714:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_ranges'
.L198:
	.word	-1,.L114,0,.L199-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_info'
.L200:
	.word	524
	.half	3
	.word	.L201
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L203,.L202
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calculateTimeQuantumLength',0,1,234,1,8
	.word	.L287
	.byte	1,1,1
	.word	.L116,.L354,.L115
	.byte	4
	.byte	'qspi',0,1,234,1,53
	.word	.L231,.L355
	.byte	4
	.byte	'maxBaudrate',0,1,234,1,65
	.word	.L279,.L356
	.byte	5
	.word	.L116,.L354
	.byte	5
	.word	.L53,.L354
	.byte	6
	.byte	'abcq',0,1,241,1,12
	.word	.L287,.L357
	.byte	6
	.byte	'tq',0,1,241,1,29
	.word	.L287,.L358
	.byte	6
	.byte	'bestTq',0,1,241,1,33
	.word	.L287,.L359
	.byte	6
	.byte	'realTQ',0,1,242,1,12
	.word	.L279,.L360
	.byte	6
	.byte	'deltaMax',0,1,242,1,20
	.word	.L279,.L361
	.byte	6
	.byte	'bestDelta',0,1,242,1,30
	.word	.L279,.L362
	.byte	6
	.byte	'achievedMax',0,1,242,1,41
	.word	.L279,.L363
	.byte	6
	.byte	'fQspi',0,1,243,1,12
	.word	.L279,.L364
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_abbrev'
.L201:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_line'
.L202:
	.word	.L725-.L724
.L724:
	.half	3
	.word	.L727-.L726
.L726:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L727:
	.byte	5,8,7,0,5,2
	.word	.L116
	.byte	3,233,1,1,5,5,9
	.half	.L473-.L116
	.byte	3,5,1,5,45,7,9
	.half	.L53-.L473
	.byte	3,4,1,5,18,9
	.half	.L472-.L53
	.byte	1,5,9,9
	.half	.L475-.L472
	.byte	3,2,1,5,17,9
	.half	.L54-.L475
	.byte	3,5,1,5,32,9
	.half	.L728-.L54
	.byte	1,5,26,9
	.half	.L729-.L728
	.byte	1,5,30,9
	.half	.L730-.L729
	.byte	1,5,23,9
	.half	.L731-.L730
	.byte	1,5,15,9
	.half	.L481-.L731
	.byte	1,5,31,9
	.half	.L482-.L481
	.byte	3,1,1,5,22,9
	.half	.L56-.L482
	.byte	1,5,17,9
	.half	.L484-.L56
	.byte	3,1,1,5,15,9
	.half	.L58-.L484
	.byte	3,2,1,5,41,9
	.half	.L486-.L58
	.byte	1,5,46,9
	.half	.L60-.L486
	.byte	3,2,1,5,44,9
	.half	.L732-.L60
	.byte	1,5,29,9
	.half	.L733-.L732
	.byte	1,5,32,9
	.half	.L487-.L733
	.byte	3,1,1,5,41,9
	.half	.L734-.L487
	.byte	1,5,39,9
	.half	.L735-.L734
	.byte	1,5,23,9
	.half	.L736-.L735
	.byte	1,5,21,9
	.half	.L489-.L736
	.byte	1,5,35,9
	.half	.L490-.L489
	.byte	3,1,1,5,29,9
	.half	.L737-.L490
	.byte	1,5,23,9
	.half	.L491-.L737
	.byte	3,1,1,5,13,9
	.half	.L62-.L491
	.byte	3,2,1,5,48,9
	.half	.L738-.L62
	.byte	1,5,23,7,9
	.half	.L739-.L738
	.byte	3,2,1,9
	.half	.L740-.L739
	.byte	3,1,1,5,13,9
	.half	.L63-.L740
	.byte	3,3,1,5,37,9
	.half	.L741-.L63
	.byte	1,5,13,7,9
	.half	.L65-.L741
	.byte	3,2,1,5,47,9
	.half	.L66-.L65
	.byte	3,113,1,5,34,9
	.half	.L59-.L66
	.byte	1,5,41,9
	.half	.L742-.L59
	.byte	1,5,25,7,9
	.half	.L67-.L742
	.byte	3,19,1,5,17,9
	.half	.L485-.L67
	.byte	1,5,5,9
	.half	.L743-.L485
	.byte	1,5,1,9
	.half	.L68-.L743
	.byte	3,1,1,7,9
	.half	.L204-.L68
	.byte	0,1,1
.L725:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_ranges'
.L203:
	.word	-1,.L116,0,.L204-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_info'
.L205:
	.word	330
	.half	3
	.word	.L206
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L208,.L207
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_getAddress',0,1,149,2,11
	.word	.L231
	.byte	1,1,1
	.word	.L118,.L365,.L117
	.byte	4
	.byte	'qspi',0,1,149,2,44
	.word	.L366,.L367
	.byte	5
	.word	.L118,.L365
	.byte	6
	.byte	'module',0,1,151,2,15
	.word	.L231,.L368
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_abbrev'
.L206:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_line'
.L207:
	.word	.L745-.L744
.L744:
	.half	3
	.word	.L747-.L746
.L746:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L747:
	.byte	5,5,7,0,5,2
	.word	.L118
	.byte	3,152,2,1,5,50,7,9
	.half	.L748-.L118
	.byte	3,2,1,5,30,9
	.half	.L749-.L748
	.byte	1,5,50,9
	.half	.L750-.L749
	.byte	1,5,56,9
	.half	.L751-.L750
	.byte	1,5,63,9
	.half	.L493-.L751
	.byte	1,5,16,9
	.half	.L69-.L493
	.byte	3,4,1,5,5,9
	.half	.L70-.L69
	.byte	3,3,1,5,1,9
	.half	.L71-.L70
	.byte	3,1,1,7,9
	.half	.L209-.L71
	.byte	0,1,1
.L745:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_ranges'
.L208:
	.word	-1,.L118,0,.L209-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_info'
.L210:
	.word	347
	.half	3
	.word	.L211
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L213,.L212
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_getIndex',0,1,166,2,15
	.word	.L366
	.byte	1,1,1
	.word	.L120,.L369,.L119
	.byte	4
	.byte	'qspi',0,1,166,2,42
	.word	.L231,.L370
	.byte	5
	.word	.L120,.L369
	.byte	6
	.byte	'index',0,1,168,2,19
	.word	.L287,.L371
	.byte	6
	.byte	'result',0,1,169,2,19
	.word	.L366,.L372
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_abbrev'
.L211:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_line'
.L212:
	.word	.L753-.L752
.L752:
	.half	3
	.word	.L755-.L754
.L754:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L755:
	.byte	5,12,7,0,5,2
	.word	.L120
	.byte	3,170,2,1,5,16,9
	.half	.L494-.L120
	.byte	3,2,1,5,48,9
	.half	.L495-.L494
	.byte	1,5,33,9
	.half	.L73-.L495
	.byte	3,2,1,5,13,9
	.half	.L756-.L73
	.byte	1,5,33,9
	.half	.L757-.L756
	.byte	1,5,40,9
	.half	.L758-.L757
	.byte	1,5,9,9
	.half	.L759-.L758
	.byte	1,5,57,7,9
	.half	.L760-.L759
	.byte	3,2,1,5,37,9
	.half	.L761-.L760
	.byte	1,5,57,9
	.half	.L762-.L761
	.byte	1,5,64,9
	.half	.L763-.L762
	.byte	1,5,22,9
	.half	.L764-.L763
	.byte	1,5,13,9
	.half	.L765-.L764
	.byte	3,1,1,5,55,9
	.half	.L74-.L765
	.byte	3,123,1,5,48,9
	.half	.L72-.L74
	.byte	1,5,5,7,9
	.half	.L75-.L72
	.byte	3,9,1,5,1,9
	.half	.L76-.L75
	.byte	3,1,1,7,9
	.half	.L214-.L76
	.byte	0,1,1
.L753:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_ranges'
.L213:
	.word	-1,.L120,0,.L214-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_info'
.L215:
	.word	415
	.half	3
	.word	.L216
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L218,.L217
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_recalcBasicConfiguration',0,1,222,2,8
	.word	.L287
	.byte	1,1,1
	.word	.L128,.L373,.L127
	.byte	4
	.byte	'oldBACON',0,1,222,2,48
	.word	.L287,.L374
	.byte	4
	.byte	'numOfData',0,1,222,2,68
	.word	.L235,.L375
	.byte	4
	.byte	'shortData',0,1,222,2,87
	.word	.L326,.L376
	.byte	4
	.byte	'lastData',0,1,222,2,106
	.word	.L326,.L377
	.byte	5
	.word	.L128,.L373
	.byte	6
	.byte	'bacon',0,1,224,2,20
	.word	.L296,.L378
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_abbrev'
.L216:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_line'
.L217:
	.word	.L767-.L766
.L766:
	.half	3
	.word	.L769-.L768
.L768:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L769:
	.byte	5,5,7,0,5,2
	.word	.L128
	.byte	3,226,2,1,5,24,7,9
	.half	.L770-.L128
	.byte	3,2,1,5,22,9
	.half	.L771-.L770
	.byte	1,5,24,9
	.half	.L772-.L771
	.byte	3,1,1,5,22,9
	.half	.L773-.L772
	.byte	1,5,18,9
	.half	.L83-.L773
	.byte	3,3,1,5,5,9
	.half	.L774-.L83
	.byte	3,1,1,5,1,9
	.half	.L84-.L774
	.byte	3,1,1,7,9
	.half	.L219-.L84
	.byte	0,1,1
.L767:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_ranges'
.L218:
	.word	-1,.L128,0,.L219-.L128,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_info'
.L220:
	.word	433
	.half	3
	.word	.L221
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L223,.L222
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_setSlaveSelectOutputControl',0,1,128,3,6,1,1,1
	.word	.L132,.L379,.L131
	.byte	4
	.byte	'qspi',0,1,128,3,52
	.word	.L231,.L380
	.byte	4
	.byte	'channelId',0,1,128,3,76
	.word	.L257,.L381
	.byte	4
	.byte	'outputEnable',0,1,128,3,95
	.word	.L326,.L382
	.byte	4
	.byte	'activeLevel',0,1,128,3,117
	.word	.L326,.L383
	.byte	5
	.word	.L132,.L379
	.byte	6
	.byte	'mask',0,1,130,3,19
	.word	.L253,.L384
	.byte	6
	.byte	'ssoc',0,1,132,3,19
	.word	.L385,.L386
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_abbrev'
.L221:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_line'
.L222:
	.word	.L776-.L775
.L775:
	.half	3
	.word	.L778-.L777
.L777:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L778:
	.byte	5,26,7,0,5,2
	.word	.L132
	.byte	3,129,3,1,5,28,9
	.half	.L779-.L132
	.byte	1,5,24,9
	.half	.L511-.L779
	.byte	3,3,1,5,5,9
	.half	.L513-.L511
	.byte	3,2,1,5,20,7,9
	.half	.L780-.L513
	.byte	3,2,1,5,27,9
	.half	.L781-.L780
	.byte	1,5,20,9
	.half	.L87-.L781
	.byte	3,4,1,5,23,9
	.half	.L782-.L87
	.byte	1,5,20,9
	.half	.L783-.L782
	.byte	1,5,5,9
	.half	.L88-.L783
	.byte	3,3,1,5,20,7,9
	.half	.L784-.L88
	.byte	3,2,1,5,27,9
	.half	.L785-.L784
	.byte	1,5,20,9
	.half	.L89-.L785
	.byte	3,4,1,5,23,9
	.half	.L786-.L89
	.byte	1,5,20,9
	.half	.L512-.L786
	.byte	1,5,18,9
	.half	.L90-.L512
	.byte	3,3,1,5,1,9
	.half	.L787-.L90
	.byte	3,1,1,7,9
	.half	.L224-.L787
	.byte	0,1,1
.L776:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_ranges'
.L223:
	.word	-1,.L132,0,.L224-.L132,0,0
	.sdecl	'.debug_info',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_info'
.L225:
	.word	608
	.half	3
	.word	.L226
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L228,.L227
	.byte	2
	.word	.L141
	.byte	3
	.byte	'IfxQspi_calculateDelayConstants',0,1,196,3,6,1,1,1
	.word	.L140,.L387,.L139
	.byte	4
	.byte	'qspi',0,1,196,3,54
	.word	.L388,.L389
	.byte	4
	.byte	'channelId',0,1,196,3,84
	.word	.L390,.L391
	.byte	4
	.byte	'chMode',0,1,196,3,115
	.word	.L292,.L392
	.byte	4
	.byte	'delayConst',0,1,196,3,143,1
	.word	.L393,.L394
	.byte	5
	.word	.L140,.L387
	.byte	6
	.byte	'divFactor',0,1,198,3,39
	.word	.L287,.L395
	.byte	6
	.byte	'dlyFactorPtr',0,1,199,3,39
	.word	.L396,.L397
	.byte	6
	.byte	'scaleTemp',0,1,200,3,39
	.word	.L279,.L398
	.byte	6
	.byte	'preTemp',0,1,201,3,39
	.word	.L326,.L399
	.byte	6
	.byte	'preFinal',0,1,202,3,39
	.word	.L326,.L400
	.byte	6
	.byte	'delayTemp',0,1,203,3,39
	.word	.L326,.L401
	.byte	6
	.byte	'delayFinal',0,1,204,3,39
	.word	.L326,.L402
	.byte	6
	.byte	'matchFound',0,1,205,3,39
	.word	.L326,.L403
	.byte	6
	.byte	'index',0,1,206,3,39
	.word	.L326,.L404
	.byte	6
	.byte	'cs',0,1,207,3,39
	.word	.L326,.L405
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_abbrev'
.L226:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_line'
.L227:
	.word	.L789-.L788
.L788:
	.half	3
	.word	.L791-.L790
.L790:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Qspi/Std/IfxQspi.c',0,0,0,0,0
.L791:
	.byte	5,6,7,0,5,2
	.word	.L140
	.byte	3,195,3,1,5,50,9
	.half	.L520-.L140
	.byte	3,6,1,9
	.half	.L521-.L520
	.byte	3,2,1,5,56,9
	.half	.L522-.L521
	.byte	3,3,1,5,54,9
	.half	.L792-.L522
	.byte	1,5,35,9
	.half	.L523-.L792
	.byte	3,3,1,5,39,9
	.half	.L793-.L523
	.byte	1,5,57,9
	.half	.L794-.L793
	.byte	1,5,63,9
	.half	.L795-.L794
	.byte	1,5,66,9
	.half	.L796-.L795
	.byte	1,5,44,9
	.half	.L797-.L796
	.byte	1,5,84,9
	.half	.L798-.L797
	.byte	1,5,90,9
	.half	.L799-.L798
	.byte	1,5,93,9
	.half	.L800-.L799
	.byte	1,5,109,9
	.half	.L801-.L800
	.byte	1,5,115,9
	.half	.L802-.L801
	.byte	1,5,97,9
	.half	.L803-.L802
	.byte	1,5,130,1,9
	.half	.L804-.L803
	.byte	1,5,136,1,9
	.half	.L805-.L804
	.byte	1,5,118,9
	.half	.L806-.L805
	.byte	1,5,71,9
	.half	.L524-.L806
	.byte	1,5,28,9
	.half	.L807-.L524
	.byte	3,3,1,5,18,9
	.half	.L808-.L807
	.byte	1,5,16,9
	.half	.L525-.L808
	.byte	3,2,1,5,30,9
	.half	.L526-.L525
	.byte	1,5,34,9
	.half	.L98-.L526
	.byte	3,3,1,5,42,9
	.half	.L809-.L98
	.byte	1,5,57,9
	.half	.L810-.L809
	.byte	1,5,55,9
	.half	.L811-.L810
	.byte	1,5,20,9
	.half	.L527-.L811
	.byte	3,3,1,5,22,9
	.half	.L528-.L527
	.byte	3,2,1,5,38,9
	.half	.L529-.L528
	.byte	1,5,47,9
	.half	.L100-.L529
	.byte	3,2,1,5,55,9
	.half	.L812-.L100
	.byte	1,5,49,9
	.half	.L813-.L812
	.byte	1,5,44,9
	.half	.L814-.L813
	.byte	1,5,70,9
	.half	.L815-.L814
	.byte	1,5,68,9
	.half	.L816-.L815
	.byte	1,5,25,9
	.half	.L817-.L816
	.byte	1,5,13,9
	.half	.L530-.L817
	.byte	3,2,1,5,47,7,9
	.half	.L531-.L530
	.byte	3,2,1,5,41,9
	.half	.L532-.L531
	.byte	1,5,21,9
	.half	.L533-.L532
	.byte	1,5,59,9
	.half	.L818-.L533
	.byte	1,5,17,9
	.half	.L819-.L818
	.byte	1,5,50,7,9
	.half	.L820-.L819
	.byte	3,2,1,5,39,9
	.half	.L821-.L820
	.byte	1,5,32,9
	.half	.L822-.L821
	.byte	3,1,1,9
	.half	.L823-.L822
	.byte	3,1,1,5,21,9
	.half	.L824-.L823
	.byte	3,1,1,5,22,9
	.half	.L102-.L824
	.byte	3,2,1,5,31,7,9
	.half	.L825-.L102
	.byte	3,2,1,5,50,9
	.half	.L534-.L825
	.byte	3,1,1,5,39,9
	.half	.L535-.L534
	.byte	1,5,32,9
	.half	.L826-.L535
	.byte	3,1,1,9
	.half	.L827-.L826
	.byte	3,1,1,5,21,9
	.half	.L828-.L827
	.byte	3,1,1,5,47,9
	.half	.L101-.L828
	.byte	3,109,1,5,38,9
	.half	.L99-.L101
	.byte	1,5,9,7,9
	.half	.L103-.L99
	.byte	3,28,1,5,24,7,9
	.half	.L829-.L103
	.byte	3,3,1,9
	.half	.L830-.L829
	.byte	3,1,1,5,19,9
	.half	.L106-.L830
	.byte	3,4,1,5,33,9
	.half	.L831-.L106
	.byte	1,5,19,9
	.half	.L832-.L831
	.byte	3,1,1,5,33,9
	.half	.L833-.L832
	.byte	1,5,37,9
	.half	.L834-.L833
	.byte	3,83,1,5,30,9
	.half	.L97-.L834
	.byte	1,5,1,7,9
	.half	.L835-.L97
	.byte	3,47,1,7,9
	.half	.L229-.L835
	.byte	0,1,1
.L789:
	.sdecl	'.debug_ranges',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_ranges'
.L228:
	.word	-1,.L140,0,.L229-.L140,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L406-.L108
	.half	2
	.byte	138,0
	.word	.L406-.L108,.L280-.L108
	.half	2
	.byte	138,32
	.word	.L280-.L108,.L280-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L282:
	.word	-1,.L108,0,.L407-.L108
	.half	1
	.byte	84
	.word	0,0
.L283:
	.word	-1,.L108,.L409-.L108,.L280-.L108
	.half	1
	.byte	89
	.word	0,0
.L286:
	.word	-1,.L108,0,.L280-.L108
	.half	2
	.byte	145,96
	.word	0,0
.L284:
	.word	-1,.L108,.L407-.L108,.L410-.L108
	.half	1
	.byte	82
	.word	.L410-.L108,.L411-.L108
	.half	5
	.byte	144,32,157,32,0
	.word	.L411-.L108,.L280-.L108
	.half	1
	.byte	82
	.word	0,0
.L281:
	.word	-1,.L108,0,.L407-.L108
	.half	1
	.byte	100
	.word	.L408-.L108,.L280-.L108
	.half	1
	.byte	108
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L110,0,.L412-.L110
	.half	2
	.byte	138,0
	.word	.L412-.L110,.L288-.L110
	.half	2
	.byte	138,8
	.word	.L288-.L110,.L288-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L297:
	.word	-1,.L110,.L416-.L110,.L288-.L110
	.half	1
	.byte	89
	.word	.L417-.L110,.L288-.L110
	.half	1
	.byte	82
	.word	0,0
.L295:
	.word	-1,.L110,0,.L413-.L110
	.half	1
	.byte	85
	.word	0,0
.L293:
	.word	-1,.L110,0,.L413-.L110
	.half	1
	.byte	101
	.word	.L415-.L110,.L288-.L110
	.half	1
	.byte	111
	.word	0,0
.L291:
	.word	-1,.L110,0,.L413-.L110
	.half	1
	.byte	84
	.word	.L414-.L110,.L288-.L110
	.half	1
	.byte	88
	.word	0,0
.L299:
	.word	-1,.L110,0,.L288-.L110
	.half	2
	.byte	145,120
	.word	0,0
.L289:
	.word	-1,.L110,0,.L413-.L110
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_loc'
.L139:
	.word	-1,.L140,0,.L387-.L140
	.half	2
	.byte	138,0
	.word	0,0
.L392:
	.word	-1,.L140,0,.L98-.L140
	.half	1
	.byte	101
	.word	0,0
.L391:
	.word	-1,.L140,0,.L98-.L140
	.half	1
	.byte	84
	.word	0,0
.L405:
	.word	-1,.L140,.L523-.L140,.L98-.L140
	.half	1
	.byte	81
	.word	0,0
.L394:
	.word	-1,.L140,0,.L98-.L140
	.half	1
	.byte	102
	.word	.L520-.L140,.L387-.L140
	.half	1
	.byte	108
	.word	0,0
.L402:
	.word	-1,.L140,.L522-.L140,.L387-.L140
	.half	1
	.byte	92
	.word	0,0
.L401:
	.word	-1,.L140,.L530-.L140,.L531-.L140
	.half	1
	.byte	82
	.word	.L532-.L140,.L533-.L140
	.half	1
	.byte	82
	.word	.L102-.L140,.L534-.L140
	.half	1
	.byte	82
	.word	.L534-.L140,.L535-.L140
	.half	1
	.byte	95
	.word	0,0
.L395:
	.word	-1,.L140,.L524-.L140,.L387-.L140
	.half	1
	.byte	93
	.word	0,0
.L397:
	.word	-1,.L140,0,.L98-.L140
	.half	1
	.byte	101
	.word	.L525-.L140,.L387-.L140
	.half	1
	.byte	109
	.word	0,0
.L404:
	.word	-1,.L140,.L526-.L140,.L387-.L140
	.half	1
	.byte	88
	.word	0,0
.L403:
	.word	-1,.L140,.L528-.L140,.L97-.L140
	.half	1
	.byte	90
	.word	0,0
.L400:
	.word	-1,.L140,.L521-.L140,.L387-.L140
	.half	1
	.byte	94
	.word	0,0
.L399:
	.word	-1,.L140,.L529-.L140,.L97-.L140
	.half	1
	.byte	89
	.word	0,0
.L389:
	.word	-1,.L140,0,.L98-.L140
	.half	1
	.byte	100
	.word	0,0
.L398:
	.word	-1,.L140,.L527-.L140,.L97-.L140
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L418-.L112
	.half	2
	.byte	138,0
	.word	.L418-.L112,.L300-.L112
	.half	2
	.byte	138,8
	.word	.L300-.L112,.L300-.L112
	.half	2
	.byte	138,0
	.word	0,0
.L317:
	.word	0,0
.L314:
	.word	-1,.L112,.L427-.L112,.L338-.L112
	.half	1
	.byte	90
	.word	.L338-.L112,.L339-.L112
	.half	1
	.byte	88
	.word	.L444-.L112,.L24-.L112
	.half	1
	.byte	90
	.word	.L445-.L112,.L446-.L112
	.half	1
	.byte	90
	.word	.L339-.L112,.L448-.L112
	.half	1
	.byte	95
	.word	.L449-.L112,.L450-.L112
	.half	1
	.byte	90
	.word	.L448-.L112,.L451-.L112
	.half	1
	.byte	90
	.word	.L452-.L112,.L453-.L112
	.half	1
	.byte	90
	.word	.L457-.L112,.L458-.L112
	.half	1
	.byte	90
	.word	0,0
.L313:
	.word	0,0
.L318:
	.word	0,0
.L323:
	.word	-1,.L112,.L436-.L112,.L300-.L112
	.half	1
	.byte	89
	.word	0,0
.L316:
	.word	-1,.L112,.L428-.L112,.L429-.L112
	.half	1
	.byte	94
	.word	0,0
.L305:
	.word	-1,.L112,0,.L419-.L112
	.half	1
	.byte	101
	.word	.L421-.L112,.L300-.L112
	.half	1
	.byte	111
	.word	0,0
.L303:
	.word	-1,.L112,0,.L419-.L112
	.half	1
	.byte	84
	.word	0,0
.L321:
	.word	-1,.L112,.L455-.L112,.L300-.L112
	.half	1
	.byte	84
	.word	0,0
.L327:
	.word	-1,.L112,.L447-.L112,.L29-.L112
	.half	1
	.byte	95
	.word	.L30-.L112,.L23-.L112
	.half	1
	.byte	95
	.word	0,0
.L307:
	.word	-1,.L112,0,.L300-.L112
	.half	2
	.byte	145,124
	.word	.L422-.L112,.L423-.L112
	.half	1
	.byte	95
	.word	.L456-.L112,.L457-.L112
	.half	1
	.byte	95
	.word	.L459-.L112,.L460-.L112
	.half	1
	.byte	95
	.word	.L461-.L112,.L462-.L112
	.half	1
	.byte	95
	.word	.L463-.L112,.L464-.L112
	.half	1
	.byte	95
	.word	.L454-.L112,.L300-.L112
	.half	1
	.byte	82
	.word	0,0
.L322:
	.word	0,0
.L312:
	.word	-1,.L112,.L426-.L112,.L338-.L112
	.half	1
	.byte	88
	.word	.L430-.L112,.L431-.L112
	.half	1
	.byte	84
	.word	.L432-.L112,.L433-.L112
	.half	1
	.byte	84
	.word	0,0
.L319:
	.word	-1,.L112,.L453-.L112,.L454-.L112
	.half	1
	.byte	82
	.word	0,0
.L310:
	.word	0,0
.L315:
	.word	-1,.L112,.L441-.L112,.L442-.L112
	.half	1
	.byte	82
	.word	.L442-.L112,.L14-.L112
	.half	1
	.byte	93
	.word	.L448-.L112,.L33-.L112
	.half	1
	.byte	95
	.word	0,0
.L301:
	.word	-1,.L112,0,.L419-.L112
	.half	1
	.byte	100
	.word	.L420-.L112,.L300-.L112
	.half	1
	.byte	108
	.word	0,0
.L334:
	.word	0,0
.L330:
	.word	0,0
.L340:
	.word	-1,.L112,.L434-.L112,.L435-.L112
	.half	1
	.byte	82
	.word	.L435-.L112,.L300-.L112
	.half	1
	.byte	92
	.word	0,0
.L325:
	.word	-1,.L112,.L440-.L112,.L443-.L112
	.half	1
	.byte	91
	.word	.L21-.L112,.L22-.L112
	.half	1
	.byte	91
	.word	0,0
.L311:
	.word	-1,.L112,.L424-.L112,.L425-.L112
	.half	1
	.byte	82
	.word	.L425-.L112,.L300-.L112
	.half	2
	.byte	145,120
	.word	.L437-.L112,.L438-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L324:
	.word	-1,.L112,.L439-.L112,.L440-.L112
	.half	1
	.byte	91
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_loc'
.L113:
	.word	-1,.L114,0,.L341-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L343:
	.word	-1,.L114,0,.L465-.L114
	.half	1
	.byte	84
	.word	0,0
.L345:
	.word	-1,.L114,.L466-.L114,.L341-.L114
	.half	1
	.byte	89
	.word	0,0
.L352:
	.word	-1,.L114,.L467-.L114,.L341-.L114
	.half	1
	.byte	90
	.word	.L471-.L114,.L341-.L114
	.half	1
	.byte	82
	.word	0,0
.L344:
	.word	0,0
.L347:
	.word	0,0
.L346:
	.word	-1,.L114,.L348-.L114,.L341-.L114
	.half	1
	.byte	91
	.word	0,0
.L351:
	.word	-1,.L114,.L468-.L114,.L341-.L114
	.half	1
	.byte	92
	.word	0,0
.L342:
	.word	-1,.L114,0,.L465-.L114
	.half	1
	.byte	100
	.word	0,0
.L349:
	.word	0,0
.L353:
	.word	-1,.L114,.L469-.L114,.L470-.L114
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L354-.L116
	.half	2
	.byte	138,0
	.word	0,0
.L357:
	.word	-1,.L116,.L486-.L116,.L354-.L116
	.half	1
	.byte	91
	.word	0,0
.L363:
	.word	-1,.L116,.L491-.L116,.L492-.L116
	.half	1
	.byte	95
	.word	0,0
.L362:
	.word	-1,.L116,.L483-.L116,.L57-.L116
	.half	1
	.byte	90
	.word	.L58-.L116,.L354-.L116
	.half	1
	.byte	90
	.word	0,0
.L359:
	.word	-1,.L116,.L484-.L116,.L485-.L116
	.half	1
	.byte	92
	.word	0,0
.L361:
	.word	0,0
.L364:
	.word	-1,.L116,.L472-.L116,.L474-.L116
	.half	1
	.byte	82
	.word	.L475-.L116,.L354-.L116
	.half	1
	.byte	89
	.word	.L477-.L116,.L478-.L116
	.half	1
	.byte	84
	.word	0,0
.L356:
	.word	-1,.L116,0,.L472-.L116
	.half	1
	.byte	84
	.word	.L473-.L116,.L354-.L116
	.half	1
	.byte	88
	.word	.L476-.L116,.L474-.L116
	.half	1
	.byte	84
	.word	.L479-.L116,.L480-.L116
	.half	1
	.byte	84
	.word	0,0
.L355:
	.word	-1,.L116,0,.L472-.L116
	.half	1
	.byte	100
	.word	0,0
.L360:
	.word	-1,.L116,.L481-.L116,.L482-.L116
	.half	1
	.byte	82
	.word	.L482-.L116,.L483-.L116
	.half	1
	.byte	90
	.word	.L57-.L116,.L58-.L116
	.half	1
	.byte	90
	.word	.L487-.L116,.L488-.L116
	.half	1
	.byte	84
	.word	0,0
.L358:
	.word	-1,.L116,.L489-.L116,.L490-.L116
	.half	1
	.byte	82
	.word	.L490-.L116,.L59-.L116
	.half	1
	.byte	93
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L365-.L118
	.half	2
	.byte	138,0
	.word	0,0
.L368:
	.word	-1,.L118,.L493-.L118,.L69-.L118
	.half	1
	.byte	98
	.word	.L70-.L118,.L365-.L118
	.half	1
	.byte	98
	.word	0,0
.L367:
	.word	-1,.L118,0,.L365-.L118
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L120,0,.L369-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L371:
	.word	-1,.L120,.L495-.L120,.L369-.L120
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L370:
	.word	-1,.L120,0,.L369-.L120
	.half	1
	.byte	100
	.word	0,0
.L372:
	.word	-1,.L120,.L494-.L120,.L369-.L120
	.half	1
	.byte	82
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_read16')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L122,0,.L230-.L122
	.half	2
	.byte	138,0
	.word	0,0
.L236:
	.word	-1,.L122,0,.L230-.L122
	.half	1
	.byte	84
	.word	0,0
.L234:
	.word	-1,.L122,0,.L230-.L122
	.half	1
	.byte	101
	.word	0,0
.L232:
	.word	-1,.L122,0,.L230-.L122
	.half	1
	.byte	100
	.word	0,0
.L238:
	.word	-1,.L122,.L496-.L122,.L230-.L122
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_read32')
	.sect	'.debug_loc'
.L123:
	.word	-1,.L124,0,.L239-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L243:
	.word	-1,.L124,0,.L239-.L124
	.half	1
	.byte	84
	.word	0,0
.L242:
	.word	-1,.L124,0,.L239-.L124
	.half	1
	.byte	101
	.word	0,0
.L240:
	.word	-1,.L124,0,.L239-.L124
	.half	1
	.byte	100
	.word	0,0
.L244:
	.word	-1,.L124,.L497-.L124,.L239-.L124
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_read8')
	.sect	'.debug_loc'
.L125:
	.word	-1,.L126,0,.L245-.L126
	.half	2
	.byte	138,0
	.word	0,0
.L249:
	.word	-1,.L126,0,.L245-.L126
	.half	1
	.byte	84
	.word	0,0
.L248:
	.word	-1,.L126,0,.L245-.L126
	.half	1
	.byte	101
	.word	0,0
.L246:
	.word	-1,.L126,0,.L245-.L126
	.half	1
	.byte	100
	.word	0,0
.L250:
	.word	-1,.L126,.L498-.L126,.L245-.L126
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_loc'
.L127:
	.word	-1,.L128,0,.L373-.L128
	.half	2
	.byte	138,0
	.word	0,0
.L378:
	.word	-1,.L128,0,.L373-.L128
	.half	1
	.byte	84
	.word	.L499-.L128,.L373-.L128
	.half	1
	.byte	82
	.word	0,0
.L377:
	.word	-1,.L128,0,.L373-.L128
	.half	1
	.byte	87
	.word	0,0
.L375:
	.word	-1,.L128,0,.L373-.L128
	.half	1
	.byte	85
	.word	0,0
.L374:
	.word	-1,.L128,0,.L373-.L128
	.half	1
	.byte	84
	.word	0,0
.L376:
	.word	-1,.L128,0,.L373-.L128
	.half	1
	.byte	86
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_loc'
.L129:
	.word	-1,.L130,0,.L251-.L130
	.half	2
	.byte	138,0
	.word	0,0
.L254:
	.word	-1,.L130,.L500-.L130,.L502-.L130
	.half	1
	.byte	82
	.word	.L503-.L130,.L251-.L130
	.half	1
	.byte	88
	.word	.L502-.L130,.L504-.L130
	.half	1
	.byte	84
	.word	.L505-.L130,.L506-.L130
	.half	1
	.byte	84
	.word	.L507-.L130,.L508-.L130
	.half	1
	.byte	84
	.word	.L509-.L130,.L510-.L130
	.half	1
	.byte	84
	.word	0,0
.L252:
	.word	-1,.L130,0,.L500-.L130
	.half	1
	.byte	100
	.word	.L501-.L130,.L251-.L130
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_loc'
.L131:
	.word	-1,.L132,0,.L379-.L132
	.half	2
	.byte	138,0
	.word	0,0
.L383:
	.word	-1,.L132,0,.L379-.L132
	.half	1
	.byte	86
	.word	0,0
.L381:
	.word	-1,.L132,0,.L379-.L132
	.half	1
	.byte	84
	.word	0,0
.L384:
	.word	-1,.L132,.L511-.L132,.L512-.L132
	.half	1
	.byte	95
	.word	0,0
.L382:
	.word	-1,.L132,0,.L379-.L132
	.half	1
	.byte	85
	.word	0,0
.L380:
	.word	-1,.L132,0,.L379-.L132
	.half	1
	.byte	100
	.word	0,0
.L386:
	.word	-1,.L132,.L513-.L132,.L379-.L132
	.half	1
	.byte	81
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_write16')
	.sect	'.debug_loc'
.L133:
	.word	-1,.L134,0,.L255-.L134
	.half	2
	.byte	138,0
	.word	0,0
.L258:
	.word	-1,.L134,0,.L255-.L134
	.half	1
	.byte	84
	.word	0,0
.L260:
	.word	-1,.L134,0,.L255-.L134
	.half	1
	.byte	85
	.word	0,0
.L262:
	.word	-1,.L134,.L514-.L134,.L255-.L134
	.half	1
	.byte	81
	.word	0,0
.L259:
	.word	-1,.L134,0,.L255-.L134
	.half	1
	.byte	101
	.word	0,0
.L264:
	.word	-1,.L134,.L515-.L134,.L255-.L134
	.half	1
	.byte	111
	.word	0,0
.L256:
	.word	-1,.L134,0,.L255-.L134
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_write32')
	.sect	'.debug_loc'
.L135:
	.word	-1,.L136,0,.L265-.L136
	.half	2
	.byte	138,0
	.word	0,0
.L267:
	.word	-1,.L136,0,.L265-.L136
	.half	1
	.byte	84
	.word	0,0
.L269:
	.word	-1,.L136,0,.L265-.L136
	.half	1
	.byte	85
	.word	0,0
.L270:
	.word	-1,.L136,.L516-.L136,.L265-.L136
	.half	1
	.byte	81
	.word	0,0
.L268:
	.word	-1,.L136,0,.L265-.L136
	.half	1
	.byte	101
	.word	0,0
.L271:
	.word	-1,.L136,.L517-.L136,.L265-.L136
	.half	1
	.byte	111
	.word	0,0
.L266:
	.word	-1,.L136,0,.L265-.L136
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxQspi_write8')
	.sect	'.debug_loc'
.L137:
	.word	-1,.L138,0,.L272-.L138
	.half	2
	.byte	138,0
	.word	0,0
.L274:
	.word	-1,.L138,0,.L272-.L138
	.half	1
	.byte	84
	.word	0,0
.L276:
	.word	-1,.L138,0,.L272-.L138
	.half	1
	.byte	85
	.word	0,0
.L277:
	.word	-1,.L138,.L518-.L138,.L272-.L138
	.half	1
	.byte	81
	.word	0,0
.L275:
	.word	-1,.L138,0,.L272-.L138
	.half	1
	.byte	101
	.word	0,0
.L278:
	.word	-1,.L138,.L519-.L138,.L272-.L138
	.half	1
	.byte	111
	.word	0,0
.L273:
	.word	-1,.L138,0,.L272-.L138
	.half	1
	.byte	100
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L836:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calcRealBaudrate')
	.sect	'.debug_frame'
	.word	36
	.word	.L836,.L108,.L280-.L108
	.byte	4
	.word	(.L406-.L108)/2
	.byte	19,32,22,26,3,19,138,32,4
	.word	(.L280-.L406)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calculateBasicConfigurationValue')
	.sect	'.debug_frame'
	.word	36
	.word	.L836,.L110,.L288-.L110
	.byte	4
	.word	(.L412-.L110)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L288-.L412)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calculateExtendedConfigurationValue')
	.sect	'.debug_frame'
	.word	36
	.word	.L836,.L112,.L300-.L112
	.byte	4
	.word	(.L418-.L112)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L300-.L418)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calculatePrescaler')
	.sect	'.debug_frame'
	.word	12
	.word	.L836,.L114,.L341-.L114
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calculateTimeQuantumLength')
	.sect	'.debug_frame'
	.word	12
	.word	.L836,.L116,.L354-.L116
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_getAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L836,.L118,.L365-.L118
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_getIndex')
	.sect	'.debug_frame'
	.word	24
	.word	.L836,.L120,.L369-.L120
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_read16')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L122,.L230-.L122
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_read32')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L124,.L239-.L124
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_read8')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L126,.L245-.L126
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_recalcBasicConfiguration')
	.sect	'.debug_frame'
	.word	24
	.word	.L836,.L128,.L373-.L128
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_resetModule')
	.sect	'.debug_frame'
	.word	12
	.word	.L836,.L130,.L251-.L130
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_setSlaveSelectOutputControl')
	.sect	'.debug_frame'
	.word	24
	.word	.L836,.L132,.L379-.L132
	.byte	8,18,8,19,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_write16')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L134,.L255-.L134
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_write32')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L136,.L265-.L136
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_write8')
	.sect	'.debug_frame'
	.word	20
	.word	.L836,.L138,.L272-.L138
	.byte	8,18,8,19,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxQspi_calculateDelayConstants')
	.sect	'.debug_frame'
	.word	12
	.word	.L836,.L140,.L387-.L140
	; Module end
