################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../code/Device.c \
../code/Flash.c \
../code/PID.c \
../code/camera.c \
../code/encoder.c \
../code/gps.c \
../code/imu.c \
../code/quaternion.c \
../code/remote.c \
../code/user_display_gps.c 

COMPILED_SRCS += \
code/Device.src \
code/Flash.src \
code/PID.src \
code/camera.src \
code/encoder.src \
code/gps.src \
code/imu.src \
code/quaternion.src \
code/remote.src \
code/user_display_gps.src 

C_DEPS += \
code/Device.d \
code/Flash.d \
code/PID.d \
code/camera.d \
code/encoder.d \
code/gps.d \
code/imu.d \
code/quaternion.d \
code/remote.d \
code/user_display_gps.d 

OBJS += \
code/Device.o \
code/Flash.o \
code/PID.o \
code/camera.o \
code/encoder.o \
code/gps.o \
code/imu.o \
code/quaternion.o \
code/remote.o \
code/user_display_gps.o 


# Each subdirectory must supply rules for building sources it contributes
code/Device.src: ../code/Device.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/Device.o: code/Device.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/Flash.src: ../code/Flash.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/Flash.o: code/Flash.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/PID.src: ../code/PID.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/PID.o: code/PID.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/camera.src: ../code/camera.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/camera.o: code/camera.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/encoder.src: ../code/encoder.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/encoder.o: code/encoder.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/gps.src: ../code/gps.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/gps.o: code/gps.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/imu.src: ../code/imu.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/imu.o: code/imu.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/quaternion.src: ../code/quaternion.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/quaternion.o: code/quaternion.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/remote.src: ../code/remote.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/remote.o: code/remote.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"
code/user_display_gps.src: ../code/user_display_gps.c code/subdir.mk
	cctc -cs --dep-file="$(*F).d" --misrac-version=2004 -D__CPU__=tc26xb "-fC:/Users/<USER>/Desktop/ORRN_Code_4G - 鍓湰/ORRN_Code/Mark_1/Debug/TASKING_C_C___Compiler-Include_paths__-I_.opt" --iso=99 --c++14 --language=+volatile --exceptions --anachronisms --fp-model=2 -O0 --tradeoff=4 --compact-max-size=200 -g -Wc-w544 -Wc-w557 -Ctc26xb -Y0 -N0 -Z0 -o "$@" "$<"
code/user_display_gps.o: code/user_display_gps.src code/subdir.mk
	astc -Og -Os --no-warnings= --error-limit=42 -o  "$@" "$<"

clean: clean-code

clean-code:
	-$(RM) code/Device.d code/Device.o code/Device.src code/Flash.d code/Flash.o code/Flash.src code/PID.d code/PID.o code/PID.src code/camera.d code/camera.o code/camera.src code/encoder.d code/encoder.o code/encoder.src code/gps.d code/gps.o code/gps.src code/imu.d code/imu.o code/imu.src code/quaternion.d code/quaternion.o code/quaternion.src code/remote.d code/remote.o code/remote.src code/user_display_gps.d code/user_display_gps.o code/user_display_gps.src

.PHONY: clean-code

