	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc3268a --dep-file=IfxMtu.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c'

	
$TC16X
	
	.sdecl	'.text.IfxMtu.IfxMtu_clearErrorTracking',code,cluster('IfxMtu_clearErrorTracking')
	.sect	'.text.IfxMtu.IfxMtu_clearErrorTracking'
	.align	2
	
	.global	IfxMtu_clearErrorTracking
; Function IfxMtu_clearErrorTracking
.L106:
IfxMtu_clearErrorTracking:	.type	func
	mov	d15,#256
.L713:
	mul	d4,d15
.L344:
	mov	d15,#4096
	addih	d15,d15,#61446
.L345:
	add	d4,d15
.L714:
	mov.a	a15,d4
.L346:
	ld.hu	d15,[a15]16
.L715:
	or	d15,#16
	st.h	[a15]16,d15
.L716:
	ret
.L305:
	
__IfxMtu_clearErrorTracking_function_end:
	.size	IfxMtu_clearErrorTracking,__IfxMtu_clearErrorTracking_function_end-IfxMtu_clearErrorTracking
.L165:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_clearSram',code,cluster('IfxMtu_clearSram')
	.sect	'.text.IfxMtu.IfxMtu_clearSram'
	.align	2
	
	.global	IfxMtu_clearSram
; Function IfxMtu_clearSram
.L108:
IfxMtu_clearSram:	.type	func
	mov	d8,d4
.L348:
	mov	d9,#0
.L349:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L347:
	mov	d10,d2
.L194:
	movh.a	a15,#61443
	ld.w	d15,[a15]@los(0xf00360f0)
	and	d15,#1
.L528:
	j	.L2
.L2:
	jne	d15,#1,.L3
.L529:
	mov	d4,d10
.L350:
	call	IfxScuWdt_clearSafetyEndinit
.L351:
	mov	d9,#1
.L3:
	mov	d4,d8
.L352:
	call	IfxMtu_clearSramStart
.L353:
	mov	d4,d10
.L354:
	call	IfxScuWdt_setSafetyEndinit
.L355:
	mul	d15,d8,#12
.L530:
	movh.a	a15,#@his(IfxMtu_sramTable)
	lea	a15,[a15]@los(IfxMtu_sramTable)
.L531:
	addsc.a	a15,a15,d15,#0
.L532:
	ld.w	d4,[a15]8
.L533:
	mov	d5,#1
.L534:
	mov	d6,d8
.L356:
	call	IfxMtu_waitForMbistDone
.L357:
	j	.L4
.L5:
	nop
.L4:
	mov	d0,#256
.L535:
	mul	d0,d8
.L536:
	mov	d15,#4096
	addih	d15,d15,#61446
.L358:
	add	d0,d15
.L537:
	mov.a	a15,d0
.L360:
	ld.hu	d15,[a15]6
.L361:
	and	d15,#1
.L362:
	j	.L6
.L6:
	jeq	d15,#0,.L5
.L538:
	mov	d4,d10
.L363:
	call	IfxScuWdt_clearSafetyEndinit
.L359:
	mov	d4,d8
.L364:
	call	IfxMtu_clearSramContinue
.L365:
	jne	d9,#1,.L7
.L539:
	mov	d4,d10
.L366:
	call	IfxScuWdt_setSafetyEndinit
.L7:
	ret
.L186:
	
__IfxMtu_clearSram_function_end:
	.size	IfxMtu_clearSram,__IfxMtu_clearSram_function_end-IfxMtu_clearSram
.L135:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_clearSramContinue',code,cluster('IfxMtu_clearSramContinue')
	.sect	'.text.IfxMtu.IfxMtu_clearSramContinue'
	.align	2
	
	.global	IfxMtu_clearSramContinue
; Function IfxMtu_clearSramContinue
.L110:
IfxMtu_clearSramContinue:	.type	func
	mov	d8,d4
.L367:
	mov	d5,#0
	mov	d4,d8
	call	IfxMtu_readSramAddress
.L206:
	sha	d0,d8,#-5
.L544:
	mul	d0,d0,#4
.L545:
	mov	d1,#16
	addih	d1,d1,#61446
.L368:
	add	d0,d1
.L546:
	mov.a	a15,d0
.L370:
	mov	d0,#1
.L369:
	and	d15,d8,#31
.L547:
	sha	d0,d0,d15
.L371:
	ld.w	d15,[a15]
.L548:
	mov	d1,#-1
	xor	d0,d1
.L372:
	and	d15,d0
	st.w	[a15],d15
.L207:
	j	.L8
.L9:
.L8:
	sha	d15,d8,#-5
.L549:
	mul	d15,d15,#4
.L550:
	mov	d0,#56
	addih	d0,d0,#61446
.L373:
	add	d15,d0
.L551:
	mov.a	a15,d15
.L375:
	mov	d0,#1
.L552:
	and	d15,d8,#31
.L374:
	sha	d0,d0,d15
.L376:
	ld.w	d15,[a15]
.L553:
	and	d15,d0
.L554:
	ne	d15,d15,#0
.L555:
	j	.L10
.L10:
	jne	d15,#0,.L9
.L556:
	ret
.L203:
	
__IfxMtu_clearSramContinue_function_end:
	.size	IfxMtu_clearSramContinue,__IfxMtu_clearSramContinue_function_end-IfxMtu_clearSramContinue
.L140:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_clearSramStart',code,cluster('IfxMtu_clearSramStart')
	.sect	'.text.IfxMtu.IfxMtu_clearSramStart'
	.align	2
	
	.global	IfxMtu_clearSramStart
; Function IfxMtu_clearSramStart
.L112:
IfxMtu_clearSramStart:	.type	func
	mov	d15,#256
.L561:
	mul	d15,d4
.L562:
	mov	d0,#4096
	addih	d0,d0,#61446
.L378:
	add	d15,d0
.L563:
	mov.a	a2,d15
.L225:
	sha	d15,d4,#-5
.L379:
	mul	d15,d15,#4
.L564:
	mov	d0,#16
	addih	d0,d0,#61446
.L380:
	add	d15,d0
.L565:
	mov.a	a15,d15
.L382:
	mov	d0,#1
.L566:
	and	d15,d4,#31
.L381:
	sha	d0,d0,d15
.L383:
	ld.w	d15,[a15]
.L567:
	or	d15,d0
	st.w	[a15],d15
.L226:
	j	.L11
.L12:
.L11:
	sha	d15,d4,#-5
.L568:
	mul	d15,d15,#4
.L569:
	mov	d0,#56
	addih	d0,d0,#61446
.L384:
	add	d15,d0
.L570:
	mov.a	a15,d15
.L386:
	mov	d0,#1
.L571:
	and	d15,d4,#31
.L385:
	sha	d0,d0,d15
.L388:
	ld.w	d15,[a15]
.L572:
	and	d15,d0
.L573:
	ne	d15,d15,#0
.L574:
	j	.L13
.L13:
	jne	d15,#0,.L12
.L235:
	mul	d15,d4,#12
.L575:
	movh.a	a15,#@his(IfxMtu_sramTable)
.L387:
	lea	a15,[a15]@los(IfxMtu_sramTable)
.L576:
	addsc.a	a15,a15,d15,#0
.L390:
	ld.bu	d8,[a15]
.L239:
	ld.hu	d0,[a15]2
.L389:
	ld.bu	d1,[a15]4
.L392:
	ld.bu	d15,[a15]5
.L577:
	add	d2,d15,d0
.L394:
	ld.bu	d15,[a15]6
.L578:
	add	d3,d15,d0
.L395:
	add	d0,d1
.L391:
	mov	d1,#0
.L393:
	mov	d4,#0
.L377:
	mov	d5,#0
.L397:
	mov	d6,#0
.L398:
	j	.L14
.L15:
	mov	d7,#0
.L399:
	j	.L16
.L17:
	jeq	d7,d2,.L18
.L579:
	jne	d7,d3,.L19
.L18:
	mov	d15,#1
.L580:
	sha	d15,d15,d1
.L581:
	or	d5,d15
.L19:
	add	d1,#1
.L582:
	mov	d15,#16
.L583:
	jlt.u	d1,d15,.L20
.L584:
	mul	d15,d4,#2
	addsc.a	a15,a2,d15,#0
.L585:
	st.h	[a15]160,d5
.L586:
	add	d4,#1
.L587:
	mov	d1,#0
.L588:
	mov	d5,#0
.L20:
	add	d7,#1
.L16:
	jlt.u	d7,d0,.L17
.L249:
	add	d6,#1
.L14:
	jlt.u	d6,d8,.L15
.L589:
	jeq	d1,#0,.L21
.L590:
	mul	d15,d4,#2
	addsc.a	a15,a2,d15,#0
.L591:
	st.h	[a15]160,d5
.L21:
	mov	d0,#16384
.L396:
	or	d15,d0,#16
.L592:
	or	d15,#1
.L593:
	st.h	[a2]4,d15
.L594:
	or	d15,d0,#16
.L595:
	st.h	[a2]4,d15
.L596:
	ret
.L221:
	
__IfxMtu_clearSramStart_function_end:
	.size	IfxMtu_clearSramStart,__IfxMtu_clearSramStart_function_end-IfxMtu_clearSramStart
.L145:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_enableErrorTracking',code,cluster('IfxMtu_enableErrorTracking')
	.sect	'.text.IfxMtu.IfxMtu_enableErrorTracking'
	.align	2
	
	.global	IfxMtu_enableErrorTracking
; Function IfxMtu_enableErrorTracking
.L114:
IfxMtu_enableErrorTracking:	.type	func
	mov	d15,#256
.L721:
	mul	d4,d15
.L400:
	mov	d15,#4096
	addih	d15,d15,#61446
.L401:
	add	d4,d15
.L722:
	mov.a	a15,d4
.L402:
	jne	d5,#0,.L22
.L723:
	ld.hu	d15,[a15]14
.L724:
	insert	d15,d15,#0,#4,#1
	st.h	[a15]14,d15
.L725:
	j	.L23
.L22:
	ld.hu	d15,[a15]14
.L726:
	or	d15,#16
	st.h	[a15]14,d15
.L23:
	ret
.L308:
	
__IfxMtu_enableErrorTracking_function_end:
	.size	IfxMtu_enableErrorTracking,__IfxMtu_enableErrorTracking_function_end-IfxMtu_enableErrorTracking
.L170:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_getSystemAddress',code,cluster('IfxMtu_getSystemAddress')
	.sect	'.text.IfxMtu.IfxMtu_getSystemAddress'
	.align	2
	
	.global	IfxMtu_getSystemAddress
; Function IfxMtu_getSystemAddress
.L116:
IfxMtu_getSystemAddress:	.type	func
	extr.u	d0,d5,#0,#13
.L403:
	extr.u	d1,d5,#13,#3
.L405:
	mov	d15,#6
	jeq	d15,d4,.L24
.L731:
	mov	d15,#9
	jeq	d15,d4,.L25
.L732:
	mov	d15,#14
	jeq	d15,d4,.L26
.L733:
	mov	d15,#16
	jeq	d15,d4,.L27
.L734:
	mov	d15,#83
	jeq	d15,d4,.L28
	j	.L29
.L27:
	sh	d0,#3
.L404:
	and	d15,d1,#1
.L735:
	sh	d15,#2
.L736:
	or	d0,d15
.L737:
	movh	d2,#28688
.L406:
	or	d2,d0
.L738:
	j	.L30
.L26:
	sh	d0,#4
.L407:
	and	d15,d1,#3
.L739:
	sh	d15,#2
.L740:
	or	d0,d15
.L741:
	insert	d2,d0,#7,#28,#3
.L408:
	j	.L31
.L25:
	sh	d0,#4
.L409:
	and	d15,d1,#1
.L742:
	sh	d15,#3
.L743:
	or	d0,d15
.L744:
	movh	d2,#24592
.L410:
	or	d2,d0
.L745:
	j	.L32
.L24:
	sh	d0,#4
.L411:
	and	d15,d1,#3
.L746:
	sh	d15,#2
.L747:
	or	d0,d15
.L748:
	insert	d2,d0,#3,#29,#2
.L412:
	j	.L33
.L28:
	sh	d0,#5
.L413:
	and	d15,d1,#3
.L749:
	sh	d15,#3
.L750:
	or	d0,d15
.L751:
	mov	d2,#8192
	addih	d2,d2,#61441
.L414:
	or	d2,d0
.L752:
	j	.L34
.L29:
	mov	d2,#0
.L34:
.L33:
.L32:
.L31:
.L30:
	j	.L35
.L35:
	ret
.L312:
	
__IfxMtu_getSystemAddress_function_end:
	.size	IfxMtu_getSystemAddress,__IfxMtu_getSystemAddress_function_end-IfxMtu_getSystemAddress
.L175:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_getTrackedSramAddresses',code,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.text.IfxMtu.IfxMtu_getTrackedSramAddresses'
	.align	2
	
	.global	IfxMtu_getTrackedSramAddresses
; Function IfxMtu_getTrackedSramAddresses
.L118:
IfxMtu_getTrackedSramAddresses:	.type	func
	mov	d15,#256
.L757:
	mul	d4,d15
.L415:
	mov	d15,#4096
	addih	d15,d15,#61446
.L416:
	add	d4,d15
.L758:
	mov.a	a15,d4
.L417:
	ld.hu	d15,[a15]16
.L759:
	sha	d15,#-5
.L760:
	and	d0,d15,#31
.L418:
	mov	d2,#0
.L419:
	mov	d1,#0
.L421:
	j	.L36
.L37:
	mov	d15,#1
.L761:
	sha	d15,d15,d1
.L762:
	and	d15,d0
.L763:
	jeq	d15,#0,.L38
.L764:
	mul	d15,d2,#2
	addsc.a	a2,a4,d15,#0
.L765:
	mul	d15,d1,#2
	addsc.a	a5,a15,d15,#0
.L766:
	ld.hu	d15,[a5]18
.L767:
	st.h	[a2],d15
.L768:
	add	d2,#1
.L420:
	extr.u	d2,d2,#0,#8
.L38:
	add	d1,#1
.L36:
	jlt	d1,#5,.L37
.L769:
	j	.L39
.L39:
	ret
.L319:
	
__IfxMtu_getTrackedSramAddresses_function_end:
	.size	IfxMtu_getTrackedSramAddresses,__IfxMtu_getTrackedSramAddresses_function_end-IfxMtu_getTrackedSramAddresses
.L180:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_readSramAddress',code,cluster('IfxMtu_readSramAddress')
	.sect	'.text.IfxMtu.IfxMtu_readSramAddress'
	.align	2
	
	.global	IfxMtu_readSramAddress
; Function IfxMtu_readSramAddress
.L120:
IfxMtu_readSramAddress:	.type	func
	mov	d8,d4
.L424:
	mov	d15,#256
.L601:
	mul	d15,d8
.L602:
	mov	d0,#4096
	addih	d0,d0,#61446
.L425:
	add	d15,d0
.L603:
	mov.a	a15,d15
.L427:
	mov	d0,#16384
.L428:
	or	d15,d0,#8
.L426:
	st.h	[a15]4,d15
.L604:
	mov	d15,#4097
.L605:
	st.h	[a15],d15
.L606:
	mov	d15,#0
.L607:
	st.h	[a15]2,d15
.L608:
	st.h	[a15]8,d5
.L609:
	or	d15,d0,#8
.L610:
	or	d15,#1
.L611:
	st.h	[a15]4,d15
.L612:
	or	d15,d0,#8
.L613:
	st.h	[a15]4,d15
.L614:
	mov	d4,#256
.L422:
	mov	d5,#1
.L423:
	mov	d6,d8
.L430:
	call	IfxMtu_waitForMbistDone
.L429:
	j	.L40
.L41:
	nop
.L40:
	mov	d15,#256
.L615:
	mul	d15,d8
.L616:
	mov	d0,#4096
	addih	d0,d0,#61446
.L431:
	add	d15,d0
.L617:
	mov.a	a15,d15
.L433:
	ld.hu	d15,[a15]6
.L432:
	and	d15,#1
.L434:
	j	.L42
.L42:
	jeq	d15,#0,.L41
.L618:
	ret
.L252:
	
__IfxMtu_readSramAddress_function_end:
	.size	IfxMtu_readSramAddress,__IfxMtu_readSramAddress_function_end-IfxMtu_readSramAddress
.L150:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_runNonDestructiveInversionTest',code,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.text.IfxMtu.IfxMtu_runNonDestructiveInversionTest'
	.align	2
	
	.global	IfxMtu_runNonDestructiveInversionTest
; Function IfxMtu_runNonDestructiveInversionTest
.L122:
IfxMtu_runNonDestructiveInversionTest:	.type	func
	mov	d8,d4
.L436:
	mov	d10,d5
.L437:
	mov	d9,d6
.L438:
	mov	d11,d7
.L440:
	mov.aa	a12,a4
.L442:
	mov	d0,#256
.L623:
	mul	d0,d8
.L624:
	mov	d1,#4096
	addih	d1,d1,#61446
.L443:
	add	d0,d1
.L625:
	mov.a	a15,d0
.L444:
	mov	d12,#0
.L446:
	mov	d13,#0
.L447:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L435:
	mov	d14,d2
.L271:
	movh.a	a2,#61443
	ld.w	d15,[a2]@los(0xf00360f0)
	and	d15,#1
.L626:
	j	.L43
.L43:
	jne	d15,#1,.L44
.L627:
	mov	d4,d14
.L448:
	call	IfxScuWdt_clearSafetyEndinit
.L449:
	mov	d13,#1
.L44:
	sha	d15,d8,#-5
.L628:
	mul	d15,d15,#4
.L629:
	mov	d0,#16
	addih	d0,d0,#61446
.L450:
	add	d15,d0
.L630:
	mov.a	a2,d15
.L452:
	mov	d0,#1
.L631:
	and	d15,d8,#31
.L451:
	sha	d0,d0,d15
.L453:
	ld.w	d15,[a2]
.L632:
	or	d15,d0
	st.w	[a2],d15
.L272:
	j	.L45
.L46:
.L45:
	sha	d15,d8,#-5
.L633:
	mul	d15,d15,#4
.L634:
	mov	d0,#56
	addih	d0,d0,#61446
.L454:
	add	d15,d0
.L635:
	mov.a	a2,d15
.L456:
	mov	d0,#1
.L636:
	and	d15,d8,#31
.L455:
	sha	d0,d0,d15
.L458:
	ld.w	d15,[a2]
.L637:
	and	d15,d0
.L638:
	ne	d15,d15,#0
.L639:
	j	.L47
.L47:
	jne	d15,#0,.L46
.L640:
	mov	d15,#16389
.L641:
	st.h	[a15],d15
.L642:
	mov	d15,#20480
.L643:
	st.h	[a15]2,d15
.L644:
	sha	d15,d10,#15
.L645:
	sha	d9,#7
.L439:
	or	d15,d9
.L646:
	or	d11,d15
.L441:
	st.h	[a15]8,d11
.L647:
	mov	d15,#16385
.L648:
	st.h	[a15]4,d15
.L649:
	ld.hu	d15,[a15]4
.L650:
	insert	d15,d15,#0,#0,#1
	st.h	[a15]4,d15
.L651:
	mov	d4,d14
.L459:
	call	IfxScuWdt_setSafetyEndinit
.L457:
	mul	d15,d8,#12
.L652:
	movh.a	a2,#@his(IfxMtu_sramTable)
	lea	a2,[a2]@los(IfxMtu_sramTable)
.L653:
	addsc.a	a2,a2,d15,#0
.L654:
	ld.w	d4,[a2]8
.L655:
	mov	d5,#4
.L656:
	mov	d6,d8
.L460:
	call	IfxMtu_waitForMbistDone
.L461:
	j	.L48
.L49:
	nop
.L48:
	mov	d15,#256
.L657:
	mul	d15,d8
.L658:
	mov	d0,#4096
	addih	d0,d0,#61446
.L462:
	add	d15,d0
.L659:
	mov.a	a2,d15
.L464:
	ld.hu	d15,[a2]6
.L463:
	and	d15,#1
.L465:
	j	.L50
.L50:
	jeq	d15,#0,.L49
.L660:
	mov	d4,d14
.L466:
	call	IfxScuWdt_clearSafetyEndinit
.L283:
	mov	d15,#256
.L661:
	mul	d15,d8
.L662:
	mov	d0,#4096
	addih	d0,d0,#61446
.L467:
	add	d15,d0
.L663:
	mov.a	a2,d15
.L469:
	ld.hu	d15,[a2]16
.L468:
	mov.u	d0,#32782
.L664:
	and	d15,d0
.L665:
	mov	d0,#0
.L666:
	lt.u	d15,d0,d15
.L667:
	j	.L51
.L51:
	jeq	d15,#0,.L52
.L668:
	ld.hu	d15,[a15]18
.L669:
	st.h	[a12],d15
.L670:
	mov	d12,#1
.L52:
	sha	d15,d8,#-5
.L671:
	mul	d15,d15,#4
.L672:
	mov	d0,#16
	addih	d0,d0,#61446
.L470:
	add	d15,d0
.L673:
	mov.a	a15,d15
.L445:
	mov	d0,#1
.L674:
	and	d15,d8,#31
.L471:
	sha	d0,d0,d15
.L472:
	ld.w	d15,[a15]
.L675:
	mov	d1,#-1
	xor	d0,d1
.L473:
	and	d15,d0
	st.w	[a15],d15
.L288:
	j	.L53
.L54:
.L53:
	sha	d15,d8,#-5
.L676:
	mul	d15,d15,#4
.L677:
	mov	d0,#56
	addih	d0,d0,#61446
.L474:
	add	d15,d0
.L678:
	mov.a	a15,d15
.L476:
	mov	d0,#1
.L679:
	and	d15,d8,#31
.L475:
	sha	d0,d0,d15
.L477:
	ld.w	d15,[a15]
.L680:
	and	d15,d0
.L681:
	ne	d15,d15,#0
.L682:
	j	.L55
.L55:
	jne	d15,#0,.L54
.L683:
	jne	d13,#1,.L56
.L684:
	mov	d4,d14
.L478:
	call	IfxScuWdt_setSafetyEndinit
.L56:
	mov	d2,d12
.L479:
	j	.L57
.L57:
	ret
.L260:
	
__IfxMtu_runNonDestructiveInversionTest_function_end:
	.size	IfxMtu_runNonDestructiveInversionTest,__IfxMtu_runNonDestructiveInversionTest_function_end-IfxMtu_runNonDestructiveInversionTest
.L155:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_writeSramAddress',code,cluster('IfxMtu_writeSramAddress')
	.sect	'.text.IfxMtu.IfxMtu_writeSramAddress'
	.align	2
	
	.global	IfxMtu_writeSramAddress
; Function IfxMtu_writeSramAddress
.L124:
IfxMtu_writeSramAddress:	.type	func
	mov	e8,d5,d4
.L689:
	mov	d0,#256
.L481:
	mul	d0,d8
.L482:
	mov	d1,#4096
	addih	d1,d1,#61446
.L483:
	add	d0,d1
.L690:
	mov.a	a15,d0
.L484:
	mov	d10,#0
.L486:
	call	IfxScuWdt_getSafetyWatchdogPassword
.L480:
	mov	d11,d2
.L300:
	movh.a	a2,#61443
	ld.w	d15,[a2]@los(0xf00360f0)
	and	d15,#1
.L691:
	j	.L58
.L58:
	jne	d15,#1,.L59
.L692:
	mov	d4,d11
.L487:
	call	IfxScuWdt_clearSafetyEndinit
.L488:
	mov	d10,#1
.L59:
	mov	d0,#16384
.L489:
	or	d15,d0,#8
.L693:
	st.h	[a15]4,d15
.L694:
	mov	d15,#4096
.L695:
	st.h	[a15],d15
.L696:
	mov	d15,#0
.L697:
	st.h	[a15]2,d15
.L490:
	st.h	[a15]8,d9
.L491:
	or	d15,d0,#8
.L698:
	or	d15,#1
.L699:
	st.h	[a15]4,d15
.L700:
	or	d15,d0,#8
.L701:
	st.h	[a15]4,d15
.L702:
	jne	d10,#1,.L60
.L703:
	mov	d4,d11
.L492:
	call	IfxScuWdt_setSafetyEndinit
.L60:
	mul	d15,d8,#12
.L493:
	movh.a	a15,#@his(IfxMtu_sramTable)
.L485:
	lea	a15,[a15]@los(IfxMtu_sramTable)
.L704:
	addsc.a	a15,a15,d15,#0
.L705:
	ld.w	d4,[a15]8
.L706:
	mov	d5,#1
.L494:
	mov	d6,d8
.L495:
	call	IfxMtu_waitForMbistDone
.L496:
	j	.L61
.L62:
	nop
.L61:
	mov	d15,#256
.L497:
	mul	d15,d8
.L498:
	mov	d0,#4096
	addih	d0,d0,#61446
.L499:
	add	d15,d0
.L707:
	mov.a	a15,d15
.L501:
	ld.hu	d15,[a15]6
.L500:
	and	d15,#1
.L502:
	j	.L63
.L63:
	jeq	d15,#0,.L62
.L708:
	ret
.L294:
	
__IfxMtu_writeSramAddress_function_end:
	.size	IfxMtu_writeSramAddress,__IfxMtu_writeSramAddress_function_end-IfxMtu_writeSramAddress
.L160:
	; End of function
	
	.sdecl	'.text.IfxMtu.IfxMtu_waitForMbistDone',code,cluster('IfxMtu_waitForMbistDone')
	.sect	'.text.IfxMtu.IfxMtu_waitForMbistDone'
	.align	2
	
; Function IfxMtu_waitForMbistDone
.L126:
IfxMtu_waitForMbistDone:	.type	func
	sub.a	a10,#8
.L503:
	mov	e8,d5,d4
.L774:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036032)
	and	d0,d15,#15
.L775:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036031)
	and	d15,#15
.L776:
	div	e0,d0,d15
.L505:
	mul	d0,d9
.L506:
	mov	d15,#22
	jeq	d15,d6,.L64
.L777:
	mov	d15,#28
	jeq	d15,d6,.L65
.L778:
	mov	d15,#29
	jeq	d15,d6,.L66
.L779:
	mov	d15,#30
	jeq	d15,d6,.L67
.L780:
	mov	d15,#31
	jeq	d15,d6,.L68
.L781:
	mov	d15,#32
	jeq	d15,d6,.L69
.L782:
	mov	d15,#33
	jeq	d15,d6,.L70
.L783:
	mov	d15,#34
	jeq	d15,d6,.L71
.L784:
	mov	d15,#36
	jeq	d15,d6,.L72
.L785:
	mov	d15,#38
	jeq	d15,d6,.L73
.L786:
	mov	d15,#39
	jeq	d15,d6,.L74
.L787:
	mov	d15,#40
	jeq	d15,d6,.L75
.L788:
	mov	d15,#46
	jeq	d15,d6,.L76
.L789:
	mov	d15,#47
	jeq	d15,d6,.L77
.L790:
	mov	d15,#48
	jeq	d15,d6,.L78
.L791:
	mov	d15,#49
	jeq	d15,d6,.L79
.L792:
	mov	d15,#50
	jeq	d15,d6,.L80
.L793:
	mov	d15,#51
	jeq	d15,d6,.L81
.L794:
	mov	d15,#52
	jeq	d15,d6,.L82
.L795:
	mov	d15,#53
	jeq	d15,d6,.L83
.L796:
	mov	d15,#78
	jeq	d15,d6,.L84
.L797:
	mov	d15,#80
	jeq	d15,d6,.L85
.L798:
	mov	d15,#81
	jeq	d15,d6,.L86
.L799:
	mov	d15,#84
	jeq	d15,d6,.L87
.L800:
	mov	d15,#85
	jeq	d15,d6,.L88
.L801:
	mov	d15,#86
	jeq	d15,d6,.L89
.L802:
	mov	d15,#87
	jeq	d15,d6,.L90
	j	.L91
.L65:
.L66:
.L67:
.L68:
.L69:
.L70:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	extr.u	d15,d15,#4,#4
.L803:
	mul	d0,d15
.L804:
	j	.L92
.L64:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036035)
	extr.u	d15,d15,#4,#4
.L805:
	mul	d0,d15
.L806:
	j	.L93
.L72:
.L71:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036030)
	and	d15,#15
.L807:
	mul	d0,d15
.L808:
	j	.L94
.L73:
.L74:
	call	IfxScuCcu_getSriFrequency
.L504:
	mov	d10,d2
.L336:
	call	IfxScuCcu_getPllErayVcoFrequency
.L809:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003602d)
	and	d15,#15
.L810:
	add	d15,#1
	itof	d15,d15
.L811:
	div.f	d15,d2,d15
.L507:
	j	.L95
.L95:
	div.f	d15,d10,d15
.L337:
	utof	d0,d9
.L508:
	mul.f	d15,d15,d0
	ftouz	d0,d15
.L509:
	j	.L96
.L75:
	call	IfxScuCcu_getSriFrequency
.L510:
	mov	d10,d2
.L341:
	call	IfxScuCcu_getPllErayVcoFrequency
.L812:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf003602d)
	and	d15,#15
.L813:
	add	d15,#1
	itof	d15,d15
.L814:
	div.f	d15,d2,d15
.L511:
	j	.L97
.L97:
	div.f	d15,d10,d15
.L342:
	utof	d0,d9
.L512:
	mul.f	d15,d15,d0
.L815:
	movh	d0,#16512
.L816:
	mul.f	d15,d15,d0
	ftouz	d0,d15
.L513:
	j	.L98
.L76:
.L77:
.L78:
.L79:
.L80:
.L81:
.L82:
.L83:
.L87:
.L88:
.L89:
.L90:
.L84:
.L85:
.L86:
	movh.a	a15,#61443
	ld.bu	d15,[a15]@los(0xf0036040)
	and	d15,#15
.L817:
	mul	d0,d15
.L818:
	j	.L99
.L91:
	j	.L100
.L100:
.L99:
.L98:
.L96:
.L94:
.L93:
.L92:
	jne	d9,#4,.L101
.L515:
	mov	d15,#30
.L516:
	madd	d15,d15,d8,d0
.L517:
	st.w	[a10],d15
.L819:
	j	.L102
.L101:
	mov	d15,#4
.L518:
	div.u	e2,d8,d15
.L519:
	mov	d15,#30
.L820:
	madd	d15,d15,d2,d0
.L821:
	st.w	[a10],d15
.L102:
	ld.w	d15,[a10]
.L822:
	mov	d0,#3
.L514:
	div.u	e0,d15,d0
.L823:
	st.w	[a10],d0
.L824:
	j	.L103
.L104:
	nop
.L103:
	ld.w	d15,[a10]
	ld.w	d0,[a10]
.L825:
	add	d0,#-1
	st.w	[a10],d0
.L826:
	jne	d15,#0,.L104
.L827:
	ret
.L328:
	
__IfxMtu_waitForMbistDone_function_end:
	.size	IfxMtu_waitForMbistDone,__IfxMtu_waitForMbistDone_function_end-IfxMtu_waitForMbistDone
.L185:
	; End of function
	
	.calls	'IfxMtu_clearSram','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxMtu_clearSram','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxMtu_clearSram','IfxMtu_clearSramStart'
	.calls	'IfxMtu_clearSram','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxMtu_clearSram','IfxMtu_waitForMbistDone'
	.calls	'IfxMtu_clearSram','IfxMtu_clearSramContinue'
	.calls	'IfxMtu_clearSramContinue','IfxMtu_readSramAddress'
	.calls	'IfxMtu_readSramAddress','IfxMtu_waitForMbistDone'
	.calls	'IfxMtu_runNonDestructiveInversionTest','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxMtu_runNonDestructiveInversionTest','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxMtu_runNonDestructiveInversionTest','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxMtu_runNonDestructiveInversionTest','IfxMtu_waitForMbistDone'
	.calls	'IfxMtu_writeSramAddress','IfxScuWdt_getSafetyWatchdogPassword'
	.calls	'IfxMtu_writeSramAddress','IfxScuWdt_clearSafetyEndinit'
	.calls	'IfxMtu_writeSramAddress','IfxScuWdt_setSafetyEndinit'
	.calls	'IfxMtu_writeSramAddress','IfxMtu_waitForMbistDone'
	.calls	'IfxMtu_waitForMbistDone','IfxScuCcu_getSriFrequency'
	.calls	'IfxMtu_waitForMbistDone','IfxScuCcu_getPllErayVcoFrequency'
	.calls	'IfxMtu_clearErrorTracking','',0
	.calls	'IfxMtu_clearSram','',0
	.calls	'IfxMtu_clearSramContinue','',0
	.calls	'IfxMtu_clearSramStart','',0
	.calls	'IfxMtu_enableErrorTracking','',0
	.calls	'IfxMtu_getSystemAddress','',0
	.calls	'IfxMtu_getTrackedSramAddresses','',0
	.calls	'IfxMtu_readSramAddress','',0
	.calls	'IfxMtu_runNonDestructiveInversionTest','',0
	.calls	'IfxMtu_writeSramAddress','',0
	.extern	IfxScuWdt_clearSafetyEndinit
	.extern	IfxScuWdt_setSafetyEndinit
	.extern	IfxScuWdt_getSafetyWatchdogPassword
	.extern	IfxScuCcu_getPllErayVcoFrequency
	.extern	IfxScuCcu_getSriFrequency
	.extern	IfxMtu_sramTable
	.calls	'IfxMtu_waitForMbistDone','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L128:
	.word	85775
	.half	3
	.word	.L129
	.byte	4
.L127:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L130
	.byte	2,1,1,3
	.word	230
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	233
	.byte	6,0,7
	.byte	'__fract',0,4,128,1
.L339:
	.byte	7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	278
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	290
	.byte	6,0,4
	.byte	'Ifx__stopPerfCounters',0,3,1,172,2,17,1,1,6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	402
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	376
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	408
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	408
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	376
	.byte	6,0,7
	.byte	'unsigned int',0,4,7
.L326:
	.byte	7
	.byte	'int',0,4,5,7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,4,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	517
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	517
	.byte	16,0,2,35,0,0,12,4,247,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	533
	.byte	4,2,35,0,0
.L189:
	.byte	7
	.byte	'unsigned char',0,1,8
.L191:
	.byte	7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,4,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,4,255,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	708
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,4,137,9,16,4,11
	.byte	'AE',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,4,135,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	952
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_SCU_WDTCPU',0,4,175,15,25,12,13
	.byte	'CON0',0
	.word	629
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	912
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	1143
	.byte	4,2,35,8,0,14
	.word	1183
	.byte	3
	.word	1246
	.byte	4
	.byte	'IfxScuWdt_clearCpuEndinitInline',0,3,3,181,3,17,1,1,5
	.byte	'watchdog',0,3,181,3,65
	.word	1251
	.byte	5
	.byte	'password',0,3,181,3,82
	.word	686
	.byte	6,0,4
	.byte	'IfxScuWdt_setCpuEndinitInline',0,3,3,140,4,17,1,1,5
	.byte	'watchdog',0,3,140,4,63
	.word	1251
	.byte	5
	.byte	'password',0,3,140,4,80
	.word	686
	.byte	6,0,8
	.byte	'IfxScuWdt_getCpuWatchdogPasswordInline',0,3,3,227,3,19
	.word	686
	.byte	1,1,5
	.byte	'watchdog',0,3,227,3,74
	.word	1251
	.byte	6,0
.L193:
	.byte	8
	.byte	'IfxScuWdt_getSafetyWatchdogEndInit',0,3,3,247,3,20
	.word	669
	.byte	1,1
.L195:
	.byte	6,0,10
	.byte	'_Ifx_P_OUT_Bits',0,6,143,3,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,181,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1530
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,6,169,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,133,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1846
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,6,110,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,148,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2417
	.byte	4,2,35,0,0,15,4
	.word	669
	.byte	16,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,6,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,6,164,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2545
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,6,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,6,180,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2760
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,6,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,6,188,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2975
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,6,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	669
	.byte	5,0,2,35,3,0,12,6,172,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3192
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,6,118,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,156,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3412
	.byte	4,2,35,0,0,15,24
	.word	669
	.byte	16,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,6,205,3,16,4,11
	.byte	'PD0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,205,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3735
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,6,226,3,16,4,11
	.byte	'PD8',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	669
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,213,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4039
	.byte	4,2,35,0,0,15,8
	.word	669
	.byte	16,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,6,88,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,140,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4364
	.byte	4,2,35,0,0,15,12
	.word	669
	.byte	16,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,6,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,197,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4704
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,6,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,189,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5070
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,6,206,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,12,6,149,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5356
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,6,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,6,165,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5503
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,6,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	494
	.byte	20,0,2,35,0,0,12,6,173,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5672
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,6,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,157,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5844
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,6,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	686
	.byte	12,0,2,35,2,0,12,6,229,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6019
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,6,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,12,6,245,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,6,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,12,6,253,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6367
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,6,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,237,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6543
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,6,249,2,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,141,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6699
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,6,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,221,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7032
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,6,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,12,6,196,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7380
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,6,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,6,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,12,6,204,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7504
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	7588
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,6,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,12,6,213,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7768
	.byte	4,2,35,0,0,15,76
	.word	669
	.byte	16,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,12,6,132,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8021
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,12,6,252,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8108
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,6,229,5,25,128,2,13
	.byte	'OUT',0
	.word	1806
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	2377
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	2496
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2536
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	2720
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	2935
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	3152
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	3372
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	2536
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	3686
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	3726
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3999
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	4315
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	4355
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	4655
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	4695
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	5030
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	5316
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	4355
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	5463
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	5632
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	5804
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	5979
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	6153
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	6327
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	6503
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	6659
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	6992
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	7340
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	4355
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	7464
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	7713
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	7972
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	8012
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	8068
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	8635
	.byte	4,3,35,252,1,0,14
	.word	8675
	.byte	3
	.word	9278
	.byte	17,5,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,5,208,4,17,1,1,5
	.byte	'port',0,5,208,4,44
	.word	9283
	.byte	5
	.byte	'pinIndex',0,5,208,4,56
	.word	669
	.byte	5
	.byte	'action',0,5,208,4,80
	.word	9288
	.byte	6,0
.L335:
	.byte	8
	.byte	'IfxScuCcu_getPll2ErayFrequency',0,3,7,201,8,20
	.word	290
	.byte	1,1
.L338:
	.byte	6,0,17,9,156,1,9,1,18
	.byte	'IfxCpu_ResourceCpu_0',0,0,18
	.byte	'IfxCpu_ResourceCpu_1',0,1,18
	.byte	'IfxCpu_ResourceCpu_none',0,2,0,8
	.byte	'IfxCpu_getCoreIndex',0,3,8,141,6,31
	.word	9514
	.byte	1,1,6,0,8
	.byte	'IfxCpu_areInterruptsEnabled',0,3,8,139,5,20
	.word	669
	.byte	1,1,6,0
.L213:
	.byte	7
	.byte	'unsigned long int',0,4,7,8
	.byte	'IfxCpu_getPerformanceCounter',0,3,8,161,6,19
	.word	9669
	.byte	1,1,5
	.byte	'address',0,8,161,6,55
	.word	686
	.byte	6,0,8
	.byte	'IfxCpu_getPerformanceCounterStickyOverflow',0,3,8,190,6,20
	.word	669
	.byte	1,1,5
	.byte	'address',0,8,190,6,70
	.word	686
	.byte	6,0,4
	.byte	'IfxCpu_updatePerformanceCounter',0,3,8,172,8,17,1,1,5
	.byte	'address',0,8,172,8,56
	.word	9669
	.byte	5
	.byte	'count',0,8,172,8,72
	.word	9669
	.byte	19,6,0,0
.L187:
	.byte	17,11,86,9,1,18
	.byte	'IfxMtu_MbistSel_none',0,127,18
	.byte	'IfxMtu_MbistSel_cpu1Dspr',0,6,18
	.byte	'IfxMtu_MbistSel_cpu1Dtag',0,8,18
	.byte	'IfxMtu_MbistSel_cpu1Pspr',0,9,18
	.byte	'IfxMtu_MbistSel_cpu1Ptag',0,11,18
	.byte	'IfxMtu_MbistSel_cpu0Dspr',0,14,18
	.byte	'IfxMtu_MbistSel_cpu0Pspr',0,16,18
	.byte	'IfxMtu_MbistSel_cpu0Ptag',0,17,18
	.byte	'IfxMtu_MbistSel_ethermac',0,22,18
	.byte	'IfxMtu_MbistSel_mod4',0,26,18
	.byte	'IfxMtu_MbistSel_gtmFifo',0,28,18
	.byte	'IfxMtu_MbistSel_gtmMcs0',0,29,18
	.byte	'IfxMtu_MbistSel_gtmMcs1',0,30,18
	.byte	'IfxMtu_MbistSel_gtmDpll1a',0,31,18
	.byte	'IfxMtu_MbistSel_gtmDpll1b',0,32,18
	.byte	'IfxMtu_MbistSel_gtmDpll2',0,33,18
	.byte	'IfxMtu_MbistSel_psi5',0,34,18
	.byte	'IfxMtu_MbistSel_mcan',0,36,18
	.byte	'IfxMtu_MbistSel_erayObf',0,38,18
	.byte	'IfxMtu_MbistSel_erayIbfTbf',0,39,18
	.byte	'IfxMtu_MbistSel_erayMbf',0,40,18
	.byte	'IfxMtu_MbistSel_stdbyRam1',0,44,18
	.byte	'IfxMtu_MbistSel_mcds',0,45,18
	.byte	'IfxMtu_MbistSel_emem0',0,46,18
	.byte	'IfxMtu_MbistSel_emem1',0,47,18
	.byte	'IfxMtu_MbistSel_emem2',0,48,18
	.byte	'IfxMtu_MbistSel_emem3',0,49,18
	.byte	'IfxMtu_MbistSel_emem4',0,50,18
	.byte	'IfxMtu_MbistSel_emem5',0,51,18
	.byte	'IfxMtu_MbistSel_emem6',0,52,18
	.byte	'IfxMtu_MbistSel_emem7',0,53,18
	.byte	'IfxMtu_MbistSel_cifJpeg1_4',0,206,0,18
	.byte	'IfxMtu_MbistSel_cifJpeg3',0,208,0,18
	.byte	'IfxMtu_MbistSel_cifCif',0,209,0,18
	.byte	'IfxMtu_MbistSel_stdbyRam2',0,210,0,18
	.byte	'IfxMtu_MbistSel_dma',0,211,0,18
	.byte	'IfxMtu_MbistSel_ememXtm0',0,212,0,18
	.byte	'IfxMtu_MbistSel_ememXtm1',0,213,0,18
	.byte	'IfxMtu_MbistSel_fft0',0,214,0,18
	.byte	'IfxMtu_MbistSel_fft1',0,215,0,0
.L205:
	.byte	4
	.byte	'IfxMtu_disableMbistShell',0,3,10,235,3,17,1,1
.L208:
	.byte	5
	.byte	'mbistSel',0,10,235,3,58
	.word	9900
.L210:
	.byte	6,0
.L224:
	.byte	4
	.byte	'IfxMtu_enableMbistShell',0,3,10,243,3,17,1,1
.L227:
	.byte	5
	.byte	'mbistSel',0,10,243,3,57
	.word	9900
.L229:
	.byte	6,0
.L215:
	.byte	8
	.byte	'IfxMtu_isAutoInitRunning',0,3,10,144,4,20
	.word	669
	.byte	1,1
.L216:
	.byte	5
	.byte	'mbistSel',0,10,144,4,61
	.word	9900
.L218:
	.byte	6,0
.L196:
	.byte	8
	.byte	'IfxMtu_isMbistDone',0,3,10,166,4,20
	.word	669
	.byte	1,1
.L197:
	.byte	5
	.byte	'mbistSel',0,10,166,4,55
	.word	9900
.L199:
	.byte	6,0
.L282:
	.byte	8
	.byte	'IfxMtu_checkErrorFlags',0,3,10,182,4,20
	.word	669
	.byte	1,1
.L284:
	.byte	5
	.byte	'mbistSel',0,10,182,4,59
	.word	9900
.L286:
	.byte	6,0,20
	.byte	'__nop',0,1,1,1,1,21
	.word	238
	.byte	22
	.word	264
	.byte	6,0,21
	.word	299
	.byte	22
	.word	331
	.byte	6,0,21
	.word	344
	.byte	6,0,21
	.word	413
	.byte	22
	.word	432
	.byte	6,0,21
	.word	448
	.byte	22
	.word	463
	.byte	22
	.word	477
	.byte	6,0,21
	.word	1256
	.byte	22
	.word	1296
	.byte	22
	.word	1314
	.byte	6,0,21
	.word	1334
	.byte	22
	.word	1372
	.byte	22
	.word	1390
	.byte	6,0,23
	.byte	'IfxScuWdt_clearSafetyEndinit',0,3,229,1,17,1,1,1,1,5
	.byte	'password',0,3,229,1,53
	.word	686
	.byte	0,23
	.byte	'IfxScuWdt_setSafetyEndinit',0,3,249,1,17,1,1,1,1,5
	.byte	'password',0,3,249,1,51
	.word	686
	.byte	0,21
	.word	1410
	.byte	22
	.word	1461
	.byte	6,0,21
	.word	1481
	.byte	6,0,24
	.byte	'IfxScuWdt_getSafetyWatchdogPassword',0,3,143,3,19
	.word	686
	.byte	1,1,1,1,21
	.word	9391
	.byte	22
	.word	9419
	.byte	22
	.word	9433
	.byte	22
	.word	9451
	.byte	6,0,21
	.word	9469
	.byte	6,0,24
	.byte	'IfxScuCcu_getPllErayVcoFrequency',0,7,155,7,20
	.word	290
	.byte	1,1,1,1,24
	.byte	'IfxScuCcu_getSriFrequency',0,7,185,7,20
	.word	290
	.byte	1,1,1,1,21
	.word	9593
	.byte	6,0,21
	.word	9627
	.byte	6,0,21
	.word	9690
	.byte	22
	.word	9731
	.byte	6,0,21
	.word	9750
	.byte	22
	.word	9805
	.byte	6,0,21
	.word	9824
	.byte	22
	.word	9864
	.byte	22
	.word	9881
	.byte	19,6,0,0,21
	.word	10939
	.byte	22
	.word	10972
	.byte	6,0,21
	.word	10992
	.byte	22
	.word	11024
	.byte	6,0,21
	.word	11044
	.byte	22
	.word	11081
	.byte	6,0,21
	.word	11101
	.byte	22
	.word	11132
	.byte	6,0,7
	.byte	'short int',0,2,5,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_MC_CONFIG0_Bits',0,12,45,16,2,11
	.byte	'ACCSTYPE',0,2
	.word	11724
	.byte	8,8,2,35,0,11
	.byte	'reserved_8',0,2
	.word	11724
	.byte	4,4,2,35,0,11
	.byte	'NUMACCS',0,2
	.word	11724
	.byte	4,0,2,35,0,0,12,12,150,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	11746
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_CONFIG1_Bits',0,12,53,16,2,11
	.byte	'ACCSPAT',0,2
	.word	11724
	.byte	8,8,2,35,0,11
	.byte	'SELFASTB',0,2
	.word	11724
	.byte	4,4,2,35,0,11
	.byte	'AG_MOD',0,2
	.word	11724
	.byte	4,0,2,35,0,0,12,12,158,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	11874
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_MCONTROL_Bits',0,12,100,16,2,11
	.byte	'START',0,2
	.word	11724
	.byte	1,15,2,35,0,11
	.byte	'RESUME',0,2
	.word	11724
	.byte	1,14,2,35,0,11
	.byte	'ESTF',0,2
	.word	11724
	.byte	1,13,2,35,0,11
	.byte	'DIR',0,2
	.word	11724
	.byte	1,12,2,35,0,11
	.byte	'DINIT',0,2
	.word	11724
	.byte	1,11,2,35,0,11
	.byte	'RCADR',0,2
	.word	11724
	.byte	1,10,2,35,0,11
	.byte	'ROWTOG',0,2
	.word	11724
	.byte	1,9,2,35,0,11
	.byte	'BITTOG',0,2
	.word	11724
	.byte	1,8,2,35,0,11
	.byte	'GP_BASE',0,2
	.word	11724
	.byte	1,7,2,35,0,11
	.byte	'FAILDMP',0,2
	.word	11724
	.byte	1,6,2,35,0,11
	.byte	'reserved_10',0,2
	.word	11724
	.byte	6,0,2,35,0,0,12,12,190,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	11998
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_MSTATUS_Bits',0,12,116,16,2,11
	.byte	'DONE',0,2
	.word	11724
	.byte	1,15,2,35,0,11
	.byte	'FAIL',0,2
	.word	11724
	.byte	1,14,2,35,0,11
	.byte	'FDA',0,2
	.word	11724
	.byte	1,13,2,35,0,11
	.byte	'SFAIL',0,2
	.word	11724
	.byte	1,12,2,35,0,11
	.byte	'reserved_4',0,2
	.word	11724
	.byte	12,0,2,35,0,0,12,12,198,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	12263
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_RANGE_Bits',0,12,126,16,2,11
	.byte	'ADDR',0,2
	.word	11724
	.byte	15,1,2,35,0,11
	.byte	'RAEN',0,2
	.word	11724
	.byte	1,0,2,35,0,0,12,12,206,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	12416
	.byte	2,2,35,0,0,15,2
	.word	669
	.byte	16,1,0,10
	.byte	'_Ifx_MC_REVID_Bits',0,12,139,1,16,2,11
	.byte	'REV_ID',0,2
	.word	11724
	.byte	16,0,2,35,0,0,12,12,222,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	12522
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_ECCS_Bits',0,12,78,16,2,11
	.byte	'CENE',0,2
	.word	11724
	.byte	1,15,2,35,0,11
	.byte	'UENE',0,2
	.word	11724
	.byte	1,14,2,35,0,11
	.byte	'AENE',0,2
	.word	11724
	.byte	1,13,2,35,0,11
	.byte	'ECE',0,2
	.word	11724
	.byte	1,12,2,35,0,11
	.byte	'TRE',0,2
	.word	11724
	.byte	1,11,2,35,0,11
	.byte	'BFLE',0,2
	.word	11724
	.byte	1,10,2,35,0,11
	.byte	'SFLE',0,2
	.word	11724
	.byte	2,8,2,35,0,11
	.byte	'ECCMAP',0,2
	.word	11724
	.byte	2,6,2,35,0,11
	.byte	'TC_WAY_SEL',0,2
	.word	11724
	.byte	2,4,2,35,0,11
	.byte	'reserved_12',0,2
	.word	11724
	.byte	4,0,2,35,0,0,12,12,174,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	12606
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_ECCD_Bits',0,12,61,16,2,11
	.byte	'SERR',0,2
	.word	11724
	.byte	1,15,2,35,0,11
	.byte	'CERR',0,2
	.word	11724
	.byte	1,14,2,35,0,11
	.byte	'UERR',0,2
	.word	11724
	.byte	1,13,2,35,0,11
	.byte	'AERR',0,2
	.word	11724
	.byte	1,12,2,35,0,11
	.byte	'TRC',0,2
	.word	11724
	.byte	1,11,2,35,0,11
	.byte	'VAL',0,2
	.word	11724
	.byte	5,6,2,35,0,11
	.byte	'RARVAL',0,2
	.word	11724
	.byte	1,5,2,35,0,11
	.byte	'CENE',0,2
	.word	11724
	.byte	1,4,2,35,0,11
	.byte	'UENE',0,2
	.word	11724
	.byte	1,3,2,35,0,11
	.byte	'AENE',0,2
	.word	11724
	.byte	1,2,2,35,0,11
	.byte	'ECE',0,2
	.word	11724
	.byte	1,1,2,35,0,11
	.byte	'EOV',0,2
	.word	11724
	.byte	1,0,2,35,0,0,12,12,166,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	12843
	.byte	2,2,35,0,0,10
	.byte	'_Ifx_MC_ETRR_Bits',0,12,93,16,2,11
	.byte	'ADDR',0,2
	.word	11724
	.byte	13,3,2,35,0,11
	.byte	'MBI',0,2
	.word	11724
	.byte	3,0,2,35,0,0
.L314:
	.byte	12,12,182,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	13097
	.byte	2,2,35,0,0,15,10
	.word	13152
	.byte	16,4,0,15,132,1
	.word	669
	.byte	16,131,1,0,10
	.byte	'_Ifx_MC_RDBFL_Bits',0,12,133,1,16,2,11
	.byte	'WDATA',0,2
	.word	11724
	.byte	16,0,2,35,0,0,12,12,214,1,9,2,13
	.byte	'U',0
	.word	686
	.byte	2,2,35,0,13
	.byte	'I',0
	.word	11711
	.byte	2,2,35,0,13
	.byte	'B',0
	.word	13212
	.byte	2,2,35,0,0,15,80
	.word	13255
	.byte	16,39,0,15,16
	.word	669
	.byte	16,15,0,10
	.byte	'_Ifx_MC',0,12,238,1,25,128,2,13
	.byte	'CONFIG0',0
	.word	11834
	.byte	2,2,35,0,13
	.byte	'CONFIG1',0
	.word	11958
	.byte	2,2,35,2,13
	.byte	'MCONTROL',0
	.word	12223
	.byte	2,2,35,4,13
	.byte	'MSTATUS',0
	.word	12376
	.byte	2,2,35,6,13
	.byte	'RANGE',0
	.word	12473
	.byte	2,2,35,8,13
	.byte	'reserved_A',0
	.word	12513
	.byte	2,2,35,10,13
	.byte	'REVID',0
	.word	12566
	.byte	2,2,35,12,13
	.byte	'ECCS',0
	.word	12803
	.byte	2,2,35,14,13
	.byte	'ECCD',0
	.word	13057
	.byte	2,2,35,16,13
	.byte	'ETRR',0
	.word	13192
	.byte	10,2,35,18,13
	.byte	'reserved_1C',0
	.word	13201
	.byte	132,1,2,35,28,13
	.byte	'RDBFL',0
	.word	13295
	.byte	80,3,35,160,1,13
	.byte	'reserved_F0',0
	.word	13304
	.byte	16,3,35,240,1,0,14
	.word	13313
.L200:
	.byte	3
	.word	13550
	.byte	14
	.word	9669
.L211:
	.byte	3
	.word	13560
	.byte	25,11,136,1,9,12,13
	.byte	'numBlocks',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'dataSize',0
	.word	686
	.byte	2,2,35,2,13
	.byte	'eccSize',0
	.word	669
	.byte	1,2,35,4,13
	.byte	'eccInvPos0',0
	.word	669
	.byte	1,2,35,5,13
	.byte	'eccInvPos1',0
	.word	669
	.byte	1,2,35,6,13
	.byte	'mbistDelay',0
	.word	9669
	.byte	4,2,35,8,0,26
	.word	13570
.L236:
	.byte	3
	.word	13691
.L265:
	.byte	3
	.word	686
	.byte	21
	.word	11152
	.byte	22
	.word	11187
	.byte	6,0
.L321:
	.byte	3
	.word	13152
.L333:
	.byte	14
	.word	9669
	.byte	27
	.byte	'__wchar_t',0,13,1,1
	.word	11711
	.byte	27
	.byte	'__size_t',0,13,1,1
	.word	494
	.byte	27
	.byte	'__ptrdiff_t',0,13,1,1
	.word	510
	.byte	28,1,3
	.word	13783
	.byte	27
	.byte	'__codeptr',0,13,1,1
	.word	13785
	.byte	27
	.byte	'boolean',0,14,101,29
	.word	669
	.byte	27
	.byte	'uint8',0,14,105,29
	.word	669
	.byte	27
	.byte	'uint16',0,14,109,29
	.word	686
	.byte	27
	.byte	'uint32',0,14,113,29
	.word	9669
	.byte	27
	.byte	'uint64',0,14,118,29
	.word	376
	.byte	27
	.byte	'sint16',0,14,126,29
	.word	11711
	.byte	7
	.byte	'long int',0,4,5,27
	.byte	'sint32',0,14,131,1,29
	.word	13898
	.byte	7
	.byte	'long long int',0,8,5,27
	.byte	'sint64',0,14,138,1,29
	.word	13926
	.byte	27
	.byte	'float32',0,14,167,1,29
	.word	290
	.byte	27
	.byte	'pvoid',0,15,57,28
	.word	408
	.byte	27
	.byte	'Ifx_TickTime',0,15,79,28
	.word	13926
	.byte	17,15,130,1,9,1,18
	.byte	'Ifx_RxSel_a',0,0,18
	.byte	'Ifx_RxSel_b',0,1,18
	.byte	'Ifx_RxSel_c',0,2,18
	.byte	'Ifx_RxSel_d',0,3,18
	.byte	'Ifx_RxSel_e',0,4,18
	.byte	'Ifx_RxSel_f',0,5,18
	.byte	'Ifx_RxSel_g',0,6,18
	.byte	'Ifx_RxSel_h',0,7,0,27
	.byte	'Ifx_RxSel',0,15,140,1,3
	.word	14011
	.byte	10
	.byte	'_Ifx_CPU_A_Bits',0,16,45,16,4,11
	.byte	'ADDR',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_A_Bits',0,16,48,3
	.word	14149
	.byte	10
	.byte	'_Ifx_CPU_BIV_Bits',0,16,51,16,4,11
	.byte	'VSS',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'BIV',0,4
	.word	517
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BIV_Bits',0,16,55,3
	.word	14210
	.byte	10
	.byte	'_Ifx_CPU_BTV_Bits',0,16,58,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'BTV',0,4
	.word	517
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_BTV_Bits',0,16,62,3
	.word	14289
	.byte	10
	.byte	'_Ifx_CPU_CCNT_Bits',0,16,65,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT_Bits',0,16,69,3
	.word	14375
	.byte	10
	.byte	'_Ifx_CPU_CCTRL_Bits',0,16,72,16,4,11
	.byte	'CM',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'CE',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'M1',0,4
	.word	517
	.byte	3,27,2,35,0,11
	.byte	'M2',0,4
	.word	517
	.byte	3,24,2,35,0,11
	.byte	'M3',0,4
	.word	517
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL_Bits',0,16,80,3
	.word	14464
	.byte	10
	.byte	'_Ifx_CPU_COMPAT_Bits',0,16,83,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'RM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'SP',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT_Bits',0,16,89,3
	.word	14610
	.byte	10
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,16,92,16,4,11
	.byte	'CORE_ID',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID_Bits',0,16,96,3
	.word	14737
	.byte	10
	.byte	'_Ifx_CPU_CPR_L_Bits',0,16,99,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L_Bits',0,16,103,3
	.word	14835
	.byte	10
	.byte	'_Ifx_CPU_CPR_U_Bits',0,16,106,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U_Bits',0,16,110,3
	.word	14928
	.byte	10
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,16,113,16,4,11
	.byte	'MODREV',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'MOD_32B',0,4
	.word	517
	.byte	8,16,2,35,0,11
	.byte	'MOD',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID_Bits',0,16,118,3
	.word	15021
	.byte	10
	.byte	'_Ifx_CPU_CPXE_Bits',0,16,121,16,4,11
	.byte	'XE',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE_Bits',0,16,125,3
	.word	15128
	.byte	10
	.byte	'_Ifx_CPU_CREVT_Bits',0,16,128,1,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT_Bits',0,16,136,1,3
	.word	15215
	.byte	10
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,16,139,1,16,4,11
	.byte	'CID',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID_Bits',0,16,143,1,3
	.word	15369
	.byte	10
	.byte	'_Ifx_CPU_D_Bits',0,16,146,1,16,4,11
	.byte	'DATA',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_D_Bits',0,16,149,1,3
	.word	15463
	.byte	10
	.byte	'_Ifx_CPU_DATR_Bits',0,16,152,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'SBE',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	517
	.byte	5,23,2,35,0,11
	.byte	'CWE',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'CFE',0,4
	.word	517
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	3,18,2,35,0,11
	.byte	'SOE',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'SME',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DATR_Bits',0,16,163,1,3
	.word	15526
	.byte	10
	.byte	'_Ifx_CPU_DBGSR_Bits',0,16,166,1,16,4,11
	.byte	'DE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'HALT',0,4
	.word	517
	.byte	2,29,2,35,0,11
	.byte	'SIH',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'PREVSUSP',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'PEVT',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'EVTSRC',0,4
	.word	517
	.byte	5,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	517
	.byte	19,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR_Bits',0,16,177,1,3
	.word	15744
	.byte	10
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,16,180,1,16,4,11
	.byte	'DTA',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR_Bits',0,16,184,1,3
	.word	15959
	.byte	10
	.byte	'_Ifx_CPU_DCON0_Bits',0,16,187,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'DCBYP',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0_Bits',0,16,192,1,3
	.word	16053
	.byte	10
	.byte	'_Ifx_CPU_DCON2_Bits',0,16,195,1,16,4,11
	.byte	'DCACHE_SZE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'DSCRATCH_SZE',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2_Bits',0,16,199,1,3
	.word	16169
	.byte	10
	.byte	'_Ifx_CPU_DCX_Bits',0,16,202,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	6,26,2,35,0,11
	.byte	'DCXValue',0,4
	.word	517
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_CPU_DCX_Bits',0,16,206,1,3
	.word	16270
	.byte	10
	.byte	'_Ifx_CPU_DEADD_Bits',0,16,209,1,16,4,11
	.byte	'ERROR_ADDRESS',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD_Bits',0,16,212,1,3
	.word	16363
	.byte	10
	.byte	'_Ifx_CPU_DIEAR_Bits',0,16,215,1,16,4,11
	.byte	'TA',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR_Bits',0,16,218,1,3
	.word	16443
	.byte	10
	.byte	'_Ifx_CPU_DIETR_Bits',0,16,221,1,16,4,11
	.byte	'IED',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	517
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	517
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR_Bits',0,16,233,1,3
	.word	16512
	.byte	10
	.byte	'_Ifx_CPU_DMS_Bits',0,16,236,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'DMSValue',0,4
	.word	517
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_DMS_Bits',0,16,240,1,3
	.word	16741
	.byte	10
	.byte	'_Ifx_CPU_DPR_L_Bits',0,16,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'LOWBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L_Bits',0,16,247,1,3
	.word	16834
	.byte	10
	.byte	'_Ifx_CPU_DPR_U_Bits',0,16,250,1,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'UPPBND',0,4
	.word	517
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U_Bits',0,16,254,1,3
	.word	16929
	.byte	10
	.byte	'_Ifx_CPU_DPRE_Bits',0,16,129,2,16,4,11
	.byte	'RE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE_Bits',0,16,133,2,3
	.word	17024
	.byte	10
	.byte	'_Ifx_CPU_DPWE_Bits',0,16,136,2,16,4,11
	.byte	'WE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE_Bits',0,16,140,2,3
	.word	17114
	.byte	10
	.byte	'_Ifx_CPU_DSTR_Bits',0,16,143,2,16,4,11
	.byte	'SRE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'GAE',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'LBE',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	3,26,2,35,0,11
	.byte	'CRE',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	517
	.byte	7,18,2,35,0,11
	.byte	'DTME',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'LOE',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'SDE',0,4
	.word	517
	.byte	1,15,2,35,0,11
	.byte	'SCE',0,4
	.word	517
	.byte	1,14,2,35,0,11
	.byte	'CAC',0,4
	.word	517
	.byte	1,13,2,35,0,11
	.byte	'MPE',0,4
	.word	517
	.byte	1,12,2,35,0,11
	.byte	'CLE',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	517
	.byte	3,8,2,35,0,11
	.byte	'ALN',0,4
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	517
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR_Bits',0,16,161,2,3
	.word	17204
	.byte	10
	.byte	'_Ifx_CPU_EXEVT_Bits',0,16,164,2,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT_Bits',0,16,172,2,3
	.word	17528
	.byte	10
	.byte	'_Ifx_CPU_FCX_Bits',0,16,175,2,16,4,11
	.byte	'FCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'FCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FCX_Bits',0,16,180,2,3
	.word	17682
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,16,183,2,16,4,11
	.byte	'TST',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'TCL',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	6,24,2,35,0,11
	.byte	'RM',0,4
	.word	517
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	8,14,2,35,0,11
	.byte	'FXE',0,4
	.word	517
	.byte	1,13,2,35,0,11
	.byte	'FUE',0,4
	.word	517
	.byte	1,12,2,35,0,11
	.byte	'FZE',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'FVE',0,4
	.word	517
	.byte	1,10,2,35,0,11
	.byte	'FIE',0,4
	.word	517
	.byte	1,9,2,35,0,11
	.byte	'reserved_23',0,4
	.word	517
	.byte	3,6,2,35,0,11
	.byte	'FX',0,4
	.word	517
	.byte	1,5,2,35,0,11
	.byte	'FU',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'FZ',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'FV',0,4
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'FI',0,4
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'reserved_31',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,16,202,2,3
	.word	17788
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,205,2,16,4,11
	.byte	'OPC',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'FMT',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	517
	.byte	7,16,2,35,0,11
	.byte	'DREG',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,16,212,2,3
	.word	18137
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,16,215,2,16,4,11
	.byte	'PC',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,16,218,2,3
	.word	18297
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,221,2,16,4,11
	.byte	'SRC1',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,16,224,2,3
	.word	18378
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,227,2,16,4,11
	.byte	'SRC2',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,16,230,2,3
	.word	18465
	.byte	10
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,233,2,16,4,11
	.byte	'SRC3',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,16,236,2,3
	.word	18552
	.byte	10
	.byte	'_Ifx_CPU_ICNT_Bits',0,16,239,2,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT_Bits',0,16,243,2,3
	.word	18639
	.byte	10
	.byte	'_Ifx_CPU_ICR_Bits',0,16,246,2,16,4,11
	.byte	'CCPN',0,4
	.word	517
	.byte	10,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	5,17,2,35,0,11
	.byte	'IE',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'PIPN',0,4
	.word	517
	.byte	10,6,2,35,0,11
	.byte	'reserved_26',0,4
	.word	517
	.byte	6,0,2,35,0,0,27
	.byte	'Ifx_CPU_ICR_Bits',0,16,253,2,3
	.word	18730
	.byte	10
	.byte	'_Ifx_CPU_ISP_Bits',0,16,128,3,16,4,11
	.byte	'ISP',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_ISP_Bits',0,16,131,3,3
	.word	18873
	.byte	10
	.byte	'_Ifx_CPU_LCX_Bits',0,16,134,3,16,4,11
	.byte	'LCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'LCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	517
	.byte	12,0,2,35,0,0,27
	.byte	'Ifx_CPU_LCX_Bits',0,16,139,3,3
	.word	18939
	.byte	10
	.byte	'_Ifx_CPU_M1CNT_Bits',0,16,142,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT_Bits',0,16,146,3,3
	.word	19045
	.byte	10
	.byte	'_Ifx_CPU_M2CNT_Bits',0,16,149,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT_Bits',0,16,153,3,3
	.word	19138
	.byte	10
	.byte	'_Ifx_CPU_M3CNT_Bits',0,16,156,3,16,4,11
	.byte	'CountValue',0,4
	.word	517
	.byte	31,1,2,35,0,11
	.byte	'SOvf',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT_Bits',0,16,160,3,3
	.word	19231
	.byte	10
	.byte	'_Ifx_CPU_PC_Bits',0,16,163,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PC',0,4
	.word	517
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_CPU_PC_Bits',0,16,167,3,3
	.word	19324
	.byte	10
	.byte	'_Ifx_CPU_PCON0_Bits',0,16,170,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PCBYP',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0_Bits',0,16,175,3,3
	.word	19409
	.byte	10
	.byte	'_Ifx_CPU_PCON1_Bits',0,16,178,3,16,4,11
	.byte	'PCINV',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PBINV',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	517
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1_Bits',0,16,183,3,3
	.word	19525
	.byte	10
	.byte	'_Ifx_CPU_PCON2_Bits',0,16,186,3,16,4,11
	.byte	'PCACHE_SZE',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'PSCRATCH_SZE',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2_Bits',0,16,190,3,3
	.word	19636
	.byte	10
	.byte	'_Ifx_CPU_PCXI_Bits',0,16,193,3,16,4,11
	.byte	'PCXO',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'PCXS',0,4
	.word	517
	.byte	4,12,2,35,0,11
	.byte	'UL',0,4
	.word	517
	.byte	1,11,2,35,0,11
	.byte	'PIE',0,4
	.word	517
	.byte	1,10,2,35,0,11
	.byte	'PCPN',0,4
	.word	517
	.byte	10,0,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI_Bits',0,16,200,3,3
	.word	19737
	.byte	10
	.byte	'_Ifx_CPU_PIEAR_Bits',0,16,203,3,16,4,11
	.byte	'TA',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR_Bits',0,16,206,3,3
	.word	19867
	.byte	10
	.byte	'_Ifx_CPU_PIETR_Bits',0,16,209,3,16,4,11
	.byte	'IED',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'IE_T',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'IE_C',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IE_S',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IE_BI',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'E_INFO',0,4
	.word	517
	.byte	6,21,2,35,0,11
	.byte	'IE_DUAL',0,4
	.word	517
	.byte	1,20,2,35,0,11
	.byte	'IE_SP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'IE_BS',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	18,0,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR_Bits',0,16,221,3,3
	.word	19936
	.byte	10
	.byte	'_Ifx_CPU_PMA0_Bits',0,16,224,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	13,19,2,35,0,11
	.byte	'DAC',0,4
	.word	517
	.byte	3,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0_Bits',0,16,229,3,3
	.word	20165
	.byte	10
	.byte	'_Ifx_CPU_PMA1_Bits',0,16,232,3,16,4,11
	.byte	'reserved_0',0,4
	.word	517
	.byte	14,18,2,35,0,11
	.byte	'CAC',0,4
	.word	517
	.byte	2,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1_Bits',0,16,237,3,3
	.word	20278
	.byte	10
	.byte	'_Ifx_CPU_PMA2_Bits',0,16,240,3,16,4,11
	.byte	'PSI',0,4
	.word	517
	.byte	16,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2_Bits',0,16,244,3,3
	.word	20391
	.byte	10
	.byte	'_Ifx_CPU_PSTR_Bits',0,16,247,3,16,4,11
	.byte	'FRE',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'FBE',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	9,20,2,35,0,11
	.byte	'FPE',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'FME',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	517
	.byte	17,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR_Bits',0,16,129,4,3
	.word	20482
	.byte	10
	.byte	'_Ifx_CPU_PSW_Bits',0,16,132,4,16,4,11
	.byte	'CDC',0,4
	.word	517
	.byte	7,25,2,35,0,11
	.byte	'CDE',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'GW',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'IS',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'IO',0,4
	.word	517
	.byte	2,20,2,35,0,11
	.byte	'PRS',0,4
	.word	517
	.byte	2,18,2,35,0,11
	.byte	'S',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'reserved_15',0,4
	.word	517
	.byte	12,5,2,35,0,11
	.byte	'SAV',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'AV',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'SV',0,4
	.word	517
	.byte	1,2,2,35,0,11
	.byte	'V',0,4
	.word	517
	.byte	1,1,2,35,0,11
	.byte	'C',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_PSW_Bits',0,16,147,4,3
	.word	20685
	.byte	10
	.byte	'_Ifx_CPU_SEGEN_Bits',0,16,150,4,16,4,11
	.byte	'ADFLIP',0,4
	.word	517
	.byte	8,24,2,35,0,11
	.byte	'ADTYPE',0,4
	.word	517
	.byte	2,22,2,35,0,11
	.byte	'reserved_10',0,4
	.word	517
	.byte	21,1,2,35,0,11
	.byte	'AE',0,4
	.word	517
	.byte	1,0,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN_Bits',0,16,156,4,3
	.word	20928
	.byte	10
	.byte	'_Ifx_CPU_SMACON_Bits',0,16,159,4,16,4,11
	.byte	'PC',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PT',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	5,24,2,35,0,11
	.byte	'DC',0,4
	.word	517
	.byte	1,23,2,35,0,11
	.byte	'reserved_9',0,4
	.word	517
	.byte	1,22,2,35,0,11
	.byte	'DT',0,4
	.word	517
	.byte	1,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	517
	.byte	13,8,2,35,0,11
	.byte	'IODT',0,4
	.word	517
	.byte	1,7,2,35,0,11
	.byte	'reserved_25',0,4
	.word	517
	.byte	7,0,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON_Bits',0,16,171,4,3
	.word	21056
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,16,174,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,16,177,4,3
	.word	21297
	.byte	10
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,16,180,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,16,183,4,3
	.word	21380
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,186,4,16,4,11
	.byte	'EN',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,16,189,4,3
	.word	21471
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,192,4,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,16,195,4,3
	.word	21562
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,16,198,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,16,202,4,3
	.word	21661
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,16,205,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,16,209,4,3
	.word	21768
	.byte	10
	.byte	'_Ifx_CPU_SWEVT_Bits',0,16,212,4,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT_Bits',0,16,220,4,3
	.word	21875
	.byte	10
	.byte	'_Ifx_CPU_SYSCON_Bits',0,16,223,4,16,4,11
	.byte	'FCDSF',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'PROTEN',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'TPROTEN',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'IS',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'IT',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON_Bits',0,16,231,4,3
	.word	22029
	.byte	10
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,16,234,4,16,4,11
	.byte	'ASI',0,4
	.word	517
	.byte	5,27,2,35,0,11
	.byte	'reserved_5',0,4
	.word	517
	.byte	27,0,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,16,238,4,3
	.word	22190
	.byte	10
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,16,241,4,16,4,11
	.byte	'TEXP0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'TEXP1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'TEXP2',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	517
	.byte	13,16,2,35,0,11
	.byte	'TTRAP',0,4
	.word	517
	.byte	1,15,2,35,0,11
	.byte	'reserved_17',0,4
	.word	517
	.byte	15,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON_Bits',0,16,249,4,3
	.word	22288
	.byte	10
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,16,252,4,16,4,11
	.byte	'Timer',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,16,255,4,3
	.word	22460
	.byte	10
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,16,130,5,16,4,11
	.byte	'ADDR',0,4
	.word	517
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR_Bits',0,16,133,5,3
	.word	22540
	.byte	10
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,16,136,5,16,4,11
	.byte	'EVTA',0,4
	.word	517
	.byte	3,29,2,35,0,11
	.byte	'BBM',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'BOD',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'SUSP',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'CNT',0,4
	.word	517
	.byte	2,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	4,20,2,35,0,11
	.byte	'TYP',0,4
	.word	517
	.byte	1,19,2,35,0,11
	.byte	'RNG',0,4
	.word	517
	.byte	1,18,2,35,0,11
	.byte	'reserved_14',0,4
	.word	517
	.byte	1,17,2,35,0,11
	.byte	'ASI_EN',0,4
	.word	517
	.byte	1,16,2,35,0,11
	.byte	'ASI',0,4
	.word	517
	.byte	5,11,2,35,0,11
	.byte	'reserved_21',0,4
	.word	517
	.byte	6,5,2,35,0,11
	.byte	'AST',0,4
	.word	517
	.byte	1,4,2,35,0,11
	.byte	'ALD',0,4
	.word	517
	.byte	1,3,2,35,0,11
	.byte	'reserved_29',0,4
	.word	517
	.byte	3,0,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT_Bits',0,16,153,5,3
	.word	22613
	.byte	10
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,16,156,5,16,4,11
	.byte	'T0',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'T1',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'T2',0,4
	.word	517
	.byte	1,29,2,35,0,11
	.byte	'T3',0,4
	.word	517
	.byte	1,28,2,35,0,11
	.byte	'T4',0,4
	.word	517
	.byte	1,27,2,35,0,11
	.byte	'T5',0,4
	.word	517
	.byte	1,26,2,35,0,11
	.byte	'T6',0,4
	.word	517
	.byte	1,25,2,35,0,11
	.byte	'T7',0,4
	.word	517
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	517
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,16,167,5,3
	.word	22931
	.byte	12,16,175,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14149
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_A',0,16,180,5,3
	.word	23126
	.byte	12,16,183,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14210
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BIV',0,16,188,5,3
	.word	23185
	.byte	12,16,191,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14289
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_BTV',0,16,196,5,3
	.word	23246
	.byte	12,16,199,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14375
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCNT',0,16,204,5,3
	.word	23307
	.byte	12,16,207,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14464
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CCTRL',0,16,212,5,3
	.word	23369
	.byte	12,16,215,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14610
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_COMPAT',0,16,220,5,3
	.word	23432
	.byte	12,16,223,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14737
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CORE_ID',0,16,228,5,3
	.word	23496
	.byte	12,16,231,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14835
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_L',0,16,236,5,3
	.word	23561
	.byte	12,16,239,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14928
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPR_U',0,16,244,5,3
	.word	23624
	.byte	12,16,247,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15021
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPU_ID',0,16,252,5,3
	.word	23687
	.byte	12,16,255,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15128
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CPXE',0,16,132,6,3
	.word	23751
	.byte	12,16,135,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15215
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CREVT',0,16,140,6,3
	.word	23813
	.byte	12,16,143,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15369
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_CUS_ID',0,16,148,6,3
	.word	23876
	.byte	12,16,151,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15463
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_D',0,16,156,6,3
	.word	23940
	.byte	12,16,159,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15526
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DATR',0,16,164,6,3
	.word	23999
	.byte	12,16,167,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15744
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGSR',0,16,172,6,3
	.word	24061
	.byte	12,16,175,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15959
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DBGTCR',0,16,180,6,3
	.word	24124
	.byte	12,16,183,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16053
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON0',0,16,188,6,3
	.word	24188
	.byte	12,16,191,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16169
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCON2',0,16,196,6,3
	.word	24251
	.byte	12,16,199,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16270
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DCX',0,16,204,6,3
	.word	24314
	.byte	12,16,207,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16363
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DEADD',0,16,212,6,3
	.word	24375
	.byte	12,16,215,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16443
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIEAR',0,16,220,6,3
	.word	24438
	.byte	12,16,223,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16512
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DIETR',0,16,228,6,3
	.word	24501
	.byte	12,16,231,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16741
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DMS',0,16,236,6,3
	.word	24564
	.byte	12,16,239,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16834
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_L',0,16,244,6,3
	.word	24625
	.byte	12,16,247,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	16929
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPR_U',0,16,252,6,3
	.word	24688
	.byte	12,16,255,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17024
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPRE',0,16,132,7,3
	.word	24751
	.byte	12,16,135,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17114
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DPWE',0,16,140,7,3
	.word	24813
	.byte	12,16,143,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17204
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_DSTR',0,16,148,7,3
	.word	24875
	.byte	12,16,151,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17528
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_EXEVT',0,16,156,7,3
	.word	24937
	.byte	12,16,159,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17682
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FCX',0,16,164,7,3
	.word	25000
	.byte	12,16,167,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	17788
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,16,172,7,3
	.word	25061
	.byte	12,16,175,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18137
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,16,180,7,3
	.word	25131
	.byte	12,16,183,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18297
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,16,188,7,3
	.word	25201
	.byte	12,16,191,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18378
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,16,196,7,3
	.word	25270
	.byte	12,16,199,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18465
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,16,204,7,3
	.word	25341
	.byte	12,16,207,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18552
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,16,212,7,3
	.word	25412
	.byte	12,16,215,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18639
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICNT',0,16,220,7,3
	.word	25483
	.byte	12,16,223,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18730
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ICR',0,16,228,7,3
	.word	25545
	.byte	12,16,231,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18873
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_ISP',0,16,236,7,3
	.word	25606
	.byte	12,16,239,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	18939
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_LCX',0,16,244,7,3
	.word	25667
	.byte	12,16,247,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19045
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M1CNT',0,16,252,7,3
	.word	25728
	.byte	12,16,255,7,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19138
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M2CNT',0,16,132,8,3
	.word	25791
	.byte	12,16,135,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19231
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_M3CNT',0,16,140,8,3
	.word	25854
	.byte	12,16,143,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19324
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PC',0,16,148,8,3
	.word	25917
	.byte	12,16,151,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19409
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON0',0,16,156,8,3
	.word	25977
	.byte	12,16,159,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19525
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON1',0,16,164,8,3
	.word	26040
	.byte	12,16,167,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19636
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCON2',0,16,172,8,3
	.word	26103
	.byte	12,16,175,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19737
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PCXI',0,16,180,8,3
	.word	26166
	.byte	12,16,183,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19867
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIEAR',0,16,188,8,3
	.word	26228
	.byte	12,16,191,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19936
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PIETR',0,16,196,8,3
	.word	26291
	.byte	12,16,199,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20165
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA0',0,16,204,8,3
	.word	26354
	.byte	12,16,207,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20278
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA1',0,16,212,8,3
	.word	26416
	.byte	12,16,215,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20391
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PMA2',0,16,220,8,3
	.word	26478
	.byte	12,16,223,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20482
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSTR',0,16,228,8,3
	.word	26540
	.byte	12,16,231,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20685
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_PSW',0,16,236,8,3
	.word	26602
	.byte	12,16,239,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20928
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SEGEN',0,16,244,8,3
	.word	26663
	.byte	12,16,247,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21056
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SMACON',0,16,252,8,3
	.word	26726
	.byte	12,16,255,8,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21297
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENA',0,16,132,9,3
	.word	26790
	.byte	12,16,135,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21380
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_ACCENB',0,16,140,9,3
	.word	26860
	.byte	12,16,143,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21471
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,16,148,9,3
	.word	26930
	.byte	12,16,151,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21562
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,16,156,9,3
	.word	27004
	.byte	12,16,159,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21661
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,16,164,9,3
	.word	27078
	.byte	12,16,167,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21768
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,16,172,9,3
	.word	27148
	.byte	12,16,175,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21875
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SWEVT',0,16,180,9,3
	.word	27218
	.byte	12,16,183,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22029
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_SYSCON',0,16,188,9,3
	.word	27281
	.byte	12,16,191,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22190
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TASK_ASI',0,16,196,9,3
	.word	27345
	.byte	12,16,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22288
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_CON',0,16,204,9,3
	.word	27411
	.byte	12,16,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22460
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TPS_TIMER',0,16,212,9,3
	.word	27476
	.byte	12,16,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22540
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_ADR',0,16,220,9,3
	.word	27543
	.byte	12,16,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22613
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TR_EVT',0,16,228,9,3
	.word	27607
	.byte	12,16,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22931
	.byte	4,2,35,0,0,27
	.byte	'Ifx_CPU_TRIG_ACC',0,16,236,9,3
	.word	27671
	.byte	10
	.byte	'_Ifx_CPU_CPR',0,16,247,9,25,8,13
	.byte	'L',0
	.word	23561
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	23624
	.byte	4,2,35,4,0,14
	.word	27737
	.byte	27
	.byte	'Ifx_CPU_CPR',0,16,251,9,3
	.word	27779
	.byte	10
	.byte	'_Ifx_CPU_DPR',0,16,254,9,25,8,13
	.byte	'L',0
	.word	24625
	.byte	4,2,35,0,13
	.byte	'U',0
	.word	24688
	.byte	4,2,35,4,0,14
	.word	27805
	.byte	27
	.byte	'Ifx_CPU_DPR',0,16,130,10,3
	.word	27847
	.byte	10
	.byte	'_Ifx_CPU_SPROT_RGN',0,16,133,10,25,16,13
	.byte	'LA',0
	.word	27078
	.byte	4,2,35,0,13
	.byte	'UA',0
	.word	27148
	.byte	4,2,35,4,13
	.byte	'ACCENA',0
	.word	26930
	.byte	4,2,35,8,13
	.byte	'ACCENB',0
	.word	27004
	.byte	4,2,35,12,0,14
	.word	27873
	.byte	27
	.byte	'Ifx_CPU_SPROT_RGN',0,16,139,10,3
	.word	27955
	.byte	15,12
	.word	27476
	.byte	16,2,0,10
	.byte	'_Ifx_CPU_TPS',0,16,142,10,25,16,13
	.byte	'CON',0
	.word	27411
	.byte	4,2,35,0,13
	.byte	'TIMER',0
	.word	27987
	.byte	12,2,35,4,0,14
	.word	27996
	.byte	27
	.byte	'Ifx_CPU_TPS',0,16,146,10,3
	.word	28044
	.byte	10
	.byte	'_Ifx_CPU_TR',0,16,149,10,25,8,13
	.byte	'EVT',0
	.word	27607
	.byte	4,2,35,0,13
	.byte	'ADR',0
	.word	27543
	.byte	4,2,35,4,0,14
	.word	28070
	.byte	27
	.byte	'Ifx_CPU_TR',0,16,153,10,3
	.word	28115
	.byte	15,176,32
	.word	669
	.byte	16,175,32,0,15,208,223,1
	.word	669
	.byte	16,207,223,1,0,15,248,1
	.word	669
	.byte	16,247,1,0,15,244,29
	.word	669
	.byte	16,243,29,0,15,188,3
	.word	669
	.byte	16,187,3,0,15,232,3
	.word	669
	.byte	16,231,3,0,15,252,23
	.word	669
	.byte	16,251,23,0,15,228,63
	.word	669
	.byte	16,227,63,0,15,128,1
	.word	27805
	.byte	16,15,0,14
	.word	28230
	.byte	15,128,31
	.word	669
	.byte	16,255,30,0,15,64
	.word	27737
	.byte	16,7,0,14
	.word	28256
	.byte	15,192,31
	.word	669
	.byte	16,191,31,0,15,16
	.word	23751
	.byte	16,3,0,15,16
	.word	24751
	.byte	16,3,0,15,16
	.word	24813
	.byte	16,3,0,15,208,7
	.word	669
	.byte	16,207,7,0,14
	.word	27996
	.byte	15,240,23
	.word	669
	.byte	16,239,23,0,15,64
	.word	28070
	.byte	16,7,0,14
	.word	28335
	.byte	15,192,23
	.word	669
	.byte	16,191,23,0,15,232,1
	.word	669
	.byte	16,231,1,0,15,28
	.word	669
	.byte	16,27,0,15,180,1
	.word	669
	.byte	16,179,1,0,15,172,1
	.word	669
	.byte	16,171,1,0,15,64
	.word	23940
	.byte	16,15,0,15,64
	.word	669
	.byte	16,63,0,15,64
	.word	23126
	.byte	16,15,0,10
	.byte	'_Ifx_CPU',0,16,166,10,25,128,128,4,13
	.byte	'reserved_0',0
	.word	28140
	.byte	176,32,2,35,0,13
	.byte	'SEGEN',0
	.word	26663
	.byte	4,3,35,176,32,13
	.byte	'reserved_1034',0
	.word	28151
	.byte	208,223,1,3,35,180,32,13
	.byte	'TASK_ASI',0
	.word	27345
	.byte	4,4,35,132,128,2,13
	.byte	'reserved_8008',0
	.word	28164
	.byte	248,1,4,35,136,128,2,13
	.byte	'PMA0',0
	.word	26354
	.byte	4,4,35,128,130,2,13
	.byte	'PMA1',0
	.word	26416
	.byte	4,4,35,132,130,2,13
	.byte	'PMA2',0
	.word	26478
	.byte	4,4,35,136,130,2,13
	.byte	'reserved_810C',0
	.word	28175
	.byte	244,29,4,35,140,130,2,13
	.byte	'DCON2',0
	.word	24251
	.byte	4,4,35,128,160,2,13
	.byte	'reserved_9004',0
	.word	4355
	.byte	8,4,35,132,160,2,13
	.byte	'SMACON',0
	.word	26726
	.byte	4,4,35,140,160,2,13
	.byte	'DSTR',0
	.word	24875
	.byte	4,4,35,144,160,2,13
	.byte	'reserved_9014',0
	.word	2536
	.byte	4,4,35,148,160,2,13
	.byte	'DATR',0
	.word	23999
	.byte	4,4,35,152,160,2,13
	.byte	'DEADD',0
	.word	24375
	.byte	4,4,35,156,160,2,13
	.byte	'DIEAR',0
	.word	24438
	.byte	4,4,35,160,160,2,13
	.byte	'DIETR',0
	.word	24501
	.byte	4,4,35,164,160,2,13
	.byte	'reserved_9028',0
	.word	3726
	.byte	24,4,35,168,160,2,13
	.byte	'DCON0',0
	.word	24188
	.byte	4,4,35,192,160,2,13
	.byte	'reserved_9044',0
	.word	28186
	.byte	188,3,4,35,196,160,2,13
	.byte	'PSTR',0
	.word	26540
	.byte	4,4,35,128,164,2,13
	.byte	'PCON1',0
	.word	26040
	.byte	4,4,35,132,164,2,13
	.byte	'PCON2',0
	.word	26103
	.byte	4,4,35,136,164,2,13
	.byte	'PCON0',0
	.word	25977
	.byte	4,4,35,140,164,2,13
	.byte	'PIEAR',0
	.word	26228
	.byte	4,4,35,144,164,2,13
	.byte	'PIETR',0
	.word	26291
	.byte	4,4,35,148,164,2,13
	.byte	'reserved_9218',0
	.word	28197
	.byte	232,3,4,35,152,164,2,13
	.byte	'COMPAT',0
	.word	23432
	.byte	4,4,35,128,168,2,13
	.byte	'reserved_9404',0
	.word	28208
	.byte	252,23,4,35,132,168,2,13
	.byte	'FPU_TRAP_CON',0
	.word	25061
	.byte	4,4,35,128,192,2,13
	.byte	'FPU_TRAP_PC',0
	.word	25201
	.byte	4,4,35,132,192,2,13
	.byte	'FPU_TRAP_OPC',0
	.word	25131
	.byte	4,4,35,136,192,2,13
	.byte	'reserved_A00C',0
	.word	2536
	.byte	4,4,35,140,192,2,13
	.byte	'FPU_TRAP_SRC1',0
	.word	25270
	.byte	4,4,35,144,192,2,13
	.byte	'FPU_TRAP_SRC2',0
	.word	25341
	.byte	4,4,35,148,192,2,13
	.byte	'FPU_TRAP_SRC3',0
	.word	25412
	.byte	4,4,35,152,192,2,13
	.byte	'reserved_A01C',0
	.word	28219
	.byte	228,63,4,35,156,192,2,13
	.byte	'DPR',0
	.word	28240
	.byte	128,1,4,35,128,128,3,13
	.byte	'reserved_C080',0
	.word	28245
	.byte	128,31,4,35,128,129,3,13
	.byte	'CPR',0
	.word	28265
	.byte	64,4,35,128,160,3,13
	.byte	'reserved_D040',0
	.word	28270
	.byte	192,31,4,35,192,160,3,13
	.byte	'CPXE',0
	.word	28281
	.byte	16,4,35,128,192,3,13
	.byte	'DPRE',0
	.word	28290
	.byte	16,4,35,144,192,3,13
	.byte	'DPWE',0
	.word	28299
	.byte	16,4,35,160,192,3,13
	.byte	'reserved_E030',0
	.word	28308
	.byte	208,7,4,35,176,192,3,13
	.byte	'TPS',0
	.word	28319
	.byte	16,4,35,128,200,3,13
	.byte	'reserved_E410',0
	.word	28324
	.byte	240,23,4,35,144,200,3,13
	.byte	'TR',0
	.word	28344
	.byte	64,4,35,128,224,3,13
	.byte	'reserved_F040',0
	.word	28349
	.byte	192,23,4,35,192,224,3,13
	.byte	'CCTRL',0
	.word	23369
	.byte	4,4,35,128,248,3,13
	.byte	'CCNT',0
	.word	23307
	.byte	4,4,35,132,248,3,13
	.byte	'ICNT',0
	.word	25483
	.byte	4,4,35,136,248,3,13
	.byte	'M1CNT',0
	.word	25728
	.byte	4,4,35,140,248,3,13
	.byte	'M2CNT',0
	.word	25791
	.byte	4,4,35,144,248,3,13
	.byte	'M3CNT',0
	.word	25854
	.byte	4,4,35,148,248,3,13
	.byte	'reserved_FC18',0
	.word	28360
	.byte	232,1,4,35,152,248,3,13
	.byte	'DBGSR',0
	.word	24061
	.byte	4,4,35,128,250,3,13
	.byte	'reserved_FD04',0
	.word	2536
	.byte	4,4,35,132,250,3,13
	.byte	'EXEVT',0
	.word	24937
	.byte	4,4,35,136,250,3,13
	.byte	'CREVT',0
	.word	23813
	.byte	4,4,35,140,250,3,13
	.byte	'SWEVT',0
	.word	27218
	.byte	4,4,35,144,250,3,13
	.byte	'reserved_FD14',0
	.word	28371
	.byte	28,4,35,148,250,3,13
	.byte	'TRIG_ACC',0
	.word	27671
	.byte	4,4,35,176,250,3,13
	.byte	'reserved_FD34',0
	.word	4695
	.byte	12,4,35,180,250,3,13
	.byte	'DMS',0
	.word	24564
	.byte	4,4,35,192,250,3,13
	.byte	'DCX',0
	.word	24314
	.byte	4,4,35,196,250,3,13
	.byte	'DBGTCR',0
	.word	24124
	.byte	4,4,35,200,250,3,13
	.byte	'reserved_FD4C',0
	.word	28380
	.byte	180,1,4,35,204,250,3,13
	.byte	'PCXI',0
	.word	26166
	.byte	4,4,35,128,252,3,13
	.byte	'PSW',0
	.word	26602
	.byte	4,4,35,132,252,3,13
	.byte	'PC',0
	.word	25917
	.byte	4,4,35,136,252,3,13
	.byte	'reserved_FE0C',0
	.word	4355
	.byte	8,4,35,140,252,3,13
	.byte	'SYSCON',0
	.word	27281
	.byte	4,4,35,148,252,3,13
	.byte	'CPU_ID',0
	.word	23687
	.byte	4,4,35,152,252,3,13
	.byte	'CORE_ID',0
	.word	23496
	.byte	4,4,35,156,252,3,13
	.byte	'BIV',0
	.word	23185
	.byte	4,4,35,160,252,3,13
	.byte	'BTV',0
	.word	23246
	.byte	4,4,35,164,252,3,13
	.byte	'ISP',0
	.word	25606
	.byte	4,4,35,168,252,3,13
	.byte	'ICR',0
	.word	25545
	.byte	4,4,35,172,252,3,13
	.byte	'reserved_FE30',0
	.word	4355
	.byte	8,4,35,176,252,3,13
	.byte	'FCX',0
	.word	25000
	.byte	4,4,35,184,252,3,13
	.byte	'LCX',0
	.word	25667
	.byte	4,4,35,188,252,3,13
	.byte	'reserved_FE40',0
	.word	13304
	.byte	16,4,35,192,252,3,13
	.byte	'CUS_ID',0
	.word	23876
	.byte	4,4,35,208,252,3,13
	.byte	'reserved_FE54',0
	.word	28391
	.byte	172,1,4,35,212,252,3,13
	.byte	'D',0
	.word	28402
	.byte	64,4,35,128,254,3,13
	.byte	'reserved_FF40',0
	.word	28411
	.byte	64,4,35,192,254,3,13
	.byte	'A',0
	.word	28420
	.byte	64,4,35,128,255,3,13
	.byte	'reserved_FFC0',0
	.word	28411
	.byte	64,4,35,192,255,3,0,14
	.word	28429
	.byte	27
	.byte	'Ifx_CPU',0,16,130,11,3
	.word	30220
	.byte	17,9,127,9,1,18
	.byte	'IfxCpu_Id_0',0,0,18
	.byte	'IfxCpu_Id_1',0,1,18
	.byte	'IfxCpu_Id_none',0,2,0,27
	.byte	'IfxCpu_Id',0,9,132,1,3
	.word	30242
	.byte	27
	.byte	'IfxCpu_ResourceCpu',0,9,161,1,3
	.word	9514
	.byte	10
	.byte	'_Ifx_SRC_SRCR_Bits',0,17,45,16,4,11
	.byte	'SRPN',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'SRE',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'TOS',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'ECC',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'SRR',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'CLRR',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'SETR',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'IOV',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'IOVCLR',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'SWS',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'SWSCLR',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SRC_SRCR_Bits',0,17,62,3
	.word	30340
	.byte	12,17,70,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30340
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SRC_SRCR',0,17,75,3
	.word	30656
	.byte	10
	.byte	'_Ifx_SRC_AGBT',0,17,86,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	30716
	.byte	27
	.byte	'Ifx_SRC_AGBT',0,17,89,3
	.word	30748
	.byte	10
	.byte	'_Ifx_SRC_ASCLIN',0,17,92,25,12,13
	.byte	'TX',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,8,0,14
	.word	30774
	.byte	27
	.byte	'Ifx_SRC_ASCLIN',0,17,97,3
	.word	30833
	.byte	10
	.byte	'_Ifx_SRC_BCUSPB',0,17,100,25,4,13
	.byte	'SBSRC',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	30861
	.byte	27
	.byte	'Ifx_SRC_BCUSPB',0,17,103,3
	.word	30898
	.byte	15,64
	.word	30656
	.byte	16,15,0,10
	.byte	'_Ifx_SRC_CAN',0,17,106,25,64,13
	.byte	'INT',0
	.word	30926
	.byte	64,2,35,0,0,14
	.word	30935
	.byte	27
	.byte	'Ifx_SRC_CAN',0,17,109,3
	.word	30967
	.byte	10
	.byte	'_Ifx_SRC_CCU6',0,17,112,25,16,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	30656
	.byte	4,2,35,12,0,14
	.word	30992
	.byte	27
	.byte	'Ifx_SRC_CCU6',0,17,118,3
	.word	31064
	.byte	15,8
	.word	30656
	.byte	16,1,0,10
	.byte	'_Ifx_SRC_CERBERUS',0,17,121,25,8,13
	.byte	'SR',0
	.word	31090
	.byte	8,2,35,0,0,14
	.word	31099
	.byte	27
	.byte	'Ifx_SRC_CERBERUS',0,17,124,3
	.word	31135
	.byte	10
	.byte	'_Ifx_SRC_CIF',0,17,127,25,16,13
	.byte	'MI',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'MIEP',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'ISP',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'MJPEG',0
	.word	30656
	.byte	4,2,35,12,0,14
	.word	31165
	.byte	27
	.byte	'Ifx_SRC_CIF',0,17,133,1,3
	.word	31238
	.byte	10
	.byte	'_Ifx_SRC_CPU',0,17,136,1,25,4,13
	.byte	'SBSRC',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	31264
	.byte	27
	.byte	'Ifx_SRC_CPU',0,17,139,1,3
	.word	31299
	.byte	15,192,1
	.word	30656
	.byte	16,47,0,10
	.byte	'_Ifx_SRC_DMA',0,17,142,1,25,208,1,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	4695
	.byte	12,2,35,4,13
	.byte	'CH',0
	.word	31325
	.byte	192,1,2,35,16,0,14
	.word	31335
	.byte	27
	.byte	'Ifx_SRC_DMA',0,17,147,1,3
	.word	31402
	.byte	10
	.byte	'_Ifx_SRC_DSADC',0,17,150,1,25,8,13
	.byte	'SRM',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SRA',0
	.word	30656
	.byte	4,2,35,4,0,14
	.word	31428
	.byte	27
	.byte	'Ifx_SRC_DSADC',0,17,154,1,3
	.word	31476
	.byte	10
	.byte	'_Ifx_SRC_EMEM',0,17,157,1,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	31504
	.byte	27
	.byte	'Ifx_SRC_EMEM',0,17,160,1,3
	.word	31537
	.byte	15,40
	.word	669
	.byte	16,39,0,10
	.byte	'_Ifx_SRC_ERAY',0,17,163,1,25,80,13
	.byte	'INT',0
	.word	31090
	.byte	8,2,35,0,13
	.byte	'TINT',0
	.word	31090
	.byte	8,2,35,8,13
	.byte	'NDAT',0
	.word	31090
	.byte	8,2,35,16,13
	.byte	'MBSC',0
	.word	31090
	.byte	8,2,35,24,13
	.byte	'OBUSY',0
	.word	30656
	.byte	4,2,35,32,13
	.byte	'IBUSY',0
	.word	30656
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	31564
	.byte	40,2,35,40,0,14
	.word	31573
	.byte	27
	.byte	'Ifx_SRC_ERAY',0,17,172,1,3
	.word	31700
	.byte	10
	.byte	'_Ifx_SRC_ETH',0,17,175,1,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	31727
	.byte	27
	.byte	'Ifx_SRC_ETH',0,17,178,1,3
	.word	31759
	.byte	10
	.byte	'_Ifx_SRC_FCE',0,17,181,1,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	31785
	.byte	27
	.byte	'Ifx_SRC_FCE',0,17,184,1,3
	.word	31817
	.byte	10
	.byte	'_Ifx_SRC_FFT',0,17,187,1,25,12,13
	.byte	'DONE',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'RFS',0
	.word	30656
	.byte	4,2,35,8,0,14
	.word	31843
	.byte	27
	.byte	'Ifx_SRC_FFT',0,17,192,1,3
	.word	31903
	.byte	10
	.byte	'_Ifx_SRC_GPSR',0,17,195,1,25,32,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	30656
	.byte	4,2,35,12,13
	.byte	'reserved_10',0
	.word	13304
	.byte	16,2,35,16,0,14
	.word	31929
	.byte	27
	.byte	'Ifx_SRC_GPSR',0,17,202,1,3
	.word	32023
	.byte	10
	.byte	'_Ifx_SRC_GPT12',0,17,205,1,25,48,13
	.byte	'CIRQ',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'T2',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'T3',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'T4',0
	.word	30656
	.byte	4,2,35,12,13
	.byte	'T5',0
	.word	30656
	.byte	4,2,35,16,13
	.byte	'T6',0
	.word	30656
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	3726
	.byte	24,2,35,24,0,14
	.word	32050
	.byte	27
	.byte	'Ifx_SRC_GPT12',0,17,214,1,3
	.word	32167
	.byte	15,12
	.word	30656
	.byte	16,2,0,15,32
	.word	30656
	.byte	16,7,0,15,32
	.word	32204
	.byte	16,0,0,15,88
	.word	669
	.byte	16,87,0,15,108
	.word	30656
	.byte	16,26,0,15,96
	.word	669
	.byte	16,95,0,15,96
	.word	32204
	.byte	16,2,0,15,160,3
	.word	669
	.byte	16,159,3,0,15,64
	.word	32204
	.byte	16,1,0,15,192,3
	.word	669
	.byte	16,191,3,0,15,16
	.word	30656
	.byte	16,3,0,15,64
	.word	32289
	.byte	16,3,0,15,192,2
	.word	669
	.byte	16,191,2,0,15,52
	.word	669
	.byte	16,51,0,10
	.byte	'_Ifx_SRC_GTM',0,17,217,1,25,204,18,13
	.byte	'AEIIRQ',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'ARUIRQ',0
	.word	32195
	.byte	12,2,35,4,13
	.byte	'reserved_10',0
	.word	2536
	.byte	4,2,35,16,13
	.byte	'BRCIRQ',0
	.word	30656
	.byte	4,2,35,20,13
	.byte	'CMPIRQ',0
	.word	30656
	.byte	4,2,35,24,13
	.byte	'SPEIRQ',0
	.word	31090
	.byte	8,2,35,28,13
	.byte	'reserved_24',0
	.word	4355
	.byte	8,2,35,36,13
	.byte	'PSM',0
	.word	32213
	.byte	32,2,35,44,13
	.byte	'reserved_4C',0
	.word	32222
	.byte	88,2,35,76,13
	.byte	'DPLL',0
	.word	32231
	.byte	108,3,35,164,1,13
	.byte	'reserved_110',0
	.word	32240
	.byte	96,3,35,144,2,13
	.byte	'ERR',0
	.word	30656
	.byte	4,3,35,240,2,13
	.byte	'reserved_174',0
	.word	4695
	.byte	12,3,35,244,2,13
	.byte	'TIM',0
	.word	32249
	.byte	96,3,35,128,3,13
	.byte	'reserved_1E0',0
	.word	32258
	.byte	160,3,3,35,224,3,13
	.byte	'MCS',0
	.word	32249
	.byte	96,3,35,128,7,13
	.byte	'reserved_3E0',0
	.word	32258
	.byte	160,3,3,35,224,7,13
	.byte	'TOM',0
	.word	32269
	.byte	64,3,35,128,11,13
	.byte	'reserved_5C0',0
	.word	32278
	.byte	192,3,3,35,192,11,13
	.byte	'ATOM',0
	.word	32298
	.byte	64,3,35,128,15,13
	.byte	'reserved_7C0',0
	.word	32307
	.byte	192,2,3,35,192,15,13
	.byte	'MCSW0',0
	.word	32195
	.byte	12,3,35,128,18,13
	.byte	'reserved_90C',0
	.word	32318
	.byte	52,3,35,140,18,13
	.byte	'MCSW1',0
	.word	32195
	.byte	12,3,35,192,18,0,14
	.word	32327
	.byte	27
	.byte	'Ifx_SRC_GTM',0,17,243,1,3
	.word	32787
	.byte	10
	.byte	'_Ifx_SRC_HSCT',0,17,246,1,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	32813
	.byte	27
	.byte	'Ifx_SRC_HSCT',0,17,249,1,3
	.word	32846
	.byte	10
	.byte	'_Ifx_SRC_HSSL',0,17,252,1,25,16,13
	.byte	'COK',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'RDI',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'TRG',0
	.word	30656
	.byte	4,2,35,12,0,14
	.word	32873
	.byte	27
	.byte	'Ifx_SRC_HSSL',0,17,130,2,3
	.word	32946
	.byte	15,56
	.word	669
	.byte	16,55,0,10
	.byte	'_Ifx_SRC_I2C',0,17,133,2,25,80,13
	.byte	'BREQ',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'LBREQ',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SREQ',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'LSREQ',0
	.word	30656
	.byte	4,2,35,12,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,16,13
	.byte	'P',0
	.word	30656
	.byte	4,2,35,20,13
	.byte	'reserved_18',0
	.word	32973
	.byte	56,2,35,24,0,14
	.word	32982
	.byte	27
	.byte	'Ifx_SRC_I2C',0,17,142,2,3
	.word	33105
	.byte	10
	.byte	'_Ifx_SRC_LMU',0,17,145,2,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	33131
	.byte	27
	.byte	'Ifx_SRC_LMU',0,17,148,2,3
	.word	33163
	.byte	10
	.byte	'_Ifx_SRC_MSC',0,17,151,2,25,20,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	30656
	.byte	4,2,35,12,13
	.byte	'SR4',0
	.word	30656
	.byte	4,2,35,16,0,14
	.word	33189
	.byte	27
	.byte	'Ifx_SRC_MSC',0,17,158,2,3
	.word	33274
	.byte	10
	.byte	'_Ifx_SRC_PMU',0,17,161,2,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	33300
	.byte	27
	.byte	'Ifx_SRC_PMU',0,17,164,2,3
	.word	33332
	.byte	10
	.byte	'_Ifx_SRC_PSI5',0,17,167,2,25,32,13
	.byte	'SR',0
	.word	32204
	.byte	32,2,35,0,0,14
	.word	33358
	.byte	27
	.byte	'Ifx_SRC_PSI5',0,17,170,2,3
	.word	33391
	.byte	10
	.byte	'_Ifx_SRC_PSI5S',0,17,173,2,25,32,13
	.byte	'SR',0
	.word	32204
	.byte	32,2,35,0,0,14
	.word	33418
	.byte	27
	.byte	'Ifx_SRC_PSI5S',0,17,176,2,3
	.word	33452
	.byte	10
	.byte	'_Ifx_SRC_QSPI',0,17,179,2,25,24,13
	.byte	'TX',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'RX',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'ERR',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'PT',0
	.word	30656
	.byte	4,2,35,12,13
	.byte	'HC',0
	.word	30656
	.byte	4,2,35,16,13
	.byte	'U',0
	.word	30656
	.byte	4,2,35,20,0,14
	.word	33480
	.byte	27
	.byte	'Ifx_SRC_QSPI',0,17,187,2,3
	.word	33573
	.byte	10
	.byte	'_Ifx_SRC_SCR',0,17,190,2,25,4,13
	.byte	'SR',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	33600
	.byte	27
	.byte	'Ifx_SRC_SCR',0,17,193,2,3
	.word	33632
	.byte	10
	.byte	'_Ifx_SRC_SCU',0,17,196,2,25,20,13
	.byte	'DTS',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'ERU',0
	.word	32289
	.byte	16,2,35,4,0,14
	.word	33658
	.byte	27
	.byte	'Ifx_SRC_SCU',0,17,200,2,3
	.word	33704
	.byte	15,24
	.word	30656
	.byte	16,5,0,10
	.byte	'_Ifx_SRC_SENT',0,17,203,2,25,24,13
	.byte	'SR',0
	.word	33730
	.byte	24,2,35,0,0,14
	.word	33739
	.byte	27
	.byte	'Ifx_SRC_SENT',0,17,206,2,3
	.word	33772
	.byte	10
	.byte	'_Ifx_SRC_SMU',0,17,209,2,25,12,13
	.byte	'SR',0
	.word	32195
	.byte	12,2,35,0,0,14
	.word	33799
	.byte	27
	.byte	'Ifx_SRC_SMU',0,17,212,2,3
	.word	33831
	.byte	10
	.byte	'_Ifx_SRC_STM',0,17,215,2,25,8,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,0,14
	.word	33857
	.byte	27
	.byte	'Ifx_SRC_STM',0,17,219,2,3
	.word	33903
	.byte	10
	.byte	'_Ifx_SRC_VADCCG',0,17,222,2,25,16,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	30656
	.byte	4,2,35,12,0,14
	.word	33929
	.byte	27
	.byte	'Ifx_SRC_VADCCG',0,17,228,2,3
	.word	34004
	.byte	10
	.byte	'_Ifx_SRC_VADCG',0,17,231,2,25,16,13
	.byte	'SR0',0
	.word	30656
	.byte	4,2,35,0,13
	.byte	'SR1',0
	.word	30656
	.byte	4,2,35,4,13
	.byte	'SR2',0
	.word	30656
	.byte	4,2,35,8,13
	.byte	'SR3',0
	.word	30656
	.byte	4,2,35,12,0,14
	.word	34033
	.byte	27
	.byte	'Ifx_SRC_VADCG',0,17,237,2,3
	.word	34107
	.byte	10
	.byte	'_Ifx_SRC_XBAR',0,17,240,2,25,4,13
	.byte	'SRC',0
	.word	30656
	.byte	4,2,35,0,0,14
	.word	34135
	.byte	27
	.byte	'Ifx_SRC_XBAR',0,17,243,2,3
	.word	34169
	.byte	15,4
	.word	30716
	.byte	16,0,0,14
	.word	34196
	.byte	10
	.byte	'_Ifx_SRC_GAGBT',0,17,128,3,25,4,13
	.byte	'AGBT',0
	.word	34205
	.byte	4,2,35,0,0,14
	.word	34210
	.byte	27
	.byte	'Ifx_SRC_GAGBT',0,17,131,3,3
	.word	34246
	.byte	15,48
	.word	30774
	.byte	16,3,0,14
	.word	34274
	.byte	10
	.byte	'_Ifx_SRC_GASCLIN',0,17,134,3,25,48,13
	.byte	'ASCLIN',0
	.word	34283
	.byte	48,2,35,0,0,14
	.word	34288
	.byte	27
	.byte	'Ifx_SRC_GASCLIN',0,17,137,3,3
	.word	34328
	.byte	14
	.word	30861
	.byte	10
	.byte	'_Ifx_SRC_GBCU',0,17,140,3,25,4,13
	.byte	'SPB',0
	.word	34358
	.byte	4,2,35,0,0,14
	.word	34363
	.byte	27
	.byte	'Ifx_SRC_GBCU',0,17,143,3,3
	.word	34397
	.byte	15,64
	.word	30935
	.byte	16,0,0,14
	.word	34424
	.byte	10
	.byte	'_Ifx_SRC_GCAN',0,17,146,3,25,64,13
	.byte	'CAN',0
	.word	34433
	.byte	64,2,35,0,0,14
	.word	34438
	.byte	27
	.byte	'Ifx_SRC_GCAN',0,17,149,3,3
	.word	34472
	.byte	15,32
	.word	30992
	.byte	16,1,0,14
	.word	34499
	.byte	10
	.byte	'_Ifx_SRC_GCCU6',0,17,152,3,25,32,13
	.byte	'CCU6',0
	.word	34508
	.byte	32,2,35,0,0,14
	.word	34513
	.byte	27
	.byte	'Ifx_SRC_GCCU6',0,17,155,3,3
	.word	34549
	.byte	14
	.word	31099
	.byte	10
	.byte	'_Ifx_SRC_GCERBERUS',0,17,158,3,25,8,13
	.byte	'CERBERUS',0
	.word	34577
	.byte	8,2,35,0,0,14
	.word	34582
	.byte	27
	.byte	'Ifx_SRC_GCERBERUS',0,17,161,3,3
	.word	34626
	.byte	15,16
	.word	31165
	.byte	16,0,0,14
	.word	34658
	.byte	10
	.byte	'_Ifx_SRC_GCIF',0,17,164,3,25,16,13
	.byte	'CIF',0
	.word	34667
	.byte	16,2,35,0,0,14
	.word	34672
	.byte	27
	.byte	'Ifx_SRC_GCIF',0,17,167,3,3
	.word	34706
	.byte	15,8
	.word	31264
	.byte	16,1,0,14
	.word	34733
	.byte	10
	.byte	'_Ifx_SRC_GCPU',0,17,170,3,25,8,13
	.byte	'CPU',0
	.word	34742
	.byte	8,2,35,0,0,14
	.word	34747
	.byte	27
	.byte	'Ifx_SRC_GCPU',0,17,173,3,3
	.word	34781
	.byte	15,208,1
	.word	31335
	.byte	16,0,0,14
	.word	34808
	.byte	10
	.byte	'_Ifx_SRC_GDMA',0,17,176,3,25,208,1,13
	.byte	'DMA',0
	.word	34818
	.byte	208,1,2,35,0,0,14
	.word	34823
	.byte	27
	.byte	'Ifx_SRC_GDMA',0,17,179,3,3
	.word	34859
	.byte	14
	.word	31428
	.byte	14
	.word	31428
	.byte	14
	.word	31428
	.byte	10
	.byte	'_Ifx_SRC_GDSADC',0,17,182,3,25,32,13
	.byte	'DSADC0',0
	.word	34886
	.byte	8,2,35,0,13
	.byte	'reserved_8',0
	.word	4355
	.byte	8,2,35,8,13
	.byte	'DSADC2',0
	.word	34891
	.byte	8,2,35,16,13
	.byte	'DSADC3',0
	.word	34896
	.byte	8,2,35,24,0,14
	.word	34901
	.byte	27
	.byte	'Ifx_SRC_GDSADC',0,17,188,3,3
	.word	34992
	.byte	15,4
	.word	31504
	.byte	16,0,0,14
	.word	35021
	.byte	10
	.byte	'_Ifx_SRC_GEMEM',0,17,191,3,25,4,13
	.byte	'EMEM',0
	.word	35030
	.byte	4,2,35,0,0,14
	.word	35035
	.byte	27
	.byte	'Ifx_SRC_GEMEM',0,17,194,3,3
	.word	35071
	.byte	15,80
	.word	31573
	.byte	16,0,0,14
	.word	35099
	.byte	10
	.byte	'_Ifx_SRC_GERAY',0,17,197,3,25,80,13
	.byte	'ERAY',0
	.word	35108
	.byte	80,2,35,0,0,14
	.word	35113
	.byte	27
	.byte	'Ifx_SRC_GERAY',0,17,200,3,3
	.word	35149
	.byte	15,4
	.word	31727
	.byte	16,0,0,14
	.word	35177
	.byte	10
	.byte	'_Ifx_SRC_GETH',0,17,203,3,25,4,13
	.byte	'ETH',0
	.word	35186
	.byte	4,2,35,0,0,14
	.word	35191
	.byte	27
	.byte	'Ifx_SRC_GETH',0,17,206,3,3
	.word	35225
	.byte	15,4
	.word	31785
	.byte	16,0,0,14
	.word	35252
	.byte	10
	.byte	'_Ifx_SRC_GFCE',0,17,209,3,25,4,13
	.byte	'FCE',0
	.word	35261
	.byte	4,2,35,0,0,14
	.word	35266
	.byte	27
	.byte	'Ifx_SRC_GFCE',0,17,212,3,3
	.word	35300
	.byte	15,12
	.word	31843
	.byte	16,0,0,14
	.word	35327
	.byte	10
	.byte	'_Ifx_SRC_GFFT',0,17,215,3,25,12,13
	.byte	'FFT',0
	.word	35336
	.byte	12,2,35,0,0,14
	.word	35341
	.byte	27
	.byte	'Ifx_SRC_GFFT',0,17,218,3,3
	.word	35375
	.byte	15,64
	.word	31929
	.byte	16,1,0,14
	.word	35402
	.byte	10
	.byte	'_Ifx_SRC_GGPSR',0,17,221,3,25,64,13
	.byte	'GPSR',0
	.word	35411
	.byte	64,2,35,0,0,14
	.word	35416
	.byte	27
	.byte	'Ifx_SRC_GGPSR',0,17,224,3,3
	.word	35452
	.byte	15,48
	.word	32050
	.byte	16,0,0,14
	.word	35480
	.byte	10
	.byte	'_Ifx_SRC_GGPT12',0,17,227,3,25,48,13
	.byte	'GPT12',0
	.word	35489
	.byte	48,2,35,0,0,14
	.word	35494
	.byte	27
	.byte	'Ifx_SRC_GGPT12',0,17,230,3,3
	.word	35532
	.byte	15,204,18
	.word	32327
	.byte	16,0,0,14
	.word	35561
	.byte	10
	.byte	'_Ifx_SRC_GGTM',0,17,233,3,25,204,18,13
	.byte	'GTM',0
	.word	35571
	.byte	204,18,2,35,0,0,14
	.word	35576
	.byte	27
	.byte	'Ifx_SRC_GGTM',0,17,236,3,3
	.word	35612
	.byte	15,4
	.word	32813
	.byte	16,0,0,14
	.word	35639
	.byte	10
	.byte	'_Ifx_SRC_GHSCT',0,17,239,3,25,4,13
	.byte	'HSCT',0
	.word	35648
	.byte	4,2,35,0,0,14
	.word	35653
	.byte	27
	.byte	'Ifx_SRC_GHSCT',0,17,242,3,3
	.word	35689
	.byte	15,64
	.word	32873
	.byte	16,3,0,14
	.word	35717
	.byte	10
	.byte	'_Ifx_SRC_GHSSL',0,17,245,3,25,68,13
	.byte	'HSSL',0
	.word	35726
	.byte	64,2,35,0,13
	.byte	'EXI',0
	.word	30656
	.byte	4,2,35,64,0,14
	.word	35731
	.byte	27
	.byte	'Ifx_SRC_GHSSL',0,17,249,3,3
	.word	35780
	.byte	15,80
	.word	32982
	.byte	16,0,0,14
	.word	35808
	.byte	10
	.byte	'_Ifx_SRC_GI2C',0,17,252,3,25,80,13
	.byte	'I2C',0
	.word	35817
	.byte	80,2,35,0,0,14
	.word	35822
	.byte	27
	.byte	'Ifx_SRC_GI2C',0,17,255,3,3
	.word	35856
	.byte	15,4
	.word	33131
	.byte	16,0,0,14
	.word	35883
	.byte	10
	.byte	'_Ifx_SRC_GLMU',0,17,130,4,25,4,13
	.byte	'LMU',0
	.word	35892
	.byte	4,2,35,0,0,14
	.word	35897
	.byte	27
	.byte	'Ifx_SRC_GLMU',0,17,133,4,3
	.word	35931
	.byte	15,40
	.word	33189
	.byte	16,1,0,14
	.word	35958
	.byte	10
	.byte	'_Ifx_SRC_GMSC',0,17,136,4,25,40,13
	.byte	'MSC',0
	.word	35967
	.byte	40,2,35,0,0,14
	.word	35972
	.byte	27
	.byte	'Ifx_SRC_GMSC',0,17,139,4,3
	.word	36006
	.byte	15,8
	.word	33300
	.byte	16,1,0,14
	.word	36033
	.byte	10
	.byte	'_Ifx_SRC_GPMU',0,17,142,4,25,8,13
	.byte	'PMU',0
	.word	36042
	.byte	8,2,35,0,0,14
	.word	36047
	.byte	27
	.byte	'Ifx_SRC_GPMU',0,17,145,4,3
	.word	36081
	.byte	15,32
	.word	33358
	.byte	16,0,0,14
	.word	36108
	.byte	10
	.byte	'_Ifx_SRC_GPSI5',0,17,148,4,25,32,13
	.byte	'PSI5',0
	.word	36117
	.byte	32,2,35,0,0,14
	.word	36122
	.byte	27
	.byte	'Ifx_SRC_GPSI5',0,17,151,4,3
	.word	36158
	.byte	15,32
	.word	33418
	.byte	16,0,0,14
	.word	36186
	.byte	10
	.byte	'_Ifx_SRC_GPSI5S',0,17,154,4,25,32,13
	.byte	'PSI5S',0
	.word	36195
	.byte	32,2,35,0,0,14
	.word	36200
	.byte	27
	.byte	'Ifx_SRC_GPSI5S',0,17,157,4,3
	.word	36238
	.byte	15,96
	.word	33480
	.byte	16,3,0,14
	.word	36267
	.byte	10
	.byte	'_Ifx_SRC_GQSPI',0,17,160,4,25,96,13
	.byte	'QSPI',0
	.word	36276
	.byte	96,2,35,0,0,14
	.word	36281
	.byte	27
	.byte	'Ifx_SRC_GQSPI',0,17,163,4,3
	.word	36317
	.byte	15,4
	.word	33600
	.byte	16,0,0,14
	.word	36345
	.byte	10
	.byte	'_Ifx_SRC_GSCR',0,17,166,4,25,4,13
	.byte	'SCR',0
	.word	36354
	.byte	4,2,35,0,0,14
	.word	36359
	.byte	27
	.byte	'Ifx_SRC_GSCR',0,17,169,4,3
	.word	36393
	.byte	14
	.word	33658
	.byte	10
	.byte	'_Ifx_SRC_GSCU',0,17,172,4,25,20,13
	.byte	'SCU',0
	.word	36420
	.byte	20,2,35,0,0,14
	.word	36425
	.byte	27
	.byte	'Ifx_SRC_GSCU',0,17,175,4,3
	.word	36459
	.byte	15,24
	.word	33739
	.byte	16,0,0,14
	.word	36486
	.byte	10
	.byte	'_Ifx_SRC_GSENT',0,17,178,4,25,24,13
	.byte	'SENT',0
	.word	36495
	.byte	24,2,35,0,0,14
	.word	36500
	.byte	27
	.byte	'Ifx_SRC_GSENT',0,17,181,4,3
	.word	36536
	.byte	15,12
	.word	33799
	.byte	16,0,0,14
	.word	36564
	.byte	10
	.byte	'_Ifx_SRC_GSMU',0,17,184,4,25,12,13
	.byte	'SMU',0
	.word	36573
	.byte	12,2,35,0,0,14
	.word	36578
	.byte	27
	.byte	'Ifx_SRC_GSMU',0,17,187,4,3
	.word	36612
	.byte	15,16
	.word	33857
	.byte	16,1,0,14
	.word	36639
	.byte	10
	.byte	'_Ifx_SRC_GSTM',0,17,190,4,25,16,13
	.byte	'STM',0
	.word	36648
	.byte	16,2,35,0,0,14
	.word	36653
	.byte	27
	.byte	'Ifx_SRC_GSTM',0,17,193,4,3
	.word	36687
	.byte	15,64
	.word	34033
	.byte	16,3,0,14
	.word	36714
	.byte	15,224,1
	.word	669
	.byte	16,223,1,0,15,32
	.word	33929
	.byte	16,1,0,14
	.word	36739
	.byte	10
	.byte	'_Ifx_SRC_GVADC',0,17,196,4,25,192,2,13
	.byte	'G',0
	.word	36723
	.byte	64,2,35,0,13
	.byte	'reserved_40',0
	.word	36728
	.byte	224,1,2,35,64,13
	.byte	'CG',0
	.word	36748
	.byte	32,3,35,160,2,0,14
	.word	36753
	.byte	27
	.byte	'Ifx_SRC_GVADC',0,17,201,4,3
	.word	36822
	.byte	14
	.word	34135
	.byte	10
	.byte	'_Ifx_SRC_GXBAR',0,17,204,4,25,4,13
	.byte	'XBAR',0
	.word	36850
	.byte	4,2,35,0,0,14
	.word	36855
	.byte	27
	.byte	'Ifx_SRC_GXBAR',0,17,207,4,3
	.word	36891
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_ACCEN0_Bits',0,4,79,3
	.word	36919
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1_Bits',0,4,85,3
	.word	37476
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,4,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,4,94,3
	.word	37553
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,4,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	669
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON0_Bits',0,4,111,3
	.word	37689
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,4,114,16,4,11
	.byte	'CANDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	669
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON1_Bits',0,4,126,3
	.word	37969
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,4,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON2_Bits',0,4,135,1,3
	.word	38207
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,4,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	669
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON3_Bits',0,4,150,1,3
	.word	38335
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,4,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	669
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	669
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON4_Bits',0,4,165,1,3
	.word	38578
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,4,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CCUCON5_Bits',0,4,174,1,3
	.word	38813
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,4,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6_Bits',0,4,181,1,3
	.word	38941
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,4,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7_Bits',0,4,188,1,3
	.word	39041
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,4,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	669
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_CHIPID_Bits',0,4,202,1,3
	.word	39141
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,4,205,1,16,4,11
	.byte	'PWD',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	494
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSCON_Bits',0,4,213,1,3
	.word	39349
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,4,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	686
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_DTSLIM_Bits',0,4,225,1,3
	.word	39514
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,4,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,4,235,1,3
	.word	39697
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,4,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	494
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	669
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EICR_Bits',0,4,129,2,3
	.word	39851
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,4,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR_Bits',0,4,143,2,3
	.word	40215
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,4,146,2,16,4,11
	.byte	'POL',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	686
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	669
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_EMSR_Bits',0,4,159,2,3
	.word	40426
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,4,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG_Bits',0,4,167,2,3
	.word	40678
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,4,170,2,16,4,11
	.byte	'ARI',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG_Bits',0,4,175,2,3
	.word	40796
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,4,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR13CON_Bits',0,4,185,2,3
	.word	40907
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,4,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVR33CON_Bits',0,4,195,2,3
	.word	41070
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,4,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,4,205,2,3
	.word	41233
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,4,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,4,215,2,3
	.word	41391
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,4,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	669
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	669
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	10,0,2,35,2,0,27
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,4,232,2,3
	.word	41556
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,4,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	669
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	669
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	686
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,4,245,2,3
	.word	41885
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,4,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVROVMON_Bits',0,4,255,2,3
	.word	42106
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,4,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,4,142,3,3
	.word	42269
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,4,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,4,152,3,3
	.word	42541
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,4,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,4,162,3,3
	.word	42694
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,4,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,4,172,3,3
	.word	42850
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,4,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,4,181,3,3
	.word	43012
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,4,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,4,191,3,3
	.word	43155
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,4,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,4,200,3,3
	.word	43320
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,4,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,4,211,3,3
	.word	43465
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,4,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,4,222,3,3
	.word	43646
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,4,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,4,232,3,3
	.word	43820
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,4,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,4,241,3,3
	.word	43980
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,4,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,4,130,4,3
	.word	44124
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,4,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,4,139,4,3
	.word	44398
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,4,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,4,149,4,3
	.word	44537
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,4,152,4,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	669
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	686
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	669
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	669
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_EXTCON_Bits',0,4,163,4,3
	.word	44700
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,4,166,4,16,4,11
	.byte	'STEP',0,2
	.word	686
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	686
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_FDR_Bits',0,4,174,4,3
	.word	44918
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,4,177,4,16,4,11
	.byte	'FS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_SCU_FMR_Bits',0,4,197,4,3
	.word	45081
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,4,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_ID_Bits',0,4,205,4,3
	.word	45417
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,4,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	669
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_SCU_IGCR_Bits',0,4,232,4,3
	.word	45524
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,4,235,4,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_IN_Bits',0,4,240,4,3
	.word	45976
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,4,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_IOCR_Bits',0,4,250,4,3
	.word	46075
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,4,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	686
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,4,131,5,3
	.word	46225
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,4,134,5,16,4,11
	.byte	'SEED',0,4
	.word	494
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,4,141,5,3
	.word	46374
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,4,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,4,149,5,3
	.word	46535
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,4,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	686
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_LCLCON_Bits',0,4,158,5,3
	.word	46665
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,4,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST_Bits',0,4,166,5,3
	.word	46797
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,4,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	686
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_MANID_Bits',0,4,174,5,3
	.word	46912
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,4,177,5,16,4,11
	.byte	'PS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	686
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_OMR_Bits',0,4,185,5,3
	.word	47023
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,4,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	669
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	669
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	669
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_OSCCON_Bits',0,4,209,5,3
	.word	47181
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,4,212,5,16,4,11
	.byte	'P0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_OUT_Bits',0,4,217,5,3
	.word	47593
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,4,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	686
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	6,0,2,35,3,0,27
	.byte	'Ifx_SCU_OVCCON_Bits',0,4,233,5,3
	.word	47694
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,4,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	494
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,4,242,5,3
	.word	47961
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,4,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC_Bits',0,4,250,5,3
	.word	48097
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,4,253,5,16,4,11
	.byte	'PD0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	669
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDR_Bits',0,4,132,6,3
	.word	48208
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,4,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR_Bits',0,4,146,6,3
	.word	48341
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,4,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLCON0_Bits',0,4,166,6,3
	.word	48544
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,4,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	669
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	669
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON1_Bits',0,4,177,6,3
	.word	48900
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,4,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	686
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLCON2_Bits',0,4,184,6,3
	.word	49078
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,4,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	669
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,4,204,6,3
	.word	49178
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,4,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	669
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	669
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	669
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	9,0,2,35,2,0,27
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,4,215,6,3
	.word	49548
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,4,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,4,227,6,3
	.word	49734
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,4,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,4,241,6,3
	.word	49932
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,4,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	494
	.byte	21,0,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR_Bits',0,4,251,6,3
	.word	50165
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,4,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	669
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	669
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,4,153,7,3
	.word	50317
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,4,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	669
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	669
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	669
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,4,170,7,3
	.word	50884
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,4,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	669
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,4,187,7,3
	.word	51178
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,4,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	669
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	669
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,4,214,7,3
	.word	51456
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,4,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,4,230,7,3
	.word	51952
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,4,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	686
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON2_Bits',0,4,243,7,3
	.word	52265
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,4,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	669
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	669
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	669
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	669
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_RSTCON_Bits',0,4,129,8,3
	.word	52474
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,4,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	669
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,4,155,8,3
	.word	52685
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,4,158,8,16,4,11
	.byte	'HBT',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON_Bits',0,4,162,8,3
	.word	53117
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,4,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	669
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	669
	.byte	7,0,2,35,3,0,27
	.byte	'Ifx_SCU_STSTAT_Bits',0,4,178,8,3
	.word	53213
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,4,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,4,186,8,3
	.word	53473
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,4,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	494
	.byte	23,0,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON_Bits',0,4,198,8,3
	.word	53598
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,4,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,4,208,8,3
	.word	53795
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,4,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,4,218,8,3
	.word	53948
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,4,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET_Bits',0,4,228,8,3
	.word	54101
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,4,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,4,238,8,3
	.word	54254
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,4,247,8,3
	.word	533
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,4,134,9,3
	.word	708
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,4,150,9,3
	.word	952
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,4,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	517
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	517
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	517
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	517
	.byte	16,0,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,4,159,9,3
	.word	54509
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,4,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,4,175,9,3
	.word	54635
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,4,178,9,16,4,11
	.byte	'AE',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,4,191,9,3
	.word	54887
	.byte	12,4,199,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36919
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN0',0,4,204,9,3
	.word	55106
	.byte	12,4,207,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37476
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ACCEN1',0,4,212,9,3
	.word	55170
	.byte	12,4,215,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37553
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ARSTDIS',0,4,220,9,3
	.word	55234
	.byte	12,4,223,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37689
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON0',0,4,228,9,3
	.word	55299
	.byte	12,4,231,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37969
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON1',0,4,236,9,3
	.word	55364
	.byte	12,4,239,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38207
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON2',0,4,244,9,3
	.word	55429
	.byte	12,4,247,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38335
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON3',0,4,252,9,3
	.word	55494
	.byte	12,4,255,9,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38578
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON4',0,4,132,10,3
	.word	55559
	.byte	12,4,135,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38813
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON5',0,4,140,10,3
	.word	55624
	.byte	12,4,143,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	38941
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON6',0,4,148,10,3
	.word	55689
	.byte	12,4,151,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39041
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CCUCON7',0,4,156,10,3
	.word	55754
	.byte	12,4,159,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39141
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_CHIPID',0,4,164,10,3
	.word	55819
	.byte	12,4,167,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39349
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSCON',0,4,172,10,3
	.word	55883
	.byte	12,4,175,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39514
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSLIM',0,4,180,10,3
	.word	55947
	.byte	12,4,183,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39697
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_DTSSTAT',0,4,188,10,3
	.word	56011
	.byte	12,4,191,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	39851
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EICR',0,4,196,10,3
	.word	56076
	.byte	12,4,199,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40215
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EIFR',0,4,204,10,3
	.word	56138
	.byte	12,4,207,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40426
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EMSR',0,4,212,10,3
	.word	56200
	.byte	12,4,215,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40678
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESRCFG',0,4,220,10,3
	.word	56262
	.byte	12,4,223,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40796
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ESROCFG',0,4,228,10,3
	.word	56326
	.byte	12,4,231,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	40907
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR13CON',0,4,236,10,3
	.word	56391
	.byte	12,4,239,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41070
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVR33CON',0,4,244,10,3
	.word	56457
	.byte	12,4,247,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41233
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRADCSTAT',0,4,252,10,3
	.word	56523
	.byte	12,4,255,10,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41391
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRDVSTAT',0,4,132,11,3
	.word	56591
	.byte	12,4,135,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41556
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRMONCTRL',0,4,140,11,3
	.word	56658
	.byte	12,4,143,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	41885
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROSCCTRL',0,4,148,11,3
	.word	56726
	.byte	12,4,151,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42106
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVROVMON',0,4,156,11,3
	.word	56794
	.byte	12,4,159,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42269
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRRSTCON',0,4,164,11,3
	.word	56860
	.byte	12,4,167,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42541
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,4,172,11,3
	.word	56927
	.byte	12,4,175,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42694
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,4,180,11,3
	.word	56996
	.byte	12,4,183,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	42850
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,4,188,11,3
	.word	57065
	.byte	12,4,191,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43012
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,4,196,11,3
	.word	57134
	.byte	12,4,199,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43155
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,4,204,11,3
	.word	57203
	.byte	12,4,207,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43320
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,4,212,11,3
	.word	57272
	.byte	12,4,215,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43465
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL1',0,4,220,11,3
	.word	57341
	.byte	12,4,223,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43646
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL2',0,4,228,11,3
	.word	57409
	.byte	12,4,231,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43820
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL3',0,4,236,11,3
	.word	57477
	.byte	12,4,239,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	43980
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSDCTRL4',0,4,244,11,3
	.word	57545
	.byte	12,4,247,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44124
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRSTAT',0,4,252,11,3
	.word	57613
	.byte	12,4,255,11,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44398
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRTRIM',0,4,132,12,3
	.word	57678
	.byte	12,4,135,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44537
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EVRUVMON',0,4,140,12,3
	.word	57743
	.byte	12,4,143,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44700
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_EXTCON',0,4,148,12,3
	.word	57809
	.byte	12,4,151,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	44918
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FDR',0,4,156,12,3
	.word	57873
	.byte	12,4,159,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45081
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_FMR',0,4,164,12,3
	.word	57934
	.byte	12,4,167,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45417
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_ID',0,4,172,12,3
	.word	57995
	.byte	12,4,175,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45524
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IGCR',0,4,180,12,3
	.word	58055
	.byte	12,4,183,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	45976
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IN',0,4,188,12,3
	.word	58117
	.byte	12,4,191,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46075
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_IOCR',0,4,196,12,3
	.word	58177
	.byte	12,4,199,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46225
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL0',0,4,204,12,3
	.word	58239
	.byte	12,4,207,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46374
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL1',0,4,212,12,3
	.word	58307
	.byte	12,4,215,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46535
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LBISTCTRL2',0,4,220,12,3
	.word	58375
	.byte	12,4,223,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46665
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLCON',0,4,228,12,3
	.word	58443
	.byte	12,4,231,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46797
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_LCLTEST',0,4,236,12,3
	.word	58507
	.byte	12,4,239,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	46912
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_MANID',0,4,244,12,3
	.word	58572
	.byte	12,4,247,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47023
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OMR',0,4,252,12,3
	.word	58635
	.byte	12,4,255,12,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47181
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OSCCON',0,4,132,13,3
	.word	58696
	.byte	12,4,135,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47593
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OUT',0,4,140,13,3
	.word	58760
	.byte	12,4,143,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47694
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCCON',0,4,148,13,3
	.word	58821
	.byte	12,4,151,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	47961
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_OVCENABLE',0,4,156,13,3
	.word	58885
	.byte	12,4,159,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48097
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDISC',0,4,164,13,3
	.word	58952
	.byte	12,4,167,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48208
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDR',0,4,172,13,3
	.word	59015
	.byte	12,4,175,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48341
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PDRR',0,4,180,13,3
	.word	59076
	.byte	12,4,183,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48544
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON0',0,4,188,13,3
	.word	59138
	.byte	12,4,191,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	48900
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON1',0,4,196,13,3
	.word	59203
	.byte	12,4,199,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49078
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLCON2',0,4,204,13,3
	.word	59268
	.byte	12,4,207,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49178
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON0',0,4,212,13,3
	.word	59333
	.byte	12,4,215,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49548
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYCON1',0,4,220,13,3
	.word	59402
	.byte	12,4,223,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49734
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLERAYSTAT',0,4,228,13,3
	.word	59471
	.byte	12,4,231,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	49932
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PLLSTAT',0,4,236,13,3
	.word	59540
	.byte	12,4,239,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50165
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMCSR',0,4,244,13,3
	.word	59605
	.byte	12,4,247,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50317
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR0',0,4,252,13,3
	.word	59668
	.byte	12,4,255,13,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	50884
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR1',0,4,132,14,3
	.word	59733
	.byte	12,4,135,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51178
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWCR2',0,4,140,14,3
	.word	59798
	.byte	12,4,143,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51456
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTAT',0,4,148,14,3
	.word	59863
	.byte	12,4,151,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	51952
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_PMSWSTATCLR',0,4,156,14,3
	.word	59929
	.byte	12,4,159,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52474
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON',0,4,164,14,3
	.word	59998
	.byte	12,4,167,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52265
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTCON2',0,4,172,14,3
	.word	60062
	.byte	12,4,175,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	52685
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_RSTSTAT',0,4,180,14,3
	.word	60127
	.byte	12,4,183,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53117
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SAFECON',0,4,188,14,3
	.word	60192
	.byte	12,4,191,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53213
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_STSTAT',0,4,196,14,3
	.word	60257
	.byte	12,4,199,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53473
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SWRSTCON',0,4,204,14,3
	.word	60321
	.byte	12,4,207,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53598
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_SYSCON',0,4,212,14,3
	.word	60387
	.byte	12,4,215,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53795
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPCLR',0,4,220,14,3
	.word	60451
	.byte	12,4,223,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	53948
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPDIS',0,4,228,14,3
	.word	60516
	.byte	12,4,231,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54101
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSET',0,4,236,14,3
	.word	60581
	.byte	12,4,239,14,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54254
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_TRAPSTAT',0,4,244,14,3
	.word	60646
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON0',0,4,252,14,3
	.word	629
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_CON1',0,4,132,15,3
	.word	912
	.byte	27
	.byte	'Ifx_SCU_WDTCPU_SR',0,4,140,15,3
	.word	1143
	.byte	12,4,143,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54509
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON0',0,4,148,15,3
	.word	60797
	.byte	12,4,151,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54635
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_CON1',0,4,156,15,3
	.word	60864
	.byte	12,4,159,15,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	54887
	.byte	4,2,35,0,0,27
	.byte	'Ifx_SCU_WDTS_SR',0,4,164,15,3
	.word	60931
	.byte	14
	.word	1183
	.byte	27
	.byte	'Ifx_SCU_WDTCPU',0,4,180,15,3
	.word	60996
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,4,183,15,25,12,13
	.byte	'CON0',0
	.word	60797
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	60864
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	60931
	.byte	4,2,35,8,0,14
	.word	61025
	.byte	27
	.byte	'Ifx_SCU_WDTS',0,4,188,15,3
	.word	61086
	.byte	15,8
	.word	56262
	.byte	16,1,0,15,20
	.word	669
	.byte	16,19,0,15,8
	.word	59605
	.byte	16,1,0,14
	.word	61025
	.byte	15,24
	.word	1183
	.byte	16,1,0,14
	.word	61145
	.byte	15,16
	.word	56076
	.byte	16,3,0,15,16
	.word	58055
	.byte	16,3,0,15,180,3
	.word	669
	.byte	16,179,3,0,10
	.byte	'_Ifx_SCU',0,4,201,15,25,128,8,13
	.byte	'reserved_0',0
	.word	4355
	.byte	8,2,35,0,13
	.byte	'ID',0
	.word	57995
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	2536
	.byte	4,2,35,12,13
	.byte	'OSCCON',0
	.word	58696
	.byte	4,2,35,16,13
	.byte	'PLLSTAT',0
	.word	59540
	.byte	4,2,35,20,13
	.byte	'PLLCON0',0
	.word	59138
	.byte	4,2,35,24,13
	.byte	'PLLCON1',0
	.word	59203
	.byte	4,2,35,28,13
	.byte	'PLLCON2',0
	.word	59268
	.byte	4,2,35,32,13
	.byte	'PLLERAYSTAT',0
	.word	59471
	.byte	4,2,35,36,13
	.byte	'PLLERAYCON0',0
	.word	59333
	.byte	4,2,35,40,13
	.byte	'PLLERAYCON1',0
	.word	59402
	.byte	4,2,35,44,13
	.byte	'CCUCON0',0
	.word	55299
	.byte	4,2,35,48,13
	.byte	'CCUCON1',0
	.word	55364
	.byte	4,2,35,52,13
	.byte	'FDR',0
	.word	57873
	.byte	4,2,35,56,13
	.byte	'EXTCON',0
	.word	57809
	.byte	4,2,35,60,13
	.byte	'CCUCON2',0
	.word	55429
	.byte	4,2,35,64,13
	.byte	'CCUCON3',0
	.word	55494
	.byte	4,2,35,68,13
	.byte	'CCUCON4',0
	.word	55559
	.byte	4,2,35,72,13
	.byte	'CCUCON5',0
	.word	55624
	.byte	4,2,35,76,13
	.byte	'RSTSTAT',0
	.word	60127
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	2536
	.byte	4,2,35,84,13
	.byte	'RSTCON',0
	.word	59998
	.byte	4,2,35,88,13
	.byte	'ARSTDIS',0
	.word	55234
	.byte	4,2,35,92,13
	.byte	'SWRSTCON',0
	.word	60321
	.byte	4,2,35,96,13
	.byte	'RSTCON2',0
	.word	60062
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	2536
	.byte	4,2,35,104,13
	.byte	'EVRRSTCON',0
	.word	56860
	.byte	4,2,35,108,13
	.byte	'ESRCFG',0
	.word	61113
	.byte	8,2,35,112,13
	.byte	'ESROCFG',0
	.word	56326
	.byte	4,2,35,120,13
	.byte	'SYSCON',0
	.word	60387
	.byte	4,2,35,124,13
	.byte	'CCUCON6',0
	.word	55689
	.byte	4,3,35,128,1,13
	.byte	'CCUCON7',0
	.word	55754
	.byte	4,3,35,132,1,13
	.byte	'reserved_88',0
	.word	61122
	.byte	20,3,35,136,1,13
	.byte	'PDR',0
	.word	59015
	.byte	4,3,35,156,1,13
	.byte	'IOCR',0
	.word	58177
	.byte	4,3,35,160,1,13
	.byte	'OUT',0
	.word	58760
	.byte	4,3,35,164,1,13
	.byte	'OMR',0
	.word	58635
	.byte	4,3,35,168,1,13
	.byte	'IN',0
	.word	58117
	.byte	4,3,35,172,1,13
	.byte	'EVRSTAT',0
	.word	57613
	.byte	4,3,35,176,1,13
	.byte	'EVRDVSTAT',0
	.word	56591
	.byte	4,3,35,180,1,13
	.byte	'EVR13CON',0
	.word	56391
	.byte	4,3,35,184,1,13
	.byte	'EVR33CON',0
	.word	56457
	.byte	4,3,35,188,1,13
	.byte	'STSTAT',0
	.word	60257
	.byte	4,3,35,192,1,13
	.byte	'reserved_C4',0
	.word	2536
	.byte	4,3,35,196,1,13
	.byte	'PMSWCR0',0
	.word	59668
	.byte	4,3,35,200,1,13
	.byte	'PMSWSTAT',0
	.word	59863
	.byte	4,3,35,204,1,13
	.byte	'PMSWSTATCLR',0
	.word	59929
	.byte	4,3,35,208,1,13
	.byte	'PMCSR',0
	.word	61131
	.byte	8,3,35,212,1,13
	.byte	'reserved_DC',0
	.word	2536
	.byte	4,3,35,220,1,13
	.byte	'DTSSTAT',0
	.word	56011
	.byte	4,3,35,224,1,13
	.byte	'DTSCON',0
	.word	55883
	.byte	4,3,35,228,1,13
	.byte	'PMSWCR1',0
	.word	59733
	.byte	4,3,35,232,1,13
	.byte	'PMSWCR2',0
	.word	59798
	.byte	4,3,35,236,1,13
	.byte	'WDTS',0
	.word	61140
	.byte	12,3,35,240,1,13
	.byte	'EMSR',0
	.word	56200
	.byte	4,3,35,252,1,13
	.byte	'WDTCPU',0
	.word	61154
	.byte	24,3,35,128,2,13
	.byte	'reserved_118',0
	.word	4695
	.byte	12,3,35,152,2,13
	.byte	'TRAPSTAT',0
	.word	60646
	.byte	4,3,35,164,2,13
	.byte	'TRAPSET',0
	.word	60581
	.byte	4,3,35,168,2,13
	.byte	'TRAPCLR',0
	.word	60451
	.byte	4,3,35,172,2,13
	.byte	'TRAPDIS',0
	.word	60516
	.byte	4,3,35,176,2,13
	.byte	'reserved_134',0
	.word	2536
	.byte	4,3,35,180,2,13
	.byte	'LCLCON1',0
	.word	58443
	.byte	4,3,35,184,2,13
	.byte	'LCLTEST',0
	.word	58507
	.byte	4,3,35,188,2,13
	.byte	'CHIPID',0
	.word	55819
	.byte	4,3,35,192,2,13
	.byte	'MANID',0
	.word	58572
	.byte	4,3,35,196,2,13
	.byte	'reserved_148',0
	.word	4355
	.byte	8,3,35,200,2,13
	.byte	'SAFECON',0
	.word	60192
	.byte	4,3,35,208,2,13
	.byte	'reserved_154',0
	.word	13304
	.byte	16,3,35,212,2,13
	.byte	'LBISTCTRL0',0
	.word	58239
	.byte	4,3,35,228,2,13
	.byte	'LBISTCTRL1',0
	.word	58307
	.byte	4,3,35,232,2,13
	.byte	'LBISTCTRL2',0
	.word	58375
	.byte	4,3,35,236,2,13
	.byte	'reserved_170',0
	.word	28371
	.byte	28,3,35,240,2,13
	.byte	'PDISC',0
	.word	58952
	.byte	4,3,35,140,3,13
	.byte	'reserved_190',0
	.word	4355
	.byte	8,3,35,144,3,13
	.byte	'EVRTRIM',0
	.word	57678
	.byte	4,3,35,152,3,13
	.byte	'EVRADCSTAT',0
	.word	56523
	.byte	4,3,35,156,3,13
	.byte	'EVRUVMON',0
	.word	57743
	.byte	4,3,35,160,3,13
	.byte	'EVROVMON',0
	.word	56794
	.byte	4,3,35,164,3,13
	.byte	'EVRMONCTRL',0
	.word	56658
	.byte	4,3,35,168,3,13
	.byte	'reserved_1AC',0
	.word	2536
	.byte	4,3,35,172,3,13
	.byte	'EVRSDCTRL1',0
	.word	57341
	.byte	4,3,35,176,3,13
	.byte	'EVRSDCTRL2',0
	.word	57409
	.byte	4,3,35,180,3,13
	.byte	'EVRSDCTRL3',0
	.word	57477
	.byte	4,3,35,184,3,13
	.byte	'EVRSDCTRL4',0
	.word	57545
	.byte	4,3,35,188,3,13
	.byte	'EVRSDCOEFF1',0
	.word	56927
	.byte	4,3,35,192,3,13
	.byte	'EVRSDCOEFF2',0
	.word	56996
	.byte	4,3,35,196,3,13
	.byte	'EVRSDCOEFF3',0
	.word	57065
	.byte	4,3,35,200,3,13
	.byte	'EVRSDCOEFF4',0
	.word	57134
	.byte	4,3,35,204,3,13
	.byte	'EVRSDCOEFF5',0
	.word	57203
	.byte	4,3,35,208,3,13
	.byte	'EVRSDCOEFF6',0
	.word	57272
	.byte	4,3,35,212,3,13
	.byte	'EVROSCCTRL',0
	.word	56726
	.byte	4,3,35,216,3,13
	.byte	'reserved_1DC',0
	.word	2536
	.byte	4,3,35,220,3,13
	.byte	'OVCENABLE',0
	.word	58885
	.byte	4,3,35,224,3,13
	.byte	'OVCCON',0
	.word	58821
	.byte	4,3,35,228,3,13
	.byte	'reserved_1E8',0
	.word	31564
	.byte	40,3,35,232,3,13
	.byte	'EICR',0
	.word	61159
	.byte	16,3,35,144,4,13
	.byte	'EIFR',0
	.word	56138
	.byte	4,3,35,160,4,13
	.byte	'FMR',0
	.word	57934
	.byte	4,3,35,164,4,13
	.byte	'PDRR',0
	.word	59076
	.byte	4,3,35,168,4,13
	.byte	'IGCR',0
	.word	61168
	.byte	16,3,35,172,4,13
	.byte	'reserved_23C',0
	.word	2536
	.byte	4,3,35,188,4,13
	.byte	'DTSLIM',0
	.word	55947
	.byte	4,3,35,192,4,13
	.byte	'reserved_244',0
	.word	61177
	.byte	180,3,3,35,196,4,13
	.byte	'ACCEN1',0
	.word	55170
	.byte	4,3,35,248,7,13
	.byte	'ACCEN0',0
	.word	55106
	.byte	4,3,35,252,7,0,14
	.word	61188
	.byte	27
	.byte	'Ifx_SCU',0,4,181,16,3
	.word	63178
	.byte	10
	.byte	'_Ifx_STM_ACCEN0_Bits',0,18,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_STM_ACCEN0_Bits',0,18,79,3
	.word	63200
	.byte	10
	.byte	'_Ifx_STM_ACCEN1_Bits',0,18,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1_Bits',0,18,85,3
	.word	63757
	.byte	10
	.byte	'_Ifx_STM_CAP_Bits',0,18,88,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAP_Bits',0,18,91,3
	.word	63834
	.byte	10
	.byte	'_Ifx_STM_CAPSV_Bits',0,18,94,16,4,11
	.byte	'STMCAP63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV_Bits',0,18,97,3
	.word	63906
	.byte	10
	.byte	'_Ifx_STM_CLC_Bits',0,18,100,16,4,11
	.byte	'DISR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_CLC_Bits',0,18,107,3
	.word	63982
	.byte	10
	.byte	'_Ifx_STM_CMCON_Bits',0,18,110,16,4,11
	.byte	'MSIZE0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'MSTART0',0,1
	.word	669
	.byte	5,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	669
	.byte	3,0,2,35,1,11
	.byte	'MSIZE1',0,1
	.word	669
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	3,0,2,35,2,11
	.byte	'MSTART1',0,1
	.word	669
	.byte	5,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	3,0,2,35,3,0,27
	.byte	'Ifx_STM_CMCON_Bits',0,18,120,3
	.word	64123
	.byte	10
	.byte	'_Ifx_STM_CMP_Bits',0,18,123,16,4,11
	.byte	'CMPVAL',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_CMP_Bits',0,18,126,3
	.word	64341
	.byte	10
	.byte	'_Ifx_STM_ICR_Bits',0,18,129,1,16,4,11
	.byte	'CMP0EN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CMP0IR',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CMP0OS',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'CMP1EN',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CMP1IR',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'CMP1OS',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,4
	.word	494
	.byte	25,0,2,35,0,0,27
	.byte	'Ifx_STM_ICR_Bits',0,18,139,1,3
	.word	64408
	.byte	10
	.byte	'_Ifx_STM_ID_Bits',0,18,142,1,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_STM_ID_Bits',0,18,147,1,3
	.word	64611
	.byte	10
	.byte	'_Ifx_STM_ISCR_Bits',0,18,150,1,16,4,11
	.byte	'CMP0IRR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CMP0IRS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CMP1IRR',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'CMP1IRS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_STM_ISCR_Bits',0,18,157,1,3
	.word	64718
	.byte	10
	.byte	'_Ifx_STM_KRST0_Bits',0,18,160,1,16,4,11
	.byte	'RST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'RSTSTAT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	494
	.byte	30,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST0_Bits',0,18,165,1,3
	.word	64869
	.byte	10
	.byte	'_Ifx_STM_KRST1_Bits',0,18,168,1,16,4,11
	.byte	'RST',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRST1_Bits',0,18,172,1,3
	.word	64980
	.byte	10
	.byte	'_Ifx_STM_KRSTCLR_Bits',0,18,175,1,16,4,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR_Bits',0,18,179,1,3
	.word	65072
	.byte	10
	.byte	'_Ifx_STM_OCS_Bits',0,18,182,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	24,8,2,35,0,11
	.byte	'SUS',0,1
	.word	669
	.byte	4,4,2,35,3,11
	.byte	'SUS_P',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'SUSSTA',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	669
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_STM_OCS_Bits',0,18,189,1,3
	.word	65168
	.byte	10
	.byte	'_Ifx_STM_TIM0_Bits',0,18,192,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0_Bits',0,18,195,1,3
	.word	65314
	.byte	10
	.byte	'_Ifx_STM_TIM0SV_Bits',0,18,198,1,16,4,11
	.byte	'STM31_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV_Bits',0,18,201,1,3
	.word	65386
	.byte	10
	.byte	'_Ifx_STM_TIM1_Bits',0,18,204,1,16,4,11
	.byte	'STM35_4',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM1_Bits',0,18,207,1,3
	.word	65462
	.byte	10
	.byte	'_Ifx_STM_TIM2_Bits',0,18,210,1,16,4,11
	.byte	'STM39_8',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM2_Bits',0,18,213,1,3
	.word	65534
	.byte	10
	.byte	'_Ifx_STM_TIM3_Bits',0,18,216,1,16,4,11
	.byte	'STM43_12',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM3_Bits',0,18,219,1,3
	.word	65606
	.byte	10
	.byte	'_Ifx_STM_TIM4_Bits',0,18,222,1,16,4,11
	.byte	'STM47_16',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM4_Bits',0,18,225,1,3
	.word	65679
	.byte	10
	.byte	'_Ifx_STM_TIM5_Bits',0,18,228,1,16,4,11
	.byte	'STM51_20',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM5_Bits',0,18,231,1,3
	.word	65752
	.byte	10
	.byte	'_Ifx_STM_TIM6_Bits',0,18,234,1,16,4,11
	.byte	'STM63_32',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_STM_TIM6_Bits',0,18,237,1,3
	.word	65825
	.byte	12,18,245,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63200
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN0',0,18,250,1,3
	.word	65898
	.byte	12,18,253,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63757
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ACCEN1',0,18,130,2,3
	.word	65962
	.byte	12,18,133,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63834
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAP',0,18,138,2,3
	.word	66026
	.byte	12,18,141,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63906
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CAPSV',0,18,146,2,3
	.word	66087
	.byte	12,18,149,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	63982
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CLC',0,18,154,2,3
	.word	66150
	.byte	12,18,157,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64123
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMCON',0,18,162,2,3
	.word	66211
	.byte	12,18,165,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64341
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_CMP',0,18,170,2,3
	.word	66274
	.byte	12,18,173,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64408
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ICR',0,18,178,2,3
	.word	66335
	.byte	12,18,181,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64611
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ID',0,18,186,2,3
	.word	66396
	.byte	12,18,189,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64718
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_ISCR',0,18,194,2,3
	.word	66456
	.byte	12,18,197,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64869
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST0',0,18,202,2,3
	.word	66518
	.byte	12,18,205,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	64980
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRST1',0,18,210,2,3
	.word	66581
	.byte	12,18,213,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65072
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_KRSTCLR',0,18,218,2,3
	.word	66644
	.byte	12,18,221,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65168
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_OCS',0,18,226,2,3
	.word	66709
	.byte	12,18,229,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65314
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0',0,18,234,2,3
	.word	66770
	.byte	12,18,237,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65386
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM0SV',0,18,242,2,3
	.word	66832
	.byte	12,18,245,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65462
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM1',0,18,250,2,3
	.word	66896
	.byte	12,18,253,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65534
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM2',0,18,130,3,3
	.word	66958
	.byte	12,18,133,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65606
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM3',0,18,138,3,3
	.word	67020
	.byte	12,18,141,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65679
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM4',0,18,146,3,3
	.word	67082
	.byte	12,18,149,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65752
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM5',0,18,154,3,3
	.word	67144
	.byte	12,18,157,3,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	65825
	.byte	4,2,35,0,0,27
	.byte	'Ifx_STM_TIM6',0,18,162,3,3
	.word	67206
	.byte	17,19,236,10,9,1,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fBack',0,0,18
	.byte	'IfxScu_CCUCON0_CLKSEL_fPll',0,1,0,27
	.byte	'IfxScu_CCUCON0_CLKSEL',0,19,240,10,3
	.word	67268
	.byte	17,19,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,27
	.byte	'IfxScu_WDTCON1_IR',0,19,255,10,3
	.word	67365
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN0_Bits',0,20,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ACCEN0_Bits',0,20,79,3
	.word	67487
	.byte	10
	.byte	'_Ifx_FLASH_ACCEN1_Bits',0,20,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1_Bits',0,20,85,3
	.word	68048
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_CFG_Bits',0,20,88,16,4,11
	.byte	'SEL',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG_Bits',0,20,95,3
	.word	68129
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_STAT_Bits',0,20,98,16,4,11
	.byte	'VLD0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'VLD1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'VLD2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'VLD3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'VLD4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'VLD5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'VLD6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'VLD7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'VLD8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'VLD9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT_Bits',0,20,111,3
	.word	68282
	.byte	10
	.byte	'_Ifx_FLASH_CBAB_TOP_Bits',0,20,114,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_CBAB_TOP_Bits',0,20,121,3
	.word	68530
	.byte	10
	.byte	'_Ifx_FLASH_COMM0_Bits',0,20,124,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	494
	.byte	24,0,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0_Bits',0,20,128,1,3
	.word	68676
	.byte	10
	.byte	'_Ifx_FLASH_COMM1_Bits',0,20,131,1,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM1_Bits',0,20,136,1,3
	.word	68774
	.byte	10
	.byte	'_Ifx_FLASH_COMM2_Bits',0,20,139,1,16,4,11
	.byte	'STATUS',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'DATA',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_COMM2_Bits',0,20,144,1,3
	.word	68890
	.byte	10
	.byte	'_Ifx_FLASH_ECCRD_Bits',0,20,147,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRD_Bits',0,20,153,1,3
	.word	69006
	.byte	10
	.byte	'_Ifx_FLASH_ECCRP_Bits',0,20,156,1,16,4,11
	.byte	'RCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'EDCERRINJ',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'ECCORDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCRP_Bits',0,20,162,1,3
	.word	69146
	.byte	10
	.byte	'_Ifx_FLASH_ECCW_Bits',0,20,165,1,16,4,11
	.byte	'WCODE',0,4
	.word	494
	.byte	22,10,2,35,0,11
	.byte	'reserved_22',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'DECENCDIS',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'PECENCDIS',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_ECCW_Bits',0,20,171,1,3
	.word	69286
	.byte	10
	.byte	'_Ifx_FLASH_FCON_Bits',0,20,174,1,16,4,11
	.byte	'WSPFLASH',0,1
	.word	669
	.byte	4,4,2,35,0,11
	.byte	'WSECPF',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'WSDFLASH',0,2
	.word	686
	.byte	6,4,2,35,0,11
	.byte	'WSECDF',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'IDLE',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'ESLDIS',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'SLEEP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'NSAFECC',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'STALL',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'RES21',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'RES23',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'VOPERM',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'SQERM',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'PROERM',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	3,2,2,35,3,11
	.byte	'PR5V',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EOBM',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FCON_Bits',0,20,193,1,3
	.word	69425
	.byte	10
	.byte	'_Ifx_FLASH_FPRO_Bits',0,20,196,1,16,4,11
	.byte	'PROINP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'PRODISP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PROIND',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'PRODISD',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'PROINHSMCOTP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'PROINOTP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'RES7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'PROINDBG',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PRODISDBG',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'PROINHSM',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	669
	.byte	5,0,2,35,1,11
	.byte	'DCFP',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'DDFP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DDFPX',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'DDFD',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'ENPE',0,1
	.word	669
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FPRO_Bits',0,20,218,1,3
	.word	69787
	.byte	10
	.byte	'_Ifx_FLASH_FSR_Bits',0,20,221,1,16,4,11
	.byte	'FABUSY',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'D0BUSY',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RES1',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'P0BUSY',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'P1BUSY',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'RES5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'RES6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'PROG',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'ERASE',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'PFPAGE',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'DFPAGE',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'OPER',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'SQER',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'PROER',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'PFSBER',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'PFDBER',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'PFMBER',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'RES17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'DFSBER',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'DFDBER',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'DFTBER',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'DFMBER',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'SRIADDERR',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	2,7,2,35,2,11
	.byte	'PVER',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EVER',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'SPND',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'SLM',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'ORIER',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_FSR_Bits',0,20,254,1,3
	.word	70228
	.byte	10
	.byte	'_Ifx_FLASH_ID_Bits',0,20,129,2,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_ID_Bits',0,20,134,2,3
	.word	70834
	.byte	10
	.byte	'_Ifx_FLASH_MARD_Bits',0,20,137,2,16,4,11
	.byte	'HMARGIN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SELD0',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'SPND',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'SPNDERR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,2
	.word	686
	.byte	10,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARD_Bits',0,20,147,2,3
	.word	70945
	.byte	10
	.byte	'_Ifx_FLASH_MARP_Bits',0,20,150,2,16,4,11
	.byte	'SELP0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SELP1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RES2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'RES3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	686
	.byte	11,1,2,35,0,11
	.byte	'TRAPDIS',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_MARP_Bits',0,20,159,2,3
	.word	71159
	.byte	10
	.byte	'_Ifx_FLASH_PROCOND_Bits',0,20,162,2,16,4,11
	.byte	'L',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'NSAFECC',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'RAMIN',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'RAMINSEL',0,1
	.word	669
	.byte	4,0,2,35,0,11
	.byte	'OSCCFG',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'MODE',0,1
	.word	669
	.byte	2,5,2,35,1,11
	.byte	'APREN',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'CAP0EN',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'CAP1EN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'CAP2EN',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'CAP3EN',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'ESR0CNT',0,2
	.word	686
	.byte	12,4,2,35,2,11
	.byte	'RES29',0,1
	.word	669
	.byte	2,2,2,35,3,11
	.byte	'RES30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCOND_Bits',0,20,179,2,3
	.word	71346
	.byte	10
	.byte	'_Ifx_FLASH_PROCONDBG_Bits',0,20,182,2,16,4,11
	.byte	'OCDSDIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EDM',0,1
	.word	669
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG_Bits',0,20,188,2,3
	.word	71670
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSM_Bits',0,20,191,2,16,4,11
	.byte	'HSMDBGDIS',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DBGIFLCK',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'TSTIFLCK',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'HSMTSTDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'RES15',0,2
	.word	686
	.byte	12,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSM_Bits',0,20,199,2,3
	.word	71813
	.byte	10
	.byte	'_Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,202,2,16,4,11
	.byte	'HSMBOOTEN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'SSWWAIT',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'HSMDX',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'HSM6X',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'HSM16X',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'HSM17X',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'HSMENPINS',0,2
	.word	686
	.byte	2,7,2,35,0,11
	.byte	'HSMENRES',0,1
	.word	669
	.byte	2,5,2,35,1,11
	.byte	'DESTDBG',0,1
	.word	669
	.byte	2,3,2,35,1,11
	.byte	'BLKFLAN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	669
	.byte	2,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	686
	.byte	14,0,2,35,2,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP_Bits',0,20,219,2,3
	.word	72002
	.byte	10
	.byte	'_Ifx_FLASH_PROCONOTP_Bits',0,20,222,2,16,4,11
	.byte	'S0ROM',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1ROM',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2ROM',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3ROM',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4ROM',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5ROM',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6ROM',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7ROM',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8ROM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9ROM',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10ROM',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11ROM',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12ROM',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13ROM',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14ROM',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15ROM',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16ROM',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17ROM',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18ROM',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19ROM',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20ROM',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21ROM',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22ROM',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23ROM',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24ROM',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25ROM',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26ROM',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	2,3,2,35,3,11
	.byte	'BML',0,1
	.word	669
	.byte	2,1,2,35,3,11
	.byte	'TP',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONOTP_Bits',0,20,254,2,3
	.word	72365
	.byte	10
	.byte	'_Ifx_FLASH_PROCONP_Bits',0,20,129,3,16,4,11
	.byte	'S0L',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1L',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2L',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3L',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4L',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5L',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6L',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7L',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8L',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9L',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10L',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11L',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12L',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13L',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14L',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15L',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16L',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17L',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18L',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19L',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20L',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21L',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22L',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23L',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24L',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25L',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26L',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'RPRO',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONP_Bits',0,20,160,3,3
	.word	72960
	.byte	10
	.byte	'_Ifx_FLASH_PROCONWOP_Bits',0,20,163,3,16,4,11
	.byte	'S0WOP',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'S1WOP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'S2WOP',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'S3WOP',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'S4WOP',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'S5WOP',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'S6WOP',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'S7WOP',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'S8WOP',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'S9WOP',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'S10WOP',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'S11WOP',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'S12WOP',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'S13WOP',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'S14WOP',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'S15WOP',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'S16WOP',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'S17WOP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'S18WOP',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'S19WOP',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'S20WOP',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'S21WOP',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'S22WOP',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'S23WOP',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'S24WOP',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'S25WOP',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'S26WOP',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	669
	.byte	4,1,2,35,3,11
	.byte	'DATM',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_PROCONWOP_Bits',0,20,194,3,3
	.word	73484
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG0_Bits',0,20,197,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0_Bits',0,20,201,3,3
	.word	74066
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG1_Bits',0,20,204,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1_Bits',0,20,208,3,3
	.word	74168
	.byte	10
	.byte	'_Ifx_FLASH_RDB_CFG2_Bits',0,20,211,3,16,4,11
	.byte	'TAG',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	494
	.byte	26,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2_Bits',0,20,215,3,3
	.word	74270
	.byte	10
	.byte	'_Ifx_FLASH_RRAD_Bits',0,20,218,3,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	3,5,2,35,0,11
	.byte	'ADD',0,4
	.word	494
	.byte	29,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD_Bits',0,20,222,3,3
	.word	74372
	.byte	10
	.byte	'_Ifx_FLASH_RRCT_Bits',0,20,225,3,16,4,11
	.byte	'STRT',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'STP',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'BUSY',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'DONE',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	669
	.byte	3,0,2,35,0,11
	.byte	'EOBM',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	669
	.byte	7,0,2,35,1,11
	.byte	'CNT',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_FLASH_RRCT_Bits',0,20,236,3,3
	.word	74466
	.byte	10
	.byte	'_Ifx_FLASH_RRD0_Bits',0,20,239,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0_Bits',0,20,242,3,3
	.word	74676
	.byte	10
	.byte	'_Ifx_FLASH_RRD1_Bits',0,20,245,3,16,4,11
	.byte	'DATA',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1_Bits',0,20,248,3,3
	.word	74749
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_CFG_Bits',0,20,251,3,16,4,11
	.byte	'SEL',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	2,0,2,35,0,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'DIS',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,4
	.word	494
	.byte	22,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG_Bits',0,20,130,4,3
	.word	74822
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_STAT_Bits',0,20,133,4,16,4,11
	.byte	'VLD0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	494
	.byte	31,0,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT_Bits',0,20,137,4,3
	.word	74977
	.byte	10
	.byte	'_Ifx_FLASH_UBAB_TOP_Bits',0,20,140,4,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	5,3,2,35,0,11
	.byte	'ADDR',0,4
	.word	494
	.byte	19,8,2,35,0,11
	.byte	'ERR',0,1
	.word	669
	.byte	6,2,2,35,3,11
	.byte	'VLD',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'CLR',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_FLASH_UBAB_TOP_Bits',0,20,147,4,3
	.word	75082
	.byte	12,20,155,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	67487
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN0',0,20,160,4,3
	.word	75230
	.byte	12,20,163,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68048
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ACCEN1',0,20,168,4,3
	.word	75296
	.byte	12,20,171,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68129
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_CFG',0,20,176,4,3
	.word	75362
	.byte	12,20,179,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68282
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_STAT',0,20,184,4,3
	.word	75430
	.byte	12,20,187,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68530
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_CBAB_TOP',0,20,192,4,3
	.word	75499
	.byte	12,20,195,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68676
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM0',0,20,200,4,3
	.word	75567
	.byte	12,20,203,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68774
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM1',0,20,208,4,3
	.word	75632
	.byte	12,20,211,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	68890
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_COMM2',0,20,216,4,3
	.word	75697
	.byte	12,20,219,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69006
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRD',0,20,224,4,3
	.word	75762
	.byte	12,20,227,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69146
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCRP',0,20,232,4,3
	.word	75827
	.byte	12,20,235,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69286
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ECCW',0,20,240,4,3
	.word	75892
	.byte	12,20,243,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69425
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FCON',0,20,248,4,3
	.word	75956
	.byte	12,20,251,4,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	69787
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FPRO',0,20,128,5,3
	.word	76020
	.byte	12,20,131,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70228
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_FSR',0,20,136,5,3
	.word	76084
	.byte	12,20,139,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70834
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_ID',0,20,144,5,3
	.word	76147
	.byte	12,20,147,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	70945
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARD',0,20,152,5,3
	.word	76209
	.byte	12,20,155,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71159
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_MARP',0,20,160,5,3
	.word	76273
	.byte	12,20,163,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71346
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCOND',0,20,168,5,3
	.word	76337
	.byte	12,20,171,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71670
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONDBG',0,20,176,5,3
	.word	76404
	.byte	12,20,179,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	71813
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSM',0,20,184,5,3
	.word	76473
	.byte	12,20,187,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72002
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONHSMCOTP',0,20,192,5,3
	.word	76542
	.byte	12,20,195,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72365
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONOTP',0,20,200,5,3
	.word	76615
	.byte	12,20,203,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	72960
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONP',0,20,208,5,3
	.word	76684
	.byte	12,20,211,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	73484
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_PROCONWOP',0,20,216,5,3
	.word	76751
	.byte	12,20,219,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74066
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG0',0,20,224,5,3
	.word	76820
	.byte	12,20,227,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74168
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG1',0,20,232,5,3
	.word	76888
	.byte	12,20,235,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74270
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RDB_CFG2',0,20,240,5,3
	.word	76956
	.byte	12,20,243,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74372
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRAD',0,20,248,5,3
	.word	77024
	.byte	12,20,251,5,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74466
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRCT',0,20,128,6,3
	.word	77088
	.byte	12,20,131,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74676
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD0',0,20,136,6,3
	.word	77152
	.byte	12,20,139,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74749
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_RRD1',0,20,144,6,3
	.word	77216
	.byte	12,20,147,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74822
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_CFG',0,20,152,6,3
	.word	77280
	.byte	12,20,155,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	74977
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_STAT',0,20,160,6,3
	.word	77348
	.byte	12,20,163,6,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	75082
	.byte	4,2,35,0,0,27
	.byte	'Ifx_FLASH_UBAB_TOP',0,20,168,6,3
	.word	77417
	.byte	10
	.byte	'_Ifx_FLASH_CBAB',0,20,179,6,25,12,13
	.byte	'CFG',0
	.word	75362
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	75430
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	75499
	.byte	4,2,35,8,0,14
	.word	77485
	.byte	27
	.byte	'Ifx_FLASH_CBAB',0,20,184,6,3
	.word	77548
	.byte	10
	.byte	'_Ifx_FLASH_RDB',0,20,187,6,25,12,13
	.byte	'CFG0',0
	.word	76820
	.byte	4,2,35,0,13
	.byte	'CFG1',0
	.word	76888
	.byte	4,2,35,4,13
	.byte	'CFG2',0
	.word	76956
	.byte	4,2,35,8,0,14
	.word	77577
	.byte	27
	.byte	'Ifx_FLASH_RDB',0,20,192,6,3
	.word	77641
	.byte	10
	.byte	'_Ifx_FLASH_UBAB',0,20,195,6,25,12,13
	.byte	'CFG',0
	.word	77280
	.byte	4,2,35,0,13
	.byte	'STAT',0
	.word	77348
	.byte	4,2,35,4,13
	.byte	'TOP',0
	.word	77417
	.byte	4,2,35,8,0,14
	.word	77669
	.byte	27
	.byte	'Ifx_FLASH_UBAB',0,20,200,6,3
	.word	77732
	.byte	27
	.byte	'Ifx_P_ACCEN0_Bits',0,6,79,3
	.word	8108
	.byte	27
	.byte	'Ifx_P_ACCEN1_Bits',0,6,85,3
	.word	8021
	.byte	27
	.byte	'Ifx_P_ESR_Bits',0,6,107,3
	.word	4364
	.byte	27
	.byte	'Ifx_P_ID_Bits',0,6,115,3
	.word	2417
	.byte	27
	.byte	'Ifx_P_IN_Bits',0,6,137,1,3
	.word	3412
	.byte	27
	.byte	'Ifx_P_IOCR0_Bits',0,6,150,1,3
	.word	2545
	.byte	27
	.byte	'Ifx_P_IOCR12_Bits',0,6,163,1,3
	.word	3192
	.byte	27
	.byte	'Ifx_P_IOCR4_Bits',0,6,176,1,3
	.word	2760
	.byte	27
	.byte	'Ifx_P_IOCR8_Bits',0,6,189,1,3
	.word	2975
	.byte	27
	.byte	'Ifx_P_LPCR0_Bits',0,6,197,1,3
	.word	7380
	.byte	27
	.byte	'Ifx_P_LPCR1_Bits',0,6,205,1,3
	.word	7504
	.byte	27
	.byte	'Ifx_P_LPCR1_P21_Bits',0,6,215,1,3
	.word	7588
	.byte	27
	.byte	'Ifx_P_LPCR2_Bits',0,6,229,1,3
	.word	7768
	.byte	27
	.byte	'Ifx_P_OMCR0_Bits',0,6,240,1,3
	.word	6019
	.byte	27
	.byte	'Ifx_P_OMCR12_Bits',0,6,250,1,3
	.word	6543
	.byte	27
	.byte	'Ifx_P_OMCR4_Bits',0,6,133,2,3
	.word	6193
	.byte	27
	.byte	'Ifx_P_OMCR8_Bits',0,6,144,2,3
	.word	6367
	.byte	27
	.byte	'Ifx_P_OMCR_Bits',0,6,166,2,3
	.word	7032
	.byte	27
	.byte	'Ifx_P_OMR_Bits',0,6,203,2,3
	.word	1846
	.byte	27
	.byte	'Ifx_P_OMSR0_Bits',0,6,213,2,3
	.word	5356
	.byte	27
	.byte	'Ifx_P_OMSR12_Bits',0,6,224,2,3
	.word	5844
	.byte	27
	.byte	'Ifx_P_OMSR4_Bits',0,6,235,2,3
	.word	5503
	.byte	27
	.byte	'Ifx_P_OMSR8_Bits',0,6,246,2,3
	.word	5672
	.byte	27
	.byte	'Ifx_P_OMSR_Bits',0,6,140,3,3
	.word	6699
	.byte	27
	.byte	'Ifx_P_OUT_Bits',0,6,162,3,3
	.word	1530
	.byte	27
	.byte	'Ifx_P_PCSR_Bits',0,6,180,3,3
	.word	5070
	.byte	27
	.byte	'Ifx_P_PDISC_Bits',0,6,202,3,3
	.word	4704
	.byte	27
	.byte	'Ifx_P_PDR0_Bits',0,6,223,3,3
	.word	3735
	.byte	27
	.byte	'Ifx_P_PDR1_Bits',0,6,244,3,3
	.word	4039
	.byte	27
	.byte	'Ifx_P_ACCEN0',0,6,129,4,3
	.word	8635
	.byte	27
	.byte	'Ifx_P_ACCEN1',0,6,137,4,3
	.word	8068
	.byte	27
	.byte	'Ifx_P_ESR',0,6,145,4,3
	.word	4655
	.byte	27
	.byte	'Ifx_P_ID',0,6,153,4,3
	.word	2496
	.byte	27
	.byte	'Ifx_P_IN',0,6,161,4,3
	.word	3686
	.byte	27
	.byte	'Ifx_P_IOCR0',0,6,169,4,3
	.word	2720
	.byte	27
	.byte	'Ifx_P_IOCR12',0,6,177,4,3
	.word	3372
	.byte	27
	.byte	'Ifx_P_IOCR4',0,6,185,4,3
	.word	2935
	.byte	27
	.byte	'Ifx_P_IOCR8',0,6,193,4,3
	.word	3152
	.byte	27
	.byte	'Ifx_P_LPCR0',0,6,201,4,3
	.word	7464
	.byte	27
	.byte	'Ifx_P_LPCR1',0,6,210,4,3
	.word	7713
	.byte	27
	.byte	'Ifx_P_LPCR2',0,6,218,4,3
	.word	7972
	.byte	27
	.byte	'Ifx_P_OMCR',0,6,226,4,3
	.word	7340
	.byte	27
	.byte	'Ifx_P_OMCR0',0,6,234,4,3
	.word	6153
	.byte	27
	.byte	'Ifx_P_OMCR12',0,6,242,4,3
	.word	6659
	.byte	27
	.byte	'Ifx_P_OMCR4',0,6,250,4,3
	.word	6327
	.byte	27
	.byte	'Ifx_P_OMCR8',0,6,130,5,3
	.word	6503
	.byte	27
	.byte	'Ifx_P_OMR',0,6,138,5,3
	.word	2377
	.byte	27
	.byte	'Ifx_P_OMSR',0,6,146,5,3
	.word	6992
	.byte	27
	.byte	'Ifx_P_OMSR0',0,6,154,5,3
	.word	5463
	.byte	27
	.byte	'Ifx_P_OMSR12',0,6,162,5,3
	.word	5979
	.byte	27
	.byte	'Ifx_P_OMSR4',0,6,170,5,3
	.word	5632
	.byte	27
	.byte	'Ifx_P_OMSR8',0,6,178,5,3
	.word	5804
	.byte	27
	.byte	'Ifx_P_OUT',0,6,186,5,3
	.word	1806
	.byte	27
	.byte	'Ifx_P_PCSR',0,6,194,5,3
	.word	5316
	.byte	27
	.byte	'Ifx_P_PDISC',0,6,202,5,3
	.word	5030
	.byte	27
	.byte	'Ifx_P_PDR0',0,6,210,5,3
	.word	3999
	.byte	27
	.byte	'Ifx_P_PDR1',0,6,218,5,3
	.word	4315
	.byte	14
	.word	8675
	.byte	27
	.byte	'Ifx_P',0,6,139,6,3
	.word	79079
	.byte	17,5,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,27
	.byte	'IfxPort_InputMode',0,5,89,3
	.word	79099
	.byte	17,5,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,27
	.byte	'IfxPort_OutputIdx',0,5,130,1,3
	.word	79250
	.byte	17,5,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,27
	.byte	'IfxPort_OutputMode',0,5,138,1,3
	.word	79494
	.byte	17,5,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,27
	.byte	'IfxPort_PadDriver',0,5,158,1,3
	.word	79592
	.byte	27
	.byte	'IfxPort_State',0,5,178,1,3
	.word	9288
	.byte	25,5,190,1,9,8,13
	.byte	'port',0
	.word	9283
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	669
	.byte	1,2,35,4,0,27
	.byte	'IfxPort_Pin',0,5,194,1,3
	.word	80057
	.byte	27
	.byte	'IfxScuCcu_PllStepsFunctionHook',0,7,148,1,16
	.word	233
	.byte	25,7,212,5,9,8,13
	.byte	'value',0
	.word	9669
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9669
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_CcuconRegConfig',0,7,216,5,3
	.word	80157
	.byte	25,7,221,5,9,8,13
	.byte	'pDivider',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'nDivider',0
	.word	669
	.byte	1,2,35,1,13
	.byte	'k2Initial',0
	.word	669
	.byte	1,2,35,2,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_InitialStepConfig',0,7,227,5,3
	.word	80228
	.byte	25,7,231,5,9,12,13
	.byte	'k2Step',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'waitTime',0
	.word	290
	.byte	4,2,35,2,13
	.byte	'hookFunction',0
	.word	80117
	.byte	4,2,35,8,0,27
	.byte	'IfxScuCcu_PllStepsConfig',0,7,236,5,3
	.word	80345
	.byte	3
	.word	230
	.byte	25,7,244,5,9,48,13
	.byte	'ccucon0',0
	.word	80157
	.byte	8,2,35,0,13
	.byte	'ccucon1',0
	.word	80157
	.byte	8,2,35,8,13
	.byte	'ccucon2',0
	.word	80157
	.byte	8,2,35,16,13
	.byte	'ccucon5',0
	.word	80157
	.byte	8,2,35,24,13
	.byte	'ccucon6',0
	.word	80157
	.byte	8,2,35,32,13
	.byte	'ccucon7',0
	.word	80157
	.byte	8,2,35,40,0,27
	.byte	'IfxScuCcu_ClockDistributionConfig',0,7,252,5,3
	.word	80447
	.byte	25,7,128,6,9,8,13
	.byte	'value',0
	.word	9669
	.byte	4,2,35,0,13
	.byte	'mask',0
	.word	9669
	.byte	4,2,35,4,0,27
	.byte	'IfxScuCcu_FlashWaitstateConfig',0,7,132,6,3
	.word	80599
	.byte	3
	.word	80345
	.byte	25,7,137,6,9,16,13
	.byte	'numOfPllDividerSteps',0
	.word	669
	.byte	1,2,35,0,13
	.byte	'pllDividerStep',0
	.word	80675
	.byte	4,2,35,4,13
	.byte	'pllInitialStep',0
	.word	80228
	.byte	8,2,35,8,0,27
	.byte	'IfxScuCcu_SysPllConfig',0,7,142,6,3
	.word	80680
	.byte	17,8,144,1,9,1,18
	.byte	'IfxCpu_CounterMode_normal',0,0,18
	.byte	'IfxCpu_CounterMode_task',0,1,0,27
	.byte	'IfxCpu_CounterMode',0,8,148,1,3
	.word	80797
	.byte	25,8,160,1,9,6,13
	.byte	'counter',0
	.word	9669
	.byte	4,2,35,0,13
	.byte	'overlfow',0
	.word	669
	.byte	1,2,35,4,0,27
	.byte	'IfxCpu_Counter',0,8,164,1,3
	.word	80886
	.byte	25,8,172,1,9,32,13
	.byte	'instruction',0
	.word	80886
	.byte	6,2,35,0,13
	.byte	'clock',0
	.word	80886
	.byte	6,2,35,6,13
	.byte	'counter1',0
	.word	80886
	.byte	6,2,35,12,13
	.byte	'counter2',0
	.word	80886
	.byte	6,2,35,18,13
	.byte	'counter3',0
	.word	80886
	.byte	6,2,35,24,0,27
	.byte	'IfxCpu_Perf',0,8,179,1,3
	.word	80952
	.byte	10
	.byte	'_Ifx_MTU_ACCEN0_Bits',0,21,45,16,4,11
	.byte	'EN0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_MTU_ACCEN0_Bits',0,21,79,3
	.word	81070
	.byte	10
	.byte	'_Ifx_MTU_ACCEN1_Bits',0,21,82,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_MTU_ACCEN1_Bits',0,21,85,3
	.word	81627
	.byte	10
	.byte	'_Ifx_MTU_CLC_Bits',0,21,88,16,4,11
	.byte	'DISR',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'DISS',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'Resvd',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'EDIS',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	494
	.byte	28,0,2,35,0,0,27
	.byte	'Ifx_MTU_CLC_Bits',0,21,95,3
	.word	81704
	.byte	10
	.byte	'_Ifx_MTU_ID_Bits',0,21,98,16,4,11
	.byte	'MODREV',0,1
	.word	669
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	669
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	686
	.byte	16,0,2,35,2,0,27
	.byte	'Ifx_MTU_ID_Bits',0,21,103,3
	.word	81840
	.byte	10
	.byte	'_Ifx_MTU_MEMMAP_Bits',0,21,106,16,4,11
	.byte	'reserved_0',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'CPU2DxMAP',0,1
	.word	669
	.byte	2,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'CPU2PxMAP',0,1
	.word	669
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'CPU1DCMAP',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTMAP',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'CPU1PCMAP',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTMAP',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	3,1,2,35,1,11
	.byte	'CPU0PCMAP',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTMAP',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'CPU0DxMAP',0,1
	.word	669
	.byte	2,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	686
	.byte	12,0,2,35,2,0,27
	.byte	'Ifx_MTU_MEMMAP_Bits',0,21,124,3
	.word	81945
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT0_Bits',0,21,127,16,4,11
	.byte	'CPU2DSAIU',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'CPU2DTAIU',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'CPU2PSAIU',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'CPU2PTAIU',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'CPU1DSAIU',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTAIU',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'CPU1PSAIU',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTAIU',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	669
	.byte	2,2,2,35,1,11
	.byte	'CPU0DSAIU',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'CPU0PSAIU',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTAIU',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'CPU0DxAIU',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'CPU1DS2AIU',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'CPU2DS2AIU',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'HSMCAIU',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'HSMTAIU',0,1
	.word	669
	.byte	1,7,2,35,3,11
	.byte	'HSMRAIU',0,1
	.word	669
	.byte	1,6,2,35,3,11
	.byte	'FSI0AIU',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'CPU0DS2AIU',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	669
	.byte	4,0,2,35,3,0,27
	.byte	'Ifx_MTU_MEMSTAT0_Bits',0,21,157,1,3
	.word	82346
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT1_Bits',0,21,160,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_MTU_MEMSTAT1_Bits',0,21,163,1,3
	.word	83004
	.byte	10
	.byte	'_Ifx_MTU_MEMSTAT2_Bits',0,21,166,1,16,4,11
	.byte	'reserved_0',0,4
	.word	494
	.byte	32,0,2,35,0,0,27
	.byte	'Ifx_MTU_MEMSTAT2_Bits',0,21,169,1,3
	.word	83087
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST0_Bits',0,21,172,1,16,4,11
	.byte	'CPU2XEN',0,1
	.word	669
	.byte	6,2,2,35,0,11
	.byte	'CPU1DSEN',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'Res',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'CPU1DTEN',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'CPU1PSEN',0,1
	.word	669
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	669
	.byte	1,5,2,35,1,11
	.byte	'CPU1PTEN',0,1
	.word	669
	.byte	1,4,2,35,1,11
	.byte	'LMUEN',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'MMCDSEN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'CPU0DSEN',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'CPU0PSEN',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'CPU0PTEN',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'CPU0DTEN',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'CPUXDS2EN',0,1
	.word	669
	.byte	2,2,2,35,2,11
	.byte	'ETHEN',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	686
	.byte	3,6,2,35,2,11
	.byte	'FSI0EN',0,1
	.word	669
	.byte	1,5,2,35,3,11
	.byte	'CPU0DS2EN',0,1
	.word	669
	.byte	1,4,2,35,3,11
	.byte	'GTMFEN',0,1
	.word	669
	.byte	1,3,2,35,3,11
	.byte	'GTMM0EN',0,1
	.word	669
	.byte	1,2,2,35,3,11
	.byte	'GTMM1EN',0,1
	.word	669
	.byte	1,1,2,35,3,11
	.byte	'GTM1AEN',0,1
	.word	669
	.byte	1,0,2,35,3,0,27
	.byte	'Ifx_MTU_MEMTEST0_Bits',0,21,198,1,3
	.word	83170
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST1_Bits',0,21,201,1,16,4,11
	.byte	'GTM1BEN',0,1
	.word	669
	.byte	1,7,2,35,0,11
	.byte	'GTM2EN',0,1
	.word	669
	.byte	1,6,2,35,0,11
	.byte	'PSI5EN',0,1
	.word	669
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	669
	.byte	1,4,2,35,0,11
	.byte	'MCAN0EN',0,1
	.word	669
	.byte	1,3,2,35,0,11
	.byte	'MCAN1EN',0,1
	.word	669
	.byte	1,2,2,35,0,11
	.byte	'ERAY0OEN',0,1
	.word	669
	.byte	1,1,2,35,0,11
	.byte	'ERAY0TEN',0,1
	.word	669
	.byte	1,0,2,35,0,11
	.byte	'ERAY0MEN',0,1
	.word	669
	.byte	1,7,2,35,1,11
	.byte	'ERAY1XEN',0,1
	.word	669
	.byte	3,4,2,35,1,11
	.byte	'STBY1EN',0,1
	.word	669
	.byte	1,3,2,35,1,11
	.byte	'MCDSEN',0,1
	.word	669
	.byte	1,2,2,35,1,11
	.byte	'EMEML0EN',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'EMEML1EN',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'EMEML2EN',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'EMEML3EN',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'EMEML4EN',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'EMEML5EN',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'EMEML6EN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'EMEML7EN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'EMEMLXEN',0,2
	.word	686
	.byte	8,2,2,35,2,11
	.byte	'EMEMUXEN',0,1
	.word	669
	.byte	2,0,2,35,3,0,27
	.byte	'Ifx_MTU_MEMTEST1_Bits',0,21,225,1,3
	.word	83705
	.byte	10
	.byte	'_Ifx_MTU_MEMTEST2_Bits',0,21,228,1,16,4,11
	.byte	'EMEMUxEN',0,2
	.word	686
	.byte	14,2,2,35,0,11
	.byte	'CIF0EN',0,1
	.word	669
	.byte	1,1,2,35,1,11
	.byte	'DAMEN',0,1
	.word	669
	.byte	1,0,2,35,1,11
	.byte	'CIF1EN',0,1
	.word	669
	.byte	1,7,2,35,2,11
	.byte	'CIF2EN',0,1
	.word	669
	.byte	1,6,2,35,2,11
	.byte	'STBY2EN',0,1
	.word	669
	.byte	1,5,2,35,2,11
	.byte	'DMAEN',0,1
	.word	669
	.byte	1,4,2,35,2,11
	.byte	'XTM0EN',0,1
	.word	669
	.byte	1,3,2,35,2,11
	.byte	'XTM1EN',0,1
	.word	669
	.byte	1,2,2,35,2,11
	.byte	'FFT0EN',0,1
	.word	669
	.byte	1,1,2,35,2,11
	.byte	'FFT1EN',0,1
	.word	669
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	669
	.byte	8,0,2,35,3,0,27
	.byte	'Ifx_MTU_MEMTEST2_Bits',0,21,242,1,3
	.word	84198
	.byte	12,21,250,1,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81070
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_ACCEN0',0,21,255,1,3
	.word	84481
	.byte	12,21,130,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81627
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_ACCEN1',0,21,135,2,3
	.word	84545
	.byte	12,21,138,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81704
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_CLC',0,21,143,2,3
	.word	84609
	.byte	12,21,146,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81840
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_ID',0,21,151,2,3
	.word	84670
	.byte	12,21,154,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	81945
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMMAP',0,21,159,2,3
	.word	84730
	.byte	12,21,162,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	82346
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMSTAT0',0,21,167,2,3
	.word	84794
	.byte	12,21,170,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83004
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMSTAT1',0,21,175,2,3
	.word	84860
	.byte	12,21,178,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83087
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMSTAT2',0,21,183,2,3
	.word	84926
	.byte	12,21,186,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83170
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMTEST0',0,21,191,2,3
	.word	84992
	.byte	12,21,194,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	83705
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMTEST1',0,21,199,2,3
	.word	85058
	.byte	12,21,202,2,9,4,13
	.byte	'U',0
	.word	494
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	510
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	84198
	.byte	4,2,35,0,0,27
	.byte	'Ifx_MTU_MEMTEST2',0,21,207,2,3
	.word	85124
	.byte	27
	.byte	'Ifx_MC_CONFIG0_Bits',0,12,50,3
	.word	11746
	.byte	27
	.byte	'Ifx_MC_CONFIG1_Bits',0,12,58,3
	.word	11874
	.byte	27
	.byte	'Ifx_MC_ECCD_Bits',0,12,75,3
	.word	12843
	.byte	27
	.byte	'Ifx_MC_ECCS_Bits',0,12,90,3
	.word	12606
	.byte	27
	.byte	'Ifx_MC_ETRR_Bits',0,12,97,3
	.word	13097
	.byte	27
	.byte	'Ifx_MC_MCONTROL_Bits',0,12,113,3
	.word	11998
	.byte	27
	.byte	'Ifx_MC_MSTATUS_Bits',0,12,123,3
	.word	12263
	.byte	27
	.byte	'Ifx_MC_RANGE_Bits',0,12,130,1,3
	.word	12416
	.byte	27
	.byte	'Ifx_MC_RDBFL_Bits',0,12,136,1,3
	.word	13212
	.byte	27
	.byte	'Ifx_MC_REVID_Bits',0,12,142,1,3
	.word	12522
	.byte	27
	.byte	'Ifx_MC_CONFIG0',0,12,155,1,3
	.word	11834
	.byte	27
	.byte	'Ifx_MC_CONFIG1',0,12,163,1,3
	.word	11958
	.byte	27
	.byte	'Ifx_MC_ECCD',0,12,171,1,3
	.word	13057
	.byte	27
	.byte	'Ifx_MC_ECCS',0,12,179,1,3
	.word	12803
	.byte	27
	.byte	'Ifx_MC_ETRR',0,12,187,1,3
	.word	13152
	.byte	27
	.byte	'Ifx_MC_MCONTROL',0,12,195,1,3
	.word	12223
	.byte	27
	.byte	'Ifx_MC_MSTATUS',0,12,203,1,3
	.word	12376
	.byte	27
	.byte	'Ifx_MC_RANGE',0,12,211,1,3
	.word	12473
	.byte	27
	.byte	'Ifx_MC_RDBFL',0,12,219,1,3
	.word	13255
	.byte	27
	.byte	'Ifx_MC_REVID',0,12,227,1,3
	.word	12566
	.byte	27
	.byte	'IfxMtu_MbistSel',0,11,128,1,3
	.word	9900
	.byte	27
	.byte	'IfxMtu_SramItem',0,11,144,1,3
	.word	13570
	.byte	15,160,8
	.word	13570
	.byte	16,87,0,26
	.word	85735
	.byte	29
	.byte	'IfxMtu_sramTable',0,11,150,1,34
	.word	85745
	.byte	1,1,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L129:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,53,0,73,19,0,0,15,1,1,11,15,73,19,0,0,16,33,0,47,15,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,11,1,0,0,20,46,0,3,8,54,15,39,12,63,12,60,12,0,0,21,46,1,49
	.byte	19,0,0,22,5,0,49,19,0,0,23,46,1,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0,24,46,0,3,8,58,15,59
	.byte	15,57,15,73,19,54,15,39,12,63,12,60,12,0,0,25,19,1,58,15,59,15,57,15,11,15,0,0,26,38,0,73,19,0,0,27,22
	.byte	0,3,8,58,15,59,15,57,15,73,19,0,0,28,21,0,54,15,0,0,29,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0
	.byte	0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L130:
	.word	.L521-.L520
.L520:
	.half	3
	.word	.L523-.L522
.L522:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxCpu_cfg.h',0
	.byte	0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxMtu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxMc_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'IfxCpu_regdef.h',0,1,0,0
	.byte	'IfxSrc_regdef.h',0,1,0,0
	.byte	'IfxStm_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxFlash_regdef.h',0,1,0,0
	.byte	'IfxMtu_regdef.h',0,1,0,0,0
.L523:
.L521:
	.sdecl	'.debug_info',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_info'
.L131:
	.word	454
	.half	3
	.word	.L132
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L134,.L133
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_clearSram',0,1,80,6,1,1,1
	.word	.L108,.L186,.L107
	.byte	4
	.byte	'mbistSel',0,1,80,39
	.word	.L187,.L188
	.byte	5
	.word	.L108,.L186
	.byte	6
	.byte	'isEndInitEnabled',0,1,82,12
	.word	.L189,.L190
	.byte	6
	.byte	'password',0,1,83,12
	.word	.L191,.L192
	.byte	7
	.word	.L193,.L194,.L2
	.byte	8
	.word	.L195,.L194,.L2
	.byte	0,7
	.word	.L196,.L4,.L6
	.byte	9
	.word	.L197,.L198
	.byte	10
	.word	.L199,.L4,.L6
	.byte	6
	.byte	'mc',0,2,168,4,13
	.word	.L200,.L201
	.byte	6
	.byte	'status',0,2,169,4,13
	.word	.L191,.L202
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_abbrev'
.L132:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_line'
.L133:
	.word	.L525-.L524
.L524:
	.half	3
	.word	.L527-.L526
.L526:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0,0
.L527:
	.byte	5,6,7,0,5,2
	.word	.L108
	.byte	3,207,0,1,5,29,9
	.half	.L348-.L108
	.byte	3,2,1,5,51,9
	.half	.L349-.L348
	.byte	3,3,1,5,14,9
	.half	.L347-.L349
	.byte	1,4,3,5,43,9
	.half	.L194-.L347
	.byte	3,164,3,1,5,5,9
	.half	.L528-.L194
	.byte	1,4,1,9
	.half	.L2-.L528
	.byte	3,223,124,1,5,38,7,9
	.half	.L529-.L2
	.byte	3,3,1,5,26,9
	.half	.L351-.L529
	.byte	3,1,1,5,27,9
	.half	.L3-.L351
	.byte	3,3,1,5,32,9
	.half	.L353-.L3
	.byte	3,3,1,5,45,9
	.half	.L355-.L353
	.byte	3,3,1,5,29,9
	.half	.L530-.L355
	.byte	1,5,45,9
	.half	.L531-.L530
	.byte	1,5,55,9
	.half	.L532-.L531
	.byte	1,5,68,9
	.half	.L533-.L532
	.byte	1,5,71,9
	.half	.L534-.L533
	.byte	1,5,41,9
	.half	.L357-.L534
	.byte	3,3,1,5,14,9
	.half	.L5-.L357
	.byte	3,2,1,4,2,5,54,9
	.half	.L4-.L5
	.byte	3,190,3,1,5,60,9
	.half	.L535-.L4
	.byte	1,5,29,9
	.half	.L536-.L535
	.byte	1,5,52,9
	.half	.L358-.L536
	.byte	1,5,16,9
	.half	.L537-.L358
	.byte	1,5,25,9
	.half	.L360-.L537
	.byte	3,3,1,5,29,9
	.half	.L361-.L360
	.byte	3,1,1,5,5,9
	.half	.L362-.L361
	.byte	1,4,1,5,13,9
	.half	.L6-.L362
	.byte	3,188,124,1,5,34,7,9
	.half	.L538-.L6
	.byte	3,6,1,5,30,9
	.half	.L359-.L538
	.byte	3,2,1,5,5,9
	.half	.L365-.L359
	.byte	3,2,1,5,36,7,9
	.half	.L539-.L365
	.byte	3,3,1,5,1,9
	.half	.L7-.L539
	.byte	3,2,1,7,9
	.half	.L135-.L7
	.byte	0,1,1
.L525:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_ranges'
.L134:
	.word	-1,.L108,0,.L135-.L108,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_info'
.L136:
	.word	470
	.half	3
	.word	.L137
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L139,.L138
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_clearSramContinue',0,1,122,6,1,1,1
	.word	.L110,.L203,.L109
	.byte	4
	.byte	'mbistSel',0,1,122,47
	.word	.L187,.L204
	.byte	5
	.word	.L110,.L203
	.byte	6
	.word	.L205,.L206,.L207
	.byte	7
	.word	.L208,.L209
	.byte	8
	.word	.L210,.L206,.L207
	.byte	9
	.byte	'mtuMemtest',0,2,237,3,22
	.word	.L211,.L212
	.byte	9
	.byte	'mask',0,2,238,3,22
	.word	.L213,.L214
	.byte	0,0,6
	.word	.L215,.L8,.L10
	.byte	7
	.word	.L216,.L217
	.byte	8
	.word	.L218,.L8,.L10
	.byte	9
	.byte	'mtuMemstat',0,2,146,4,22
	.word	.L211,.L219
	.byte	9
	.byte	'mask',0,2,147,4,22
	.word	.L213,.L220
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_abbrev'
.L137:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,29,1,49
	.byte	16,17,1,18,1,0,0,7,5,0,49,16,2,6,0,0,8,11,1,49,16,17,1,18,1,0,0,9,52,0,3,8,58,15,59,15,57,15,73,16,2,6
	.byte	0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_line'
.L138:
	.word	.L541-.L540
.L540:
	.half	3
	.word	.L543-.L542
.L542:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0,0
.L543:
	.byte	5,6,7,0,5,2
	.word	.L110
	.byte	3,249,0,1,5,38,9
	.half	.L367-.L110
	.byte	3,3,1,4,2,5,93,9
	.half	.L206-.L367
	.byte	3,240,2,1,5,81,9
	.half	.L544-.L206
	.byte	1,5,55,9
	.half	.L545-.L544
	.byte	1,5,81,9
	.half	.L368-.L545
	.byte	1,5,33,9
	.half	.L546-.L368
	.byte	1,5,35,9
	.half	.L370-.L546
	.byte	3,1,1,5,50,9
	.half	.L369-.L370
	.byte	1,5,37,9
	.half	.L547-.L369
	.byte	1,5,6,9
	.half	.L371-.L547
	.byte	3,1,1,5,20,9
	.half	.L548-.L371
	.byte	1,5,17,9
	.half	.L372-.L548
	.byte	1,4,1,5,46,9
	.half	.L207-.L372
	.byte	3,147,125,1,4,2,5,93,9
	.half	.L8-.L207
	.byte	3,144,3,1,5,81,9
	.half	.L549-.L8
	.byte	1,5,55,9
	.half	.L550-.L549
	.byte	1,5,81,9
	.half	.L373-.L550
	.byte	1,5,33,9
	.half	.L551-.L373
	.byte	1,5,35,9
	.half	.L375-.L551
	.byte	3,1,1,5,50,9
	.half	.L552-.L375
	.byte	1,5,37,9
	.half	.L374-.L552
	.byte	1,5,13,9
	.half	.L376-.L374
	.byte	3,1,1,5,25,9
	.half	.L553-.L376
	.byte	1,5,33,9
	.half	.L554-.L553
	.byte	1,5,5,9
	.half	.L555-.L554
	.byte	1,4,1,5,46,9
	.half	.L10-.L555
	.byte	3,238,124,1,5,1,7,9
	.half	.L556-.L10
	.byte	3,2,1,7,9
	.half	.L140-.L556
	.byte	0,1,1
.L541:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_ranges'
.L139:
	.word	-1,.L110,0,.L140-.L110,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_info'
.L141:
	.word	794
	.half	3
	.word	.L142
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L144,.L143
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_clearSramStart',0,1,135,1,6,1,1,1
	.word	.L112,.L221,.L111
	.byte	4
	.byte	'mbistSel',0,1,135,1,44
	.word	.L187,.L222
	.byte	5
	.word	.L112,.L221
	.byte	6
	.byte	'mc',0,1,137,1,13
	.word	.L200,.L223
	.byte	7
	.word	.L224,.L225,.L226
	.byte	8
	.word	.L227,.L228
	.byte	9
	.word	.L229,.L225,.L226
	.byte	6
	.byte	'mtuMemtest',0,2,245,3,22
	.word	.L211,.L230
	.byte	6
	.byte	'mask',0,2,246,3,22
	.word	.L213,.L231
	.byte	0,0,7
	.word	.L215,.L11,.L13
	.byte	8
	.word	.L216,.L232
	.byte	9
	.word	.L218,.L11,.L13
	.byte	6
	.byte	'mtuMemstat',0,2,146,4,22
	.word	.L211,.L233
	.byte	6
	.byte	'mask',0,2,147,4,22
	.word	.L213,.L234
	.byte	0,0,5
	.word	.L235,.L21
	.byte	6
	.byte	'item',0,1,148,1,32
	.word	.L236,.L237
	.byte	6
	.byte	'numBlocks',0,1,150,1,32
	.word	.L189,.L238
	.byte	5
	.word	.L239,.L21
	.byte	6
	.byte	'dataSize',0,1,153,1,32
	.word	.L191,.L240
	.byte	6
	.byte	'eccSize',0,1,154,1,32
	.word	.L189,.L241
	.byte	6
	.byte	'eccInvPos0',0,1,155,1,32
	.word	.L213,.L242
	.byte	6
	.byte	'eccInvPos1',0,1,156,1,32
	.word	.L213,.L243
	.byte	6
	.byte	'memSize',0,1,158,1,32
	.word	.L213,.L244
	.byte	6
	.byte	'bitPos',0,1,160,1,32
	.word	.L213,.L245
	.byte	6
	.byte	'wordIx',0,1,161,1,32
	.word	.L213,.L246
	.byte	6
	.byte	'data',0,1,162,1,32
	.word	.L191,.L247
	.byte	6
	.byte	'mem',0,1,164,1,32
	.word	.L213,.L248
	.byte	5
	.word	.L15,.L249
	.byte	6
	.byte	'i',0,1,168,1,20
	.word	.L213,.L250
	.byte	0,0,0,5
	.word	.L21,.L221
	.byte	6
	.byte	'mcontrolMask',0,1,196,1,12
	.word	.L191,.L251
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_abbrev'
.L142:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_line'
.L143:
	.word	.L558-.L557
.L557:
	.half	3
	.word	.L560-.L559
.L559:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0,0
.L560:
	.byte	5,54,7,0,5,2
	.word	.L112
	.byte	3,136,1,1,5,60,9
	.half	.L561-.L112
	.byte	1,5,29,9
	.half	.L562-.L561
	.byte	1,5,52,9
	.half	.L378-.L562
	.byte	1,5,16,9
	.half	.L563-.L378
	.byte	1,4,2,5,93,9
	.half	.L225-.L563
	.byte	3,236,2,1,5,81,9
	.half	.L379-.L225
	.byte	1,5,55,9
	.half	.L564-.L379
	.byte	1,5,81,9
	.half	.L380-.L564
	.byte	1,5,33,9
	.half	.L565-.L380
	.byte	1,5,35,9
	.half	.L382-.L565
	.byte	3,1,1,5,50,9
	.half	.L566-.L382
	.byte	1,5,37,9
	.half	.L381-.L566
	.byte	1,5,6,9
	.half	.L383-.L381
	.byte	3,1,1,5,17,9
	.half	.L567-.L383
	.byte	1,4,1,5,46,9
	.half	.L226-.L567
	.byte	3,151,125,1,4,2,5,93,9
	.half	.L11-.L226
	.byte	3,132,3,1,5,81,9
	.half	.L568-.L11
	.byte	1,5,55,9
	.half	.L569-.L568
	.byte	1,5,81,9
	.half	.L384-.L569
	.byte	1,5,33,9
	.half	.L570-.L384
	.byte	1,5,35,9
	.half	.L386-.L570
	.byte	3,1,1,5,50,9
	.half	.L571-.L386
	.byte	1,5,37,9
	.half	.L385-.L571
	.byte	1,5,13,9
	.half	.L388-.L385
	.byte	3,1,1,5,25,9
	.half	.L572-.L388
	.byte	1,5,33,9
	.half	.L573-.L572
	.byte	1,5,5,9
	.half	.L574-.L573
	.byte	1,4,1,5,46,9
	.half	.L13-.L574
	.byte	3,250,124,1,5,80,7,9
	.half	.L235-.L13
	.byte	3,6,1,5,64,9
	.half	.L575-.L235
	.byte	1,5,80,9
	.half	.L576-.L575
	.byte	1,5,48,9
	.half	.L390-.L576
	.byte	3,2,1,5,49,9
	.half	.L239-.L390
	.byte	3,3,1,9
	.half	.L389-.L239
	.byte	3,1,1,5,60,9
	.half	.L392-.L389
	.byte	3,1,1,5,54,9
	.half	.L577-.L392
	.byte	1,5,60,9
	.half	.L394-.L577
	.byte	3,1,1,5,54,9
	.half	.L578-.L394
	.byte	1,9
	.half	.L395-.L578
	.byte	3,2,1,5,43,9
	.half	.L391-.L395
	.byte	3,2,1,9
	.half	.L393-.L391
	.byte	3,1,1,9
	.half	.L377-.L393
	.byte	3,1,1,5,18,9
	.half	.L397-.L377
	.byte	3,4,1,5,38,9
	.half	.L398-.L397
	.byte	1,5,20,9
	.half	.L15-.L398
	.byte	3,4,1,5,36,9
	.half	.L399-.L15
	.byte	1,5,21,9
	.half	.L17-.L399
	.byte	3,2,1,5,45,7,9
	.half	.L579-.L17
	.byte	1,5,30,7,9
	.half	.L18-.L579
	.byte	3,2,1,5,32,9
	.half	.L580-.L18
	.byte	1,5,26,9
	.half	.L581-.L580
	.byte	1,5,17,9
	.half	.L19-.L581
	.byte	3,3,1,5,31,9
	.half	.L582-.L19
	.byte	3,2,1,5,17,9
	.half	.L583-.L582
	.byte	1,5,30,7,9
	.half	.L584-.L583
	.byte	3,2,1,5,43,9
	.half	.L585-.L584
	.byte	1,5,37,9
	.half	.L586-.L585
	.byte	1,5,43,9
	.half	.L587-.L586
	.byte	3,1,1,9
	.half	.L588-.L587
	.byte	3,1,1,5,38,9
	.half	.L20-.L588
	.byte	3,115,1,5,36,9
	.half	.L16-.L20
	.byte	1,5,40,7,9
	.half	.L249-.L16
	.byte	3,124,1,5,38,9
	.half	.L14-.L249
	.byte	1,5,9,7,9
	.half	.L589-.L14
	.byte	3,23,1,5,22,7,9
	.half	.L590-.L589
	.byte	3,2,1,5,33,9
	.half	.L591-.L590
	.byte	1,5,25,9
	.half	.L21-.L591
	.byte	3,5,1,5,35,9
	.half	.L396-.L21
	.byte	3,1,1,5,70,9
	.half	.L592-.L396
	.byte	1,5,20,9
	.half	.L593-.L592
	.byte	1,5,70,9
	.half	.L594-.L593
	.byte	3,1,1,5,20,9
	.half	.L595-.L594
	.byte	1,5,1,9
	.half	.L596-.L595
	.byte	3,1,1,7,9
	.half	.L145-.L596
	.byte	0,1,1
.L558:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_ranges'
.L144:
	.word	-1,.L112,0,.L145-.L112,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_info'
.L146:
	.word	452
	.half	3
	.word	.L147
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L149,.L148
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_readSramAddress',0,1,149,2,6,1,1,1
	.word	.L120,.L252,.L119
	.byte	4
	.byte	'mbistSel',0,1,149,2,45
	.word	.L187,.L253
	.byte	4
	.byte	'sramAddress',0,1,149,2,62
	.word	.L191,.L254
	.byte	5
	.word	.L120,.L252
	.byte	6
	.byte	'mc',0,1,151,2,13
	.word	.L200,.L255
	.byte	6
	.byte	'mcontrolMask',0,1,154,2,13
	.word	.L191,.L256
	.byte	7
	.word	.L196,.L40,.L42
	.byte	8
	.word	.L197,.L257
	.byte	9
	.word	.L199,.L40,.L42
	.byte	6
	.byte	'mc',0,2,168,4,13
	.word	.L200,.L258
	.byte	6
	.byte	'status',0,2,169,4,13
	.word	.L191,.L259
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_abbrev'
.L147:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,5,0,49,16,2,6,0,0,9,11,1,49,16,17,1,18
	.byte	1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_line'
.L148:
	.word	.L598-.L597
.L597:
	.half	3
	.word	.L600-.L599
.L599:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0,0
.L600:
	.byte	5,6,7,0,5,2
	.word	.L120
	.byte	3,148,2,1,5,54,9
	.half	.L424-.L120
	.byte	3,2,1,5,60,9
	.half	.L601-.L424
	.byte	1,5,29,9
	.half	.L602-.L601
	.byte	1,5,52,9
	.half	.L425-.L602
	.byte	1,5,16,9
	.half	.L603-.L425
	.byte	1,5,26,9
	.half	.L427-.L603
	.byte	3,3,1,5,35,9
	.half	.L428-.L427
	.byte	3,1,1,5,20,9
	.half	.L426-.L428
	.byte	1,5,56,9
	.half	.L604-.L426
	.byte	3,1,1,5,20,9
	.half	.L605-.L604
	.byte	1,5,22,9
	.half	.L606-.L605
	.byte	3,1,1,5,20,9
	.half	.L607-.L606
	.byte	1,5,17,9
	.half	.L608-.L607
	.byte	3,3,1,5,35,9
	.half	.L609-.L608
	.byte	3,3,1,5,68,9
	.half	.L610-.L609
	.byte	1,5,20,9
	.half	.L611-.L610
	.byte	1,5,35,9
	.half	.L612-.L611
	.byte	3,1,1,5,20,9
	.half	.L613-.L612
	.byte	1,5,29,9
	.half	.L614-.L613
	.byte	3,3,1,5,34,9
	.half	.L422-.L614
	.byte	1,5,37,9
	.half	.L423-.L422
	.byte	1,5,41,9
	.half	.L429-.L423
	.byte	3,2,1,5,14,9
	.half	.L41-.L429
	.byte	3,2,1,4,2,5,54,9
	.half	.L40-.L41
	.byte	3,253,1,1,5,60,9
	.half	.L615-.L40
	.byte	1,5,29,9
	.half	.L616-.L615
	.byte	1,5,52,9
	.half	.L431-.L616
	.byte	1,5,16,9
	.half	.L617-.L431
	.byte	1,5,25,9
	.half	.L433-.L617
	.byte	3,3,1,5,29,9
	.half	.L432-.L433
	.byte	3,1,1,5,5,9
	.half	.L434-.L432
	.byte	1,4,1,5,13,9
	.half	.L42-.L434
	.byte	3,253,125,1,5,1,7,9
	.half	.L618-.L42
	.byte	3,4,1,7,9
	.half	.L150-.L618
	.byte	0,1,1
.L598:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_ranges'
.L149:
	.word	-1,.L120,0,.L150-.L120,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_info'
.L151:
	.word	985
	.half	3
	.word	.L152
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L154,.L153
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_runNonDestructiveInversionTest',0,1,176,2,7
	.word	.L189
	.byte	1,1,1
	.word	.L122,.L260,.L121
	.byte	4
	.byte	'mbistSel',0,1,176,2,61
	.word	.L187,.L261
	.byte	4
	.byte	'rangeSel',0,1,176,2,77
	.word	.L189,.L262
	.byte	4
	.byte	'rangeAddrUp',0,1,176,2,93
	.word	.L189,.L263
	.byte	4
	.byte	'rangeAddrLow',0,1,176,2,112
	.word	.L189,.L264
	.byte	4
	.byte	'errorAddr',0,1,176,2,134,1
	.word	.L265,.L266
	.byte	5
	.word	.L122,.L260
	.byte	6
	.byte	'mc',0,1,181,2,13
	.word	.L200,.L267
	.byte	6
	.byte	'password',0,1,182,2,13
	.word	.L191,.L268
	.byte	6
	.byte	'retVal',0,1,183,2,13
	.word	.L189,.L269
	.byte	6
	.byte	'isEndInitEnabled',0,1,184,2,13
	.word	.L189,.L270
	.byte	7
	.word	.L193,.L271,.L43
	.byte	8
	.word	.L195,.L271,.L43
	.byte	0,7
	.word	.L224,.L44,.L272
	.byte	9
	.word	.L227,.L273
	.byte	10
	.word	.L229,.L44,.L272
	.byte	6
	.byte	'mtuMemtest',0,2,245,3,22
	.word	.L211,.L274
	.byte	6
	.byte	'mask',0,2,246,3,22
	.word	.L213,.L275
	.byte	0,0,7
	.word	.L215,.L45,.L47
	.byte	9
	.word	.L216,.L276
	.byte	10
	.word	.L218,.L45,.L47
	.byte	6
	.byte	'mtuMemstat',0,2,146,4,22
	.word	.L211,.L277
	.byte	6
	.byte	'mask',0,2,147,4,22
	.word	.L213,.L278
	.byte	0,0,7
	.word	.L196,.L48,.L50
	.byte	9
	.word	.L197,.L279
	.byte	10
	.word	.L199,.L48,.L50
	.byte	6
	.byte	'mc',0,2,168,4,13
	.word	.L200,.L280
	.byte	6
	.byte	'status',0,2,169,4,13
	.word	.L191,.L281
	.byte	0,0,7
	.word	.L282,.L283,.L51
	.byte	9
	.word	.L284,.L285
	.byte	10
	.word	.L286,.L283,.L51
	.byte	6
	.byte	'mc',0,2,184,4,13
	.word	.L200,.L287
	.byte	0,0,7
	.word	.L205,.L52,.L288
	.byte	9
	.word	.L208,.L289
	.byte	10
	.word	.L210,.L52,.L288
	.byte	6
	.byte	'mtuMemtest',0,2,237,3,22
	.word	.L211,.L290
	.byte	6
	.byte	'mask',0,2,238,3,22
	.word	.L213,.L291
	.byte	0,0,7
	.word	.L215,.L53,.L55
	.byte	9
	.word	.L216,.L276
	.byte	10
	.word	.L218,.L53,.L55
	.byte	6
	.byte	'mtuMemstat',0,2,146,4,22
	.word	.L211,.L292
	.byte	6
	.byte	'mask',0,2,147,4,22
	.word	.L213,.L293
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_abbrev'
.L152:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,9,5,0,49
	.byte	16,2,6,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_line'
.L153:
	.word	.L620-.L619
.L619:
	.half	3
	.word	.L622-.L621
.L621:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0,0
.L622:
	.byte	5,7,7,0,5,2
	.word	.L122
	.byte	3,175,2,1,5,68,9
	.half	.L442-.L122
	.byte	3,5,1,5,74,9
	.half	.L623-.L442
	.byte	1,5,43,9
	.half	.L624-.L623
	.byte	1,5,66,9
	.half	.L443-.L624
	.byte	1,5,30,9
	.half	.L625-.L443
	.byte	1,9
	.half	.L444-.L625
	.byte	3,2,1,9
	.half	.L446-.L444
	.byte	3,1,1,5,51,9
	.half	.L447-.L446
	.byte	3,1,1,5,14,9
	.half	.L435-.L447
	.byte	1,4,3,5,43,9
	.half	.L271-.L435
	.byte	3,192,1,1,5,5,9
	.half	.L626-.L271
	.byte	1,4,1,9
	.half	.L43-.L626
	.byte	3,195,126,1,5,38,7,9
	.half	.L627-.L43
	.byte	3,3,1,5,26,9
	.half	.L449-.L627
	.byte	3,1,1,4,2,5,93,9
	.half	.L44-.L449
	.byte	3,181,1,1,5,81,9
	.half	.L628-.L44
	.byte	1,5,55,9
	.half	.L629-.L628
	.byte	1,5,81,9
	.half	.L450-.L629
	.byte	1,5,33,9
	.half	.L630-.L450
	.byte	1,5,35,9
	.half	.L452-.L630
	.byte	3,1,1,5,50,9
	.half	.L631-.L452
	.byte	1,5,37,9
	.half	.L451-.L631
	.byte	1,5,6,9
	.half	.L453-.L451
	.byte	3,1,1,5,17,9
	.half	.L632-.L453
	.byte	1,4,1,5,46,9
	.half	.L272-.L632
	.byte	3,208,126,1,4,2,5,93,9
	.half	.L45-.L272
	.byte	3,203,1,1,5,81,9
	.half	.L633-.L45
	.byte	1,5,55,9
	.half	.L634-.L633
	.byte	1,5,81,9
	.half	.L454-.L634
	.byte	1,5,33,9
	.half	.L635-.L454
	.byte	1,5,35,9
	.half	.L456-.L635
	.byte	3,1,1,5,50,9
	.half	.L636-.L456
	.byte	1,5,37,9
	.half	.L455-.L636
	.byte	1,5,13,9
	.half	.L458-.L455
	.byte	3,1,1,5,25,9
	.half	.L637-.L458
	.byte	1,5,33,9
	.half	.L638-.L637
	.byte	1,5,5,9
	.half	.L639-.L638
	.byte	1,4,1,5,46,9
	.half	.L47-.L639
	.byte	3,179,126,1,5,21,7,9
	.half	.L640-.L47
	.byte	3,4,1,5,19,9
	.half	.L641-.L640
	.byte	1,5,21,9
	.half	.L642-.L641
	.byte	3,1,1,5,19,9
	.half	.L643-.L642
	.byte	1,5,31,9
	.half	.L644-.L643
	.byte	3,2,1,5,53,9
	.half	.L645-.L644
	.byte	1,5,38,9
	.half	.L439-.L645
	.byte	1,5,59,9
	.half	.L646-.L439
	.byte	1,5,19,9
	.half	.L441-.L646
	.byte	1,5,28,9
	.half	.L647-.L441
	.byte	3,3,1,5,26,9
	.half	.L648-.L647
	.byte	1,5,19,9
	.half	.L649-.L648
	.byte	3,1,1,5,26,9
	.half	.L650-.L649
	.byte	1,5,32,9
	.half	.L651-.L650
	.byte	3,2,1,5,45,9
	.half	.L457-.L651
	.byte	3,3,1,5,29,9
	.half	.L652-.L457
	.byte	1,5,45,9
	.half	.L653-.L652
	.byte	1,5,55,9
	.half	.L654-.L653
	.byte	1,5,68,9
	.half	.L655-.L654
	.byte	1,5,71,9
	.half	.L656-.L655
	.byte	1,5,41,9
	.half	.L461-.L656
	.byte	3,3,1,5,14,9
	.half	.L49-.L461
	.byte	3,2,1,4,2,5,54,9
	.half	.L48-.L49
	.byte	3,204,1,1,5,60,9
	.half	.L657-.L48
	.byte	1,5,29,9
	.half	.L658-.L657
	.byte	1,5,52,9
	.half	.L462-.L658
	.byte	1,5,16,9
	.half	.L659-.L462
	.byte	1,5,25,9
	.half	.L464-.L659
	.byte	3,3,1,5,29,9
	.half	.L463-.L464
	.byte	3,1,1,5,5,9
	.half	.L465-.L463
	.byte	1,4,1,5,13,9
	.half	.L50-.L465
	.byte	3,174,126,1,5,34,7,9
	.half	.L660-.L50
	.byte	3,6,1,4,2,5,54,9
	.half	.L283-.L660
	.byte	3,216,1,1,5,60,9
	.half	.L661-.L283
	.byte	1,5,29,9
	.half	.L662-.L661
	.byte	1,5,52,9
	.half	.L467-.L662
	.byte	1,5,16,9
	.half	.L663-.L467
	.byte	1,5,31,9
	.half	.L469-.L663
	.byte	3,1,1,5,36,9
	.half	.L468-.L469
	.byte	1,5,34,9
	.half	.L664-.L468
	.byte	1,5,63,9
	.half	.L665-.L664
	.byte	1,5,61,9
	.half	.L666-.L665
	.byte	1,5,5,9
	.half	.L667-.L666
	.byte	1,4,1,9
	.half	.L51-.L667
	.byte	3,170,126,1,5,33,7,9
	.half	.L668-.L51
	.byte	3,3,1,5,20,9
	.half	.L669-.L668
	.byte	1,9
	.half	.L670-.L669
	.byte	3,1,1,4,2,5,93,9
	.half	.L52-.L670
	.byte	3,134,1,1,5,81,9
	.half	.L671-.L52
	.byte	1,5,55,9
	.half	.L672-.L671
	.byte	1,5,81,9
	.half	.L470-.L672
	.byte	1,5,33,9
	.half	.L673-.L470
	.byte	1,5,35,9
	.half	.L445-.L673
	.byte	3,1,1,5,50,9
	.half	.L674-.L445
	.byte	1,5,37,9
	.half	.L471-.L674
	.byte	1,5,6,9
	.half	.L472-.L471
	.byte	3,1,1,5,20,9
	.half	.L675-.L472
	.byte	1,5,17,9
	.half	.L473-.L675
	.byte	1,4,1,5,46,9
	.half	.L288-.L473
	.byte	3,255,126,1,4,2,5,93,9
	.half	.L53-.L288
	.byte	3,164,1,1,5,81,9
	.half	.L676-.L53
	.byte	1,5,55,9
	.half	.L677-.L676
	.byte	1,5,81,9
	.half	.L474-.L677
	.byte	1,5,33,9
	.half	.L678-.L474
	.byte	1,5,35,9
	.half	.L476-.L678
	.byte	3,1,1,5,50,9
	.half	.L679-.L476
	.byte	1,5,37,9
	.half	.L475-.L679
	.byte	1,5,13,9
	.half	.L477-.L475
	.byte	3,1,1,5,25,9
	.half	.L680-.L477
	.byte	1,5,33,9
	.half	.L681-.L680
	.byte	1,5,5,9
	.half	.L682-.L681
	.byte	1,4,1,5,46,9
	.half	.L55-.L682
	.byte	3,218,126,1,5,5,7,9
	.half	.L683-.L55
	.byte	3,4,1,5,36,7,9
	.half	.L684-.L683
	.byte	3,3,1,5,5,9
	.half	.L56-.L684
	.byte	3,3,1,5,1,9
	.half	.L57-.L56
	.byte	3,1,1,7,9
	.half	.L155-.L57
	.byte	0,1,1
.L620:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_ranges'
.L154:
	.word	-1,.L122,0,.L155-.L122,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_info'
.L156:
	.word	542
	.half	3
	.word	.L157
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L159,.L158
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_writeSramAddress',0,1,252,2,6,1,1,1
	.word	.L124,.L294,.L123
	.byte	4
	.byte	'mbistSel',0,1,252,2,46
	.word	.L187,.L295
	.byte	4
	.byte	'sramAddress',0,1,252,2,63
	.word	.L191,.L296
	.byte	5
	.word	.L124,.L294
	.byte	6
	.byte	'mc',0,1,254,2,13
	.word	.L200,.L297
	.byte	6
	.byte	'isEndInitEnabled',0,1,255,2,13
	.word	.L189,.L298
	.byte	6
	.byte	'password',0,1,128,3,13
	.word	.L191,.L299
	.byte	7
	.word	.L193,.L300,.L58
	.byte	8
	.word	.L195,.L300,.L58
	.byte	0,5
	.word	.L59,.L294
	.byte	6
	.byte	'mcontrolMask',0,1,140,3,12
	.word	.L191,.L301
	.byte	7
	.word	.L196,.L61,.L63
	.byte	9
	.word	.L197,.L302
	.byte	10
	.word	.L199,.L61,.L63
	.byte	6
	.byte	'mc',0,2,168,4,13
	.word	.L200,.L303
	.byte	6
	.byte	'status',0,2,169,4,13
	.word	.L191,.L304
	.byte	0,0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_abbrev'
.L157:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,0,49,16,17,1,18,1,0,0,9,5,0,49,16,2
	.byte	6,0,0,10,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_line'
.L158:
	.word	.L686-.L685
.L685:
	.half	3
	.word	.L688-.L687
.L687:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\Mtu\\Std\\IfxMtu.h',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuWdt.h',0
	.byte	0,0,0,0
.L688:
	.byte	5,6,7,0,5,2
	.word	.L124
	.byte	3,251,2,1,5,68,9
	.half	.L689-.L124
	.byte	3,2,1,5,74,9
	.half	.L481-.L689
	.byte	1,5,43,9
	.half	.L482-.L481
	.byte	1,5,66,9
	.half	.L483-.L482
	.byte	1,5,30,9
	.half	.L690-.L483
	.byte	1,9
	.half	.L484-.L690
	.byte	3,1,1,5,51,9
	.half	.L486-.L484
	.byte	3,2,1,5,14,9
	.half	.L480-.L486
	.byte	1,4,3,5,43,9
	.half	.L300-.L480
	.byte	3,248,0,1,5,5,9
	.half	.L691-.L300
	.byte	1,4,1,9
	.half	.L58-.L691
	.byte	3,139,127,1,5,38,7,9
	.half	.L692-.L58
	.byte	3,3,1,5,26,9
	.half	.L488-.L692
	.byte	3,1,1,5,25,9
	.half	.L59-.L488
	.byte	3,4,1,5,35,9
	.half	.L489-.L59
	.byte	3,1,1,5,20,9
	.half	.L693-.L489
	.byte	1,5,56,9
	.half	.L694-.L693
	.byte	3,1,1,5,20,9
	.half	.L695-.L694
	.byte	1,5,22,9
	.half	.L696-.L695
	.byte	3,1,1,5,20,9
	.half	.L697-.L696
	.byte	1,5,17,9
	.half	.L490-.L697
	.byte	3,3,1,5,35,9
	.half	.L491-.L490
	.byte	3,3,1,5,68,9
	.half	.L698-.L491
	.byte	1,5,20,9
	.half	.L699-.L698
	.byte	1,5,35,9
	.half	.L700-.L699
	.byte	3,1,1,5,20,9
	.half	.L701-.L700
	.byte	1,5,5,9
	.half	.L702-.L701
	.byte	3,2,1,5,36,7,9
	.half	.L703-.L702
	.byte	3,3,1,5,45,9
	.half	.L60-.L703
	.byte	3,4,1,5,29,9
	.half	.L493-.L60
	.byte	1,5,45,9
	.half	.L704-.L493
	.byte	1,5,55,9
	.half	.L705-.L704
	.byte	1,5,68,9
	.half	.L706-.L705
	.byte	1,5,71,9
	.half	.L494-.L706
	.byte	1,5,41,9
	.half	.L496-.L494
	.byte	3,3,1,5,14,9
	.half	.L62-.L496
	.byte	3,2,1,4,2,5,54,9
	.half	.L61-.L62
	.byte	3,132,1,1,5,60,9
	.half	.L497-.L61
	.byte	1,5,29,9
	.half	.L498-.L497
	.byte	1,5,52,9
	.half	.L499-.L498
	.byte	1,5,16,9
	.half	.L707-.L499
	.byte	1,5,25,9
	.half	.L501-.L707
	.byte	3,3,1,5,29,9
	.half	.L500-.L501
	.byte	3,1,1,5,5,9
	.half	.L502-.L500
	.byte	1,4,1,5,13,9
	.half	.L63-.L502
	.byte	3,246,126,1,5,1,7,9
	.half	.L708-.L63
	.byte	3,4,1,7,9
	.half	.L160-.L708
	.byte	0,1,1
.L686:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_ranges'
.L159:
	.word	-1,.L124,0,.L160-.L124,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_info'
.L161:
	.word	328
	.half	3
	.word	.L162
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L164,.L163
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_clearErrorTracking',0,1,73,6,1,1,1
	.word	.L106,.L305,.L105
	.byte	4
	.byte	'mbistSel',0,1,73,48
	.word	.L187,.L306
	.byte	5
	.word	.L106,.L305
	.byte	6
	.byte	'mc',0,1,75,13
	.word	.L200,.L307
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_abbrev'
.L162:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_line'
.L163:
	.word	.L710-.L709
.L709:
	.half	3
	.word	.L712-.L711
.L711:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0,0
.L712:
	.byte	5,54,7,0,5,2
	.word	.L106
	.byte	3,202,0,1,5,60,9
	.half	.L713-.L106
	.byte	1,5,29,9
	.half	.L344-.L713
	.byte	1,5,52,9
	.half	.L345-.L344
	.byte	1,5,16,9
	.half	.L714-.L345
	.byte	1,5,13,9
	.half	.L346-.L714
	.byte	3,1,1,5,16,9
	.half	.L715-.L346
	.byte	1,5,1,9
	.half	.L716-.L715
	.byte	3,1,1,7,9
	.half	.L165-.L716
	.byte	0,1,1
.L710:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_ranges'
.L164:
	.word	-1,.L106,0,.L165-.L106,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_info'
.L166:
	.word	352
	.half	3
	.word	.L167
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L169,.L168
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_enableErrorTracking',0,1,202,1,6,1,1,1
	.word	.L114,.L308,.L113
	.byte	4
	.byte	'mbistSel',0,1,202,1,49
	.word	.L187,.L309
	.byte	4
	.byte	'enable',0,1,202,1,67
	.word	.L189,.L310
	.byte	5
	.word	.L114,.L308
	.byte	6
	.byte	'mc',0,1,204,1,13
	.word	.L200,.L311
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_abbrev'
.L167:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3
	.byte	8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_line'
.L168:
	.word	.L718-.L717
.L717:
	.half	3
	.word	.L720-.L719
.L719:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0,0
.L720:
	.byte	5,54,7,0,5,2
	.word	.L114
	.byte	3,203,1,1,5,60,9
	.half	.L721-.L114
	.byte	1,5,29,9
	.half	.L400-.L721
	.byte	1,5,52,9
	.half	.L401-.L400
	.byte	1,5,16,9
	.half	.L722-.L401
	.byte	1,5,5,9
	.half	.L402-.L722
	.byte	3,2,1,5,17,7,9
	.half	.L723-.L402
	.byte	3,2,1,5,20,9
	.half	.L724-.L723
	.byte	1,5,50,9
	.half	.L725-.L724
	.byte	1,5,17,9
	.half	.L22-.L725
	.byte	3,4,1,5,20,9
	.half	.L726-.L22
	.byte	1,5,1,9
	.half	.L23-.L726
	.byte	3,2,1,7,9
	.half	.L170-.L23
	.byte	0,1,1
.L718:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_ranges'
.L169:
	.word	-1,.L114,0,.L170-.L114,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_info'
.L171:
	.word	418
	.half	3
	.word	.L172
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L174,.L173
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_getSystemAddress',0,1,217,1,8
	.word	.L213
	.byte	1,1,1
	.word	.L116,.L312,.L115
	.byte	4
	.byte	'mbistSel',0,1,217,1,48
	.word	.L187,.L313
	.byte	4
	.byte	'trackedSramAddress',0,1,217,1,70
	.word	.L314,.L315
	.byte	5
	.word	.L116,.L312
	.byte	6
	.byte	'sramAddress',0,1,219,1,12
	.word	.L213,.L316
	.byte	6
	.byte	'mbi',0,1,220,1,12
	.word	.L213,.L317
	.byte	6
	.byte	'systemAddress',0,1,221,1,12
	.word	.L213,.L318
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_abbrev'
.L172:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_line'
.L173:
	.word	.L728-.L727
.L727:
	.half	3
	.word	.L730-.L729
.L729:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0,0
.L730:
	.byte	5,48,7,0,5,2
	.word	.L116
	.byte	3,218,1,1,9
	.half	.L403-.L116
	.byte	3,1,1,5,10,9
	.half	.L405-.L403
	.byte	3,17,1,9
	.half	.L731-.L405
	.byte	3,124,1,9
	.half	.L732-.L731
	.byte	3,124,1,9
	.half	.L733-.L732
	.byte	3,124,1,9
	.half	.L734-.L733
	.byte	3,16,1,5,52,9
	.half	.L27-.L734
	.byte	3,113,1,5,66,9
	.half	.L404-.L27
	.byte	1,5,71,9
	.half	.L735-.L404
	.byte	1,5,58,9
	.half	.L736-.L735
	.byte	1,5,25,9
	.half	.L737-.L736
	.byte	1,5,36,9
	.half	.L406-.L737
	.byte	1,5,9,9
	.half	.L738-.L406
	.byte	3,1,1,5,52,9
	.half	.L26-.L738
	.byte	3,3,1,5,66,9
	.half	.L407-.L26
	.byte	1,5,71,9
	.half	.L739-.L407
	.byte	1,5,58,9
	.half	.L740-.L739
	.byte	1,5,36,9
	.half	.L741-.L740
	.byte	1,5,9,9
	.half	.L408-.L741
	.byte	3,1,1,5,52,9
	.half	.L25-.L408
	.byte	3,3,1,5,66,9
	.half	.L409-.L25
	.byte	1,5,71,9
	.half	.L742-.L409
	.byte	1,5,58,9
	.half	.L743-.L742
	.byte	1,5,25,9
	.half	.L744-.L743
	.byte	1,5,36,9
	.half	.L410-.L744
	.byte	1,5,9,9
	.half	.L745-.L410
	.byte	3,1,1,5,52,9
	.half	.L24-.L745
	.byte	3,3,1,5,66,9
	.half	.L411-.L24
	.byte	1,5,71,9
	.half	.L746-.L411
	.byte	1,5,58,9
	.half	.L747-.L746
	.byte	1,5,36,9
	.half	.L748-.L747
	.byte	1,5,9,9
	.half	.L412-.L748
	.byte	3,1,1,5,52,9
	.half	.L28-.L412
	.byte	3,3,1,5,66,9
	.half	.L413-.L28
	.byte	1,5,71,9
	.half	.L749-.L413
	.byte	1,5,58,9
	.half	.L750-.L749
	.byte	1,5,25,9
	.half	.L751-.L750
	.byte	1,5,36,9
	.half	.L414-.L751
	.byte	1,5,9,9
	.half	.L752-.L414
	.byte	3,1,1,5,23,9
	.half	.L29-.L752
	.byte	3,3,1,5,5,9
	.half	.L30-.L29
	.byte	3,3,1,5,1,9
	.half	.L35-.L30
	.byte	3,1,1,7,9
	.half	.L175-.L35
	.byte	0,1,1
.L728:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_ranges'
.L174:
	.word	-1,.L116,0,.L175-.L116,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_info'
.L176:
	.word	446
	.half	3
	.word	.L177
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L179,.L178
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_getTrackedSramAddresses',0,1,253,1,7
	.word	.L189
	.byte	1,1,1
	.word	.L118,.L319,.L117
	.byte	4
	.byte	'mbistSel',0,1,253,1,54
	.word	.L187,.L320
	.byte	4
	.byte	'trackedSramAddresses',0,1,253,1,77
	.word	.L321,.L322
	.byte	5
	.word	.L118,.L319
	.byte	6
	.byte	'mc',0,1,255,1,13
	.word	.L200,.L323
	.byte	6
	.byte	'validFlags',0,1,128,2,13
	.word	.L189,.L324
	.byte	6
	.byte	'numTrackedAddresses',0,1,129,2,13
	.word	.L189,.L325
	.byte	6
	.byte	'i',0,1,130,2,13
	.word	.L326,.L327
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_abbrev'
.L177:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,73,16
	.byte	54,15,39,12,63,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6
	.byte	52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_line'
.L178:
	.word	.L754-.L753
.L753:
	.half	3
	.word	.L756-.L755
.L755:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0,0
.L756:
	.byte	5,71,7,0,5,2
	.word	.L118
	.byte	3,254,1,1,5,77,9
	.half	.L757-.L118
	.byte	1,5,46,9
	.half	.L415-.L757
	.byte	1,5,69,9
	.half	.L416-.L415
	.byte	1,5,33,9
	.half	.L758-.L416
	.byte	1,5,44,9
	.half	.L417-.L758
	.byte	3,1,1,5,47,9
	.half	.L759-.L417
	.byte	1,5,71,9
	.half	.L760-.L759
	.byte	1,5,33,9
	.half	.L418-.L760
	.byte	3,1,1,5,12,9
	.half	.L419-.L418
	.byte	3,7,1,5,49,9
	.half	.L421-.L419
	.byte	1,5,27,9
	.half	.L37-.L421
	.byte	3,2,1,5,29,9
	.half	.L761-.L37
	.byte	1,5,24,9
	.half	.L762-.L761
	.byte	1,5,9,9
	.half	.L763-.L762
	.byte	1,5,33,7,9
	.half	.L764-.L763
	.byte	3,2,1,5,67,9
	.half	.L765-.L764
	.byte	1,5,70,9
	.half	.L766-.L765
	.byte	1,5,57,9
	.half	.L767-.L766
	.byte	1,5,13,9
	.half	.L768-.L767
	.byte	3,1,1,5,51,9
	.half	.L38-.L768
	.byte	3,123,1,5,49,9
	.half	.L36-.L38
	.byte	1,5,5,7,9
	.half	.L769-.L36
	.byte	3,9,1,5,1,9
	.half	.L39-.L769
	.byte	3,1,1,7,9
	.half	.L180-.L39
	.byte	0,1,1
.L754:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_ranges'
.L179:
	.word	-1,.L118,0,.L180-.L118,0,0
	.sdecl	'.debug_info',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_info'
.L181:
	.word	527
	.half	3
	.word	.L182
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L184,.L183
	.byte	2
	.word	.L127
	.byte	3
	.byte	'IfxMtu_waitForMbistDone',0,1,169,3,17,1,1
	.word	.L126,.L328,.L125
	.byte	4
	.byte	'towerDepth',0,1,169,3,48
	.word	.L213,.L329
	.byte	4
	.byte	'numInstructions',0,1,169,3,66
	.word	.L189,.L330
	.byte	4
	.byte	'mbistSel',0,1,169,3,99
	.word	.L187,.L331
	.byte	5
	.word	.L126,.L328
	.byte	6
	.byte	'waitFact',0,1,171,3,21
	.word	.L213,.L332
	.byte	6
	.byte	'waitTime',0,1,172,3,21
	.word	.L333,.L334
	.byte	7
	.word	.L335,.L336,.L337
	.byte	8
	.word	.L338,.L336,.L337
	.byte	6
	.byte	'pll2ErayFrequency',0,2,203,8,13
	.word	.L339,.L340
	.byte	0,0,7
	.word	.L335,.L341,.L342
	.byte	8
	.word	.L338,.L341,.L342
	.byte	6
	.byte	'pll2ErayFrequency',0,2,203,8,13
	.word	.L339,.L343
	.byte	0,0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_abbrev'
.L182:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,17,1,18,1,64,6,0,0,4,5,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,5,11,1,17,1,18,1,0,0,6,52,0,3,8,58
	.byte	15,59,15,57,15,73,16,2,6,0,0,7,29,1,49,16,17,1,18,1,0,0,8,11,1,49,16,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_line'
.L183:
	.word	.L771-.L770
.L770:
	.half	3
	.word	.L773-.L772
.L772:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/Mtu/Std/IfxMtu.c',0,0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Scu\\Std\\IfxScuCcu.h',0
	.byte	0,0,0,0
.L773:
	.byte	5,17,7,0,5,2
	.word	.L126
	.byte	3,168,3,1,5,46,9
	.half	.L774-.L126
	.byte	3,2,1,5,69,9
	.half	.L775-.L774
	.byte	1,5,54,9
	.half	.L776-.L775
	.byte	1,5,78,9
	.half	.L505-.L776
	.byte	1,5,10,9
	.half	.L506-.L505
	.byte	3,13,1,9
	.half	.L777-.L506
	.byte	3,120,1,9
	.half	.L778-.L777
	.byte	3,1,1,9
	.half	.L779-.L778
	.byte	3,1,1,9
	.half	.L780-.L779
	.byte	3,1,1,9
	.half	.L781-.L780
	.byte	3,1,1,9
	.half	.L782-.L781
	.byte	3,1,1,9
	.half	.L783-.L782
	.byte	3,8,1,9
	.half	.L784-.L783
	.byte	3,127,1,9
	.half	.L785-.L784
	.byte	3,6,1,9
	.half	.L786-.L785
	.byte	3,1,1,9
	.half	.L787-.L786
	.byte	3,4,1,9
	.half	.L788-.L787
	.byte	3,4,1,9
	.half	.L789-.L788
	.byte	3,1,1,9
	.half	.L790-.L789
	.byte	3,1,1,9
	.half	.L791-.L790
	.byte	3,1,1,9
	.half	.L792-.L791
	.byte	3,1,1,9
	.half	.L793-.L792
	.byte	3,1,1,9
	.half	.L794-.L793
	.byte	3,1,1,9
	.half	.L795-.L794
	.byte	3,1,1,9
	.half	.L796-.L795
	.byte	3,5,1,9
	.half	.L797-.L796
	.byte	3,1,1,9
	.half	.L798-.L797
	.byte	3,1,1,9
	.half	.L799-.L798
	.byte	3,122,1,9
	.half	.L800-.L799
	.byte	3,1,1,9
	.half	.L801-.L800
	.byte	3,1,1,9
	.half	.L802-.L801
	.byte	3,1,1,5,44,9
	.half	.L70-.L802
	.byte	3,96,1,5,29,9
	.half	.L803-.L70
	.byte	1,5,9,9
	.half	.L804-.L803
	.byte	3,1,1,5,44,9
	.half	.L64-.L804
	.byte	3,2,1,5,29,9
	.half	.L805-.L64
	.byte	1,5,9,9
	.half	.L806-.L805
	.byte	3,1,1,5,44,9
	.half	.L71-.L806
	.byte	3,4,1,5,29,9
	.half	.L807-.L71
	.byte	1,5,9,9
	.half	.L808-.L807
	.byte	3,2,1,5,46,9
	.half	.L74-.L808
	.byte	3,4,1,5,49,9
	.half	.L504-.L74
	.byte	1,4,2,5,57,9
	.half	.L336-.L504
	.byte	3,137,5,1,5,80,9
	.half	.L809-.L336
	.byte	1,5,87,9
	.half	.L810-.L809
	.byte	1,5,60,9
	.half	.L811-.L810
	.byte	1,5,5,9
	.half	.L507-.L811
	.byte	3,2,1,4,1,5,49,9
	.half	.L95-.L507
	.byte	3,245,122,1,5,87,9
	.half	.L337-.L95
	.byte	1,5,85,9
	.half	.L508-.L337
	.byte	1,5,9,9
	.half	.L509-.L508
	.byte	3,1,1,5,46,9
	.half	.L75-.L509
	.byte	3,3,1,5,49,9
	.half	.L510-.L75
	.byte	1,4,2,5,57,9
	.half	.L341-.L510
	.byte	3,133,5,1,5,80,9
	.half	.L812-.L341
	.byte	1,5,87,9
	.half	.L813-.L812
	.byte	1,5,60,9
	.half	.L814-.L813
	.byte	1,5,5,9
	.half	.L511-.L814
	.byte	3,2,1,4,1,5,49,9
	.half	.L97-.L511
	.byte	3,249,122,1,5,87,9
	.half	.L342-.L97
	.byte	1,5,85,9
	.half	.L512-.L342
	.byte	1,5,105,9
	.half	.L815-.L512
	.byte	1,5,103,9
	.half	.L816-.L815
	.byte	1,5,9,9
	.half	.L513-.L816
	.byte	3,1,1,5,44,9
	.half	.L86-.L513
	.byte	3,17,1,5,29,9
	.half	.L817-.L86
	.byte	1,5,9,9
	.half	.L818-.L817
	.byte	3,1,1,9
	.half	.L91-.L818
	.byte	3,2,1,5,5,9
	.half	.L92-.L91
	.byte	3,3,1,5,46,7,9
	.half	.L515-.L92
	.byte	3,2,1,5,44,9
	.half	.L516-.L515
	.byte	1,5,18,9
	.half	.L517-.L516
	.byte	1,5,48,9
	.half	.L819-.L517
	.byte	1,5,35,9
	.half	.L101-.L819
	.byte	3,4,1,5,33,9
	.half	.L518-.L101
	.byte	1,5,52,9
	.half	.L519-.L518
	.byte	1,5,50,9
	.half	.L820-.L519
	.byte	1,5,18,9
	.half	.L821-.L820
	.byte	1,5,16,9
	.half	.L102-.L821
	.byte	3,3,1,5,27,9
	.half	.L822-.L102
	.byte	1,5,25,9
	.half	.L514-.L822
	.byte	1,5,14,9
	.half	.L823-.L514
	.byte	1,5,22,9
	.half	.L824-.L823
	.byte	3,2,1,5,14,9
	.half	.L104-.L824
	.byte	3,2,1,5,12,9
	.half	.L103-.L104
	.byte	3,126,1,5,20,9
	.half	.L825-.L103
	.byte	1,5,22,9
	.half	.L826-.L825
	.byte	1,5,1,7,9
	.half	.L827-.L826
	.byte	3,4,1,7,9
	.half	.L185-.L827
	.byte	0,1,1
.L771:
	.sdecl	'.debug_ranges',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_ranges'
.L184:
	.word	-1,.L126,0,.L185-.L126,0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_loc'
.L105:
	.word	-1,.L106,0,.L305-.L106
	.half	2
	.byte	138,0
	.word	0,0
.L306:
	.word	-1,.L106,0,.L344-.L106
	.half	1
	.byte	84
	.word	0,0
.L307:
	.word	-1,.L106,.L345-.L106,.L305-.L106
	.half	1
	.byte	84
	.word	.L346-.L106,.L305-.L106
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_loc'
.L107:
	.word	-1,.L108,0,.L186-.L108
	.half	2
	.byte	138,0
	.word	0,0
.L190:
	.word	-1,.L108,.L349-.L108,.L186-.L108
	.half	1
	.byte	89
	.word	0,0
.L188:
	.word	-1,.L108,0,.L347-.L108
	.half	1
	.byte	84
	.word	.L348-.L108,.L186-.L108
	.half	1
	.byte	88
	.word	.L352-.L108,.L353-.L108
	.half	1
	.byte	84
	.word	.L356-.L108,.L357-.L108
	.half	1
	.byte	86
	.word	.L364-.L108,.L365-.L108
	.half	1
	.byte	84
	.word	0,0
.L198:
	.word	0,0
.L201:
	.word	-1,.L108,.L5-.L108,.L4-.L108
	.half	1
	.byte	111
	.word	.L5-.L108,.L4-.L108
	.half	5
	.byte	144,32,157,32,0
	.word	.L358-.L108,.L359-.L108
	.half	5
	.byte	144,32,157,32,0
	.word	.L360-.L108,.L186-.L108
	.half	1
	.byte	111
	.word	0,0
.L192:
	.word	-1,.L108,.L347-.L108,.L350-.L108
	.half	1
	.byte	82
	.word	.L194-.L108,.L186-.L108
	.half	1
	.byte	90
	.word	.L350-.L108,.L351-.L108
	.half	1
	.byte	84
	.word	.L354-.L108,.L355-.L108
	.half	1
	.byte	84
	.word	.L363-.L108,.L359-.L108
	.half	1
	.byte	84
	.word	.L366-.L108,.L7-.L108
	.half	1
	.byte	84
	.word	0,0
.L202:
	.word	-1,.L108,.L361-.L108,.L362-.L108
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_loc'
.L109:
	.word	-1,.L110,0,.L203-.L110
	.half	2
	.byte	138,0
	.word	0,0
.L214:
	.word	-1,.L110,.L371-.L110,.L372-.L110
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L220:
	.word	-1,.L110,.L376-.L110,.L203-.L110
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L204:
	.word	-1,.L110,0,.L206-.L110
	.half	1
	.byte	84
	.word	.L367-.L110,.L203-.L110
	.half	1
	.byte	88
	.word	0,0
.L209:
	.word	0,0
.L217:
	.word	0,0
.L219:
	.word	-1,.L110,.L373-.L110,.L374-.L110
	.half	1
	.byte	95
	.word	.L375-.L110,.L203-.L110
	.half	1
	.byte	111
	.word	0,0
.L212:
	.word	-1,.L110,.L368-.L110,.L369-.L110
	.half	5
	.byte	144,32,157,32,0
	.word	.L370-.L110,.L8-.L110
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_loc'
.L111:
	.word	-1,.L112,0,.L221-.L112
	.half	2
	.byte	138,0
	.word	0,0
.L245:
	.word	-1,.L112,.L393-.L112,.L221-.L112
	.half	1
	.byte	81
	.word	0,0
.L247:
	.word	-1,.L112,.L397-.L112,.L221-.L112
	.half	1
	.byte	85
	.word	0,0
.L240:
	.word	-1,.L112,.L389-.L112,.L391-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L242:
	.word	-1,.L112,.L394-.L112,.L221-.L112
	.half	1
	.byte	82
	.word	0,0
.L243:
	.word	-1,.L112,.L395-.L112,.L221-.L112
	.half	1
	.byte	83
	.word	0,0
.L241:
	.word	-1,.L112,.L392-.L112,.L393-.L112
	.half	1
	.byte	81
	.word	0,0
.L250:
	.word	-1,.L112,.L399-.L112,.L14-.L112
	.half	1
	.byte	87
	.word	0,0
.L237:
	.word	-1,.L112,.L390-.L112,.L15-.L112
	.half	1
	.byte	111
	.word	0,0
.L231:
	.word	-1,.L112,.L383-.L112,.L11-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L234:
	.word	-1,.L112,.L388-.L112,.L389-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L222:
	.word	-1,.L112,0,.L377-.L112
	.half	1
	.byte	84
	.word	0,0
.L228:
	.word	0,0
.L232:
	.word	0,0
.L223:
	.word	-1,.L112,.L378-.L112,.L379-.L112
	.half	1
	.byte	95
	.word	.L225-.L112,.L221-.L112
	.half	1
	.byte	98
	.word	0,0
.L251:
	.word	-1,.L112,.L396-.L112,.L221-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L248:
	.word	-1,.L112,.L398-.L112,.L221-.L112
	.half	1
	.byte	86
	.word	0,0
.L244:
	.word	-1,.L112,.L395-.L112,.L396-.L112
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L233:
	.word	-1,.L112,.L384-.L112,.L385-.L112
	.half	1
	.byte	95
	.word	.L386-.L112,.L387-.L112
	.half	1
	.byte	111
	.word	0,0
.L230:
	.word	-1,.L112,.L380-.L112,.L381-.L112
	.half	1
	.byte	95
	.word	.L382-.L112,.L11-.L112
	.half	1
	.byte	111
	.word	0,0
.L238:
	.word	-1,.L112,.L239-.L112,.L221-.L112
	.half	1
	.byte	88
	.word	0,0
.L246:
	.word	-1,.L112,.L377-.L112,.L221-.L112
	.half	1
	.byte	84
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_loc'
.L113:
	.word	-1,.L114,0,.L308-.L114
	.half	2
	.byte	138,0
	.word	0,0
.L310:
	.word	-1,.L114,0,.L308-.L114
	.half	1
	.byte	85
	.word	0,0
.L309:
	.word	-1,.L114,0,.L400-.L114
	.half	1
	.byte	84
	.word	0,0
.L311:
	.word	-1,.L114,.L401-.L114,.L308-.L114
	.half	1
	.byte	84
	.word	.L402-.L114,.L308-.L114
	.half	1
	.byte	111
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L116,0,.L312-.L116
	.half	2
	.byte	138,0
	.word	0,0
.L317:
	.word	-1,.L116,.L405-.L116,.L312-.L116
	.half	1
	.byte	81
	.word	0,0
.L313:
	.word	-1,.L116,0,.L312-.L116
	.half	1
	.byte	84
	.word	0,0
.L316:
	.word	-1,.L116,.L403-.L116,.L404-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	.L26-.L116,.L407-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	.L25-.L116,.L409-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	.L24-.L116,.L411-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	.L28-.L116,.L413-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	.L29-.L116,.L30-.L116
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L318:
	.word	-1,.L116,.L406-.L116,.L26-.L116
	.half	1
	.byte	82
	.word	.L408-.L116,.L25-.L116
	.half	1
	.byte	82
	.word	.L410-.L116,.L24-.L116
	.half	1
	.byte	82
	.word	.L412-.L116,.L28-.L116
	.half	1
	.byte	82
	.word	.L414-.L116,.L29-.L116
	.half	1
	.byte	82
	.word	.L30-.L116,.L312-.L116
	.half	1
	.byte	82
	.word	0,0
.L315:
	.word	-1,.L116,0,.L312-.L116
	.half	1
	.byte	85
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_loc'
.L117:
	.word	-1,.L118,0,.L319-.L118
	.half	2
	.byte	138,0
	.word	0,0
.L327:
	.word	-1,.L118,.L421-.L118,.L319-.L118
	.half	1
	.byte	81
	.word	0,0
.L320:
	.word	-1,.L118,0,.L415-.L118
	.half	1
	.byte	84
	.word	0,0
.L323:
	.word	-1,.L118,.L416-.L118,.L319-.L118
	.half	1
	.byte	84
	.word	.L417-.L118,.L319-.L118
	.half	1
	.byte	111
	.word	0,0
.L325:
	.word	-1,.L118,.L419-.L118,.L420-.L118
	.half	1
	.byte	82
	.word	.L38-.L118,.L319-.L118
	.half	1
	.byte	82
	.word	0,0
.L322:
	.word	-1,.L118,0,.L319-.L118
	.half	1
	.byte	100
	.word	0,0
.L324:
	.word	-1,.L118,.L418-.L118,.L319-.L118
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_loc'
.L119:
	.word	-1,.L120,0,.L252-.L120
	.half	2
	.byte	138,0
	.word	0,0
.L253:
	.word	-1,.L120,0,.L422-.L120
	.half	1
	.byte	84
	.word	.L424-.L120,.L252-.L120
	.half	1
	.byte	88
	.word	.L430-.L120,.L429-.L120
	.half	1
	.byte	86
	.word	0,0
.L257:
	.word	0,0
.L255:
	.word	-1,.L120,.L425-.L120,.L426-.L120
	.half	1
	.byte	95
	.word	.L427-.L120,.L41-.L120
	.half	1
	.byte	111
	.word	0,0
.L258:
	.word	-1,.L120,.L41-.L120,.L40-.L120
	.half	1
	.byte	111
	.word	.L431-.L120,.L432-.L120
	.half	1
	.byte	95
	.word	.L433-.L120,.L252-.L120
	.half	1
	.byte	111
	.word	0,0
.L256:
	.word	-1,.L120,.L428-.L120,.L429-.L120
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L254:
	.word	-1,.L120,0,.L423-.L120
	.half	1
	.byte	85
	.word	0,0
.L259:
	.word	-1,.L120,.L432-.L120,.L434-.L120
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_loc'
.L121:
	.word	-1,.L122,0,.L260-.L122
	.half	2
	.byte	138,0
	.word	0,0
.L266:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	100
	.word	.L442-.L122,.L260-.L122
	.half	1
	.byte	108
	.word	0,0
.L270:
	.word	-1,.L122,.L447-.L122,.L260-.L122
	.half	1
	.byte	93
	.word	0,0
.L291:
	.word	-1,.L122,.L472-.L122,.L473-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L275:
	.word	-1,.L122,.L453-.L122,.L45-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L293:
	.word	-1,.L122,.L477-.L122,.L56-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L278:
	.word	-1,.L122,.L458-.L122,.L457-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L261:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	84
	.word	.L436-.L122,.L260-.L122
	.half	1
	.byte	88
	.word	.L460-.L122,.L461-.L122
	.half	1
	.byte	86
	.word	0,0
.L289:
	.word	0,0
.L273:
	.word	0,0
.L276:
	.word	0,0
.L279:
	.word	0,0
.L285:
	.word	0,0
.L267:
	.word	-1,.L122,.L443-.L122,.L435-.L122
	.half	5
	.byte	144,32,157,32,0
	.word	.L444-.L122,.L445-.L122
	.half	1
	.byte	111
	.word	0,0
.L280:
	.word	-1,.L122,.L49-.L122,.L48-.L122
	.half	1
	.byte	98
	.word	.L462-.L122,.L463-.L122
	.half	1
	.byte	95
	.word	.L464-.L122,.L283-.L122
	.half	1
	.byte	98
	.word	0,0
.L287:
	.word	-1,.L122,.L467-.L122,.L468-.L122
	.half	1
	.byte	95
	.word	.L469-.L122,.L56-.L122
	.half	1
	.byte	98
	.word	0,0
.L292:
	.word	-1,.L122,.L474-.L122,.L475-.L122
	.half	1
	.byte	95
	.word	.L476-.L122,.L260-.L122
	.half	1
	.byte	111
	.word	0,0
.L277:
	.word	-1,.L122,.L454-.L122,.L455-.L122
	.half	1
	.byte	95
	.word	.L456-.L122,.L457-.L122
	.half	1
	.byte	98
	.word	0,0
.L290:
	.word	-1,.L122,.L470-.L122,.L471-.L122
	.half	1
	.byte	95
	.word	.L445-.L122,.L53-.L122
	.half	1
	.byte	111
	.word	0,0
.L274:
	.word	-1,.L122,.L450-.L122,.L451-.L122
	.half	1
	.byte	95
	.word	.L452-.L122,.L45-.L122
	.half	1
	.byte	98
	.word	0,0
.L268:
	.word	-1,.L122,.L435-.L122,.L448-.L122
	.half	1
	.byte	82
	.word	.L271-.L122,.L260-.L122
	.half	1
	.byte	94
	.word	.L448-.L122,.L449-.L122
	.half	1
	.byte	84
	.word	.L459-.L122,.L457-.L122
	.half	1
	.byte	84
	.word	.L466-.L122,.L283-.L122
	.half	1
	.byte	84
	.word	.L478-.L122,.L56-.L122
	.half	1
	.byte	84
	.word	0,0
.L264:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	87
	.word	.L440-.L122,.L441-.L122
	.half	1
	.byte	91
	.word	0,0
.L263:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	86
	.word	.L438-.L122,.L439-.L122
	.half	1
	.byte	89
	.word	0,0
.L262:
	.word	-1,.L122,0,.L435-.L122
	.half	1
	.byte	85
	.word	.L437-.L122,.L260-.L122
	.half	1
	.byte	90
	.word	0,0
.L269:
	.word	-1,.L122,.L446-.L122,.L260-.L122
	.half	1
	.byte	92
	.word	.L479-.L122,.L260-.L122
	.half	1
	.byte	82
	.word	0,0
.L281:
	.word	-1,.L122,.L463-.L122,.L465-.L122
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_loc'
.L125:
	.word	-1,.L126,0,.L503-.L126
	.half	2
	.byte	138,0
	.word	.L503-.L126,.L328-.L126
	.half	2
	.byte	138,8
	.word	.L328-.L126,.L328-.L126
	.half	2
	.byte	138,0
	.word	0,0
.L331:
	.word	-1,.L126,0,.L504-.L126
	.half	1
	.byte	86
	.word	.L75-.L126,.L510-.L126
	.half	1
	.byte	86
	.word	.L86-.L126,.L92-.L126
	.half	1
	.byte	86
	.word	0,0
.L330:
	.word	-1,.L126,0,.L504-.L126
	.half	1
	.byte	85
	.word	.L505-.L126,.L506-.L126
	.half	1
	.byte	89
	.word	.L337-.L126,.L508-.L126
	.half	1
	.byte	89
	.word	.L75-.L126,.L510-.L126
	.half	1
	.byte	85
	.word	.L342-.L126,.L512-.L126
	.half	1
	.byte	89
	.word	.L86-.L126,.L92-.L126
	.half	1
	.byte	85
	.word	.L92-.L126,.L515-.L126
	.half	1
	.byte	89
	.word	0,0
.L340:
	.word	-1,.L126,.L507-.L126,.L337-.L126
	.half	1
	.byte	95
	.word	0,0
.L343:
	.word	-1,.L126,.L511-.L126,.L342-.L126
	.half	1
	.byte	95
	.word	0,0
.L329:
	.word	-1,.L126,0,.L504-.L126
	.half	1
	.byte	84
	.word	.L75-.L126,.L510-.L126
	.half	1
	.byte	84
	.word	.L86-.L126,.L92-.L126
	.half	1
	.byte	84
	.word	.L516-.L126,.L517-.L126
	.half	1
	.byte	88
	.word	.L518-.L126,.L519-.L126
	.half	1
	.byte	88
	.word	0,0
.L332:
	.word	-1,.L126,.L505-.L126,.L504-.L126
	.half	5
	.byte	144,32,157,32,0
	.word	.L509-.L126,.L510-.L126
	.half	5
	.byte	144,32,157,32,0
	.word	.L513-.L126,.L514-.L126
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L334:
	.word	-1,.L126,0,.L328-.L126
	.half	2
	.byte	145,120
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_loc'
.L123:
	.word	-1,.L124,0,.L294-.L124
	.half	2
	.byte	138,0
	.word	0,0
.L298:
	.word	-1,.L124,.L486-.L124,.L294-.L124
	.half	1
	.byte	90
	.word	0,0
.L295:
	.word	-1,.L124,0,.L480-.L124
	.half	1
	.byte	84
	.word	.L481-.L124,.L482-.L124
	.half	1
	.byte	88
	.word	.L60-.L124,.L493-.L124
	.half	1
	.byte	88
	.word	.L494-.L124,.L495-.L124
	.half	1
	.byte	88
	.word	.L495-.L124,.L496-.L124
	.half	1
	.byte	86
	.word	.L497-.L124,.L498-.L124
	.half	1
	.byte	88
	.word	0,0
.L302:
	.word	0,0
.L297:
	.word	-1,.L124,.L483-.L124,.L480-.L124
	.half	5
	.byte	144,32,157,32,0
	.word	.L484-.L124,.L485-.L124
	.half	1
	.byte	111
	.word	0,0
.L303:
	.word	-1,.L124,.L62-.L124,.L61-.L124
	.half	1
	.byte	111
	.word	.L499-.L124,.L500-.L124
	.half	1
	.byte	95
	.word	.L501-.L124,.L294-.L124
	.half	1
	.byte	111
	.word	0,0
.L301:
	.word	-1,.L124,.L489-.L124,.L60-.L124
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L299:
	.word	-1,.L124,.L480-.L124,.L487-.L124
	.half	1
	.byte	82
	.word	.L300-.L124,.L294-.L124
	.half	1
	.byte	91
	.word	.L487-.L124,.L488-.L124
	.half	1
	.byte	84
	.word	.L492-.L124,.L60-.L124
	.half	1
	.byte	84
	.word	0,0
.L296:
	.word	-1,.L124,0,.L480-.L124
	.half	1
	.byte	85
	.word	.L490-.L124,.L491-.L124
	.half	1
	.byte	89
	.word	0,0
.L304:
	.word	-1,.L124,.L500-.L124,.L502-.L124
	.half	1
	.byte	95
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L828:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_clearErrorTracking')
	.sect	'.debug_frame'
	.word	24
	.word	.L828,.L106,.L305-.L106
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_clearSram')
	.sect	'.debug_frame'
	.word	12
	.word	.L828,.L108,.L186-.L108
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_clearSramContinue')
	.sect	'.debug_frame'
	.word	12
	.word	.L828,.L110,.L203-.L110
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_clearSramStart')
	.sect	'.debug_frame'
	.word	24
	.word	.L828,.L112,.L221-.L112
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_enableErrorTracking')
	.sect	'.debug_frame'
	.word	24
	.word	.L828,.L114,.L308-.L114
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_getSystemAddress')
	.sect	'.debug_frame'
	.word	24
	.word	.L828,.L116,.L312-.L116
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_getTrackedSramAddresses')
	.sect	'.debug_frame'
	.word	20
	.word	.L828,.L118,.L319-.L118
	.byte	8,19,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_readSramAddress')
	.sect	'.debug_frame'
	.word	12
	.word	.L828,.L120,.L252-.L120
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_runNonDestructiveInversionTest')
	.sect	'.debug_frame'
	.word	12
	.word	.L828,.L122,.L260-.L122
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_writeSramAddress')
	.sect	'.debug_frame'
	.word	12
	.word	.L828,.L124,.L294-.L124
	.sdecl	'.debug_frame',debug,cluster('IfxMtu_waitForMbistDone')
	.sect	'.debug_frame'
	.word	36
	.word	.L828,.L126,.L328-.L126
	.byte	4
	.word	(.L503-.L126)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L328-.L503)/2
	.byte	19,0,8,26,0,0
	; Module end
