/**
 * \file IfxCif_bf.h
 * \brief
 * \copyright Copyright (c) 2015 Infineon Technologies AG. All rights reserved.
 *
 * Version: TC26XB_UM_V1.2.R0
 * Specification: tc26xB_um_v1.2_MCSFR.xml (Revision: UM_V1.2)
 * MAY BE CHANGED BY USER [yes/no]: No
 *
 *                                 IMPORTANT NOTICE
 *
 * Infineon Technologies AG (Infineon) is supplying this file for use
 * exclusively with Infineon's microcontroller products. This file can be freely
 * distributed within development tools that are supporting such microcontroller
 * products.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS".  NO WARRANTIES, WHETHER EXPRESS, IMPLIED
 * OR STATUTORY, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE.
 * INFINEON SHALL NOT, IN ANY CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL,
 * OR CONSEQUENTIAL DAMAGES, FOR ANY REASON WHATSOEVER.
 *
 * \defgroup IfxLld_Cif_BitfieldsMask Bitfields mask and offset
 * \ingroup IfxLld_Cif
 * 
 */
#ifndef IFXCIF_BF_H
#define IFXCIF_BF_H 1
/******************************************************************************/
/******************************************************************************/
/** \addtogroup IfxLld_Cif_BitfieldsMask
 * \{  */

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN0 */
#define IFX_CIF_BBB_ACCEN0_EN0_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN0 */
#define IFX_CIF_BBB_ACCEN0_EN0_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN0 */
#define IFX_CIF_BBB_ACCEN0_EN0_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN10 */
#define IFX_CIF_BBB_ACCEN0_EN10_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN10 */
#define IFX_CIF_BBB_ACCEN0_EN10_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN10 */
#define IFX_CIF_BBB_ACCEN0_EN10_OFF (10u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN11 */
#define IFX_CIF_BBB_ACCEN0_EN11_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN11 */
#define IFX_CIF_BBB_ACCEN0_EN11_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN11 */
#define IFX_CIF_BBB_ACCEN0_EN11_OFF (11u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN12 */
#define IFX_CIF_BBB_ACCEN0_EN12_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN12 */
#define IFX_CIF_BBB_ACCEN0_EN12_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN12 */
#define IFX_CIF_BBB_ACCEN0_EN12_OFF (12u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN13 */
#define IFX_CIF_BBB_ACCEN0_EN13_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN13 */
#define IFX_CIF_BBB_ACCEN0_EN13_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN13 */
#define IFX_CIF_BBB_ACCEN0_EN13_OFF (13u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN14 */
#define IFX_CIF_BBB_ACCEN0_EN14_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN14 */
#define IFX_CIF_BBB_ACCEN0_EN14_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN14 */
#define IFX_CIF_BBB_ACCEN0_EN14_OFF (14u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN15 */
#define IFX_CIF_BBB_ACCEN0_EN15_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN15 */
#define IFX_CIF_BBB_ACCEN0_EN15_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN15 */
#define IFX_CIF_BBB_ACCEN0_EN15_OFF (15u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN16 */
#define IFX_CIF_BBB_ACCEN0_EN16_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN16 */
#define IFX_CIF_BBB_ACCEN0_EN16_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN16 */
#define IFX_CIF_BBB_ACCEN0_EN16_OFF (16u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN17 */
#define IFX_CIF_BBB_ACCEN0_EN17_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN17 */
#define IFX_CIF_BBB_ACCEN0_EN17_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN17 */
#define IFX_CIF_BBB_ACCEN0_EN17_OFF (17u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN18 */
#define IFX_CIF_BBB_ACCEN0_EN18_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN18 */
#define IFX_CIF_BBB_ACCEN0_EN18_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN18 */
#define IFX_CIF_BBB_ACCEN0_EN18_OFF (18u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN19 */
#define IFX_CIF_BBB_ACCEN0_EN19_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN19 */
#define IFX_CIF_BBB_ACCEN0_EN19_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN19 */
#define IFX_CIF_BBB_ACCEN0_EN19_OFF (19u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN1 */
#define IFX_CIF_BBB_ACCEN0_EN1_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN1 */
#define IFX_CIF_BBB_ACCEN0_EN1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN1 */
#define IFX_CIF_BBB_ACCEN0_EN1_OFF (1u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN20 */
#define IFX_CIF_BBB_ACCEN0_EN20_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN20 */
#define IFX_CIF_BBB_ACCEN0_EN20_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN20 */
#define IFX_CIF_BBB_ACCEN0_EN20_OFF (20u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN21 */
#define IFX_CIF_BBB_ACCEN0_EN21_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN21 */
#define IFX_CIF_BBB_ACCEN0_EN21_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN21 */
#define IFX_CIF_BBB_ACCEN0_EN21_OFF (21u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN22 */
#define IFX_CIF_BBB_ACCEN0_EN22_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN22 */
#define IFX_CIF_BBB_ACCEN0_EN22_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN22 */
#define IFX_CIF_BBB_ACCEN0_EN22_OFF (22u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN23 */
#define IFX_CIF_BBB_ACCEN0_EN23_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN23 */
#define IFX_CIF_BBB_ACCEN0_EN23_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN23 */
#define IFX_CIF_BBB_ACCEN0_EN23_OFF (23u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN24 */
#define IFX_CIF_BBB_ACCEN0_EN24_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN24 */
#define IFX_CIF_BBB_ACCEN0_EN24_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN24 */
#define IFX_CIF_BBB_ACCEN0_EN24_OFF (24u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN25 */
#define IFX_CIF_BBB_ACCEN0_EN25_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN25 */
#define IFX_CIF_BBB_ACCEN0_EN25_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN25 */
#define IFX_CIF_BBB_ACCEN0_EN25_OFF (25u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN26 */
#define IFX_CIF_BBB_ACCEN0_EN26_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN26 */
#define IFX_CIF_BBB_ACCEN0_EN26_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN26 */
#define IFX_CIF_BBB_ACCEN0_EN26_OFF (26u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN27 */
#define IFX_CIF_BBB_ACCEN0_EN27_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN27 */
#define IFX_CIF_BBB_ACCEN0_EN27_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN27 */
#define IFX_CIF_BBB_ACCEN0_EN27_OFF (27u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN28 */
#define IFX_CIF_BBB_ACCEN0_EN28_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN28 */
#define IFX_CIF_BBB_ACCEN0_EN28_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN28 */
#define IFX_CIF_BBB_ACCEN0_EN28_OFF (28u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN29 */
#define IFX_CIF_BBB_ACCEN0_EN29_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN29 */
#define IFX_CIF_BBB_ACCEN0_EN29_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN29 */
#define IFX_CIF_BBB_ACCEN0_EN29_OFF (29u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN2 */
#define IFX_CIF_BBB_ACCEN0_EN2_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN2 */
#define IFX_CIF_BBB_ACCEN0_EN2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN2 */
#define IFX_CIF_BBB_ACCEN0_EN2_OFF (2u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN30 */
#define IFX_CIF_BBB_ACCEN0_EN30_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN30 */
#define IFX_CIF_BBB_ACCEN0_EN30_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN30 */
#define IFX_CIF_BBB_ACCEN0_EN30_OFF (30u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN31 */
#define IFX_CIF_BBB_ACCEN0_EN31_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN31 */
#define IFX_CIF_BBB_ACCEN0_EN31_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN31 */
#define IFX_CIF_BBB_ACCEN0_EN31_OFF (31u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN3 */
#define IFX_CIF_BBB_ACCEN0_EN3_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN3 */
#define IFX_CIF_BBB_ACCEN0_EN3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN3 */
#define IFX_CIF_BBB_ACCEN0_EN3_OFF (3u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN4 */
#define IFX_CIF_BBB_ACCEN0_EN4_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN4 */
#define IFX_CIF_BBB_ACCEN0_EN4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN4 */
#define IFX_CIF_BBB_ACCEN0_EN4_OFF (4u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN5 */
#define IFX_CIF_BBB_ACCEN0_EN5_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN5 */
#define IFX_CIF_BBB_ACCEN0_EN5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN5 */
#define IFX_CIF_BBB_ACCEN0_EN5_OFF (5u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN6 */
#define IFX_CIF_BBB_ACCEN0_EN6_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN6 */
#define IFX_CIF_BBB_ACCEN0_EN6_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN6 */
#define IFX_CIF_BBB_ACCEN0_EN6_OFF (6u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN7 */
#define IFX_CIF_BBB_ACCEN0_EN7_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN7 */
#define IFX_CIF_BBB_ACCEN0_EN7_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN7 */
#define IFX_CIF_BBB_ACCEN0_EN7_OFF (7u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN8 */
#define IFX_CIF_BBB_ACCEN0_EN8_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN8 */
#define IFX_CIF_BBB_ACCEN0_EN8_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN8 */
#define IFX_CIF_BBB_ACCEN0_EN8_OFF (8u)

/** \brief  Length for Ifx_CIF_BBB_ACCEN0_Bits.EN9 */
#define IFX_CIF_BBB_ACCEN0_EN9_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_ACCEN0_Bits.EN9 */
#define IFX_CIF_BBB_ACCEN0_EN9_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_ACCEN0_Bits.EN9 */
#define IFX_CIF_BBB_ACCEN0_EN9_OFF (9u)

/** \brief  Length for Ifx_CIF_BBB_CLC_Bits.DISR */
#define IFX_CIF_BBB_CLC_DISR_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_CLC_Bits.DISR */
#define IFX_CIF_BBB_CLC_DISR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_CLC_Bits.DISR */
#define IFX_CIF_BBB_CLC_DISR_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_CLC_Bits.DISS */
#define IFX_CIF_BBB_CLC_DISS_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_CLC_Bits.DISS */
#define IFX_CIF_BBB_CLC_DISS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_CLC_Bits.DISS */
#define IFX_CIF_BBB_CLC_DISS_OFF (1u)

/** \brief  Length for Ifx_CIF_BBB_GPCTL_Bits.PISEL */
#define IFX_CIF_BBB_GPCTL_PISEL_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_GPCTL_Bits.PISEL */
#define IFX_CIF_BBB_GPCTL_PISEL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_GPCTL_Bits.PISEL */
#define IFX_CIF_BBB_GPCTL_PISEL_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_KRST0_Bits.RST */
#define IFX_CIF_BBB_KRST0_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_KRST0_Bits.RST */
#define IFX_CIF_BBB_KRST0_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_KRST0_Bits.RST */
#define IFX_CIF_BBB_KRST0_RST_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_KRST0_Bits.RSTSTAT */
#define IFX_CIF_BBB_KRST0_RSTSTAT_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_KRST0_Bits.RSTSTAT */
#define IFX_CIF_BBB_KRST0_RSTSTAT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_KRST0_Bits.RSTSTAT */
#define IFX_CIF_BBB_KRST0_RSTSTAT_OFF (1u)

/** \brief  Length for Ifx_CIF_BBB_KRST1_Bits.RST */
#define IFX_CIF_BBB_KRST1_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_KRST1_Bits.RST */
#define IFX_CIF_BBB_KRST1_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_KRST1_Bits.RST */
#define IFX_CIF_BBB_KRST1_RST_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_KRSTCLR_Bits.CLR */
#define IFX_CIF_BBB_KRSTCLR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_BBB_KRSTCLR_Bits.CLR */
#define IFX_CIF_BBB_KRSTCLR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_BBB_KRSTCLR_Bits.CLR */
#define IFX_CIF_BBB_KRSTCLR_CLR_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_MODID_Bits.MODNUMBER */
#define IFX_CIF_BBB_MODID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_CIF_BBB_MODID_Bits.MODNUMBER */
#define IFX_CIF_BBB_MODID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_BBB_MODID_Bits.MODNUMBER */
#define IFX_CIF_BBB_MODID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_CIF_BBB_MODID_Bits.MODREV */
#define IFX_CIF_BBB_MODID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_CIF_BBB_MODID_Bits.MODREV */
#define IFX_CIF_BBB_MODID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_BBB_MODID_Bits.MODREV */
#define IFX_CIF_BBB_MODID_MODREV_OFF (0u)

/** \brief  Length for Ifx_CIF_BBB_MODID_Bits.MODTYPE */
#define IFX_CIF_BBB_MODID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_CIF_BBB_MODID_Bits.MODTYPE */
#define IFX_CIF_BBB_MODID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_BBB_MODID_Bits.MODTYPE */
#define IFX_CIF_BBB_MODID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_CIF_CCL_Bits.CIF_CCLDISS */
#define IFX_CIF_CCL_CIF_CCLDISS_LEN (1u)

/** \brief  Mask for Ifx_CIF_CCL_Bits.CIF_CCLDISS */
#define IFX_CIF_CCL_CIF_CCLDISS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_CCL_Bits.CIF_CCLDISS */
#define IFX_CIF_CCL_CIF_CCLDISS_OFF (1u)

/** \brief  Length for Ifx_CIF_CCL_Bits.CIF_CCLFDIS */
#define IFX_CIF_CCL_CIF_CCLFDIS_LEN (1u)

/** \brief  Mask for Ifx_CIF_CCL_Bits.CIF_CCLFDIS */
#define IFX_CIF_CCL_CIF_CCLFDIS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_CCL_Bits.CIF_CCLFDIS */
#define IFX_CIF_CCL_CIF_CCLFDIS_OFF (2u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.DP_EN */
#define IFX_CIF_DP_CTRL_DP_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.DP_EN */
#define IFX_CIF_DP_CTRL_DP_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.DP_EN */
#define IFX_CIF_DP_CTRL_DP_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.DP_SEL */
#define IFX_CIF_DP_CTRL_DP_SEL_LEN (3u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.DP_SEL */
#define IFX_CIF_DP_CTRL_DP_SEL_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.DP_SEL */
#define IFX_CIF_DP_CTRL_DP_SEL_OFF (1u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.FNC_EN */
#define IFX_CIF_DP_CTRL_FNC_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.FNC_EN */
#define IFX_CIF_DP_CTRL_FNC_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.FNC_EN */
#define IFX_CIF_DP_CTRL_FNC_EN_OFF (13u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.LNC_EN */
#define IFX_CIF_DP_CTRL_LNC_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.LNC_EN */
#define IFX_CIF_DP_CTRL_LNC_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.LNC_EN */
#define IFX_CIF_DP_CTRL_LNC_EN_OFF (14u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.RST_FNC */
#define IFX_CIF_DP_CTRL_RST_FNC_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.RST_FNC */
#define IFX_CIF_DP_CTRL_RST_FNC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.RST_FNC */
#define IFX_CIF_DP_CTRL_RST_FNC_OFF (8u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.RST_LNC */
#define IFX_CIF_DP_CTRL_RST_LNC_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.RST_LNC */
#define IFX_CIF_DP_CTRL_RST_LNC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.RST_LNC */
#define IFX_CIF_DP_CTRL_RST_LNC_OFF (9u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.RST_PD */
#define IFX_CIF_DP_CTRL_RST_PD_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.RST_PD */
#define IFX_CIF_DP_CTRL_RST_PD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.RST_PD */
#define IFX_CIF_DP_CTRL_RST_PD_OFF (11u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.RST_TSC */
#define IFX_CIF_DP_CTRL_RST_TSC_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.RST_TSC */
#define IFX_CIF_DP_CTRL_RST_TSC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.RST_TSC */
#define IFX_CIF_DP_CTRL_RST_TSC_OFF (10u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.TSC_EN */
#define IFX_CIF_DP_CTRL_TSC_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.TSC_EN */
#define IFX_CIF_DP_CTRL_TSC_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.TSC_EN */
#define IFX_CIF_DP_CTRL_TSC_EN_OFF (15u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS1 */
#define IFX_CIF_DP_CTRL_UDS1_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS1 */
#define IFX_CIF_DP_CTRL_UDS1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS1 */
#define IFX_CIF_DP_CTRL_UDS1_OFF (16u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS2 */
#define IFX_CIF_DP_CTRL_UDS2_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS2 */
#define IFX_CIF_DP_CTRL_UDS2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS2 */
#define IFX_CIF_DP_CTRL_UDS2_OFF (17u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS3 */
#define IFX_CIF_DP_CTRL_UDS3_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS3 */
#define IFX_CIF_DP_CTRL_UDS3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS3 */
#define IFX_CIF_DP_CTRL_UDS3_OFF (18u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS4 */
#define IFX_CIF_DP_CTRL_UDS4_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS4 */
#define IFX_CIF_DP_CTRL_UDS4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS4 */
#define IFX_CIF_DP_CTRL_UDS4_OFF (19u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS5 */
#define IFX_CIF_DP_CTRL_UDS5_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS5 */
#define IFX_CIF_DP_CTRL_UDS5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS5 */
#define IFX_CIF_DP_CTRL_UDS5_OFF (20u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS6 */
#define IFX_CIF_DP_CTRL_UDS6_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS6 */
#define IFX_CIF_DP_CTRL_UDS6_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS6 */
#define IFX_CIF_DP_CTRL_UDS6_OFF (21u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS7 */
#define IFX_CIF_DP_CTRL_UDS7_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS7 */
#define IFX_CIF_DP_CTRL_UDS7_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS7 */
#define IFX_CIF_DP_CTRL_UDS7_OFF (22u)

/** \brief  Length for Ifx_CIF_DP_CTRL_Bits.UDS8 */
#define IFX_CIF_DP_CTRL_UDS8_LEN (1u)

/** \brief  Mask for Ifx_CIF_DP_CTRL_Bits.UDS8 */
#define IFX_CIF_DP_CTRL_UDS8_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_DP_CTRL_Bits.UDS8 */
#define IFX_CIF_DP_CTRL_UDS8_OFF (23u)

/** \brief  Length for Ifx_CIF_DP_FLC_STAT_Bits.FNC_VAL */
#define IFX_CIF_DP_FLC_STAT_FNC_VAL_LEN (15u)

/** \brief  Mask for Ifx_CIF_DP_FLC_STAT_Bits.FNC_VAL */
#define IFX_CIF_DP_FLC_STAT_FNC_VAL_MSK (0x7fffu)

/** \brief  Offset for Ifx_CIF_DP_FLC_STAT_Bits.FNC_VAL */
#define IFX_CIF_DP_FLC_STAT_FNC_VAL_OFF (0u)

/** \brief  Length for Ifx_CIF_DP_FLC_STAT_Bits.LNC_VAL */
#define IFX_CIF_DP_FLC_STAT_LNC_VAL_LEN (15u)

/** \brief  Mask for Ifx_CIF_DP_FLC_STAT_Bits.LNC_VAL */
#define IFX_CIF_DP_FLC_STAT_LNC_VAL_MSK (0x7fffu)

/** \brief  Offset for Ifx_CIF_DP_FLC_STAT_Bits.LNC_VAL */
#define IFX_CIF_DP_FLC_STAT_LNC_VAL_OFF (16u)

/** \brief  Length for Ifx_CIF_DP_PDIV_CTRL_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_CTRL_PDIV_VAL_LEN (32u)

/** \brief  Mask for Ifx_CIF_DP_PDIV_CTRL_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_CTRL_PDIV_VAL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_CIF_DP_PDIV_CTRL_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_CTRL_PDIV_VAL_OFF (0u)

/** \brief  Length for Ifx_CIF_DP_PDIV_STAT_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_STAT_PDIV_VAL_LEN (32u)

/** \brief  Mask for Ifx_CIF_DP_PDIV_STAT_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_STAT_PDIV_VAL_MSK (0xffffffffu)

/** \brief  Offset for Ifx_CIF_DP_PDIV_STAT_Bits.PDIV_VAL */
#define IFX_CIF_DP_PDIV_STAT_PDIV_VAL_OFF (0u)

/** \brief  Length for Ifx_CIF_DP_TSC_STAT_Bits.TSC_VAL */
#define IFX_CIF_DP_TSC_STAT_TSC_VAL_LEN (30u)

/** \brief  Mask for Ifx_CIF_DP_TSC_STAT_Bits.TSC_VAL */
#define IFX_CIF_DP_TSC_STAT_TSC_VAL_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_DP_TSC_STAT_Bits.TSC_VAL */
#define IFX_CIF_DP_TSC_STAT_TSC_VAL_OFF (0u)

/** \brief  Length for Ifx_CIF_DP_UDS_Bits.UDS */
#define IFX_CIF_DP_UDS_UDS_LEN (15u)

/** \brief  Mask for Ifx_CIF_DP_UDS_Bits.UDS */
#define IFX_CIF_DP_UDS_UDS_MSK (0x7fffu)

/** \brief  Offset for Ifx_CIF_DP_UDS_Bits.UDS */
#define IFX_CIF_DP_UDS_UDS_OFF (0u)

/** \brief  Length for Ifx_CIF_DPCL_Bits.CIF_CHAN_MODE */
#define IFX_CIF_DPCL_CIF_CHAN_MODE_LEN (2u)

/** \brief  Mask for Ifx_CIF_DPCL_Bits.CIF_CHAN_MODE */
#define IFX_CIF_DPCL_CIF_CHAN_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_DPCL_Bits.CIF_CHAN_MODE */
#define IFX_CIF_DPCL_CIF_CHAN_MODE_OFF (2u)

/** \brief  Length for Ifx_CIF_DPCL_Bits.CIF_MP_MUX */
#define IFX_CIF_DPCL_CIF_MP_MUX_LEN (2u)

/** \brief  Mask for Ifx_CIF_DPCL_Bits.CIF_MP_MUX */
#define IFX_CIF_DPCL_CIF_MP_MUX_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_DPCL_Bits.CIF_MP_MUX */
#define IFX_CIF_DPCL_CIF_MP_MUX_OFF (0u)

/** \brief  Length for Ifx_CIF_DPCL_Bits.IF_SELECT */
#define IFX_CIF_DPCL_IF_SELECT_LEN (2u)

/** \brief  Mask for Ifx_CIF_DPCL_Bits.IF_SELECT */
#define IFX_CIF_DPCL_IF_SELECT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_DPCL_Bits.IF_SELECT */
#define IFX_CIF_DPCL_IF_SELECT_OFF (8u)

/** \brief  Length for Ifx_CIF_EP_IC_CTRL_Bits.IC_EN */
#define IFX_CIF_EP_IC_CTRL_IC_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_EP_IC_CTRL_Bits.IC_EN */
#define IFX_CIF_EP_IC_CTRL_IC_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_EP_IC_CTRL_Bits.IC_EN */
#define IFX_CIF_EP_IC_CTRL_IC_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_DISPLACE_Bits.DX */
#define IFX_CIF_EP_IC_DISPLACE_DX_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_DISPLACE_Bits.DX */
#define IFX_CIF_EP_IC_DISPLACE_DX_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_DISPLACE_Bits.DX */
#define IFX_CIF_EP_IC_DISPLACE_DX_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_DISPLACE_Bits.DY */
#define IFX_CIF_EP_IC_DISPLACE_DY_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_DISPLACE_Bits.DY */
#define IFX_CIF_EP_IC_DISPLACE_DY_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_DISPLACE_Bits.DY */
#define IFX_CIF_EP_IC_DISPLACE_DY_OFF (16u)

/** \brief  Length for Ifx_CIF_EP_IC_H_OFFS_Bits.H_OFFS */
#define IFX_CIF_EP_IC_H_OFFS_H_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_H_OFFS_Bits.H_OFFS */
#define IFX_CIF_EP_IC_H_OFFS_H_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_H_OFFS_Bits.H_OFFS */
#define IFX_CIF_EP_IC_H_OFFS_H_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_H_OFFS_SHD_Bits.H_OFFS_SHD */
#define IFX_CIF_EP_IC_H_OFFS_SHD_H_OFFS_SHD_LEN (13u)

/** \brief  Mask for Ifx_CIF_EP_IC_H_OFFS_SHD_Bits.H_OFFS_SHD */
#define IFX_CIF_EP_IC_H_OFFS_SHD_H_OFFS_SHD_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_EP_IC_H_OFFS_SHD_Bits.H_OFFS_SHD */
#define IFX_CIF_EP_IC_H_OFFS_SHD_H_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_H_SIZE_Bits.H_SIZE */
#define IFX_CIF_EP_IC_H_SIZE_H_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_H_SIZE_Bits.H_SIZE */
#define IFX_CIF_EP_IC_H_SIZE_H_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_H_SIZE_Bits.H_SIZE */
#define IFX_CIF_EP_IC_H_SIZE_H_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_H_SIZE_SHD_Bits.H_SIZE_SHD */
#define IFX_CIF_EP_IC_H_SIZE_SHD_H_SIZE_SHD_LEN (13u)

/** \brief  Mask for Ifx_CIF_EP_IC_H_SIZE_SHD_Bits.H_SIZE_SHD */
#define IFX_CIF_EP_IC_H_SIZE_SHD_H_SIZE_SHD_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_EP_IC_H_SIZE_SHD_Bits.H_SIZE_SHD */
#define IFX_CIF_EP_IC_H_SIZE_SHD_H_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_MAX_DX_Bits.MAX_DX */
#define IFX_CIF_EP_IC_MAX_DX_MAX_DX_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_MAX_DX_Bits.MAX_DX */
#define IFX_CIF_EP_IC_MAX_DX_MAX_DX_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_MAX_DX_Bits.MAX_DX */
#define IFX_CIF_EP_IC_MAX_DX_MAX_DX_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_MAX_DY_Bits.MAX_DY */
#define IFX_CIF_EP_IC_MAX_DY_MAX_DY_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_MAX_DY_Bits.MAX_DY */
#define IFX_CIF_EP_IC_MAX_DY_MAX_DY_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_MAX_DY_Bits.MAX_DY */
#define IFX_CIF_EP_IC_MAX_DY_MAX_DY_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_RECENTER_Bits.RECENTER */
#define IFX_CIF_EP_IC_RECENTER_RECENTER_LEN (3u)

/** \brief  Mask for Ifx_CIF_EP_IC_RECENTER_Bits.RECENTER */
#define IFX_CIF_EP_IC_RECENTER_RECENTER_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_EP_IC_RECENTER_Bits.RECENTER */
#define IFX_CIF_EP_IC_RECENTER_RECENTER_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_V_OFFS_SHD_Bits.V_OFFS_SHD */
#define IFX_CIF_EP_IC_V_OFFS_SHD_V_OFFS_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_V_OFFS_SHD_Bits.V_OFFS_SHD */
#define IFX_CIF_EP_IC_V_OFFS_SHD_V_OFFS_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_V_OFFS_SHD_Bits.V_OFFS_SHD */
#define IFX_CIF_EP_IC_V_OFFS_SHD_V_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_V_OFFS_Bits.V_OFFS */
#define IFX_CIF_EP_IC_V_OFFS_V_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_V_OFFS_Bits.V_OFFS */
#define IFX_CIF_EP_IC_V_OFFS_V_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_V_OFFS_Bits.V_OFFS */
#define IFX_CIF_EP_IC_V_OFFS_V_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_V_SIZE_SHD_Bits.V_SIZE_SHD */
#define IFX_CIF_EP_IC_V_SIZE_SHD_V_SIZE_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_V_SIZE_SHD_Bits.V_SIZE_SHD */
#define IFX_CIF_EP_IC_V_SIZE_SHD_V_SIZE_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_V_SIZE_SHD_Bits.V_SIZE_SHD */
#define IFX_CIF_EP_IC_V_SIZE_SHD_V_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_EP_IC_V_SIZE_Bits.V_SIZE */
#define IFX_CIF_EP_IC_V_SIZE_V_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_EP_IC_V_SIZE_Bits.V_SIZE */
#define IFX_CIF_EP_IC_V_SIZE_V_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_EP_IC_V_SIZE_Bits.V_SIZE */
#define IFX_CIF_EP_IC_V_SIZE_V_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_DEBUG_PATH_CLK_EN */
#define IFX_CIF_ICCL_CIF_DEBUG_PATH_CLK_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_DEBUG_PATH_CLK_EN */
#define IFX_CIF_ICCL_CIF_DEBUG_PATH_CLK_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_DEBUG_PATH_CLK_EN */
#define IFX_CIF_ICCL_CIF_DEBUG_PATH_CLK_EN_OFF (19u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_EXTRA_PATHS_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_EXTRA_PATHS_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_EXTRA_PATHS_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_EXTRA_PATHS_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_EXTRA_PATHS_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_EXTRA_PATHS_CLK_ENABLE_OFF (18u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_ISP_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_ISP_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_ISP_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_ISP_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_ISP_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_ISP_CLK_ENABLE_OFF (0u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_JPEG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_JPEG_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_JPEG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_JPEG_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_JPEG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_JPEG_CLK_ENABLE_OFF (5u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_LIN_DSCALER_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_LIN_DSCALER_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_LIN_DSCALER_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_LIN_DSCALER_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_LIN_DSCALER_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_LIN_DSCALER_CLK_ENABLE_OFF (17u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_MI_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_MI_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_MI_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_MI_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_MI_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_MI_CLK_ENABLE_OFF (6u)

/** \brief  Length for Ifx_CIF_ICCL_Bits.CIF_WATCHDOG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_WATCHDOG_CLK_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ICCL_Bits.CIF_WATCHDOG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_WATCHDOG_CLK_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ICCL_Bits.CIF_WATCHDOG_CLK_ENABLE */
#define IFX_CIF_ICCL_CIF_WATCHDOG_CLK_ENABLE_OFF (16u)

/** \brief  Length for Ifx_CIF_ID_Bits.MODNUMBER */
#define IFX_CIF_ID_MODNUMBER_LEN (16u)

/** \brief  Mask for Ifx_CIF_ID_Bits.MODNUMBER */
#define IFX_CIF_ID_MODNUMBER_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_ID_Bits.MODNUMBER */
#define IFX_CIF_ID_MODNUMBER_OFF (16u)

/** \brief  Length for Ifx_CIF_ID_Bits.MODREV */
#define IFX_CIF_ID_MODREV_LEN (8u)

/** \brief  Mask for Ifx_CIF_ID_Bits.MODREV */
#define IFX_CIF_ID_MODREV_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_ID_Bits.MODREV */
#define IFX_CIF_ID_MODREV_OFF (0u)

/** \brief  Length for Ifx_CIF_ID_Bits.MODTYPE */
#define IFX_CIF_ID_MODTYPE_LEN (8u)

/** \brief  Mask for Ifx_CIF_ID_Bits.MODTYPE */
#define IFX_CIF_ID_MODTYPE_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_ID_Bits.MODTYPE */
#define IFX_CIF_ID_MODTYPE_OFF (8u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_DEBUG_PATH_RST */
#define IFX_CIF_IRCL_CIF_DEBUG_PATH_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_DEBUG_PATH_RST */
#define IFX_CIF_IRCL_CIF_DEBUG_PATH_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_DEBUG_PATH_RST */
#define IFX_CIF_IRCL_CIF_DEBUG_PATH_RST_OFF (19u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_EXTRA_PATHS_RST */
#define IFX_CIF_IRCL_CIF_EXTRA_PATHS_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_EXTRA_PATHS_RST */
#define IFX_CIF_IRCL_CIF_EXTRA_PATHS_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_EXTRA_PATHS_RST */
#define IFX_CIF_IRCL_CIF_EXTRA_PATHS_RST_OFF (18u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_GLOBAL_RST */
#define IFX_CIF_IRCL_CIF_GLOBAL_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_GLOBAL_RST */
#define IFX_CIF_IRCL_CIF_GLOBAL_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_GLOBAL_RST */
#define IFX_CIF_IRCL_CIF_GLOBAL_RST_OFF (7u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_ISP_SOFT_RST */
#define IFX_CIF_IRCL_CIF_ISP_SOFT_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_ISP_SOFT_RST */
#define IFX_CIF_IRCL_CIF_ISP_SOFT_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_ISP_SOFT_RST */
#define IFX_CIF_IRCL_CIF_ISP_SOFT_RST_OFF (0u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_JPEG_SOFT_RST */
#define IFX_CIF_IRCL_CIF_JPEG_SOFT_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_JPEG_SOFT_RST */
#define IFX_CIF_IRCL_CIF_JPEG_SOFT_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_JPEG_SOFT_RST */
#define IFX_CIF_IRCL_CIF_JPEG_SOFT_RST_OFF (5u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_LIN_DSCALER_RST */
#define IFX_CIF_IRCL_CIF_LIN_DSCALER_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_LIN_DSCALER_RST */
#define IFX_CIF_IRCL_CIF_LIN_DSCALER_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_LIN_DSCALER_RST */
#define IFX_CIF_IRCL_CIF_LIN_DSCALER_RST_OFF (17u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_MI_SOFT_RST */
#define IFX_CIF_IRCL_CIF_MI_SOFT_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_MI_SOFT_RST */
#define IFX_CIF_IRCL_CIF_MI_SOFT_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_MI_SOFT_RST */
#define IFX_CIF_IRCL_CIF_MI_SOFT_RST_OFF (6u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_WATCHDOG_RST */
#define IFX_CIF_IRCL_CIF_WATCHDOG_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_WATCHDOG_RST */
#define IFX_CIF_IRCL_CIF_WATCHDOG_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_WATCHDOG_RST */
#define IFX_CIF_IRCL_CIF_WATCHDOG_RST_OFF (16u)

/** \brief  Length for Ifx_CIF_IRCL_Bits.CIF_YCS_SOFT_RST */
#define IFX_CIF_IRCL_CIF_YCS_SOFT_RST_LEN (1u)

/** \brief  Mask for Ifx_CIF_IRCL_Bits.CIF_YCS_SOFT_RST */
#define IFX_CIF_IRCL_CIF_YCS_SOFT_RST_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_IRCL_Bits.CIF_YCS_SOFT_RST */
#define IFX_CIF_IRCL_CIF_YCS_SOFT_RST_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_H_OFFS_Bits.ACQ_H_OFFS */
#define IFX_CIF_ISP_ACQ_H_OFFS_ACQ_H_OFFS_LEN (13u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_H_OFFS_Bits.ACQ_H_OFFS */
#define IFX_CIF_ISP_ACQ_H_OFFS_ACQ_H_OFFS_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_H_OFFS_Bits.ACQ_H_OFFS */
#define IFX_CIF_ISP_ACQ_H_OFFS_ACQ_H_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_H_SIZE_Bits.ACQ_H_SIZE */
#define IFX_CIF_ISP_ACQ_H_SIZE_ACQ_H_SIZE_LEN (13u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_H_SIZE_Bits.ACQ_H_SIZE */
#define IFX_CIF_ISP_ACQ_H_SIZE_ACQ_H_SIZE_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_H_SIZE_Bits.ACQ_H_SIZE */
#define IFX_CIF_ISP_ACQ_H_SIZE_ACQ_H_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_NR_FRAMES_Bits.ACQ_NR_FRAMES */
#define IFX_CIF_ISP_ACQ_NR_FRAMES_ACQ_NR_FRAMES_LEN (10u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_NR_FRAMES_Bits.ACQ_NR_FRAMES */
#define IFX_CIF_ISP_ACQ_NR_FRAMES_ACQ_NR_FRAMES_MSK (0x3ffu)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_NR_FRAMES_Bits.ACQ_NR_FRAMES */
#define IFX_CIF_ISP_ACQ_NR_FRAMES_ACQ_NR_FRAMES_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.CCIR_SEQ */
#define IFX_CIF_ISP_ACQ_PROP_CCIR_SEQ_LEN (2u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.CCIR_SEQ */
#define IFX_CIF_ISP_ACQ_PROP_CCIR_SEQ_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.CCIR_SEQ */
#define IFX_CIF_ISP_ACQ_PROP_CCIR_SEQ_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_INVERT */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_INVERT_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_INVERT */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_INVERT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_INVERT */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_INVERT_OFF (11u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_SELECTION_LEN (2u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_SELECTION_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.FIELD_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_FIELD_SELECTION_OFF (9u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.HSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_HSYNC_POL_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.HSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_HSYNC_POL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.HSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_HSYNC_POL_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_LEN (4u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_MSK (0xfu)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION_NO_APP */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_NO_APP_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION_NO_APP */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_NO_APP_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION_NO_APP */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_NO_APP_OFF (20u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.INPUT_SELECTION */
#define IFX_CIF_ISP_ACQ_PROP_INPUT_SELECTION_OFF (12u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.SAMPLE_EDGE */
#define IFX_CIF_ISP_ACQ_PROP_SAMPLE_EDGE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.SAMPLE_EDGE */
#define IFX_CIF_ISP_ACQ_PROP_SAMPLE_EDGE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.SAMPLE_EDGE */
#define IFX_CIF_ISP_ACQ_PROP_SAMPLE_EDGE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_PROP_Bits.VSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_VSYNC_POL_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_PROP_Bits.VSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_VSYNC_POL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_PROP_Bits.VSYNC_POL */
#define IFX_CIF_ISP_ACQ_PROP_VSYNC_POL_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_V_OFFS_Bits.ACQ_V_OFFS */
#define IFX_CIF_ISP_ACQ_V_OFFS_ACQ_V_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_V_OFFS_Bits.ACQ_V_OFFS */
#define IFX_CIF_ISP_ACQ_V_OFFS_ACQ_V_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_V_OFFS_Bits.ACQ_V_OFFS */
#define IFX_CIF_ISP_ACQ_V_OFFS_ACQ_V_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ACQ_V_SIZE_Bits.ACQ_V_SIZE */
#define IFX_CIF_ISP_ACQ_V_SIZE_ACQ_V_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_ACQ_V_SIZE_Bits.ACQ_V_SIZE */
#define IFX_CIF_ISP_ACQ_V_SIZE_ACQ_V_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_ACQ_V_SIZE_Bits.ACQ_V_SIZE */
#define IFX_CIF_ISP_ACQ_V_SIZE_ACQ_V_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_CFG_UPD_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_CFG_UPD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_CFG_UPD_OFF (9u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_C_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_C_RANGE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_C_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_C_RANGE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_C_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_C_RANGE_OFF (14u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_Y_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_Y_RANGE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_Y_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_Y_RANGE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_CSM_Y_RANGE */
#define IFX_CIF_ISP_CTRL_ISP_CSM_Y_RANGE_OFF (13u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_ENABLE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_GEN_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_GEN_CFG_UPD_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_GEN_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_GEN_CFG_UPD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_GEN_CFG_UPD */
#define IFX_CIF_ISP_CTRL_ISP_GEN_CFG_UPD_OFF (10u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_INFORM_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_INFORM_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_INFORM_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_INFORM_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_INFORM_ENABLE */
#define IFX_CIF_ISP_CTRL_ISP_INFORM_ENABLE_OFF (4u)

/** \brief  Length for Ifx_CIF_ISP_CTRL_Bits.ISP_MODE */
#define IFX_CIF_ISP_CTRL_ISP_MODE_LEN (3u)

/** \brief  Mask for Ifx_CIF_ISP_CTRL_Bits.ISP_MODE */
#define IFX_CIF_ISP_CTRL_ISP_MODE_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_ISP_CTRL_Bits.ISP_MODE */
#define IFX_CIF_ISP_CTRL_ISP_MODE_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ERR_CLR_Bits.INFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_INFORM_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_CLR_Bits.INFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_INFORM_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_CLR_Bits.INFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_INFORM_SIZE_ERR_CLR_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ERR_CLR_Bits.IS_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_IS_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_CLR_Bits.IS_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_IS_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_CLR_Bits.IS_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_IS_SIZE_ERR_CLR_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ERR_CLR_Bits.OUTFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_OUTFORM_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_CLR_Bits.OUTFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_OUTFORM_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_CLR_Bits.OUTFORM_SIZE_ERR_CLR */
#define IFX_CIF_ISP_ERR_CLR_OUTFORM_SIZE_ERR_CLR_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_ERR_Bits.INFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_INFORM_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_Bits.INFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_INFORM_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_Bits.INFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_INFORM_SIZE_ERR_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ERR_Bits.IS_SIZE_ERR */
#define IFX_CIF_ISP_ERR_IS_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_Bits.IS_SIZE_ERR */
#define IFX_CIF_ISP_ERR_IS_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_Bits.IS_SIZE_ERR */
#define IFX_CIF_ISP_ERR_IS_SIZE_ERR_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ERR_Bits.OUTFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_OUTFORM_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ERR_Bits.OUTFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_OUTFORM_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ERR_Bits.OUTFORM_SIZE_ERR */
#define IFX_CIF_ISP_ERR_OUTFORM_SIZE_ERR_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.INFORM_FIELD */
#define IFX_CIF_ISP_FLAGS_SHD_INFORM_FIELD_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.INFORM_FIELD */
#define IFX_CIF_ISP_FLAGS_SHD_INFORM_FIELD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.INFORM_FIELD */
#define IFX_CIF_ISP_FLAGS_SHD_INFORM_FIELD_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_ENABLE_SHD_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_ENABLE_SHD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_ENABLE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_INFORM_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_INFORM_ENABLE_SHD_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_INFORM_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_INFORM_ENABLE_SHD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.ISP_INFORM_ENABLE_SHD */
#define IFX_CIF_ISP_FLAGS_SHD_ISP_INFORM_ENABLE_SHD_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_DATA */
#define IFX_CIF_ISP_FLAGS_SHD_S_DATA_LEN (16u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_DATA */
#define IFX_CIF_ISP_FLAGS_SHD_S_DATA_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_DATA */
#define IFX_CIF_ISP_FLAGS_SHD_S_DATA_OFF (14u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_HSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_HSYNC_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_HSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_HSYNC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_HSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_HSYNC_OFF (31u)

/** \brief  Length for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_VSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_VSYNC_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_VSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_VSYNC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_FLAGS_SHD_Bits.S_VSYNC */
#define IFX_CIF_ISP_FLAGS_SHD_S_VSYNC_OFF (30u)

/** \brief  Length for Ifx_CIF_ISP_FRAME_COUNT_Bits.FRAME_COUNTER */
#define IFX_CIF_ISP_FRAME_COUNT_FRAME_COUNTER_LEN (10u)

/** \brief  Mask for Ifx_CIF_ISP_FRAME_COUNT_Bits.FRAME_COUNTER */
#define IFX_CIF_ISP_FRAME_COUNT_FRAME_COUNTER_MSK (0x3ffu)

/** \brief  Offset for Ifx_CIF_ISP_FRAME_COUNT_Bits.FRAME_COUNTER */
#define IFX_CIF_ISP_FRAME_COUNT_FRAME_COUNTER_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_DATA_LOSS */
#define IFX_CIF_ISP_ICR_ICR_DATA_LOSS_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_DATA_LOSS */
#define IFX_CIF_ISP_ICR_ICR_DATA_LOSS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_DATA_LOSS */
#define IFX_CIF_ISP_ICR_ICR_DATA_LOSS_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME_IN */
#define IFX_CIF_ISP_ICR_ICR_FRAME_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME_IN */
#define IFX_CIF_ISP_ICR_ICR_FRAME_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME_IN */
#define IFX_CIF_ISP_ICR_ICR_FRAME_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME */
#define IFX_CIF_ISP_ICR_ICR_FRAME_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME */
#define IFX_CIF_ISP_ICR_ICR_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_FRAME */
#define IFX_CIF_ISP_ICR_ICR_FRAME_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_H_START */
#define IFX_CIF_ISP_ICR_ICR_H_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_H_START */
#define IFX_CIF_ISP_ICR_ICR_H_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_H_START */
#define IFX_CIF_ISP_ICR_ICR_H_START_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_ISP_OFF */
#define IFX_CIF_ISP_ICR_ICR_ISP_OFF_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_ISP_OFF */
#define IFX_CIF_ISP_ICR_ICR_ISP_OFF_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_ISP_OFF */
#define IFX_CIF_ISP_ICR_ICR_ISP_OFF_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ICR_ICR_PIC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ICR_ICR_PIC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ICR_ICR_PIC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_V_START */
#define IFX_CIF_ISP_ICR_ICR_V_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_V_START */
#define IFX_CIF_ISP_ICR_ICR_V_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_V_START */
#define IFX_CIF_ISP_ICR_ICR_V_START_OFF (6u)

/** \brief  Length for Ifx_CIF_ISP_ICR_Bits.ICR_WD_TRIG */
#define IFX_CIF_ISP_ICR_ICR_WD_TRIG_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ICR_Bits.ICR_WD_TRIG */
#define IFX_CIF_ISP_ICR_ICR_WD_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ICR_Bits.ICR_WD_TRIG */
#define IFX_CIF_ISP_ICR_ICR_WD_TRIG_OFF (19u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_DATA_LOSS */
#define IFX_CIF_ISP_IMSC_IMSC_DATA_LOSS_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_DATA_LOSS */
#define IFX_CIF_ISP_IMSC_IMSC_DATA_LOSS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_DATA_LOSS */
#define IFX_CIF_ISP_IMSC_IMSC_DATA_LOSS_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME_IN */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME_IN */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME_IN */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_FRAME */
#define IFX_CIF_ISP_IMSC_IMSC_FRAME_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_H_START */
#define IFX_CIF_ISP_IMSC_IMSC_H_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_H_START */
#define IFX_CIF_ISP_IMSC_IMSC_H_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_H_START */
#define IFX_CIF_ISP_IMSC_IMSC_H_START_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_ISP_OFF */
#define IFX_CIF_ISP_IMSC_IMSC_ISP_OFF_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_ISP_OFF */
#define IFX_CIF_ISP_IMSC_IMSC_ISP_OFF_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_ISP_OFF */
#define IFX_CIF_ISP_IMSC_IMSC_ISP_OFF_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_PIC_SIZE_ERR */
#define IFX_CIF_ISP_IMSC_IMSC_PIC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_PIC_SIZE_ERR */
#define IFX_CIF_ISP_IMSC_IMSC_PIC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_PIC_SIZE_ERR */
#define IFX_CIF_ISP_IMSC_IMSC_PIC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_V_START */
#define IFX_CIF_ISP_IMSC_IMSC_V_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_V_START */
#define IFX_CIF_ISP_IMSC_IMSC_V_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_V_START */
#define IFX_CIF_ISP_IMSC_IMSC_V_START_OFF (6u)

/** \brief  Length for Ifx_CIF_ISP_IMSC_Bits.IMSC_WD_TRIG */
#define IFX_CIF_ISP_IMSC_IMSC_WD_TRIG_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_IMSC_Bits.IMSC_WD_TRIG */
#define IFX_CIF_ISP_IMSC_IMSC_WD_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_IMSC_Bits.IMSC_WD_TRIG */
#define IFX_CIF_ISP_IMSC_IMSC_WD_TRIG_OFF (19u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_DATA_LOSS */
#define IFX_CIF_ISP_ISR_ISR_DATA_LOSS_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_DATA_LOSS */
#define IFX_CIF_ISP_ISR_ISR_DATA_LOSS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_DATA_LOSS */
#define IFX_CIF_ISP_ISR_ISR_DATA_LOSS_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME_IN */
#define IFX_CIF_ISP_ISR_ISR_FRAME_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME_IN */
#define IFX_CIF_ISP_ISR_ISR_FRAME_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME_IN */
#define IFX_CIF_ISP_ISR_ISR_FRAME_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME */
#define IFX_CIF_ISP_ISR_ISR_FRAME_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME */
#define IFX_CIF_ISP_ISR_ISR_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_FRAME */
#define IFX_CIF_ISP_ISR_ISR_FRAME_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_H_START */
#define IFX_CIF_ISP_ISR_ISR_H_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_H_START */
#define IFX_CIF_ISP_ISR_ISR_H_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_H_START */
#define IFX_CIF_ISP_ISR_ISR_H_START_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_ISP_OFF */
#define IFX_CIF_ISP_ISR_ISR_ISP_OFF_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_ISP_OFF */
#define IFX_CIF_ISP_ISR_ISR_ISP_OFF_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_ISP_OFF */
#define IFX_CIF_ISP_ISR_ISR_ISP_OFF_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ISR_ISR_PIC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ISR_ISR_PIC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_PIC_SIZE_ERR */
#define IFX_CIF_ISP_ISR_ISR_PIC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_V_START */
#define IFX_CIF_ISP_ISR_ISR_V_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_V_START */
#define IFX_CIF_ISP_ISR_ISR_V_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_V_START */
#define IFX_CIF_ISP_ISR_ISR_V_START_OFF (6u)

/** \brief  Length for Ifx_CIF_ISP_ISR_Bits.ISR_WD_TRIG */
#define IFX_CIF_ISP_ISR_ISR_WD_TRIG_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_ISR_Bits.ISR_WD_TRIG */
#define IFX_CIF_ISP_ISR_ISR_WD_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_ISR_Bits.ISR_WD_TRIG */
#define IFX_CIF_ISP_ISR_ISR_WD_TRIG_OFF (19u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_DATA_LOSS */
#define IFX_CIF_ISP_MIS_MIS_DATA_LOSS_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_DATA_LOSS */
#define IFX_CIF_ISP_MIS_MIS_DATA_LOSS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_DATA_LOSS */
#define IFX_CIF_ISP_MIS_MIS_DATA_LOSS_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME_IN */
#define IFX_CIF_ISP_MIS_MIS_FRAME_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME_IN */
#define IFX_CIF_ISP_MIS_MIS_FRAME_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME_IN */
#define IFX_CIF_ISP_MIS_MIS_FRAME_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME */
#define IFX_CIF_ISP_MIS_MIS_FRAME_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME */
#define IFX_CIF_ISP_MIS_MIS_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_FRAME */
#define IFX_CIF_ISP_MIS_MIS_FRAME_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_H_START */
#define IFX_CIF_ISP_MIS_MIS_H_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_H_START */
#define IFX_CIF_ISP_MIS_MIS_H_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_H_START */
#define IFX_CIF_ISP_MIS_MIS_H_START_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_ISP_OFF */
#define IFX_CIF_ISP_MIS_MIS_ISP_OFF_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_ISP_OFF */
#define IFX_CIF_ISP_MIS_MIS_ISP_OFF_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_ISP_OFF */
#define IFX_CIF_ISP_MIS_MIS_ISP_OFF_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_MIS_MIS_PIC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_MIS_MIS_PIC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_MIS_MIS_PIC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_V_START */
#define IFX_CIF_ISP_MIS_MIS_V_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_V_START */
#define IFX_CIF_ISP_MIS_MIS_V_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_V_START */
#define IFX_CIF_ISP_MIS_MIS_V_START_OFF (6u)

/** \brief  Length for Ifx_CIF_ISP_MIS_Bits.MIS_WD_TRIG */
#define IFX_CIF_ISP_MIS_MIS_WD_TRIG_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_MIS_Bits.MIS_WD_TRIG */
#define IFX_CIF_ISP_MIS_MIS_WD_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_MIS_Bits.MIS_WD_TRIG */
#define IFX_CIF_ISP_MIS_MIS_WD_TRIG_OFF (19u)

/** \brief  Length for Ifx_CIF_ISP_OUT_H_OFFS_Bits.ISP_OUT_H_OFFS */
#define IFX_CIF_ISP_OUT_H_OFFS_ISP_OUT_H_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_H_OFFS_Bits.ISP_OUT_H_OFFS */
#define IFX_CIF_ISP_OUT_H_OFFS_ISP_OUT_H_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_H_OFFS_Bits.ISP_OUT_H_OFFS */
#define IFX_CIF_ISP_OUT_H_OFFS_ISP_OUT_H_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_H_OFFS_SHD_Bits.ISP_OUT_H_OFFS_SHD */
#define IFX_CIF_ISP_OUT_H_OFFS_SHD_ISP_OUT_H_OFFS_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_H_OFFS_SHD_Bits.ISP_OUT_H_OFFS_SHD */
#define IFX_CIF_ISP_OUT_H_OFFS_SHD_ISP_OUT_H_OFFS_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_H_OFFS_SHD_Bits.ISP_OUT_H_OFFS_SHD */
#define IFX_CIF_ISP_OUT_H_OFFS_SHD_ISP_OUT_H_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_H_SIZE_Bits.ISP_OUT_H_SIZE */
#define IFX_CIF_ISP_OUT_H_SIZE_ISP_OUT_H_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_H_SIZE_Bits.ISP_OUT_H_SIZE */
#define IFX_CIF_ISP_OUT_H_SIZE_ISP_OUT_H_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_H_SIZE_Bits.ISP_OUT_H_SIZE */
#define IFX_CIF_ISP_OUT_H_SIZE_ISP_OUT_H_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_H_SIZE_SHD_Bits.ISP_OUT_H_SIZE_SHD */
#define IFX_CIF_ISP_OUT_H_SIZE_SHD_ISP_OUT_H_SIZE_SHD_LEN (13u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_H_SIZE_SHD_Bits.ISP_OUT_H_SIZE_SHD */
#define IFX_CIF_ISP_OUT_H_SIZE_SHD_ISP_OUT_H_SIZE_SHD_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_H_SIZE_SHD_Bits.ISP_OUT_H_SIZE_SHD */
#define IFX_CIF_ISP_OUT_H_SIZE_SHD_ISP_OUT_H_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_V_OFFS_Bits.ISP_OUT_V_OFFS */
#define IFX_CIF_ISP_OUT_V_OFFS_ISP_OUT_V_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_V_OFFS_Bits.ISP_OUT_V_OFFS */
#define IFX_CIF_ISP_OUT_V_OFFS_ISP_OUT_V_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_V_OFFS_Bits.ISP_OUT_V_OFFS */
#define IFX_CIF_ISP_OUT_V_OFFS_ISP_OUT_V_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_V_OFFS_SHD_Bits.ISP_OUT_V_OFFS_SHD */
#define IFX_CIF_ISP_OUT_V_OFFS_SHD_ISP_OUT_V_OFFS_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_V_OFFS_SHD_Bits.ISP_OUT_V_OFFS_SHD */
#define IFX_CIF_ISP_OUT_V_OFFS_SHD_ISP_OUT_V_OFFS_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_V_OFFS_SHD_Bits.ISP_OUT_V_OFFS_SHD */
#define IFX_CIF_ISP_OUT_V_OFFS_SHD_ISP_OUT_V_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_V_SIZE_Bits.ISP_OUT_V_SIZE */
#define IFX_CIF_ISP_OUT_V_SIZE_ISP_OUT_V_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_V_SIZE_Bits.ISP_OUT_V_SIZE */
#define IFX_CIF_ISP_OUT_V_SIZE_ISP_OUT_V_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_V_SIZE_Bits.ISP_OUT_V_SIZE */
#define IFX_CIF_ISP_OUT_V_SIZE_ISP_OUT_V_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_OUT_V_SIZE_SHD_Bits.ISP_OUT_V_SIZE_SHD */
#define IFX_CIF_ISP_OUT_V_SIZE_SHD_ISP_OUT_V_SIZE_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISP_OUT_V_SIZE_SHD_Bits.ISP_OUT_V_SIZE_SHD */
#define IFX_CIF_ISP_OUT_V_SIZE_SHD_ISP_OUT_V_SIZE_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISP_OUT_V_SIZE_SHD_Bits.ISP_OUT_V_SIZE_SHD */
#define IFX_CIF_ISP_OUT_V_SIZE_SHD_ISP_OUT_V_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_DATA_LOSS */
#define IFX_CIF_ISP_RIS_RIS_DATA_LOSS_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_DATA_LOSS */
#define IFX_CIF_ISP_RIS_RIS_DATA_LOSS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_DATA_LOSS */
#define IFX_CIF_ISP_RIS_RIS_DATA_LOSS_OFF (2u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME_IN */
#define IFX_CIF_ISP_RIS_RIS_FRAME_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME_IN */
#define IFX_CIF_ISP_RIS_RIS_FRAME_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME_IN */
#define IFX_CIF_ISP_RIS_RIS_FRAME_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME */
#define IFX_CIF_ISP_RIS_RIS_FRAME_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME */
#define IFX_CIF_ISP_RIS_RIS_FRAME_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_FRAME */
#define IFX_CIF_ISP_RIS_RIS_FRAME_OFF (1u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_H_START */
#define IFX_CIF_ISP_RIS_RIS_H_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_H_START */
#define IFX_CIF_ISP_RIS_RIS_H_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_H_START */
#define IFX_CIF_ISP_RIS_RIS_H_START_OFF (7u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_ISP_OFF */
#define IFX_CIF_ISP_RIS_RIS_ISP_OFF_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_ISP_OFF */
#define IFX_CIF_ISP_RIS_RIS_ISP_OFF_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_ISP_OFF */
#define IFX_CIF_ISP_RIS_RIS_ISP_OFF_OFF (0u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_RIS_RIS_PIC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_RIS_RIS_PIC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_PIC_SIZE_ERR */
#define IFX_CIF_ISP_RIS_RIS_PIC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_V_START */
#define IFX_CIF_ISP_RIS_RIS_V_START_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_V_START */
#define IFX_CIF_ISP_RIS_RIS_V_START_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_V_START */
#define IFX_CIF_ISP_RIS_RIS_V_START_OFF (6u)

/** \brief  Length for Ifx_CIF_ISP_RIS_Bits.RIS_WD_TRIG */
#define IFX_CIF_ISP_RIS_RIS_WD_TRIG_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISP_RIS_Bits.RIS_WD_TRIG */
#define IFX_CIF_ISP_RIS_RIS_WD_TRIG_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISP_RIS_Bits.RIS_WD_TRIG */
#define IFX_CIF_ISP_RIS_RIS_WD_TRIG_OFF (19u)

/** \brief  Length for Ifx_CIF_ISPIS_CTRL_Bits.IS_EN */
#define IFX_CIF_ISPIS_CTRL_IS_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_ISPIS_CTRL_Bits.IS_EN */
#define IFX_CIF_ISPIS_CTRL_IS_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_ISPIS_CTRL_Bits.IS_EN */
#define IFX_CIF_ISPIS_CTRL_IS_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_DISPLACE_Bits.DX */
#define IFX_CIF_ISPIS_DISPLACE_DX_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_DISPLACE_Bits.DX */
#define IFX_CIF_ISPIS_DISPLACE_DX_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_DISPLACE_Bits.DX */
#define IFX_CIF_ISPIS_DISPLACE_DX_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_DISPLACE_Bits.DY */
#define IFX_CIF_ISPIS_DISPLACE_DY_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_DISPLACE_Bits.DY */
#define IFX_CIF_ISPIS_DISPLACE_DY_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_DISPLACE_Bits.DY */
#define IFX_CIF_ISPIS_DISPLACE_DY_OFF (16u)

/** \brief  Length for Ifx_CIF_ISPIS_H_OFFS_Bits.IS_H_OFFS */
#define IFX_CIF_ISPIS_H_OFFS_IS_H_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_H_OFFS_Bits.IS_H_OFFS */
#define IFX_CIF_ISPIS_H_OFFS_IS_H_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_H_OFFS_Bits.IS_H_OFFS */
#define IFX_CIF_ISPIS_H_OFFS_IS_H_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_H_OFFS_SHD_Bits.IS_H_OFFS_SHD */
#define IFX_CIF_ISPIS_H_OFFS_SHD_IS_H_OFFS_SHD_LEN (13u)

/** \brief  Mask for Ifx_CIF_ISPIS_H_OFFS_SHD_Bits.IS_H_OFFS_SHD */
#define IFX_CIF_ISPIS_H_OFFS_SHD_IS_H_OFFS_SHD_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_ISPIS_H_OFFS_SHD_Bits.IS_H_OFFS_SHD */
#define IFX_CIF_ISPIS_H_OFFS_SHD_IS_H_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_H_SIZE_Bits.IS_H_SIZE */
#define IFX_CIF_ISPIS_H_SIZE_IS_H_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_H_SIZE_Bits.IS_H_SIZE */
#define IFX_CIF_ISPIS_H_SIZE_IS_H_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_H_SIZE_Bits.IS_H_SIZE */
#define IFX_CIF_ISPIS_H_SIZE_IS_H_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_H_SIZE_SHD_Bits.ISP_H_SIZE_SHD */
#define IFX_CIF_ISPIS_H_SIZE_SHD_ISP_H_SIZE_SHD_LEN (13u)

/** \brief  Mask for Ifx_CIF_ISPIS_H_SIZE_SHD_Bits.ISP_H_SIZE_SHD */
#define IFX_CIF_ISPIS_H_SIZE_SHD_ISP_H_SIZE_SHD_MSK (0x1fffu)

/** \brief  Offset for Ifx_CIF_ISPIS_H_SIZE_SHD_Bits.ISP_H_SIZE_SHD */
#define IFX_CIF_ISPIS_H_SIZE_SHD_ISP_H_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_MAX_DX_Bits.IS_MAX_DX */
#define IFX_CIF_ISPIS_MAX_DX_IS_MAX_DX_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_MAX_DX_Bits.IS_MAX_DX */
#define IFX_CIF_ISPIS_MAX_DX_IS_MAX_DX_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_MAX_DX_Bits.IS_MAX_DX */
#define IFX_CIF_ISPIS_MAX_DX_IS_MAX_DX_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_MAX_DY_Bits.IS_MAX_DY */
#define IFX_CIF_ISPIS_MAX_DY_IS_MAX_DY_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_MAX_DY_Bits.IS_MAX_DY */
#define IFX_CIF_ISPIS_MAX_DY_IS_MAX_DY_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_MAX_DY_Bits.IS_MAX_DY */
#define IFX_CIF_ISPIS_MAX_DY_IS_MAX_DY_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_RECENTER_Bits.RECENTER */
#define IFX_CIF_ISPIS_RECENTER_RECENTER_LEN (3u)

/** \brief  Mask for Ifx_CIF_ISPIS_RECENTER_Bits.RECENTER */
#define IFX_CIF_ISPIS_RECENTER_RECENTER_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_ISPIS_RECENTER_Bits.RECENTER */
#define IFX_CIF_ISPIS_RECENTER_RECENTER_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_V_OFFS_Bits.IS_V_OFFS */
#define IFX_CIF_ISPIS_V_OFFS_IS_V_OFFS_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_V_OFFS_Bits.IS_V_OFFS */
#define IFX_CIF_ISPIS_V_OFFS_IS_V_OFFS_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_V_OFFS_Bits.IS_V_OFFS */
#define IFX_CIF_ISPIS_V_OFFS_IS_V_OFFS_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_V_OFFS_SHD_Bits.IS_V_OFFS_SHD */
#define IFX_CIF_ISPIS_V_OFFS_SHD_IS_V_OFFS_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_V_OFFS_SHD_Bits.IS_V_OFFS_SHD */
#define IFX_CIF_ISPIS_V_OFFS_SHD_IS_V_OFFS_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_V_OFFS_SHD_Bits.IS_V_OFFS_SHD */
#define IFX_CIF_ISPIS_V_OFFS_SHD_IS_V_OFFS_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_V_SIZE_Bits.IS_V_SIZE */
#define IFX_CIF_ISPIS_V_SIZE_IS_V_SIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_V_SIZE_Bits.IS_V_SIZE */
#define IFX_CIF_ISPIS_V_SIZE_IS_V_SIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_V_SIZE_Bits.IS_V_SIZE */
#define IFX_CIF_ISPIS_V_SIZE_IS_V_SIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_ISPIS_V_SIZE_SHD_Bits.ISP_V_SIZE_SHD */
#define IFX_CIF_ISPIS_V_SIZE_SHD_ISP_V_SIZE_SHD_LEN (12u)

/** \brief  Mask for Ifx_CIF_ISPIS_V_SIZE_SHD_Bits.ISP_V_SIZE_SHD */
#define IFX_CIF_ISPIS_V_SIZE_SHD_ISP_V_SIZE_SHD_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_ISPIS_V_SIZE_SHD_Bits.ISP_V_SIZE_SHD */
#define IFX_CIF_ISPIS_V_SIZE_SHD_ISP_V_SIZE_SHD_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_AC_TABLE_SELECT_Bits.AC_TABLE_SELECT */
#define IFX_CIF_JPE_AC_TABLE_SELECT_AC_TABLE_SELECT_LEN (3u)

/** \brief  Mask for Ifx_CIF_JPE_AC_TABLE_SELECT_Bits.AC_TABLE_SELECT */
#define IFX_CIF_JPE_AC_TABLE_SELECT_AC_TABLE_SELECT_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_JPE_AC_TABLE_SELECT_Bits.AC_TABLE_SELECT */
#define IFX_CIF_JPE_AC_TABLE_SELECT_AC_TABLE_SELECT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_CBCR_SCALE_EN_Bits.CBCR_SCALE_EN */
#define IFX_CIF_JPE_CBCR_SCALE_EN_CBCR_SCALE_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_CBCR_SCALE_EN_Bits.CBCR_SCALE_EN */
#define IFX_CIF_JPE_CBCR_SCALE_EN_CBCR_SCALE_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_CBCR_SCALE_EN_Bits.CBCR_SCALE_EN */
#define IFX_CIF_JPE_CBCR_SCALE_EN_CBCR_SCALE_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_DC_TABLE_SELECT_Bits.DC_TABLE_SELECT */
#define IFX_CIF_JPE_DC_TABLE_SELECT_DC_TABLE_SELECT_LEN (3u)

/** \brief  Mask for Ifx_CIF_JPE_DC_TABLE_SELECT_Bits.DC_TABLE_SELECT */
#define IFX_CIF_JPE_DC_TABLE_SELECT_DC_TABLE_SELECT_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_JPE_DC_TABLE_SELECT_Bits.DC_TABLE_SELECT */
#define IFX_CIF_JPE_DC_TABLE_SELECT_DC_TABLE_SELECT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_DEBUG_Bits.DEB_BAD_TABLE_ACCESS */
#define IFX_CIF_JPE_DEBUG_DEB_BAD_TABLE_ACCESS_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_DEBUG_Bits.DEB_BAD_TABLE_ACCESS */
#define IFX_CIF_JPE_DEBUG_DEB_BAD_TABLE_ACCESS_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_DEBUG_Bits.DEB_BAD_TABLE_ACCESS */
#define IFX_CIF_JPE_DEBUG_DEB_BAD_TABLE_ACCESS_OFF (8u)

/** \brief  Length for Ifx_CIF_JPE_DEBUG_Bits.DEB_QIQ_TABLE_ACC */
#define IFX_CIF_JPE_DEBUG_DEB_QIQ_TABLE_ACC_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_DEBUG_Bits.DEB_QIQ_TABLE_ACC */
#define IFX_CIF_JPE_DEBUG_DEB_QIQ_TABLE_ACC_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_DEBUG_Bits.DEB_QIQ_TABLE_ACC */
#define IFX_CIF_JPE_DEBUG_DEB_QIQ_TABLE_ACC_OFF (2u)

/** \brief  Length for Ifx_CIF_JPE_DEBUG_Bits.DEB_R2B_MEMORY_FULL */
#define IFX_CIF_JPE_DEBUG_DEB_R2B_MEMORY_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_DEBUG_Bits.DEB_R2B_MEMORY_FULL */
#define IFX_CIF_JPE_DEBUG_DEB_R2B_MEMORY_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_DEBUG_Bits.DEB_R2B_MEMORY_FULL */
#define IFX_CIF_JPE_DEBUG_DEB_R2B_MEMORY_FULL_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_ENCODE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_ENCODE_BUSY_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_ENCODE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_ENCODE_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_ENCODE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_ENCODE_BUSY_OFF (3u)

/** \brief  Length for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_TABLE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_TABLE_BUSY_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_TABLE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_TABLE_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_DEBUG_Bits.DEB_VLC_TABLE_BUSY */
#define IFX_CIF_JPE_DEBUG_DEB_VLC_TABLE_BUSY_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_ENC_HSIZE_Bits.ENC_HSIZE */
#define IFX_CIF_JPE_ENC_HSIZE_ENC_HSIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_JPE_ENC_HSIZE_Bits.ENC_HSIZE */
#define IFX_CIF_JPE_ENC_HSIZE_ENC_HSIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_JPE_ENC_HSIZE_Bits.ENC_HSIZE */
#define IFX_CIF_JPE_ENC_HSIZE_ENC_HSIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_ENC_VSIZE_Bits.ENC_VSIZE */
#define IFX_CIF_JPE_ENC_VSIZE_ENC_VSIZE_LEN (12u)

/** \brief  Mask for Ifx_CIF_JPE_ENC_VSIZE_Bits.ENC_VSIZE */
#define IFX_CIF_JPE_ENC_VSIZE_ENC_VSIZE_MSK (0xfffu)

/** \brief  Offset for Ifx_CIF_JPE_ENC_VSIZE_Bits.ENC_VSIZE */
#define IFX_CIF_JPE_ENC_VSIZE_ENC_VSIZE_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_ENCODE_Bits.CONT_MODE */
#define IFX_CIF_JPE_ENCODE_CONT_MODE_LEN (2u)

/** \brief  Mask for Ifx_CIF_JPE_ENCODE_Bits.CONT_MODE */
#define IFX_CIF_JPE_ENCODE_CONT_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_JPE_ENCODE_Bits.CONT_MODE */
#define IFX_CIF_JPE_ENCODE_CONT_MODE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ENCODE_Bits.ENCODE */
#define IFX_CIF_JPE_ENCODE_ENCODE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ENCODE_Bits.ENCODE */
#define IFX_CIF_JPE_ENCODE_ENCODE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ENCODE_Bits.ENCODE */
#define IFX_CIF_JPE_ENCODE_ENCODE_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_ENCODE_MODE_Bits.ENCODE_MODE */
#define IFX_CIF_JPE_ENCODE_MODE_ENCODE_MODE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ENCODE_MODE_Bits.ENCODE_MODE */
#define IFX_CIF_JPE_ENCODE_MODE_ENCODE_MODE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ENCODE_MODE_Bits.ENCODE_MODE */
#define IFX_CIF_JPE_ENCODE_MODE_ENCODE_MODE_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_ENCODER_BUSY_Bits.CODEC_BUSY */
#define IFX_CIF_JPE_ENCODER_BUSY_CODEC_BUSY_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ENCODER_BUSY_Bits.CODEC_BUSY */
#define IFX_CIF_JPE_ENCODER_BUSY_CODEC_BUSY_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ENCODER_BUSY_Bits.CODEC_BUSY */
#define IFX_CIF_JPE_ENCODER_BUSY_CODEC_BUSY_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ICR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ICR_DCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ICR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ICR_DCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ICR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ICR_DCT_ERR_OFF (7u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ICR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_R2B_IMG_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ICR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_R2B_IMG_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ICR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_R2B_IMG_SIZE_ERR_OFF (9u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_SYMBOL_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_SYMBOL_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_SYMBOL_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_TABLE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_TABLE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ICR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ICR_VLC_TABLE_ERR_OFF (10u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_IMR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_IMR_DCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_IMR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_IMR_DCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_IMR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_IMR_DCT_ERR_OFF (7u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_IMR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_R2B_IMG_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_IMR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_R2B_IMG_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_IMR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_R2B_IMG_SIZE_ERR_OFF (9u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_SYMBOL_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_SYMBOL_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_SYMBOL_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_TABLE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_TABLE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_IMR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_IMR_VLC_TABLE_ERR_OFF (10u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ISR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ISR_DCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ISR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ISR_DCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ISR_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_ISR_DCT_ERR_OFF (7u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ISR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_R2B_IMG_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ISR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_R2B_IMG_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ISR_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_R2B_IMG_SIZE_ERR_OFF (9u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_SYMBOL_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_SYMBOL_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_SYMBOL_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_TABLE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_TABLE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_ISR_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_ISR_VLC_TABLE_ERR_OFF (10u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_MIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_MIS_DCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_MIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_MIS_DCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_MIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_MIS_DCT_ERR_OFF (7u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_MIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_R2B_IMG_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_MIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_R2B_IMG_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_MIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_R2B_IMG_SIZE_ERR_OFF (9u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_SYMBOL_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_SYMBOL_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_SYMBOL_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_TABLE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_TABLE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_MIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_MIS_VLC_TABLE_ERR_OFF (10u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_RIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_RIS_DCT_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_RIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_RIS_DCT_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_RIS_Bits.DCT_ERR */
#define IFX_CIF_JPE_ERROR_RIS_DCT_ERR_OFF (7u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_RIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_R2B_IMG_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_RIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_R2B_IMG_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_RIS_Bits.R2B_IMG_SIZE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_R2B_IMG_SIZE_ERR_OFF (9u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_SYMBOL_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_SYMBOL_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_SYMBOL_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_SYMBOL_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_TABLE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_TABLE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_ERROR_RIS_Bits.VLC_TABLE_ERR */
#define IFX_CIF_JPE_ERROR_RIS_VLC_TABLE_ERR_OFF (10u)

/** \brief  Length for Ifx_CIF_JPE_GEN_HEADER_Bits.GEN_HEADER */
#define IFX_CIF_JPE_GEN_HEADER_GEN_HEADER_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_GEN_HEADER_Bits.GEN_HEADER */
#define IFX_CIF_JPE_GEN_HEADER_GEN_HEADER_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_GEN_HEADER_Bits.GEN_HEADER */
#define IFX_CIF_JPE_GEN_HEADER_GEN_HEADER_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_HEADER_MODE_Bits.HEADER_MODE */
#define IFX_CIF_JPE_HEADER_MODE_HEADER_MODE_LEN (2u)

/** \brief  Mask for Ifx_CIF_JPE_HEADER_MODE_Bits.HEADER_MODE */
#define IFX_CIF_JPE_HEADER_MODE_HEADER_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_JPE_HEADER_MODE_Bits.HEADER_MODE */
#define IFX_CIF_JPE_HEADER_MODE_HEADER_MODE_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_INIT_Bits.JP_INIT */
#define IFX_CIF_JPE_INIT_JP_INIT_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_INIT_Bits.JP_INIT */
#define IFX_CIF_JPE_INIT_JP_INIT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_INIT_Bits.JP_INIT */
#define IFX_CIF_JPE_INIT_JP_INIT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_PIC_FORMAT_Bits.ENC_PIC_FORMAT */
#define IFX_CIF_JPE_PIC_FORMAT_ENC_PIC_FORMAT_LEN (3u)

/** \brief  Mask for Ifx_CIF_JPE_PIC_FORMAT_Bits.ENC_PIC_FORMAT */
#define IFX_CIF_JPE_PIC_FORMAT_ENC_PIC_FORMAT_MSK (0x7u)

/** \brief  Offset for Ifx_CIF_JPE_PIC_FORMAT_Bits.ENC_PIC_FORMAT */
#define IFX_CIF_JPE_PIC_FORMAT_ENC_PIC_FORMAT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_RESTART_INTERVAL_Bits.RESTART_INTERVAL */
#define IFX_CIF_JPE_RESTART_INTERVAL_RESTART_INTERVAL_LEN (16u)

/** \brief  Mask for Ifx_CIF_JPE_RESTART_INTERVAL_Bits.RESTART_INTERVAL */
#define IFX_CIF_JPE_RESTART_INTERVAL_RESTART_INTERVAL_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_JPE_RESTART_INTERVAL_Bits.RESTART_INTERVAL */
#define IFX_CIF_JPE_RESTART_INTERVAL_RESTART_INTERVAL_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_ICR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ICR_ENCODE_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_ICR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ICR_ENCODE_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_ICR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ICR_ENCODE_DONE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_ICR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ICR_GEN_HEADER_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_ICR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ICR_GEN_HEADER_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_ICR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ICR_GEN_HEADER_DONE_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_IMR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_IMR_ENCODE_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_IMR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_IMR_ENCODE_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_IMR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_IMR_ENCODE_DONE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_IMR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_IMR_GEN_HEADER_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_IMR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_IMR_GEN_HEADER_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_IMR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_IMR_GEN_HEADER_DONE_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_ISR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ISR_ENCODE_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_ISR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ISR_ENCODE_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_ISR_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_ISR_ENCODE_DONE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_ISR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ISR_GEN_HEADER_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_ISR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ISR_GEN_HEADER_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_ISR_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_ISR_GEN_HEADER_DONE_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_MIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_MIS_ENCODE_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_MIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_MIS_ENCODE_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_MIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_MIS_ENCODE_DONE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_MIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_MIS_GEN_HEADER_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_MIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_MIS_GEN_HEADER_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_MIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_MIS_GEN_HEADER_DONE_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_RIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_RIS_ENCODE_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_RIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_RIS_ENCODE_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_RIS_Bits.ENCODE_DONE */
#define IFX_CIF_JPE_STATUS_RIS_ENCODE_DONE_OFF (4u)

/** \brief  Length for Ifx_CIF_JPE_STATUS_RIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_RIS_GEN_HEADER_DONE_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_STATUS_RIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_RIS_GEN_HEADER_DONE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_STATUS_RIS_Bits.GEN_HEADER_DONE */
#define IFX_CIF_JPE_STATUS_RIS_GEN_HEADER_DONE_OFF (5u)

/** \brief  Length for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_H */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_H_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_H */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_H_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_H */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_H_OFF (8u)

/** \brief  Length for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_L */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_L_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_L */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_L_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TABLE_DATA_Bits.TABLE_WDATA_L */
#define IFX_CIF_JPE_TABLE_DATA_TABLE_WDATA_L_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TABLE_FLUSH_Bits.TABLE_FLUSH */
#define IFX_CIF_JPE_TABLE_FLUSH_TABLE_FLUSH_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_TABLE_FLUSH_Bits.TABLE_FLUSH */
#define IFX_CIF_JPE_TABLE_FLUSH_TABLE_FLUSH_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_TABLE_FLUSH_Bits.TABLE_FLUSH */
#define IFX_CIF_JPE_TABLE_FLUSH_TABLE_FLUSH_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TABLE_ID_Bits.TABLE_ID */
#define IFX_CIF_JPE_TABLE_ID_TABLE_ID_LEN (4u)

/** \brief  Mask for Ifx_CIF_JPE_TABLE_ID_Bits.TABLE_ID */
#define IFX_CIF_JPE_TABLE_ID_TABLE_ID_MSK (0xfu)

/** \brief  Offset for Ifx_CIF_JPE_TABLE_ID_Bits.TABLE_ID */
#define IFX_CIF_JPE_TABLE_ID_TABLE_ID_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TAC0_LEN_Bits.TAC0_LEN */
#define IFX_CIF_JPE_TAC0_LEN_TAC0_LEN_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TAC0_LEN_Bits.TAC0_LEN */
#define IFX_CIF_JPE_TAC0_LEN_TAC0_LEN_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TAC0_LEN_Bits.TAC0_LEN */
#define IFX_CIF_JPE_TAC0_LEN_TAC0_LEN_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TAC1_LEN_Bits.TAC1_LEN */
#define IFX_CIF_JPE_TAC1_LEN_TAC1_LEN_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TAC1_LEN_Bits.TAC1_LEN */
#define IFX_CIF_JPE_TAC1_LEN_TAC1_LEN_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TAC1_LEN_Bits.TAC1_LEN */
#define IFX_CIF_JPE_TAC1_LEN_TAC1_LEN_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TDC0_LEN_Bits.TDC0_LEN */
#define IFX_CIF_JPE_TDC0_LEN_TDC0_LEN_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TDC0_LEN_Bits.TDC0_LEN */
#define IFX_CIF_JPE_TDC0_LEN_TDC0_LEN_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TDC0_LEN_Bits.TDC0_LEN */
#define IFX_CIF_JPE_TDC0_LEN_TDC0_LEN_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TDC1_LEN_Bits.TDC1_LEN */
#define IFX_CIF_JPE_TDC1_LEN_TDC1_LEN_LEN (8u)

/** \brief  Mask for Ifx_CIF_JPE_TDC1_LEN_Bits.TDC1_LEN */
#define IFX_CIF_JPE_TDC1_LEN_TDC1_LEN_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_JPE_TDC1_LEN_Bits.TDC1_LEN */
#define IFX_CIF_JPE_TDC1_LEN_TDC1_LEN_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TQ_U_SELECT_Bits.TQ1_SELECT */
#define IFX_CIF_JPE_TQ_U_SELECT_TQ1_SELECT_LEN (2u)

/** \brief  Mask for Ifx_CIF_JPE_TQ_U_SELECT_Bits.TQ1_SELECT */
#define IFX_CIF_JPE_TQ_U_SELECT_TQ1_SELECT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_JPE_TQ_U_SELECT_Bits.TQ1_SELECT */
#define IFX_CIF_JPE_TQ_U_SELECT_TQ1_SELECT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TQ_V_SELECT_Bits.TQ2_SELECT */
#define IFX_CIF_JPE_TQ_V_SELECT_TQ2_SELECT_LEN (2u)

/** \brief  Mask for Ifx_CIF_JPE_TQ_V_SELECT_Bits.TQ2_SELECT */
#define IFX_CIF_JPE_TQ_V_SELECT_TQ2_SELECT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_JPE_TQ_V_SELECT_Bits.TQ2_SELECT */
#define IFX_CIF_JPE_TQ_V_SELECT_TQ2_SELECT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_TQ_Y_SELECT_Bits.TQ0_SELECT */
#define IFX_CIF_JPE_TQ_Y_SELECT_TQ0_SELECT_LEN (2u)

/** \brief  Mask for Ifx_CIF_JPE_TQ_Y_SELECT_Bits.TQ0_SELECT */
#define IFX_CIF_JPE_TQ_Y_SELECT_TQ0_SELECT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_JPE_TQ_Y_SELECT_Bits.TQ0_SELECT */
#define IFX_CIF_JPE_TQ_Y_SELECT_TQ0_SELECT_OFF (0u)

/** \brief  Length for Ifx_CIF_JPE_Y_SCALE_EN_Bits.Y_SCALE_EN */
#define IFX_CIF_JPE_Y_SCALE_EN_Y_SCALE_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_JPE_Y_SCALE_EN_Bits.Y_SCALE_EN */
#define IFX_CIF_JPE_Y_SCALE_EN_Y_SCALE_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_JPE_Y_SCALE_EN_Bits.Y_SCALE_EN */
#define IFX_CIF_JPE_Y_SCALE_EN_Y_SCALE_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_LDS_CTRL_Bits.LDS_H_EN */
#define IFX_CIF_LDS_CTRL_LDS_H_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_LDS_CTRL_Bits.LDS_H_EN */
#define IFX_CIF_LDS_CTRL_LDS_H_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_LDS_CTRL_Bits.LDS_H_EN */
#define IFX_CIF_LDS_CTRL_LDS_H_EN_OFF (1u)

/** \brief  Length for Ifx_CIF_LDS_CTRL_Bits.LDS_H_MODE */
#define IFX_CIF_LDS_CTRL_LDS_H_MODE_LEN (2u)

/** \brief  Mask for Ifx_CIF_LDS_CTRL_Bits.LDS_H_MODE */
#define IFX_CIF_LDS_CTRL_LDS_H_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_LDS_CTRL_Bits.LDS_H_MODE */
#define IFX_CIF_LDS_CTRL_LDS_H_MODE_OFF (8u)

/** \brief  Length for Ifx_CIF_LDS_CTRL_Bits.LDS_V_EN */
#define IFX_CIF_LDS_CTRL_LDS_V_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_LDS_CTRL_Bits.LDS_V_EN */
#define IFX_CIF_LDS_CTRL_LDS_V_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_LDS_CTRL_Bits.LDS_V_EN */
#define IFX_CIF_LDS_CTRL_LDS_V_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_LDS_CTRL_Bits.LDS_V_MODE */
#define IFX_CIF_LDS_CTRL_LDS_V_MODE_LEN (2u)

/** \brief  Mask for Ifx_CIF_LDS_CTRL_Bits.LDS_V_MODE */
#define IFX_CIF_LDS_CTRL_LDS_V_MODE_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_LDS_CTRL_Bits.LDS_V_MODE */
#define IFX_CIF_LDS_CTRL_LDS_V_MODE_OFF (4u)

/** \brief  Length for Ifx_CIF_LDS_FAC_Bits.LDS_H_FAC */
#define IFX_CIF_LDS_FAC_LDS_H_FAC_LEN (8u)

/** \brief  Mask for Ifx_CIF_LDS_FAC_Bits.LDS_H_FAC */
#define IFX_CIF_LDS_FAC_LDS_H_FAC_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_LDS_FAC_Bits.LDS_H_FAC */
#define IFX_CIF_LDS_FAC_LDS_H_FAC_OFF (16u)

/** \brief  Length for Ifx_CIF_LDS_FAC_Bits.LDS_V_FAC */
#define IFX_CIF_LDS_FAC_LDS_V_FAC_LEN (8u)

/** \brief  Mask for Ifx_CIF_LDS_FAC_Bits.LDS_V_FAC */
#define IFX_CIF_LDS_FAC_LDS_V_FAC_MSK (0xffu)

/** \brief  Offset for Ifx_CIF_LDS_FAC_Bits.LDS_V_FAC */
#define IFX_CIF_LDS_FAC_LDS_V_FAC_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_BYTE_CNT_Bits.BYTE_CNT */
#define IFX_CIF_MI_BYTE_CNT_BYTE_CNT_LEN (24u)

/** \brief  Mask for Ifx_CIF_MI_BYTE_CNT_Bits.BYTE_CNT */
#define IFX_CIF_MI_BYTE_CNT_BYTE_CNT_MSK (0xffffffu)

/** \brief  Offset for Ifx_CIF_MI_BYTE_CNT_Bits.BYTE_CNT */
#define IFX_CIF_MI_BYTE_CNT_BYTE_CNT_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_CHROM */
#define IFX_CIF_MI_CTRL_BURST_LEN_CHROM_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_CHROM */
#define IFX_CIF_MI_CTRL_BURST_LEN_CHROM_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_CHROM */
#define IFX_CIF_MI_CTRL_BURST_LEN_CHROM_OFF (18u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_LUM */
#define IFX_CIF_MI_CTRL_BURST_LEN_LUM_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_LUM */
#define IFX_CIF_MI_CTRL_BURST_LEN_LUM_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.BURST_LEN_LUM */
#define IFX_CIF_MI_CTRL_BURST_LEN_LUM_OFF (16u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MI_CTRL_BYTE_SWAP_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MI_CTRL_BYTE_SWAP_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MI_CTRL_BYTE_SWAP_OFF (7u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MI_CTRL_INIT_BASE_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MI_CTRL_INIT_BASE_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MI_CTRL_INIT_BASE_EN_OFF (20u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MI_CTRL_INIT_OFFSET_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MI_CTRL_INIT_OFFSET_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MI_CTRL_INIT_OFFSET_EN_OFF (21u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.JPEG_ENABLE */
#define IFX_CIF_MI_CTRL_JPEG_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.JPEG_ENABLE */
#define IFX_CIF_MI_CTRL_JPEG_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.JPEG_ENABLE */
#define IFX_CIF_MI_CTRL_JPEG_ENABLE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.MP_ENABLE */
#define IFX_CIF_MI_CTRL_MP_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.MP_ENABLE */
#define IFX_CIF_MI_CTRL_MP_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.MP_ENABLE */
#define IFX_CIF_MI_CTRL_MP_ENABLE_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.MP_WRITE_FORMAT */
#define IFX_CIF_MI_CTRL_MP_WRITE_FORMAT_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.MP_WRITE_FORMAT */
#define IFX_CIF_MI_CTRL_MP_WRITE_FORMAT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.MP_WRITE_FORMAT */
#define IFX_CIF_MI_CTRL_MP_WRITE_FORMAT_OFF (22u)

/** \brief  Length for Ifx_CIF_MI_CTRL_Bits.RAW_ENABLE */
#define IFX_CIF_MI_CTRL_RAW_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_Bits.RAW_ENABLE */
#define IFX_CIF_MI_CTRL_RAW_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_Bits.RAW_ENABLE */
#define IFX_CIF_MI_CTRL_RAW_ENABLE_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_IN_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_OUT_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_OUT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.JPEG_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_JPEG_ENABLE_OUT_OFF (18u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_IN_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_OUT_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_OUT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.MP_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_MP_ENABLE_OUT_OFF (16u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_IN */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_IN_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_OUT_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_OUT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_CTRL_SHD_Bits.RAW_ENABLE_OUT */
#define IFX_CIF_MI_CTRL_SHD_RAW_ENABLE_OUT_OFF (19u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.BUS_ERROR */
#define IFX_CIF_MI_ICR_BUS_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.BUS_ERROR */
#define IFX_CIF_MI_ICR_BUS_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.BUS_ERROR */
#define IFX_CIF_MI_ICR_BUS_ERROR_OFF (10u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.FILL_MPY */
#define IFX_CIF_MI_ICR_FILL_MPY_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.FILL_MPY */
#define IFX_CIF_MI_ICR_FILL_MPY_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.FILL_MPY */
#define IFX_CIF_MI_ICR_FILL_MPY_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ICR_MBLK_LINE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ICR_MBLK_LINE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ICR_MBLK_LINE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ICR_MP_FRAME_END_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ICR_MP_FRAME_END_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ICR_MP_FRAME_END_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ICR_WRAP_MP_CB_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ICR_WRAP_MP_CB_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ICR_WRAP_MP_CB_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ICR_WRAP_MP_CR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ICR_WRAP_MP_CR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ICR_WRAP_MP_CR_OFF (6u)

/** \brief  Length for Ifx_CIF_MI_ICR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ICR_WRAP_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ICR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ICR_WRAP_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ICR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ICR_WRAP_MP_Y_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.BUS_ERROR */
#define IFX_CIF_MI_IMSC_BUS_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.BUS_ERROR */
#define IFX_CIF_MI_IMSC_BUS_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.BUS_ERROR */
#define IFX_CIF_MI_IMSC_BUS_ERROR_OFF (10u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.FILL_MP_Y */
#define IFX_CIF_MI_IMSC_FILL_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.FILL_MP_Y */
#define IFX_CIF_MI_IMSC_FILL_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.FILL_MP_Y */
#define IFX_CIF_MI_IMSC_FILL_MP_Y_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.MBLK_LINE */
#define IFX_CIF_MI_IMSC_MBLK_LINE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.MBLK_LINE */
#define IFX_CIF_MI_IMSC_MBLK_LINE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.MBLK_LINE */
#define IFX_CIF_MI_IMSC_MBLK_LINE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.MP_FRAME_END */
#define IFX_CIF_MI_IMSC_MP_FRAME_END_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.MP_FRAME_END */
#define IFX_CIF_MI_IMSC_MP_FRAME_END_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.MP_FRAME_END */
#define IFX_CIF_MI_IMSC_MP_FRAME_END_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_IMSC_WRAP_MP_CB_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_IMSC_WRAP_MP_CB_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_IMSC_WRAP_MP_CB_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_IMSC_WRAP_MP_CR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_IMSC_WRAP_MP_CR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_IMSC_WRAP_MP_CR_OFF (6u)

/** \brief  Length for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_IMSC_WRAP_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_IMSC_WRAP_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_IMSC_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_IMSC_WRAP_MP_Y_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_INIT_Bits.MI_CFG_UPD */
#define IFX_CIF_MI_INIT_MI_CFG_UPD_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_INIT_Bits.MI_CFG_UPD */
#define IFX_CIF_MI_INIT_MI_CFG_UPD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_INIT_Bits.MI_CFG_UPD */
#define IFX_CIF_MI_INIT_MI_CFG_UPD_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_INIT_Bits.MI_SKIP */
#define IFX_CIF_MI_INIT_MI_SKIP_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_INIT_Bits.MI_SKIP */
#define IFX_CIF_MI_INIT_MI_SKIP_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_INIT_Bits.MI_SKIP */
#define IFX_CIF_MI_INIT_MI_SKIP_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.Bus_ERROR */
#define IFX_CIF_MI_ISR_BUS_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.Bus_ERROR */
#define IFX_CIF_MI_ISR_BUS_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.Bus_ERROR */
#define IFX_CIF_MI_ISR_BUS_ERROR_OFF (10u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.FILL_MP_Y */
#define IFX_CIF_MI_ISR_FILL_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.FILL_MP_Y */
#define IFX_CIF_MI_ISR_FILL_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.FILL_MP_Y */
#define IFX_CIF_MI_ISR_FILL_MP_Y_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ISR_MBLK_LINE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ISR_MBLK_LINE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.MBLK_LINE */
#define IFX_CIF_MI_ISR_MBLK_LINE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ISR_MP_FRAME_END_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ISR_MP_FRAME_END_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.MP_FRAME_END */
#define IFX_CIF_MI_ISR_MP_FRAME_END_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ISR_WRAP_MP_CB_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ISR_WRAP_MP_CB_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_ISR_WRAP_MP_CB_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ISR_WRAP_MP_CR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ISR_WRAP_MP_CR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_ISR_WRAP_MP_CR_OFF (6u)

/** \brief  Length for Ifx_CIF_MI_ISR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ISR_WRAP_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_ISR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ISR_WRAP_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_ISR_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_ISR_WRAP_MP_Y_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_MIS_BUS_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_MIS_BUS_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_MIS_BUS_ERROR_OFF (10u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_MIS_FILL_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_MIS_FILL_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_MIS_FILL_MP_Y_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_MIS_MBLK_LINE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_MIS_MBLK_LINE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_MIS_MBLK_LINE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_MIS_MP_FRAME_END_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_MIS_MP_FRAME_END_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_MIS_MP_FRAME_END_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_MIS_WRAP_MP_CB_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_MIS_WRAP_MP_CB_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_MIS_WRAP_MP_CB_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_MIS_WRAP_MP_CR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_MIS_WRAP_MP_CR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_MIS_WRAP_MP_CR_OFF (6u)

/** \brief  Length for Ifx_CIF_MI_MIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_MIS_WRAP_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_MIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_MIS_WRAP_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_MIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_MIS_WRAP_MP_Y_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.MP_CB_BASEAD_INIT */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_MP_CB_BASEAD_INIT_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.MP_CB_BASEAD_INIT */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_MP_CB_BASEAD_INIT_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_BASE_AD_INIT_Bits.MP_CB_BASEAD_INIT */
#define IFX_CIF_MI_MP_CB_BASE_AD_INIT_MP_CB_BASEAD_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.MP_CB_BASE_AD */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_MP_CB_BASE_AD_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.MP_CB_BASE_AD */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_MP_CB_BASE_AD_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_BASE_AD_SHD_Bits.MP_CB_BASE_AD */
#define IFX_CIF_MI_MP_CB_BASE_AD_SHD_MP_CB_BASE_AD_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.MP_CB_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_MP_CB_OFFS_CNT_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.MP_CB_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_MP_CB_OFFS_CNT_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_INIT_Bits.MP_CB_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_INIT_MP_CB_OFFS_CNT_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.MP_CB_OFFS_CNT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_MP_CB_OFFS_CNT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.MP_CB_OFFS_CNT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_MP_CB_OFFS_CNT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_SHD_Bits.MP_CB_OFFS_CNT */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_SHD_MP_CB_OFFS_CNT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.MP_CB_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_MP_CB_OFFS_CNT_START_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.MP_CB_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_MP_CB_OFFS_CNT_START_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_OFFS_CNT_START_Bits.MP_CB_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CB_OFFS_CNT_START_MP_CB_OFFS_CNT_START_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.MP_CB_SIZE_INIT */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_MP_CB_SIZE_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.MP_CB_SIZE_INIT */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_MP_CB_SIZE_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_SIZE_INIT_Bits.MP_CB_SIZE_INIT */
#define IFX_CIF_MI_MP_CB_SIZE_INIT_MP_CB_SIZE_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.MP_CB_SIZE */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_MP_CB_SIZE_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.MP_CB_SIZE */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_MP_CB_SIZE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CB_SIZE_SHD_Bits.MP_CB_SIZE */
#define IFX_CIF_MI_MP_CB_SIZE_SHD_MP_CB_SIZE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.MP_CR_BASE_AD_INIT */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_MP_CR_BASE_AD_INIT_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.MP_CR_BASE_AD_INIT */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_MP_CR_BASE_AD_INIT_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_BASE_AD_INIT_Bits.MP_CR_BASE_AD_INIT */
#define IFX_CIF_MI_MP_CR_BASE_AD_INIT_MP_CR_BASE_AD_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.MP_CR_BASE_AD */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_MP_CR_BASE_AD_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.MP_CR_BASE_AD */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_MP_CR_BASE_AD_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_BASE_AD_SHD_Bits.MP_CR_BASE_AD */
#define IFX_CIF_MI_MP_CR_BASE_AD_SHD_MP_CR_BASE_AD_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.MP_CR_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_MP_CR_OFFS_CNT_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.MP_CR_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_MP_CR_OFFS_CNT_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_INIT_Bits.MP_CR_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_INIT_MP_CR_OFFS_CNT_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.MP_CR_OFFS_CNT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_MP_CR_OFFS_CNT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.MP_CR_OFFS_CNT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_MP_CR_OFFS_CNT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_SHD_Bits.MP_CR_OFFS_CNT */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_SHD_MP_CR_OFFS_CNT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.MP_CR_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_MP_CR_OFFS_CNT_START_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.MP_CR_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_MP_CR_OFFS_CNT_START_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_OFFS_CNT_START_Bits.MP_CR_OFFS_CNT_START */
#define IFX_CIF_MI_MP_CR_OFFS_CNT_START_MP_CR_OFFS_CNT_START_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.MP_CR_SIZE_INIT */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_MP_CR_SIZE_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.MP_CR_SIZE_INIT */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_MP_CR_SIZE_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_SIZE_INIT_Bits.MP_CR_SIZE_INIT */
#define IFX_CIF_MI_MP_CR_SIZE_INIT_MP_CR_SIZE_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.MP_CR_SIZE */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_MP_CR_SIZE_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.MP_CR_SIZE */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_MP_CR_SIZE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_CR_SIZE_SHD_Bits.MP_CR_SIZE */
#define IFX_CIF_MI_MP_CR_SIZE_SHD_MP_CR_SIZE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.MP_Y_BASE_AD_INIT */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_MP_Y_BASE_AD_INIT_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.MP_Y_BASE_AD_INIT */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_MP_Y_BASE_AD_INIT_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_BASE_AD_INIT_Bits.MP_Y_BASE_AD_INIT */
#define IFX_CIF_MI_MP_Y_BASE_AD_INIT_MP_Y_BASE_AD_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.MP_Y_BASE_AD */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_MP_Y_BASE_AD_LEN (30u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.MP_Y_BASE_AD */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_MP_Y_BASE_AD_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_BASE_AD_SHD_Bits.MP_Y_BASE_AD */
#define IFX_CIF_MI_MP_Y_BASE_AD_SHD_MP_Y_BASE_AD_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.MP_Y_IRQ_OFFS_INIT */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_MP_Y_IRQ_OFFS_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.MP_Y_IRQ_OFFS_INIT */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_MP_Y_IRQ_OFFS_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_IRQ_OFFS_INIT_Bits.MP_Y_IRQ_OFFS_INIT */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_INIT_MP_Y_IRQ_OFFS_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.MP_Y_IRQ_OFFS */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_MP_Y_IRQ_OFFS_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.MP_Y_IRQ_OFFS */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_MP_Y_IRQ_OFFS_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_IRQ_OFFS_SHD_Bits.MP_Y_IRQ_OFFS */
#define IFX_CIF_MI_MP_Y_IRQ_OFFS_SHD_MP_Y_IRQ_OFFS_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.MP_Y_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_MP_Y_OFFS_CNT_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.MP_Y_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_MP_Y_OFFS_CNT_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_INIT_Bits.MP_Y_OFFS_CNT_INIT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_INIT_MP_Y_OFFS_CNT_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.MP_Y_OFFS_CNT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_MP_Y_OFFS_CNT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.MP_Y_OFFS_CNT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_MP_Y_OFFS_CNT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_SHD_Bits.MP_Y_OFFS_CNT */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_SHD_MP_Y_OFFS_CNT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.MP_Y_OFFS_CNT_START */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_MP_Y_OFFS_CNT_START_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.MP_Y_OFFS_CNT_START */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_MP_Y_OFFS_CNT_START_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_OFFS_CNT_START_Bits.MP_Y_OFFS_CNT_START */
#define IFX_CIF_MI_MP_Y_OFFS_CNT_START_MP_Y_OFFS_CNT_START_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.MP_Y_SIZE_INIT */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_MP_Y_SIZE_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.MP_Y_SIZE_INIT */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_MP_Y_SIZE_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_SIZE_INIT_Bits.MP_Y_SIZE_INIT */
#define IFX_CIF_MI_MP_Y_SIZE_INIT_MP_Y_SIZE_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.MP_Y_SIZE */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_MP_Y_SIZE_LEN (22u)

/** \brief  Mask for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.MP_Y_SIZE */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_MP_Y_SIZE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MI_MP_Y_SIZE_SHD_Bits.MP_Y_SIZE */
#define IFX_CIF_MI_MP_Y_SIZE_SHD_MP_Y_SIZE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_RIS_BUS_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_RIS_BUS_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.BUS_ERROR */
#define IFX_CIF_MI_RIS_BUS_ERROR_OFF (10u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_RIS_FILL_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_RIS_FILL_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.FILL_MP_Y */
#define IFX_CIF_MI_RIS_FILL_MP_Y_OFF (3u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_RIS_MBLK_LINE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_RIS_MBLK_LINE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.MBLK_LINE */
#define IFX_CIF_MI_RIS_MBLK_LINE_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_RIS_MP_FRAME_END_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_RIS_MP_FRAME_END_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.MP_FRAME_END */
#define IFX_CIF_MI_RIS_MP_FRAME_END_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_RIS_WRAP_MP_CB_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_RIS_WRAP_MP_CB_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CB */
#define IFX_CIF_MI_RIS_WRAP_MP_CB_OFF (5u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_RIS_WRAP_MP_CR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_RIS_WRAP_MP_CR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.WRAP_MP_CR */
#define IFX_CIF_MI_RIS_WRAP_MP_CR_OFF (6u)

/** \brief  Length for Ifx_CIF_MI_RIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_RIS_WRAP_MP_Y_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_RIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_RIS_WRAP_MP_Y_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_RIS_Bits.WRAP_MP_Y */
#define IFX_CIF_MI_RIS_WRAP_MP_Y_OFF (4u)

/** \brief  Length for Ifx_CIF_MI_STATUS_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_BUS_WRITE_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_BUS_WRITE_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_BUS_WRITE_ERROR_OFF (8u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_CLR_BUS_WRITE_ERROR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_CLR_BUS_WRITE_ERROR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.BUS_WRITE_ERROR */
#define IFX_CIF_MI_STATUS_CLR_BUS_WRITE_ERROR_OFF (8u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_1_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_1_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_1_FIFO_FULL_OFF (24u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_2_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_2_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_2_FIFO_FULL_OFF (25u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_3_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_3_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_3_FIFO_FULL_OFF (26u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_4_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_4_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_4_FIFO_FULL_OFF (27u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_5_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_5_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_EP_5_FIFO_FULL_OFF (28u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CB_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CB_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CB_FIFO_FULL_OFF (1u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CR_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CR_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_CR_FIFO_FULL_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_STATUS_CLR_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_Y_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_CLR_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_Y_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_CLR_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_CLR_MP_Y_FIFO_FULL_OFF (0u)

/** \brief  Length for Ifx_CIF_MI_STATUS_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CB_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CB_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_Bits.MP_CB_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CB_FIFO_FULL_OFF (1u)

/** \brief  Length for Ifx_CIF_MI_STATUS_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CR_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CR_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_Bits.MP_CR_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_CR_FIFO_FULL_OFF (2u)

/** \brief  Length for Ifx_CIF_MI_STATUS_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_Y_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MI_STATUS_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_Y_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MI_STATUS_Bits.MP_Y_FIFO_FULL */
#define IFX_CIF_MI_STATUS_MP_Y_FIFO_FULL_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.EP_BASE_AD_INIT */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_EP_BASE_AD_INIT_LEN (30u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.EP_BASE_AD_INIT */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_EP_BASE_AD_INIT_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.EP_BASE_AD_INIT */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_EP_BASE_AD_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_BASE_AD_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.EP_BASE_AD */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_EP_BASE_AD_LEN (30u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.EP_BASE_AD */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_EP_BASE_AD_MSK (0x3fffffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.EP_BASE_AD */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_EP_BASE_AD_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_BASE_AD_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_BASE_AD_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MIEP_CH_CTRL_BYTE_SWAP_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MIEP_CH_CTRL_BYTE_SWAP_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_Bits.BYTE_SWAP */
#define IFX_CIF_MIEP_CH_CTRL_BYTE_SWAP_OFF (7u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_ENABLE */
#define IFX_CIF_MIEP_CH_CTRL_EP_ENABLE_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_ENABLE */
#define IFX_CIF_MIEP_CH_CTRL_EP_ENABLE_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_ENABLE */
#define IFX_CIF_MIEP_CH_CTRL_EP_ENABLE_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_WRITE_FORMAT */
#define IFX_CIF_MIEP_CH_CTRL_EP_WRITE_FORMAT_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_WRITE_FORMAT */
#define IFX_CIF_MIEP_CH_CTRL_EP_WRITE_FORMAT_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_Bits.EP_WRITE_FORMAT */
#define IFX_CIF_MIEP_CH_CTRL_EP_WRITE_FORMAT_OFF (22u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_BASE_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_BASE_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_BASE_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_BASE_EN_OFF (20u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_OFFSET_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_OFFSET_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_Bits.INIT_OFFSET_EN */
#define IFX_CIF_MIEP_CH_CTRL_INIT_OFFSET_EN_OFF (21u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_IN */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_IN_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_IN */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_IN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_IN */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_IN_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_OUT */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_OUT_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_OUT */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_OUT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_CTRL_SHD_Bits.EP_ENABLE_OUT */
#define IFX_CIF_MIEP_CH_CTRL_SHD_EP_ENABLE_OUT_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_CFG_UPD */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_CFG_UPD_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_CFG_UPD */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_CFG_UPD_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_CFG_UPD */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_CFG_UPD_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_SKIP */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_SKIP_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_SKIP */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_SKIP_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_INIT_Bits.MI_EP_SKIP */
#define IFX_CIF_MIEP_CH_INIT_MI_EP_SKIP_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.EP_IRQ_OFFS_INIT */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_EP_IRQ_OFFS_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.EP_IRQ_OFFS_INIT */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_EP_IRQ_OFFS_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.EP_IRQ_OFFS_INIT */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_EP_IRQ_OFFS_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_IRQ_OFFS_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.EP_IRQ_OFFS */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_EP_IRQ_OFFS_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.EP_IRQ_OFFS */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_EP_IRQ_OFFS_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.EP_IRQ_OFFS */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_EP_IRQ_OFFS_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_IRQ_OFFS_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_IRQ_OFFS_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.EP_OFFS_CNT_INIT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_EP_OFFS_CNT_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.EP_OFFS_CNT_INIT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_EP_OFFS_CNT_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.EP_OFFS_CNT_INIT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_EP_OFFS_CNT_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.EP_OFFS_CNT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_EP_OFFS_CNT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.EP_OFFS_CNT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_EP_OFFS_CNT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.EP_OFFS_CNT */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_EP_OFFS_CNT_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.EP_OFFS_CNT_START */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_EP_OFFS_CNT_START_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.EP_OFFS_CNT_START */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_EP_OFFS_CNT_START_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.EP_OFFS_CNT_START */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_EP_OFFS_CNT_START_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_OFFS_CNT_START_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_OFFS_CNT_START_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.EP_SIZE_INIT */
#define IFX_CIF_MIEP_CH_SIZE_INIT_EP_SIZE_INIT_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.EP_SIZE_INIT */
#define IFX_CIF_MIEP_CH_SIZE_INIT_EP_SIZE_INIT_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.EP_SIZE_INIT */
#define IFX_CIF_MIEP_CH_SIZE_INIT_EP_SIZE_INIT_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_INIT_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_INIT_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_SIZE_INIT_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_INIT_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.EP_SIZE */
#define IFX_CIF_MIEP_CH_SIZE_SHD_EP_SIZE_LEN (22u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.EP_SIZE */
#define IFX_CIF_MIEP_CH_SIZE_SHD_EP_SIZE_MSK (0x3fffffu)

/** \brief  Offset for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.EP_SIZE */
#define IFX_CIF_MIEP_CH_SIZE_SHD_EP_SIZE_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_SHD_FIXED_TO_00_LEN (2u)

/** \brief  Mask for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_SHD_FIXED_TO_00_MSK (0x3u)

/** \brief  Offset for Ifx_CIF_MIEP_CH_SIZE_SHD_Bits.FIXED_TO_00 */
#define IFX_CIF_MIEP_CH_SIZE_SHD_FIXED_TO_00_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ICR_FILL_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ICR_FILL_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ICR_FILL_EP_1_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ICR_FILL_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ICR_FILL_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ICR_FILL_EP_2_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ICR_FILL_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ICR_FILL_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ICR_FILL_EP_3_OFF (9u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ICR_FILL_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ICR_FILL_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ICR_FILL_EP_4_OFF (13u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ICR_FILL_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ICR_FILL_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ICR_FILL_EP_5_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_1_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_2_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_3_OFF (8u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_4_OFF (12u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ICR_FRAME_END_EP_5_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_1_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_3_OFF (11u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_4_OFF (15u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ICR_MBLK_LINE_EP_5_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_1_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_2_OFF (6u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_3_OFF (10u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_4_OFF (14u)

/** \brief  Length for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ICR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ICR_WRAP_EP_5_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_1_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_2_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_3_OFF (9u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_4_OFF (13u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_IMSC_FILL_EP_5_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_1_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_2_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_3_OFF (8u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_4_OFF (12u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_IMSC_FRAME_END_EP_5_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_1_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_2_OFF (7u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_3_OFF (11u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_4_OFF (15u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_IMSC_MBLK_LINE_EP_5_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_1_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_2_OFF (6u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_3_OFF (10u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_4_OFF (14u)

/** \brief  Length for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_IMSC_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_IMSC_WRAP_EP_5_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ISR_FILL_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ISR_FILL_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_ISR_FILL_EP_1_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ISR_FILL_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ISR_FILL_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_ISR_FILL_EP_2_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ISR_FILL_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ISR_FILL_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_ISR_FILL_EP_3_OFF (9u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ISR_FILL_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ISR_FILL_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_ISR_FILL_EP_4_OFF (13u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ISR_FILL_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ISR_FILL_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_ISR_FILL_EP_5_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_1_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_2_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_3_OFF (8u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_4_OFF (12u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_ISR_FRAME_END_EP_5_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_1_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_2_OFF (7u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_3_OFF (11u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_4_OFF (15u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_ISR_MBLK_LINE_EP_5_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_1_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_2_OFF (6u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_3_OFF (10u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_4_OFF (14u)

/** \brief  Length for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_ISR_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_ISR_WRAP_EP_5_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_MIS_FILL_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_MIS_FILL_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_MIS_FILL_EP_1_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_MIS_FILL_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_MIS_FILL_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_MIS_FILL_EP_2_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_MIS_FILL_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_MIS_FILL_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_MIS_FILL_EP_3_OFF (9u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_MIS_FILL_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_MIS_FILL_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_MIS_FILL_EP_4_OFF (13u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_MIS_FILL_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_MIS_FILL_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_MIS_FILL_EP_5_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_1_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_2_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_3_OFF (8u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_4_OFF (12u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_MIS_FRAME_END_EP_5_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_1_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_2_OFF (7u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_3_OFF (11u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_4_OFF (15u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_MIS_MBLK_LINE_EP_5_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_1_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_2_OFF (6u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_3_OFF (10u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_4_OFF (14u)

/** \brief  Length for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_MIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_MIS_WRAP_EP_5_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_RIS_FILL_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_RIS_FILL_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_1 */
#define IFX_CIF_MIEP_RIS_FILL_EP_1_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_RIS_FILL_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_RIS_FILL_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_2 */
#define IFX_CIF_MIEP_RIS_FILL_EP_2_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_RIS_FILL_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_RIS_FILL_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_3 */
#define IFX_CIF_MIEP_RIS_FILL_EP_3_OFF (9u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_RIS_FILL_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_RIS_FILL_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_4 */
#define IFX_CIF_MIEP_RIS_FILL_EP_4_OFF (13u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_RIS_FILL_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_RIS_FILL_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FILL_EP_5 */
#define IFX_CIF_MIEP_RIS_FILL_EP_5_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_1 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_1_OFF (0u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_2 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_2_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_3 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_3_OFF (8u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_4 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_4_OFF (12u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.FRAME_END_EP_5 */
#define IFX_CIF_MIEP_RIS_FRAME_END_EP_5_OFF (16u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_1 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_1_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_2 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_2_OFF (7u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_3 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_3_OFF (11u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_4 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_4_OFF (15u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.MBLK_LINE_EP_5 */
#define IFX_CIF_MIEP_RIS_MBLK_LINE_EP_5_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_1_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_1_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_1 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_1_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_2_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_2_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_2 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_2_OFF (6u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_3_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_3_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_3 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_3_OFF (10u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_4_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_4_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_4 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_4_OFF (14u)

/** \brief  Length for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_5_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_5_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_RIS_Bits.WRAP_EP_5 */
#define IFX_CIF_MIEP_RIS_WRAP_EP_5_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_1_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_1_IC_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_1_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_1_IC_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_1_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_1_IC_SIZE_ERR_CLR_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_2_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_2_IC_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_2_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_2_IC_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_2_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_2_IC_SIZE_ERR_CLR_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_3_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_3_IC_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_3_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_3_IC_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_3_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_3_IC_SIZE_ERR_CLR_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_4_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_4_IC_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_4_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_4_IC_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_4_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_4_IC_SIZE_ERR_CLR_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_5_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_5_IC_SIZE_ERR_CLR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_5_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_5_IC_SIZE_ERR_CLR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_CLR_Bits.EP_5_IC_SIZE_ERR_CLR */
#define IFX_CIF_MIEP_STA_ERR_CLR_EP_5_IC_SIZE_ERR_CLR_OFF (5u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_1_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_1_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_1_FIFO_FULL_OFF (17u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_1_IC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_1_IC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_1_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_1_IC_SIZE_ERR_OFF (1u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_2_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_2_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_2_FIFO_FULL_OFF (18u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_2_IC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_2_IC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_2_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_2_IC_SIZE_ERR_OFF (2u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_3_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_3_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_3_FIFO_FULL_OFF (19u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_3_IC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_3_IC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_3_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_3_IC_SIZE_ERR_OFF (3u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_4_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_4_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_4_FIFO_FULL_OFF (20u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_4_IC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_4_IC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_4_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_4_IC_SIZE_ERR_OFF (4u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_5_FIFO_FULL_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_5_FIFO_FULL_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_FIFO_FULL */
#define IFX_CIF_MIEP_STA_ERR_EP_5_FIFO_FULL_OFF (21u)

/** \brief  Length for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_5_IC_SIZE_ERR_LEN (1u)

/** \brief  Mask for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_5_IC_SIZE_ERR_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_MIEP_STA_ERR_Bits.EP_5_IC_SIZE_ERR */
#define IFX_CIF_MIEP_STA_ERR_EP_5_IC_SIZE_ERR_OFF (5u)

/** \brief  Length for Ifx_CIF_WD_CTRL_Bits.RST_H_CNT */
#define IFX_CIF_WD_CTRL_RST_H_CNT_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_CTRL_Bits.RST_H_CNT */
#define IFX_CIF_WD_CTRL_RST_H_CNT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_CTRL_Bits.RST_H_CNT */
#define IFX_CIF_WD_CTRL_RST_H_CNT_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_CTRL_Bits.RST_PD_CNT */
#define IFX_CIF_WD_CTRL_RST_PD_CNT_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_CTRL_Bits.RST_PD_CNT */
#define IFX_CIF_WD_CTRL_RST_PD_CNT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_CTRL_Bits.RST_PD_CNT */
#define IFX_CIF_WD_CTRL_RST_PD_CNT_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_CTRL_Bits.RST_V_CNT */
#define IFX_CIF_WD_CTRL_RST_V_CNT_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_CTRL_Bits.RST_V_CNT */
#define IFX_CIF_WD_CTRL_RST_V_CNT_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_CTRL_Bits.RST_V_CNT */
#define IFX_CIF_WD_CTRL_RST_V_CNT_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_CTRL_Bits.WD_EN */
#define IFX_CIF_WD_CTRL_WD_EN_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_CTRL_Bits.WD_EN */
#define IFX_CIF_WD_CTRL_WD_EN_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_CTRL_Bits.WD_EN */
#define IFX_CIF_WD_CTRL_WD_EN_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_CTRL_Bits.WD_PREDIV */
#define IFX_CIF_WD_CTRL_WD_PREDIV_LEN (16u)

/** \brief  Mask for Ifx_CIF_WD_CTRL_Bits.WD_PREDIV */
#define IFX_CIF_WD_CTRL_WD_PREDIV_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_WD_CTRL_Bits.WD_PREDIV */
#define IFX_CIF_WD_CTRL_WD_PREDIV_OFF (16u)

/** \brief  Length for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HES_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HES_TO_LEN (16u)

/** \brief  Mask for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HES_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HES_TO_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HES_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HES_TO_OFF (16u)

/** \brief  Length for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HSE_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HSE_TO_LEN (16u)

/** \brief  Mask for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HSE_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HSE_TO_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_WD_H_TIMEOUT_Bits.WD_HSE_TO */
#define IFX_CIF_WD_H_TIMEOUT_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_ICR_Bits.ICR_WD_HES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ICR_Bits.ICR_WD_HES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ICR_Bits.ICR_WD_HES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HES_TO_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_ICR_Bits.ICR_WD_HSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ICR_Bits.ICR_WD_HSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ICR_Bits.ICR_WD_HSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_ICR_Bits.ICR_WD_VES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ICR_Bits.ICR_WD_VES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ICR_Bits.ICR_WD_VES_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VES_TO_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_ICR_Bits.ICR_WD_VSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ICR_Bits.ICR_WD_VSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ICR_Bits.ICR_WD_VSE_TO */
#define IFX_CIF_WD_ICR_ICR_WD_VSE_TO_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HES_TO_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_HSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VES_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VES_TO_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_IMSC_Bits.IMSC_WD_VSE_TO */
#define IFX_CIF_WD_IMSC_IMSC_WD_VSE_TO_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_ISR_Bits.ISR_WD_HES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ISR_Bits.ISR_WD_HES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ISR_Bits.ISR_WD_HES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HES_TO_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_ISR_Bits.ISR_WD_HSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ISR_Bits.ISR_WD_HSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ISR_Bits.ISR_WD_HSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_ISR_Bits.ISR_WD_VES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ISR_Bits.ISR_WD_VES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ISR_Bits.ISR_WD_VES_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VES_TO_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_ISR_Bits.ISR_WD_VSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_ISR_Bits.ISR_WD_VSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_ISR_Bits.ISR_WD_VSE_TO */
#define IFX_CIF_WD_ISR_ISR_WD_VSE_TO_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_MIS_Bits.MIS_WD_HES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_MIS_Bits.MIS_WD_HES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_MIS_Bits.MIS_WD_HES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HES_TO_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_MIS_Bits.MIS_WD_HSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_MIS_Bits.MIS_WD_HSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_MIS_Bits.MIS_WD_HSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_MIS_Bits.MIS_WD_VES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_MIS_Bits.MIS_WD_VES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_MIS_Bits.MIS_WD_VES_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VES_TO_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_MIS_Bits.MIS_WD_VSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_MIS_Bits.MIS_WD_VSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_MIS_Bits.MIS_WD_VSE_TO */
#define IFX_CIF_WD_MIS_MIS_WD_VSE_TO_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_RIS_Bits.RIS_WD_HES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_RIS_Bits.RIS_WD_HES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_RIS_Bits.RIS_WD_HES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HES_TO_OFF (1u)

/** \brief  Length for Ifx_CIF_WD_RIS_Bits.RIS_WD_HSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_RIS_Bits.RIS_WD_HSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_RIS_Bits.RIS_WD_HSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_HSE_TO_OFF (0u)

/** \brief  Length for Ifx_CIF_WD_RIS_Bits.RIS_WD_VES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VES_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_RIS_Bits.RIS_WD_VES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VES_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_RIS_Bits.RIS_WD_VES_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VES_TO_OFF (3u)

/** \brief  Length for Ifx_CIF_WD_RIS_Bits.RIS_WD_VSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VSE_TO_LEN (1u)

/** \brief  Mask for Ifx_CIF_WD_RIS_Bits.RIS_WD_VSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VSE_TO_MSK (0x1u)

/** \brief  Offset for Ifx_CIF_WD_RIS_Bits.RIS_WD_VSE_TO */
#define IFX_CIF_WD_RIS_RIS_WD_VSE_TO_OFF (2u)

/** \brief  Length for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VES_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VES_TO_LEN (16u)

/** \brief  Mask for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VES_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VES_TO_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VES_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VES_TO_OFF (16u)

/** \brief  Length for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VSE_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VSE_TO_LEN (16u)

/** \brief  Mask for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VSE_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VSE_TO_MSK (0xffffu)

/** \brief  Offset for Ifx_CIF_WD_V_TIMEOUT_Bits.WD_VSE_TO */
#define IFX_CIF_WD_V_TIMEOUT_WD_VSE_TO_OFF (0u)
/** \}  */
/******************************************************************************/
/******************************************************************************/
#endif /* IFXCIF_BF_H */
