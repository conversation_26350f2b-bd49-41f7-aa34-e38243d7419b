	; Module start
	.compiler_version	"TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler v1.1r8 Build 22011964"
	.compiler_invocation	"ctc -f cc18252a --dep-file=IfxSmu_PinMap.d -c99 --fp-model=2cFlnrSTz -D__CPU__=tc26xb -D__CPU_TC26XB__ --core=tc1.6.x -D__CPU__=tc26xb -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user1 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\code\\\\user2\\\\xf_asr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Configurations -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Platform\\\\Tricore\\\\Compilers -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\If\\\\Ccu6If -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\StdIf -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Bsp -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\SysSe\\\\Math -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Service\\\\CpuGeneric\\\\_Utilities -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Asc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Asclin\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Ccu6\\\\Timer -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\CStart -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Irq -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Cpu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Dma -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Dma\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Flash\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12 -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\IncrEnc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gpt12\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Atom\\\\Pwm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Gtm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Mtu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Port\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\SpiMaster -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Qspi\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Scu\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Src\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Stm\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Adc -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\Vadc\\\\Std -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Impl -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_Lib\\\\DataHandling -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\\\_PinMap -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_common -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_components -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_device -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\zf_driver -IC:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\user -g2 -w544 -w557 -t4 --language=+volatile -N0 -O0 -Y0 -Z0 --compact-max-size=200 --misrac-version=2004 -o libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.src ../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c"
	.compiler_name		"ctc"
	;source	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c'

	
$TC16X
	
	.sdecl	'.rodata.IfxSmu_PinMap.IfxSmu_FSP_P33_8_OUT',data,rom,cluster('IfxSmu_FSP_P33_8_OUT')
	.sect	'.rodata.IfxSmu_PinMap.IfxSmu_FSP_P33_8_OUT'
	.global	IfxSmu_FSP_P33_8_OUT
	.align	4
IfxSmu_FSP_P33_8_OUT:	.type	object
	.size	IfxSmu_FSP_P33_8_OUT,16
	.word	-268212224,-268184832
	.byte	8
	.space	3
	.byte	136
	.space	3
	.sdecl	'.data.IfxSmu_PinMap.IfxSmu_Fsp_Out_pinTable',data,cluster('IfxSmu_Fsp_Out_pinTable')
	.sect	'.data.IfxSmu_PinMap.IfxSmu_Fsp_Out_pinTable'
	.global	IfxSmu_Fsp_Out_pinTable
	.align	4
IfxSmu_Fsp_Out_pinTable:	.type	object
	.size	IfxSmu_Fsp_Out_pinTable,4
	.word	IfxSmu_FSP_P33_8_OUT
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	45294
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1
	.word	.L5
	.byte	2,1,1,3
	.word	237
	.byte	4
	.byte	'Ifx__jump_and_link',0,3,1,61,17,1,1,5
	.byte	'fun',0,1,61,43
	.word	240
	.byte	6,0,7
	.byte	'__fract',0,4,128,1,7
	.byte	'float',0,4,4,8
	.byte	'Ifx__float_to_fract',0,3,1,152,2,18
	.word	285
	.byte	1,1,5
	.byte	'a',0,1,152,2,44
	.word	297
	.byte	6,0,7
	.byte	'unsigned long long int',0,8,7,9
	.byte	'void',0,3
	.word	377
	.byte	8
	.byte	'__ld64',0,3,2,135,1,19
	.word	351
	.byte	1,1,5
	.byte	'addr',0,2,135,1,32
	.word	383
	.byte	6,0,4
	.byte	'__st64',0,3,2,143,1,17,1,1,5
	.byte	'addr',0,2,143,1,30
	.word	383
	.byte	5
	.byte	'value',0,2,143,1,43
	.word	351
	.byte	6,0,7
	.byte	'unsigned int',0,4,7,7
	.byte	'int',0,4,5,7
	.byte	'unsigned char',0,1,8,7
	.byte	'unsigned short int',0,2,7,10
	.byte	'_Ifx_P_OUT_Bits',0,4,143,3,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,181,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	531
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMR_Bits',0,4,169,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,133,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	847
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ID_Bits',0,4,110,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,148,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1418
	.byte	4,2,35,0,0,14,4
	.word	492
	.byte	15,3,0,10
	.byte	'_Ifx_P_IOCR0_Bits',0,4,140,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC2',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC3',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,164,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1546
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR4_Bits',0,4,166,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC4',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC5',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC6',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC7',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,180,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1761
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR8_Bits',0,4,179,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC8',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC9',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC10',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC11',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,188,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	1976
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IOCR12_Bits',0,4,153,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PC12',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PC13',0,1
	.word	492
	.byte	5,0,2,35,1,11
	.byte	'reserved_16',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PC14',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PC15',0,1
	.word	492
	.byte	5,0,2,35,3,0,12,4,172,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2193
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_IN_Bits',0,4,118,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'P2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'P3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'P4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'P5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'P6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'P7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'P8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'P9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'P10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'P11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'P12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'P13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'P14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'P15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,156,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2413
	.byte	4,2,35,0,0,14,24
	.word	492
	.byte	15,23,0,10
	.byte	'_Ifx_P_PDR0_Bits',0,4,205,3,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD2',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL2',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD3',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL3',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD4',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL4',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD5',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL5',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD6',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL6',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD7',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL7',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,205,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	2736
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PDR1_Bits',0,4,226,3,16,4,11
	.byte	'PD8',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL8',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD9',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL9',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PD10',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'PL10',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PD11',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'PL11',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'PD12',0,1
	.word	492
	.byte	3,5,2,35,2,11
	.byte	'PL12',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PD13',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'PL13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PD14',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'PL14',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PD15',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'PL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,213,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3040
	.byte	4,2,35,0,0,14,8
	.word	492
	.byte	15,7,0,10
	.byte	'_Ifx_P_ESR_Bits',0,4,88,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,140,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3365
	.byte	4,2,35,0,0,14,12
	.word	492
	.byte	15,11,0,10
	.byte	'_Ifx_P_PDISC_Bits',0,4,183,3,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDIS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDIS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDIS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDIS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDIS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDIS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PDIS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PDIS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PDIS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PDIS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PDIS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PDIS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PDIS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PDIS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,197,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	3705
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_PCSR_Bits',0,4,165,3,16,4,11
	.byte	'SEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SEL3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SEL4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'SEL5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'SEL6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'SEL7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'SEL10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SEL11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	19,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,189,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4071
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR0_Bits',0,4,206,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,12,4,149,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4357
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR4_Bits',0,4,227,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,165,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4504
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR8_Bits',0,4,238,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,4
	.word	469
	.byte	20,0,2,35,0,0,12,4,173,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4673
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR12_Bits',0,4,216,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	12,4,2,35,0,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,157,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	4845
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR0_Bits',0,4,232,1,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'reserved_20',0,2
	.word	509
	.byte	12,0,2,35,2,0,12,4,229,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5020
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR4_Bits',0,4,253,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	20,12,2,35,0,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,12,4,245,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5194
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR8_Bits',0,4,136,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,12,4,253,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5368
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR12_Bits',0,4,243,1,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,237,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5544
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMSR_Bits',0,4,249,2,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PS8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PS9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'PS10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PS11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'PS12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'PS13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PS14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'PS15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,141,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	5700
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_OMCR_Bits',0,4,147,2,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'PCL2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'PCL3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'PCL4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PCL5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PCL6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PCL7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'PCL8',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'PCL9',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'PCL10',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'PCL11',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'PCL12',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'PCL13',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'PCL14',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'PCL15',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,221,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6033
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR0_Bits',0,4,192,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,12,4,196,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6381
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_Bits',0,4,200,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,10
	.byte	'_Ifx_P_LPCR1_P21_Bits',0,4,208,1,16,4,11
	.byte	'RDIS_CTRL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'RX_DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'TERM',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'LRXTERM',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,12,4,204,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6505
	.byte	4,2,35,0,13
	.byte	'B_P21',0
	.word	6589
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_LPCR2_Bits',0,4,218,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'LVDSR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'LVDSRL',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'TDIS_CTRL',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'TX_DIS',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'TX_PD',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'TX_PWDPD',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,12,4,213,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	6769
	.byte	4,2,35,0,0,14,76
	.word	492
	.byte	15,75,0,10
	.byte	'_Ifx_P_ACCEN1_Bits',0,4,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,12,4,132,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7022
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P_ACCEN0_Bits',0,4,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,12,4,252,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	7109
	.byte	4,2,35,0,0,10
	.byte	'_Ifx_P',0,4,229,5,25,128,2,13
	.byte	'OUT',0
	.word	807
	.byte	4,2,35,0,13
	.byte	'OMR',0
	.word	1378
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	1497
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	1537
	.byte	4,2,35,12,13
	.byte	'IOCR0',0
	.word	1721
	.byte	4,2,35,16,13
	.byte	'IOCR4',0
	.word	1936
	.byte	4,2,35,20,13
	.byte	'IOCR8',0
	.word	2153
	.byte	4,2,35,24,13
	.byte	'IOCR12',0
	.word	2373
	.byte	4,2,35,28,13
	.byte	'reserved_20',0
	.word	1537
	.byte	4,2,35,32,13
	.byte	'IN',0
	.word	2687
	.byte	4,2,35,36,13
	.byte	'reserved_28',0
	.word	2727
	.byte	24,2,35,40,13
	.byte	'PDR0',0
	.word	3000
	.byte	4,2,35,64,13
	.byte	'PDR1',0
	.word	3316
	.byte	4,2,35,68,13
	.byte	'reserved_48',0
	.word	3356
	.byte	8,2,35,72,13
	.byte	'ESR',0
	.word	3656
	.byte	4,2,35,80,13
	.byte	'reserved_54',0
	.word	3696
	.byte	12,2,35,84,13
	.byte	'PDISC',0
	.word	4031
	.byte	4,2,35,96,13
	.byte	'PCSR',0
	.word	4317
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	3356
	.byte	8,2,35,104,13
	.byte	'OMSR0',0
	.word	4464
	.byte	4,2,35,112,13
	.byte	'OMSR4',0
	.word	4633
	.byte	4,2,35,116,13
	.byte	'OMSR8',0
	.word	4805
	.byte	4,2,35,120,13
	.byte	'OMSR12',0
	.word	4980
	.byte	4,2,35,124,13
	.byte	'OMCR0',0
	.word	5154
	.byte	4,3,35,128,1,13
	.byte	'OMCR4',0
	.word	5328
	.byte	4,3,35,132,1,13
	.byte	'OMCR8',0
	.word	5504
	.byte	4,3,35,136,1,13
	.byte	'OMCR12',0
	.word	5660
	.byte	4,3,35,140,1,13
	.byte	'OMSR',0
	.word	5993
	.byte	4,3,35,144,1,13
	.byte	'OMCR',0
	.word	6341
	.byte	4,3,35,148,1,13
	.byte	'reserved_98',0
	.word	3356
	.byte	8,3,35,152,1,13
	.byte	'LPCR0',0
	.word	6465
	.byte	4,3,35,160,1,13
	.byte	'LPCR1',0
	.word	6714
	.byte	4,3,35,164,1,13
	.byte	'LPCR2',0
	.word	6973
	.byte	4,3,35,168,1,13
	.byte	'reserved_A4',0
	.word	7013
	.byte	76,3,35,172,1,13
	.byte	'ACCEN1',0
	.word	7069
	.byte	4,3,35,248,1,13
	.byte	'ACCEN0',0
	.word	7636
	.byte	4,3,35,252,1,0,16
	.word	7676
	.byte	3
	.word	8279
	.byte	17,3,172,1,9,4,18
	.byte	'IfxPort_State_notChanged',0,0,18
	.byte	'IfxPort_State_high',0,1,18
	.byte	'IfxPort_State_low',0,128,128,4,18
	.byte	'IfxPort_State_toggled',0,129,128,4,0,4
	.byte	'IfxPort_setPinState',0,3,3,208,4,17,1,1,5
	.byte	'port',0,3,208,4,44
	.word	8284
	.byte	5
	.byte	'pinIndex',0,3,208,4,56
	.word	492
	.byte	5
	.byte	'action',0,3,208,4,80
	.word	8289
	.byte	6,0,19
	.word	245
	.byte	20
	.word	271
	.byte	6,0,19
	.word	306
	.byte	20
	.word	338
	.byte	6,0,19
	.word	388
	.byte	20
	.word	407
	.byte	6,0,19
	.word	423
	.byte	20
	.word	438
	.byte	20
	.word	452
	.byte	6,0,19
	.word	8392
	.byte	20
	.word	8420
	.byte	20
	.word	8434
	.byte	20
	.word	8452
	.byte	6,0,7
	.byte	'short int',0,2,5,21
	.byte	'__wchar_t',0,5,1,1
	.word	8545
	.byte	21
	.byte	'__size_t',0,5,1,1
	.word	469
	.byte	21
	.byte	'__ptrdiff_t',0,5,1,1
	.word	485
	.byte	22,1,3
	.word	8613
	.byte	21
	.byte	'__codeptr',0,5,1,1
	.word	8615
	.byte	7
	.byte	'unsigned int',0,4,7,10
	.byte	'_Ifx_SMU_ACCEN0_Bits',0,6,45,16,4,11
	.byte	'EN0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'EN1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'EN2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EN3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'EN4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'EN5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'EN6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'EN7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'EN8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'EN9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'EN10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'EN11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'EN12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'EN13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'EN14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'EN15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'EN16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'EN17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'EN18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'EN19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'EN20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'EN21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'EN22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'EN23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'EN24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'EN25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'EN26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'EN27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'EN28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'EN29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'EN30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'EN31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_ACCEN0_Bits',0,6,79,3
	.word	8654
	.byte	10
	.byte	'_Ifx_SMU_ACCEN1_Bits',0,6,82,16,4,11
	.byte	'reserved_0',0,4
	.word	8638
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SMU_ACCEN1_Bits',0,6,85,3
	.word	9211
	.byte	10
	.byte	'_Ifx_SMU_AD_Bits',0,6,88,16,4,11
	.byte	'DF0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'DF1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'DF2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'DF3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'DF4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'DF5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'DF6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'DF7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'DF8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'DF9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'DF10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'DF11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'DF12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'DF13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'DF14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'DF15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'DF16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'DF17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'DF18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'DF19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'DF20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'DF21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'DF22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'DF23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'DF24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'DF25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'DF26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'DF27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'DF28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'DF29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'DF30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'DF31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_AD_Bits',0,6,122,3
	.word	9288
	.byte	10
	.byte	'_Ifx_SMU_AFCNT_Bits',0,6,125,16,4,11
	.byte	'FCNT',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	4,24,2,35,0,11
	.byte	'ACNT',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8638
	.byte	14,2,2,35,0,11
	.byte	'FCO',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'ACO',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_AFCNT_Bits',0,6,133,1,3
	.word	9837
	.byte	10
	.byte	'_Ifx_SMU_AG_Bits',0,6,136,1,16,4,11
	.byte	'SF0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'SF1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'SF2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'SF3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'SF4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'SF5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'SF6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'SF7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'SF8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'SF9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'SF10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'SF11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'SF12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'SF13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'SF14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'SF15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'SF16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'SF17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'SF18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'SF19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'SF20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'SF21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'SF22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'SF23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'SF24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'SF25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'SF26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'SF27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'SF28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'SF29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'SF30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'SF31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_AG_Bits',0,6,170,1,3
	.word	9998
	.byte	10
	.byte	'_Ifx_SMU_AGC_Bits',0,6,173,1,16,4,11
	.byte	'IGCS0',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'reserved_3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'IGCS1',0,4
	.word	8638
	.byte	3,25,2,35,0,11
	.byte	'reserved_7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'IGCS2',0,4
	.word	8638
	.byte	3,21,2,35,0,11
	.byte	'reserved_11',0,4
	.word	8638
	.byte	5,16,2,35,0,11
	.byte	'ICS',0,4
	.word	8638
	.byte	3,13,2,35,0,11
	.byte	'reserved_19',0,4
	.word	8638
	.byte	5,8,2,35,0,11
	.byte	'PES',0,4
	.word	8638
	.byte	5,3,2,35,0,11
	.byte	'EFRST',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8638
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_SMU_AGC_Bits',0,6,186,1,3
	.word	10549
	.byte	10
	.byte	'_Ifx_SMU_AGCF_Bits',0,6,189,1,16,4,11
	.byte	'CF0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'CF1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'CF2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'CF3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'CF4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'CF5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'CF6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'CF7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'CF8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'CF9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'CF10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'CF11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'CF12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'CF13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'CF14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'CF15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'CF16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'CF17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'CF18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'CF19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'CF20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'CF21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'CF22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'CF23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'CF24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'CF25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'CF26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'CF27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'CF28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'CF29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'CF30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'CF31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_AGCF_Bits',0,6,223,1,3
	.word	10811
	.byte	10
	.byte	'_Ifx_SMU_AGFSP_Bits',0,6,226,1,16,4,11
	.byte	'FE0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'FE1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'FE2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'FE3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'FE4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'FE5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'FE6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'FE7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'FE8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'FE9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'FE10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'FE11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'FE12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'FE13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'FE14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'FE15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'FE16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'FE17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'FE18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'FE19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'FE20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'FE21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'FE22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'FE23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'FE24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'FE25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'FE26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'FE27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'FE28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'FE29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'FE30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'FE31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_AGFSP_Bits',0,6,132,2,3
	.word	11366
	.byte	10
	.byte	'_Ifx_SMU_CLC_Bits',0,6,135,2,16,4,11
	.byte	'DISR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'DISS',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'FDIS',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EDIS',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SMU_CLC_Bits',0,6,142,2,3
	.word	11923
	.byte	10
	.byte	'_Ifx_SMU_CMD_Bits',0,6,145,2,16,4,11
	.byte	'CMD',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ARG',0,4
	.word	8638
	.byte	4,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SMU_CMD_Bits',0,6,150,2,3
	.word	12060
	.byte	10
	.byte	'_Ifx_SMU_DBG_Bits',0,6,153,2,16,4,11
	.byte	'SSM',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SMU_DBG_Bits',0,6,157,2,3
	.word	12163
	.byte	10
	.byte	'_Ifx_SMU_FSP_Bits',0,6,160,2,16,4,11
	.byte	'PRE1',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'PRE2',0,4
	.word	8638
	.byte	2,27,2,35,0,11
	.byte	'MODE',0,4
	.word	8638
	.byte	2,25,2,35,0,11
	.byte	'PES',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'TFSP_LOW',0,4
	.word	8638
	.byte	14,10,2,35,0,11
	.byte	'TFSP_HIGH',0,4
	.word	8638
	.byte	10,0,2,35,0,0,21
	.byte	'Ifx_SMU_FSP_Bits',0,6,168,2,3
	.word	12251
	.byte	10
	.byte	'_Ifx_SMU_ID_Bits',0,6,171,2,16,4,11
	.byte	'MODREV',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'MODTYPE',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'MODNUMBER',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SMU_ID_Bits',0,6,176,2,3
	.word	12406
	.byte	10
	.byte	'_Ifx_SMU_KEYS_Bits',0,6,179,2,16,4,11
	.byte	'CFGLCK',0,4
	.word	8638
	.byte	8,24,2,35,0,11
	.byte	'PERLCK',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'reserved_16',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SMU_KEYS_Bits',0,6,184,2,3
	.word	12513
	.byte	10
	.byte	'_Ifx_SMU_KRST0_Bits',0,6,187,2,16,4,11
	.byte	'RST',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RSTSTAT',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SMU_KRST0_Bits',0,6,192,2,3
	.word	12625
	.byte	10
	.byte	'_Ifx_SMU_KRST1_Bits',0,6,195,2,16,4,11
	.byte	'RST',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SMU_KRST1_Bits',0,6,199,2,3
	.word	12736
	.byte	10
	.byte	'_Ifx_SMU_KRSTCLR_Bits',0,6,202,2,16,4,11
	.byte	'CLR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SMU_KRSTCLR_Bits',0,6,206,2,3
	.word	12828
	.byte	10
	.byte	'_Ifx_SMU_OCS_Bits',0,6,209,2,16,4,11
	.byte	'TGS',0,4
	.word	8638
	.byte	2,30,2,35,0,11
	.byte	'TGB',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'TG_P',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'reserved_4',0,4
	.word	8638
	.byte	20,8,2,35,0,11
	.byte	'SUS',0,4
	.word	8638
	.byte	4,4,2,35,0,11
	.byte	'SUS_P',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'SUSSTA',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'reserved_30',0,4
	.word	8638
	.byte	2,0,2,35,0,0,21
	.byte	'Ifx_SMU_OCS_Bits',0,6,219,2,3
	.word	12924
	.byte	10
	.byte	'_Ifx_SMU_PCTL_Bits',0,6,222,2,16,4,11
	.byte	'HWDIR',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'HWEN',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	5,25,2,35,0,11
	.byte	'PCS',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'reserved_8',0,4
	.word	8638
	.byte	8,16,2,35,0,11
	.byte	'PCFG',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SMU_PCTL_Bits',0,6,230,2,3
	.word	13116
	.byte	10
	.byte	'_Ifx_SMU_RMCTL_Bits',0,6,233,2,16,4,11
	.byte	'TE',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'reserved_1',0,4
	.word	8638
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SMU_RMCTL_Bits',0,6,237,2,3
	.word	13277
	.byte	10
	.byte	'_Ifx_SMU_RMEF_Bits',0,6,240,2,16,4,11
	.byte	'EF0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'EF1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'EF2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'EF3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'EF4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'EF5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'EF6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'EF7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'EF8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'EF9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'EF10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'EF11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'EF12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'EF13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'EF14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'EF15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'EF16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'EF17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'EF18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'EF19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'EF20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'EF21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'EF22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'EF23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'EF24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'EF25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'EF26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'EF27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'EF28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'EF29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'EF30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'EF31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_RMEF_Bits',0,6,146,3,3
	.word	13368
	.byte	10
	.byte	'_Ifx_SMU_RMSTS_Bits',0,6,149,3,16,4,11
	.byte	'STS0',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'STS1',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'STS2',0,4
	.word	8638
	.byte	1,29,2,35,0,11
	.byte	'STS3',0,4
	.word	8638
	.byte	1,28,2,35,0,11
	.byte	'STS4',0,4
	.word	8638
	.byte	1,27,2,35,0,11
	.byte	'STS5',0,4
	.word	8638
	.byte	1,26,2,35,0,11
	.byte	'STS6',0,4
	.word	8638
	.byte	1,25,2,35,0,11
	.byte	'STS7',0,4
	.word	8638
	.byte	1,24,2,35,0,11
	.byte	'STS8',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'STS9',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'STS10',0,4
	.word	8638
	.byte	1,21,2,35,0,11
	.byte	'STS11',0,4
	.word	8638
	.byte	1,20,2,35,0,11
	.byte	'STS12',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'STS13',0,4
	.word	8638
	.byte	1,18,2,35,0,11
	.byte	'STS14',0,4
	.word	8638
	.byte	1,17,2,35,0,11
	.byte	'STS15',0,4
	.word	8638
	.byte	1,16,2,35,0,11
	.byte	'STS16',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'STS17',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'STS18',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'STS19',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'STS20',0,4
	.word	8638
	.byte	1,11,2,35,0,11
	.byte	'STS21',0,4
	.word	8638
	.byte	1,10,2,35,0,11
	.byte	'STS22',0,4
	.word	8638
	.byte	1,9,2,35,0,11
	.byte	'STS23',0,4
	.word	8638
	.byte	1,8,2,35,0,11
	.byte	'STS24',0,4
	.word	8638
	.byte	1,7,2,35,0,11
	.byte	'STS25',0,4
	.word	8638
	.byte	1,6,2,35,0,11
	.byte	'STS26',0,4
	.word	8638
	.byte	1,5,2,35,0,11
	.byte	'STS27',0,4
	.word	8638
	.byte	1,4,2,35,0,11
	.byte	'STS28',0,4
	.word	8638
	.byte	1,3,2,35,0,11
	.byte	'STS29',0,4
	.word	8638
	.byte	1,2,2,35,0,11
	.byte	'STS30',0,4
	.word	8638
	.byte	1,1,2,35,0,11
	.byte	'STS31',0,4
	.word	8638
	.byte	1,0,2,35,0,0,21
	.byte	'Ifx_SMU_RMSTS_Bits',0,6,183,3,3
	.word	13923
	.byte	10
	.byte	'_Ifx_SMU_RTAC0_Bits',0,6,186,3,16,4,11
	.byte	'GID0',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'ALID0',0,4
	.word	8638
	.byte	5,24,2,35,0,11
	.byte	'GID1',0,4
	.word	8638
	.byte	3,21,2,35,0,11
	.byte	'ALID1',0,4
	.word	8638
	.byte	5,16,2,35,0,11
	.byte	'GID2',0,4
	.word	8638
	.byte	3,13,2,35,0,11
	.byte	'ALID2',0,4
	.word	8638
	.byte	5,8,2,35,0,11
	.byte	'GID3',0,4
	.word	8638
	.byte	3,5,2,35,0,11
	.byte	'ALID3',0,4
	.word	8638
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_SMU_RTAC0_Bits',0,6,196,3,3
	.word	14512
	.byte	10
	.byte	'_Ifx_SMU_RTAC1_Bits',0,6,199,3,16,4,11
	.byte	'GID0',0,4
	.word	8638
	.byte	3,29,2,35,0,11
	.byte	'ALID0',0,4
	.word	8638
	.byte	5,24,2,35,0,11
	.byte	'GID1',0,4
	.word	8638
	.byte	3,21,2,35,0,11
	.byte	'ALID1',0,4
	.word	8638
	.byte	5,16,2,35,0,11
	.byte	'GID2',0,4
	.word	8638
	.byte	3,13,2,35,0,11
	.byte	'ALID2',0,4
	.word	8638
	.byte	5,8,2,35,0,11
	.byte	'GID3',0,4
	.word	8638
	.byte	3,5,2,35,0,11
	.byte	'ALID3',0,4
	.word	8638
	.byte	5,0,2,35,0,0,21
	.byte	'Ifx_SMU_RTAC1_Bits',0,6,209,3,3
	.word	14699
	.byte	10
	.byte	'_Ifx_SMU_RTC_Bits',0,6,212,3,16,4,11
	.byte	'RT0E',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'RT1E',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'reserved_2',0,4
	.word	8638
	.byte	6,24,2,35,0,11
	.byte	'RTD',0,4
	.word	8638
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SMU_RTC_Bits',0,6,218,3,3
	.word	14886
	.byte	10
	.byte	'_Ifx_SMU_STS_Bits',0,6,221,3,16,4,11
	.byte	'CMD',0,4
	.word	8638
	.byte	4,28,2,35,0,11
	.byte	'ARG',0,4
	.word	8638
	.byte	4,24,2,35,0,11
	.byte	'RES',0,4
	.word	8638
	.byte	1,23,2,35,0,11
	.byte	'ASCE',0,4
	.word	8638
	.byte	1,22,2,35,0,11
	.byte	'FSP',0,4
	.word	8638
	.byte	2,20,2,35,0,11
	.byte	'FSTS',0,4
	.word	8638
	.byte	1,19,2,35,0,11
	.byte	'reserved_13',0,4
	.word	8638
	.byte	3,16,2,35,0,11
	.byte	'RTS0',0,4
	.word	8638
	.byte	1,15,2,35,0,11
	.byte	'RTME0',0,4
	.word	8638
	.byte	1,14,2,35,0,11
	.byte	'RTS1',0,4
	.word	8638
	.byte	1,13,2,35,0,11
	.byte	'RTME1',0,4
	.word	8638
	.byte	1,12,2,35,0,11
	.byte	'reserved_20',0,4
	.word	8638
	.byte	12,0,2,35,0,0,21
	.byte	'Ifx_SMU_STS_Bits',0,6,235,3,3
	.word	15006
	.byte	12,6,243,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	8654
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_ACCEN0',0,6,248,3,3
	.word	15261
	.byte	12,6,251,3,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9211
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_ACCEN1',0,6,128,4,3
	.word	15325
	.byte	12,6,131,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AD',0,6,136,4,3
	.word	15389
	.byte	12,6,139,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9837
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AFCNT',0,6,144,4,3
	.word	15449
	.byte	12,6,147,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	9998
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AG',0,6,152,4,3
	.word	15512
	.byte	12,6,155,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10549
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AGC',0,6,160,4,3
	.word	15572
	.byte	12,6,163,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	10811
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AGCF',0,6,168,4,3
	.word	15633
	.byte	12,6,171,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11366
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_AGFSP',0,6,176,4,3
	.word	15695
	.byte	12,6,179,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	11923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_CLC',0,6,184,4,3
	.word	15758
	.byte	12,6,187,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12060
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_CMD',0,6,192,4,3
	.word	15819
	.byte	12,6,195,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12163
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_DBG',0,6,200,4,3
	.word	15880
	.byte	12,6,203,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12251
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_FSP',0,6,208,4,3
	.word	15941
	.byte	12,6,211,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12406
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_ID',0,6,216,4,3
	.word	16002
	.byte	12,6,219,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12513
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_KEYS',0,6,224,4,3
	.word	16062
	.byte	12,6,227,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12625
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_KRST0',0,6,232,4,3
	.word	16124
	.byte	12,6,235,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12736
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_KRST1',0,6,240,4,3
	.word	16187
	.byte	12,6,243,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12828
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_KRSTCLR',0,6,248,4,3
	.word	16250
	.byte	12,6,251,4,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	12924
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_OCS',0,6,128,5,3
	.word	16315
	.byte	12,6,131,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13116
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_PCTL',0,6,136,5,3
	.word	16376
	.byte	12,6,139,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RMCTL',0,6,144,5,3
	.word	16438
	.byte	12,6,147,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13368
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RMEF',0,6,152,5,3
	.word	16501
	.byte	12,6,155,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	13923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RMSTS',0,6,160,5,3
	.word	16563
	.byte	12,6,163,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14512
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RTAC0',0,6,168,5,3
	.word	16626
	.byte	12,6,171,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14699
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RTAC1',0,6,176,5,3
	.word	16689
	.byte	12,6,179,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	14886
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_RTC',0,6,184,5,3
	.word	16752
	.byte	12,6,187,5,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	15006
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SMU_STS',0,6,192,5,3
	.word	16813
	.byte	14,20
	.word	492
	.byte	15,19,0,14,28
	.word	492
	.byte	15,27,0,14,152,1
	.word	492
	.byte	15,151,1,0,14,12
	.word	15633
	.byte	15,2,0,14,84
	.word	16903
	.byte	15,6,0,14,44
	.word	492
	.byte	15,43,0,14,28
	.word	15695
	.byte	15,6,0,14,36
	.word	492
	.byte	15,35,0,14,28
	.word	15512
	.byte	15,6,0,14,28
	.word	15389
	.byte	15,6,0,14,228,1
	.word	492
	.byte	15,227,1,0,14,220,9
	.word	492
	.byte	15,219,9,0,10
	.byte	'_Ifx_SMU',0,6,203,5,25,128,16,13
	.byte	'CLC',0
	.word	15758
	.byte	4,2,35,0,13
	.byte	'reserved_4',0
	.word	1537
	.byte	4,2,35,4,13
	.byte	'ID',0
	.word	16002
	.byte	4,2,35,8,13
	.byte	'reserved_C',0
	.word	16874
	.byte	20,2,35,12,13
	.byte	'CMD',0
	.word	15819
	.byte	4,2,35,32,13
	.byte	'STS',0
	.word	16813
	.byte	4,2,35,36,13
	.byte	'FSP',0
	.word	15941
	.byte	4,2,35,40,13
	.byte	'AGC',0
	.word	15572
	.byte	4,2,35,44,13
	.byte	'RTC',0
	.word	16752
	.byte	4,2,35,48,13
	.byte	'KEYS',0
	.word	16062
	.byte	4,2,35,52,13
	.byte	'DBG',0
	.word	15880
	.byte	4,2,35,56,13
	.byte	'PCTL',0
	.word	16376
	.byte	4,2,35,60,13
	.byte	'AFCNT',0
	.word	15449
	.byte	4,2,35,64,13
	.byte	'reserved_44',0
	.word	16883
	.byte	28,2,35,68,13
	.byte	'RTAC0',0
	.word	16626
	.byte	4,2,35,96,13
	.byte	'RTAC1',0
	.word	16689
	.byte	4,2,35,100,13
	.byte	'reserved_68',0
	.word	16892
	.byte	152,1,2,35,104,13
	.byte	'AGCF',0
	.word	16912
	.byte	84,3,35,128,2,13
	.byte	'reserved_154',0
	.word	16921
	.byte	44,3,35,212,2,13
	.byte	'AGFSP',0
	.word	16930
	.byte	28,3,35,128,3,13
	.byte	'reserved_19C',0
	.word	16939
	.byte	36,3,35,156,3,13
	.byte	'AG',0
	.word	16948
	.byte	28,3,35,192,3,13
	.byte	'reserved_1DC',0
	.word	16939
	.byte	36,3,35,220,3,13
	.byte	'AD',0
	.word	16957
	.byte	28,3,35,128,4,13
	.byte	'reserved_21C',0
	.word	16966
	.byte	228,1,3,35,156,4,13
	.byte	'RMCTL',0
	.word	16438
	.byte	4,3,35,128,6,13
	.byte	'RMEF',0
	.word	16501
	.byte	4,3,35,132,6,13
	.byte	'RMSTS',0
	.word	16563
	.byte	4,3,35,136,6,13
	.byte	'reserved_30C',0
	.word	16977
	.byte	220,9,3,35,140,6,13
	.byte	'OCS',0
	.word	16315
	.byte	4,3,35,232,15,13
	.byte	'KRSTCLR',0
	.word	16250
	.byte	4,3,35,236,15,13
	.byte	'KRST1',0
	.word	16187
	.byte	4,3,35,240,15,13
	.byte	'KRST0',0
	.word	16124
	.byte	4,3,35,244,15,13
	.byte	'ACCEN1',0
	.word	15325
	.byte	4,3,35,248,15,13
	.byte	'ACCEN0',0
	.word	15261
	.byte	4,3,35,252,15,0,16
	.word	16988
	.byte	21
	.byte	'Ifx_SMU',0,6,240,5,3
	.word	17583
	.byte	21
	.byte	'boolean',0,7,101,29
	.word	492
	.byte	21
	.byte	'uint8',0,7,105,29
	.word	492
	.byte	21
	.byte	'uint16',0,7,109,29
	.word	509
	.byte	7
	.byte	'unsigned long int',0,4,7,21
	.byte	'uint32',0,7,113,29
	.word	17650
	.byte	21
	.byte	'uint64',0,7,118,29
	.word	351
	.byte	21
	.byte	'sint16',0,7,126,29
	.word	8545
	.byte	7
	.byte	'long int',0,4,5,21
	.byte	'sint32',0,7,131,1,29
	.word	17716
	.byte	7
	.byte	'long long int',0,8,5,21
	.byte	'sint64',0,7,138,1,29
	.word	17744
	.byte	21
	.byte	'float32',0,7,167,1,29
	.word	297
	.byte	21
	.byte	'pvoid',0,8,57,28
	.word	383
	.byte	21
	.byte	'Ifx_TickTime',0,8,79,28
	.word	17744
	.byte	21
	.byte	'Ifx_P_ACCEN0_Bits',0,4,79,3
	.word	7109
	.byte	21
	.byte	'Ifx_P_ACCEN1_Bits',0,4,85,3
	.word	7022
	.byte	21
	.byte	'Ifx_P_ESR_Bits',0,4,107,3
	.word	3365
	.byte	21
	.byte	'Ifx_P_ID_Bits',0,4,115,3
	.word	1418
	.byte	21
	.byte	'Ifx_P_IN_Bits',0,4,137,1,3
	.word	2413
	.byte	21
	.byte	'Ifx_P_IOCR0_Bits',0,4,150,1,3
	.word	1546
	.byte	21
	.byte	'Ifx_P_IOCR12_Bits',0,4,163,1,3
	.word	2193
	.byte	21
	.byte	'Ifx_P_IOCR4_Bits',0,4,176,1,3
	.word	1761
	.byte	21
	.byte	'Ifx_P_IOCR8_Bits',0,4,189,1,3
	.word	1976
	.byte	21
	.byte	'Ifx_P_LPCR0_Bits',0,4,197,1,3
	.word	6381
	.byte	21
	.byte	'Ifx_P_LPCR1_Bits',0,4,205,1,3
	.word	6505
	.byte	21
	.byte	'Ifx_P_LPCR1_P21_Bits',0,4,215,1,3
	.word	6589
	.byte	21
	.byte	'Ifx_P_LPCR2_Bits',0,4,229,1,3
	.word	6769
	.byte	21
	.byte	'Ifx_P_OMCR0_Bits',0,4,240,1,3
	.word	5020
	.byte	21
	.byte	'Ifx_P_OMCR12_Bits',0,4,250,1,3
	.word	5544
	.byte	21
	.byte	'Ifx_P_OMCR4_Bits',0,4,133,2,3
	.word	5194
	.byte	21
	.byte	'Ifx_P_OMCR8_Bits',0,4,144,2,3
	.word	5368
	.byte	21
	.byte	'Ifx_P_OMCR_Bits',0,4,166,2,3
	.word	6033
	.byte	21
	.byte	'Ifx_P_OMR_Bits',0,4,203,2,3
	.word	847
	.byte	21
	.byte	'Ifx_P_OMSR0_Bits',0,4,213,2,3
	.word	4357
	.byte	21
	.byte	'Ifx_P_OMSR12_Bits',0,4,224,2,3
	.word	4845
	.byte	21
	.byte	'Ifx_P_OMSR4_Bits',0,4,235,2,3
	.word	4504
	.byte	21
	.byte	'Ifx_P_OMSR8_Bits',0,4,246,2,3
	.word	4673
	.byte	21
	.byte	'Ifx_P_OMSR_Bits',0,4,140,3,3
	.word	5700
	.byte	21
	.byte	'Ifx_P_OUT_Bits',0,4,162,3,3
	.word	531
	.byte	21
	.byte	'Ifx_P_PCSR_Bits',0,4,180,3,3
	.word	4071
	.byte	21
	.byte	'Ifx_P_PDISC_Bits',0,4,202,3,3
	.word	3705
	.byte	21
	.byte	'Ifx_P_PDR0_Bits',0,4,223,3,3
	.word	2736
	.byte	21
	.byte	'Ifx_P_PDR1_Bits',0,4,244,3,3
	.word	3040
	.byte	21
	.byte	'Ifx_P_ACCEN0',0,4,129,4,3
	.word	7636
	.byte	21
	.byte	'Ifx_P_ACCEN1',0,4,137,4,3
	.word	7069
	.byte	21
	.byte	'Ifx_P_ESR',0,4,145,4,3
	.word	3656
	.byte	21
	.byte	'Ifx_P_ID',0,4,153,4,3
	.word	1497
	.byte	21
	.byte	'Ifx_P_IN',0,4,161,4,3
	.word	2687
	.byte	21
	.byte	'Ifx_P_IOCR0',0,4,169,4,3
	.word	1721
	.byte	21
	.byte	'Ifx_P_IOCR12',0,4,177,4,3
	.word	2373
	.byte	21
	.byte	'Ifx_P_IOCR4',0,4,185,4,3
	.word	1936
	.byte	21
	.byte	'Ifx_P_IOCR8',0,4,193,4,3
	.word	2153
	.byte	21
	.byte	'Ifx_P_LPCR0',0,4,201,4,3
	.word	6465
	.byte	21
	.byte	'Ifx_P_LPCR1',0,4,210,4,3
	.word	6714
	.byte	21
	.byte	'Ifx_P_LPCR2',0,4,218,4,3
	.word	6973
	.byte	21
	.byte	'Ifx_P_OMCR',0,4,226,4,3
	.word	6341
	.byte	21
	.byte	'Ifx_P_OMCR0',0,4,234,4,3
	.word	5154
	.byte	21
	.byte	'Ifx_P_OMCR12',0,4,242,4,3
	.word	5660
	.byte	21
	.byte	'Ifx_P_OMCR4',0,4,250,4,3
	.word	5328
	.byte	21
	.byte	'Ifx_P_OMCR8',0,4,130,5,3
	.word	5504
	.byte	21
	.byte	'Ifx_P_OMR',0,4,138,5,3
	.word	1378
	.byte	21
	.byte	'Ifx_P_OMSR',0,4,146,5,3
	.word	5993
	.byte	21
	.byte	'Ifx_P_OMSR0',0,4,154,5,3
	.word	4464
	.byte	21
	.byte	'Ifx_P_OMSR12',0,4,162,5,3
	.word	4980
	.byte	21
	.byte	'Ifx_P_OMSR4',0,4,170,5,3
	.word	4633
	.byte	21
	.byte	'Ifx_P_OMSR8',0,4,178,5,3
	.word	4805
	.byte	21
	.byte	'Ifx_P_OUT',0,4,186,5,3
	.word	807
	.byte	21
	.byte	'Ifx_P_PCSR',0,4,194,5,3
	.word	4317
	.byte	21
	.byte	'Ifx_P_PDISC',0,4,202,5,3
	.word	4031
	.byte	21
	.byte	'Ifx_P_PDR0',0,4,210,5,3
	.word	3000
	.byte	21
	.byte	'Ifx_P_PDR1',0,4,218,5,3
	.word	3316
	.byte	16
	.word	7676
	.byte	21
	.byte	'Ifx_P',0,4,139,6,3
	.word	19147
	.byte	17,9,250,10,9,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy16384',0,0,18
	.byte	'IfxScu_WDTCON1_IR_divBy256',0,1,18
	.byte	'IfxScu_WDTCON1_IR_divBy64',0,2,0,21
	.byte	'IfxScu_WDTCON1_IR',0,9,255,10,3
	.word	19167
	.byte	10
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,10,45,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'EN3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'EN4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'EN5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'EN6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'EN7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EN8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EN9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'EN10',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EN11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'EN12',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'EN13',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'EN14',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'EN15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'EN16',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'EN17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'EN18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'EN19',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'EN20',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'EN21',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'EN22',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'EN23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EN24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'EN25',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'EN26',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EN27',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'EN28',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'EN29',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'EN30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'EN31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_ACCEN0_Bits',0,10,79,3
	.word	19289
	.byte	10
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,10,82,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	32,0,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1_Bits',0,10,85,3
	.word	19846
	.byte	10
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,10,88,16,4,11
	.byte	'STM0DIS',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'STM1DIS',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'STM2DIS',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,10,94,3
	.word	19923
	.byte	10
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,10,97,16,4,11
	.byte	'BAUD1DIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'BAUD2DIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'LPDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'FSI2DIV',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'FSIDIV',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'CLKSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON0_Bits',0,10,111,3
	.word	20059
	.byte	10
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,10,114,16,4,11
	.byte	'CANDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'ERAYDIV',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'ETHDIV',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'ASCLINFDIV',0,1
	.word	492
	.byte	4,0,2,35,2,11
	.byte	'ASCLINSDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'INSEL',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON1_Bits',0,10,126,3
	.word	20339
	.byte	10
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,10,129,1,16,4,11
	.byte	'BBBDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON2_Bits',0,10,135,1,3
	.word	20577
	.byte	10
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,10,138,1,16,4,11
	.byte	'PLLDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'PLLSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'PLLERAYDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'PLLERAYSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SRIDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'SRISEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON3_Bits',0,10,150,1,3
	.word	20705
	.byte	10
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,10,153,1,16,4,11
	.byte	'SPBDIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'SPBSEL',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'GTMDIV',0,1
	.word	492
	.byte	6,2,2,35,1,11
	.byte	'GTMSEL',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'STMDIV',0,1
	.word	492
	.byte	6,2,2,35,2,11
	.byte	'STMSEL',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON4_Bits',0,10,165,1,3
	.word	20948
	.byte	10
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,10,168,1,16,4,11
	.byte	'MAXDIV',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	26,2,2,35,0,11
	.byte	'UP',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CCUCON5_Bits',0,10,174,1,3
	.word	21183
	.byte	10
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,10,177,1,16,4,11
	.byte	'CPU0DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6_Bits',0,10,181,1,3
	.word	21311
	.byte	10
	.byte	'_Ifx_SCU_CCUCON7_Bits',0,10,184,1,16,4,11
	.byte	'CPU1DIV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7_Bits',0,10,188,1,3
	.word	21411
	.byte	10
	.byte	'_Ifx_SCU_CHIPID_Bits',0,10,191,1,16,4,11
	.byte	'CHREV',0,1
	.word	492
	.byte	6,2,2,35,0,11
	.byte	'CHTEC',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'CHID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'EEA',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'UCODE',0,1
	.word	492
	.byte	7,0,2,35,2,11
	.byte	'FSIZE',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'SP',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SEC',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_CHIPID_Bits',0,10,202,1,3
	.word	21511
	.byte	10
	.byte	'_Ifx_SCU_DTSCON_Bits',0,10,205,1,16,4,11
	.byte	'PWD',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'START',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'CAL',0,4
	.word	469
	.byte	20,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSCON_Bits',0,10,213,1,3
	.word	21719
	.byte	10
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,10,216,1,16,4,11
	.byte	'LOWER',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	5,1,2,35,1,11
	.byte	'LLU',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'UPPER',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	4,2,2,35,3,11
	.byte	'SLCK',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'UOF',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_DTSLIM_Bits',0,10,225,1,3
	.word	21884
	.byte	10
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,10,228,1,16,4,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'RDY',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,10,235,1,3
	.word	22067
	.byte	10
	.byte	'_Ifx_SCU_EICR_Bits',0,10,238,1,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'EXIS0',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'FEN0',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'REN0',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'LDEN0',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'EIEN0',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'INP0',0,1
	.word	492
	.byte	3,1,2,35,1,11
	.byte	'reserved_15',0,4
	.word	469
	.byte	5,12,2,35,0,11
	.byte	'EXIS1',0,1
	.word	492
	.byte	3,1,2,35,2,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'FEN1',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'REN1',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'LDEN1',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'EIEN1',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'INP1',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'reserved_31',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EICR_Bits',0,10,129,2,3
	.word	22221
	.byte	10
	.byte	'_Ifx_SCU_EIFR_Bits',0,10,132,2,16,4,11
	.byte	'INTF0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'INTF1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'INTF2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'INTF3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'INTF4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'INTF5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'INTF6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'INTF7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR_Bits',0,10,143,2,3
	.word	22585
	.byte	10
	.byte	'_Ifx_SCU_EMSR_Bits',0,10,146,2,16,4,11
	.byte	'POL',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'ENON',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PSEL',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,2
	.word	509
	.byte	12,0,2,35,0,11
	.byte	'EMSF',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SEMSF',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	6,0,2,35,2,11
	.byte	'EMSFM',0,1
	.word	492
	.byte	2,6,2,35,3,11
	.byte	'SEMSFM',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_EMSR_Bits',0,10,159,2,3
	.word	22796
	.byte	10
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,10,162,2,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'EDCON',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG_Bits',0,10,167,2,3
	.word	23048
	.byte	10
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,10,170,2,16,4,11
	.byte	'ARI',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ARC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG_Bits',0,10,175,2,3
	.word	23166
	.byte	10
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,10,178,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR13OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR13OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR13CON_Bits',0,10,185,2,3
	.word	23277
	.byte	10
	.byte	'_Ifx_SCU_EVR33CON_Bits',0,10,188,2,16,4,11
	.byte	'reserved_0',0,4
	.word	469
	.byte	28,4,2,35,0,11
	.byte	'EVR33OFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPEVR33OFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVR33CON_Bits',0,10,195,2,3
	.word	23440
	.byte	10
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,10,198,2,16,4,11
	.byte	'ADC13V',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'ADC33V',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'ADCSWDV',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,10,205,2,3
	.word	23603
	.byte	10
	.byte	'_Ifx_SCU_EVRDVSTAT_Bits',0,10,208,2,16,4,11
	.byte	'DVS13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DVS33TRIM',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'VAL',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRDVSTAT_Bits',0,10,215,2,3
	.word	23761
	.byte	10
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,10,218,2,16,4,11
	.byte	'EVR13OVMOD',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'EVR13UVMOD',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'EVR33OVMOD',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'EVR33UVMOD',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SWDOVMOD',0,1
	.word	492
	.byte	2,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	2,4,2,35,2,11
	.byte	'SWDUVMOD',0,1
	.word	492
	.byte	2,2,2,35,2,11
	.byte	'reserved_22',0,2
	.word	509
	.byte	10,0,2,35,2,0,21
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,10,232,2,3
	.word	23926
	.byte	10
	.byte	'_Ifx_SCU_EVROSCCTRL_Bits',0,10,235,2,16,4,11
	.byte	'OSCTRIM',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'OSCPTAT',0,1
	.word	492
	.byte	6,0,2,35,1,11
	.byte	'OSCANASEL',0,1
	.word	492
	.byte	4,4,2,35,2,11
	.byte	'HPBGTRIM',0,2
	.word	509
	.byte	7,5,2,35,2,11
	.byte	'HPBGCLKEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'OSC3V3',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	2,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROSCCTRL_Bits',0,10,245,2,3
	.word	24255
	.byte	10
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,10,248,2,16,4,11
	.byte	'EVR13OVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33OVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDOVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVROVMON_Bits',0,10,255,2,3
	.word	24476
	.byte	10
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,10,130,3,16,4,11
	.byte	'RST13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	16,8,2,35,0,11
	.byte	'RST13OFF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'BPRST13OFF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST33OFF',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'BPRST33OFF',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'RSTSWDOFF',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'BPRSTSWDOFF',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,10,142,3,3
	.word	24639
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF1_Bits',0,10,145,3,16,4,11
	.byte	'SD5P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD5I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD5D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1_Bits',0,10,152,3,3
	.word	24911
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,10,155,3,16,4,11
	.byte	'SD33P',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SD33I',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SD33D',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,10,162,3,3
	.word	25064
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF3_Bits',0,10,165,3,16,4,11
	.byte	'CT5REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT5REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3_Bits',0,10,172,3,3
	.word	25220
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF4_Bits',0,10,175,3,16,4,11
	.byte	'CT5REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT5REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4_Bits',0,10,181,3,3
	.word	25382
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF5_Bits',0,10,184,3,16,4,11
	.byte	'CT33REG0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG1',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'CT33REG2',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5_Bits',0,10,191,3,3
	.word	25525
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCOEFF6_Bits',0,10,194,3,16,4,11
	.byte	'CT33REG3',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'CT33REG4',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6_Bits',0,10,200,3,3
	.word	25690
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,10,203,3,16,4,11
	.byte	'SDFREQSPRD',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'SDFREQ',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDSTEP',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	2,2,2,35,3,11
	.byte	'SDSAMPLE',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,10,211,3,3
	.word	25835
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,10,214,3,16,4,11
	.byte	'DRVP',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDMINMAXDC',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'DRVN',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'SDLUT',0,1
	.word	492
	.byte	6,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,10,222,3,3
	.word	26016
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,10,225,3,16,4,11
	.byte	'SDPWMPRE',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDPID',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SDVOKLVL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,10,232,3,3
	.word	26190
	.byte	10
	.byte	'_Ifx_SCU_EVRSDCTRL4_Bits',0,10,235,3,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SYNCDIV',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	20,1,2,35,0,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4_Bits',0,10,241,3,3
	.word	26350
	.byte	10
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,10,244,3,16,4,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OV13',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'OV33',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'OVSWD',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'UV13',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UV33',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'UVSWD',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'EXTPASS13',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'EXTPASS33',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'BGPROK',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,10,130,4,3
	.word	26494
	.byte	10
	.byte	'_Ifx_SCU_EVRTRIM_Bits',0,10,133,4,16,4,11
	.byte	'EVR13TRIM',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'SDVOUTSEL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	15,1,2,35,2,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRTRIM_Bits',0,10,139,4,3
	.word	26768
	.byte	10
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,10,142,4,16,4,11
	.byte	'EVR13UVVAL',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'EVR33UVVAL',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SWDUVVAL',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,10,149,4,3
	.word	26907
	.byte	10
	.byte	'_Ifx_SCU_EXTCON_Bits',0,10,152,4,16,4,11
	.byte	'EN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'SEL0',0,1
	.word	492
	.byte	4,2,2,35,0,11
	.byte	'reserved_6',0,2
	.word	509
	.byte	10,0,2,35,0,11
	.byte	'EN1',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'NSEL',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SEL1',0,1
	.word	492
	.byte	4,2,2,35,2,11
	.byte	'reserved_22',0,1
	.word	492
	.byte	2,0,2,35,2,11
	.byte	'DIV1',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_EXTCON_Bits',0,10,163,4,3
	.word	27070
	.byte	10
	.byte	'_Ifx_SCU_FDR_Bits',0,10,166,4,16,4,11
	.byte	'STEP',0,2
	.word	509
	.byte	10,6,2,35,0,11
	.byte	'reserved_10',0,1
	.word	492
	.byte	4,2,2,35,1,11
	.byte	'DM',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'RESULT',0,2
	.word	509
	.byte	10,6,2,35,2,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	5,1,2,35,3,11
	.byte	'DISCLK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_FDR_Bits',0,10,174,4,3
	.word	27288
	.byte	10
	.byte	'_Ifx_SCU_FMR_Bits',0,10,177,4,16,4,11
	.byte	'FS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'FS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'FS2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FS3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'FS4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'FS5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'FS6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'FS7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'FC0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'FC1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'FC2',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'FC3',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'FC4',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'FC5',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'FC6',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'FC7',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	8,0,2,35,3,0,21
	.byte	'Ifx_SCU_FMR_Bits',0,10,197,4,3
	.word	27451
	.byte	10
	.byte	'_Ifx_SCU_ID_Bits',0,10,200,4,16,4,11
	.byte	'MODREV',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'MODTYPE',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'MODNUMBER',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_ID_Bits',0,10,205,4,3
	.word	27787
	.byte	10
	.byte	'_Ifx_SCU_IGCR_Bits',0,10,208,4,16,4,11
	.byte	'IPEN00',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'IPEN01',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IPEN02',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'IPEN03',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'IPEN04',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IPEN05',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'IPEN06',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'IPEN07',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	5,3,2,35,1,11
	.byte	'GEEN0',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'IGP0',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'IPEN10',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'IPEN11',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'IPEN12',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'IPEN13',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'IPEN14',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'IPEN15',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'IPEN16',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'IPEN17',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	5,3,2,35,3,11
	.byte	'GEEN1',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'IGP1',0,1
	.word	492
	.byte	2,0,2,35,3,0,21
	.byte	'Ifx_SCU_IGCR_Bits',0,10,232,4,3
	.word	27894
	.byte	10
	.byte	'_Ifx_SCU_IN_Bits',0,10,235,4,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_IN_Bits',0,10,240,4,3
	.word	28346
	.byte	10
	.byte	'_Ifx_SCU_IOCR_Bits',0,10,243,4,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	4,4,2,35,0,11
	.byte	'PC0',0,1
	.word	492
	.byte	4,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'PC1',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_IOCR_Bits',0,10,250,4,3
	.word	28445
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,10,253,4,16,4,11
	.byte	'LBISTREQ',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LBISTREQP',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PATTERNS',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,10,131,5,3
	.word	28595
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,10,134,5,16,4,11
	.byte	'SEED',0,4
	.word	469
	.byte	23,9,2,35,0,11
	.byte	'reserved_23',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'SPLITSH',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'BODY',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'LBISTFREQU',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,10,141,5,3
	.word	28744
	.byte	10
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,10,144,5,16,4,11
	.byte	'SIGNATURE',0,4
	.word	469
	.byte	24,8,2,35,0,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	7,1,2,35,3,11
	.byte	'LBISTDONE',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,10,149,5,3
	.word	28905
	.byte	10
	.byte	'_Ifx_SCU_LCLCON_Bits',0,10,152,5,16,4,11
	.byte	'reserved_0',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'LS',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,2
	.word	509
	.byte	14,1,2,35,2,11
	.byte	'LSEN',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_LCLCON_Bits',0,10,158,5,3
	.word	29035
	.byte	10
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,10,161,5,16,4,11
	.byte	'LCLT0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'LCLT1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST_Bits',0,10,166,5,3
	.word	29167
	.byte	10
	.byte	'_Ifx_SCU_MANID_Bits',0,10,169,5,16,4,11
	.byte	'DEPT',0,1
	.word	492
	.byte	5,3,2,35,0,11
	.byte	'MANUF',0,2
	.word	509
	.byte	11,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_MANID_Bits',0,10,174,5,3
	.word	29282
	.byte	10
	.byte	'_Ifx_SCU_OMR_Bits',0,10,177,5,16,4,11
	.byte	'PS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	14,0,2,35,0,11
	.byte	'PCL0',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'PCL1',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_OMR_Bits',0,10,185,5,3
	.word	29393
	.byte	10
	.byte	'_Ifx_SCU_OSCCON_Bits',0,10,188,5,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PLLLV',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OSCRES',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'GAINSEL',0,1
	.word	492
	.byte	2,3,2,35,0,11
	.byte	'MODE',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'SHBY',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'PLLHV',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'X1D',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'X1DEN',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'OSCVAL',0,1
	.word	492
	.byte	5,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'APREN',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'CAP0EN',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'CAP1EN',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'CAP2EN',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'CAP3EN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_OSCCON_Bits',0,10,209,5,3
	.word	29551
	.byte	10
	.byte	'_Ifx_SCU_OUT_Bits',0,10,212,5,16,4,11
	.byte	'P0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'P1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_OUT_Bits',0,10,217,5,3
	.word	29963
	.byte	10
	.byte	'_Ifx_SCU_OVCCON_Bits',0,10,220,5,16,4,11
	.byte	'CSEL0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CSEL1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'CSEL2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,2
	.word	509
	.byte	13,0,2,35,0,11
	.byte	'OVSTRT',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'OVSTP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'DCINVAL',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'OVCONF',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'POVCONF',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	6,0,2,35,3,0,21
	.byte	'Ifx_SCU_OVCCON_Bits',0,10,233,5,3
	.word	30064
	.byte	10
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,10,236,5,16,4,11
	.byte	'OVEN0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OVEN1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'OVEN2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,4
	.word	469
	.byte	29,0,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,10,242,5,3
	.word	30331
	.byte	10
	.byte	'_Ifx_SCU_PDISC_Bits',0,10,245,5,16,4,11
	.byte	'PDIS0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDIS1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC_Bits',0,10,250,5,3
	.word	30467
	.byte	10
	.byte	'_Ifx_SCU_PDR_Bits',0,10,253,5,16,4,11
	.byte	'PD0',0,1
	.word	492
	.byte	3,5,2,35,0,11
	.byte	'PL0',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PD1',0,1
	.word	492
	.byte	3,1,2,35,0,11
	.byte	'PL1',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDR_Bits',0,10,132,6,3
	.word	30578
	.byte	10
	.byte	'_Ifx_SCU_PDRR_Bits',0,10,135,6,16,4,11
	.byte	'PDR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PDR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PDR2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PDR3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PDR4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PDR5',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PDR6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PDR7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR_Bits',0,10,146,6,3
	.word	30711
	.byte	10
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,10,149,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'MODEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLCON0_Bits',0,10,166,6,3
	.word	30914
	.byte	10
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,10,169,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON1_Bits',0,10,177,6,3
	.word	31270
	.byte	10
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,10,180,6,16,4,11
	.byte	'MODCFG',0,2
	.word	509
	.byte	16,0,2,35,0,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLCON2_Bits',0,10,184,6,3
	.word	31448
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,10,187,6,16,4,11
	.byte	'VCOBYP',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'VCOPWD',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETFINDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'CLRFINDIS',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'OSCDISCDIS',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'reserved_7',0,2
	.word	509
	.byte	2,7,2,35,0,11
	.byte	'NDIV',0,1
	.word	492
	.byte	5,2,2,35,1,11
	.byte	'reserved_14',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'PLLPWD',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'RESLD',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'reserved_19',0,1
	.word	492
	.byte	5,0,2,35,2,11
	.byte	'PDIV',0,1
	.word	492
	.byte	4,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,10,204,6,3
	.word	31548
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,10,207,6,16,4,11
	.byte	'K2DIV',0,1
	.word	492
	.byte	7,1,2,35,0,11
	.byte	'reserved_7',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'K3DIV',0,1
	.word	492
	.byte	4,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'K1DIV',0,1
	.word	492
	.byte	7,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	9,0,2,35,2,0,21
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,10,215,6,3
	.word	31918
	.byte	10
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,10,218,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'PWDSTAT',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,4
	.word	469
	.byte	26,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,10,227,6,3
	.word	32104
	.byte	10
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,10,230,6,16,4,11
	.byte	'VCOBYST',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'VCOLOCK',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'FINDIS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'K1RDY',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'K2RDY',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'reserved_6',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'MODRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,4
	.word	469
	.byte	24,0,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,10,241,6,3
	.word	32302
	.byte	10
	.byte	'_Ifx_SCU_PMCSR_Bits',0,10,244,6,16,4,11
	.byte	'REQSLP',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'SMUSLP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'reserved_3',0,1
	.word	492
	.byte	5,0,2,35,0,11
	.byte	'PMST',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,4
	.word	469
	.byte	21,0,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR_Bits',0,10,251,6,3
	.word	32535
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,10,254,6,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'ESR0DFEN',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'ESR0EDCON',0,1
	.word	492
	.byte	2,1,2,35,0,11
	.byte	'ESR1DFEN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'ESR1EDCON',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'PINADFEN',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'PINAEDCON',0,1
	.word	492
	.byte	2,3,2,35,1,11
	.byte	'PINBDFEN',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'PINBEDCON',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'SCREN',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'STBYRAMSEL',0,1
	.word	492
	.byte	2,5,2,35,2,11
	.byte	'SCRCLKSEL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'TRISTEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'TRISTREQ',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'reserved_24',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'DCDCSYNC',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	3,3,2,35,3,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,2,2,35,3,11
	.byte	'reserved_30',0,1
	.word	492
	.byte	1,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,10,153,7,3
	.word	32687
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,10,156,7,16,4,11
	.byte	'SCRSTEN',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SCRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	6,0,2,35,0,11
	.byte	'CPUIDLSEL',0,1
	.word	492
	.byte	3,5,2,35,1,11
	.byte	'reserved_11',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'IRADIS',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'reserved_13',0,1
	.word	492
	.byte	3,0,2,35,1,11
	.byte	'SCRCFG',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'CPUSEL',0,1
	.word	492
	.byte	3,5,2,35,3,11
	.byte	'STBYEVEN',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'STBYEV',0,1
	.word	492
	.byte	3,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,10,170,7,3
	.word	33254
	.byte	10
	.byte	'_Ifx_SCU_PMSWCR2_Bits',0,10,173,7,16,4,11
	.byte	'SCRINT',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'BUSY',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'SCRECC',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'SCRWDT',0,1
	.word	492
	.byte	1,5,2,35,1,11
	.byte	'SCRRST',0,1
	.word	492
	.byte	1,4,2,35,1,11
	.byte	'reserved_12',0,1
	.word	492
	.byte	4,0,2,35,1,11
	.byte	'TCINT',0,1
	.word	492
	.byte	8,0,2,35,2,11
	.byte	'TCINTREQ',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SMURST',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'RST',0,1
	.word	492
	.byte	1,5,2,35,3,11
	.byte	'reserved_27',0,1
	.word	492
	.byte	4,1,2,35,3,11
	.byte	'LCK',0,1
	.word	492
	.byte	1,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWCR2_Bits',0,10,187,7,3
	.word	33548
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,10,190,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKP',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUN',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKP',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUN',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKP',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUN',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'PORSTDF',0,1
	.word	492
	.byte	1,6,2,35,1,11
	.byte	'HWCFGEVR',0,1
	.word	492
	.byte	3,3,2,35,1,11
	.byte	'STBYRAM',0,1
	.word	492
	.byte	2,1,2,35,1,11
	.byte	'TRIST',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'SCRST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKP',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'SCR',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'SCRWKEN',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'ESR1WKEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'PINAWKEN',0,1
	.word	492
	.byte	1,2,2,35,2,11
	.byte	'PINBWKEN',0,1
	.word	492
	.byte	1,1,2,35,2,11
	.byte	'reserved_23',0,2
	.word	509
	.byte	4,5,2,35,2,11
	.byte	'ESR0TRIST',0,1
	.word	492
	.byte	1,4,2,35,3,11
	.byte	'reserved_28',0,1
	.word	492
	.byte	4,0,2,35,3,0,21
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,10,214,7,3
	.word	33826
	.byte	10
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,10,217,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1WKPCLR',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'ESR1OVRUNCLR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'PINAWKPCLR',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'PINAOVRUNCLR',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'PINBWKPCLR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PINBOVRUNCLR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'SCRSTCLR',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'SCRWKPCLR',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,2
	.word	509
	.byte	14,0,2,35,2,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,10,230,7,3
	.word	34322
	.byte	10
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,10,233,7,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'CLRC',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,2
	.word	509
	.byte	10,4,2,35,0,11
	.byte	'CSS0',0,1
	.word	492
	.byte	1,3,2,35,1,11
	.byte	'CSS1',0,1
	.word	492
	.byte	1,2,2,35,1,11
	.byte	'CSS2',0,1
	.word	492
	.byte	1,1,2,35,1,11
	.byte	'reserved_15',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'USRINFO',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON2_Bits',0,10,243,7,3
	.word	34635
	.byte	10
	.byte	'_Ifx_SCU_RSTCON_Bits',0,10,246,7,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	2,2,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	2,0,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	2,6,2,35,1,11
	.byte	'STM0',0,1
	.word	492
	.byte	2,4,2,35,1,11
	.byte	'STM1',0,1
	.word	492
	.byte	2,2,2,35,1,11
	.byte	'STM2',0,1
	.word	492
	.byte	2,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_RSTCON_Bits',0,10,129,8,3
	.word	34844
	.byte	10
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,10,132,8,16,4,11
	.byte	'ESR0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMU',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'SW',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'STM0',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'STM1',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'STM2',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'reserved_8',0,1
	.word	492
	.byte	8,0,2,35,1,11
	.byte	'PORST',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'reserved_17',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'CB0',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'CB1',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'CB3',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	2,1,2,35,2,11
	.byte	'EVR13',0,1
	.word	492
	.byte	1,0,2,35,2,11
	.byte	'EVR33',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'SWD',0,1
	.word	492
	.byte	1,6,2,35,3,11
	.byte	'reserved_26',0,1
	.word	492
	.byte	2,4,2,35,3,11
	.byte	'STBYR',0,1
	.word	492
	.byte	1,3,2,35,3,11
	.byte	'reserved_29',0,1
	.word	492
	.byte	3,0,2,35,3,0,21
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,10,155,8,3
	.word	35055
	.byte	10
	.byte	'_Ifx_SCU_SAFECON_Bits',0,10,158,8,16,4,11
	.byte	'HBT',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,4
	.word	469
	.byte	31,0,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON_Bits',0,10,162,8,3
	.word	35487
	.byte	10
	.byte	'_Ifx_SCU_STSTAT_Bits',0,10,165,8,16,4,11
	.byte	'HWCFG',0,1
	.word	492
	.byte	8,0,2,35,0,11
	.byte	'FTM',0,1
	.word	492
	.byte	7,1,2,35,1,11
	.byte	'MODE',0,1
	.word	492
	.byte	1,0,2,35,1,11
	.byte	'FCBAE',0,1
	.word	492
	.byte	1,7,2,35,2,11
	.byte	'LUDIS',0,1
	.word	492
	.byte	1,6,2,35,2,11
	.byte	'reserved_18',0,1
	.word	492
	.byte	1,5,2,35,2,11
	.byte	'TRSTL',0,1
	.word	492
	.byte	1,4,2,35,2,11
	.byte	'SPDEN',0,1
	.word	492
	.byte	1,3,2,35,2,11
	.byte	'reserved_21',0,1
	.word	492
	.byte	3,0,2,35,2,11
	.byte	'RAMINT',0,1
	.word	492
	.byte	1,7,2,35,3,11
	.byte	'reserved_25',0,1
	.word	492
	.byte	7,0,2,35,3,0,21
	.byte	'Ifx_SCU_STSTAT_Bits',0,10,178,8,3
	.word	35583
	.byte	10
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,10,181,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'SWRSTREQ',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,4
	.word	469
	.byte	30,0,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,10,186,8,3
	.word	35843
	.byte	10
	.byte	'_Ifx_SCU_SYSCON_Bits',0,10,189,8,16,4,11
	.byte	'CCTRIG0',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'RAMINTM',0,1
	.word	492
	.byte	2,4,2,35,0,11
	.byte	'SETLUDIS',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'reserved_5',0,1
	.word	492
	.byte	3,0,2,35,0,11
	.byte	'DATM',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'reserved_9',0,4
	.word	469
	.byte	23,0,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON_Bits',0,10,198,8,3
	.word	35968
	.byte	10
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,10,201,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,10,208,8,3
	.word	36165
	.byte	10
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,10,211,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,10,218,8,3
	.word	36318
	.byte	10
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,10,221,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET_Bits',0,10,228,8,3
	.word	36471
	.byte	10
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,10,231,8,16,4,11
	.byte	'ESR0T',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'ESR1T',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'reserved_2',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'SMUT',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,4
	.word	469
	.byte	28,0,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,10,238,8,3
	.word	36624
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,10,241,8,16,4,11
	.byte	'ENDINIT',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8638
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,10,247,8,3
	.word	36779
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,10,250,8,16,4,11
	.byte	'reserved_0',0,1
	.word	492
	.byte	2,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,10,134,9,3
	.word	36909
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,10,137,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,10,150,9,3
	.word	37147
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,10,153,9,16,4,11
	.byte	'ENDINIT',0,4
	.word	8638
	.byte	1,31,2,35,0,11
	.byte	'LCK',0,4
	.word	8638
	.byte	1,30,2,35,0,11
	.byte	'PW',0,4
	.word	8638
	.byte	14,16,2,35,0,11
	.byte	'REL',0,4
	.word	8638
	.byte	16,0,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,10,159,9,3
	.word	37370
	.byte	10
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,10,162,9,16,4,11
	.byte	'CLRIRF',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'reserved_1',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IR0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DR',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'reserved_4',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IR1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'UR',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAR',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCR',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCTR',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'reserved_16',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,10,175,9,3
	.word	37496
	.byte	10
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,10,178,9,16,4,11
	.byte	'AE',0,1
	.word	492
	.byte	1,7,2,35,0,11
	.byte	'OE',0,1
	.word	492
	.byte	1,6,2,35,0,11
	.byte	'IS0',0,1
	.word	492
	.byte	1,5,2,35,0,11
	.byte	'DS',0,1
	.word	492
	.byte	1,4,2,35,0,11
	.byte	'TO',0,1
	.word	492
	.byte	1,3,2,35,0,11
	.byte	'IS1',0,1
	.word	492
	.byte	1,2,2,35,0,11
	.byte	'US',0,1
	.word	492
	.byte	1,1,2,35,0,11
	.byte	'PAS',0,1
	.word	492
	.byte	1,0,2,35,0,11
	.byte	'TCS',0,1
	.word	492
	.byte	1,7,2,35,1,11
	.byte	'TCT',0,1
	.word	492
	.byte	7,0,2,35,1,11
	.byte	'TIM',0,2
	.word	509
	.byte	16,0,2,35,2,0,21
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,10,191,9,3
	.word	37748
	.byte	12,10,199,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19289
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN0',0,10,204,9,3
	.word	37967
	.byte	12,10,207,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19846
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ACCEN1',0,10,212,9,3
	.word	38031
	.byte	12,10,215,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	19923
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ARSTDIS',0,10,220,9,3
	.word	38095
	.byte	12,10,223,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20059
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON0',0,10,228,9,3
	.word	38160
	.byte	12,10,231,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20339
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON1',0,10,236,9,3
	.word	38225
	.byte	12,10,239,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20577
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON2',0,10,244,9,3
	.word	38290
	.byte	12,10,247,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20705
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON3',0,10,252,9,3
	.word	38355
	.byte	12,10,255,9,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	20948
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON4',0,10,132,10,3
	.word	38420
	.byte	12,10,135,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21183
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON5',0,10,140,10,3
	.word	38485
	.byte	12,10,143,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21311
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON6',0,10,148,10,3
	.word	38550
	.byte	12,10,151,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21411
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CCUCON7',0,10,156,10,3
	.word	38615
	.byte	12,10,159,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21511
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_CHIPID',0,10,164,10,3
	.word	38680
	.byte	12,10,167,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21719
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSCON',0,10,172,10,3
	.word	38744
	.byte	12,10,175,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	21884
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSLIM',0,10,180,10,3
	.word	38808
	.byte	12,10,183,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22067
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_DTSSTAT',0,10,188,10,3
	.word	38872
	.byte	12,10,191,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22221
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EICR',0,10,196,10,3
	.word	38937
	.byte	12,10,199,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22585
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EIFR',0,10,204,10,3
	.word	38999
	.byte	12,10,207,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	22796
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EMSR',0,10,212,10,3
	.word	39061
	.byte	12,10,215,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23048
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESRCFG',0,10,220,10,3
	.word	39123
	.byte	12,10,223,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23166
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ESROCFG',0,10,228,10,3
	.word	39187
	.byte	12,10,231,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23277
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR13CON',0,10,236,10,3
	.word	39252
	.byte	12,10,239,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23440
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVR33CON',0,10,244,10,3
	.word	39318
	.byte	12,10,247,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23603
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRADCSTAT',0,10,252,10,3
	.word	39384
	.byte	12,10,255,10,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23761
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRDVSTAT',0,10,132,11,3
	.word	39452
	.byte	12,10,135,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	23926
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRMONCTRL',0,10,140,11,3
	.word	39519
	.byte	12,10,143,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24255
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROSCCTRL',0,10,148,11,3
	.word	39587
	.byte	12,10,151,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24476
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVROVMON',0,10,156,11,3
	.word	39655
	.byte	12,10,159,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24639
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRRSTCON',0,10,164,11,3
	.word	39721
	.byte	12,10,167,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	24911
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF1',0,10,172,11,3
	.word	39788
	.byte	12,10,175,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25064
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,10,180,11,3
	.word	39857
	.byte	12,10,183,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25220
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF3',0,10,188,11,3
	.word	39926
	.byte	12,10,191,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25382
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF4',0,10,196,11,3
	.word	39995
	.byte	12,10,199,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25525
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF5',0,10,204,11,3
	.word	40064
	.byte	12,10,207,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25690
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCOEFF6',0,10,212,11,3
	.word	40133
	.byte	12,10,215,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	25835
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL1',0,10,220,11,3
	.word	40202
	.byte	12,10,223,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26016
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL2',0,10,228,11,3
	.word	40270
	.byte	12,10,231,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26190
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL3',0,10,236,11,3
	.word	40338
	.byte	12,10,239,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26350
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSDCTRL4',0,10,244,11,3
	.word	40406
	.byte	12,10,247,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26494
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRSTAT',0,10,252,11,3
	.word	40474
	.byte	12,10,255,11,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26768
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRTRIM',0,10,132,12,3
	.word	40539
	.byte	12,10,135,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	26907
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EVRUVMON',0,10,140,12,3
	.word	40604
	.byte	12,10,143,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27070
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_EXTCON',0,10,148,12,3
	.word	40670
	.byte	12,10,151,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27288
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FDR',0,10,156,12,3
	.word	40734
	.byte	12,10,159,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27451
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_FMR',0,10,164,12,3
	.word	40795
	.byte	12,10,167,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27787
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_ID',0,10,172,12,3
	.word	40856
	.byte	12,10,175,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	27894
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IGCR',0,10,180,12,3
	.word	40916
	.byte	12,10,183,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28346
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IN',0,10,188,12,3
	.word	40978
	.byte	12,10,191,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28445
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_IOCR',0,10,196,12,3
	.word	41038
	.byte	12,10,199,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28595
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL0',0,10,204,12,3
	.word	41100
	.byte	12,10,207,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28744
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL1',0,10,212,12,3
	.word	41168
	.byte	12,10,215,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	28905
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LBISTCTRL2',0,10,220,12,3
	.word	41236
	.byte	12,10,223,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29035
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLCON',0,10,228,12,3
	.word	41304
	.byte	12,10,231,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29167
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_LCLTEST',0,10,236,12,3
	.word	41368
	.byte	12,10,239,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29282
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_MANID',0,10,244,12,3
	.word	41433
	.byte	12,10,247,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29393
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OMR',0,10,252,12,3
	.word	41496
	.byte	12,10,255,12,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29551
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OSCCON',0,10,132,13,3
	.word	41557
	.byte	12,10,135,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	29963
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OUT',0,10,140,13,3
	.word	41621
	.byte	12,10,143,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30064
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCCON',0,10,148,13,3
	.word	41682
	.byte	12,10,151,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30331
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_OVCENABLE',0,10,156,13,3
	.word	41746
	.byte	12,10,159,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30467
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDISC',0,10,164,13,3
	.word	41813
	.byte	12,10,167,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30578
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDR',0,10,172,13,3
	.word	41876
	.byte	12,10,175,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30711
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PDRR',0,10,180,13,3
	.word	41937
	.byte	12,10,183,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	30914
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON0',0,10,188,13,3
	.word	41999
	.byte	12,10,191,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31270
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON1',0,10,196,13,3
	.word	42064
	.byte	12,10,199,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31448
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLCON2',0,10,204,13,3
	.word	42129
	.byte	12,10,207,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31548
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON0',0,10,212,13,3
	.word	42194
	.byte	12,10,215,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	31918
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYCON1',0,10,220,13,3
	.word	42263
	.byte	12,10,223,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32104
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLERAYSTAT',0,10,228,13,3
	.word	42332
	.byte	12,10,231,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32302
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PLLSTAT',0,10,236,13,3
	.word	42401
	.byte	12,10,239,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32535
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMCSR',0,10,244,13,3
	.word	42466
	.byte	12,10,247,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	32687
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR0',0,10,252,13,3
	.word	42529
	.byte	12,10,255,13,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33254
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR1',0,10,132,14,3
	.word	42594
	.byte	12,10,135,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33548
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWCR2',0,10,140,14,3
	.word	42659
	.byte	12,10,143,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	33826
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTAT',0,10,148,14,3
	.word	42724
	.byte	12,10,151,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34322
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_PMSWSTATCLR',0,10,156,14,3
	.word	42790
	.byte	12,10,159,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34844
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON',0,10,164,14,3
	.word	42859
	.byte	12,10,167,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	34635
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTCON2',0,10,172,14,3
	.word	42923
	.byte	12,10,175,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35055
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_RSTSTAT',0,10,180,14,3
	.word	42988
	.byte	12,10,183,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35487
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SAFECON',0,10,188,14,3
	.word	43053
	.byte	12,10,191,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35583
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_STSTAT',0,10,196,14,3
	.word	43118
	.byte	12,10,199,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35843
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SWRSTCON',0,10,204,14,3
	.word	43182
	.byte	12,10,207,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	35968
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_SYSCON',0,10,212,14,3
	.word	43248
	.byte	12,10,215,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36165
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPCLR',0,10,220,14,3
	.word	43312
	.byte	12,10,223,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36318
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPDIS',0,10,228,14,3
	.word	43377
	.byte	12,10,231,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36471
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSET',0,10,236,14,3
	.word	43442
	.byte	12,10,239,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36624
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_TRAPSTAT',0,10,244,14,3
	.word	43507
	.byte	12,10,247,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36779
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON0',0,10,252,14,3
	.word	43573
	.byte	12,10,255,14,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	36909
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_CON1',0,10,132,15,3
	.word	43642
	.byte	12,10,135,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37147
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTCPU_SR',0,10,140,15,3
	.word	43711
	.byte	12,10,143,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37370
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON0',0,10,148,15,3
	.word	43778
	.byte	12,10,151,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37496
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_CON1',0,10,156,15,3
	.word	43845
	.byte	12,10,159,15,9,4,13
	.byte	'U',0
	.word	469
	.byte	4,2,35,0,13
	.byte	'I',0
	.word	485
	.byte	4,2,35,0,13
	.byte	'B',0
	.word	37748
	.byte	4,2,35,0,0,21
	.byte	'Ifx_SCU_WDTS_SR',0,10,164,15,3
	.word	43912
	.byte	10
	.byte	'_Ifx_SCU_WDTCPU',0,10,175,15,25,12,13
	.byte	'CON0',0
	.word	43573
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43642
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43711
	.byte	4,2,35,8,0,16
	.word	43977
	.byte	21
	.byte	'Ifx_SCU_WDTCPU',0,10,180,15,3
	.word	44040
	.byte	10
	.byte	'_Ifx_SCU_WDTS',0,10,183,15,25,12,13
	.byte	'CON0',0
	.word	43778
	.byte	4,2,35,0,13
	.byte	'CON1',0
	.word	43845
	.byte	4,2,35,4,13
	.byte	'SR',0
	.word	43912
	.byte	4,2,35,8,0,16
	.word	44069
	.byte	21
	.byte	'Ifx_SCU_WDTS',0,10,188,15,3
	.word	44130
	.byte	17,3,83,9,1,18
	.byte	'IfxPort_InputMode_undefined',0,127,18
	.byte	'IfxPort_InputMode_noPullDevice',0,0,18
	.byte	'IfxPort_InputMode_pullDown',0,8,18
	.byte	'IfxPort_InputMode_pullUp',0,16,0,21
	.byte	'IfxPort_InputMode',0,3,89,3
	.word	44157
	.byte	17,3,120,9,1,18
	.byte	'IfxPort_OutputIdx_general',0,128,1,18
	.byte	'IfxPort_OutputIdx_alt1',0,136,1,18
	.byte	'IfxPort_OutputIdx_alt2',0,144,1,18
	.byte	'IfxPort_OutputIdx_alt3',0,152,1,18
	.byte	'IfxPort_OutputIdx_alt4',0,160,1,18
	.byte	'IfxPort_OutputIdx_alt5',0,168,1,18
	.byte	'IfxPort_OutputIdx_alt6',0,176,1,18
	.byte	'IfxPort_OutputIdx_alt7',0,184,1,0,21
	.byte	'IfxPort_OutputIdx',0,3,130,1,3
	.word	44308
	.byte	17,3,134,1,9,1,18
	.byte	'IfxPort_OutputMode_pushPull',0,128,1,18
	.byte	'IfxPort_OutputMode_openDrain',0,192,1,0,21
	.byte	'IfxPort_OutputMode',0,3,138,1,3
	.word	44552
	.byte	17,3,144,1,9,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed1',0,0,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed2',0,1,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed3',0,2,18
	.byte	'IfxPort_PadDriver_cmosAutomotiveSpeed4',0,3,18
	.byte	'IfxPort_PadDriver_lvdsSpeed1',0,4,18
	.byte	'IfxPort_PadDriver_lvdsSpeed2',0,5,18
	.byte	'IfxPort_PadDriver_lvdsSpeed3',0,6,18
	.byte	'IfxPort_PadDriver_lvdsSpeed4',0,7,18
	.byte	'IfxPort_PadDriver_ttlSpeed1',0,8,18
	.byte	'IfxPort_PadDriver_ttlSpeed2',0,9,18
	.byte	'IfxPort_PadDriver_ttlSpeed3',0,10,18
	.byte	'IfxPort_PadDriver_ttlSpeed4',0,11,0,21
	.byte	'IfxPort_PadDriver',0,3,158,1,3
	.word	44650
	.byte	21
	.byte	'IfxPort_State',0,3,178,1,3
	.word	8289
	.byte	23,3,190,1,9,8,13
	.byte	'port',0
	.word	8284
	.byte	4,2,35,0,13
	.byte	'pinIndex',0
	.word	492
	.byte	1,2,35,4,0,21
	.byte	'IfxPort_Pin',0,3,194,1,3
	.word	45115
	.byte	16
	.word	16988
	.byte	3
	.word	45175
	.byte	23,11,58,15,16,13
	.byte	'module',0
	.word	45180
	.byte	4,2,35,0,13
	.byte	'pin',0
	.word	45115
	.byte	8,2,35,4,13
	.byte	'select',0
	.word	44308
	.byte	1,2,35,12,0,24
	.word	45185
	.byte	21
	.byte	'IfxSmu_Fsp_Out',0,11,63,3
	.word	45236
.L10:
	.byte	24
	.word	45185
	.byte	24
	.word	45185
	.byte	3
	.word	45269
	.byte	14,4
	.word	45274
	.byte	15,0,0
.L11:
	.byte	14,4
	.word	45279
	.byte	15,0,0,0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,21,0,54,15,39,12,0,0,3,15,0,73,19,0,0,4,46,1,3,8,32,13
	.byte	58,15,59,15,57,15,54,15,39,12,0,0,5,5,0,3,8,58,15,59,15,57,15,73,19,0,0,6,11,0,0,0,7,36,0,3,8,11,15,62
	.byte	15,0,0,8,46,1,3,8,32,13,58,15,59,15,57,15,73,19,54,15,39,12,0,0,9,59,0,3,8,0,0,10,19,1,3,8,58,15,59,15
	.byte	57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,12,23,1,58,15,59,15,57,15,11,15,0,0,13,13
	.byte	0,3,8,73,19,11,15,56,9,0,0,14,1,1,11,15,73,19,0,0,15,33,0,47,15,0,0,16,53,0,73,19,0,0,17,4,1,58,15,59
	.byte	15,57,15,11,15,0,0,18,40,0,3,8,28,13,0,0,19,46,1,49,19,0,0,20,5,0,49,19,0,0,21,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,22,21,0,54,15,0,0,23,19,1,58,15,59,15,57,15,11,15,0,0,24,38,0,73,19,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L13-.L12
.L12:
	.half	3
	.word	.L15-.L14
.L14:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\Infra\\\\Sfr\\\\TC26B\\\\_Reg',0
	.byte	0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_IntrinsicsTasking.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\IfxCpu_Intrinsics.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Port\\Std\\IfxPort.h',0
	.byte	0,0,0
	.byte	'IfxPort_regdef.h',0,1,0,0
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c',0,0,0,0
	.byte	'IfxSmu_regdef.h',0,1,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Platform_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\Cpu\\Std\\Ifx_Types.h',0
	.byte	0,0,0
	.byte	'C:\\\\Users\\\\<USER>\\\\Desktop\\\\ORRN_Code_4G - \270\261\261\276\\\\ORRN_Code\\\\Mark_1\\\\libraries\\\\infineon_libraries\\\\iLLD\\\\TC26B\\\\Tricore\\_Impl\\IfxScu_cfg.h',0
	.byte	0,0,0
	.byte	'IfxScu_regdef.h',0,1,0,0
	.byte	'..\\libraries\\infineon_libraries\\iLLD\\TC26B\\Tricore\\_PinMap\\IfxSmu_PinMap.h',0,0,0,0,0
.L15:
.L13:
	.sdecl	'.debug_info',debug,cluster('IfxSmu_FSP_P33_8_OUT')
	.sect	'.debug_info'
.L6:
	.word	271
	.half	3
	.word	.L7
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSmu_FSP_P33_8_OUT',0,5,48,16
	.word	.L10
	.byte	1,5,3
	.word	IfxSmu_FSP_P33_8_OUT
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSmu_FSP_P33_8_OUT')
	.sect	'.debug_abbrev'
.L7:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	.sdecl	'.debug_info',debug,cluster('IfxSmu_Fsp_Out_pinTable')
	.sect	'.debug_info'
.L8:
	.word	274
	.half	3
	.word	.L9
	.byte	4,1
	.byte	'../libraries/infineon_libraries/iLLD/TC26B/Tricore/_PinMap/IfxSmu_PinMap.c',0
	.byte	'TASKING VX-toolset for AURIX Development Studio (non-commercial): C compiler',0
	.byte	'C:\\Users\\<USER>\\Desktop\\ORRN_Code_4G - \270\261\261\276\\ORRN_Code\\Mark_1\\Debug\\',0,12,1,2
	.word	.L2
	.byte	3
	.byte	'IfxSmu_Fsp_Out_pinTable',0,5,51,23
	.word	.L11
	.byte	1,5,3
	.word	IfxSmu_Fsp_Out_pinTable
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('IfxSmu_Fsp_Out_pinTable')
	.sect	'.debug_abbrev'
.L9:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,0,0,2,61,0,24,16,0,0,3,52,0,3,8,58,15,59,15,57,15,73,16,63,12,2,9
	.byte	0,0,0
	; Module end
